# Kasuat ERP Backend

[![Pipeline Status](https://gitlab.cs.ui.ac.id/propensi-2024-2025-genap/kelas-b/b09-rip-be/badges/main/pipeline.svg)](https://gitlab.cs.ui.ac.id/propensi-2024-2025-genap/kelas-b/b09-rip-be/-/pipelines)

Modern, high-performance ERP system backend built with Bun and Elysia.js.

## Features

- 🚀 High-performance API with Bun + Elysia.js
- 🔐 JWT-based authentication with role-based access control (RBAC)
- 📦 Supabase PostgreSQL for reliable data storage with Row Level Security
- 📚 OpenAPI/Swagger documentation
- 🏗️ Modular architecture with clear separation of concerns
- 🧩 Reference implementation for rapid development
- 🦊 Built on Elysia.js for type-safe API development
- 🐳 Docker-ready (can be containerized easily)

## Getting Started

### Prerequisites

- [Bun](https://bun.sh) >= 1.2.0
- [Tailscale](https://tailscale.com) to connect to the local development environment network
- Node.js >= 22

### Installation

1. Clone the repository:

```bash
git clone https://gitlab.cs.ui.ac.id/propensi-2024-2025-genap/kelas-b/b09-rip-be.git
cd b09-rip-be
```

2. Install dependencies:

```bash
bun install
```

3. Set up environment variables:

```bash
cp .env.example .env.local
```

Update the following variables in `.env.local`:

```env
# Supabase Configuration
SUPABASE_URL=https://your-project.supabase.co
SUPABASE_SERVICE_ROLE=your-service-role-key
BUN_ENV=development
```

4. Start development server:

```bash
bun --watch src/index.ts
```

The API will be available at `http://localhost:3000`, and Swagger documentation at `http://localhost:3000/docs`.

## Implementation

Current Phase: Core Setup & Authentication

### Phase 1: Core Setup & Authentication ✓

- [x] Initialize project structure
- [x] Configure development environment
- [x] Set up Supabase integration
- [x] Implement authentication system
- [x] Add role-based access control
- [x] Create example modules
- [x] Add Swagger documentation
- [x] Implement User Profiles

### Phase 2: Core ERP Modules (In Progress)

- [x] Organization management
- [ ] Employee management
- [ ] Project management
- [ ] Task management
- [ ] Client management
- [ ] Reporting

### Phase 3: Advanced Features

- [ ] Notification system
- [ ] Document management
- [ ] Workflow automation
- [ ] Audit logging
- [ ] Advanced reporting

### Phase 4: Optimization & Deployment

- [ ] Performance tuning
- [ ] Security hardening
- [ ] Production deployment
- [ ] CI/CD pipeline

## Project Structure

The application follows a modular architecture:

```
src/
├── config/             # Swagger config
│   ├── schemas/        # Swagger schemas
│   └── swagger.ts      # Swagger setup
├── database/           # Database
│   ├── migrations/     # SQL migrations
│   ├── models/         # Database models
│   └── seeds/          # Sample data
├── libs/               # External libraries integration
│   └── supabase.ts     # Supabase client
├── middleware/         # Middleware
│   ├── api-response.ts # API response middleware
│   └── auth.ts         # Authentication & RBAC middleware
├── modules/            # Business modules
│   ├── admin/          # Admin module
│   ├── auth/           # Authentication module
│   ├── organization/   # Organization module (reference implementation)
│   ├── employee/       # Employee module (in progress)
│   └── examples/       # RBAC example module
└── utils/              # Utility functions
    ├── api-response.ts # API response utilities
    ├── database.ts     # Database utilities
    └── database.types.ts # Database type definitions
```

## Development

### Module Structure

Each module follows a consistent structure that separates concerns:

```
modules/example/
├── controller.ts    # Request handlers
├── service.ts       # Business logic
├── schema.ts        # Validation schemas
├── routes.ts        # API endpoints
└── index.ts         # Module exports
```

The `organization` module serves as a reference implementation that demonstrates best practices for building new modules.

### Available Commands

```bash
# Development
bun --watch src/index.ts  # Start development server with hot reload

# Database
bun db:migrate             # Run migrations (when implemented)
bun db:seed                # Seed database (when implemented)

# Testing
bun test                   # Run all tests
bun test ./src/tests/path/to/test.ts  # Run specific test file
./scripts/run-project-task-tests.sh   # Run project task tests separately
```

### Testing

The application uses Bun's built-in test runner for unit and integration tests. Tests are organized by module and follow a consistent structure:

```
tests/module-name/
├── controller.test.ts  # Controller tests
├── service.test.ts     # Service tests
└── test-utils.ts       # Test utilities
```

#### Test Utilities

Common test utilities are available in the `test-utils.ts` file for each module. These utilities include:

- `createMockData`: Functions to create mock data for testing
- `setupMocks`: Functions to set up and reset mocks between tests
- `createMockFn`: A helper function for creating mockable functions with call tracking

#### Running Tests

Due to the way Bun's test runner handles mocks, some tests may need to be run separately to avoid conflicts. For these cases, we provide scripts in the `scripts/` directory:

```bash
# Run project task tests separately
./scripts/run-project-task-tests.sh
```

#### Writing Tests

When writing tests:

1. Follow the Arrange-Act-Assert pattern
2. Use descriptive test names
3. Group related tests using `describe` blocks
4. Test both success and error cases
5. Use the test utilities to create mock data and set up mocks

### API Documentation

Once the server is running, access the API documentation at:

- Swagger UI: `http://localhost:3000/docs`

## Authentication & Authorization

The application uses JWT-based authentication with Supabase and includes a comprehensive role-based access control (RBAC) system:

### User Roles

- **Manager**: Full access to all resources
- **HR**: Access to employee and HR-related resources
- **Finance**: Access to financial resources
- **Operation**: Access to operational resources
- **Client**: Limited access to their own organization's resources

### Data Filtering

In addition to route-level RBAC, the system includes data filtering based on user roles and relationships:

- Managers can see all data
- Clients can only see their organization's data
- Employees can only see data assigned to them

This is enforced at both the application and database level using Supabase Row Level Security (RLS).

## Contributing

Please read our [Contributing Guide](contributing-guide.md) for details on our code of conduct and development process.

### Quick Start for New Modules

1. Use the `organization` module as a reference implementation
2. Follow the module structure outlined above
3. Implement appropriate RBAC for your module
4. Document your API with Swagger annotations

## License

This software is proprietary and confidential. Unauthorized copying, transferring, or reproduction of the contents of this repository, via any medium, is strictly prohibited.

All rights reserved.

© 2025 Kasuat. All rights reserved.

This software is protected by copyright law and international treaties. Any unauthorized use, reproduction, or distribution of this software or any portion of it may result in severe civil and criminal penalties, and will be prosecuted to the maximum extent possible under law.
