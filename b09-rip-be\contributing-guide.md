# Contributing Guide: Building New APIs

This guide will walk you through the process of contributing to our project by creating new API endpoints from scratch. We'll cover everything from database model design to implementing a complete API with proper validation, error handling, and security.

## Table of Contents

1. [Project Overview](#project-overview)
2. [Understanding the Architecture](#understanding-the-architecture)
3. [Step 1: Creating Database Models](#step-1-creating-database-models)
4. [Step 2: Creating Database Migrations](#step-2-creating-database-migrations)
5. [Step 3: Creating Validation Schemas](#step-3-creating-validation-schemas)
6. [Step 4: Implementing the Service Layer](#step-4-implementing-the-service-layer)
7. [Step 5: Implementing the Controller Layer](#step-5-implementing-the-controller-layer)
8. [Step 6: Setting Up Routes](#step-6-setting-up-routes)
9. [Step 7: Registering Your Module](#step-7-registering-your-module)
10. [Step 8: Documenting Your API with Swagger](#step-8-documenting-your-api-with-swagger)
11. [Authentication & Authorization](#authentication--authorization)
12. [Advanced Topics](#advanced-topics)
13. [Testing Your API](#testing-your-api)
14. [Pull Request Guidelines](#pull-request-guidelines)

## Project Overview

Our project uses the following technologies:

- **Backend Framework**: [Elysia](https://elysiajs.com/) (a Bun-based web framework)
- **Database**: PostgreSQL with [Supabase](https://supabase.io/)
- **Authentication**: Supabase Auth with JWT
- **API Validation**: Elysia's built-in validation

The project follows a modular architecture where each feature is organized in its own module with clear separation of concerns:

```
src/
├── config/             # Swagger config
│   ├── schemas/        # Swagger schemas
│   └── swagger.ts      # Swagger setup
├── database/           # Database
│   ├── migrations/     # SQL migrations
│   ├── models/         # Database models
│   └── seeds/          # Sample data
├── libs/               # External libraries integration
│   └── supabase.ts     # Supabase client
├── middleware/         # Middleware
│   ├── api-response.ts # API response middleware
│   └── auth.ts         # Authentication & RBAC middleware
├── modules/            # Business modules
│   ├── admin/          # Admin module
│   ├── auth/           # Authentication module
│   ├── organization/   # Organization module (reference implementation)
│   ├── employee/       # Employee module (in progress)
│   └── examples/       # RBAC example module
└── utils/              # Utility functions
    ├── api-response.ts # API response utilities
    ├── database.ts     # Database utilities
    └── database.types.ts # Database type definitions
```

## Understanding the Architecture

Our application follows a layered architecture that separates concerns:

1. **Models**: Define the structure of your data
2. **Services**: Contain business logic and database operations
3. **Controllers**: Handle HTTP requests and responses
4. **Routes**: Define API endpoints and connect them to controllers
5. **Validation Schemas**: Ensure data integrity
6. **Middleware**: Handle cross-cutting concerns like authentication

We also use a set of database utilities to handle common operations like creating, reading, updating, and deleting records, with automatic management of audit fields.

## Step 1: Creating Database Models

Let's start by defining the database model for your feature. We'll use an "Organization" feature as an example.

1. Create a new file in `src/database/models/organization.model.ts`:

```typescript
/**
 * Organization model interfaces
 */
import { BaseRecord } from "../../utils/database.types";

/**
 * Base Organization interface representing the database table
 */
export interface Organization extends BaseRecord {
  name: string;
  phone: string;
  address: string;
  client_type: string;
  notes: string | null;
}

/**
 * Data transfer object for creating a new organization
 */
export interface CreateOrganizationDto {
  name: string;
  phone: string;
  address: string;
  client_type: string;
  notes?: string | null;
}

/**
 * Data transfer object for updating an existing organization
 */
export interface UpdateOrganizationDto {
  name?: string;
  phone?: string;
  address?: string;
  client_type?: string;
  notes?: string | null;
}
```

This model extends the `BaseRecord` interface, which includes audit fields like `created_at`, `created_by`, etc. By extending this interface, your model automatically inherits these fields.

## Step 2: Creating Database Migrations

Now, let's create a SQL migration to add the tasks table to the database.

1. Create a new file in `src/database/migrations/` with a sequential number, e.g., `004_create_tasks.sql`:

```sql
-- Create the organizations table
CREATE TABLE IF NOT EXISTS public.organizations (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  name TEXT NOT NULL,
  phone TEXT NOT NULL,
  address TEXT NOT NULL,
  client_type TEXT NOT NULL,
  notes TEXT,
  created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
  created_by UUID NOT NULL REFERENCES auth.users(id),
  updated_at TIMESTAMPTZ,
  updated_by UUID REFERENCES auth.users(id),
  deleted_at TIMESTAMPTZ,
  deleted_by UUID REFERENCES auth.users(id)
);

-- Add Row Level Security (RLS) policies
ALTER TABLE public.organizations ENABLE ROW LEVEL SECURITY;

-- Policy: Admin and Managers can view all organizations
CREATE POLICY "Admin and Managers can view all organizations" ON public.organizations
  FOR SELECT
  USING (
    auth.uid() IN (
      SELECT user_id FROM public.user_profiles
      WHERE role IN ('Admin', 'Manager') AND is_active = true
    )
  );

-- Policy: Admin and Managers can insert organizations
CREATE POLICY "Admin and Managers can insert organizations" ON public.organizations
  FOR INSERT
  WITH CHECK (
    auth.uid() IN (
      SELECT user_id FROM public.user_profiles
      WHERE role IN ('Admin', 'Manager') AND is_active = true
    )
  );

-- Policy: Admin and Managers can update organizations
CREATE POLICY "Admin and Managers can update organizations" ON public.organizations
  FOR UPDATE
  USING (
    auth.uid() IN (
      SELECT user_id FROM public.user_profiles
      WHERE role IN ('Admin', 'Manager') AND is_active = true
    )
  );

-- Policy: Admin and Managers can soft-delete organizations
CREATE POLICY "Admin and Managers can soft-delete organizations" ON public.organizations
  FOR UPDATE
  USING (
    auth.uid() IN (
      SELECT user_id FROM public.user_profiles
      WHERE role IN ('Admin', 'Manager') AND is_active = true
    )
  );
```

2. Apply this migration in Supabase's SQL Editor or using your preferred migration tool.

## Step 3: Creating Validation Schemas

Let's create validation schemas to ensure data integrity.

1. Create a new module directory: `src/modules/organization`
2. Create a schema file in `src/modules/organization/schema.ts`:

```typescript
import { t } from "elysia";

// Common organization schema patterns
export const nameSchema = t.String({
  minLength: 2,
  maxLength: 100,
  description: "Organization name (2-100 characters)",
});

export const phoneSchema = t.String({
  pattern: "^[0-9]+$",
  minLength: 10,
  description: "Phone number (minimum 10 digits, only numbers allowed)",
});

export const addressSchema = t.String({
  minLength: 5,
  maxLength: 500,
  description: "Organization address (5-500 characters)",
});

export const clientTypeSchema = t.String({
  minLength: 2,
  maxLength: 50,
  description: "Type of client organization (2-50 characters)",
});

export const notesSchema = t.Optional(
  t.String({
    maxLength: 1000,
    description: "Optional notes about the organization (max 1000 characters)",
  })
);

// Organization validation schemas
export const createOrganizationSchema = {
  body: t.Object({
    name: nameSchema,
    phone: phoneSchema,
    address: addressSchema,
    client_type: clientTypeSchema,
    notes: notesSchema,
  }),
};

export const updateOrganizationSchema = {
  params: t.Object({
    id: t.String({
      format: "uuid",
      description: "Organization ID",
    }),
  }),
  body: t.Object({
    name: t.Optional(nameSchema),
    phone: t.Optional(phoneSchema),
    address: t.Optional(addressSchema),
    client_type: t.Optional(clientTypeSchema),
    notes: notesSchema,
  }),
};

export const getOrganizationSchema = {
  params: t.Object({
    id: t.String({
      format: "uuid",
      description: "Organization ID",
    }),
  }),
};

export const deleteOrganizationSchema = {
  params: t.Object({
    id: t.String({
      format: "uuid",
      description: "Organization ID",
    }),
  }),
};

// Schema for GET /organizations query parameters
export const getOrganizationsQuerySchema = {
  query: t.Object({
    search: t.Optional(
      t.String({
        description:
          "Search term to filter organizations by name, phone, address, or client type",
      })
    ),
    page: t.Optional(
      t.Numeric({
        description: "Page number for pagination (starts at 1)",
      })
    ),
    pageSize: t.Optional(
      t.Numeric({
        description: "Number of items per page",
      })
    ),
    client_type: t.Optional(
      t.String({
        description: "Filter organizations by client type",
      })
    ),
  }),
};
```

These schemas will validate incoming requests to ensure they match the expected format.

## Step 4: Implementing the Service Layer

Now, let's create the service layer to handle business logic and database operations.

1. Create a service file in `src/modules/organization/service.ts`:

```typescript
import { dbUtils } from "../../utils/database";
import {
  CreateOrganizationDto,
  Organization,
  UpdateOrganizationDto,
} from "../../database/models/organization.model";
import { QueryOptions } from "../../utils/database.types";

export class OrganizationService {
  private static readonly TABLE_NAME = "organizations";

  /**
   * Create a new organization
   */
  static async create(data: CreateOrganizationDto, userId: string) {
    return dbUtils.create<Organization>(this.TABLE_NAME, data, userId);
  }

  /**
   * Get an organization by ID
   */
  static async getById(id: string) {
    return dbUtils.getById<Organization>(this.TABLE_NAME, id);
  }

  /**
   * Get all organizations with search, filter, and pagination
   * @param options Query options including search, filters, and pagination
   */
  static async getAll(options: QueryOptions = {}) {
    return dbUtils.getAll<Organization>(this.TABLE_NAME, options);
  }

  /**
   * Update an organization
   */
  static async update(id: string, data: UpdateOrganizationDto, userId: string) {
    return dbUtils.update<Organization>(this.TABLE_NAME, id, data, userId);
  }

  /**
   * Delete an organization (soft delete)
   */
  static async delete(id: string, userId: string) {
    return dbUtils.softDelete<Organization>(this.TABLE_NAME, id, userId);
  }
}
```

The service layer handles business logic and database operations using our database utilities. It automatically manages audit fields and security.

## Step 5: Implementing the Controller Layer

Let's create the controller to handle HTTP requests and responses.

1. Create a controller file in `src/modules/tasks/controller.ts`:

```typescript
import { OrganizationService } from "./models";
import {
  CreateOrganizationDto,
  UpdateOrganizationDto,
} from "../../database/models/organization.model";
import { AuthUser } from "../../middleware/auth";
import { FilterOption, QueryOptions } from "../../utils/database.types";

/**
 * Helper function to ensure API response functions are available
 */
function ensureResponseFunctions(context: any) {
  // Check if the basic response functions are available
  if (typeof context.success !== "function") {
    console.error("API Response middleware functions not available in context");
    // Provide fallback response if middleware functions aren't available
    return {
      success: (data: any, message = "Operation successful") => ({
        success: true,
        message,
        data,
      }),
      forbidden: (message = "Forbidden", errorCode?: string) => ({
        success: false,
        message,
        data: null,
        error: { code: errorCode || "FORBIDDEN" },
      }),
      unauthorized: (message = "Unauthorized", errorCode?: string) => ({
        success: false,
        message,
        data: null,
        error: { code: errorCode || "UNAUTHORIZED" },
      }),
      notFound: (message = "Not found", errorCode?: string) => ({
        success: false,
        message,
        data: null,
        error: { code: errorCode || "NOT_FOUND" },
      }),
      serverError: (message = "Server error", error?: Error) => ({
        success: false,
        message,
        data: null,
        error: {
          code: "INTERNAL_SERVER_ERROR",
          details: error ? { stack: error.stack } : undefined,
        },
      }),
    };
  }

  // If functions are available, return the original context
  return context;
}

export class OrganizationController {
  /**
   * Get all organizations with search, filter, and pagination
   */
  static async getAll(context: any) {
    const {
      query = {},
      success,
      serverError,
    } = ensureResponseFunctions(context);

    // Build query options from request parameters
    const options: QueryOptions = {};

    // Handle search
    if (query.search) {
      options.search = {
        term: query.search,
        fields: ["name", "phone", "address", "client_type"], // Searchable fields
      };
    }

    // Handle filters
    const filters: FilterOption[] = [];

    // Only filter by client_type
    if (query.client_type) {
      filters.push({ field: "client_type", value: query.client_type });
    }

    if (filters.length > 0) {
      options.filters = filters;
    }

    // Handle pagination
    if (query.page || query.pageSize) {
      options.pagination = {
        page: query.page ? parseInt(query.page, 10) : 1,
        pageSize: query.pageSize ? parseInt(query.pageSize, 10) : 10,
      };
    }

    // Call the model with the constructed options
    const { data, error, result } = await OrganizationService.getAll(options);

    if (error) {
      return serverError(error.message, error);
    }

    return success(data, "Organizations retrieved successfully");
  }

  /**
   * Get organization by ID
   */
  static async getById(context: any) {
    const { params, success, notFound, serverError } =
      ensureResponseFunctions(context);
    const { id } = params;

    const { data, error } = await OrganizationService.getById(id);

    if (error) {
      return serverError(error.message, error);
    }

    if (!data) {
      return notFound("Organization not found");
    }

    return success(data, "Organization retrieved successfully");
  }

  /**
   * Create a new organization
   */
  static async create(context: any) {
    const { body, user, success, serverError } =
      ensureResponseFunctions(context);

    const { data, error } = await OrganizationService.create(body, user.id);

    if (error) {
      return serverError(error.message, error);
    }

    return success(data, "Organization created successfully");
  }

  /**
   * Update an organization
   */
  static async update(context: any) {
    const { params, body, user, success, notFound, serverError } =
      ensureResponseFunctions(context);
    const { id } = params;

    const { data, error } = await OrganizationService.update(id, body, user.id);

    if (error) {
      return serverError(error.message, error);
    }

    if (!data) {
      return notFound("Organization not found");
    }

    return success(data, "Organization updated successfully");
  }

  /**
   * Delete an organization
   */
  static async delete(context: any) {
    const { params, user, success, notFound, serverError } =
      ensureResponseFunctions(context);
    const { id } = params;

    const { data, error } = await OrganizationService.delete(id, user.id);

    if (error) {
      return serverError(error.message, error);
    }

    if (!data) {
      return notFound("Organization not found");
    }

    return success(null, "Organization deleted successfully");
  }
}
```

The controller handles HTTP requests and responses, extracting data from the request, calling the appropriate service methods, and formatting the response.

## Step 6: Setting Up Routes

Now, let's create routes to define API endpoints, including proper authorization:

1. Create a routes file in `src/modules/tasks/routes.ts`:

```typescript
import { Elysia } from "elysia";
import { OrganizationController } from "./controller";
import {
  createOrganizationSchema,
  updateOrganizationSchema,
  getOrganizationSchema,
  deleteOrganizationSchema,
  getOrganizationsQuerySchema,
} from "./schema";
import { requireRole, requireActiveUser } from "../../middleware/auth";
import { UserRole } from "../../database/models/user-profile.model";
import { apiResponse } from "../../middleware/api-response";

/**
 * Organizations routes
 *
 * Admin and Managers can manage organizations
 * All users must be active to access these routes
 * Note: API response middleware is applied at the module level via index.ts
 */
export const organizationRoutes = (app: Elysia) =>
  app.group("/organizations", (app) =>
    app
      // First ensure users are active
      .use(requireActiveUser)

      // Then apply role-based access control for active users
      .use(requireRole([UserRole.Admin, UserRole.Manager]))

      // CRUD operations
      .get("/", OrganizationController.getAll, {
        schema: getOrganizationsQuerySchema,
        detail: {
          tags: ["organizations"],
          summary: "Get all organizations",
          description:
            "Retrieve all organizations with search, filter, and pagination",
          security: [{ bearerAuth: [] }],
          responses: {
            "200": {
              description: "Successfully retrieved organizations",
              content: {
                "application/json": {
                  schema: {
                    $ref: "#/components/schemas/Organization",
                  },
                  examples: {
                    getOrganizationsExample: {
                      $ref: "#/components/examples/getOrganizationsExample",
                    },
                  },
                },
              },
            },
            "403": {
              description: "Forbidden - User does not have required role",
              content: {
                "application/json": {
                  examples: {
                    notFoundErrorExample: {
                      $ref: "#/components/examples/notFoundErrorExample",
                    },
                  },
                },
              },
            },
          },
        },
      })
      .get("/:id", OrganizationController.getById, {
        schema: getOrganizationSchema,
        detail: {
          tags: ["organizations"],
          summary: "Get organization by ID",
          description: "Retrieve a specific organization by ID",
          security: [{ bearerAuth: [] }],
          responses: {
            "200": {
              description: "Successfully retrieved organization",
              content: {
                "application/json": {
                  schema: {
                    $ref: "#/components/schemas/Organization",
                  },
                  examples: {
                    getOrganizationExample: {
                      $ref: "#/components/examples/getOrganizationExample",
                    },
                  },
                },
              },
            },
            "404": {
              description: "Organization not found",
              content: {
                "application/json": {
                  examples: {
                    notFoundErrorExample: {
                      $ref: "#/components/examples/notFoundErrorExample",
                    },
                  },
                },
              },
            },
          },
        },
      })
      .post("/", OrganizationController.create, {
        schema: createOrganizationSchema,
        detail: {
          tags: ["organizations"],
          summary: "Create organization",
          description: "Create a new organization",
          security: [{ bearerAuth: [] }],
          requestBody: {
            content: {
              "application/json": {
                schema: {
                  $ref: "#/components/schemas/CreateOrganizationDto",
                },
                examples: {
                  createOrganizationExample: {
                    $ref: "#/components/examples/createOrganizationExample",
                  },
                },
              },
            },
          },
          responses: {
            "200": {
              description: "Successfully created organization",
              content: {
                "application/json": {
                  schema: {
                    $ref: "#/components/schemas/Organization",
                  },
                },
              },
            },
          },
        },
      })
      .put("/:id", OrganizationController.update, {
        schema: updateOrganizationSchema,
        detail: {
          tags: ["organizations"],
          summary: "Update organization",
          description: "Update an existing organization",
          security: [{ bearerAuth: [] }],
          requestBody: {
            content: {
              "application/json": {
                schema: {
                  $ref: "#/components/schemas/UpdateOrganizationDto",
                },
                examples: {
                  updateOrganizationExample: {
                    $ref: "#/components/examples/updateOrganizationExample",
                  },
                },
              },
            },
          },
          responses: {
            "200": {
              description: "Successfully updated organization",
              content: {
                "application/json": {
                  schema: {
                    $ref: "#/components/schemas/Organization",
                  },
                },
              },
            },
            "404": {
              description: "Organization not found",
              content: {
                "application/json": {
                  examples: {
                    notFoundErrorExample: {
                      $ref: "#/components/examples/notFoundErrorExample",
                    },
                  },
                },
              },
            },
          },
        },
      })
      .delete("/:id", OrganizationController.delete, {
        schema: deleteOrganizationSchema,
        detail: {
          tags: ["organizations"],
          summary: "Delete organization",
          description: "Delete an organization (soft delete)",
          security: [{ bearerAuth: [] }],
          responses: {
            "200": {
              description: "Successfully deleted organization",
              content: {
                "application/json": {
                  examples: {
                    deleteOrganizationExample: {
                      $ref: "#/components/examples/deleteOrganizationExample",
                    },
                  },
                },
              },
            },
            "404": {
              description: "Organization not found",
              content: {
                "application/json": {
                  examples: {
                    notFoundErrorExample: {
                      $ref: "#/components/examples/notFoundErrorExample",
                    },
                  },
                },
              },
            },
          },
        },
      })
  );
```

2. Create an index file in `src/modules/tasks/index.ts` to export your module:

```typescript
import { taskRoutes } from "./routes";

export const tasks = taskRoutes;

// Re-export for convenience
export * from "./schema";
export * from "./controller";
export * from "./service";
```

## Step 7: Registering Your Module

Finally, register your new module in the main app:

1. Edit `src/index.ts` to include your new module:

```typescript
import { Elysia } from "elysia";
import { auth } from "./modules/auth/index";
import { organizations } from "./modules/organization";
import { admin } from "./modules/admin";
import { swaggerConfig } from "./config/swagger";

const app = new Elysia()
  .use(swaggerConfig)
  .group("/v1", (app) =>
    app
      .use(auth)
      .use(organizations) // Use your new module
      .use(admin)
  )
  .get("/", () => "Hello Elysia")
  .listen(3000);

console.log(
  `🦊 Elysia is running at ${app.server?.hostname}:${app.server?.port}`
);
```

Congratulations! You've now created a complete API module, including database models, services, controllers, and routes.

## Step 8: Documenting Your API with Swagger

Proper API documentation is crucial for developer experience. Our project uses Swagger/OpenAPI to document all endpoints, making it easier for other developers to understand and use your API.

### Understanding the Config Structure

The `src/config/` directory contains our Swagger configuration:

```
src/config/
├── schemas/                 # API schemas by module
│   ├── admin/               # Admin module schemas
│   ├── auth/                # Auth module schemas
│   ├── organization/        # Organization module schemas
│   └── index.ts             # Re-exports all schemas
└── swagger.ts               # Main Swagger configuration
```

Each module has its own schema directory with:

- Schema definitions matching database models
- Request/response examples
- Documentation metadata

### Creating Schema Definitions

1. Create a directory for your module in `src/config/schemas/`:

```
mkdir -p src/config/schemas/your-module
```

2. Create a `schemas.ts` file to define your schemas and examples:

```typescript
// Define module schema examples for Swagger documentation
export const yourModuleExamples = {
  createExample: {
    summary: "Example create request",
    value: {
      // Example request data
      name: "Example Name",
      // Other fields...
    },
  },
  // Other examples...
};

// Define module schemas for Swagger documentation
export const yourModuleSchemas = {
  YourEntity: {
    type: "object" as const,
    required: ["id", "name", "created_at", "created_by"],
    properties: {
      id: {
        type: "string" as const,
        format: "uuid",
        description: "Unique identifier",
      },
      name: {
        type: "string" as const,
        description: "Name of the entity",
      },
      // Other properties...
      created_at: {
        type: "string" as const,
        format: "date-time",
        description: "Timestamp when the entity was created",
      },
      created_by: {
        type: "string" as const,
        description: "ID of the user who created the entity",
      },
    },
  },
  // Other schemas (CreateDTO, UpdateDTO, etc.)...
};
```

3. Create an `index.ts` file to re-export your schemas:

```typescript
// Re-export module schemas
export * from "./schemas";
```

4. Add your module to the main schemas index at `src/config/schemas/index.ts`:

```typescript
// Re-export all module schemas
export * from "./auth";
export * from "./admin";
export * from "./organization";
export * from "./your-module"; // Add your module
```

### Documenting Routes

When defining routes, use the `detail` property to document them:

```typescript
.get("/", YourController.getAll, {
  schema: getAllQuerySchema,
  detail: {
    tags: ["your-module"], // Module tag
    summary: "Get all items",
    description: "Retrieve all items with filtering and pagination",
    security: [{ bearerAuth: [] }], // Auth requirement
    responses: {
      "200": {
        description: "Successfully retrieved items",
        content: {
          "application/json": {
            schema: {
              $ref: "#/components/schemas/YourEntity", // Reference your schema
            },
            examples: {
              getAllExample: {
                $ref: "#/components/examples/getAllExample", // Reference example
              },
            },
          },
        },
      },
      "403": {
        description: "Forbidden - User does not have required role",
        // Error examples...
      },
    },
  },
})
```

### Real-world Example: Organization Module

Let's look at the Organization module as a complete example:

1. **Schema Definition** (`src/config/schemas/organization/schemas.ts`):

```typescript
// Define organization schemas for Swagger documentation
export const organizationSchemas = {
  Organization: {
    type: "object" as const,
    required: [
      "id",
      "name",
      "phone",
      "address",
      "client_type",
      "created_at",
      "created_by",
    ],
    properties: {
      id: {
        type: "string" as const,
        format: "uuid",
        description: "Unique identifier for the organization",
      },
      name: {
        type: "string" as const,
        minLength: 2,
        maxLength: 100,
        description: "Organization name (2-100 characters)",
      },
      // Other properties...
    },
  },
  // CreateOrganizationDto, UpdateOrganizationDto, etc.
};

// Define examples
export const organizationExamples = {
  createOrganizationExample: {
    summary: "Example create organization request",
    value: {
      name: "Acme Corporation",
      phone: "6281234567890",
      // Other properties...
    },
  },
  // Other examples...
};
```

2. **Route Documentation** (`src/modules/organization/routes.ts`):

```typescript
.get("/", OrganizationController.getAll, {
  schema: getOrganizationsQuerySchema,
  detail: {
    tags: ["organizations"],
    summary: "Get all organizations",
    description: "Retrieve all organizations with search, filter, and pagination",
    security: [{ bearerAuth: [] }],
    responses: {
      "200": {
        description: "Successfully retrieved organizations",
        content: {
          "application/json": {
            schema: {
              $ref: "#/components/schemas/Organization",
            },
            examples: {
              getOrganizationsExample: {
                $ref: "#/components/examples/getOrganizationsExample",
              },
            },
          },
        },
      },
      // Error responses...
    },
  },
})
```

3. **Swagger Integration** (`src/config/swagger.ts`):

```typescript
import { swagger } from "@elysiajs/swagger";
import {
  authExamples,
  authSchemas,
  organizationExamples,
  organizationSchemas,
  // Import your module schemas and examples
} from "./schemas";

export const swaggerConfig = swagger({
  documentation: {
    // Basic info...
    tags: [
      { name: "auth", description: "Authentication endpoints" },
      {
        name: "organizations",
        description: "Organization management endpoints",
      },
      // Add your module's tag
      { name: "your-module", description: "Your module description" },
    ],
    components: {
      // Security schemes...
      examples: {
        ...authExamples,
        ...organizationExamples,
        // Add your module's examples
      },
      schemas: {
        ...authSchemas,
        ...organizationSchemas,
        // Add your module's schemas
      },
    },
  },
  path: "/docs",
  version: "3.1.0",
});
```

### Testing Your Documentation

After implementing your API and documentation:

1. Start your development server:

```bash
bun --watch src/index.ts
```

2. Open the Swagger UI in your browser:

```
http://localhost:3000/docs
```

3. Check that your module's endpoints are properly documented with:
   - Accurate schemas
   - Helpful examples
   - Clear descriptions
   - Authentication requirements

### Best Practices

1. **Keep schemas in sync** with database models and validation schemas
2. **Provide realistic examples** for both successful and error responses
3. **Document all parameters** including query parameters, path parameters, and request bodies
4. **Specify security requirements** for authenticated endpoints
5. **Include descriptions** for all fields and endpoints
6. **Document error responses** to help API consumers handle failures

By following these guidelines, you'll create well-documented APIs that are easy for other developers to understand and use.

## Authentication & Authorization

Our application uses JWT (JSON Web Token) authentication with Supabase. We've implemented a middleware that handles token verification and makes the authenticated user available in your controllers.

### Authentication Middleware

The authentication middleware is defined in `src/middleware/auth.ts`. It:

1. Extracts the JWT token from the Authorization header
2. Verifies the token with Supabase
3. Fetches the user's profile data
4. Makes both the user and profile available in your controllers

```typescript
// Example of the auth middleware (simplified)
export const auth = new Elysia({ name: "auth" })
  .derive({ as: "global" }, async ({ headers }) => {
    // Extract and verify token
    const token = headers.authorization?.split(" ")[1];
    const { data } = await supabase.auth.getUser(token);

    // Get user profile
    const { data: profile } = await UserProfileModel.getByUserId(data.user.id);

    // Return user and profile
    return { user: data.user, profile };
  })
  .onBeforeHandle(({ user, set }) => {
    // Return 401 if user not authenticated
    if (!user) {
      set.status = 401;
      return { error: "Unauthorized" };
    }
  });
```

### Protecting Routes

To protect your routes, use the `protectRoute` function in your route definitions:

```typescript
import { protectRoute } from "../../middleware/auth";

export const yourRoutes = (app: Elysia) =>
  app.group(
    "/your-route",
    (app) =>
      app
        // Apply auth  to all routes in this group
        .use(protectRoute)

        // Your protected routes
        .get("/", YourController.getSomething)
    // ...
  );
```

### Role-Based Access Control (RBAC)

Our application uses a role-based access control system to manage permissions. There are two main approaches to implementing RBAC:

1. Group-level RBAC: Apply role requirements to a group of routes
2. Route-level RBAC: Apply role requirements to individual routes

#### Available Roles

- `Admin`: Full system access (has access to all APIs regardless of role requirements)
- `Manager`: Management-level access
- `HR`: Human resources access
- `Finance`: Financial operations access
- `Operation`: Operational access
- `Client`: Client-specific access

#### Implementation Patterns

##### 1. Group-Level RBAC

Use this approach when multiple routes in a group require the same role(s):

```typescript
app.group("/invoices", (app) =>
  app
    .use(requireActiveUser)
    .use(requireRole([UserRole.Admin])) // Apply to all routes in this group
    .post("/", InvoiceController.create)
    .get("/", InvoiceController.list)
);
```

Note: Admin users will always have access to these routes, regardless of the role requirements.

##### 2. Route-Level RBAC

Use this approach when different routes need different role requirements:

```typescript
app.group("/invoices", (app) =>
  app
    .use(requireActiveUser)
    .post("/", InvoiceController.create, {
      beforeHandle: [checkRoles([UserRole.Admin])],
    })
    .get("/", InvoiceController.list, {
      beforeHandle: [checkRoles([UserRole.Admin, UserRole.Manager])],
    })
);
```

Note: Admin users will always have access to these routes, regardless of the role requirements.

#### Best Practices

1. **Always Use requireActiveUser**

   - Apply `requireActiveUser` middleware first to ensure the user is authenticated and active
   - This is required for both group-level and route-level RBAC

2. **Choose the Right Approach**

   - Use group-level RBAC when multiple routes share the same role requirements
   - Use route-level RBAC when different routes need different role requirements
   - Route-level RBAC is more flexible but requires more code

3. **Multiple Roles**

   - You can specify multiple roles using an array: `[UserRole.Admin, UserRole.Manager]`
   - The user must have at least one of the specified roles
   - Note: Admin users automatically have access to all routes

4. **Predefined Role Shortcuts**

   - Use predefined shortcuts for common role combinations:
     ```typescript
     managerOnly(app);
     employeesOnly(app);
     clientsOnly(app);
     hrAndManagerOnly(app);
     financeAndManagerOnly(app);
     ```
   - Note: Admin users automatically have access to all routes, even when using these shortcuts

5. **Custom Role Combinations**
   - Use `customRoles([...])` for custom role combinations:
     ```typescript
     customRoles([UserRole.Admin, UserRole.Finance])(app);
     ```
   - Note: Admin users automatically have access to all routes, even with custom role combinations

#### Example Implementation

Here's a complete example showing both approaches:

```typescript
// Group-level RBAC for admin-only routes
app.group("/admin", (app) =>
  app
    .use(requireActiveUser)
    .use(requireRole([UserRole.Admin]))
    .post("/users", UserController.create)
    .delete("/users/:id", UserController.delete)
);

// Route-level RBAC for mixed access
app.group("/invoices", (app) =>
  app
    .use(requireActiveUser)
    // Only admins can create invoices
    .post("/", InvoiceController.create, {
      beforeHandle: [checkRoles([UserRole.Admin])],
    })
    // Admins and managers can list invoices
    .get("/", InvoiceController.list, {
      beforeHandle: [checkRoles([UserRole.Admin, UserRole.Manager])],
    })
    // Finance and managers can view invoice details
    .get("/:id", InvoiceController.getById, {
      beforeHandle: [checkRoles([UserRole.Finance, UserRole.Manager])],
    })
);
```

#### Error Handling

When a user doesn't have the required role(s), they will receive a 403 Forbidden response with:

```json
{
  "success": false,
  "message": "You do not have permission to access this resource",
  "data": null,
  "error": {
    "code": "INSUFFICIENT_ROLE"
  }
}
```

### Accessing Authenticated User Data

In your controller methods, the authenticated user and profile are available in the context:

```typescript
static async yourMethod({ user, profile }) {
  // The user object contains auth information
  const userId = user.id;
  const userEmail = user.email;

  // The profile object contains additional user data
  const fullname = profile.fullname;
  const role = profile.role;

  // Use this information to perform operations
  const result = await YourService.doSomething(userId);

  return {
    success: true,
    data: result
  };
}
```

### Row Level Security (RLS)

Row Level Security (RLS) is a PostgreSQL feature that allows you to define security policies that restrict which rows a user can access. When combined with our RBAC system, RLS provides an additional layer of security at the database level.

#### Overview

RLS works by:

1. Enabling RLS on a table
2. Creating policies that define access rules
3. Automatically applying these rules to all queries

Benefits of using RLS with RBAC:

- Database-level security enforcement
- Protection against unauthorized access
- Automatic filtering of data
- No need to modify application queries

#### Implementation Patterns

##### 1. Basic RLS Setup

First, enable RLS on your table:

```sql
-- Enable RLS on a table
ALTER TABLE public.your_table ENABLE ROW LEVEL SECURITY;

-- Create a basic policy
CREATE POLICY "Users can only view their own data" ON public.your_table
  FOR SELECT
  USING (auth.uid() = created_by);
```

##### 2. Role-Specific Policies

Create policies based on user roles:

```sql
-- Policy for clients to see only their organization's data
CREATE POLICY "Clients can only view their organization's data" ON public.your_table
  FOR SELECT
  USING (
    auth.uid() IN (
      SELECT user_id FROM public.user_profiles
      WHERE role = 'Client' AND org_id = your_table.org_id
    )
  );

-- Policy for employees to see only their assigned data
CREATE POLICY "Employees can only view their assigned data" ON public.your_table
  FOR SELECT
  USING (
    auth.uid() IN (
      SELECT user_id FROM public.user_profiles
      WHERE role IN ('Operation', 'HR', 'Finance')
      AND employee_id = your_table.employee_id
    )
  );

-- Policy for managers to see all data
CREATE POLICY "Managers can view all data" ON public.your_table
  FOR SELECT
  USING (
    auth.uid() IN (
      SELECT user_id FROM public.user_profiles
      WHERE role = 'Manager'
    )
  );
```

##### 3. Operation-Specific Policies

Define different policies for different operations:

```sql
-- Insert policies
CREATE POLICY "Operations staff can create records for themselves" ON public.your_table
  FOR INSERT
  WITH CHECK (
    auth.uid() IN (
      SELECT user_id FROM public.user_profiles
      WHERE role = 'Operation'
      AND employee_id = your_table.employee_id
    )
  );

-- Update policies
CREATE POLICY "Clients can update their organization's data" ON public.your_table
  FOR UPDATE
  USING (
    auth.uid() IN (
      SELECT user_id FROM public.user_profiles
      WHERE role = 'Client'
      AND org_id = your_table.org_id
    )
  );

-- Delete policies
CREATE POLICY "Only managers can delete records" ON public.your_table
  FOR DELETE
  USING (
    auth.uid() IN (
      SELECT user_id FROM public.user_profiles
      WHERE role = 'Manager'
    )
  );
```

##### 4. Using Views and Functions with RLS

Create role-specific views and helper functions:

```sql
-- Role-specific view
CREATE OR REPLACE VIEW client_data AS
SELECT t.*
FROM public.your_table t
JOIN public.user_profiles up ON up.user_id = auth.uid()
WHERE up.role = 'Client' AND up.org_id = t.org_id;

-- Smart function that returns appropriate data based on role
CREATE OR REPLACE FUNCTION get_user_data()
RETURNS SETOF public.your_table AS $$
DECLARE
  user_role TEXT;
BEGIN
  -- Get the current user's role
  SELECT role INTO user_role
  FROM public.user_profiles
  WHERE user_id = auth.uid();

  -- Return the appropriate data based on role
  CASE user_role
    WHEN 'Manager' THEN
      RETURN QUERY SELECT * FROM manager_data;
    WHEN 'Client' THEN
      RETURN QUERY SELECT * FROM client_data;
    WHEN 'Operation' THEN
      RETURN QUERY SELECT * FROM operation_data;
    -- ... other roles ...
  END CASE;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;
```

#### Best Practices

1. **Policy Design**

   - Keep policies simple and focused
   - Use appropriate policy types (USING vs WITH CHECK)
   - Consider policy precedence
   - Document policy purposes

2. **Security Considerations**

   - Test policies thoroughly
   - Audit access patterns
   - Handle edge cases
   - Consider cascading effects

3. **Performance Optimization**
   - Use appropriate indexes
   - Keep policy conditions simple
   - Avoid complex joins in policies
   - Monitor query performance

#### Example Implementation

Here's a complete example for a projects table:

```sql
-- Enable RLS
ALTER TABLE public.projects ENABLE ROW LEVEL SECURITY;

-- Basic policies
CREATE POLICY "Users can view their own projects" ON public.projects
  FOR SELECT
  USING (auth.uid() = created_by);

-- Role-specific policies
CREATE POLICY "Clients can view organization projects" ON public.projects
  FOR SELECT
  USING (
    auth.uid() IN (
      SELECT user_id FROM public.user_profiles
      WHERE role = 'Client' AND org_id = projects.org_id
    )
  );

CREATE POLICY "Managers can view all projects" ON public.projects
  FOR SELECT
  USING (
    auth.uid() IN (
      SELECT user_id FROM public.user_profiles
      WHERE role = 'Manager'
    )
  );

-- Operation-specific policies
CREATE POLICY "Managers can create projects" ON public.projects
  FOR INSERT
  WITH CHECK (
    auth.uid() IN (
      SELECT user_id FROM public.user_profiles
      WHERE role = 'Manager'
    )
  );

CREATE POLICY "Managers can update projects" ON public.projects
  FOR UPDATE
  USING (
    auth.uid() IN (
      SELECT user_id FROM public.user_profiles
      WHERE role = 'Manager'
    )
  );

-- Using RLS in your application code
static async getProjects() {
  // RLS automatically applies policies
  return dbUtils.getAll<Project>("projects");
}

static async createProject(data: CreateProjectDto) {
  // RLS checks insert permissions
  return dbUtils.create<Project>("projects", data);
}
```

#### Testing RLS Policies

1. **Basic Testing**

   ```sql
   -- Test as different roles
   SET ROLE client_user;
   SELECT * FROM projects; -- Should only see client's projects

   SET ROLE manager_user;
   SELECT * FROM projects; -- Should see all projects
   ```

2. **Testing Different Operations**

   ```sql
   -- Test insert
   SET ROLE client_user;
   INSERT INTO projects (...) VALUES (...); -- Should fail

   SET ROLE manager_user;
   INSERT INTO projects (...) VALUES (...); -- Should succeed

   -- Test update
   SET ROLE client_user;
   UPDATE projects SET ... WHERE id = '...'; -- Should fail

   SET ROLE manager_user;
   UPDATE projects SET ... WHERE id = '...'; -- Should succeed
   ```

3. **Debugging Tips**
   - Use `EXPLAIN` to see how policies affect queries
   - Check policy precedence
   - Verify user roles and permissions
   - Monitor query performance

## Testing Your API

You can test your API using tools like Postman, cURL, or JavaScript fetch. Here's an example of testing with cURL:

```bash
# Get JWT token by signing in
curl -X POST "http://localhost:3000/auth/sign-in" \
  -H "Content-Type: application/json" \
  -d '{"email":"<EMAIL>","password":"yourpassword"}'

# The response will include a JWT token
# Use this token in subsequent requests

# Get all tasks (protected route)
curl -X GET "http://localhost:3000/tasks" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"

# Create a new task
curl -X POST "http://localhost:3000/tasks" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -d '{"title":"New Task","description":"Task description","priority":"high"}'

# Get a specific task
curl -X GET "http://localhost:3000/tasks/TASK_ID" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"

# Update a task
curl -X PUT "http://localhost:3000/tasks/TASK_ID" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -d '{"status":"done"}'

# Delete a task
curl -X DELETE "http://localhost:3000/tasks/TASK_ID" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"

# Get your user profile
curl -X GET "http://localhost:3000/auth/me" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"
```

## Pull Request Guidelines

When submitting a pull request for your new API:

1. **Description**: Provide a clear description of what your API does
2. **Testing**: Explain how you've tested your API
3. **Documentation**: Include documentation for your API endpoints
4. **Code Style**: Ensure your code follows the project's style guidelines
5. **Migrations**: Include database migrations if needed
6. **Dependencies**: List any new dependencies you've added

By following these guidelines, your pull request will be more likely to be accepted quickly.

---

This guide should help you create a complete API module for our project. If you have any questions or need further clarification, please reach out to the team.
