# Invoice Payment Proof Feature

This document describes the invoice payment proof feature, which allows Staff Finance users to upload and view payment proof documents for invoices.

## Overview

The invoice payment proof feature provides the following functionality:

1. Upload payment proof documents for invoices
2. View all payment proofs for an invoice
3. Delete payment proofs

## API Endpoints

### Upload Payment Proof

```http
POST /api/v1/invoices/:id/payment-proofs
```

**Request Body:**

- `file`: The payment proof document file (required)
- `notes`: Additional notes about the payment proof (optional)

**Response:**

```json
{
  "success": true,
  "message": "Payment proof uploaded successfully",
  "data": {
    "id": "123e4567-e89b-12d3-a456-426614174000",
    "invoice_id": "123e4567-e89b-12d3-a456-426614174001",
    "file_path": "123e4567-e89b-12d3-a456-426614174001/1620000000000_payment_receipt.pdf",
    "file_name": "payment_receipt.pdf",
    "file_type": "application/pdf",
    "file_size": 1024000,
    "notes": "Payment receipt for invoice #001/ORDEV/Kasuat/III/2025",
    "created_at": "2025-03-01T00:00:00.000Z",
    "created_by": "auth0|123456789",
    "updated_at": null,
    "updated_by": null,
    "deleted_at": null,
    "deleted_by": null
  }
}
```

### Get Payment Proofs for an Invoice

```http
GET /api/v1/invoices/:id/payment-proofs
```

**Query Parameters:**

- `page`: Page number (default: 1)
- `pageSize`: Number of items per page (default: 10)

**Response:**

```json
{
  "success": true,
  "message": "Payment proofs retrieved successfully",
  "data": {
    "items": [
      {
        "id": "123e4567-e89b-12d3-a456-426614174000",
        "invoice_id": "123e4567-e89b-12d3-a456-426614174001",
        "file_path": "123e4567-e89b-12d3-a456-426614174001/1620000000000_payment_receipt.pdf",
        "file_name": "payment_receipt.pdf",
        "file_type": "application/pdf",
        "file_size": 1024000,
        "notes": "Payment receipt for invoice #001/ORDEV/Kasuat/III/2025",
        "download_url": "https://example.com/download/signed-url",
        "created_at": "2025-03-01T00:00:00.000Z",
        "created_by": "auth0|123456789",
        "updated_at": null,
        "updated_by": null,
        "deleted_at": null,
        "deleted_by": null
      }
    ],
    "pagination": {
      "page": 1,
      "pageSize": 10,
      "totalItems": 1,
      "totalPages": 1
    }
  }
}
```

### Delete Payment Proof

```http
DELETE /api/v1/invoices/:id/payment-proofs/:proofId
```

**Response:**

```json
{
  "success": true,
  "message": "Payment proof deleted successfully",
  "data": null
}
```

## Supabase Storage Setup

Before using this feature, you need to set up Supabase Storage:

1. **Create Storage Bucket**:

   - Log in to the Supabase dashboard
   - Navigate to the Storage section
   - Click "Create bucket"
   - Enter "invoice-payment-proofs" as the bucket name
   - Select "Private" for bucket type
   - Click "Create bucket"

2. **Configure CORS**:

   - In the Supabase dashboard, go to Storage > Policies
   - Click on "CORS" tab
   - Add the following configuration:
     ```json
     {
       "origin": "*",
       "methods": ["GET", "POST", "PUT", "DELETE"],
       "headers": ["Content-Type", "Authorization"],
       "maxAgeSeconds": 3600
     }
     ```
   - For production, replace "\*" with your application domain

3. **Add Storage Policies**:

   - In the Supabase dashboard, go to Storage > Policies
   - Click on the "invoice-payment-proofs" bucket
   - Add the following policies:

   **For SELECT operations (download files)**:

   - Policy name: "Allow Finance and Manager to download files"
   - Policy definition:
     ```sql
     (auth.uid() IN (
       SELECT user_id FROM user_profiles
       WHERE role IN ('finance', 'manager')
       AND deleted_at IS NULL
     ))
     ```

   **For INSERT operations (upload files)**:

   - Policy name: "Allow Finance and Manager to upload files"
   - Policy definition:
     ```sql
     (auth.uid() IN (
       SELECT user_id FROM user_profiles
       WHERE role IN ('finance', 'manager')
       AND deleted_at IS NULL
     ))
     ```

   **For DELETE operations (delete files)**:

   - Policy name: "Allow Finance and Manager to delete files"
   - Policy definition:
     ```sql
     (auth.uid() IN (
       SELECT user_id FROM user_profiles
       WHERE role IN ('finance', 'manager')
       AND deleted_at IS NULL
     ))
     ```

## User Flow

1. **Upload Payment Proof**:

   - Finance user views an invoice
   - Clicks "Upload Payment Proof" button
   - Selects a file (receipt, bank transfer confirmation, etc.)
   - Optionally adds notes about the payment
   - Submits the form

2. **View Payment Proofs**:

   - Finance user views an invoice
   - Sees a list of attached payment proofs with download links
   - Can download/view any proof by clicking on it

3. **Delete Payment Proof**:
   - Finance user views the list of payment proofs
   - Clicks "Delete" button next to a proof
   - Confirms deletion

## Manual Testing

To manually test the invoice payment proof feature, you can use the following API requests:

### 1. Upload a Payment Proof

```bash
# Using curl
curl -X POST \
  "http://localhost:3000/v1/invoices/YOUR_INVOICE_ID/payment-proofs" \
  -H "Authorization: Bearer YOUR_AUTH_TOKEN" \
  -F "file=@/path/to/your/file.pdf" \
  -F "notes=Payment receipt for invoice"

# Using Postman
# 1. Create a new POST request to http://localhost:3000/v1/invoices/YOUR_INVOICE_ID/payment-proofs
# 2. Add Authorization header with Bearer token
# 3. In the Body tab, select form-data
# 4. Add a file field named "file" and select your file
# 5. Add a text field named "notes" with your notes
# 6. Send the request
```

### 2. Get Payment Proofs for an Invoice

```bash
# Using curl
curl -X GET \
  "http://localhost:3000/v1/invoices/YOUR_INVOICE_ID/payment-proofs" \
  -H "Authorization: Bearer YOUR_AUTH_TOKEN"

# Using Postman
# 1. Create a new GET request to http://localhost:3000/v1/invoices/YOUR_INVOICE_ID/payment-proofs
# 2. Add Authorization header with Bearer token
# 3. Send the request
```

### 3. Delete a Payment Proof

```bash
# Using curl
curl -X DELETE \
  "http://localhost:3000/v1/invoices/YOUR_INVOICE_ID/payment-proofs/YOUR_PROOF_ID" \
  -H "Authorization: Bearer YOUR_AUTH_TOKEN"

# Using Postman
# 1. Create a new DELETE request to http://localhost:3000/v1/invoices/YOUR_INVOICE_ID/payment-proofs/YOUR_PROOF_ID
# 2. Add Authorization header with Bearer token
# 3. Send the request
```
