# Swagger Documentation Guide

This guide provides instructions for maintaining and updating the Swagger documentation for the API.

## Why Document Your API?

Proper API documentation is crucial for:
- Helping other developers understand and use your API
- Providing a clear contract between frontend and backend
- Making it easier to test and debug endpoints
- Ensuring consistency across the codebase

## Swagger Documentation Checklist

When adding a new module or updating an existing one, ensure the following steps are completed:

### 1. Schema Definitions

- [ ] Create a schema file in `src/config/schemas/your-module/schemas.ts`
- [ ] Define all entity schemas (e.g., YourEntity, CreateYourEntityDto, UpdateYourEntityDto)
- [ ] Define examples for all operations (create, read, update, delete)
- [ ] Export schemas and examples separately

Example:
```typescript
// Define module schemas for Swagger documentation
export const yourModuleSchemas = {
  YourEntity: {
    type: "object" as const,
    required: ["id", "name", "created_at", "created_by"],
    properties: {
      id: {
        type: "string" as const,
        format: "uuid",
        description: "Unique identifier",
      },
      // Other properties...
    },
  },
  
  CreateYourEntityDto: {
    // Schema for creating an entity
  },
  
  UpdateYourEntityDto: {
    // Schema for updating an entity
  },
};

// Define examples separately
export const yourModuleExamples = {
  createYourEntityExample: {
    summary: "Create entity example",
    value: {
      // Example data
    },
  },
  
  updateYourEntityExample: {
    // Example for update operation
  },
};
```

### 2. Schema Integration

- [ ] Create an index file in `src/config/schemas/your-module/index.ts` to re-export schemas
- [ ] Add your module to the main schemas index at `src/config/schemas/index.ts`

Example for `src/config/schemas/your-module/index.ts`:
```typescript
// Re-export module schemas
export * from "./schemas";
```

Example for updating `src/config/schemas/index.ts`:
```typescript
// Re-export all module schemas
export * from "./auth";
export * from "./admin";
// Other modules...
export * from "./your-module"; // Add your module
```

### 3. Swagger Configuration

- [ ] Import your schemas and examples in `src/config/swagger.ts`
- [ ] Add your module's tag to the tags array
- [ ] Include your schemas and examples in the components section

Example for updating `src/config/swagger.ts`:
```typescript
import {
  // Existing imports...
  yourModuleSchemas,
  yourModuleExamples,
} from "./schemas";

export const swaggerConfig = swagger({
  documentation: {
    // Existing configuration...
    tags: [
      // Existing tags...
      {
        name: "your-module",
        description: "Your module description",
      },
    ],
    components: {
      // Existing components...
      examples: {
        // Existing examples...
        ...yourModuleExamples,
      },
      schemas: {
        // Existing schemas...
        ...yourModuleSchemas,
      },
    },
  },
});
```

### 4. Route Documentation

- [ ] Add `detail` objects to all routes with:
  - [ ] `summary`: Brief description of the endpoint
  - [ ] `tags`: Array of tags for categorization
  - [ ] `description`: Detailed description (optional)
  - [ ] `security`: Security requirements (if applicable)

Example:
```typescript
.post("/", YourController.create, {
  body: createYourEntitySchema,
  detail: {
    summary: "Create a new entity",
    tags: ["your-module"],
    description: "Creates a new entity with the provided details",
    security: [{ bearerAuth: [] }],
  },
})
```

### 5. Testing

- [ ] Verify that your module appears in the Swagger UI at `/docs`
- [ ] Test that all endpoints are properly documented
- [ ] Ensure examples and schemas are correctly displayed
- [ ] Check that the API can be tested directly from the Swagger UI

## Best Practices

1. **Be Consistent**: Follow the same patterns used in other modules
2. **Be Descriptive**: Provide clear descriptions for all properties and endpoints
3. **Include Validation Rules**: Document min/max lengths, formats, and other constraints
4. **Document Responses**: Include examples of successful and error responses
5. **Keep Documentation Updated**: Update documentation when endpoints change
6. **Document Error Responses**: Include possible error codes and messages

## Response Documentation

For each endpoint, consider documenting:

1. **Success Response**: What a successful response looks like
2. **Error Responses**: Common error scenarios and their responses
3. **Response Codes**: HTTP status codes that might be returned

## Regular Maintenance

Schedule regular reviews of the API documentation to ensure it remains accurate and up-to-date. Remove documentation for deprecated endpoints and update documentation when endpoints change.

## Additional Resources

- [OpenAPI Specification](https://swagger.io/specification/)
- [Elysia Swagger Documentation](https://elysiajs.com/plugins/swagger.html)
