# KPI Module Improvements

This document outlines recommended improvements for the KPI module endpoints based on a comprehensive analysis of the current implementation.

## Status Endpoint (PATCH /kpi/:id/status)

### Priority 0 (Critical)
1. **Implement Status Transition Rules**
   - Add validation to ensure status changes follow a logical progression
   - Create a state machine for allowed transitions

```typescript
// In KpiService.updateKpiStatus:
// Define valid transitions
const validTransitions = {
  [KpiStatus.NOT_STARTED]: [KpiStatus.IN_PROGRESS, KpiStatus.CANCELLED],
  [KpiStatus.IN_PROGRESS]: [
    KpiStatus.COMPLETED_BELOW_TARGET, 
    KpiStatus.COMPLETED_ON_TARGET, 
    KpiStatus.COMPLETED_ABOVE_TARGET,
    KpiStatus.CANCELLED
  ],
  // Completed states can't transition (or only to other completed states)
  [KpiStatus.COMPLETED_BELOW_TARGET]: [KpiStatus.COMPLETED_ON_TARGET, KpiStatus.COMPLETED_ABOVE_TARGET],
  [KpiStatus.COMPLETED_ON_TARGET]: [KpiStatus.COMPLETED_BELOW_TARGET, KpiStatus.COMPLETED_ABOVE_TARGET],
  [KpiStatus.COMPLETED_ABOVE_TARGET]: [KpiStatus.COMPLETED_BELOW_TARGET, KpiStatus.COMPLETED_ON_TARGET],
  [KpiStatus.CANCELLED]: [] // Terminal state
};

// Validate transition
if (existingKpi.status !== status) {
  const allowedNextStates = validTransitions[existingKpi.status] || [];
  if (!allowedNextStates.includes(status)) {
    throw new Error(`Invalid status transition from ${existingKpi.status} to ${status}`);
  }
}
```

### Priority 1 (High)
1. **Implement Performance Assessment Logic**
   - Add fields for tracking actual vs target performance
   - Provide helpers to calculate appropriate status based on metrics

```typescript
// New schema fields:
export const updateKpiStatusSchema = {
  // ... existing schema
  body: t.Object({
    status: kpiStatusSchema,
    actual_value: t.Optional(t.Number()),
    performance_percentage: t.Optional(t.Number())
  }),
};

// In service:
async calculateKpiStatus(targetValue: number, actualValue: number): Promise<KpiStatus> {
  const performancePercentage = (actualValue / targetValue) * 100;
  
  if (performancePercentage < 90) {
    return KpiStatus.COMPLETED_BELOW_TARGET;
  } else if (performancePercentage >= 90 && performancePercentage <= 110) {
    return KpiStatus.COMPLETED_ON_TARGET;
  } else {
    return KpiStatus.COMPLETED_ABOVE_TARGET;
  }
}
```

### Priority 2 (Medium)
1. **Add Status Change History**
   - Create a new table to track status changes over time
   - Record reason for each status change

```typescript
// New migration file for history table:
CREATE TABLE IF NOT EXISTS public.kpi_history (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  kpi_id UUID NOT NULL REFERENCES public.kpi(id),
  field_name TEXT NOT NULL,
  old_value TEXT,
  new_value TEXT NOT NULL,
  changed_by UUID REFERENCES auth.users(id),
  changed_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
  comments TEXT
);
```

2. **Add Comments Field for Status Changes**
   - Allow users to provide context for status changes

```typescript
export const updateKpiStatusSchema = {
  // ... existing schema
  body: t.Object({
    status: kpiStatusSchema,
    comments: t.Optional(t.String({
      maxLength: 500,
      description: "Explanation for the status change"
    }))
  }),
};
```

## Bonus Endpoint (PATCH /kpi/:id/bonus)

### Priority 0 (Critical)
1. **Implement Status-Based Bonus Validation**
   - Only allow bonuses to be set on completed KPIs
   - Check KPI status before updating bonus

```typescript
// In KpiService.updateKpiBonus:
// Check if KPI is in a completed state
const completedStatuses = [
  KpiStatus.COMPLETED_BELOW_TARGET,
  KpiStatus.COMPLETED_ON_TARGET, 
  KpiStatus.COMPLETED_ABOVE_TARGET
];

if (!completedStatuses.includes(existingKpi.status)) {
  throw new Error("Bonus can only be set for completed KPIs");
}
```

### Priority 1 (High)
1. **Add Bonus Calculation Helpers**
   - Provide suggested bonus amounts based on performance metrics

```typescript
// In service:
async calculateSuggestedBonus(kpi: Kpi): Promise<number> {
  // Simple calculation based on status
  switch (kpi.status) {
    case KpiStatus.COMPLETED_BELOW_TARGET:
      return 0; // No bonus for below target
    case KpiStatus.COMPLETED_ON_TARGET:
      return 500; // Base bonus amount
    case KpiStatus.COMPLETED_ABOVE_TARGET:
      // If we have performance data, scale bonus based on that
      if (kpi.performance_percentage && kpi.performance_percentage > 110) {
        const extraPerformance = kpi.performance_percentage - 110;
        // 10% extra per 10% over-performance, up to 50% extra
        const extraBonus = Math.min(0.5, extraPerformance / 100);
        return 500 * (1 + extraBonus);
      }
      return 750; // Default higher bonus amount
    default:
      return 0;
  }
}
```

2. **Add Budget Validation**
   - Check against team/department bonus budgets
   - Prevent exceeding allocated bonus budgets

### Priority 2 (Medium)
1. **Add Justification Field for Bonuses**
   - Require explanation for bonus amounts
   - Track who approved the bonus

```typescript
export const updateKpiBonusSchema = {
  // ... existing schema
  body: t.Object({
    bonus_received: bonusReceivedSchema,
    justification: t.Optional(t.String({
      maxLength: 500,
      description: "Justification for the bonus amount"
    }))
  }),
};
```

2. **Add Bonus History Tracking**
   - Record changes to bonus amounts over time
   - Use the same history table as status changes

### Priority 3 (Low)
1. **Implement Approval Workflow**
   - Add multi-step approval process for bonuses
   - Require higher-level approval for large bonuses

```typescript
// New status field for bonus approval
export enum BonusApprovalStatus {
  PENDING = "pending",
  APPROVED = "approved",
  REJECTED = "rejected"
}

// Update schema
export const updateKpiBonusSchema = {
  // ... existing schema
  body: t.Object({
    bonus_received: bonusReceivedSchema,
    approval_status: t.Optional(t.Enum(BonusApprovalStatus)),
    approver_id: t.Optional(t.String()),
    justification: t.Optional(t.String())
  }),
};
```

2. **Integrate with Notification System**
   - Send notifications on bonus updates
   - Alert managers when bonuses need approval

## General Improvements

1. **Add API Documentation**
   - Improve endpoint descriptions
   - Document request/response examples

2. **Add Business Rules Documentation**
   - Document valid status transitions
   - Explain bonus calculation rules

3. **Implement End-to-end Testing**
   - Add comprehensive test suite
   - Include business rule validation tests 