[phases.setup]
nixPkgs = ["unzip", "bun"]

[phases.install]
cmds = ["bun install"]

[phases.build]
cmds = ["bun install --production"]

[start]
cmd = "bun run src/index.ts"

[variables]
BUN_RUNTIME = "1"
PORT = "3000"

# Cache directories for faster builds
[phases.setup.cache]
directories = [
  { name = "bun-packages", path = "node_modules/.bun/install/cache" },
  { name = "node-modules", path = "node_modules" }
]

# Provide information about the app
[nixpacks]
provider = "bun" 