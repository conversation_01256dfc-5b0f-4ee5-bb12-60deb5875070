{"name": "kasuat-ely-supa", "version": "1.0.50", "scripts": {"test": "bun test --preload ./src/tests/setup.ts", "test:watch": "bun test --watch --preload ./src/tests/setup.ts", "test:invoice": "bun test src/tests/invoice/*.test.ts --preload ./src/tests/setup.ts", "dev": "bun run --watch src/index.ts", "db:migrate": "bun run src/database/migrate.ts", "db:migrate-new": "bun run src/database/migrate-new.ts", "db:seed": "bun run src/database/seed.ts", "create:invoices": "bun run scripts/create-invoices.ts"}, "dependencies": {"@elysiajs/cookie": "^0.3.0", "@elysiajs/cors": "^1.2.0", "@elysiajs/swagger": "^1.2.2", "@otherguy/elysia-logging": "^0.0.17", "@rasla/logify": "^5.0.0", "@supabase/supabase-js": "^2.49.1", "@types/uuid": "^10.0.0", "date-fns": "^4.1.0", "date-fns-tz": "^3.2.0", "dotenv": "^16.4.7", "elysia": "^1.2.25", "uuid": "^11.1.0"}, "devDependencies": {"bun-types": "latest"}, "module": "src/index.js", "packageManager": "pnpm@10.6.2+sha512.47870716bea1572b53df34ad8647b42962bc790ce2bf4562ba0f643237d7302a3d6a8ecef9e4bdfc01d23af1969aa90485d4cebb0b9638fa5ef1daef656f6c1b"}