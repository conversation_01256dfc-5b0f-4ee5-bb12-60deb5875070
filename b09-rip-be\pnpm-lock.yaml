lockfileVersion: '9.0'

settings:
  autoInstallPeers: true
  excludeLinksFromLockfile: false

importers:

  .:
    dependencies:
      '@elysiajs/cookie':
        specifier: ^0.3.0
        version: 0.3.0(elysia@1.2.25(@sinclair/typebox@0.34.33)(openapi-types@12.1.3))
      '@elysiajs/cors':
        specifier: ^1.2.0
        version: 1.2.0(elysia@1.2.25(@sinclair/typebox@0.34.33)(openapi-types@12.1.3))
      '@elysiajs/swagger':
        specifier: ^1.2.2
        version: 1.2.2(elysia@1.2.25(@sinclair/typebox@0.34.33)(openapi-types@12.1.3))
      '@otherguy/elysia-logging':
        specifier: ^0.0.17
        version: 0.0.17(elysia@1.2.25(@sinclair/typebox@0.34.33)(openapi-types@12.1.3))
      '@rasla/logify':
        specifier: ^5.0.0
        version: 5.0.0(elysia@1.2.25(@sinclair/typebox@0.34.33)(openapi-types@12.1.3))
      '@supabase/supabase-js':
        specifier: ^2.49.1
        version: 2.49.4
      date-fns:
        specifier: ^4.1.0
        version: 4.1.0
      date-fns-tz:
        specifier: ^3.2.0
        version: 3.2.0(date-fns@4.1.0)
      dotenv:
        specifier: ^16.4.7
        version: 16.5.0
      elysia:
        specifier: ^1.2.25
        version: 1.2.25(@sinclair/typebox@0.34.33)(openapi-types@12.1.3)
    devDependencies:
      bun-types:
        specifier: latest
        version: 1.2.10

packages:

  '@babel/runtime@7.27.0':
    resolution: {integrity: sha512-VtPOkrdPHZsKc/clNqyi9WUA8TINkZ4cGk63UUE3u4pmB2k+ZMQRDuIOagv8UVd6j7k0T3+RRIb7beKTebNbcw==}
    engines: {node: '>=6.9.0'}

  '@elysiajs/cookie@0.3.0':
    resolution: {integrity: sha512-DFNe7lRidToH4C5c4h2T+JWQFUVV5XHGMj5wmAiWJckMg3c2MJ1SL5ccJG6EPILERP9Wey1NY5ycUMpvn7qECQ==}
    peerDependencies:
      elysia: '>= 0.3.0'

  '@elysiajs/cors@1.2.0':
    resolution: {integrity: sha512-qsJwDAg6WfdQRMfj6uSMcDPSpXvm/zQFeAX1uuJXhIgazH8itSfcDxcH9pMuXVRX1yQNi2pPwNQLJmAcw5mzvw==}
    peerDependencies:
      elysia: '>= 1.2.0'

  '@elysiajs/swagger@1.2.2':
    resolution: {integrity: sha512-DG0PbX/wzQNQ6kIpFFPCvmkkWTIbNWDS7lVLv3Puy6ONklF14B4NnbDfpYjX1hdSYKeCqKBBOuenh6jKm8tbYA==}
    peerDependencies:
      elysia: '>= 1.2.0'

  '@otherguy/elysia-logging@0.0.17':
    resolution: {integrity: sha512-/urPnB+VQ0TjmztVKVEVmUP8+5ld7jW2hWRBEPf/UhE8EF5vUOGSwNR11rmvjG80qVKssd3lwPa/DOfHz6mflg==}
    peerDependencies:
      elysia: '>= 0.7.17'

  '@rasla/logify@5.0.0':
    resolution: {integrity: sha512-MVJswOdqgyQFYmVSXdkfYlsvizccv2x3WG+Pwa3Hnat62mCJJi1l0lh+I26KAruLXxGYLnb4zuPZ4UC/xxJlqg==}
    engines: {bun: '>=1.0.0', node: '>=16.0.0'}
    peerDependencies:
      elysia: '>=0.7.0'

  '@scalar/openapi-types@0.1.1':
    resolution: {integrity: sha512-NMy3QNk6ytcCoPUGJH0t4NNr36OWXgZhA3ormr3TvhX1NDgoF95wFyodGVH8xiHeUyn2/FxtETm8UBLbB5xEmg==}
    engines: {node: '>=18'}

  '@scalar/openapi-types@0.2.0':
    resolution: {integrity: sha512-waiKk12cRCqyUCWTOX0K1WEVX46+hVUK+zRPzAahDJ7G0TApvbNkuy5wx7aoUyEk++HHde0XuQnshXnt8jsddA==}
    engines: {node: '>=18'}

  '@scalar/themes@0.9.86':
    resolution: {integrity: sha512-QUHo9g5oSWi+0Lm1vJY9TaMZRau8LHg+vte7q5BVTBnu6NuQfigCaN+ouQ73FqIVd96TwMO6Db+dilK1B+9row==}
    engines: {node: '>=18'}

  '@scalar/types@0.0.12':
    resolution: {integrity: sha512-XYZ36lSEx87i4gDqopQlGCOkdIITHHEvgkuJFrXFATQs9zHARop0PN0g4RZYWj+ZpCUclOcaOjbCt8JGe22mnQ==}
    engines: {node: '>=18'}

  '@scalar/types@0.1.7':
    resolution: {integrity: sha512-irIDYzTQG2KLvFbuTI8k2Pz/R4JR+zUUSykVTbEMatkzMmVFnn1VzNSMlODbadycwZunbnL2tA27AXed9URVjw==}
    engines: {node: '>=18'}

  '@sinclair/typebox@0.34.33':
    resolution: {integrity: sha512-5HAV9exOMcXRUxo+9iYB5n09XxzCXnfy4VTNW4xnDv+FgjzAGY989C28BIdljKqmF+ZltUwujE3aossvcVtq6g==}

  '@supabase/auth-js@2.69.1':
    resolution: {integrity: sha512-FILtt5WjCNzmReeRLq5wRs3iShwmnWgBvxHfqapC/VoljJl+W8hDAyFmf1NVw3zH+ZjZ05AKxiKxVeb0HNWRMQ==}

  '@supabase/functions-js@2.4.4':
    resolution: {integrity: sha512-WL2p6r4AXNGwop7iwvul2BvOtuJ1YQy8EbOd0dhG1oN1q8el/BIRSFCFnWAMM/vJJlHWLi4ad22sKbKr9mvjoA==}

  '@supabase/node-fetch@2.6.15':
    resolution: {integrity: sha512-1ibVeYUacxWYi9i0cf5efil6adJ9WRyZBLivgjs+AUpewx1F3xPi7gLgaASI2SmIQxPoCEjAsLAzKPgMJVgOUQ==}
    engines: {node: 4.x || >=6.0.0}

  '@supabase/postgrest-js@1.19.4':
    resolution: {integrity: sha512-O4soKqKtZIW3olqmbXXbKugUtByD2jPa8kL2m2c1oozAO11uCcGrRhkZL0kVxjBLrXHE0mdSkFsMj7jDSfyNpw==}

  '@supabase/realtime-js@2.11.2':
    resolution: {integrity: sha512-u/XeuL2Y0QEhXSoIPZZwR6wMXgB+RQbJzG9VErA3VghVt7uRfSVsjeqd7m5GhX3JR6dM/WRmLbVR8URpDWG4+w==}

  '@supabase/storage-js@2.7.1':
    resolution: {integrity: sha512-asYHcyDR1fKqrMpytAS1zjyEfvxuOIp1CIXX7ji4lHHcJKqyk+sLl/Vxgm4sN6u8zvuUtae9e4kDxQP2qrwWBA==}

  '@supabase/supabase-js@2.49.4':
    resolution: {integrity: sha512-jUF0uRUmS8BKt37t01qaZ88H9yV1mbGYnqLeuFWLcdV+x1P4fl0yP9DGtaEhFPZcwSom7u16GkLEH9QJZOqOkw==}

  '@types/cookie-signature@1.1.2':
    resolution: {integrity: sha512-2OhrZV2LVnUAXklUFwuYUTokalh/dUb8rqt70OW6ByMSxYpauPZ+kfNLknX3aJyjY5iu8i3cUyoLZP9Fn37tTg==}

  '@types/cookie@0.5.4':
    resolution: {integrity: sha512-7z/eR6O859gyWIAjuvBWFzNURmf2oPBmJlfVWkwehU5nzIyjwBsTh7WMmEEV4JFnHuQ3ex4oyTvfKzcyJVDBNA==}

  '@types/node@22.15.2':
    resolution: {integrity: sha512-uKXqKN9beGoMdBfcaTY1ecwz6ctxuJAcUlwE55938g0ZJ8lRxwAZqRz2AJ4pzpt5dHdTPMB863UZ0ESiFUcP7A==}

  '@types/phoenix@1.6.6':
    resolution: {integrity: sha512-PIzZZlEppgrpoT2QgbnDU+MMzuR6BbCjllj0bM70lWoejMeNJAxCchxnv7J3XFkI8MpygtRpzXrIlmWUBclP5A==}

  '@types/pino@7.0.5':
    resolution: {integrity: sha512-wKoab31pknvILkxAF8ss+v9iNyhw5Iu/0jLtRkUD74cNfOOLJNnqfFKAv0r7wVaTQxRZtWrMpGfShwwBjOcgcg==}
    deprecated: This is a stub types definition. pino provides its own type definitions, so you do not need this installed.

  '@types/ws@8.18.1':
    resolution: {integrity: sha512-ThVF6DCVhA8kUGy+aazFQ4kXQ7E1Ty7A3ypFOe0IcJV8O/M511G99AW24irKrW56Wt44yG9+ij8FaqoBGkuBXg==}

  '@unhead/schema@1.11.20':
    resolution: {integrity: sha512-0zWykKAaJdm+/Y7yi/Yds20PrUK7XabLe9c3IRcjnwYmSWY6z0Cr19VIs3ozCj8P+GhR+/TI2mwtGlueCEYouA==}

  abort-controller@3.0.0:
    resolution: {integrity: sha512-h8lQ8tacZYnR3vNQTgibj+tODHI5/+l06Au2Pcriv/Gmet0eaj4TwWH41sO9wnHDiQsEj19q0drzdWdeAHtweg==}
    engines: {node: '>=6.5'}

  atomic-sleep@1.0.0:
    resolution: {integrity: sha512-kNOjDqAh7px0XWNI+4QbzoiR/nTkHAWNud2uvnJquD1/x5a7EQZMJT0AczqK0Qn67oY/TTQ1LbUKajZpp3I9tQ==}
    engines: {node: '>=8.0.0'}

  base64-js@1.5.1:
    resolution: {integrity: sha512-AKpaYlHn8t4SVbOHCy+b5+KKgvR4vrsD8vbvrbiQJps7fKDTkjkDry6ji0rUJjC0kzbNePLwzxq8iypo41qeWA==}

  buffer@6.0.3:
    resolution: {integrity: sha512-FTiCpNxtwiZZHEZbcbTIcZjERVICn9yq/pDFkTl95/AxzD1naBctN7YO68riM/gLSDY7sdrMby8hofADYuuqOA==}

  bun-types@1.2.10:
    resolution: {integrity: sha512-b5ITZMnVdf3m1gMvJHG+gIfeJHiQPJak0f7925Hxu6ZN5VKA8AGy4GZ4lM+Xkn6jtWxg5S3ldWvfmXdvnkp3GQ==}

  chalk@5.4.1:
    resolution: {integrity: sha512-zgVZuo2WcZgfUEmsn6eO3kINexW8RAE4maiQ8QNs8CtpPCSyMiYsULR3HQYkm3w8FIA3SberyMJMSldGsW+U3w==}
    engines: {node: ^12.17.0 || ^14.13 || >=16.0.0}

  cookie-signature@1.2.2:
    resolution: {integrity: sha512-D76uU73ulSXrD1UXF4KE2TMxVVwhsnCgfAyTg9k8P6KGZjlXKrOLe4dJQKI3Bxi5wjesZoFXJWElNWBjPZMbhg==}
    engines: {node: '>=6.6.0'}

  cookie@0.5.0:
    resolution: {integrity: sha512-YZ3GUyn/o8gfKJlnlX7g7xq4gyO6OSuhGPKaaGssGB2qgDUS0gPgtTvoyZLTt9Ab6dC4hfc9dV5arkvc/OCmrw==}
    engines: {node: '>= 0.6'}

  cookie@1.0.2:
    resolution: {integrity: sha512-9Kr/j4O16ISv8zBBhJoi4bXOYNTkFLOqSL3UDB0njXxCXNezjeyVrJyGOWtgfs/q2km1gwBcfH8q1yEGoMYunA==}
    engines: {node: '>=18'}

  date-fns-tz@2.0.1:
    resolution: {integrity: sha512-fJCG3Pwx8HUoLhkepdsP7Z5RsucUi+ZBOxyM5d0ZZ6c4SdYustq0VMmOu6Wf7bli+yS/Jwp91TOCqn9jMcVrUA==}
    peerDependencies:
      date-fns: 2.x

  date-fns-tz@3.2.0:
    resolution: {integrity: sha512-sg8HqoTEulcbbbVXeg84u5UnlsQa8GS5QXMqjjYIhS4abEVVKIUwe0/l/UhrZdKaL/W5eWZNlbTeEIiOXTcsBQ==}
    peerDependencies:
      date-fns: ^3.0.0 || ^4.0.0

  date-fns@2.30.0:
    resolution: {integrity: sha512-fnULvOpxnC5/Vg3NCiWelDsLiUc9bRwAPs/+LfTLNvetFCtCTN+yQz15C/fs4AwX1R9K5GLtLfn8QW+dWisaAw==}
    engines: {node: '>=0.11'}

  date-fns@4.1.0:
    resolution: {integrity: sha512-Ukq0owbQXxa/U3EGtsdVBkR1w7KOQ5gIBqdH2hkvknzZPYvBxb/aa6E8L7tmjFtkwZBu3UXBbjIgPo/Ez4xaNg==}

  dotenv@16.5.0:
    resolution: {integrity: sha512-m/C+AwOAr9/W1UOIZUo232ejMNnJAJtYQjUbHoNTBNTJSvqzzDh7vnrei3o3r3m9blf6ZoDkvcw0VmozNRFJxg==}
    engines: {node: '>=12'}

  elysia@1.2.25:
    resolution: {integrity: sha512-WsdQpORJvb4uszzeqYT0lg97knw1iBW1NTzJ1Jm57tiHg+DfAotlWXYbjmvQ039ssV0fYELDHinLLoUazZkEHg==}
    peerDependencies:
      '@sinclair/typebox': '>= 0.34.0'
      openapi-types: '>= 12.0.0'
      typescript: '>= 5.0.0'
    peerDependenciesMeta:
      openapi-types:
        optional: true
      typescript:
        optional: true

  event-target-shim@5.0.1:
    resolution: {integrity: sha512-i/2XbnSz/uxRCU6+NdVJgKWDTM427+MqYbkQzD321DuCQJUqOuJKIA0IM2+W2xtYHdKOmZ4dR6fExsd4SXL+WQ==}
    engines: {node: '>=6'}

  events@3.3.0:
    resolution: {integrity: sha512-mQw+2fkQbALzQ7V0MY0IqdnXNOeTtP4r0lN9z7AAawCXgqea7bDii20AYrIBrFd/Hx0M2Ocz6S111CaFkUcb0Q==}
    engines: {node: '>=0.8.x'}

  fast-redact@3.5.0:
    resolution: {integrity: sha512-dwsoQlS7h9hMeYUq1W++23NDcBLV4KqONnITDV9DjfS3q1SgDGVrBdvvTLUotWtPSD7asWDV9/CmsZPy8Hf70A==}
    engines: {node: '>=6'}

  hookable@5.5.3:
    resolution: {integrity: sha512-Yc+BQe8SvoXH1643Qez1zqLRmbA5rCL+sSmk6TVos0LWVfNIB7PGncdlId77WzLGSIB5KaWgTaNTs2lNVEI6VQ==}

  ieee754@1.2.1:
    resolution: {integrity: sha512-dcyqhDvX1C46lXZcVqCpK+FtMRQVdIMN6/Df5js2zouUsqG7I6sFxitIC+7KYK29KdXOLHdu9zL4sFnoVQnqaA==}

  memoirist@0.3.0:
    resolution: {integrity: sha512-wR+4chMgVPq+T6OOsk40u9Wlpw1Pjx66NMNiYxCQQ4EUJ7jDs3D9kTCeKdBOkvAiqXlHLVJlvYL01PvIJ1MPNg==}

  nanoid@5.1.5:
    resolution: {integrity: sha512-Ir/+ZpE9fDsNH0hQ3C68uyThDXzYcim2EqcZ8zn8Chtt1iylPT9xXJB0kPCnqzgcEGikO9RxSrh63MsmVCU7Fw==}
    engines: {node: ^18 || >=20}
    hasBin: true

  on-exit-leak-free@2.1.2:
    resolution: {integrity: sha512-0eJJY6hXLGf1udHwfNftBqH+g73EU4B504nZeKpz1sYRKafAghwxEJunB2O7rDZkL4PGfsMVnTXZ2EjibbqcsA==}
    engines: {node: '>=14.0.0'}

  openapi-types@12.1.3:
    resolution: {integrity: sha512-N4YtSYJqghVu4iek2ZUvcN/0aqH1kRDuNqzcycDxhOUpg7GdvLa2F3DgS6yBNhInhv2r/6I0Flkn7CqL8+nIcw==}

  pathe@1.1.2:
    resolution: {integrity: sha512-whLdWMYL2TwI08hn8/ZqAbrVemu0LNaNNJZX73O6qaIdCTfXutsLhMkjdENX0qhsQ9uIimo4/aQOmXkoon2nDQ==}

  pino-abstract-transport@1.2.0:
    resolution: {integrity: sha512-Guhh8EZfPCfH+PMXAb6rKOjGQEoy0xlAIn+irODG5kgfYV+BQ0rGYYWTIel3P5mmyXqkYkPmdIkywsn6QKUR1Q==}

  pino-std-serializers@6.2.2:
    resolution: {integrity: sha512-cHjPPsE+vhj/tnhCy/wiMh3M3z3h/j15zHQX+S9GkTBgqJuTuJzYJ4gUyACLhDaJ7kk9ba9iRDmbH2tJU03OiA==}

  pino@8.21.0:
    resolution: {integrity: sha512-ip4qdzjkAyDDZklUaZkcRFb2iA118H9SgRh8yzTkSQK8HilsOJF7rSY8HoW5+I0M46AZgX/pxbprf2vvzQCE0Q==}
    hasBin: true

  process-warning@3.0.0:
    resolution: {integrity: sha512-mqn0kFRl0EoqhnL0GQ0veqFHyIN1yig9RHh/InzORTUiZHFRAur+aMtRkELNwGs9aNwKS6tg/An4NYBPGwvtzQ==}

  process@0.11.10:
    resolution: {integrity: sha512-cdGef/drWFoydD1JsMzuFf8100nZl+GT+yacc2bEced5f9Rjk4z+WtFUTBu9PhOi9j/jfmBPu0mMEY4wIdAF8A==}
    engines: {node: '>= 0.6.0'}

  quick-format-unescaped@4.0.4:
    resolution: {integrity: sha512-tYC1Q1hgyRuHgloV/YXs2w15unPVh8qfu/qCTfhTYamaw7fyhumKa2yGpdSo87vY32rIclj+4fWYQXUMs9EHvg==}

  readable-stream@4.7.0:
    resolution: {integrity: sha512-oIGGmcpTLwPga8Bn6/Z75SVaH1z5dUut2ibSyAMVhmUggWpmDn2dapB0n7f8nwaSiRtepAsfJyfXIO5DCVAODg==}
    engines: {node: ^12.22.0 || ^14.17.0 || >=16.0.0}

  real-require@0.2.0:
    resolution: {integrity: sha512-57frrGM/OCTLqLOAh0mhVA9VBMHd+9U7Zb2THMGdBUoZVOtGbJzjxsYGDJ3A9AYYCP4hn6y1TVbaOfzWtm5GFg==}
    engines: {node: '>= 12.13.0'}

  regenerator-runtime@0.14.1:
    resolution: {integrity: sha512-dYnhHh0nJoMfnkZs6GmmhFknAGRrLznOu5nc9ML+EJxGvrx6H7teuevqVqCuPcPK//3eDrrjQhehXVx9cnkGdw==}

  safe-buffer@5.2.1:
    resolution: {integrity: sha512-rp3So07KcdmmKbGvgaNxQSJr7bGVSVk5S9Eq1F+ppbRo70+YeaDxkw5Dd8NPN+GD6bjnYm2VuPuCXmpuYvmCXQ==}

  safe-stable-stringify@2.5.0:
    resolution: {integrity: sha512-b3rppTKm9T+PsVCBEOUR46GWI7fdOs00VKZ1+9c1EWDaDMvjQc6tUwuFyIprgGgTcWoVHSKrU8H31ZHA2e0RHA==}
    engines: {node: '>=10'}

  sonic-boom@3.8.1:
    resolution: {integrity: sha512-y4Z8LCDBuum+PBP3lSV7RHrXscqksve/bi0as7mhwVnBW+/wUqKT/2Kb7um8yqcFy0duYbbPxzt89Zy2nOCaxg==}

  split2@4.2.0:
    resolution: {integrity: sha512-UcjcJOWknrNkF6PLX83qcHM6KHgVKNkV62Y8a5uYDVv9ydGQVwAHMKqHdJje1VTWpljG0WYpCDhrCdAOYH4TWg==}
    engines: {node: '>= 10.x'}

  string_decoder@1.3.0:
    resolution: {integrity: sha512-hkRX8U1WjJFd8LsDJ2yQ/wWWxaopEsABU1XfkM8A+j0+85JAGppt16cr1Whg6KIbb4okU6Mql6BOj+uup/wKeA==}

  thread-stream@2.7.0:
    resolution: {integrity: sha512-qQiRWsU/wvNolI6tbbCKd9iKaTnCXsTwVxhhKM6nctPdujTyztjlbUkUTUymidWcMnZ5pWR0ej4a0tjsW021vw==}

  tr46@0.0.3:
    resolution: {integrity: sha512-N3WMsuqV66lT30CrXNbEjx4GEwlow3v6rr4mCcv6prnfwhS01rkgyFdjPNBYd9br7LpXV1+Emh01fHnq2Gdgrw==}

  type-fest@4.40.0:
    resolution: {integrity: sha512-ABHZ2/tS2JkvH1PEjxFDTUWC8dB5OsIGZP4IFLhR293GqT5Y5qB1WwL2kMPYhQW9DVgVD8Hd7I8gjwPIf5GFkw==}
    engines: {node: '>=16'}

  undici-types@6.21.0:
    resolution: {integrity: sha512-iwDZqg0QAGrg9Rav5H4n0M64c3mkR59cJ6wQp+7C4nI0gsmExaedaYLNO44eT4AtBBwjbTiGPMlt2Md0T9H9JQ==}

  webidl-conversions@3.0.1:
    resolution: {integrity: sha512-2JAn3z8AR6rjK8Sm8orRC0h/bcl/DqL7tRPdGZ4I1CjdF+EaMLmYxBHyXuKL849eucPFhvBoxMsflfOb8kxaeQ==}

  whatwg-url@5.0.0:
    resolution: {integrity: sha512-saE57nupxk6v3HY35+jzBwYa0rKSy0XR8JSxZPwgLr7ys0IBzhGviA1/TUGJLmSVqs8pb9AnvICXEuOHLprYTw==}

  ws@8.18.1:
    resolution: {integrity: sha512-RKW2aJZMXeMxVpnZ6bck+RswznaxmzdULiBr6KY7XkTnW8uvt0iT9H5DkHUChXrc+uurzwa0rVI16n/Xzjdz1w==}
    engines: {node: '>=10.0.0'}
    peerDependencies:
      bufferutil: ^4.0.1
      utf-8-validate: '>=5.0.2'
    peerDependenciesMeta:
      bufferutil:
        optional: true
      utf-8-validate:
        optional: true

  zhead@2.2.4:
    resolution: {integrity: sha512-8F0OI5dpWIA5IGG5NHUg9staDwz/ZPxZtvGVf01j7vHqSyZ0raHY+78atOVxRqb73AotX22uV1pXt3gYSstGag==}

  zod@3.24.3:
    resolution: {integrity: sha512-HhY1oqzWCQWuUqvBFnsyrtZRhyPeR7SUGv+C4+MsisMuVfSPx8HpwWqH8tRahSlt6M3PiFAcoeFhZAqIXTxoSg==}

snapshots:

  '@babel/runtime@7.27.0':
    dependencies:
      regenerator-runtime: 0.14.1

  '@elysiajs/cookie@0.3.0(elysia@1.2.25(@sinclair/typebox@0.34.33)(openapi-types@12.1.3))':
    dependencies:
      '@types/cookie': 0.5.4
      '@types/cookie-signature': 1.1.2
      cookie: 0.5.0
      cookie-signature: 1.2.2
      elysia: 1.2.25(@sinclair/typebox@0.34.33)(openapi-types@12.1.3)

  '@elysiajs/cors@1.2.0(elysia@1.2.25(@sinclair/typebox@0.34.33)(openapi-types@12.1.3))':
    dependencies:
      elysia: 1.2.25(@sinclair/typebox@0.34.33)(openapi-types@12.1.3)

  '@elysiajs/swagger@1.2.2(elysia@1.2.25(@sinclair/typebox@0.34.33)(openapi-types@12.1.3))':
    dependencies:
      '@scalar/themes': 0.9.86
      '@scalar/types': 0.0.12
      elysia: 1.2.25(@sinclair/typebox@0.34.33)(openapi-types@12.1.3)
      openapi-types: 12.1.3
      pathe: 1.1.2

  '@otherguy/elysia-logging@0.0.17(elysia@1.2.25(@sinclair/typebox@0.34.33)(openapi-types@12.1.3))':
    dependencies:
      date-fns: 2.30.0
      date-fns-tz: 2.0.1(date-fns@2.30.0)
      elysia: 1.2.25(@sinclair/typebox@0.34.33)(openapi-types@12.1.3)
    optionalDependencies:
      '@types/pino': 7.0.5
      pino: 8.21.0

  '@rasla/logify@5.0.0(elysia@1.2.25(@sinclair/typebox@0.34.33)(openapi-types@12.1.3))':
    dependencies:
      chalk: 5.4.1
      elysia: 1.2.25(@sinclair/typebox@0.34.33)(openapi-types@12.1.3)

  '@scalar/openapi-types@0.1.1': {}

  '@scalar/openapi-types@0.2.0':
    dependencies:
      zod: 3.24.3

  '@scalar/themes@0.9.86':
    dependencies:
      '@scalar/types': 0.1.7

  '@scalar/types@0.0.12':
    dependencies:
      '@scalar/openapi-types': 0.1.1
      '@unhead/schema': 1.11.20

  '@scalar/types@0.1.7':
    dependencies:
      '@scalar/openapi-types': 0.2.0
      '@unhead/schema': 1.11.20
      nanoid: 5.1.5
      type-fest: 4.40.0
      zod: 3.24.3

  '@sinclair/typebox@0.34.33': {}

  '@supabase/auth-js@2.69.1':
    dependencies:
      '@supabase/node-fetch': 2.6.15

  '@supabase/functions-js@2.4.4':
    dependencies:
      '@supabase/node-fetch': 2.6.15

  '@supabase/node-fetch@2.6.15':
    dependencies:
      whatwg-url: 5.0.0

  '@supabase/postgrest-js@1.19.4':
    dependencies:
      '@supabase/node-fetch': 2.6.15

  '@supabase/realtime-js@2.11.2':
    dependencies:
      '@supabase/node-fetch': 2.6.15
      '@types/phoenix': 1.6.6
      '@types/ws': 8.18.1
      ws: 8.18.1
    transitivePeerDependencies:
      - bufferutil
      - utf-8-validate

  '@supabase/storage-js@2.7.1':
    dependencies:
      '@supabase/node-fetch': 2.6.15

  '@supabase/supabase-js@2.49.4':
    dependencies:
      '@supabase/auth-js': 2.69.1
      '@supabase/functions-js': 2.4.4
      '@supabase/node-fetch': 2.6.15
      '@supabase/postgrest-js': 1.19.4
      '@supabase/realtime-js': 2.11.2
      '@supabase/storage-js': 2.7.1
    transitivePeerDependencies:
      - bufferutil
      - utf-8-validate

  '@types/cookie-signature@1.1.2':
    dependencies:
      '@types/node': 22.15.2

  '@types/cookie@0.5.4': {}

  '@types/node@22.15.2':
    dependencies:
      undici-types: 6.21.0

  '@types/phoenix@1.6.6': {}

  '@types/pino@7.0.5':
    dependencies:
      pino: 8.21.0
    optional: true

  '@types/ws@8.18.1':
    dependencies:
      '@types/node': 22.15.2

  '@unhead/schema@1.11.20':
    dependencies:
      hookable: 5.5.3
      zhead: 2.2.4

  abort-controller@3.0.0:
    dependencies:
      event-target-shim: 5.0.1
    optional: true

  atomic-sleep@1.0.0:
    optional: true

  base64-js@1.5.1:
    optional: true

  buffer@6.0.3:
    dependencies:
      base64-js: 1.5.1
      ieee754: 1.2.1
    optional: true

  bun-types@1.2.10:
    dependencies:
      '@types/node': 22.15.2

  chalk@5.4.1: {}

  cookie-signature@1.2.2: {}

  cookie@0.5.0: {}

  cookie@1.0.2: {}

  date-fns-tz@2.0.1(date-fns@2.30.0):
    dependencies:
      date-fns: 2.30.0

  date-fns-tz@3.2.0(date-fns@4.1.0):
    dependencies:
      date-fns: 4.1.0

  date-fns@2.30.0:
    dependencies:
      '@babel/runtime': 7.27.0

  date-fns@4.1.0: {}

  dotenv@16.5.0: {}

  elysia@1.2.25(@sinclair/typebox@0.34.33)(openapi-types@12.1.3):
    dependencies:
      '@sinclair/typebox': 0.34.33
      cookie: 1.0.2
      memoirist: 0.3.0
    optionalDependencies:
      openapi-types: 12.1.3

  event-target-shim@5.0.1:
    optional: true

  events@3.3.0:
    optional: true

  fast-redact@3.5.0:
    optional: true

  hookable@5.5.3: {}

  ieee754@1.2.1:
    optional: true

  memoirist@0.3.0: {}

  nanoid@5.1.5: {}

  on-exit-leak-free@2.1.2:
    optional: true

  openapi-types@12.1.3: {}

  pathe@1.1.2: {}

  pino-abstract-transport@1.2.0:
    dependencies:
      readable-stream: 4.7.0
      split2: 4.2.0
    optional: true

  pino-std-serializers@6.2.2:
    optional: true

  pino@8.21.0:
    dependencies:
      atomic-sleep: 1.0.0
      fast-redact: 3.5.0
      on-exit-leak-free: 2.1.2
      pino-abstract-transport: 1.2.0
      pino-std-serializers: 6.2.2
      process-warning: 3.0.0
      quick-format-unescaped: 4.0.4
      real-require: 0.2.0
      safe-stable-stringify: 2.5.0
      sonic-boom: 3.8.1
      thread-stream: 2.7.0
    optional: true

  process-warning@3.0.0:
    optional: true

  process@0.11.10:
    optional: true

  quick-format-unescaped@4.0.4:
    optional: true

  readable-stream@4.7.0:
    dependencies:
      abort-controller: 3.0.0
      buffer: 6.0.3
      events: 3.3.0
      process: 0.11.10
      string_decoder: 1.3.0
    optional: true

  real-require@0.2.0:
    optional: true

  regenerator-runtime@0.14.1: {}

  safe-buffer@5.2.1:
    optional: true

  safe-stable-stringify@2.5.0:
    optional: true

  sonic-boom@3.8.1:
    dependencies:
      atomic-sleep: 1.0.0
    optional: true

  split2@4.2.0:
    optional: true

  string_decoder@1.3.0:
    dependencies:
      safe-buffer: 5.2.1
    optional: true

  thread-stream@2.7.0:
    dependencies:
      real-require: 0.2.0
    optional: true

  tr46@0.0.3: {}

  type-fest@4.40.0: {}

  undici-types@6.21.0: {}

  webidl-conversions@3.0.1: {}

  whatwg-url@5.0.0:
    dependencies:
      tr46: 0.0.3
      webidl-conversions: 3.0.1

  ws@8.18.1: {}

  zhead@2.2.4: {}

  zod@3.24.3: {}
