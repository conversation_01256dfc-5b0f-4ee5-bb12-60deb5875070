# Scripts

This directory contains utility scripts for the Kasuat application.

## Available Scripts

### `create-admin.ts`

Creates an admin user with the Manager role.

```bash
bun run scripts/create-admin.ts <email> <password>
```

### `create-employee-users.ts`

Creates users for each employee role (Manager, HR, Finance, Operation) with complete profiles.

```bash
# Create users for all roles
bun run scripts/create-employee-users.ts

# Create a user for a specific role
bun run scripts/create-employee-users.ts --role=Manager
bun run scripts/create-employee-users.ts --role=HR
bun run scripts/create-employee-users.ts --role=Finance
bun run scripts/create-employee-users.ts --role=Operation
```

#### Default User Credentials

| Role      | Email               | Password      |
| --------- | ------------------- | ------------- |
| Manager   | <EMAIL>   | Manager@123   |
| HR        | <EMAIL>        | HRUser@123    |
| Finance   | <EMAIL>   | Finance@123   |
| Operation | <EMAIL> | Operation@123 |

### `create-organizations.ts`

Creates 11 sample organizations with diverse client types and locations across Indonesia.

```bash
# Create all organizations
bun run scripts/create-organizations.ts

# Create a specific organization (partial name match)
bun run scripts/create-organizations.ts --name=TechNova
```

#### Created Organizations

The script creates organizations of various types:

- Corporate (2)
- Small Business (2)
- Enterprise (1)
- Government (1)
- Non-Profit (2)
- Educational (1)
- Healthcare (1)
- Retail (1)

### `create-invoices.ts`

Creates 15 sample invoices with various service types, payment statuses, and items.

```bash
# Create all invoices (default: 15)
bun run scripts/create-invoices.ts

# Create a specific number of invoices
bun run scripts/create-invoices.ts --count=10

# Create invoices for a specific service type
bun run scripts/create-invoices.ts --service-type=HCM

# Create invoices with a specific payment status
bun run scripts/create-invoices.ts --status=pending
```

#### Features

- Generates invoices across all service types (HCM, ORDEV, BE, IT, MARKETING, etc.)
- Includes different payment statuses with weighted distribution
- Each invoice contains 2-4 items with realistic service descriptions
- Uses existing organizations as invoice recipients
- Generates realistic invoice numbers in the format: Number/ServiceType/Kasuat/Month(Roman)/Year

### `create-kpis.ts`

Creates sample KPI records for employees, ensuring that employees exist before adding KPIs.

```bash
# Create KPIs for all employees
bun run scripts/create-kpis.ts

# Create KPIs for a specific employee
bun run scripts/create-kpis.ts --employee=employee_id
```

#### Features

- Generates KPIs across different quarterly periods (2023-Q1 through 2024-Q2)
- Creates KPIs with realistic descriptions, targets, and statuses
- Randomly assigns bonuses to completed KPIs
- Validates employee existence before creating KPIs
- Avoids creating duplicate KPIs for the same employee and period
- Provides summary statistics of created and skipped KPIs

## Create Attendance Records

Script: `create-attendance-records.ts`

This script generates attendance records for the last 7 days (excluding today) for all active employees or a specific employee.

### Prerequisites

- An admin user account (`<EMAIL>`) must exist
- Employees must be already created in the system with valid employee_id values

### Usage

#### Generate attendance records for all employees:

```bash
bun scripts/create-attendance-records.ts
```

#### Generate attendance records for a specific employee:

```bash
bun scripts/create-attendance-records.ts --employee=<user_id>
```

Where `<user_id>` is the ID from the `user_profiles` table, not the employee_id.

#### Clear all existing attendance records before creating new ones:

```bash
bun scripts/create-attendance-records.ts --clear
```

You can combine options:

```bash
bun scripts/create-attendance-records.ts --clear --employee=<user_id>
```

### Features

- Creates attendance records for the last 7 days excluding today
- Generates random attendance statuses (present, absent, permit, leave) with realistic probabilities
- For present status, generates random check-in times (7:00-9:30) and check-out times (16:00-19:00)
- For other statuses, adds appropriate notes explaining the absence
- Creates tasks for present attendance records with a 60% probability
  - Each attendance can have 0-2 randomly selected tasks
  - Tasks have random completion status and due dates
  - Tasks are assigned by random supervisors
- Skips dates that already have attendance records (to avoid duplicates)
- Option to clear all existing records and start fresh

### Output

The script will output a summary of created and skipped records.

## Requirements

- Make sure you have a `.env.local` file with the following variables:
  - `supabase_url`: Your Supabase project URL
  - `supabase_service_role`: Your Supabase service role key (with admin privileges)

> **IMPORTANT SECURITY NOTE**: Never hardcode Supabase credentials directly in script files. Always use environment variables through the `.env.local` file, which is excluded from version control via `.gitignore`.

## Notes

- These scripts use the Supabase admin API to create users and profiles
- Users are created with email confirmation already set to true
- If a user with the specified email already exists, the script will skip that user
- Employee users are created with complete profiles (no placeholder values)
