// Load environment variables from .env.local
import { config } from "dotenv";
config({ path: ".env.local" });

import { createClient } from "@supabase/supabase-js";

// Initialize Supabase client with admin privileges
const supabaseUrl = process.env.supabase_url || "";
const supabaseServiceKey = process.env.supabase_service_role || "";

if (!supabaseUrl || !supabaseServiceKey) {
  console.error(
    "Error: supabase_url and supabase_service_role must be set in .env.local"
  );
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseServiceKey);

async function main() {
  try {
    // Get current period
    const now = new Date();
    const currentYear = now.getFullYear();
    const currentMonth = now.getMonth() + 1;
    const period = `${currentYear}-${currentMonth.toString().padStart(2, "0")}`;

    console.log(`Checking salary records for period: ${period}`);

    // Get all salaries for the period
    const { data: salaries, error: fetchError } = await supabase
      .from("salaries")
      .select("id, employee_id")
      .eq("period", period);

    if (fetchError) {
      console.error("Error fetching salaries:", fetchError.message);
      process.exit(1);
    }

    if (!salaries || salaries.length === 0) {
      console.log(`No salary records found for period ${period}`);
      process.exit(0);
    }

    console.log(`Found ${salaries.length} salary records for period ${period}`);

    // Count unique employee IDs
    const uniqueEmployeeIds = new Set(salaries.map(s => s.employee_id));
    console.log(`Number of unique employees: ${uniqueEmployeeIds.size}`);

    // Check for duplicates
    const employeeCounts = {};
    salaries.forEach(s => {
      employeeCounts[s.employee_id] = (employeeCounts[s.employee_id] || 0) + 1;
    });

    const duplicates = Object.entries(employeeCounts)
      .filter(([_, count]) => count > 1)
      .map(([empId, count]) => ({ employeeId: empId, count }));

    if (duplicates.length > 0) {
      console.log("Found duplicate salary records for these employees:");
      duplicates.forEach(d => {
        console.log(`- Employee ${d.employeeId}: ${d.count} records`);
      });
    } else {
      console.log("No duplicate salary records found.");
    }

  } catch (error: any) {
    console.error("Unexpected error:", error.message);
    process.exit(1);
  }
}

main();
