// Load environment variables from .env.local
import { config } from "dotenv";
config({ path: ".env.local" });

import { createClient } from "@supabase/supabase-js";

// Initialize Supabase client with admin privileges
const supabaseUrl = process.env.supabase_url || "";
const supabaseServiceKey = process.env.supabase_service_role || "";

if (!supabaseUrl || !supabaseServiceKey) {
  console.error(
    "Error: supabase_url and supabase_service_role must be set in .env.local"
  );
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseServiceKey);

async function main() {
  try {
    // Get current period
    const now = new Date();
    const currentYear = now.getFullYear();
    const currentMonth = now.getMonth() + 1;
    const period = `${currentYear}-${currentMonth.toString().padStart(2, "0")}`;

    console.log(`Clearing salary records for period: ${period}`);

    // First, get all salary IDs for the period
    const { data: salaries, error: fetchError } = await supabase
      .from("salaries")
      .select("id")
      .eq("period", period);

    if (fetchError) {
      console.error("Error fetching salaries:", fetchError.message);
      process.exit(1);
    }

    if (!salaries || salaries.length === 0) {
      console.log(`No salary records found for period ${period}`);
      process.exit(0);
    }

    console.log(`Found ${salaries.length} salary records to delete`);

    // Get all salary IDs
    const salaryIds = salaries.map((s) => s.id);

    // Delete related records first
    console.log("Deleting related bonus records...");
    const { error: bonusError } = await supabase
      .from("bonus_salaries")
      .delete()
      .in("salary_id", salaryIds);

    if (bonusError) {
      console.error("Error deleting bonus records:", bonusError.message);
    }

    console.log("Deleting related deduction records...");
    const { error: deductionError } = await supabase
      .from("deduction_salaries")
      .delete()
      .in("salary_id", salaryIds);

    if (deductionError) {
      console.error("Error deleting deduction records:", deductionError.message);
    }

    console.log("Deleting related allowance records...");
    const { error: allowanceError } = await supabase
      .from("allowance_salaries")
      .delete()
      .in("salary_id", salaryIds);

    if (allowanceError) {
      console.error("Error deleting allowance records:", allowanceError.message);
    }

    console.log("Deleting related history records...");
    const { error: historyError } = await supabase
      .from("salary_update_history")
      .delete()
      .in("salary_id", salaryIds);

    if (historyError) {
      console.error("Error deleting history records:", historyError.message);
    }

    // Now delete the salary records
    console.log("Deleting salary records...");
    const { error: deleteError } = await supabase
      .from("salaries")
      .delete()
      .in("id", salaryIds);

    if (deleteError) {
      console.error("Error deleting salary records:", deleteError.message);
      process.exit(1);
    }

    console.log(`Successfully deleted ${salaries.length} salary records for period ${period}`);
  } catch (error: any) {
    console.error("Unexpected error:", error.message);
    process.exit(1);
  }
}

main();
