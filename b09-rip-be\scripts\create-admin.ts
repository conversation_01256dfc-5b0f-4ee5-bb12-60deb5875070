// Load environment variables from .env.local
import { config } from "dotenv";
config({ path: ".env.local" });

import { createClient } from "@supabase/supabase-js";

// Initialize Supabase client with admin privileges
const supabaseUrl = process.env.supabase_url || "";
const supabaseServiceKey = process.env.supabase_service_role || "";

if (!supabaseUrl || !supabaseServiceKey) {
  console.error(
    "Error: supabase_url and supabase_service_role must be set in .env.local"
  );
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseServiceKey);

async function createOrUpdateAdminUser() {
  const email = process.argv[2];
  const password = process.argv[3];

  if (!email || !password) {
    console.error("Usage: bun run scripts/create-admin.ts <email> <password>");
    process.exit(1);
  }

  console.log(`Processing admin user with email: ${email}`);

  try {
    // Try to create the user first - if it fails, we'll update instead
    const { data: authData, error: authError } =
      await supabase.auth.admin.createUser({
        email,
        password,
        email_confirm: true,
      });

    let userId;

    if (authError) {
      // If error is because user already exists, get the user and update
      if (authError.message.includes("already been registered")) {
        console.log("User already exists, fetching user ID...");

        // Get the user by email
        const { data: userData, error: userError } =
          await supabase.auth.admin.listUsers();

        if (userError) {
          console.error("Error fetching users:", userError.message);
          process.exit(1);
        }

        // Find the user with the matching email
        const user = userData.users.find((u) => u.email === email);

        if (!user) {
          console.error("Could not find user with email:", email);
          process.exit(1);
        }

        userId = user.id;

        // Update password
        console.log("Updating password...");
        const { error: passwordError } =
          await supabase.auth.admin.updateUserById(userId, { password });

        if (passwordError) {
          console.error("Error updating password:", passwordError.message);
          process.exit(1);
        }

        console.log("Password updated successfully");
      } else {
        // If it's any other error, exit
        console.error("Error creating auth user:", authError.message);
        process.exit(1);
      }
    } else {
      // New user was created successfully
      console.log("Auth user created successfully");
      userId = authData.user.id;
    }

    // Step 2: Check if profile exists
    const { data: existingProfile } = await supabase
      .from("user_profiles")
      .select("*")
      .eq("user_id", userId)
      .single();

    let profileData;

    if (existingProfile) {
      console.log("User profile exists, updating...");

      // Update profile
      const { data: updatedProfile, error: updateError } = await supabase
        .from("user_profiles")
        .update({
          fullname: "Admin User",
          phonenum: "1234567890",
          role: "Manager",
          is_active: true,
        })
        .eq("user_id", userId)
        .select()
        .single();

      if (updateError) {
        console.error("Error updating profile:", updateError.message);
        process.exit(1);
      }

      profileData = updatedProfile;
      console.log("Profile updated successfully");
    } else {
      console.log("Creating user profile...");

      // Create profile
      const { data: newProfile, error: insertError } = await supabase
        .from("user_profiles")
        .insert({
          user_id: userId,
          fullname: "Admin User",
          phonenum: "1234567890",
          role: "Manager",
          is_active: true,
        })
        .select()
        .single();

      if (insertError) {
        console.error("Error creating user profile:", insertError.message);
        process.exit(1);
      }

      profileData = newProfile;
      console.log("User profile created successfully");
    }

    console.log(JSON.stringify(profileData, null, 2));

    console.log(`\nAdmin user setup completed!`);
    console.log(`Email: ${email}`);
    console.log(`User ID: ${userId}`);
    console.log(`\nNOTE: This user has been created with the 'Manager' role.`);
    console.log(
      `You will need to manually update the enum to include 'Admin' and then update this user's role.`
    );
  } catch (err) {
    console.error("Unexpected error:", err);
    process.exit(1);
  }
}

createOrUpdateAdminUser();
