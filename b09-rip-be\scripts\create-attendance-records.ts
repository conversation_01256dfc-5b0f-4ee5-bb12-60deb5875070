// Load environment variables from .env.local
import { config } from "dotenv";
config({ path: ".env.local" });

import { createClient } from "@supabase/supabase-js";
import { PresenceStatus } from "../src/database/models/attendance.model";
import { addDays, format, subDays } from "date-fns";
import { toZonedTime } from "date-fns-tz";

// Initialize Supabase client with admin privileges
const supabaseUrl = process.env.supabase_url || "";
const supabaseServiceKey = process.env.supabase_service_role || "";

if (!supabaseUrl || !supabaseServiceKey) {
  console.error(
    "Error: supabase_url and supabase_service_role must be set in .env.local"
  );
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseServiceKey);

// Admin user credentials
const ADMIN_EMAIL = "<EMAIL>";
const ADMIN_PASSWORD = "Admin@123";

// Jakarta timezone
const TIMEZONE = "Asia/Jakarta";

// Attendance statuses with weighted probabilities
const ATTENDANCE_STATUSES = [
  { status: PresenceStatus.PRESENT, weight: 80 }, // 80% chance of being present
  { status: PresenceStatus.ABSENT, weight: 5 }, // 5% chance of being absent
  { status: PresenceStatus.PERMIT, weight: 10 }, // 10% chance of having a permit
  { status: PresenceStatus.LEAVE, weight: 5 }, // 5% chance of being on leave
];

// Notes templates for different statuses
const STATUS_NOTES = {
  [PresenceStatus.ABSENT]: [
    "Sick leave - Flu",
    "Sick leave - Fever",
    "Family emergency",
    "Transportation issues",
    "No notification received",
  ],
  [PresenceStatus.PERMIT]: [
    "Doctor's appointment",
    "Family event",
    "Government documentation",
    "Vehicle maintenance",
    "Banking errands",
  ],
  [PresenceStatus.LEAVE]: [
    "Annual leave",
    "Personal leave",
    "Family vacation",
    "Religious holiday",
    "Wedding preparation",
  ],
};

// Tasks templates for creating random tasks
const TASK_TEMPLATES = [
  {
    description: "Complete daily report",
    probability: 0.7, // 70% chance this task will be assigned
  },
  {
    description: "Attend team meeting",
    probability: 0.5, // 50% chance
  },
  {
    description: "Update project documentation",
    probability: 0.3, // 30% chance
  },
  {
    description: "Prepare presentation for client",
    probability: 0.2, // 20% chance
  },
  {
    description: "Review code pull request",
    probability: 0.4, // 40% chance
  },
  {
    description: "Setup development environment",
    probability: 0.1, // 10% chance
  },
  {
    description: "Create weekly status report",
    probability: 0.3, // 30% chance
  },
  {
    description: "Participate in design review",
    probability: 0.25, // 25% chance
  },
  {
    description: "Complete training module",
    probability: 0.2, // 20% chance
  },
  {
    description: "Attend company workshop",
    probability: 0.15, // 15% chance
  },
];

// Task assigners (supervisors) - removed since assigned_by field doesn't exist in tasks table
// const TASK_ASSIGNERS = [
//   "John Doe (Manager)",
//   "Jane Smith (Supervisor)",
//   "Robert Brown (Team Lead)",
//   "Michael Johnson (Project Manager)",
//   "Emily Wilson (Department Head)",
// ];

/**
 * Get current date in Jakarta timezone
 */
function getCurrentJakartaDate(): string {
  const now = new Date();
  const jakartaTime = toZonedTime(now, TIMEZONE);
  return format(jakartaTime, "yyyy-MM-dd");
}

/**
 * Generate a random time between start and end times (format: "HH:mm")
 */
function getRandomTime(startTime: string, endTime: string): string {
  const [startHour, startMinute] = startTime.split(":").map(Number);
  const [endHour, endMinute] = endTime.split(":").map(Number);

  const startMinutes = startHour * 60 + startMinute;
  const endMinutes = endHour * 60 + endMinute;

  const randomMinutes =
    Math.floor(Math.random() * (endMinutes - startMinutes + 1)) + startMinutes;

  const hours = Math.floor(randomMinutes / 60);
  const minutes = randomMinutes % 60;

  // Return in HH:MM:SS format as required by the updated database schema
  return `${hours.toString().padStart(2, "0")}:${minutes
    .toString()
    .padStart(2, "0")}:00`;
}

/**
 * Get a random item from an array
 */
function getRandomItem<T>(arr: T[]): T {
  return arr[Math.floor(Math.random() * arr.length)];
}

/**
 * Get a random status based on weighted probabilities
 */
function getRandomWeightedStatus(): PresenceStatus {
  const totalWeight = ATTENDANCE_STATUSES.reduce(
    (sum, item) => sum + item.weight,
    0
  );
  let random = Math.floor(Math.random() * totalWeight);

  for (const item of ATTENDANCE_STATUSES) {
    if (random < item.weight) {
      return item.status;
    }
    random -= item.weight;
  }

  // Default fallback
  return PresenceStatus.PRESENT;
}

/**
 * Generate date range for the last 7 days excluding today
 */
function getLastSevenDaysExcludingToday(): string[] {
  const today = getCurrentJakartaDate();
  const dates: string[] = [];

  for (let i = 1; i <= 7; i++) {
    const date = format(subDays(new Date(today), i), "yyyy-MM-dd");
    dates.push(date);
  }

  return dates;
}

/**
 * Authenticate as admin user and return the user ID
 */
async function authenticateAdmin() {
  console.log(`Authenticating as admin user (${ADMIN_EMAIL})...`);

  try {
    const { data, error } = await supabase.auth.signInWithPassword({
      email: ADMIN_EMAIL,
      password: ADMIN_PASSWORD,
    });

    if (error) {
      console.error("Error authenticating as admin:", error.message);
      process.exit(1);
    }

    console.log(
      `Authenticated as admin successfully! User ID: ${data.user.id}`
    );
    return data.user.id;
  } catch (err) {
    console.error("Unexpected error during authentication:", err);
    process.exit(1);
  }
}

/**
 * Get all active employees from the database
 */
async function getAllActiveEmployees() {
  try {
    const { data, error } = await supabase
      .from("user_profiles")
      .select("id, fullname, employee_id")
      .is("is_active", true)
      .not("employee_id", "is", null);

    if (error) {
      console.error("Error fetching employees:", error.message);
      return [];
    }

    return data;
  } catch (err) {
    console.error("Unexpected error fetching employees:", err);
    return [];
  }
}

/**
 * Check if attendance record already exists for an employee on a specific date
 */
async function attendanceExistsForEmployeeAndDate(
  employeeId: string,
  date: string
): Promise<boolean> {
  try {
    const { data, error } = await supabase
      .from("attendances")
      .select("id")
      .eq("employee_id", employeeId)
      .eq("date", date)
      .is("deleted_at", null)
      .maybeSingle();

    return !!data;
  } catch (err) {
    console.error("Error checking for existing attendance:", err);
    return false;
  }
}

/**
 * Create tasks for an attendance record
 * Returns the number of tasks created
 */
async function createTasksForAttendance(
  attendanceId: string,
  employeeId: string,
  date: string,
  userId: string
): Promise<number> {
  try {
    const taskCount = Math.floor(Math.random() * 3) + 0; // 0-2 tasks per attendance

    if (taskCount === 0) {
      return 0; // No tasks for this attendance
    }

    console.log(`Creating ${taskCount} tasks for attendance ${attendanceId}`);

    // Select random tasks based on probability
    const selectedTasks = TASK_TEMPLATES.filter(
      () => Math.random() < 0.3
    ).slice(0, taskCount);

    if (selectedTasks.length === 0) {
      return 0; // No tasks selected after probability filter
    }

    let createdCount = 0;

    // Create each task
    for (const taskTemplate of selectedTasks) {
      // Random completion status (30% chance of being completed) - now boolean
      const completionStatus = Math.random() < 0.3;

      // Due date is either the same day or next day
      const dueDate =
        Math.random() < 0.7
          ? date
          : format(addDays(new Date(date), 1), "yyyy-MM-dd");

      // Create task data (removed assigned_by field)
      const taskData = {
        attendance_id: attendanceId,
        employee_id: employeeId,
        description: taskTemplate.description,
        completion_status: completionStatus, // Boolean value
        due_date: dueDate,
        created_by: userId,
      };

      // Insert task
      const { data, error } = await supabase
        .from("tasks")
        .insert([taskData])
        .select()
        .single();

      if (error) {
        console.error("Error creating task:", error.message);
      } else {
        console.log(
          `Created task: "${taskData.description}" (${
            taskData.completion_status ? "completed" : "not completed"
          }) for attendance ${attendanceId}`
        );
        createdCount++;
      }
    }

    return createdCount;
  } catch (err) {
    console.error("Unexpected error creating tasks:", err);
    return 0;
  }
}

/**
 * Create a single attendance record
 */
async function createAttendance(
  employeeId: string,
  date: string,
  presenceStatus: PresenceStatus,
  userId: string
) {
  try {
    // Check if record already exists
    if (await attendanceExistsForEmployeeAndDate(employeeId, date)) {
      console.log(
        `Attendance already exists for employee ${employeeId} on ${date}. Skipping.`
      );
      return null;
    }

    // Prepare the attendance record
    const attendanceData: any = {
      employee_id: employeeId,
      date,
      status: presenceStatus,
    };

    // Add check_in/check_out times for all statuses (required by database schema)
    // For present status, use normal working hours
    if (presenceStatus === PresenceStatus.PRESENT) {
      attendanceData.clock_in = getRandomTime("07:00", "09:30");
      attendanceData.clock_out = getRandomTime("16:00", "19:00");
    } else {
      // For all other statuses, use fixed times to indicate non-attendance
      attendanceData.clock_in = "00:00:00";
      attendanceData.clock_out = "00:00:00";
    }

    // Add notes for non-present statuses
    if (presenceStatus !== PresenceStatus.PRESENT) {
      attendanceData.notes = getRandomItem(
        STATUS_NOTES[presenceStatus] || ["No reason provided"]
      );
    }

    // Create the record
    const { data, error } = await supabase
      .from("attendances")
      .insert([
        {
          ...attendanceData,
          created_by: userId,
        },
      ])
      .select()
      .single();

    if (error) {
      console.error("Error creating attendance record:", error.message);
      return null;
    }

    console.log(
      `Created attendance for employee ${employeeId} on ${date} - Status: ${presenceStatus}`
    );

    return data;
  } catch (err) {
    console.error("Unexpected error creating attendance:", err);
    return null;
  }
}

/**
 * Create attendance records for all employees for the last 7 days
 */
async function createAttendanceForAllEmployees(userId: string) {
  // Get all active employees
  const employees = await getAllActiveEmployees();
  if (employees.length === 0) {
    console.error("No active employees found in the system.");
    return;
  }

  console.log(`Found ${employees.length} active employees.`);

  // Get date range for the last 7 days
  const dates = getLastSevenDaysExcludingToday();
  // Reverse the dates to process from most recent to oldest
  dates.reverse();
  console.log(`Creating attendance records for dates: ${dates.join(", ")}`);

  let createdCount = 0;
  let skippedCount = 0;
  let tasksCreatedCount = 0;

  // Create attendance records for each date first, then for each employee
  for (const date of dates) {
    console.log(`\nProcessing attendance records for date: ${date}`);

    // Reset daily counters
    let dailyCreatedCount = 0;
    let dailySkippedCount = 0;
    let dailyTasksCreatedCount = 0;

    // Create attendance records for all employees on this date
    for (const employee of employees) {
      // Skip if employee_id is null
      if (!employee.employee_id) {
        console.log(`Skipping ${employee.fullname} - No employee_id`);
        skippedCount++;
        dailySkippedCount++;
        continue;
      }

      // Get a random attendance status based on weighted probabilities
      const status = getRandomWeightedStatus();

      const result = await createAttendance(
        employee.employee_id,
        date,
        status,
        userId
      );

      if (result) {
        createdCount++;
        dailyCreatedCount++;

        // Only create tasks for PRESENT status
        if (status === PresenceStatus.PRESENT) {
          // 60% chance of having tasks
          if (Math.random() < 0.6) {
            const tasksCreated = await createTasksForAttendance(
              result.id,
              employee.employee_id,
              date,
              userId
            );
            tasksCreatedCount += tasksCreated;
            dailyTasksCreatedCount += tasksCreated;
          }
        }
      } else {
        skippedCount++;
        dailySkippedCount++;
      }
    }

    console.log(`Completed processing for date ${date}:`);
    console.log(`- Records created: ${dailyCreatedCount}`);
    console.log(`- Tasks created: ${dailyTasksCreatedCount}`);
    console.log(`- Records skipped: ${dailySkippedCount}`);
  }

  console.log("\nAttendance record creation summary:");
  console.log(`- Total attendance records created: ${createdCount}`);
  console.log(`- Total tasks created: ${tasksCreatedCount}`);
  console.log(`- Total records skipped: ${skippedCount}`);
}

/**
 * Create attendance records for a specific employee
 */
async function createAttendanceForEmployee(userId: string, adminId: string) {
  // Check if user profile exists
  const { data: profile, error } = await supabase
    .from("user_profiles")
    .select("id, fullname, employee_id")
    .eq("id", userId)
    .single();

  if (error || !profile) {
    console.error(`User with ID ${userId} not found.`);
    return;
  }

  if (!profile.employee_id) {
    console.error(
      `User ${profile.fullname} (${userId}) exists but has no employee record.`
    );
    return;
  }

  console.log(`Creating attendance records for employee: ${profile.fullname}`);

  // Get date range for the last 7 days
  const dates = getLastSevenDaysExcludingToday();
  // Reverse the dates to process from most recent to oldest
  dates.reverse();
  console.log(`Creating attendance records for dates: ${dates.join(", ")}`);

  let createdCount = 0;
  let skippedCount = 0;
  let tasksCreatedCount = 0;

  for (const date of dates) {
    console.log(`\nProcessing attendance for date: ${date}`);

    // Daily counters for this date
    let dailyCreated = 0;
    let dailySkipped = 0;
    let dailyTasks = 0;

    // Get a random attendance status based on weighted probabilities
    const status = getRandomWeightedStatus();

    const result = await createAttendance(
      profile.employee_id,
      date,
      status,
      adminId
    );

    if (result) {
      createdCount++;
      dailyCreated++;

      // Only create tasks for PRESENT status
      if (status === PresenceStatus.PRESENT) {
        // 60% chance of having tasks
        if (Math.random() < 0.6) {
          const tasksCreated = await createTasksForAttendance(
            result.id,
            profile.employee_id,
            date,
            adminId
          );
          tasksCreatedCount += tasksCreated;
          dailyTasks += tasksCreated;
        }
      }
    } else {
      skippedCount++;
      dailySkipped++;
    }

    console.log(`Completed processing for date ${date}:`);
    console.log(`- Records created: ${dailyCreated}`);
    console.log(`- Tasks created: ${dailyTasks}`);
    console.log(`- Records skipped: ${dailySkipped}`);
  }

  console.log("\nAttendance record creation summary for this employee:");
  console.log(`- Total attendance records created: ${createdCount}`);
  console.log(`- Total tasks created: ${tasksCreatedCount}`);
  console.log(`- Total records skipped: ${skippedCount}`);
}

/**
 * Clear all attendance records and related tasks
 */
async function clearAllAttendances(userId: string) {
  console.log("Clearing all attendance records and related tasks...");

  try {
    // First, soft delete all tasks related to attendances
    console.log("Soft deleting all tasks...");
    const now = new Date().toISOString();

    const { error: tasksError } = await supabase
      .from("tasks")
      .update({
        deleted_at: now,
        deleted_by: userId,
      })
      .is("deleted_at", null);

    if (tasksError) {
      console.error("Error clearing tasks:", tasksError.message);
      // Continue anyway - we'll still try to delete attendances
    } else {
      console.log("All tasks cleared successfully!");
    }

    // Now soft delete all attendances
    // Soft delete by setting deleted_at and deleted_by
    const { data, error } = await supabase.rpc("soft_delete_all_attendances", {
      user_id: userId,
    });

    if (error) {
      // If the RPC doesn't exist, fall back to direct update
      console.log("Soft delete RPC not found, using direct update...");
      const { error: updateError } = await supabase
        .from("attendances")
        .update({
          deleted_at: now,
          deleted_by: userId,
        })
        .is("deleted_at", null);

      if (updateError) {
        console.error(
          "Error clearing attendance records:",
          updateError.message
        );
        return false;
      }
    }

    console.log("All attendance records cleared successfully!");
    return true;
  } catch (err) {
    console.error("Unexpected error clearing records:", err);
    return false;
  }
}

/**
 * Main function to run the script
 */
async function main() {
  console.log("Starting attendance record creation script...");
  console.log(`Current date in Jakarta: ${getCurrentJakartaDate()}`);

  // Authenticate as admin
  const adminId = await authenticateAdmin();

  // Parse command line arguments
  const args = process.argv.slice(2);
  const clearFlag = args.includes("--clear");
  const employeeArg = args.find((arg) => arg.startsWith("--employee="));

  // Clear all records if requested
  if (clearFlag) {
    const cleared = await clearAllAttendances(adminId);
    if (!cleared) {
      console.error("Failed to clear attendance records. Exiting.");
      process.exit(1);
    }
  }

  if (employeeArg) {
    const userId = employeeArg.split("=")[1];
    await createAttendanceForEmployee(userId, adminId);
  } else {
    await createAttendanceForAllEmployees(adminId);
  }

  console.log("\nAttendance record creation completed!");
}

// Run the main function
main();
