// Load environment variables from .env.local
process.env.NODE_TLS_REJECT_UNAUTHORIZED = "0";

import { config } from "dotenv";
config({ path: ".env.local" });

import { createClient } from "@supabase/supabase-js";
import { UserRole } from "../src/database/models/user-profile.model";
import { PresenceStatus } from "../src/database/models/attendance.model";
import { EmploymentStatus } from "../src/database/models/employee.model";

// Initialize Supabase client with admin privileges
const supabaseUrl = process.env.supabase_url || "";
const supabaseServiceKey = process.env.supabase_service_role || "";

if (!supabaseUrl || !supabaseServiceKey) {
  console.error(
    "Error: supabase_url and supabase_service_role must be set in .env.local"
  );
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseServiceKey);

// Define employee roles
const employeeRoles = [
  UserRole.Manager,
  UserRole.HR,
  UserRole.Finance,
  UserRole.Operation,
  UserRole.Admin,
];

// Define user data structure
type UserData = {
  email: string;
  password: string;
  fullname: string;
  phonenum: string;
  department: string;
  dob: string;
  address: string;
  bank_account: string;
  bank_name: string;
};

// Define user data for each role
const userData: Record<string, UserData> = {
  [UserRole.Manager]: {
    email: "<EMAIL>",
    password: "Manager@123",
    fullname: "Manager User",
    phonenum: "*************",
    department: "Management",
    dob: "1985-05-15",
    address: "Jl. Sudirman No. 123, Jakarta",
    bank_account: "**********",
    bank_name: "Bank Central Asia",
  },
  [UserRole.HR]: {
    email: "<EMAIL>",
    password: "HRUser@123",
    fullname: "HR User",
    phonenum: "*************",
    department: "Human Resources",
    dob: "1988-08-20",
    address: "Jl. Gatot Subroto No. 456, Jakarta",
    bank_account: "**********",
    bank_name: "Bank Mandiri",
  },
  [UserRole.Finance]: {
    email: "<EMAIL>",
    password: "Finance@123",
    fullname: "Finance User",
    phonenum: "*************",
    department: "Finance",
    dob: "1982-03-10",
    address: "Jl. Thamrin No. 789, Jakarta",
    bank_account: "**********",
    bank_name: "Bank Negara Indonesia",
  },
  [UserRole.Operation]: {
    email: "<EMAIL>",
    password: "Operation@123",
    fullname: "Operation User",
    phonenum: "*************",
    department: "Operations",
    dob: "1990-11-25",
    address: "Jl. Kuningan No. 101, Jakarta",
    bank_account: "**********",
    bank_name: "Bank Rakyat Indonesia",
  },
  [UserRole.Admin]: {
    email: "<EMAIL>",
    password: "Admin@123",
    fullname: "Admin User",
    phonenum: "*************",
    department: "Admins",
    dob: "1990-11-25",
    address: "Jl. Kuningan No. 101, Jakarta",
    bank_account: "**********",
    bank_name: "Bank Rakyat Indonesia",
  },
};

async function createEmployeeUser(role: UserRole) {
  const user = userData[role];
  console.log(`\nCreating ${role} user with email: ${user.email}`);

  try {
    // Step 1: Create auth user
    const { data: authData, error: authError } =
      await supabase.auth.admin.createUser({
        email: user.email,
        password: user.password,
        email_confirm: true,
      });

    if (authError) {
      if (authError.message.includes("already been registered")) {
        console.log(`User ${user.email} already exists, skipping...`);
        return;
      } else {
        console.error(
          `Error creating auth user for ${role}:`,
          authError.message
        );
        return;
      }
    }

    const userId = authData.user.id;
    console.log(`Auth user created with ID: ${userId}`);

    // Step 2: Create user profile
    const { data: profileData, error: profileError } = await supabase
      .from("user_profiles")
      .insert({
        user_id: userId,
        fullname: user.fullname,
        phonenum: user.phonenum,
        role: role,
        is_active: true,
        created_by: userId,
      })
      .select()
      .single();

    if (profileError) {
      console.error(
        `Error creating profile for ${role}:`,
        profileError.message
      );
      return;
    }

    console.log(`User profile created with ID: ${profileData.id}`);

    // Step 3: Create employee record
    const today = new Date().toISOString().split("T")[0];

    const { data: employeeData, error: employeeError } = await supabase
      .from("employees")
      .insert({
        profile_id: profileData.id,
        dob: user.dob,
        address: user.address,
        bank_account: user.bank_account,
        bank_name: user.bank_name,
        employment_status: EmploymentStatus.FULLTIME,
        presence_status: PresenceStatus.PRESENT,
        department: user.department,
        start_date: today,
        created_by: userId,
      })
      .select()
      .single();

    if (employeeError) {
      console.error(
        `Error creating employee record for ${role}:`,
        employeeError.message
      );
      return;
    }

    console.log(`Employee record created with ID: ${employeeData.id}`);

    // Step 4: Update profile with employee_id
    const { error: updateError } = await supabase
      .from("user_profiles")
      .update({
        employee_id: employeeData.id,
      })
      .eq("id", profileData.id);

    if (updateError) {
      console.error(
        `Error updating profile with employee_id for ${role}:`,
        updateError.message
      );
      return;
    }

    console.log(`Profile updated with employee_id: ${employeeData.id}`);
    console.log(`\n${role} user created successfully!`);
    console.log(`Email: ${user.email}`);
    console.log(`Password: ${user.password}`);

    return {
      role,
      userId,
      profileId: profileData.id,
      employeeId: employeeData.id,
    };
  } catch (err) {
    console.error(`Unexpected error creating ${role} user:`, err);
  }
}

async function createAllEmployeeUsers() {
  console.log("Starting creation of employee users...");

  const roleParam = process.argv[2];

  if (roleParam && roleParam.startsWith("--role=")) {
    const role = roleParam.split("=")[1];
    if (employeeRoles.includes(role as UserRole)) {
      await createEmployeeUser(role as UserRole);
    } else {
      console.error(
        `Invalid role: ${role}. Available roles: ${employeeRoles.join(", ")}`
      );
    }
  } else {
    // Create users for all roles
    for (const role of employeeRoles) {
      await createEmployeeUser(role);
    }
  }

  console.log("\nEmployee user creation completed!");
}

createAllEmployeeUsers();
