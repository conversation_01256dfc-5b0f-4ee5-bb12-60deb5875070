// Load environment variables from .env.local
import { config } from "dotenv";
config({ path: ".env.local" });

import { createClient } from "@supabase/supabase-js";
import {
  CreateInvoiceDto,
  CreateInvoiceItemDto,
  InvoiceType,
  ServiceType,
  PaymentStatus,
  PaymentMethod,
} from "../src/database/models/invoice.model";

// Initialize Supabase client with admin privileges
const supabaseUrl = process.env.supabase_url || "";
const supabaseServiceKey = process.env.supabase_service_role || "";

if (!supabaseUrl || !supabaseServiceKey) {
  console.error(
    "Error: supabase_url and supabase_service_role must be set in .env.local"
  );
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseServiceKey);

// Admin user credentials
const ADMIN_EMAIL = "<EMAIL>";
const ADMIN_PASSWORD = "Admin@123";

// Service type distribution
const serviceTypes = Object.values(ServiceType);

// Sample project names by service type
const projectNamesByType: Record<ServiceType, string[]> = {
  [ServiceType.HCM]: [
    "Employee Onboarding System",
    "Performance Review Portal",
    "HR Analytics Dashboard",
    "Recruitment Management System",
  ],
  [ServiceType.ORDEV]: [
    "Leadership Development Program",
    "Team Structure Optimization",
    "Department Reorganization",
    "Corporate Culture Enhancement",
  ],
  [ServiceType.BE]: [
    "Backend API Development",
    "Database Optimization",
    "Authentication System",
    "Payment Integration Service",
  ],
  [ServiceType.IT]: [
    "Infrastructure Migration",
    "Security Audit",
    "Cloud Deployment",
    "Network Optimization",
  ],
  [ServiceType.MARKETING]: [
    "Digital Marketing Campaign",
    "Brand Identity Redesign",
    "SEO Optimization",
    "Social Media Strategy",
  ],
  [ServiceType.FINANCE]: [
    "Financial Reporting System",
    "Budget Planning Tool",
    "Expense Management Portal",
    "Financial Analytics Dashboard",
  ],
  [ServiceType.SALES]: [
    "CRM Implementation",
    "Sales Pipeline Optimization",
    "Lead Generation System",
    "Sales Analytics Platform",
  ],
  [ServiceType.OTHER]: [
    "General Consulting",
    "Strategic Planning",
    "Custom Solutions",
    "Process Optimization",
  ],
};

// Sample invoice items by service type
const invoiceItemsByType: Record<ServiceType, CreateInvoiceItemDto[]> = {
  [ServiceType.HCM]: [
    {
      item_name: "HR Process Design",
      item_amount: 1,
      item_price: 15000000,
      total_price: 15000000,
    },
    {
      item_name: "Employee Portal Development",
      item_amount: 1,
      item_price: 20000000,
      total_price: 20000000,
    },
    {
      item_name: "User Training",
      item_amount: 2,
      item_price: 5000000,
      total_price: 10000000,
    },
    {
      item_name: "Documentation",
      item_amount: 1,
      item_price: 7500000,
      total_price: 7500000,
    },
  ],
  [ServiceType.ORDEV]: [
    {
      item_name: "Organization Assessment",
      item_amount: 1,
      item_price: 18000000,
      total_price: 18000000,
    },
    {
      item_name: "Team Building Workshop",
      item_amount: 2,
      item_price: 12000000,
      total_price: 24000000,
    },
    {
      item_name: "Leadership Coaching",
      item_amount: 4,
      item_price: 8000000,
      total_price: 32000000,
    },
    {
      item_name: "Organizational Analysis Report",
      item_amount: 1,
      item_price: 15000000,
      total_price: 15000000,
    },
  ],
  [ServiceType.BE]: [
    {
      item_name: "API Development",
      item_amount: 1,
      item_price: 25000000,
      total_price: 25000000,
    },
    {
      item_name: "Database Design",
      item_amount: 1,
      item_price: 15000000,
      total_price: 15000000,
    },
    {
      item_name: "Performance Optimization",
      item_amount: 1,
      item_price: 12000000,
      total_price: 12000000,
    },
    {
      item_name: "Integration Services",
      item_amount: 1,
      item_price: 18000000,
      total_price: 18000000,
    },
  ],
  [ServiceType.IT]: [
    {
      item_name: "Infrastructure Setup",
      item_amount: 1,
      item_price: 30000000,
      total_price: 30000000,
    },
    {
      item_name: "Security Implementation",
      item_amount: 1,
      item_price: 22000000,
      total_price: 22000000,
    },
    {
      item_name: "Monitoring Setup",
      item_amount: 1,
      item_price: 15000000,
      total_price: 15000000,
    },
    {
      item_name: "IT Support (Monthly)",
      item_amount: 1,
      item_price: 10000000,
      total_price: 10000000,
    },
  ],
  [ServiceType.MARKETING]: [
    {
      item_name: "Marketing Strategy",
      item_amount: 1,
      item_price: 20000000,
      total_price: 20000000,
    },
    {
      item_name: "Content Creation",
      item_amount: 10,
      item_price: 2000000,
      total_price: 20000000,
    },
    {
      item_name: "Digital Campaign",
      item_amount: 1,
      item_price: 15000000,
      total_price: 15000000,
    },
    {
      item_name: "Analytics Report",
      item_amount: 1,
      item_price: 8000000,
      total_price: 8000000,
    },
  ],
  [ServiceType.FINANCE]: [
    {
      item_name: "Financial System Setup",
      item_amount: 1,
      item_price: 25000000,
      total_price: 25000000,
    },
    {
      item_name: "Financial Process Design",
      item_amount: 1,
      item_price: 20000000,
      total_price: 20000000,
    },
    {
      item_name: "Reporting Dashboard",
      item_amount: 1,
      item_price: 15000000,
      total_price: 15000000,
    },
    {
      item_name: "Financial Training",
      item_amount: 2,
      item_price: 7500000,
      total_price: 15000000,
    },
  ],
  [ServiceType.SALES]: [
    {
      item_name: "Sales Process Optimization",
      item_amount: 1,
      item_price: 18000000,
      total_price: 18000000,
    },
    {
      item_name: "CRM Implementation",
      item_amount: 1,
      item_price: 30000000,
      total_price: 30000000,
    },
    {
      item_name: "Sales Training",
      item_amount: 3,
      item_price: 8000000,
      total_price: 24000000,
    },
    {
      item_name: "Performance Analytics",
      item_amount: 1,
      item_price: 15000000,
      total_price: 15000000,
    },
  ],
  [ServiceType.OTHER]: [
    {
      item_name: "Consulting Service",
      item_amount: 1,
      item_price: 20000000,
      total_price: 20000000,
    },
    {
      item_name: "Implementation Support",
      item_amount: 1,
      item_price: 25000000,
      total_price: 25000000,
    },
    {
      item_name: "Project Management",
      item_amount: 1,
      item_price: 15000000,
      total_price: 15000000,
    },
    {
      item_name: "Quality Assurance",
      item_amount: 1,
      item_price: 10000000,
      total_price: 10000000,
    },
  ],
};

/**
 * Generates a random date between start and end dates (inclusive)
 */
function randomDate(start: Date, end: Date): Date {
  return new Date(
    start.getTime() + Math.random() * (end.getTime() - start.getTime())
  );
}

/**
 * Formats a date to YYYY-MM-DD
 */
function formatDate(date: Date): string {
  return date.toISOString().split("T")[0];
}

/**
 * Converts a 0-indexed month number (0-11) to Roman numeral
 */
function monthToRoman(month: number): string {
  const romanNumerals = [
    "I",
    "II",
    "III",
    "IV",
    "V",
    "VI",
    "VII",
    "VIII",
    "IX",
    "X",
    "XI",
    "XII",
  ];

  // Validate month is in valid range
  if (month < 0 || month > 11) {
    throw new Error(
      `Invalid month index: ${month}. Month must be between 0-11`
    );
  }

  return romanNumerals[month];
}

/**
 * Generates invoice number in the format: Number/ServiceType/Kasuat/Month(Roman)/Year
 */
function generateInvoiceNumber(
  serviceType: ServiceType,
  sequenceNumber: number,
  date: Date
): string {
  const year = date.getFullYear();
  const month = date.getMonth(); // 0-indexed (0 = January)

  // Format the sequence number with leading zeros (e.g., 007)
  const formattedSequence = String(sequenceNumber).padStart(3, "0");
  const romanMonth = monthToRoman(month);

  // Construct the invoice number
  return `${formattedSequence}/${serviceType}/Kasuat/${romanMonth}/${year}`;
}

/**
 * Get random items for an invoice based on service type
 */
function getRandomInvoiceItems(
  serviceType: ServiceType,
  count: number
): CreateInvoiceItemDto[] {
  const availableItems = invoiceItemsByType[serviceType];
  const selectedItems: CreateInvoiceItemDto[] = [];

  // Ensure we don't ask for more items than available
  const itemCount = Math.min(count, availableItems.length);

  // Shuffle array and get the first n items
  const shuffled = [...availableItems].sort(() => 0.5 - Math.random());

  for (let i = 0; i < itemCount; i++) {
    // Create a copy of the item to avoid mutating the original
    const item = { ...shuffled[i] };

    // Randomize amount slightly (between 1-3)
    if (Math.random() > 0.7) {
      const newAmount = Math.floor(Math.random() * 3) + 1;
      item.item_amount = newAmount;
      item.total_price = item.item_price * newAmount;
    }

    selectedItems.push(item);
  }

  return selectedItems;
}

/**
 * Calculate total amount from invoice items
 */
function calculateTotalAmount(items: CreateInvoiceItemDto[]): number {
  return items.reduce((sum, item) => sum + item.total_price, 0);
}

/**
 * Authenticate as admin user and return the user ID
 */
async function authenticateAdmin() {
  console.log(`Authenticating as admin user (${ADMIN_EMAIL})...`);

  try {
    const { data, error } = await supabase.auth.signInWithPassword({
      email: ADMIN_EMAIL,
      password: ADMIN_PASSWORD,
    });

    if (error) {
      console.error("Error authenticating as admin:", error.message);
      process.exit(1);
    }

    console.log(
      `Authenticated as admin successfully! User ID: ${data.user.id}`
    );
    return data.user.id;
  } catch (err) {
    console.error("Unexpected error during authentication:", err);
    process.exit(1);
  }
}

/**
 * Get all organizations to use as invoice recipients
 */
async function getOrganizations() {
  console.log("Fetching organizations...");

  try {
    const { data, error } = await supabase
      .from("organizations")
      .select("id, name")
      .order("name");

    if (error) {
      console.error("Error fetching organizations:", error.message);
      process.exit(1);
    }

    if (!data || data.length === 0) {
      console.error(
        "No organizations found. Please run create-organizations.ts first."
      );
      process.exit(1);
    }

    console.log(`Found ${data.length} organizations.`);
    return data;
  } catch (err) {
    console.error("Unexpected error fetching organizations:", err);
    process.exit(1);
  }
}

/**
 * Create a single invoice with its items
 */
async function createInvoice(invoiceData: CreateInvoiceDto, userId: string) {
  console.log(`Creating invoice for ${invoiceData.recipient_name}...`);

  try {
    const timestamp = new Date().toISOString();

    // Start a transaction for the invoice and its items
    const { data, error } = await supabase
      .from("invoices")
      .insert({
        invoice_number: invoiceData.invoice_number,
        invoice_type: invoiceData.invoice_type,
        service_type: invoiceData.service_type,
        recipient_name: invoiceData.recipient_name,
        project_name: invoiceData.project_name,
        due_date: invoiceData.due_date,
        payment_method: invoiceData.payment_method,
        payment_status: invoiceData.payment_status || PaymentStatus.PENDING,
        notes: invoiceData.notes,
        total_amount: invoiceData.total_amount,
        created_at: timestamp,
        created_by: userId,
      })
      .select()
      .single();

    if (error) {
      console.error(
        `Error creating invoice for "${invoiceData.recipient_name}":`,
        error.message
      );
      return null;
    }

    // Now create all invoice items
    const invoiceItems = invoiceData.items.map((item) => ({
      ...item,
      invoice_id: data.id,
      created_at: timestamp,
      created_by: userId,
    }));

    const { error: itemsError } = await supabase
      .from("invoice_items")
      .insert(invoiceItems);

    if (itemsError) {
      console.error(
        `Error creating invoice items for "${invoiceData.recipient_name}":`,
        itemsError.message
      );
      // Consider deleting the invoice in case of error
      return null;
    }

    console.log(
      `Invoice created with ID: ${data.id} and ${invoiceItems.length} items`
    );
    return data;
  } catch (err) {
    console.error(
      `Unexpected error creating invoice for "${invoiceData.recipient_name}":`,
      err
    );
    return null;
  }
}

/**
 * Generate a sample invoice
 */
function generateSampleInvoice(
  serviceType: ServiceType,
  sequenceNumber: number,
  organization: any
): CreateInvoiceDto {
  // Generate invoice date and due date
  const today = new Date();
  const invoiceDate = randomDate(
    new Date(today.getFullYear(), today.getMonth() - 1, 1),
    today
  );

  // Due date between 15-30 days after invoice date
  const dueDate = new Date(invoiceDate);
  dueDate.setDate(dueDate.getDate() + 15 + Math.floor(Math.random() * 15));

  // Get random invoice items (between 2-4 items)
  const itemCount = Math.floor(Math.random() * 3) + 2; // 2-4 items
  const items = getRandomInvoiceItems(serviceType, itemCount);
  const totalAmount = calculateTotalAmount(items);

  // Select a random project name for this service type
  const projectName =
    projectNamesByType[serviceType][
      Math.floor(Math.random() * projectNamesByType[serviceType].length)
    ];

  // Random payment status weighted towards pending
  const paymentStatusValues = Object.values(PaymentStatus);
  const paymentStatusWeights = [0.4, 0.2, 0.2, 0.1, 0.1]; // pending has higher weight

  let paymentStatus;
  const randomValue = Math.random();
  let cumulativeWeight = 0;

  for (let i = 0; i < paymentStatusWeights.length; i++) {
    cumulativeWeight += paymentStatusWeights[i];
    if (randomValue <= cumulativeWeight) {
      paymentStatus = paymentStatusValues[i];
      break;
    }
  }

  // Random payment method
  const paymentMethods = Object.values(PaymentMethod);
  const paymentMethod =
    paymentMethods[Math.floor(Math.random() * paymentMethods.length)];

  // Random invoice type (80% external, 20% internal)
  const invoiceType =
    Math.random() < 0.8 ? InvoiceType.EXTERNAL : InvoiceType.INTERNAL;

  // Generate a random note
  const notes = [
    "Please pay before due date",
    "Thank you for your business",
    "Payment via bank transfer preferred",
    "Contact <EMAIL> for payment information",
    "Include invoice number in payment reference",
    null, // 1/6 chance of null note
  ];

  const note = notes[Math.floor(Math.random() * notes.length)];

  return {
    invoice_number: generateInvoiceNumber(
      serviceType,
      sequenceNumber,
      invoiceDate
    ),
    invoice_type: invoiceType,
    service_type: serviceType,
    recipient_name: organization.name,
    project_name: projectName,
    due_date: formatDate(dueDate),
    payment_method: paymentMethod,
    payment_status: paymentStatus,
    notes: note,
    total_amount: totalAmount,
    items: items,
  };
}

/**
 * Create all invoices
 */
async function createAllInvoices() {
  console.log("Starting creation of invoices...");

  // First authenticate as admin
  const adminUserId = await authenticateAdmin();
  if (!adminUserId) {
    console.error("Failed to authenticate as admin. Cannot proceed.");
    process.exit(1);
  }

  // Get organizations for recipients
  const organizations = await getOrganizations();
  if (!organizations || organizations.length === 0) {
    console.error("No organizations found. Cannot proceed.");
    process.exit(1);
  }

  // Parse command line arguments
  let invoiceCount = 15; // Default count
  let filterServiceType = null;
  let filterStatus = null;

  process.argv.forEach((arg) => {
    if (arg.startsWith("--count=")) {
      invoiceCount = parseInt(arg.split("=")[1], 10);
    } else if (arg.startsWith("--service-type=")) {
      filterServiceType = arg.split("=")[1];
    } else if (arg.startsWith("--status=")) {
      filterStatus = arg.split("=")[1];
    }
  });

  console.log(`Generating ${invoiceCount} invoices...`);

  // Distribute invoices across service types
  const results = [];
  let sequenceNumber = 1;

  // For more even distribution, we'll cycle through service types
  let serviceTypeIndex = 0;

  for (let i = 0; i < invoiceCount; i++) {
    // Select service type
    let serviceType: ServiceType;

    if (
      filterServiceType &&
      Object.values(ServiceType).includes(filterServiceType as ServiceType)
    ) {
      serviceType = filterServiceType as ServiceType;
    } else {
      serviceType = serviceTypes[serviceTypeIndex % serviceTypes.length];
      serviceTypeIndex++;
    }

    // Select random organization
    const randomOrgIndex = Math.floor(Math.random() * organizations.length);
    const organization = organizations[randomOrgIndex];

    // Generate invoice data
    const invoiceData = generateSampleInvoice(
      serviceType,
      sequenceNumber,
      organization
    );

    // Apply status filter if specified
    if (
      filterStatus &&
      Object.values(PaymentStatus).includes(filterStatus as PaymentStatus)
    ) {
      invoiceData.payment_status = filterStatus as PaymentStatus;
    }

    // Create the invoice
    const result = await createInvoice(invoiceData, adminUserId);
    if (result) {
      results.push(result);
      sequenceNumber++;
    }
  }

  console.log(`\nCreated ${results.length} invoices successfully!`);
  console.log("\nInvoice creation process completed!");
}

createAllInvoices();
