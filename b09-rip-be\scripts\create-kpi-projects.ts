// Load environment variables from .env.local
process.env.NODE_TLS_REJECT_UNAUTHORIZED = "0";

import { config } from "dotenv";
config({ path: ".env.local" });

import { createClient } from "@supabase/supabase-js";
import {
  KpiStatus,
  CreateKpiProjectDto,
} from "../src/database/models/kpi-project.model";
import { parseISO, format } from "date-fns";

// Initialize Supabase client with admin privileges
const supabaseUrl = process.env.supabase_url || "";
const supabaseServiceKey = process.env.supabase_service_role || "";

if (!supabaseUrl || !supabaseServiceKey) {
  console.error(
    "Error: supabase_url and supabase_service_role must be set in .env.local"
  );
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseServiceKey);

// Admin user credentials
const ADMIN_EMAIL = "<EMAIL>";
const ADMIN_PASSWORD = "Admin@123";

// KPI description templates
const KPI_DESCRIPTION_TEMPLATES = [
  "Achieve {{percentage}}% client satisfaction rating for the project",
  "Complete project within {{percentage}}% of the allocated budget",
  "Deliver project milestones on time with less than {{days}} days of delay",
  "Achieve {{percentage}}% of the defined project objectives",
  "Implement {{percentage}}% of the requested features with high quality",
  "Maintain team productivity at {{percentage}}% efficiency throughout the project",
  "Resolve {{percentage}}% of identified issues within {{days}} business days",
  "Achieve {{percentage}}% test coverage for all developed components",
  "Reduce project risks by {{percentage}}% through proactive management",
  "Ensure {{percentage}}% compliance with industry standards and regulations",
  "Achieve {{percentage}}% stakeholder engagement throughout the project lifecycle",
  "Maintain documentation quality at {{percentage}}% completeness",
  "Deliver {{percentage}}% of the project scope as defined in requirements",
  "Achieve {{percentage}}% user adoption rate after implementation",
  "Reduce system downtime to less than {{hours}} hours during implementation",
];

// KPI target templates
const KPI_TARGET_TEMPLATES = [
  "{{percentage}}% satisfaction rating based on client feedback surveys",
  "Budget variance < {{percentage}}% of allocated budget",
  "All milestones completed within {{days}} days of scheduled dates",
  "{{percentage}}% of objectives achieved as measured by project scorecard",
  "{{percentage}}% of features implemented and passing quality assurance",
  "Team productivity maintained at {{percentage}}% based on velocity metrics",
  "{{percentage}}% of issues resolved within {{days}} business days",
  "Code test coverage of {{percentage}}% across all components",
  "Risk impact reduced by {{percentage}}% compared to initial assessment",
  "{{percentage}}% compliance with all applicable standards and regulations",
  "{{percentage}}% stakeholder attendance and participation in project meetings",
  "Documentation completeness and accuracy at {{percentage}}%",
  "{{percentage}}% of defined scope delivered and accepted by client",
  "{{percentage}}% user adoption rate within {{days}} days of deployment",
  "System availability of {{percentage}}% during implementation phase",
];

// KPI additional notes templates
const KPI_NOTES_TEMPLATES = [
  "This KPI will be measured through regular client feedback surveys",
  "Budget tracking will be performed weekly to ensure compliance",
  "Timeline adherence will be monitored through the project management tool",
  "Objectives achievement will be evaluated at each milestone",
  "Feature implementation will be tracked through the development backlog",
  "Team productivity will be measured using sprint velocity metrics",
  "Issue resolution time will be tracked through the issue management system",
  "Test coverage will be verified through automated testing tools",
  "Risk reduction will be evaluated through regular risk assessment meetings",
  "Compliance will be verified through regular audits",
  "Stakeholder engagement will be measured through meeting attendance and feedback",
  "Documentation quality will be reviewed by the quality assurance team",
  "Scope delivery will be tracked through the requirements traceability matrix",
  "User adoption will be measured through system usage analytics",
  "System availability will be monitored through the monitoring tools",
];

// Function to get random item from an array
function getRandomItem<T>(arr: T[]): T {
  return arr[Math.floor(Math.random() * arr.length)];
}

// Function to get random number between min and max (inclusive)
function getRandomNumber(min: number, max: number): number {
  return Math.floor(Math.random() * (max - min + 1)) + min;
}

// Function to extract period from date (e.g., "2023-Q2")
function extractPeriodFromDate(dateStr: string): string {
  try {
    const date = parseISO(dateStr);
    const year = format(date, "yyyy");
    const month = parseInt(format(date, "M"));
    const quarter = Math.ceil(month / 3);
    return `${year}-Q${quarter}`;
  } catch (error) {
    console.error(`Error extracting period from date ${dateStr}:`, error);
    return "Unknown";
  }
}

// Function to generate a random KPI description
function generateKpiDescription(): string {
  const template = getRandomItem(KPI_DESCRIPTION_TEMPLATES);
  const percentage = getRandomNumber(70, 99);
  const days = getRandomNumber(2, 14);
  const hours = getRandomNumber(1, 8);

  return template
    .replace("{{percentage}}", percentage.toString())
    .replace("{{days}}", days.toString())
    .replace("{{hours}}", hours.toString());
}

// Function to generate a random KPI target
function generateKpiTarget(): string {
  const template = getRandomItem(KPI_TARGET_TEMPLATES);
  const percentage = getRandomNumber(70, 99);
  const days = getRandomNumber(7, 30);

  return template
    .replace("{{percentage}}", percentage.toString())
    .replace("{{days}}", days.toString());
}

/**
 * Authenticate as admin user and return the user ID
 */
async function authenticateAdmin() {
  console.log(`Authenticating as admin user (${ADMIN_EMAIL})...`);

  try {
    const { data, error } = await supabase.auth.signInWithPassword({
      email: ADMIN_EMAIL,
      password: ADMIN_PASSWORD,
    });

    if (error) {
      console.error("Error authenticating as admin:", error.message);
      process.exit(1);
    }

    console.log(
      `Authenticated as admin successfully! User ID: ${data.user.id}`
    );
    return data.user.id;
  } catch (err) {
    console.error("Unexpected error during authentication:", err);
    process.exit(1);
  }
}

/**
 * Get all projects from the database
 */
async function getAllProjects() {
  try {
    const { data, error } = await supabase
      .from("projects")
      .select(
        "id, project_name, start_project, end_project, objectives, status_project"
      )
      .is("deleted_at", null);

    if (error) {
      console.error("Error fetching projects:", error.message);
      return [];
    }

    return data;
  } catch (err) {
    console.error("Unexpected error fetching projects:", err);
    return [];
  }
}

/**
 * Check if a KPI project already exists for a project with the same description
 */
async function kpiProjectExistsForProject(
  projectId: string,
  description: string
): Promise<boolean> {
  try {
    const { data, error } = await supabase
      .from("kpi_projects")
      .select("id")
      .eq("project_id", projectId)
      .eq("description", description)
      .is("deleted_at", null)
      .maybeSingle();

    return !!data;
  } catch (err) {
    console.error("Error checking for existing KPI project:", err);
    return false;
  }
}

/**
 * Create a single KPI project
 */
async function createKpiProject(
  kpiProjectData: CreateKpiProjectDto,
  userId: string
) {
  console.log(
    `Creating KPI project for project ID ${kpiProjectData.project_id}: ${kpiProjectData.description}`
  );

  try {
    // Check if KPI project already exists for this project with the same description
    if (
      await kpiProjectExistsForProject(
        kpiProjectData.project_id,
        kpiProjectData.description
      )
    ) {
      console.log(
        `KPI project for project ID ${kpiProjectData.project_id} with description "${kpiProjectData.description}" already exists. Skipping.`
      );
      return null;
    }

    const timestamp = new Date().toISOString();

    const { data, error } = await supabase
      .from("kpi_projects")
      .insert({
        ...kpiProjectData,
        created_at: timestamp,
        created_by: userId,
      })
      .select()
      .single();

    if (error) {
      console.error(
        `Error creating KPI project for project ID ${kpiProjectData.project_id}:`,
        error.message
      );
      return null;
    }

    console.log(`KPI project created with ID: ${data.id}`);
    return data;
  } catch (err) {
    console.error(
      `Unexpected error creating KPI project for project ID ${kpiProjectData.project_id}:`,
      err
    );
    return null;
  }
}

/**
 * Generate random KPI projects for a project
 */
async function generateRandomKpiProjectsForProject(
  project: any,
  userId: string
) {
  if (!project || !project.id) {
    console.error("Invalid project data");
    return [];
  }

  // Extract period from project start date
  const period = extractPeriodFromDate(project.start_project);

  console.log(
    `Generating KPI projects for project: ${project.project_name} (Period: ${period})`
  );

  const createdKpiProjects = [];

  // Determine how many KPI projects to create (1-3 per project)
  const kpiCount = getRandomNumber(1, 3);

  for (let i = 0; i < kpiCount; i++) {
    // Generate KPI data
    const description = generateKpiDescription();
    const target = generateKpiTarget();

    // Determine KPI status based on project status
    let status: KpiStatus;

    switch (project.status_project) {
      case "Not Started":
        status = KpiStatus.NOT_STARTED;
        break;
      case "In Progress":
        // For in-progress projects, randomly choose between NOT_STARTED and IN_PROGRESS
        status =
          Math.random() > 0.3 ? KpiStatus.IN_PROGRESS : KpiStatus.NOT_STARTED;
        break;
      case "Completed":
        // For completed projects, randomly choose between the COMPLETED statuses
        const completedStatuses = [
          KpiStatus.COMPLETED_BELOW_TARGET,
          KpiStatus.COMPLETED_ON_TARGET,
          KpiStatus.COMPLETED_ABOVE_TARGET,
        ];
        status = getRandomItem(completedStatuses);
        break;
      case "Cancelled":
        // For cancelled projects, use either NOT_STARTED or IN_PROGRESS
        status =
          Math.random() > 0.5 ? KpiStatus.NOT_STARTED : KpiStatus.IN_PROGRESS;
        break;
      default:
        status = KpiStatus.NOT_STARTED;
    }

    // Create KPI project data
    const kpiProjectData: CreateKpiProjectDto = {
      project_name: `KPI ${i + 1} for ${project.project_name}`,
      project_id: project.id,
      description,
      target,
      period,
      status,
      additional_notes: getRandomItem(KPI_NOTES_TEMPLATES),
    };

    // Create the KPI project
    const kpiProject = await createKpiProject(kpiProjectData, userId);
    if (kpiProject) {
      createdKpiProjects.push(kpiProject);
    }
  }

  return createdKpiProjects;
}

/**
 * Create KPI projects for all projects
 */
async function createKpiProjectsForAllProjects(userId: string) {
  // Get all projects
  const projects = await getAllProjects();
  if (projects.length === 0) {
    console.error("No projects found in the system.");
    return;
  }

  console.log(`Found ${projects.length} projects.`);

  let createdCount = 0;

  // Create KPI projects for each project
  for (const project of projects) {
    const kpiProjects = await generateRandomKpiProjectsForProject(
      project,
      userId
    );
    createdCount += kpiProjects.length;
  }

  console.log("\nKPI project creation summary:");
  console.log(`- Total created: ${createdCount}`);
}

/**
 * Create KPI projects for a specific project
 */
async function createKpiProjectsForProject(projectId: string, userId: string) {
  // Get the project
  const { data: project, error } = await supabase
    .from("projects")
    .select(
      "id, project_name, start_project, end_project, objectives, status_project"
    )
    .eq("id", projectId)
    .is("deleted_at", null)
    .single();

  if (error || !project) {
    console.error(`Project with ID ${projectId} not found.`);
    return;
  }

  console.log(`Creating KPI projects for project: ${project.project_name}`);

  // Generate KPI projects for the project
  const kpiProjects = await generateRandomKpiProjectsForProject(
    project,
    userId
  );

  console.log("\nKPI project creation summary for this project:");
  console.log(`- Total created: ${kpiProjects.length}`);
}

/**
 * Main function to run the script
 */
async function main() {
  console.log("Starting KPI project creation script...");

  // Authenticate as admin
  const userId = await authenticateAdmin();

  const projectParam = process.argv[2];

  if (projectParam && projectParam.startsWith("--project=")) {
    const projectId = projectParam.split("=")[1];
    await createKpiProjectsForProject(projectId, userId);
  } else {
    await createKpiProjectsForAllProjects(userId);
  }

  console.log("\nKPI project creation completed!");
}

// Run the main function
main();
