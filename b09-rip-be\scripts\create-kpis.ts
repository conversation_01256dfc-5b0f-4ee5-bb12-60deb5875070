// Load environment variables from .env.local
import { config } from "dotenv";
config({ path: ".env.local" });

import { createClient } from "@supabase/supabase-js";
import { CreateKpiDto, KpiStatus } from "../src/database/models/kpi.model";

// Initialize Supabase client with admin privileges
const supabaseUrl = process.env.supabase_url || "";
const supabaseServiceKey = process.env.supabase_service_role || "";

if (!supabaseUrl || !supabaseServiceKey) {
  console.error(
    "Error: supabase_url and supabase_service_role must be set in .env.local"
  );
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseServiceKey);

// Admin user credentials
const ADMIN_EMAIL = "<EMAIL>";
const ADMIN_PASSWORD = "Admin@123";

// KPI periods (quarters)
const KPI_PERIODS = [
  "2023-Q1",
  "2023-Q2",
  "2023-Q3",
  "2023-Q4",
  "2024-Q1",
  "2024-Q2",
];

// KPI template data to create variations from
const KPI_TEMPLATES = [
  {
    description: "Increase customer satisfaction score",
    target: "Achieve a 4.5+ rating out of 5",
  },
  {
    description: "Improve team productivity",
    target: "Increase output by 15% compared to previous quarter",
  },
  {
    description: "Reduce operational costs",
    target: "Cut departmental expenses by 10%",
  },
  {
    description: "Complete professional development training",
    target: "Finish 3 certified courses in relevant skills",
  },
  {
    description: "Implement new system feature",
    target: "Successfully deploy with less than 5 bugs",
  },
  {
    description: "Improve client onboarding process",
    target: "Reduce onboarding time from 7 days to 3 days",
  },
  {
    description: "Increase sales conversion rate",
    target: "Reach 25% conversion from leads to customers",
  },
  {
    description: "Enhance documentation quality",
    target: "Update all technical documents with 95% accuracy",
  },
  {
    description: "Reduce support ticket resolution time",
    target: "Decrease average resolution time by 30%",
  },
  {
    description: "Expand market reach",
    target: "Enter 2 new market segments with positive reception",
  },
];

// Function to get random items from an array
function getRandomItems<T>(arr: T[], count: number): T[] {
  const shuffled = [...arr].sort(() => 0.5 - Math.random());
  return shuffled.slice(0, count);
}

/**
 * Authenticate as admin user and return the user ID
 */
async function authenticateAdmin() {
  console.log(`Authenticating as admin user (${ADMIN_EMAIL})...`);

  try {
    const { data, error } = await supabase.auth.signInWithPassword({
      email: ADMIN_EMAIL,
      password: ADMIN_PASSWORD,
    });

    if (error) {
      console.error("Error authenticating as admin:", error.message);
      process.exit(1);
    }

    console.log(
      `Authenticated as admin successfully! User ID: ${data.user.id}`
    );
    return data.user.id;
  } catch (err) {
    console.error("Unexpected error during authentication:", err);
    process.exit(1);
  }
}

/**
 * Check if an employee exists in the database
 */
async function employeeExists(employeeId: string): Promise<boolean> {
  try {
    const { data, error } = await supabase
      .from("employees")
      .select("id")
      .eq("id", employeeId)
      .single();

    if (error || !data) {
      return false;
    }

    return true;
  } catch (err) {
    console.error(`Error checking if employee ${employeeId} exists:`, err);
    return false;
  }
}

/**
 * Get all active employees from the database
 */
async function getAllActiveEmployees() {
  try {
    const { data, error } = await supabase
      .from("user_profiles")
      .select("id, fullname, employee_id")
      .is("is_active", true)
      .not("employee_id", "is", null);

    if (error) {
      console.error("Error fetching employees:", error.message);
      return [];
    }

    return data;
  } catch (err) {
    console.error("Unexpected error fetching employees:", err);
    return [];
  }
}

/**
 * Check if a KPI already exists for an employee in a specific period
 */
async function kpiExistsForEmployeeAndPeriod(
  employeeId: string,
  period: string
): Promise<boolean> {
  try {
    const { data, error } = await supabase
      .from("kpis")
      .select("id")
      .eq("employee_id", employeeId)
      .eq("period", period)
      .is("deleted_at", null)
      .maybeSingle();

    return !!data;
  } catch (err) {
    console.error("Error checking for existing KPI:", err);
    return false;
  }
}

/**
 * Create a single KPI
 */
async function createKpi(kpiData: CreateKpiDto, userId: string) {
  try {
    // Check if employee exists
    if (!(await employeeExists(kpiData.employee_id))) {
      console.error(
        `Employee with ID ${kpiData.employee_id} does not exist. Skipping KPI creation.`
      );
      return null;
    }

    // Check if KPI already exists for this employee and period
    if (
      await kpiExistsForEmployeeAndPeriod(kpiData.employee_id, kpiData.period)
    ) {
      console.log(
        `KPI already exists for employee ${kpiData.full_name} (${kpiData.employee_id}) in period ${kpiData.period}. Skipping.`
      );
      return null;
    }

    const { data, error } = await supabase
      .from("kpis")
      .insert([
        {
          ...kpiData,
          created_by: userId,
        },
      ])
      .select()
      .single();

    if (error) {
      console.error("Error creating KPI:", error.message);
      return null;
    }

    console.log(
      `Created KPI for ${kpiData.full_name} (${kpiData.employee_id}) - Period: ${kpiData.period} - Status: ${kpiData.status}`
    );
    return data;
  } catch (err) {
    console.error("Unexpected error creating KPI:", err);
    return null;
  }
}

/**
 * Create KPIs for all employees across different periods
 */
async function createKpisForAllEmployees(userId: string) {
  // Get all active employees
  const employees = await getAllActiveEmployees();
  if (employees.length === 0) {
    console.error("No active employees found in the system.");
    return;
  }

  console.log(`Found ${employees.length} active employees.`);

  let createdCount = 0;
  let skippedCount = 0;

  // Create KPIs for each employee across different periods
  for (const employee of employees) {
    // Skip if employee_id is null
    if (!employee.employee_id) {
      console.log(`Skipping employee ${employee.fullname} - No employee_id`);
      skippedCount++;
      continue;
    }

    // Create 2-4 KPIs per employee with different periods
    const periodsToCreate = getRandomItems(
      KPI_PERIODS,
      Math.floor(Math.random() * 3) + 2
    );

    for (const period of periodsToCreate) {
      // Pick 1-3 random KPI templates for this employee and period
      const templatesForThisPeriod = getRandomItems(
        KPI_TEMPLATES,
        Math.floor(Math.random() * 3) + 1
      );

      for (const template of templatesForThisPeriod) {
        // Randomize the KPI status
        const statusValues = Object.values(KpiStatus);
        const randomStatus =
          statusValues[Math.floor(Math.random() * statusValues.length)];

        // Maybe assign a bonus for completed KPIs
        let bonusReceived = null;
        if (
          randomStatus === KpiStatus.COMPLETED_ON_TARGET ||
          randomStatus === KpiStatus.COMPLETED_ABOVE_TARGET
        ) {
          // Random bonus between 500,000 and 5,000,000
          bonusReceived = Math.floor(Math.random() * 4500000) + 500000;
        }

        const kpiData: CreateKpiDto = {
          full_name: employee.fullname,
          employee_id: employee.employee_id,
          description: template.description,
          target: template.target,
          period: period,
          status: randomStatus,
          bonus_received: bonusReceived,
          additional_notes: bonusReceived
            ? `Bonus of IDR ${bonusReceived.toLocaleString()} awarded for successful completion.`
            : null,
        };

        const result = await createKpi(kpiData, userId);
        if (result) {
          createdCount++;
        } else {
          skippedCount++;
        }
      }
    }
  }

  console.log("\nKPI creation summary:");
  console.log(`- Total created: ${createdCount}`);
  console.log(`- Total skipped: ${skippedCount}`);
}

/**
 * Create KPIs for a specific employee
 */
async function createKpisForEmployee(employeeId: string, userId: string) {
  // Check if employee exists
  const { data: profile, error } = await supabase
    .from("user_profiles")
    .select("id, fullname, employee_id")
    .eq("id", employeeId)
    .single();

  if (error || !profile || !profile.employee_id) {
    console.error(
      `Employee with ID ${employeeId} not found or has no employee record.`
    );
    return;
  }

  console.log(`Creating KPIs for employee: ${profile.fullname}`);

  let createdCount = 0;
  let skippedCount = 0;

  // Create KPIs for all periods
  for (const period of KPI_PERIODS) {
    // Pick 1-2 random KPI templates for this period
    const templatesForThisPeriod = getRandomItems(
      KPI_TEMPLATES,
      Math.floor(Math.random() * 2) + 1
    );

    for (const template of templatesForThisPeriod) {
      // Randomize the KPI status
      const statusValues = Object.values(KpiStatus);
      const randomStatus =
        statusValues[Math.floor(Math.random() * statusValues.length)];

      // Maybe assign a bonus for completed KPIs
      let bonusReceived = null;
      if (
        randomStatus === KpiStatus.COMPLETED_ON_TARGET ||
        randomStatus === KpiStatus.COMPLETED_ABOVE_TARGET
      ) {
        // Random bonus between 500,000 and 5,000,000
        bonusReceived = Math.floor(Math.random() * 4500000) + 500000;
      }

      const kpiData: CreateKpiDto = {
        full_name: profile.fullname,
        employee_id: profile.employee_id,
        description: template.description,
        target: template.target,
        period: period,
        status: randomStatus,
        bonus_received: bonusReceived,
        additional_notes: bonusReceived
          ? `Bonus of IDR ${bonusReceived.toLocaleString()} awarded for successful completion.`
          : null,
      };

      const result = await createKpi(kpiData, userId);
      if (result) {
        createdCount++;
      } else {
        skippedCount++;
      }
    }
  }

  console.log("\nKPI creation summary for this employee:");
  console.log(`- Total created: ${createdCount}`);
  console.log(`- Total skipped: ${skippedCount}`);
}

/**
 * Main function to run the script
 */
async function main() {
  console.log("Starting KPI creation script...");

  // Authenticate as admin
  const userId = await authenticateAdmin();

  const employeeParam = process.argv[2];

  if (employeeParam && employeeParam.startsWith("--employee=")) {
    const employeeId = employeeParam.split("=")[1];
    await createKpisForEmployee(employeeId, userId);
  } else {
    await createKpisForAllEmployees(userId);
  }

  console.log("\nKPI creation completed!");
}

// Run the main function
main();
