// Load environment variables from .env.local
import { config } from "dotenv";
config({ path: ".env.local" });

import { createClient } from "@supabase/supabase-js";
import { SalaryPaymentStatus } from "../src/database/models/salary.model";
import { addMonths, format, subMonths } from "date-fns";
import { toZonedTime } from "date-fns-tz";
import { SalaryService } from "../src/modules/salary/service";
import { randomUUID } from "crypto";

// Initialize Supabase client with admin privileges
const supabaseUrl = process.env.supabase_url || "";
const supabaseServiceKey = process.env.supabase_service_role || "";

if (!supabaseUrl || !supabaseServiceKey) {
  console.error(
    "Error: supabase_url and supabase_service_role must be set in .env.local"
  );
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseServiceKey);

// Admin user credentials
const ADMIN_EMAIL = "<EMAIL>";
const ADMIN_PASSWORD = "Admin@123";

// Jakarta timezone
const TIMEZONE = "Asia/Jakarta";

// Define attendance status type
type AttendanceStatus = "present" | "permit" | "leave" | "absent" | "missing";

// Base salaries by role
const BASE_SALARIES: Record<string, number> = {
  Manager: 10000000,
  HR: 7500000,
  Finance: 8000000,
  Operation: 7000000,
  Admin: 6000000,
};

// Attendance deduction factors
const ATTENDANCE_DEDUCTIONS: Record<AttendanceStatus, number> = {
  present: 0,
  permit: 0.5,
  leave: 1,
  absent: 1,
  missing: 1,
};

/**
 * Get current date in Jakarta timezone
 */
function getCurrentJakartaDate(): Date {
  const now = new Date();
  return toZonedTime(now, TIMEZONE);
}

/**
 * Format date to YYYY-MM format for salary period
 */
function formatSalaryPeriod(date: Date): string {
  return format(date, "yyyy-MM");
}

/**
 * Authenticate as admin user and return the user ID
 */
async function authenticateAdmin() {
  console.log(`Authenticating as admin user (${ADMIN_EMAIL})...`);

  try {
    const { data, error } = await supabase.auth.signInWithPassword({
      email: ADMIN_EMAIL,
      password: ADMIN_PASSWORD,
    });

    if (error) {
      console.error("Error authenticating as admin:", error.message);
      process.exit(1);
    }

    console.log(
      `Authenticated as admin successfully! User ID: ${data.user.id}`
    );
    return data.user.id;
  } catch (err) {
    console.error("Unexpected error during authentication:", err);
    process.exit(1);
  }
}

/**
 * Check if salaries exist for a specific period
 */
async function salariesExistForPeriod(period: string): Promise<boolean> {
  try {
    const { data, error } = await SalaryService.getByPeriod(period);

    if (error) {
      console.error("Error checking for existing salaries:", error);
      return false;
    }

    return data && data.length > 0;
  } catch (err) {
    console.error("Unexpected error checking for existing salaries:", err);
    return false;
  }
}

/**
 * Get the last 2 months (including current month) in YYYY-MM format
 */
function getLastTwoMonths(): string[] {
  const currentDate = getCurrentJakartaDate();
  const periods = [];

  for (let i = 0; i < 2; i++) {
    const date = subMonths(currentDate, i);
    periods.push(formatSalaryPeriod(date));
  }

  return periods;
}

/**
 * Generate salaries for a specific period
 */
async function generateSalariesForPeriod(
  period: string,
  userId: string,
  forceGeneration: boolean = false
): Promise<number> {
  try {
    // Extract year and month from period string (format: YYYY-MM)
    const [year, month] = period.split("-").map(Number);

    console.log(`\nGenerating salaries for period: ${period}`);

    // Check if salaries already exist for this period
    const salariesExist = await salariesExistForPeriod(period);

    if (salariesExist && !forceGeneration) {
      console.log(
        `Salaries already exist for period ${period}. Use --force to regenerate.`
      );
      return 0;
    }

    // For past months, we need to use our custom implementation
    const count = await generateSalariesForMonth(
      year,
      month,
      userId,
      forceGeneration
    );
    console.log(
      `Successfully generated ${count} salary records for period ${period}`
    );

    return count;
  } catch (err) {
    console.error(
      `Unexpected error generating salaries for period ${period}:`,
      err
    );
    return 0;
  }
}

/**
 * Custom implementation to generate salaries for a specific month
 * Based on SalaryService.generateMonthlySalaries but modified to work with past months
 */
async function generateSalariesForMonth(
  year: number,
  month: number,
  userId: string,
  forceGeneration: boolean = false
): Promise<number> {
  try {
    // Get all employees
    const employees = await supabase
      .from("employees")
      .select(
        `
        id, 
        profile_id, 
        department, 
        bank_account,
        bank_name,
        presence_status
      `
      )
      .is("deleted_at", null);

    if (!employees.data || employees.data.length === 0) {
      console.log("No active employees found.");
      return 0;
    }

    // Get employee profiles to determine roles and active status
    const profileIds = employees.data.map((emp) => emp.profile_id);

    const profiles = await supabase
      .from("user_profiles")
      .select(
        `
        id,
        role,
        fullname,
        is_active
      `
      )
      .in("id", profileIds)
      .is("deleted_at", null);

    if (!profiles.data) {
      console.log("Failed to fetch employee profiles.");
      return 0;
    }

    // Map profiles for easy lookup
    const profileMap = new Map();
    profiles.data.forEach((profile) => {
      profileMap.set(profile.id, profile);
    });

    // Filter for valid employees (active with valid roles)
    const validEmployees = employees.data
      .map((employee) => {
        const profile = profileMap.get(employee.profile_id) || {};
        const isActive = profile.is_active === true;
        const role = profile.role || "";
        const hasValidRole = Object.keys(BASE_SALARIES)
          .map((r) => r.toLowerCase())
          .includes(role.toLowerCase());

        return {
          ...employee,
          role: profile.role || "",
          fullname: profile.fullname || "Unknown",
          isActive,
          hasValidRole,
        };
      })
      .filter((employee) => employee.isActive && employee.hasValidRole);

    console.log(`Found ${validEmployees.length} valid employees.`);

    // Get all attendance records for this month
    const period = `${year}-${month.toString().padStart(2, "0")}`;

    const attendances = await supabase
      .from("attendances")
      .select(
        `
        id,
        date,
        employee_id,
        status,
        clock_in,
        clock_out,
        notes
      `
      )
      .like("date", `${period}%`)
      .is("deleted_at", null);

    if (!attendances.data) {
      console.log(`No attendance records found for period ${period}.`);
    }

    // Check for existing salaries for this period
    const existingSalaries = await supabase
      .from("salaries")
      .select("*")
      .eq("period", period)
      .is("deleted_at", null);

    // Map of employees who already have salaries
    const employeesWithSalaries = new Set();

    if (existingSalaries.data && existingSalaries.data.length > 0) {
      existingSalaries.data.forEach((salary) => {
        employeesWithSalaries.add(salary.employee_id);
      });
    }

    let createdCount = 0;

    // Process each employee
    for (const employee of validEmployees) {
      try {
        // Skip if already has salary and not forcing regeneration
        if (employeesWithSalaries.has(employee.id) && !forceGeneration) {
          continue;
        }

        // Get base salary for employee's role
        const baseSalary = BASE_SALARIES[employee.role] || null;
        if (baseSalary === null) {
          console.log(`No base salary defined for role: ${employee.role}`);
          continue;
        }

        // Calculate attendance data
        const employeeAttendances =
          attendances.data?.filter((a) => a.employee_id === employee.id) || [];

        // Calculate working days in month
        const daysInMonth = new Date(year, month, 0).getDate();
        let workingDays = 0;

        for (let day = 1; day <= daysInMonth; day++) {
          const date = new Date(year, month - 1, day);
          const dayOfWeek = date.getDay();

          // Weekdays only (not Saturday or Sunday)
          if (dayOfWeek !== 0 && dayOfWeek !== 6) {
            workingDays++;
          }
        }

        // Check for perfect attendance (no absences)
        const hasPerfectAttendance = employeeAttendances.every(
          (a) =>
            a.status === "present" ||
            a.status === "permit" ||
            a.status === "leave"
        );

        // Calculate bonus (20% for perfect attendance)
        const bonusPercentage = hasPerfectAttendance ? 0.2 : 0;
        const bonus = Number((baseSalary * bonusPercentage).toFixed(2));

        // Calculate pay reductions based on attendance
        let payReduction = 0;

        // Calculate per day salary
        const perDaySalary = baseSalary / workingDays;

        // For each attendance record, apply deductions based on status
        for (const attendance of employeeAttendances) {
          const status = attendance.status as AttendanceStatus;
          const deductionFactor = ATTENDANCE_DEDUCTIONS[status] || 0;
          payReduction += perDaySalary * deductionFactor;
        }

        // Also apply deduction for missing attendance records
        const attendanceDates = new Set(employeeAttendances.map((a) => a.date));

        // Check for missing attendance on working days
        for (let day = 1; day <= daysInMonth; day++) {
          const date = new Date(year, month - 1, day);
          const dayOfWeek = date.getDay();

          // Only check weekdays
          if (dayOfWeek !== 0 && dayOfWeek !== 6) {
            const dateStr = format(date, "yyyy-MM-dd");

            if (!attendanceDates.has(dateStr)) {
              // Missing attendance record
              payReduction += perDaySalary;
            }
          }
        }

        // Round to 2 decimal places
        payReduction = Number(payReduction.toFixed(2));

        // Calculate total salary
        const totalSalary = Number(
          (baseSalary + bonus - payReduction).toFixed(2)
        );

        // Create salary record
        const salaryId = randomUUID();

        if (forceGeneration && employeesWithSalaries.has(employee.id)) {
          const existingSalary = existingSalaries.data?.find(
            (s) => s.employee_id === employee.id
          );

          if (existingSalary) {
            // Update existing salary record
            const { error } = await supabase
              .from("salaries")
              .update({
                base_salary: baseSalary,
                bonus: bonus,
                pay_reduction: payReduction,
                total_salary: totalSalary,
                updated_at: new Date().toISOString(),
                updated_by: userId,
              })
              .eq("id", existingSalary.id);

            if (error) {
              console.error(
                `Error updating salary for ${employee.fullname}:`,
                error.message
              );
              continue;
            }

            createdCount++;
            continue;
          }
        }

        // Create new salary record
        const { error } = await supabase.from("salaries").insert({
          id: salaryId,
          employee_id: employee.id,
          base_salary: baseSalary,
          bonus: bonus,
          pay_reduction: payReduction,
          total_salary: totalSalary,
          payment_status: SalaryPaymentStatus.UNPAID,
          period: period,
          created_at: new Date().toISOString(),
          created_by: userId,
          updated_at: new Date().toISOString(),
          updated_by: userId,
        });

        if (error) {
          console.error(
            `Error creating salary for ${employee.fullname}:`,
            error.message
          );
          continue;
        }

        createdCount++;
      } catch (error) {
        console.error(`Error processing employee ${employee.id}:`, error);
        continue;
      }
    }

    return createdCount;
  } catch (error) {
    console.error("Error generating salaries:", error);
    return 0;
  }
}

/**
 * Clear all salary records for a specific period
 */
async function clearSalariesForPeriod(
  period: string,
  userId: string
): Promise<boolean> {
  try {
    console.log(`Clearing salary records for period ${period}...`);

    const { data, error } = await SalaryService.getByPeriod(period);

    if (error) {
      console.error(`Error fetching salaries for period ${period}:`, error);
      return false;
    }

    if (!data || data.length === 0) {
      console.log(`No salary records found for period ${period}.`);
      return true;
    }

    const now = new Date().toISOString();

    // Soft delete each salary record
    for (const salary of data) {
      const { error: deleteError } = await supabase
        .from("salaries")
        .update({
          deleted_at: now,
          deleted_by: userId,
        })
        .eq("id", salary.id)
        .is("deleted_at", null);

      if (deleteError) {
        console.error(
          `Error deleting salary record ${salary.id}:`,
          deleteError.message
        );
      }
    }

    console.log(`Cleared ${data.length} salary records for period ${period}.`);
    return true;
  } catch (err) {
    console.error(
      `Unexpected error clearing salaries for period ${period}:`,
      err
    );
    return false;
  }
}

/**
 * Display help information
 */
function displayHelp() {
  console.log(`
Usage: bun run scripts/create-monthly-salaries.ts [options]

Options:
  --period=YYYY-MM   Generate salaries for a specific period
  --force            Force regeneration of salaries even if they already exist
  --clear            Clear salary records for the specified period
  --help             Display this help message

Examples:
  bun run scripts/create-monthly-salaries.ts                    # Generate salaries for the last 2 months
  bun run scripts/create-monthly-salaries.ts --period=2023-05   # Generate salaries for May 2023
  bun run scripts/create-monthly-salaries.ts --period=2023-05 --force  # Force regenerate salaries for May 2023
  bun run scripts/create-monthly-salaries.ts --period=2023-05 --clear  # Clear salaries for May 2023
`);
  process.exit(0);
}

/**
 * Generate salaries for the last 2 months
 */
async function generateSalariesForLastTwoMonths(
  userId: string,
  forceGeneration: boolean = false
) {
  const periods = getLastTwoMonths();
  // Reverse the periods to process from oldest to most recent
  const reversedPeriods = [...periods].reverse();
  console.log(`Generating salaries for periods: ${reversedPeriods.join(", ")}`);

  let totalGenerated = 0;

  // Process each period one by one, from oldest to most recent
  for (const period of reversedPeriods) {
    const count = await generateSalariesForPeriod(
      period,
      userId,
      forceGeneration
    );
    totalGenerated += count;
  }

  console.log(`\nSalary generation summary:`);
  console.log(`- Total salary records generated: ${totalGenerated}`);
}

/**
 * Main function to run the script
 */
async function main() {
  console.log("Starting salary record creation script...");
  console.log(
    `Current date in Jakarta: ${format(getCurrentJakartaDate(), "yyyy-MM-dd")}`
  );

  // Parse command line arguments
  const args = process.argv.slice(2);

  // Check for help flag
  if (args.includes("--help")) {
    displayHelp();
  }

  // Authenticate as admin
  const adminId = await authenticateAdmin();

  const clearFlag = args.includes("--clear");
  const forceFlag = args.includes("--force");
  const periodArg = args.find((arg) => arg.startsWith("--period="));

  // Clear all records for specific period if requested
  if (clearFlag && periodArg) {
    const period = periodArg.split("=")[1];
    const cleared = await clearSalariesForPeriod(period, adminId);
    if (!cleared) {
      console.error(
        `Failed to clear salary records for period ${period}. Exiting.`
      );
      process.exit(1);
    }
  } else if (periodArg) {
    // Generate salaries for a specific period
    const period = periodArg.split("=")[1];
    await generateSalariesForPeriod(period, adminId, forceFlag);
  } else {
    // Generate salaries for the last 2 months
    await generateSalariesForLastTwoMonths(adminId, forceFlag);
  }

  console.log("\nSalary record creation completed!");
}

// Run the main function
main();
