// Load environment variables from .env.local
import { config } from "dotenv";
config({ path: ".env.local" });

import { createClient } from "@supabase/supabase-js";
import { CreateOrganizationDto } from "../src/database/models/organization.model";

// Initialize Supabase client with admin privileges
const supabaseUrl = process.env.supabase_url || "";
const supabaseServiceKey = process.env.supabase_service_role || "";

if (!supabaseUrl || !supabaseServiceKey) {
  console.error(
    "Error: supabase_url and supabase_service_role must be set in .env.local"
  );
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseServiceKey);

// Admin user credentials
const ADMIN_EMAIL = "<EMAIL>";
const ADMIN_PASSWORD = "Admin@123";

// Define client types
const clientTypes = [
  "Corporate",
  "Small Business",
  "Enterprise",
  "Government",
  "Non-Profit",
  "Educational",
  "Healthcare",
  "Retail",
];

// Define organization data
const organizationsData: CreateOrganizationDto[] = [
  {
    name: "TechNova Solutions",
    phone: "6281234567001",
    address: "Jl. Sudirman No. 123, Jakarta",
    client_type: "Corporate",
    notes: "Enterprise software solutions provider",
  },
  {
    name: "Green Earth Foundation",
    phone: "6281234567002",
    address: "Jl. Thamrin No. 45, Jakarta",
    client_type: "Non-Profit",
    notes: "Environmental conservation organization",
  },
  {
    name: "Jakarta Medical Center",
    phone: "6281234567003",
    address: "Jl. Gatot Subroto No. 78, Jakarta",
    client_type: "Healthcare",
    notes: "Premier healthcare provider in central Jakarta",
  },
  {
    name: "Bali Eco Resorts",
    phone: "6281234567004",
    address: "Jl. Raya Ubud No. 99, Bali",
    client_type: "Small Business",
    notes: "Sustainable tourism accommodations",
  },
  {
    name: "Ministry of Digital Economy",
    phone: "6281234567005",
    address: "Jl. Merdeka No. 10, Jakarta",
    client_type: "Government",
    notes: "Government department focused on digital transformation",
  },
  {
    name: "Bandung Institute of Technology",
    phone: "6281234567006",
    address: "Jl. Ganesha No. 10, Bandung",
    client_type: "Educational",
    notes: "Leading technical university in Indonesia",
  },
  {
    name: "Jakarta Retail Group",
    phone: "6281234567007",
    address: "Jl. MH Thamrin No. 20, Jakarta",
    client_type: "Retail",
    notes: "Operates multiple retail chains across Indonesia",
  },
  {
    name: "Surabaya Shipping Logistics",
    phone: "6281234567008",
    address: "Jl. Tanjung Perak No. 15, Surabaya",
    client_type: "Enterprise",
    notes: "Major logistics and shipping company in East Java",
  },
  {
    name: "Yogyakarta Cultural Center",
    phone: "6281234567009",
    address: "Jl. Malioboro No. 45, Yogyakarta",
    client_type: "Non-Profit",
    notes: "Preserving traditional Javanese arts and culture",
  },
  {
    name: "Medan Agribusiness Corporation",
    phone: "6281234567010",
    address: "Jl. SM Raja No. 88, Medan",
    client_type: "Corporate",
    notes: "Agricultural products and processing",
  },
  {
    name: "Makassar Tech Incubator",
    phone: "6281234567011",
    address: "Jl. Pettarani No. 30, Makassar",
    client_type: "Small Business",
    notes: "Technology startup incubator in Eastern Indonesia",
  },
];

/**
 * Authenticate as admin user and return the user ID
 */
async function authenticateAdmin() {
  console.log(`Authenticating as admin user (${ADMIN_EMAIL})...`);

  try {
    const { data, error } = await supabase.auth.signInWithPassword({
      email: ADMIN_EMAIL,
      password: ADMIN_PASSWORD,
    });

    if (error) {
      console.error("Error authenticating as admin:", error.message);
      process.exit(1);
    }

    console.log(
      `Authenticated as admin successfully! User ID: ${data.user.id}`
    );
    return data.user.id;
  } catch (err) {
    console.error("Unexpected error during authentication:", err);
    process.exit(1);
  }
}

/**
 * Create a single organization
 */
async function createOrganization(
  orgData: CreateOrganizationDto,
  userId: string
) {
  console.log(`Creating organization: ${orgData.name}`);

  try {
    const timestamp = new Date().toISOString();

    const { data, error } = await supabase
      .from("organizations")
      .insert({
        ...orgData,
        created_at: timestamp,
        created_by: userId,
      })
      .select()
      .single();

    if (error) {
      if (error.message.includes("duplicate")) {
        console.log(
          `Organization "${orgData.name}" already exists, skipping...`
        );
        return null;
      } else {
        console.error(
          `Error creating organization "${orgData.name}":`,
          error.message
        );
        return null;
      }
    }

    console.log(`Organization created with ID: ${data.id}`);
    return data;
  } catch (err) {
    console.error(
      `Unexpected error creating organization "${orgData.name}":`,
      err
    );
    return null;
  }
}

/**
 * Create all organizations
 */
async function createAllOrganizations() {
  console.log("Starting creation of organizations...");

  // First authenticate as admin
  const adminUserId = await authenticateAdmin();
  if (!adminUserId) {
    console.error("Failed to authenticate as admin. Cannot proceed.");
    process.exit(1);
  }

  const specificOrgName = process.argv[2];

  if (specificOrgName && specificOrgName.startsWith("--name=")) {
    const orgName = specificOrgName.split("=")[1];
    const org = organizationsData.find((o) =>
      o.name.toLowerCase().includes(orgName.toLowerCase())
    );

    if (org) {
      await createOrganization(org, adminUserId);
    } else {
      console.error(`No organization found matching name: ${orgName}`);
    }
  } else {
    // Create all organizations
    const results = [];
    for (const org of organizationsData) {
      const result = await createOrganization(org, adminUserId);
      if (result) results.push(result);
    }

    console.log(`\nCreated ${results.length} organizations successfully!`);
  }

  console.log("\nOrganization creation process completed!");
}

createAllOrganizations();
