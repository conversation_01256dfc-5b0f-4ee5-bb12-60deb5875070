// Load environment variables from .env.local
process.env.NODE_TLS_REJECT_UNAUTHORIZED = "0";

import { config } from "dotenv";
config({ path: ".env.local" });

import { createClient } from "@supabase/supabase-js";
import { format } from "date-fns";

// Initialize Supabase client with admin privileges
const supabaseUrl = process.env.supabase_url || "";
const supabaseServiceKey = process.env.supabase_service_role || "";

if (!supabaseUrl || !supabaseServiceKey) {
  console.error(
    "Error: supabase_url and supabase_service_role must be set in .env.local"
  );
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseServiceKey);

// Admin user credentials
const ADMIN_EMAIL = "<EMAIL>";
const ADMIN_PASSWORD = "Admin@123";

// Project charter templates
const KEY_STAKEHOLDERS_TEMPLATES = [
  "Project Sponsor: {{sponsor_name}}\nProject Manager: {{manager_name}}\nClient Representative: {{client_name}}\nTechnical Lead: {{tech_lead_name}}\nQuality Assurance Lead: {{qa_lead_name}}",
  "Executive Sponsor: {{sponsor_name}}\nProject Director: {{manager_name}}\nClient Stakeholders: {{client_name}}\nDevelopment Team Lead: {{tech_lead_name}}\nBusiness Analyst: {{ba_name}}",
  "Business Owner: {{sponsor_name}}\nProject Lead: {{manager_name}}\nEnd User Representatives: {{client_name}}\nSenior Developer: {{tech_lead_name}}\nOperations Manager: {{ops_name}}",
  "Department Head: {{sponsor_name}}\nProject Coordinator: {{manager_name}}\nCustomer Success Manager: {{client_name}}\nTechnical Architect: {{tech_lead_name}}\nSecurity Officer: {{security_name}}",
];

const PROJECT_AUTHORITY_TEMPLATES = [
  "The Project Manager has full authority to allocate resources, manage the budget, and make decisions within the scope of the project. Any changes to project scope, timeline, or budget exceeding 10% must be approved by the Project Sponsor.",
  "The Project Director is authorized to manage all aspects of the project including resource allocation, vendor selection, and technical decisions. Major changes requiring additional funding or timeline extensions must be approved by the Executive Sponsor.",
  "The Project Lead has authority over day-to-day operations, team assignments, and technical implementation decisions. Scope changes, budget adjustments, and milestone extensions require approval from the Business Owner.",
  "The Project Coordinator is empowered to manage project activities, coordinate resources, and implement the approved project plan. Significant deviations from the plan or additional resource requirements must be approved by the Department Head.",
];

const PROJECT_DESCRIPTION_TEMPLATES = [
  "This project aims to {{action}} a {{system_type}} for {{client}} to {{benefit}}. The solution will include {{feature_1}}, {{feature_2}}, and {{feature_3}}. Implementation will follow {{methodology}} methodology with {{approach}} approach.",
  "The purpose of this project is to {{action}} {{client}}'s existing {{system_type}} to {{benefit}}. Key components include {{feature_1}}, {{feature_2}}, and {{feature_3}}. The project will be executed using {{methodology}} with emphasis on {{approach}}.",
  "This initiative will {{action}} a comprehensive {{system_type}} to help {{client}} {{benefit}}. The solution encompasses {{feature_1}}, {{feature_2}}, and {{feature_3}}. Development will follow {{methodology}} principles with a focus on {{approach}}.",
  "The project involves {{action}} a {{system_type}} solution that enables {{client}} to {{benefit}}. Core functionalities include {{feature_1}}, {{feature_2}}, and {{feature_3}}. The implementation approach will utilize {{methodology}} with {{approach}} practices.",
];

const OBJECTIVE_AND_KEY_RESULTS_TEMPLATES = [
  "Objective: Successfully {{action}} the {{system_type}} within the defined timeline and budget.\n\nKey Results:\n1. Complete all deliverables by {{deadline}}\n2. Achieve {{percentage}}% user adoption within {{timeframe}} of launch\n3. Maintain project costs within {{budget_variance}}% of allocated budget\n4. Achieve {{satisfaction}}% client satisfaction rating",
  "Objective: Implement a high-quality {{system_type}} that meets all business requirements.\n\nKey Results:\n1. Deliver all core functionalities by {{deadline}}\n2. Ensure {{percentage}}% test coverage across all components\n3. Resolve {{issue_percentage}}% of identified issues before launch\n4. Achieve {{performance}}% improvement in {{metric}} compared to baseline",
  "Objective: Deploy a scalable and secure {{system_type}} that enhances business operations.\n\nKey Results:\n1. Complete implementation by {{deadline}}\n2. Ensure system can handle {{capacity}} without performance degradation\n3. Pass all security audits with zero critical findings\n4. Reduce {{process_time}} by {{efficiency}}% through automation",
  "Objective: Deliver a user-friendly {{system_type}} that drives business value.\n\nKey Results:\n1. Launch all phases by {{deadline}}\n2. Achieve user satisfaction rating of {{satisfaction}}%\n3. Reduce error rates by {{error_reduction}}% compared to previous system\n4. Enable {{benefit_metric}} improvement of {{benefit_percentage}}%",
];

const PURPOSE_TEMPLATES = [
  "This project charter formally authorizes the project and provides the project manager with the authority to apply organizational resources to project activities. It serves as a reference point for current and future project decisions and establishes the framework for project governance.",
  "This document officially sanctions the project and outlines the authority granted to the project director. It defines the project's purpose, objectives, and success criteria, serving as the foundation for all project planning and execution activities.",
  "The purpose of this charter is to authorize the commencement of the project and document initial requirements and expectations. It establishes the relationship between the project team and stakeholders and provides a baseline for measuring project success.",
  "This charter formally initiates the project and empowers the project lead to utilize organizational resources. It documents high-level project information, key stakeholders, and success criteria to guide project execution and evaluation.",
];

const KEY_ASSUMPTION_TEMPLATES = [
  "1. Stakeholders will be available for timely reviews and approvals\n2. Required technical infrastructure will be available when needed\n3. Third-party integrations will be supported by vendors\n4. Client will provide timely feedback on deliverables\n5. Existing systems will remain stable during integration",
  "1. Resources allocated to the project will remain available throughout\n2. Technical requirements will not change significantly after approval\n3. Client data will be provided in the expected format\n4. Necessary licenses and permissions will be obtained on time\n5. User training can be completed within the planned timeframe",
  "1. Project scope will remain stable after initial planning phase\n2. Required expertise is available within the organization\n3. External dependencies will be delivered as scheduled\n4. Stakeholders will actively participate in the project\n5. Existing documentation is accurate and up-to-date",
  "1. Budget approved will be sufficient to complete the project\n2. Client representatives have decision-making authority\n3. Technology selected will meet all functional requirements\n4. Security requirements will not change significantly\n5. Regulatory environment will remain stable during implementation",
];

const ASSUMPTIONS_CONSTRAINS_RISKS_TEMPLATES = [
  "Assumptions:\n- {{assumption_1}}\n- {{assumption_2}}\n- {{assumption_3}}\n\nConstraints:\n- Budget limited to {{budget}}\n- Project must be completed by {{deadline}}\n- Team limited to {{team_size}} members\n- Must comply with {{compliance}}\n\nRisks:\n- {{risk_1}}\n- {{risk_2}}\n- {{risk_3}}",
  "Assumptions:\n- {{assumption_1}}\n- {{assumption_2}}\n- {{assumption_3}}\n\nConstraints:\n- Timeline constraint of {{deadline}}\n- Resource availability limited to {{resource_limit}}\n- Technical environment restrictions: {{tech_constraint}}\n- Regulatory requirements: {{regulation}}\n\nRisks:\n- {{risk_1}}\n- {{risk_2}}\n- {{risk_3}}",
  "Assumptions:\n- {{assumption_1}}\n- {{assumption_2}}\n- {{assumption_3}}\n\nConstraints:\n- Fixed budget of {{budget}}\n- Delivery deadline of {{deadline}}\n- Limited access to {{resource_constraint}}\n- Must integrate with {{integration_constraint}}\n\nRisks:\n- {{risk_1}}\n- {{risk_2}}\n- {{risk_3}}",
  "Assumptions:\n- {{assumption_1}}\n- {{assumption_2}}\n- {{assumption_3}}\n\nConstraints:\n- Project duration limited to {{timeline}}\n- Team composition constraints: {{team_constraint}}\n- Technology stack limitations: {{tech_limitation}}\n- Security requirements: {{security_requirement}}\n\nRisks:\n- {{risk_1}}\n- {{risk_2}}\n- {{risk_3}}",
];

const HIGH_LEVEL_RESOURCES_TEMPLATES = [
  "Human Resources:\n- Project Manager (100%)\n- Business Analyst (75%)\n- Senior Developers ({{dev_count}})\n- QA Engineers ({{qa_count}})\n- UI/UX Designer (50%)\n\nTechnical Resources:\n- Development Environment\n- Testing Environment\n- Staging Environment\n- CI/CD Pipeline\n- Version Control System",
  "Team Resources:\n- Project Director (100%)\n- Technical Lead (100%)\n- Developers ({{dev_count}})\n- Quality Assurance ({{qa_count}})\n- Business Analyst (75%)\n\nInfrastructure:\n- Cloud Computing Resources\n- Database Servers\n- Application Servers\n- Network Infrastructure\n- Security Tools",
  "Personnel:\n- Project Lead (100%)\n- Solution Architect (50%)\n- Development Team ({{dev_count}} FTEs)\n- Testing Team ({{qa_count}} FTEs)\n- DevOps Engineer (50%)\n\nTechnology:\n- Development Tools\n- Testing Frameworks\n- Monitoring Solutions\n- Deployment Tools\n- Documentation Platform",
  "Human Capital:\n- Project Coordinator (100%)\n- Technical Architect (75%)\n- Software Engineers ({{dev_count}})\n- QA Specialists ({{qa_count}})\n- UX Researcher (25%)\n\nTechnical Assets:\n- Development Workstations\n- Test Environments\n- Production Infrastructure\n- Collaboration Tools\n- Security Infrastructure",
];

const HIGH_LEVEL_MILESTONES_TEMPLATES = [
  "1. Project Kickoff: {{kickoff_date}}\n2. Requirements Finalization: {{req_date}}\n3. Design Approval: {{design_date}}\n4. Development Completion: {{dev_date}}\n5. Testing Completion: {{test_date}}\n6. User Acceptance Testing: {{uat_date}}\n7. Production Deployment: {{deploy_date}}\n8. Project Closure: {{closure_date}}",
  "1. Project Initiation: {{kickoff_date}}\n2. Requirements Gathering: {{req_date}}\n3. Architecture Design: {{design_date}}\n4. Phase 1 Development: {{phase1_date}}\n5. Phase 2 Development: {{phase2_date}}\n6. System Testing: {{test_date}}\n7. User Training: {{training_date}}\n8. Go-Live: {{deploy_date}}",
  "1. Project Launch: {{kickoff_date}}\n2. Scope Finalization: {{req_date}}\n3. Prototype Approval: {{prototype_date}}\n4. Alpha Release: {{alpha_date}}\n5. Beta Release: {{beta_date}}\n6. Performance Testing: {{perf_date}}\n7. Final Deployment: {{deploy_date}}\n8. Post-Implementation Review: {{review_date}}",
  "1. Project Start: {{kickoff_date}}\n2. Business Requirements Document: {{req_date}}\n3. Technical Specification Approval: {{spec_date}}\n4. Sprint 1 Completion: {{sprint1_date}}\n5. Sprint 2 Completion: {{sprint2_date}}\n6. Integration Testing: {{test_date}}\n7. Production Release: {{deploy_date}}\n8. Project Handover: {{handover_date}}",
];

const STATEMENT_PREDICTION_OF_BENEFIT_TEMPLATES = [
  "This project is expected to deliver the following benefits:\n\n1. Increase operational efficiency by {{efficiency}}%\n2. Reduce processing time by {{time_reduction}}%\n3. Improve data accuracy by {{accuracy}}%\n4. Generate cost savings of approximately {{cost_saving}} annually\n5. Enhance customer satisfaction by {{satisfaction}}%\n\nROI is projected at {{roi}}% within {{roi_period}} of implementation.",
  "The implementation of this solution will provide:\n\n1. Revenue increase of approximately {{revenue}}% through improved processes\n2. Reduction in manual effort by {{effort_reduction}}%\n3. Decrease in error rates by {{error_reduction}}%\n4. Estimated annual savings of {{cost_saving}}\n5. Improved compliance with {{compliance}}\n\nThe investment is expected to be recouped within {{payback_period}} with an ROI of {{roi}}%.",
  "Upon successful implementation, the project will deliver:\n\n1. Productivity improvement of {{productivity}}%\n2. Reduction in operational costs by {{cost_reduction}}%\n3. Improved reporting capabilities, reducing report generation time by {{report_time}}%\n4. Enhanced security, reducing incidents by {{security_improvement}}%\n5. Better user experience, increasing user satisfaction by {{user_satisfaction}}%\n\nThe total economic benefit is estimated at {{total_benefit}} over {{benefit_period}} years.",
  "The business value of this project includes:\n\n1. Streamlined workflows resulting in {{workflow_improvement}}% efficiency gain\n2. Reduced downtime by {{downtime_reduction}}%\n3. Improved data insights, leading to {{decision_improvement}}% better decision-making\n4. Cost avoidance of {{cost_avoidance}} over {{avoidance_period}} years\n5. Competitive advantage through {{competitive_feature}}\n\nThe expected payback period is {{payback_period}} with a projected ROI of {{roi}}%.",
];

// Function to get random item from an array
function getRandomItem<T>(arr: T[]): T {
  return arr[Math.floor(Math.random() * arr.length)];
}

// Function to get random number between min and max (inclusive)
function getRandomNumber(min: number, max: number): number {
  return Math.floor(Math.random() * (max - min + 1)) + min;
}

/**
 * Authenticate as admin user and return the user ID
 */
async function authenticateAdmin() {
  console.log(`Authenticating as admin user (${ADMIN_EMAIL})...`);

  try {
    const { data, error } = await supabase.auth.signInWithPassword({
      email: ADMIN_EMAIL,
      password: ADMIN_PASSWORD,
    });

    if (error) {
      console.error("Error authenticating as admin:", error.message);
      process.exit(1);
    }

    console.log(
      `Authenticated as admin successfully! User ID: ${data.user.id}`
    );
    return data.user.id;
  } catch (err) {
    console.error("Unexpected error during authentication:", err);
    process.exit(1);
  }
}

/**
 * Get all projects from the database
 */
async function getAllProjects() {
  try {
    const { data, error } = await supabase
      .from("projects")
      .select(
        "id, project_name, start_project, end_project, objectives, status_project, budget_project"
      )
      .is("deleted_at", null);

    if (error) {
      console.error("Error fetching projects:", error.message);
      return [];
    }

    return data;
  } catch (err) {
    console.error("Unexpected error fetching projects:", err);
    return [];
  }
}

/**
 * Check if a project charter already exists for a project
 */
async function projectCharterExistsForProject(
  projectId: string
): Promise<boolean> {
  try {
    const { data, error } = await supabase
      .from("project_charters")
      .select("id")
      .eq("project_id", projectId)
      .is("deleted_at", null)
      .maybeSingle();

    return !!data;
  } catch (err) {
    console.error("Error checking for existing project charter:", err);
    return false;
  }
}

/**
 * Create a single project charter
 */
async function createProjectCharter(charterData: any, userId: string) {
  console.log(
    `Creating project charter for project ID ${charterData.project_id}`
  );

  try {
    // Check if project charter already exists for this project
    if (await projectCharterExistsForProject(charterData.project_id)) {
      console.log(
        `Project charter for project ID ${charterData.project_id} already exists. Skipping.`
      );
      return null;
    }

    const timestamp = new Date().toISOString();

    const { data, error } = await supabase
      .from("project_charters")
      .insert({
        ...charterData,
        created_at: timestamp,
        created_by: userId,
      })
      .select()
      .single();

    if (error) {
      console.error(
        `Error creating project charter for project ID ${charterData.project_id}:`,
        error.message
      );
      return null;
    }

    console.log(`Project charter created with ID: ${data.id}`);

    // Update the project with the project charter ID
    try {
      const { error: updateError } = await supabase
        .from("projects")
        .update({ project_charter_id: data.id })
        .eq("id", charterData.project_id);

      if (updateError) {
        console.error(
          `Error updating project with charter ID: ${updateError.message}`
        );
      } else {
        console.log(
          `Updated project ${charterData.project_id} with charter ID: ${data.id}`
        );
      }
    } catch (updateErr) {
      console.error(
        `Unexpected error updating project with charter ID: ${updateErr}`
      );
    }

    return data;
  } catch (err) {
    console.error(
      `Unexpected error creating project charter for project ID ${charterData.project_id}:`,
      err
    );
    return null;
  }
}

/**
 * Generate a random project charter for a project
 */
async function generateRandomProjectCharterForProject(
  project: any,
  userId: string
) {
  if (!project || !project.id) {
    console.error("Invalid project data");
    return null;
  }

  console.log(
    `Generating project charter for project: ${project.project_name}`
  );

  // Generate random names for stakeholders
  const sponsorName = getRandomItem([
    "John Smith",
    "Sarah Johnson",
    "Michael Wong",
    "Anita Patel",
    "David Chen",
    "Maria Rodriguez",
    "Robert Kim",
    "Lisa Nguyen",
  ]);

  const managerName = getRandomItem([
    "James Wilson",
    "Emily Davis",
    "Daniel Lee",
    "Sophia Martinez",
    "Thomas Brown",
    "Olivia Garcia",
    "William Taylor",
    "Emma Anderson",
  ]);

  const clientName = getRandomItem([
    "Alex Thompson",
    "Jennifer White",
    "Christopher Lewis",
    "Amanda Clark",
    "Ryan Moore",
    "Jessica Hall",
    "Kevin Young",
    "Michelle Allen",
  ]);

  const techLeadName = getRandomItem([
    "Brian Scott",
    "Nicole King",
    "Jason Wright",
    "Stephanie Green",
    "Eric Baker",
    "Rachel Adams",
    "Matthew Nelson",
    "Laura Hill",
  ]);

  const otherNames = {
    qa_lead_name: "Patricia Evans",
    ba_name: "Mark Collins",
    ops_name: "Karen Phillips",
    security_name: "Steven Carter",
  };

  // Generate random dates for milestones
  const startDate = new Date(project.start_project);
  const endDate = new Date(project.end_project);
  const projectDuration = endDate.getTime() - startDate.getTime();

  const milestonesDates = {
    kickoff_date: format(startDate, "yyyy-MM-dd"),
    req_date: format(
      new Date(startDate.getTime() + projectDuration * 0.1),
      "yyyy-MM-dd"
    ),
    design_date: format(
      new Date(startDate.getTime() + projectDuration * 0.2),
      "yyyy-MM-dd"
    ),
    prototype_date: format(
      new Date(startDate.getTime() + projectDuration * 0.25),
      "yyyy-MM-dd"
    ),
    dev_date: format(
      new Date(startDate.getTime() + projectDuration * 0.5),
      "yyyy-MM-dd"
    ),
    phase1_date: format(
      new Date(startDate.getTime() + projectDuration * 0.3),
      "yyyy-MM-dd"
    ),
    phase2_date: format(
      new Date(startDate.getTime() + projectDuration * 0.5),
      "yyyy-MM-dd"
    ),
    alpha_date: format(
      new Date(startDate.getTime() + projectDuration * 0.4),
      "yyyy-MM-dd"
    ),
    beta_date: format(
      new Date(startDate.getTime() + projectDuration * 0.6),
      "yyyy-MM-dd"
    ),
    sprint1_date: format(
      new Date(startDate.getTime() + projectDuration * 0.35),
      "yyyy-MM-dd"
    ),
    sprint2_date: format(
      new Date(startDate.getTime() + projectDuration * 0.55),
      "yyyy-MM-dd"
    ),
    spec_date: format(
      new Date(startDate.getTime() + projectDuration * 0.15),
      "yyyy-MM-dd"
    ),
    test_date: format(
      new Date(startDate.getTime() + projectDuration * 0.7),
      "yyyy-MM-dd"
    ),
    perf_date: format(
      new Date(startDate.getTime() + projectDuration * 0.75),
      "yyyy-MM-dd"
    ),
    uat_date: format(
      new Date(startDate.getTime() + projectDuration * 0.8),
      "yyyy-MM-dd"
    ),
    training_date: format(
      new Date(startDate.getTime() + projectDuration * 0.85),
      "yyyy-MM-dd"
    ),
    deploy_date: format(
      new Date(startDate.getTime() + projectDuration * 0.9),
      "yyyy-MM-dd"
    ),
    closure_date: format(endDate, "yyyy-MM-dd"),
    review_date: format(
      new Date(endDate.getTime() + 7 * 24 * 60 * 60 * 1000),
      "yyyy-MM-dd"
    ), // 1 week after end
    handover_date: format(
      new Date(endDate.getTime() + 3 * 24 * 60 * 60 * 1000),
      "yyyy-MM-dd"
    ), // 3 days after end
  };

  // Generate random values for templates
  const templateValues = {
    // Project description values
    action: getRandomItem([
      "implement",
      "develop",
      "create",
      "deploy",
      "design",
      "upgrade",
      "optimize",
    ]),
    system_type: getRandomItem([
      "ERP system",
      "CRM platform",
      "mobile application",
      "web portal",
      "data warehouse",
      "business intelligence solution",
      "content management system",
    ]),
    client: project.project_name.split(" ")[0], // Use first part of project name as client
    benefit: getRandomItem([
      "improve operational efficiency",
      "enhance customer experience",
      "streamline business processes",
      "reduce operational costs",
      "increase data accuracy",
      "enable better decision-making",
    ]),
    feature_1: getRandomItem([
      "user management",
      "reporting dashboard",
      "data analytics",
      "mobile access",
      "automated workflows",
      "document management",
    ]),
    feature_2: getRandomItem([
      "role-based access control",
      "real-time notifications",
      "data visualization",
      "integration capabilities",
      "audit logging",
      "search functionality",
    ]),
    feature_3: getRandomItem([
      "customizable reports",
      "multi-language support",
      "offline capabilities",
      "data export/import",
      "automated backups",
      "performance monitoring",
    ]),
    methodology: getRandomItem([
      "Agile",
      "Scrum",
      "Waterfall",
      "Hybrid",
      "Kanban",
      "Lean",
    ]),
    approach: getRandomItem([
      "iterative",
      "incremental",
      "phased",
      "continuous delivery",
      "test-driven",
      "user-centered",
    ]),

    // OKR values
    deadline: milestonesDates.deploy_date,
    percentage: getRandomNumber(80, 95),
    timeframe: getRandomItem(["1 month", "2 months", "3 months", "6 months"]),
    budget_variance: getRandomNumber(5, 15),
    satisfaction: getRandomNumber(85, 98),
    issue_percentage: getRandomNumber(90, 99),
    performance: getRandomNumber(20, 50),
    metric: getRandomItem([
      "processing time",
      "response time",
      "throughput",
      "resource utilization",
    ]),
    capacity: getRandomItem([
      "500 concurrent users",
      "1000 transactions per minute",
      "5TB of data",
      "10,000 daily active users",
    ]),
    process_time: getRandomItem([
      "manual processing",
      "report generation",
      "data entry",
      "approval workflows",
    ]),
    efficiency: getRandomNumber(30, 70),
    error_reduction: getRandomNumber(40, 80),
    benefit_metric: getRandomItem([
      "revenue",
      "customer retention",
      "operational efficiency",
      "employee productivity",
    ]),
    benefit_percentage: getRandomNumber(15, 40),

    // Resources values
    dev_count: getRandomNumber(2, 6),
    qa_count: getRandomNumber(1, 3),

    // Assumptions, constraints, risks values
    assumption_1: getRandomItem([
      "Stakeholders will be available for timely reviews",
      "Required infrastructure will be available",
      "Client data will be provided in expected format",
    ]),
    assumption_2: getRandomItem([
      "Technical requirements will remain stable",
      "Necessary licenses will be obtained on time",
      "Third-party integrations will be supported",
    ]),
    assumption_3: getRandomItem([
      "Team members will be available as planned",
      "Existing systems will remain stable",
      "User training can be completed as scheduled",
    ]),
    budget: project.budget_project,
    team_size: getRandomNumber(5, 15),
    compliance: getRandomItem([
      "ISO 27001",
      "GDPR",
      "HIPAA",
      "SOC 2",
      "PCI DSS",
    ]),
    risk_1: getRandomItem([
      "Scope creep due to evolving requirements",
      "Resource constraints affecting timeline",
      "Technical complexity exceeding initial estimates",
    ]),
    risk_2: getRandomItem([
      "Integration issues with legacy systems",
      "Stakeholder availability constraints",
      "Data migration challenges",
    ]),
    risk_3: getRandomItem([
      "Vendor delays impacting critical path",
      "Regulatory changes affecting implementation",
      "User adoption resistance",
    ]),
    resource_limit: getRandomItem([
      "current team capacity",
      "available infrastructure",
      "budget constraints",
    ]),
    tech_constraint: getRandomItem([
      "legacy system compatibility",
      "security requirements",
      "performance requirements",
    ]),
    regulation: getRandomItem([
      "data protection laws",
      "industry standards",
      "corporate policies",
    ]),
    resource_constraint: getRandomItem([
      "subject matter experts",
      "specialized hardware",
      "third-party services",
    ]),
    integration_constraint: getRandomItem([
      "existing ERP system",
      "legacy databases",
      "third-party APIs",
    ]),
    timeline: getRandomItem(["6 months", "12 months", "18 months"]),
    team_constraint: getRandomItem([
      "limited specialized skills",
      "part-time availability",
      "geographical distribution",
    ]),
    tech_limitation: getRandomItem([
      "approved technology stack",
      "infrastructure constraints",
      "licensing restrictions",
    ]),
    security_requirement: getRandomItem([
      "data encryption",
      "access controls",
      "audit logging",
    ]),

    // Benefits values
    time_reduction: getRandomNumber(20, 60),
    accuracy: getRandomNumber(15, 50),
    cost_saving: `$${getRandomNumber(50, 500)}K`,
    roi: getRandomNumber(100, 300),
    roi_period: getRandomItem(["1 year", "18 months", "2 years"]),
    revenue: getRandomNumber(5, 25),
    effort_reduction: getRandomNumber(30, 70),
    payback_period: getRandomItem(["12 months", "18 months", "24 months"]),
    productivity: getRandomNumber(15, 45),
    cost_reduction: getRandomNumber(10, 35),
    report_time: getRandomNumber(40, 80),
    security_improvement: getRandomNumber(30, 70),
    user_satisfaction: getRandomNumber(20, 50),
    total_benefit: `$${getRandomNumber(200, 2000)}K`,
    benefit_period: getRandomNumber(3, 5),
    workflow_improvement: getRandomNumber(20, 50),
    downtime_reduction: getRandomNumber(30, 80),
    decision_improvement: getRandomNumber(15, 40),
    cost_avoidance: `$${getRandomNumber(100, 1000)}K`,
    avoidance_period: getRandomNumber(2, 5),
    competitive_feature: getRandomItem([
      "faster time-to-market",
      "enhanced customer experience",
      "improved data insights",
      "reduced operational costs",
    ]),
  };

  // Generate key stakeholders
  let keyStakeholders = getRandomItem(KEY_STAKEHOLDERS_TEMPLATES)
    .replace("{{sponsor_name}}", sponsorName)
    .replace("{{manager_name}}", managerName)
    .replace("{{client_name}}", clientName)
    .replace("{{tech_lead_name}}", techLeadName)
    .replace("{{qa_lead_name}}", otherNames.qa_lead_name)
    .replace("{{ba_name}}", otherNames.ba_name)
    .replace("{{ops_name}}", otherNames.ops_name)
    .replace("{{security_name}}", otherNames.security_name);

  // Generate project authority
  let projectAuthority = getRandomItem(PROJECT_AUTHORITY_TEMPLATES);

  // Generate project description
  let projectDescription = getRandomItem(PROJECT_DESCRIPTION_TEMPLATES);
  for (const [key, value] of Object.entries(templateValues)) {
    projectDescription = projectDescription.replace(
      `{{${key}}}`,
      value.toString()
    );
  }

  // Generate objective and key results
  let objectiveAndKeyResults = getRandomItem(
    OBJECTIVE_AND_KEY_RESULTS_TEMPLATES
  );
  for (const [key, value] of Object.entries(templateValues)) {
    objectiveAndKeyResults = objectiveAndKeyResults.replace(
      `{{${key}}}`,
      value.toString()
    );
  }

  // Generate purpose
  let purpose = getRandomItem(PURPOSE_TEMPLATES);

  // Generate key assumption
  let keyAssumption = getRandomItem(KEY_ASSUMPTION_TEMPLATES);

  // Generate assumptions, constraints, risks
  let assumptionsConstrainsRisks = getRandomItem(
    ASSUMPTIONS_CONSTRAINS_RISKS_TEMPLATES
  );
  for (const [key, value] of Object.entries(templateValues)) {
    assumptionsConstrainsRisks = assumptionsConstrainsRisks.replace(
      `{{${key}}}`,
      value.toString()
    );
  }

  // Generate high-level resources
  let highLevelResources = getRandomItem(HIGH_LEVEL_RESOURCES_TEMPLATES);
  for (const [key, value] of Object.entries(templateValues)) {
    highLevelResources = highLevelResources.replace(
      `{{${key}}}`,
      value.toString()
    );
  }

  // Generate high-level milestones
  let highLevelMilestones = getRandomItem(HIGH_LEVEL_MILESTONES_TEMPLATES);
  for (const [key, value] of Object.entries(milestonesDates)) {
    highLevelMilestones = highLevelMilestones.replace(
      `{{${key}}}`,
      value.toString()
    );
  }

  // Generate statement prediction of benefit
  let statementPredictionOfBenefit = getRandomItem(
    STATEMENT_PREDICTION_OF_BENEFIT_TEMPLATES
  );
  for (const [key, value] of Object.entries(templateValues)) {
    statementPredictionOfBenefit = statementPredictionOfBenefit.replace(
      `{{${key}}}`,
      value.toString()
    );
  }

  // Create charter data
  const charterData = {
    project_id: project.id,
    key_stakeholders: keyStakeholders,
    project_authority: projectAuthority,
    project_description: projectDescription,
    objective_and_key_results: objectiveAndKeyResults,
    purpose: purpose,
    key_assumption: keyAssumption,
    assumptions_constrains_risks: assumptionsConstrainsRisks,
    high_level_resources: highLevelResources,
    high_level_milestones: highLevelMilestones,
    statement_prediction_of_benefit: statementPredictionOfBenefit,
    approval: Math.random() > 0.2, // 80% chance of being approved
  };

  // Create the project charter
  return await createProjectCharter(charterData, userId);
}

/**
 * Create project charters for all projects
 */
async function createProjectChartersForAllProjects(userId: string) {
  // Get all projects
  const projects = await getAllProjects();
  if (projects.length === 0) {
    console.error("No projects found in the system.");
    return;
  }

  console.log(`Found ${projects.length} projects.`);

  let createdCount = 0;
  let skippedCount = 0;

  // Create project charters for each project
  for (const project of projects) {
    const charter = await generateRandomProjectCharterForProject(
      project,
      userId
    );
    if (charter) {
      createdCount++;
    } else {
      skippedCount++;
    }
  }

  console.log("\nProject charter creation summary:");
  console.log(`- Total created: ${createdCount}`);
  console.log(`- Total skipped: ${skippedCount}`);
}

/**
 * Create a project charter for a specific project
 */
async function createProjectCharterForProject(
  projectId: string,
  userId: string
) {
  // Get the project
  const { data: project, error } = await supabase
    .from("projects")
    .select(
      "id, project_name, start_project, end_project, objectives, status_project, budget_project"
    )
    .eq("id", projectId)
    .is("deleted_at", null)
    .single();

  if (error || !project) {
    console.error(`Project with ID ${projectId} not found.`);
    return;
  }

  console.log(`Creating project charter for project: ${project.project_name}`);

  // Generate project charter for the project
  const charter = await generateRandomProjectCharterForProject(project, userId);

  console.log("\nProject charter creation summary for this project:");
  console.log(`- Created: ${charter ? "Yes" : "No (already exists)"}`);
}

/**
 * Main function to run the script
 */
async function main() {
  console.log("Starting project charter creation script...");

  // Authenticate as admin
  const userId = await authenticateAdmin();

  const projectParam = process.argv[2];

  if (projectParam && projectParam.startsWith("--project=")) {
    const projectId = projectParam.split("=")[1];
    await createProjectCharterForProject(projectId, userId);
  } else {
    await createProjectChartersForAllProjects(userId);
  }

  console.log("\nProject charter creation completed!");
}

// Run the main function
main();
