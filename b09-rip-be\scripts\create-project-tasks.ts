// Load environment variables from .env.local
process.env.NODE_TLS_REJECT_UNAUTHORIZED = "0";

import { config } from "dotenv";
config({ path: ".env.local" });

import { createClient } from "@supabase/supabase-js";
import { TaskStatus } from "../src/database/models/task.model";
import { CreateProjectTaskDto } from "../src/database/models/project-task.model";
import { addDays, format, subDays, parseISO } from "date-fns";

// Initialize Supabase client with admin privileges
const supabaseUrl = process.env.supabase_url || "";
const supabaseServiceKey = process.env.supabase_service_role || "";

if (!supabaseUrl || !supabaseServiceKey) {
  console.error(
    "Error: supabase_url and supabase_service_role must be set in .env.local"
  );
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseServiceKey);

// Reference date for task status determination
const REFERENCE_DATE = new Date("2025-05-23");

// Admin user credentials
const ADMIN_EMAIL = "<EMAIL>";
const ADMIN_PASSWORD = "Admin@123";

// Task description templates
const TASK_DESCRIPTION_TEMPLATES = [
  "{{action}} {{component}} for {{purpose}}",
  "{{action}} {{component}} documentation",
  "Review and test {{component}} functionality",
  "Implement {{component}} {{feature}}",
  "Fix {{component}} issues related to {{feature}}",
  "Optimize {{component}} performance",
  "Create {{component}} design specifications",
  "Integrate {{component}} with {{feature}}",
  "Update {{component}} to support {{feature}}",
  "Migrate {{component}} data to new format",
];

// Task components
const TASK_COMPONENTS = [
  "user interface",
  "database",
  "API",
  "authentication system",
  "reporting module",
  "dashboard",
  "notification system",
  "payment gateway",
  "admin panel",
  "user management",
  "file upload system",
  "search functionality",
  "analytics module",
  "integration layer",
  "mobile app",
  "web portal",
];

// Task actions
const TASK_ACTIONS = [
  "Develop",
  "Design",
  "Implement",
  "Create",
  "Update",
  "Refactor",
  "Optimize",
  "Test",
  "Document",
  "Deploy",
  "Configure",
  "Analyze",
  "Research",
  "Prototype",
];

// Task features
const TASK_FEATURES = [
  "user authentication",
  "data visualization",
  "real-time updates",
  "export functionality",
  "import functionality",
  "batch processing",
  "automated reporting",
  "user permissions",
  "multi-language support",
  "responsive design",
  "offline capabilities",
  "data encryption",
  "performance monitoring",
  "error logging",
  "user feedback collection",
];

// Task purposes
const TASK_PURPOSES = [
  "improving user experience",
  "enhancing security",
  "meeting client requirements",
  "increasing performance",
  "reducing technical debt",
  "supporting new features",
  "complying with regulations",
  "enabling scalability",
  "facilitating maintenance",
  "supporting mobile devices",
  "improving accessibility",
  "reducing costs",
  "streamlining workflows",
];

// Function to get random item from an array
function getRandomItem<T>(arr: T[]): T {
  return arr[Math.floor(Math.random() * arr.length)];
}

// Function to get random number between min and max (inclusive)
function getRandomNumber(min: number, max: number): number {
  return Math.floor(Math.random() * (max - min + 1)) + min;
}

// Function to generate a random date within a range
function getRandomDate(startDate: Date, endDate: Date): Date {
  const timeDiff = endDate.getTime() - startDate.getTime();
  const randomTime = Math.random() * timeDiff;
  return new Date(startDate.getTime() + randomTime);
}

// Function to generate a random task description
function generateTaskDescription(): string {
  const template = getRandomItem(TASK_DESCRIPTION_TEMPLATES);
  const component = getRandomItem(TASK_COMPONENTS);
  const action = getRandomItem(TASK_ACTIONS);
  const feature = getRandomItem(TASK_FEATURES);
  const purpose = getRandomItem(TASK_PURPOSES);

  return template
    .replace("{{action}}", action)
    .replace("{{component}}", component)
    .replace("{{feature}}", feature)
    .replace("{{purpose}}", purpose);
}

/**
 * Authenticate as admin user and return the user ID
 */
async function authenticateAdmin() {
  console.log(`Authenticating as admin user (${ADMIN_EMAIL})...`);

  try {
    const { data, error } = await supabase.auth.signInWithPassword({
      email: ADMIN_EMAIL,
      password: ADMIN_PASSWORD,
    });

    if (error) {
      console.error("Error authenticating as admin:", error.message);
      process.exit(1);
    }

    console.log(
      `Authenticated as admin successfully! User ID: ${data.user.id}`
    );
    return data.user.id;
  } catch (err) {
    console.error("Unexpected error during authentication:", err);
    process.exit(1);
  }
}

/**
 * Get all projects from the database
 */
async function getAllProjects() {
  try {
    const { data, error } = await supabase
      .from("projects")
      .select("id, project_name, start_project, end_project, status_project")
      .is("deleted_at", null);

    if (error) {
      console.error("Error fetching projects:", error.message);
      return [];
    }

    return data;
  } catch (err) {
    console.error("Unexpected error fetching projects:", err);
    return [];
  }
}

/**
 * Get all active employees from the database
 */
async function getAllActiveEmployees() {
  try {
    const { data, error } = await supabase
      .from("employees")
      .select("id, profile_id")
      .is("deleted_at", null);

    if (error) {
      console.error("Error fetching employees:", error.message);
      return [];
    }

    return data;
  } catch (err) {
    console.error("Unexpected error fetching employees:", err);
    return [];
  }
}

/**
 * Get all managers from the database (for task assignment)
 */
async function getAllManagers() {
  try {
    const { data, error } = await supabase
      .from("user_profiles")
      .select("id, user_id, role, employee_id")
      .eq("role", "Manager")
      .is("deleted_at", null)
      .not("employee_id", "is", null); // Ensure the manager has an associated employee record

    if (error) {
      console.error("Error fetching managers:", error.message);
      return [];
    }

    return data;
  } catch (err) {
    console.error("Unexpected error fetching managers:", err);
    return [];
  }
}

/**
 * Create a single project task
 */
async function createProjectTask(
  taskData: CreateProjectTaskDto,
  userId: string
) {
  console.log(`Creating task: ${taskData.description}`);

  try {
    const timestamp = new Date().toISOString();

    const { data, error } = await supabase
      .from("project_tasks")
      .insert({
        ...taskData,
        created_at: timestamp,
        created_by: userId,
      })
      .select()
      .single();

    if (error) {
      console.error(
        `Error creating task "${taskData.description}":`,
        error.message
      );
      return null;
    }

    console.log(`Task created with ID: ${data.id}`);
    return data;
  } catch (err) {
    console.error(
      `Unexpected error creating task "${taskData.description}":`,
      err
    );
    return null;
  }
}

/**
 * Generate random tasks for a project
 */
async function generateRandomTasksForProject(
  project: any,
  employees: any[],
  managers: any[],
  userId: string
) {
  if (!project || !project.id) {
    console.error("Invalid project data");
    return [];
  }

  // Parse project dates
  const startDate = parseISO(project.start_project);
  const endDate = parseISO(project.end_project);

  // Skip projects that haven't started yet
  const now = new Date();
  if (startDate > now) {
    console.log(
      `Project ${project.project_name} hasn't started yet. Skipping task creation.`
    );
    return [];
  }

  // Determine how many tasks to create (3-10 per project)
  const tasksCount = getRandomNumber(3, 10);
  console.log(
    `Generating ${tasksCount} tasks for project: ${project.project_name}`
  );

  const createdTasks = [];

  // Get a random manager to assign tasks
  const manager = getRandomItem(managers);
  if (!manager || !manager.employee_id) {
    console.error(
      "No managers with employee records available to assign tasks"
    );
    return [];
  }

  // Use the manager's employee_id for assigned_by
  const assignedBy = manager.employee_id;

  for (let i = 0; i < tasksCount; i++) {
    // Get random employee as assignee
    const employee = getRandomItem(employees);
    if (!employee) {
      console.error("No employees available to assign tasks");
      continue;
    }

    // Generate random dates within project timeline
    const taskStartDate = getRandomDate(startDate, endDate);

    // Due date is between start date and project end date
    // For some tasks, make due date very close to start date
    const dueDate =
      Math.random() > 0.7
        ? getRandomDate(taskStartDate, addDays(taskStartDate, 14)) // 30% of tasks due within 2 weeks
        : getRandomDate(taskStartDate, endDate); // 70% of tasks due anytime before project end

    // Format dates as YYYY-MM-DD
    const taskStartDateStr = format(taskStartDate, "yyyy-MM-dd");
    const dueDateStr = format(dueDate, "yyyy-MM-dd");

    // Determine task status based on due date compared to May 23, 2025
    let taskStatus: TaskStatus;

    if (dueDate < REFERENCE_DATE) {
      // Before May 23, 2025: Complete
      taskStatus = TaskStatus.COMPLETED;
    } else if (dueDate.toDateString() === REFERENCE_DATE.toDateString()) {
      // On May 23, 2025: Ongoing
      taskStatus = TaskStatus.ON_PROGRESS;
    } else {
      // After May 23, 2025: Not started
      taskStatus = TaskStatus.NOT_COMPLETED;
    }

    // Create task data
    const taskData: CreateProjectTaskDto = {
      assigned_by: assignedBy,
      description: generateTaskDescription(),
      completion_status: taskStatus,
      employee_id: employee.id,
      initial_date: taskStartDateStr,
      due_date: dueDateStr,
      project_id: project.id,
    };

    // Create the task
    const task = await createProjectTask(taskData, userId);
    if (task) {
      createdTasks.push(task);
    }
  }

  return createdTasks;
}

/**
 * Create tasks for all projects
 */
async function createTasksForAllProjects(userId: string) {
  // Get all projects
  const projects = await getAllProjects();
  if (projects.length === 0) {
    console.error("No projects found in the system.");
    return;
  }

  console.log(`Found ${projects.length} projects.`);

  // Get all employees
  const employees = await getAllActiveEmployees();
  if (employees.length === 0) {
    console.error("No active employees found in the system.");
    return;
  }

  console.log(`Found ${employees.length} active employees.`);

  // Get all managers
  const managers = await getAllManagers();
  if (managers.length === 0) {
    console.error("No managers found in the system.");
    return;
  }

  console.log(`Found ${managers.length} managers.`);

  let createdCount = 0;

  // Create tasks for each project
  for (const project of projects) {
    const tasks = await generateRandomTasksForProject(
      project,
      employees,
      managers,
      userId
    );
    createdCount += tasks.length;
  }

  console.log("\nTask creation summary:");
  console.log(`- Total created: ${createdCount}`);
}

/**
 * Create tasks for a specific project
 */
async function createTasksForProject(projectId: string, userId: string) {
  // Get the project
  const { data: project, error } = await supabase
    .from("projects")
    .select("id, project_name, start_project, end_project, status_project")
    .eq("id", projectId)
    .is("deleted_at", null)
    .single();

  if (error || !project) {
    console.error(`Project with ID ${projectId} not found.`);
    return;
  }

  console.log(`Creating tasks for project: ${project.project_name}`);

  // Get all employees
  const employees = await getAllActiveEmployees();
  if (employees.length === 0) {
    console.error("No active employees found in the system.");
    return;
  }

  // Get all managers
  const managers = await getAllManagers();
  if (managers.length === 0) {
    console.error("No managers found in the system.");
    return;
  }

  // Generate tasks for the project
  const tasks = await generateRandomTasksForProject(
    project,
    employees,
    managers,
    userId
  );

  console.log("\nTask creation summary for this project:");
  console.log(`- Total created: ${tasks.length}`);
}

/**
 * Main function to run the script
 */
async function main() {
  console.log("Starting project task creation script...");

  // Authenticate as admin
  const userId = await authenticateAdmin();

  const projectParam = process.argv[2];

  if (projectParam && projectParam.startsWith("--project=")) {
    const projectId = projectParam.split("=")[1];
    await createTasksForProject(projectId, userId);
  } else {
    await createTasksForAllProjects(userId);
  }

  console.log("\nProject task creation completed!");
}

// Run the main function
main();
