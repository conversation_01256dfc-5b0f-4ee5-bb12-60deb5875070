// Load environment variables from .env.local
process.env.NODE_TLS_REJECT_UNAUTHORIZED = "0";

import { config } from "dotenv";
config({ path: ".env.local" });

import { createClient } from "@supabase/supabase-js";
import {
  ProjectCategory,
  ProjectStatus,
  CreateProjectDto,
} from "../src/database/models/project.model";
import { addDays, format, subDays } from "date-fns";

// Initialize Supabase client with admin privileges
const supabaseUrl = process.env.supabase_url || "";
const supabaseServiceKey = process.env.supabase_service_role || "";

if (!supabaseUrl || !supabaseServiceKey) {
  console.error(
    "Error: supabase_url and supabase_service_role must be set in .env.local"
  );
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseServiceKey);

// Admin user credentials
const ADMIN_EMAIL = "<EMAIL>";
const ADMIN_PASSWORD = "Admin@123";

// Project name templates
const PROJECT_NAME_TEMPLATES = [
  "Development of {{client}} {{system}}",
  "{{client}} {{type}} Implementation",
  "{{client}} Digital Transformation",
  "{{client}} {{system}} Integration",
  "{{client}} {{type}} Upgrade",
  "{{client}} {{system}} Migration",
  "{{client}} {{type}} Optimization",
  "{{client}} {{system}} Deployment",
  "{{client}} {{type}} Consulting",
  "{{client}} {{system}} Support",
];

// System types for project names
const SYSTEM_TYPES = [
  "ERP System",
  "CRM Platform",
  "HR Management System",
  "Inventory Management",
  "Financial System",
  "Accounting Software",
  "E-commerce Platform",
  "Mobile Application",
  "Web Portal",
  "Business Intelligence",
  "Data Warehouse",
  "Content Management System",
];

// Project types for project names
const PROJECT_TYPES = [
  "System",
  "Platform",
  "Infrastructure",
  "Software",
  "Network",
  "Security",
  "Cloud",
  "Database",
  "Application",
  "Architecture",
];

// Budget ranges (in IDR)
const BUDGET_RANGES = [
  { min: ********, max: ******** }, // 10-50 million
  { min: ********, max: ********0 }, // 50-100 million
  { min: ********0, max: ********0 }, // 100-500 million
  { min: ********0, max: ********00 }, // 500 million - 1 billion
  { min: ********00, max: ********** }, // 1-5 billion
];

// Objectives templates
const OBJECTIVES_TEMPLATES = [
  "Implement a {{system}} to improve {{area}} efficiency by {{percentage}}%",
  "Develop a {{system}} to streamline {{area}} processes and reduce costs",
  "Create a {{system}} that enhances {{area}} capabilities and user experience",
  "Upgrade existing {{system}} to improve {{area}} performance and security",
  "Design and deploy a {{system}} that meets {{area}} requirements and industry standards",
  "Integrate {{system}} with existing infrastructure to optimize {{area}} operations",
  "Establish a {{system}} that supports {{area}} growth and scalability",
  "Implement a {{system}} that ensures {{area}} compliance with regulations",
  "Develop a {{system}} that provides real-time {{area}} analytics and reporting",
  "Create a {{system}} that automates {{area}} workflows and reduces manual effort",
];

// Business areas for objectives
const BUSINESS_AREAS = [
  "operational",
  "financial",
  "customer service",
  "sales",
  "marketing",
  "human resources",
  "inventory management",
  "supply chain",
  "data management",
  "business intelligence",
  "administrative",
  "communication",
];

// Function to get random item from an array
function getRandomItem<T>(arr: T[]): T {
  return arr[Math.floor(Math.random() * arr.length)];
}

// Function to get random number between min and max (inclusive)
function getRandomNumber(min: number, max: number): number {
  return Math.floor(Math.random() * (max - min + 1)) + min;
}

// Function to format currency in IDR
function formatCurrency(amount: number): string {
  return amount.toLocaleString("id-ID");
}

// Function to generate a random date within a range
function getRandomDate(startDate: Date, endDate: Date): Date {
  const timeDiff = endDate.getTime() - startDate.getTime();
  const randomTime = Math.random() * timeDiff;
  return new Date(startDate.getTime() + randomTime);
}

// Function to generate a random project name
function generateProjectName(orgName: string): string {
  const template = getRandomItem(PROJECT_NAME_TEMPLATES);
  const systemType = getRandomItem(SYSTEM_TYPES);
  const projectType = getRandomItem(PROJECT_TYPES);

  return template
    .replace("{{client}}", orgName)
    .replace("{{system}}", systemType)
    .replace("{{type}}", projectType);
}

// Function to generate random objectives
function generateObjectives(): string {
  const template = getRandomItem(OBJECTIVES_TEMPLATES);
  const systemType = getRandomItem(SYSTEM_TYPES);
  const businessArea = getRandomItem(BUSINESS_AREAS);
  const percentage = getRandomNumber(10, 50);

  return template
    .replace("{{system}}", systemType)
    .replace("{{area}}", businessArea)
    .replace("{{percentage}}", percentage.toString());
}

// Function to generate a random budget
function generateBudget(): string {
  const range = getRandomItem(BUDGET_RANGES);
  const budget = getRandomNumber(range.min, range.max);
  return formatCurrency(budget);
}

/**
 * Authenticate as admin user and return the user ID
 */
async function authenticateAdmin() {
  console.log(`Authenticating as admin user (${ADMIN_EMAIL})...`);

  try {
    const { data, error } = await supabase.auth.signInWithPassword({
      email: ADMIN_EMAIL,
      password: ADMIN_PASSWORD,
    });

    if (error) {
      console.error("Error authenticating as admin:", error.message);
      process.exit(1);
    }

    console.log(
      `Authenticated as admin successfully! User ID: ${data.user.id}`
    );
    return data.user.id;
  } catch (err) {
    console.error("Unexpected error during authentication:", err);
    process.exit(1);
  }
}

/**
 * Get all organizations from the database
 */
async function getAllOrganizations() {
  try {
    const { data, error } = await supabase
      .from("organizations")
      .select("id, name")
      .is("deleted_at", null);

    if (error) {
      console.error("Error fetching organizations:", error.message);
      return [];
    }

    return data;
  } catch (err) {
    console.error("Unexpected error fetching organizations:", err);
    return [];
  }
}

/**
 * Get all active employees from the database
 */
async function getAllActiveEmployees() {
  try {
    const { data, error } = await supabase
      .from("employees")
      .select("id, profile_id")
      .is("deleted_at", null);

    if (error) {
      console.error("Error fetching employees:", error.message);
      return [];
    }

    return data;
  } catch (err) {
    console.error("Unexpected error fetching employees:", err);
    return [];
  }
}

/**
 * Check if a project already exists for an organization with the same name
 */
async function projectExistsForOrganization(
  organizationId: string,
  projectName: string
): Promise<boolean> {
  try {
    const { data, error } = await supabase
      .from("projects")
      .select("id")
      .eq("organization_id", organizationId)
      .eq("project_name", projectName)
      .is("deleted_at", null)
      .maybeSingle();

    return !!data;
  } catch (err) {
    console.error("Error checking for existing project:", err);
    return false;
  }
}

/**
 * Create a single project
 */
async function createProject(projectData: CreateProjectDto, userId: string) {
  console.log(`Creating project: ${projectData.project_name}`);

  try {
    // Check if project already exists for this organization with the same name
    if (
      await projectExistsForOrganization(
        projectData.organization_id,
        projectData.project_name
      )
    ) {
      console.log(
        `Project "${projectData.project_name}" already exists for this organization. Skipping.`
      );
      return null;
    }

    const timestamp = new Date().toISOString();

    const { data, error } = await supabase
      .from("projects")
      .insert({
        ...projectData,
        created_at: timestamp,
        created_by: userId,
        gantt_chart_id: "TODO-gantt-chart-implementation",
        project_charter_id: "TODO-project-charter-implementation",
      })
      .select()
      .single();

    if (error) {
      console.error(
        `Error creating project "${projectData.project_name}":`,
        error.message
      );
      return null;
    }

    console.log(`Project created with ID: ${data.id}`);
    return data;
  } catch (err) {
    console.error(
      `Unexpected error creating project "${projectData.project_name}":`,
      err
    );
    return null;
  }
}

/**
 * Generate a random project for an organization
 */
async function generateRandomProject(
  organization: { id: string; name: string },
  employees: any[],
  userId: string
) {
  // Get random employee as PIC
  const randomEmployee = getRandomItem(employees);
  if (!randomEmployee) {
    console.error("No employees available to assign as PIC");
    return null;
  }

  // Generate random dates
  const now = new Date();
  const pastDate = subDays(now, getRandomNumber(30, 365)); // Start date between 1 month and 1 year ago
  const futureDate = addDays(now, getRandomNumber(30, 730)); // End date between 1 month and 2 years in future

  // For some projects, make them start in the future
  const startDate =
    Math.random() > 0.7
      ? getRandomDate(now, addDays(now, 180)) // 30% of projects start in the future (up to 6 months)
      : getRandomDate(pastDate, now); // 70% of projects started in the past

  // End date is always after start date
  const endDate = getRandomDate(
    addDays(startDate, 30), // Minimum 1 month duration
    addDays(startDate, 730) // Maximum 2 years duration
  );

  // Format dates as YYYY-MM-DD
  const startDateStr = format(startDate, "yyyy-MM-dd");
  const endDateStr = format(endDate, "yyyy-MM-dd");

  // Determine status based on dates
  let status: ProjectStatus;
  if (startDate > now) {
    status = ProjectStatus.NOT_STARTED;
  } else if (endDate < now) {
    // For completed projects, randomly mark some as cancelled
    status =
      Math.random() > 0.9 ? ProjectStatus.CANCELLED : ProjectStatus.COMPLETED;
  } else {
    status = ProjectStatus.IN_PROGRESS;
  }

  // Generate project data
  const projectData: CreateProjectDto = {
    organization_id: organization.id,
    project_category: getRandomItem(Object.values(ProjectCategory)),
    project_name: generateProjectName(organization.name),
    pic_project: randomEmployee.id,
    start_project: startDateStr,
    end_project: endDateStr,
    status_project: status,
    budget_project: generateBudget(),
    objectives: generateObjectives(),
  };

  // Create the project
  return await createProject(projectData, userId);
}

/**
 * Create projects for all organizations
 */
async function createProjectsForAllOrganizations(userId: string) {
  // Get all organizations
  const organizations = await getAllOrganizations();
  if (organizations.length === 0) {
    console.error("No organizations found in the system.");
    return;
  }

  console.log(`Found ${organizations.length} organizations.`);

  // Get all employees
  const employees = await getAllActiveEmployees();
  if (employees.length === 0) {
    console.error("No active employees found in the system.");
    return;
  }

  console.log(`Found ${employees.length} active employees.`);

  let createdCount = 0;
  let skippedCount = 0;

  // Create projects for each organization
  for (const organization of organizations) {
    // Create 1-3 projects per organization
    const projectsToCreate = getRandomNumber(1, 3);

    console.log(
      `\nCreating ${projectsToCreate} projects for ${organization.name}...`
    );

    for (let i = 0; i < projectsToCreate; i++) {
      const result = await generateRandomProject(
        organization,
        employees,
        userId
      );
      if (result) {
        createdCount++;
      } else {
        skippedCount++;
      }
    }
  }

  console.log("\nProject creation summary:");
  console.log(`- Total created: ${createdCount}`);
  console.log(`- Total skipped: ${skippedCount}`);
}

/**
 * Create projects for a specific organization
 */
async function createProjectsForOrganization(
  organizationId: string,
  userId: string
) {
  // Get the organization
  const { data: organization, error } = await supabase
    .from("organizations")
    .select("id, name")
    .eq("id", organizationId)
    .is("deleted_at", null)
    .single();

  if (error || !organization) {
    console.error(`Organization with ID ${organizationId} not found.`);
    return;
  }

  console.log(`Creating projects for organization: ${organization.name}`);

  // Get all employees
  const employees = await getAllActiveEmployees();
  if (employees.length === 0) {
    console.error("No active employees found in the system.");
    return;
  }

  // Create 1-3 projects for the organization
  const projectsToCreate = getRandomNumber(1, 3);
  let createdCount = 0;
  let skippedCount = 0;

  for (let i = 0; i < projectsToCreate; i++) {
    const result = await generateRandomProject(organization, employees, userId);
    if (result) {
      createdCount++;
    } else {
      skippedCount++;
    }
  }

  console.log("\nProject creation summary for this organization:");
  console.log(`- Total created: ${createdCount}`);
  console.log(`- Total skipped: ${skippedCount}`);
}

/**
 * Main function to run the script
 */
async function main() {
  console.log("Starting project creation script...");

  // Authenticate as admin
  const userId = await authenticateAdmin();

  const organizationParam = process.argv[2];

  if (organizationParam && organizationParam.startsWith("--organization=")) {
    const organizationId = organizationParam.split("=")[1];
    await createProjectsForOrganization(organizationId, userId);
  } else {
    await createProjectsForAllOrganizations(userId);
  }

  console.log("\nProject creation completed!");
}

// Run the main function
main();
