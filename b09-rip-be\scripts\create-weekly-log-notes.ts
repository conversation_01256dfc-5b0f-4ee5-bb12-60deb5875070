// Load environment variables from .env.local
process.env.NODE_TLS_REJECT_UNAUTHORIZED = "0";

import { config } from "dotenv";
config({ path: ".env.local" });

import { createClient } from "@supabase/supabase-js";
import { format } from "date-fns";
import { CreateWeeklyLogNoteDto } from "../src/database/models/weekly-log-note.model";

// Initialize Supabase client with admin privileges
const supabaseUrl = process.env.supabase_url || "";
const supabaseServiceKey = process.env.supabase_service_role || "";

if (!supabaseUrl || !supabaseServiceKey) {
  console.error(
    "Error: supabase_url and supabase_service_role must be set in .env.local"
  );
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseServiceKey);

// Admin user credentials
const ADMIN_EMAIL = "<EMAIL>";
const ADMIN_PASSWORD = "Admin@123";

// Note templates for different days of the week
const NOTE_TEMPLATES = {
  1: [
    // Monday
    "Started the week by reviewing project goals and priorities.",
    "Conducted team meeting to align on weekly objectives.",
    "Set up project environment and prepared resources for the week.",
    "Analyzed requirements and created detailed task breakdown.",
    "Identified potential blockers and developed mitigation strategies.",
    "Established communication channels with stakeholders.",
    "Reviewed feedback from previous week and adjusted approach.",
  ],
  2: [
    // Tuesday
    "Made progress on core development tasks.",
    "Resolved technical issues from previous implementation.",
    "Collaborated with design team on UI/UX improvements.",
    "Conducted code review for team members' work.",
    "Implemented key features according to sprint plan.",
    "Documented technical decisions and architecture changes.",
    "Optimized performance for critical system components.",
  ],
  3: [
    // Wednesday
    "Reached milestone in development phase.",
    "Conducted mid-week progress review with team.",
    "Addressed integration issues between components.",
    "Implemented feedback from initial testing.",
    "Refined implementation based on stakeholder input.",
    "Prepared demo for upcoming review meeting.",
    "Documented progress and updated project timeline.",
  ],
  4: [
    // Thursday
    "Focused on resolving remaining technical challenges.",
    "Conducted thorough testing of implemented features.",
    "Prepared documentation for handover/knowledge transfer.",
    "Optimized code and improved system performance.",
    "Addressed edge cases and improved error handling.",
    "Collaborated with QA team on test scenarios.",
    "Finalized implementation details for current sprint items.",
  ],
  5: [
    // Friday
    "Completed weekly objectives and documented achievements.",
    "Conducted end-of-week review meeting with team.",
    "Prepared status report for project stakeholders.",
    "Planned tasks and priorities for next week.",
    "Addressed final feedback and made necessary adjustments.",
    "Conducted retrospective to identify improvement areas.",
    "Ensured all documentation is up-to-date before weekend.",
  ],
};

// Function to get random item from an array
function getRandomItem<T>(arr: T[]): T {
  return arr[Math.floor(Math.random() * arr.length)];
}

// Function to get random number between min and max (inclusive)
function getRandomNumber(min: number, max: number): number {
  return Math.floor(Math.random() * (max - min + 1)) + min;
}

/**
 * Authenticate as admin user and return the user ID
 */
async function authenticateAdmin() {
  console.log(`Authenticating as admin user (${ADMIN_EMAIL})...`);

  try {
    const { data, error } = await supabase.auth.signInWithPassword({
      email: ADMIN_EMAIL,
      password: ADMIN_PASSWORD,
    });

    if (error) {
      console.error("Error authenticating as admin:", error.message);
      process.exit(1);
    }

    console.log(
      `Authenticated as admin successfully! User ID: ${data.user.id}`
    );
    return data.user.id;
  } catch (err) {
    console.error("Unexpected error during authentication:", err);
    process.exit(1);
  }
}

/**
 * Get all weekly logs from the database
 */
async function getAllWeeklyLogs() {
  try {
    const { data, error } = await supabase
      .from("weekly_logs")
      .select("id, week_number, week_start_date, week_end_date, project_id")
      .is("deleted_at", null);

    if (error) {
      console.error("Error fetching weekly logs:", error.message);
      return [];
    }

    return data;
  } catch (err) {
    console.error("Unexpected error fetching weekly logs:", err);
    return [];
  }
}

/**
 * Check if a note already exists for a weekly log and day of week
 */
async function noteExistsForWeeklyLogAndDay(
  weeklyLogId: string,
  dayOfWeek: number
): Promise<boolean> {
  try {
    const { data, error } = await supabase
      .from("weekly_log_notes")
      .select("id")
      .eq("weekly_log_id", weeklyLogId)
      .eq("day_of_week", dayOfWeek)
      .is("deleted_at", null)
      .maybeSingle();

    return !!data;
  } catch (err) {
    console.error("Error checking for existing note:", err);
    return false;
  }
}

/**
 * Create a single weekly log note
 */
async function createWeeklyLogNote(
  noteData: CreateWeeklyLogNoteDto,
  userId: string
) {
  console.log(
    `Creating note for weekly log ID ${noteData.weekly_log_id}, day ${noteData.day_of_week}`
  );

  try {
    // Check if note already exists for this weekly log and day
    if (
      await noteExistsForWeeklyLogAndDay(
        noteData.weekly_log_id,
        noteData.day_of_week
      )
    ) {
      console.log(
        `Note for weekly log ID ${noteData.weekly_log_id}, day ${noteData.day_of_week} already exists. Skipping.`
      );
      return null;
    }

    const timestamp = new Date().toISOString();

    const { data, error } = await supabase
      .from("weekly_log_notes")
      .insert({
        ...noteData,
        created_at: timestamp,
        created_by: userId,
      })
      .select()
      .single();

    if (error) {
      console.error(
        `Error creating note for weekly log ID ${noteData.weekly_log_id}, day ${noteData.day_of_week}:`,
        error.message
      );
      return null;
    }

    console.log(`Note created with ID: ${data.id}`);
    return data;
  } catch (err) {
    console.error(
      `Unexpected error creating note for weekly log ID ${noteData.weekly_log_id}, day ${noteData.day_of_week}:`,
      err
    );
    return null;
  }
}

/**
 * Generate random notes for a weekly log
 */
async function generateRandomNotesForWeeklyLog(weeklyLog: any, userId: string) {
  if (!weeklyLog || !weeklyLog.id) {
    console.error("Invalid weekly log data");
    return [];
  }

  console.log(
    `Generating notes for weekly log: Week ${weeklyLog.week_number} (ID: ${weeklyLog.id})`
  );

  const createdNotes = [];

  // Generate notes for weekdays (1-5, Monday-Friday)
  for (let dayOfWeek = 1; dayOfWeek <= 5; dayOfWeek++) {
    // Randomly decide whether to create a note for this day (70% chance)
    if (Math.random() > 0.3) {
      // Get templates for this day of week
      const templates =
        NOTE_TEMPLATES[dayOfWeek as keyof typeof NOTE_TEMPLATES] || [];

      if (templates.length === 0) {
        console.error(`No templates found for day ${dayOfWeek}`);
        continue;
      }

      // Create note data
      const noteData: CreateWeeklyLogNoteDto = {
        note: getRandomItem(templates),
        weekly_log_id: weeklyLog.id,
        day_of_week: dayOfWeek,
      };

      // Create the note
      const note = await createWeeklyLogNote(noteData, userId);
      if (note) {
        createdNotes.push(note);
      }
    }
  }

  return createdNotes;
}

/**
 * Create notes for all weekly logs
 */
async function createNotesForAllWeeklyLogs(userId: string) {
  // Get all weekly logs
  const weeklyLogs = await getAllWeeklyLogs();
  if (weeklyLogs.length === 0) {
    console.error("No weekly logs found in the system.");
    return;
  }

  console.log(`Found ${weeklyLogs.length} weekly logs.`);

  let createdCount = 0;

  // Create notes for each weekly log
  for (const weeklyLog of weeklyLogs) {
    const notes = await generateRandomNotesForWeeklyLog(weeklyLog, userId);
    createdCount += notes.length;
  }

  console.log("\nWeekly log note creation summary:");
  console.log(`- Total created: ${createdCount}`);
}

/**
 * Create notes for a specific weekly log
 */
async function createNotesForWeeklyLog(weeklyLogId: string, userId: string) {
  // Get the weekly log
  const { data: weeklyLog, error } = await supabase
    .from("weekly_logs")
    .select("id, week_number, week_start_date, week_end_date, project_id")
    .eq("id", weeklyLogId)
    .is("deleted_at", null)
    .single();

  if (error || !weeklyLog) {
    console.error(`Weekly log with ID ${weeklyLogId} not found.`);
    return;
  }

  console.log(`Creating notes for weekly log: Week ${weeklyLog.week_number}`);

  // Generate notes for the weekly log
  const notes = await generateRandomNotesForWeeklyLog(weeklyLog, userId);

  console.log("\nWeekly log note creation summary for this weekly log:");
  console.log(`- Total created: ${notes.length}`);
}

/**
 * Main function to run the script
 */
async function main() {
  console.log("Starting weekly log note creation script...");

  // Authenticate as admin
  const userId = await authenticateAdmin();

  const weeklyLogParam = process.argv[2];

  if (weeklyLogParam && weeklyLogParam.startsWith("--weekly-log=")) {
    const weeklyLogId = weeklyLogParam.split("=")[1];
    await createNotesForWeeklyLog(weeklyLogId, userId);
  } else {
    await createNotesForAllWeeklyLogs(userId);
  }

  console.log("\nWeekly log note creation completed!");
}

// Run the main function
main();
