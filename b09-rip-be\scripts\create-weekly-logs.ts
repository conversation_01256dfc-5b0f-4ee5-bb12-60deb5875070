// Load environment variables from .env.local
process.env.NODE_TLS_REJECT_UNAUTHORIZED = "0";

import { config } from "dotenv";
config({ path: ".env.local" });

import { createClient } from "@supabase/supabase-js";
import {
  addDays,
  format,
  parseISO,
  startOfWeek,
  endOfWeek,
  differenceInWeeks,
  isBefore,
  isAfter,
} from "date-fns";
import { CreateWeeklyLogDto } from "../src/database/models/weekly-log.model";

// Initialize Supabase client with admin privileges
const supabaseUrl = process.env.supabase_url || "";
const supabaseServiceKey = process.env.supabase_service_role || "";

if (!supabaseUrl || !supabaseServiceKey) {
  console.error(
    "Error: supabase_url and supabase_service_role must be set in .env.local"
  );
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseServiceKey);

// Admin user credentials
const ADMIN_EMAIL = "<EMAIL>";
const ADMIN_PASSWORD = "Admin@123";

// Function to get random number between min and max (inclusive)
function getRandomNumber(min: number, max: number): number {
  return Math.floor(Math.random() * (max - min + 1)) + min;
}

/**
 * Authenticate as admin user and return the user ID
 */
async function authenticateAdmin() {
  console.log(`Authenticating as admin user (${ADMIN_EMAIL})...`);

  try {
    const { data, error } = await supabase.auth.signInWithPassword({
      email: ADMIN_EMAIL,
      password: ADMIN_PASSWORD,
    });

    if (error) {
      console.error("Error authenticating as admin:", error.message);
      process.exit(1);
    }

    console.log(
      `Authenticated as admin successfully! User ID: ${data.user.id}`
    );
    return data.user.id;
  } catch (err) {
    console.error("Unexpected error during authentication:", err);
    process.exit(1);
  }
}

/**
 * Get all projects from the database
 */
async function getAllProjects() {
  try {
    const { data, error } = await supabase
      .from("projects")
      .select("id, project_name, start_project, end_project, status_project")
      .is("deleted_at", null);

    if (error) {
      console.error("Error fetching projects:", error.message);
      return [];
    }

    return data;
  } catch (err) {
    console.error("Unexpected error fetching projects:", err);
    return [];
  }
}

/**
 * Check if a weekly log already exists for a project and week number
 */
async function weeklyLogExistsForProjectAndWeek(
  projectId: string,
  weekNumber: number
): Promise<boolean> {
  try {
    const { data, error } = await supabase
      .from("weekly_logs")
      .select("id")
      .eq("project_id", projectId)
      .eq("week_number", weekNumber)
      .is("deleted_at", null)
      .maybeSingle();

    return !!data;
  } catch (err) {
    console.error("Error checking for existing weekly log:", err);
    return false;
  }
}

/**
 * Create a single weekly log
 */
async function createWeeklyLog(
  weeklyLogData: CreateWeeklyLogDto,
  userId: string
) {
  console.log(
    `Creating weekly log for project ID ${weeklyLogData.project_id}, week ${weeklyLogData.week_number}`
  );

  try {
    // Check if weekly log already exists for this project and week
    if (
      await weeklyLogExistsForProjectAndWeek(
        weeklyLogData.project_id,
        weeklyLogData.week_number
      )
    ) {
      console.log(
        `Weekly log for project ID ${weeklyLogData.project_id}, week ${weeklyLogData.week_number} already exists. Skipping.`
      );
      return null;
    }

    const timestamp = new Date().toISOString();

    const { data, error } = await supabase
      .from("weekly_logs")
      .insert({
        ...weeklyLogData,
        created_at: timestamp,
        created_by: userId,
      })
      .select()
      .single();

    if (error) {
      console.error(
        `Error creating weekly log for project ID ${weeklyLogData.project_id}, week ${weeklyLogData.week_number}:`,
        error.message
      );
      return null;
    }

    console.log(`Weekly log created with ID: ${data.id}`);
    return data;
  } catch (err) {
    console.error(
      `Unexpected error creating weekly log for project ID ${weeklyLogData.project_id}, week ${weeklyLogData.week_number}:`,
      err
    );
    return null;
  }
}

/**
 * Generate weekly logs for a project
 */
async function generateWeeklyLogsForProject(project: any, userId: string) {
  if (!project || !project.id) {
    console.error("Invalid project data");
    return [];
  }

  // Parse project dates
  const startDate = parseISO(project.start_project);
  const endDate = parseISO(project.end_project);
  const now = new Date();

  // Skip projects that haven't started yet
  if (isAfter(startDate, now)) {
    console.log(
      `Project ${project.project_name} hasn't started yet. Skipping weekly log creation.`
    );
    return [];
  }

  // Calculate the effective end date (either project end date or current date, whichever is earlier)
  const effectiveEndDate = isBefore(endDate, now) ? endDate : now;

  // Calculate the number of weeks between start date and effective end date
  const totalWeeks = differenceInWeeks(effectiveEndDate, startDate) + 1;

  console.log(
    `Generating weekly logs for project: ${project.project_name} (${totalWeeks} weeks)`
  );

  const createdLogs = [];

  // Create weekly logs for each week
  for (let weekNumber = 1; weekNumber <= totalWeeks; weekNumber++) {
    // Calculate the start and end dates for this week
    // Ensure week starts on Monday
    const weekStartDate = startOfWeek(
      addDays(startDate, (weekNumber - 1) * 7),
      { weekStartsOn: 1 }
    );
    // End date is Friday (4 days after Monday)
    const weekEndDate = addDays(weekStartDate, 4);

    // Format dates as YYYY-MM-DD
    const weekStartDateStr = format(weekStartDate, "yyyy-MM-dd");
    const weekEndDateStr = format(weekEndDate, "yyyy-MM-dd");

    // Create weekly log data
    const weeklyLogData: CreateWeeklyLogDto = {
      week_number: weekNumber,
      week_start_date: weekStartDateStr,
      week_end_date: weekEndDateStr,
      project_id: project.id,
    };

    // Create the weekly log
    const log = await createWeeklyLog(weeklyLogData, userId);
    if (log) {
      createdLogs.push(log);
    }
  }

  return createdLogs;
}

/**
 * Create weekly logs for all projects
 */
async function createWeeklyLogsForAllProjects(userId: string) {
  // Get all projects
  const projects = await getAllProjects();
  if (projects.length === 0) {
    console.error("No projects found in the system.");
    return;
  }

  console.log(`Found ${projects.length} projects.`);

  let createdCount = 0;
  let skippedCount = 0;

  // Create weekly logs for each project
  for (const project of projects) {
    const logs = await generateWeeklyLogsForProject(project, userId);
    createdCount += logs.length;

    // If no logs were created, the project was skipped
    if (logs.length === 0) {
      skippedCount++;
    }
  }

  console.log("\nWeekly log creation summary:");
  console.log(`- Total created: ${createdCount}`);
  console.log(`- Projects skipped: ${skippedCount}`);
}

/**
 * Create weekly logs for a specific project
 */
async function createWeeklyLogsForProject(projectId: string, userId: string) {
  // Get the project
  const { data: project, error } = await supabase
    .from("projects")
    .select("id, project_name, start_project, end_project, status_project")
    .eq("id", projectId)
    .is("deleted_at", null)
    .single();

  if (error || !project) {
    console.error(`Project with ID ${projectId} not found.`);
    return;
  }

  console.log(`Creating weekly logs for project: ${project.project_name}`);

  // Generate weekly logs for the project
  const logs = await generateWeeklyLogsForProject(project, userId);

  console.log("\nWeekly log creation summary for this project:");
  console.log(`- Total created: ${logs.length}`);
}

/**
 * Main function to run the script
 */
async function main() {
  console.log("Starting weekly log creation script...");

  // Authenticate as admin
  const userId = await authenticateAdmin();

  const projectParam = process.argv[2];

  if (projectParam && projectParam.startsWith("--project=")) {
    const projectId = projectParam.split("=")[1];
    await createWeeklyLogsForProject(projectId, userId);
  } else {
    await createWeeklyLogsForAllProjects(userId);
  }

  console.log("\nWeekly log creation completed!");
}

// Run the main function
main();
