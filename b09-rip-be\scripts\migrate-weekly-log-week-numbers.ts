// Load environment variables from .env.local
import { config } from "dotenv";
config({ path: ".env.local" });

import { supabase } from "../src/libs/supabase";
import {
  getProjectWeekNumber,
  getProjectWeekStartDate,
  getProjectWeekEndDate,
} from "../src/utils/date";

/**
 * Migrate existing weekly logs to use project-specific week numbers
 *
 * This script updates all existing weekly logs to use project-specific week numbers
 * instead of ISO week numbers based on the year.
 *
 * For each weekly log:
 * 1. Get the project's start date
 * 2. Calculate the project-specific week number based on the weekly log's start date
 * 3. Update the weekly log with the new week number
 * 4. Also update the week_start_date and week_end_date to ensure consistency
 */
async function migrateWeeklyLogs() {
  console.log("Starting weekly log migration...");

  try {
    // 1. Get all projects with their start dates
    const { data: projects, error: projectsError } = await supabase
      .from("projects")
      .select("id, project_name, start_project")
      .is("deleted_at", null);

    if (projectsError) {
      throw new Error(`Failed to get projects: ${projectsError.message}`);
    }

    console.log(`Found ${projects.length} projects to process`);

    // Create a map of project ID to start date for quick lookup
    const projectStartDates = new Map<string, string>();
    projects.forEach((project) => {
      projectStartDates.set(project.id, project.start_project);
    });

    // 2. Get all weekly logs
    const { data: weeklyLogs, error: logsError } = await supabase
      .from("weekly_logs")
      .select("*")
      .is("deleted_at", null);

    if (logsError) {
      throw new Error(`Failed to get weekly logs: ${logsError.message}`);
    }

    console.log(`Found ${weeklyLogs.length} weekly logs to process`);

    // 3. Process each weekly log
    let updatedCount = 0;
    let skippedCount = 0;
    let errorCount = 0;

    for (const weeklyLog of weeklyLogs) {
      try {
        // Get the project start date
        const projectStartDate = projectStartDates.get(weeklyLog.project_id);

        if (!projectStartDate) {
          console.warn(
            `Project ${weeklyLog.project_id} not found or has no start date, skipping weekly log ${weeklyLog.id}`
          );
          skippedCount++;
          continue;
        }

        // Calculate the project-specific week number based on the weekly log's start date
        const newWeekNumber = getProjectWeekNumber(
          projectStartDate,
          weeklyLog.week_start_date
        );

        // Calculate the correct start and end dates for this week number
        const newWeekStartDate = getProjectWeekStartDate(
          projectStartDate,
          newWeekNumber
        );
        const newWeekEndDate = getProjectWeekEndDate(
          projectStartDate,
          newWeekNumber
        );

        // Check if anything needs to be updated
        if (
          weeklyLog.week_number === newWeekNumber &&
          weeklyLog.week_start_date === newWeekStartDate &&
          weeklyLog.week_end_date === newWeekEndDate
        ) {
          console.log(
            `Weekly log ${weeklyLog.id} already has correct week number and dates, skipping`
          );
          skippedCount++;
          continue;
        }

        // Update the weekly log with the new week number and dates
        const { error: updateError } = await supabase
          .from("weekly_logs")
          .update({
            week_number: newWeekNumber,
            week_start_date: newWeekStartDate,
            week_end_date: newWeekEndDate,
            updated_at: new Date().toISOString(),
            updated_by: "system-migration",
          })
          .eq("id", weeklyLog.id);

        if (updateError) {
          console.error(
            `Error updating weekly log ${weeklyLog.id}: ${updateError.message}`
          );
          errorCount++;
        } else {
          console.log(
            `Updated weekly log ${weeklyLog.id}: week ${weeklyLog.week_number} -> week ${newWeekNumber}`
          );
          updatedCount++;
        }
      } catch (error) {
        console.error(`Error processing weekly log ${weeklyLog.id}:`, error);
        errorCount++;
      }
    }

    console.log("Weekly log migration completed:");
    console.log(`- Updated: ${updatedCount}`);
    console.log(`- Skipped: ${skippedCount}`);
    console.log(`- Errors: ${errorCount}`);
    console.log(`- Total: ${weeklyLogs.length}`);
  } catch (error) {
    console.error("Migration failed:", error);
  }
}

// Run the migration
migrateWeeklyLogs().catch(console.error);
