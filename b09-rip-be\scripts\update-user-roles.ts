// Load environment variables from .env.local
import { config } from "dotenv";
config({ path: ".env.local" });

import { createClient } from "@supabase/supabase-js";
import { UserRole } from "../src/database/models/user-profile.model";

// Initialize Supabase client with admin privileges
const supabaseUrl = process.env.supabase_url || "";
const supabaseServiceKey = process.env.supabase_service_role || "";

if (!supabaseUrl || !supabaseServiceKey) {
  console.error(
    "Error: supabase_url and supabase_service_role must be set in .env.local"
  );
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseServiceKey);

/**
 * Updates user_metadata in Supabase Auth with the role from the user_profiles table
 * This ensures the JWT token will include the user's role
 */
async function updateAllUserRoles() {
  console.log("Starting user role metadata update...");

  try {
    // Get all user profiles
    const { data: profiles, error: profileError } = await supabase
      .from("user_profiles")
      .select("user_id, role")
      .is("deleted_at", null);

    if (profileError) {
      console.error("Error fetching user profiles:", profileError.message);
      process.exit(1);
    }

    if (!profiles || profiles.length === 0) {
      console.log("No user profiles found to update");
      return;
    }

    console.log(`Found ${profiles.length} profiles to update`);

    // Update each user's metadata
    let successCount = 0;
    let errorCount = 0;

    for (const profile of profiles) {
      try {
        // Get current user metadata first
        const { data: userData, error: userError } =
          await supabase.auth.admin.getUserById(profile.user_id);

        if (userError) {
          console.error(
            `Error fetching user ${profile.user_id}:`,
            userError.message
          );
          errorCount++;
          continue;
        }

        if (!userData || !userData.user) {
          console.error(`User ${profile.user_id} not found in auth system`);
          errorCount++;
          continue;
        }

        // Preserve existing metadata if any
        const currentMetadata = userData.user.user_metadata || {};

        // Update user metadata with role
        const { error: updateError } = await supabase.auth.admin.updateUserById(
          profile.user_id,
          {
            user_metadata: { ...currentMetadata, role: profile.role },
          }
        );

        if (updateError) {
          console.error(
            `Error updating metadata for user ${profile.user_id}:`,
            updateError.message
          );
          errorCount++;
          continue;
        }

        console.log(
          `✅ Updated role for user ${profile.user_id} to ${profile.role}`
        );
        successCount++;
      } catch (err) {
        console.error(
          `Unexpected error processing user ${profile.user_id}:`,
          err
        );
        errorCount++;
      }
    }

    console.log("\nUpdate complete!");
    console.log(`Successfully updated: ${successCount} users`);
    console.log(`Failed updates: ${errorCount} users`);

    if (errorCount > 0) {
      console.log(
        "\nSome updates failed. You may want to run the script again to retry."
      );
    }
  } catch (err) {
    console.error("Unexpected error during update process:", err);
    process.exit(1);
  }
}

// Run the update process
updateAllUserRoles();
