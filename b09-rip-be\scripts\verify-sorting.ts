// Load environment variables from .env.local
import { config } from "dotenv";
config({ path: ".env.local" });

import { createClient } from "@supabase/supabase-js";

// Initialize Supabase client with admin privileges
const supabaseUrl = process.env.supabase_url || "";
const supabaseServiceKey = process.env.supabase_service_role || "";

if (!supabaseUrl || !supabaseServiceKey) {
  console.error(
    "Error: supabase_url and supabase_service_role must be set in .env.local"
  );
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseServiceKey);

async function main() {
  console.log("Verifying sorting of attendance records...");

  try {
    // Fetch by created_at DESC order
    console.log("\nRecords ordered by created_at DESC:");
    const { data: createdAtDesc, error: error1 } = await supabase
      .from("attendances")
      .select("id, employee_id, date, status, created_at")
      .is("deleted_at", null)
      .order("created_at", { ascending: false })
      .limit(5);

    if (error1) {
      console.error("Query error:", error1.message);
    } else {
      console.table(
        createdAtDesc.map((record) => ({
          id: record.id.substring(0, 8),
          date: record.date,
          status: record.status,
          created_at: new Date(record.created_at).toISOString(),
        }))
      );
    }

    // Fetch by date DESC order
    console.log("\nRecords ordered by date DESC:");
    const { data: dateDesc, error: error2 } = await supabase
      .from("attendances")
      .select("id, employee_id, date, status, created_at")
      .is("deleted_at", null)
      .order("date", { ascending: false })
      .limit(5);

    if (error2) {
      console.error("Query error:", error2.message);
    } else {
      console.table(
        dateDesc.map((record) => ({
          id: record.id.substring(0, 8),
          date: record.date,
          status: record.status,
          created_at: new Date(record.created_at).toISOString(),
        }))
      );
    }

    // Fetch with no explicit order (should use our default created_at DESC)
    console.log(
      "\nRecords with no explicit order (should now use created_at DESC by default):"
    );
    const { data: noOrder, error: error3 } = await supabase
      .from("attendances")
      .select("id, employee_id, date, status, created_at")
      .is("deleted_at", null)
      .limit(5);

    if (error3) {
      console.error("Query error:", error3.message);
    } else {
      console.table(
        noOrder.map((record) => ({
          id: record.id.substring(0, 8),
          date: record.date,
          status: record.status,
          created_at: new Date(record.created_at).toISOString(),
        }))
      );

      // Compare the explicit created_at DESC and no order results
      // They should match if our default sorting is working
      if (createdAtDesc && noOrder) {
        const explicitIds = createdAtDesc.map((r) => r.id);
        const noOrderIds = noOrder.map((r) => r.id);

        console.log("\nVerification result:");
        const orderMatches =
          JSON.stringify(explicitIds) === JSON.stringify(noOrderIds);
        console.log(
          `Default order matches explicit created_at DESC: ${
            orderMatches ? "YES ✅" : "NO ❌"
          }`
        );

        if (!orderMatches) {
          console.log(
            "The records are returned in a different order, which means:"
          );
          console.log(
            "- The automatic sorting isn't applied at the database level"
          );
          console.log(
            "- Our change will be applied when using dbUtils.getAll() in the application code"
          );
        } else {
          console.log(
            "Great! The records appear to be sorted by created_at DESC by default."
          );
          console.log(
            "This confirms our change to the getAll function is affecting the query results."
          );
        }
      }
    }
  } catch (err) {
    console.error("Error verifying sorting:", err);
  }
}

main();
