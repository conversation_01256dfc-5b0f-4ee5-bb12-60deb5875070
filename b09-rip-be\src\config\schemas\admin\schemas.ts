// Define admin schema examples
export const adminExamples = {
  getUsersExample: {
    summary: "Example get users response",
    value: {
      success: true,
      message: "Users retrieved successfully",
      data: {
        items: [
          {
            id: "example-auth-user-id-12345",
            email: "<EMAIL>",
            profile: {
              id: "example-profile-id-12345",
              user_id: "example-auth-user-id-12345",
              fullname: "Example Admin User",
              phonenum: "1234567890",
              role: "Admin",
              org_id: null,
              employee_id: null,
              created_at: "2023-01-01T00:00:00.000Z",
              created_by: "example-creator-id-12345",
              updated_at: null,
              updated_by: null,
              deleted_at: null,
              deleted_by: null,
              is_active: true,
            },
          },
          {
            id: "example-auth-user-id-67890",
            email: "<EMAIL>",
            profile: {
              id: "example-profile-id-67890",
              user_id: "example-auth-user-id-67890",
              fullname: "Example Manager User",
              phonenum: "0987654321",
              role: "Manager",
              org_id: null,
              employee_id: "example-employee-id-67890",
              created_at: "2023-01-02T00:00:00.000Z",
              created_by: "example-creator-id-12345",
              updated_at: null,
              updated_by: null,
              deleted_at: null,
              deleted_by: null,
              is_active: false,
            },
          },
        ],
        pagination: {
          total: 6,
          page: 1,
          pageSize: 10,
          pageCount: 1,
        },
      },
    },
  },
  forbiddenErrorExample: {
    summary: "Example forbidden error response",
    value: {
      success: false,
      message: "Admin access required",
      data: null,
      error: {
        code: "INSUFFICIENT_ROLE",
        details: null,
      },
    },
  },
  activateUserExample: {
    summary: "Example activate user request",
    value: {
      id: "example-profile-id-67890", // Profile ID, not auth user_id
      org_id: "example-organization-id-12345", // Only required for Client role
    },
  },
  deleteUserExample: {
    summary: "Example delete user response",
    value: {
      success: true,
      message:
        "User profile deleted successfully. Auth record must be deleted separately.",
      data: {
        id: "example-profile-id-67890",
        deleted: true,
      },
    },
  },
};

// Define admin schema components
export const adminSchemas = {
  UserWithProfile: {
    type: "object" as const,
    description: "User with combined auth and profile data",
    properties: {
      id: {
        type: "string" as const,
        format: "uuid",
        description: "Auth user ID",
      },
      email: {
        type: "string" as const,
        format: "email",
        description: "User's email address from auth system",
      },
      profile: {
        type: "object" as const,
        description: "User profile data",
        $ref: "#/components/schemas/UserProfile",
      },
    },
  },
  UserProfile: {
    type: "object" as const,
    properties: {
      id: {
        type: "string" as const,
        format: "uuid",
        description: "Profile ID (primary key in user_profiles table)",
      },
      user_id: {
        type: "string" as const,
        format: "uuid",
        description: "Auth user ID (foreign key to auth.users table)",
      },
      fullname: {
        type: "string" as const,
      },
      phonenum: {
        type: "string" as const,
      },
      role: {
        type: "string" as const,
        enum: ["Admin", "Manager", "HR", "Finance", "Operation", "Client"],
      },
      org_id: {
        type: "string" as const,
        format: "uuid",
        nullable: true,
      },
      employee_id: {
        type: "string" as const,
        format: "uuid",
        nullable: true,
      },
      is_active: {
        type: "boolean" as const,
        default: false,
      },
      created_at: {
        type: "string" as const,
        format: "date-time",
      },
      created_by: {
        type: "string" as const,
        format: "uuid",
      },
      updated_at: {
        type: "string" as const,
        format: "date-time",
        nullable: true,
      },
      updated_by: {
        type: "string" as const,
        format: "uuid",
        nullable: true,
      },
      deleted_at: {
        type: "string" as const,
        format: "date-time",
        nullable: true,
      },
      deleted_by: {
        type: "string" as const,
        format: "uuid",
        nullable: true,
      },
    },
  },
  PaginationResult: {
    type: "object" as const,
    properties: {
      total: {
        type: "integer" as const,
        description: "Total number of items across all pages",
      },
      page: {
        type: "integer" as const,
        description: "Current page (1-based)",
      },
      pageSize: {
        type: "integer" as const,
        description: "Number of items per page",
      },
      pageCount: {
        type: "integer" as const,
        description: "Total number of pages",
      },
    },
  },
  ActivateUserRequest: {
    type: "object" as const,
    properties: {
      id: {
        type: "string" as const,
        format: "uuid",
        description:
          "Profile ID to activate (from user_profiles table, not auth user_id)",
      },
      org_id: {
        type: "string" as const,
        format: "uuid",
        description: "Organization ID (required for Client role)",
      },
    },
    required: ["id"],
  },
  GenericSuccessResponse: {
    type: "object" as const,
    properties: {
      success: {
        type: "boolean" as const,
        example: true,
      },
      message: {
        type: "string" as const,
      },
      data: {
        type: "object" as const,
      },
    },
  },
  GenericErrorResponse: {
    type: "object" as const,
    properties: {
      success: {
        type: "boolean" as const,
        example: false,
      },
      message: {
        type: "string" as const,
      },
      data: {
        type: "object" as const,
        nullable: true,
        example: null,
      },
      error: {
        type: "object" as const,
        properties: {
          code: {
            type: "string" as const,
          },
          details: {
            type: "object" as const,
            nullable: true,
          },
        },
      },
    },
  },
};
