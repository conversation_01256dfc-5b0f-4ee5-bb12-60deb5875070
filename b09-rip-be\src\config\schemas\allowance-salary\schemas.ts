import { AllowanceSalaryType } from "../../../database/models/allowance-salary.model";

export const allowanceSalaryExamples = {
  createAllowanceExample: {
    summary: "Example create allowance request",
    value: {
      salary_id: "123e4567-e89b-12d3-a456-426614174000",
      amount: 200000,
      allowance_type: AllowanceSalaryType.TRANSPORT,
      notes: "Transport allowance for May 2025",
    },
  },
  getAllowancesExample: {
    summary: "Example get allowances response",
    value: {
      success: true,
      message: "Allowances retrieved successfully",
      data: [
        {
          id: "223e4567-e89b-12d3-a456-426614174001",
          salary_id: "123e4567-e89b-12d3-a456-426614174000",
          amount: 200000,
          allowance_type: "transport",
          notes: "Transport allowance for May 2025",
          created_at: "2025-05-01T00:00:00.000Z",
          created_by: "auth0|123456789",
          updated_at: null,
          updated_by: null,
          deleted_at: null,
          deleted_by: null,
        },
        {
          id: "323e4567-e89b-12d3-a456-426614174002",
          salary_id: "123e4567-e89b-12d3-a456-426614174000",
          amount: 150000,
          allowance_type: "meal",
          notes: "Meal allowance for May 2025",
          created_at: "2025-05-01T00:00:00.000Z",
          created_by: "auth0|123456789",
          updated_at: null,
          updated_by: null,
          deleted_at: null,
          deleted_by: null,
        }
      ]
    },
  },
};

export const allowanceSalarySchemas = {
  AllowanceSalary: {
    type: "object" as const,
    required: [
      "id",
      "salary_id",
      "amount",
      "allowance_type",
      "created_at",
      "created_by",
    ],
    properties: {
      id: {
        type: "string" as const,
        format: "uuid",
        description: "Unique identifier for the allowance",
      },
      salary_id: {
        type: "string" as const,
        format: "uuid",
        description: "ID of the salary this allowance belongs to",
      },
      amount: {
        type: "number" as const,
        minimum: 0,
        description: "Allowance amount",
      },
      allowance_type: {
        type: "string" as const,
        enum: Object.values(AllowanceSalaryType),
        description: "Type of allowance",
      },
      notes: {
        type: "string" as const,
        nullable: true,
        description: "Additional notes about the allowance",
      },
      created_at: {
        type: "string" as const,
        format: "date-time",
        description: "Timestamp when the allowance was created",
      },
      created_by: {
        type: "string" as const,
        description: "ID of the user who created the allowance",
      },
      updated_at: {
        type: "string" as const,
        format: "date-time",
        nullable: true,
        description: "Timestamp when the allowance was last updated",
      },
      updated_by: {
        type: "string" as const,
        nullable: true,
        description: "ID of the user who last updated the allowance",
      },
      deleted_at: {
        type: "string" as const,
        format: "date-time",
        nullable: true,
        description: "Timestamp when the allowance was deleted",
      },
      deleted_by: {
        type: "string" as const,
        nullable: true,
        description: "ID of the user who deleted the allowance",
      },
    },
  },
  CreateAllowanceSalaryDto: {
    type: "object" as const,
    required: ["salary_id", "amount", "allowance_type"],
    properties: {
      salary_id: {
        type: "string" as const,
        format: "uuid",
        description: "ID of the salary this allowance belongs to",
      },
      amount: {
        type: "number" as const,
        minimum: 0,
        description: "Allowance amount",
      },
      allowance_type: {
        type: "string" as const,
        enum: Object.values(AllowanceSalaryType),
        description: "Type of allowance",
      },
      notes: {
        type: "string" as const,
        nullable: true,
        description: "Additional notes about the allowance",
      },
    },
  },
};
