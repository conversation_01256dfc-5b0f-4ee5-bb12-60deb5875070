export const attendanceSchemas = {
  Attendance: {
    type: "object" as const,
    properties: {
      id: {
        type: "string" as const,
        format: "uuid",
        description: "Unique attendance ID",
      },
      date: {
        type: "string" as const,
        format: "date",
        description: "Attendance date",
      },
      employee_id: { type: "string" as const, description: "Employee ID" },
      status: {
        type: "string" as const,
        enum: ["present", "absent", "permit", "leave"],
        description: "Presence status",
      },
      clock_in: {
        type: "string" as const,
        format: "time",
        description: "Clock-in time",
      },
      clock_out: {
        type: "string" as const,
        format: "time",
        description: "Clock-out time",
      },
      notes: {
        type: "string" as const,
        maxLength: 1000,
        description: "Additional notes",
      },
    },
  },

  // Create/Update Attendance Schema
  CreateAttendanceDto: {
    type: "object" as const,
    properties: {
      status: {
        type: "string" as const,
        enum: ["present", "absent", "permit", "leave"],
        description: "Presence status (present, absent, permit, leave)",
      },
      notes: {
        type: "string" as const,
        maxLength: 1000,
        description: "Optional notes about attendance",
      },
      tasks: {
        type: "array" as const,
        items: {
          type: "object" as const,
          properties: {
            description: {
              type: "string" as const,
              minLength: 3,
              maxLength: 255,
              description: "Task description",
            },
            due_date: {
              type: "string" as const,
              pattern: "^\\d{4}-\\d{2}-\\d{2}$",
              description: "Due date in format YYYY-MM-DD",
            },
            completion_status: {
              type: "boolean" as const,
              description:
                "Task completion status (true = completed, false = not completed)",
              default: false,
            },
          },
          required: ["description", "due_date"],
        },
        description: "Array of tasks to be created with attendance",
      },
    },
    required: ["status"],
  },

  // Employee Attendance Request Schema
  EmployeeAttendanceRequest: {
    type: "object" as const,
    properties: {
      employee_id: {
        type: "string" as const,
        description: "Employee ID",
      },
      fromDate: {
        type: "string" as const,
        pattern: "^\\d{4}-\\d{2}-\\d{2}$",
        description: "Start date in YYYY-MM-DD format",
      },
      toDate: {
        type: "string" as const,
        pattern: "^\\d{4}-\\d{2}-\\d{2}$",
        description: "End date in YYYY-MM-DD format",
      },
      status: {
        type: "string" as const,
        enum: ["present", "absent", "permit", "leave"],
        description: "Filter by attendance status",
      },
      search: {
        type: "string" as const,
        description: "Search term to find in attendance notes",
      },
      page: {
        type: "integer" as const,
        minimum: 1,
        default: 1,
        description: "Page number for pagination",
      },
      pageSize: {
        type: "integer" as const,
        minimum: 1,
        maximum: 100,
        default: 10,
        description: "Number of records per page",
      },
    },
    required: ["employee_id"],
  },

  // Employee Attendance Response Schema
  EmployeeAttendanceResponse: {
    type: "object" as const,
    properties: {
      success: { type: "boolean" as const, example: true },
      message: { type: "string" as const },
      data: {
        type: "object" as const,
        properties: {
          data: {
            type: "array" as const,
            items: { $ref: "#/components/schemas/AttendanceWithTasks" },
          },
          pagination: { $ref: "#/components/schemas/PaginationResult" },
        },
      },
    },
  },

  // Personal Attendance Response Schema
  PersonalAttendanceResponse: {
    type: "object" as const,
    properties: {
      success: { type: "boolean" as const, example: true },
      message: { type: "string" as const },
      data: {
        type: "object" as const,
        properties: {
          data: {
            type: "array" as const,
            items: { $ref: "#/components/schemas/AttendanceWithTasks" },
          },
          pagination: { $ref: "#/components/schemas/PaginationResult" },
        },
      },
    },
  },

  // Attendance with Tasks Schema
  AttendanceWithTasks: {
    type: "object" as const,
    properties: {
      id: { type: "string" as const },
      employee_id: { type: "string" as const },
      date: { type: "string" as const },
      clock_in: { type: "string" as const },
      clock_out: { type: "string" as const, nullable: true },
      status: { type: "string" as const },
      notes: { type: "string" as const, nullable: true },
      tasks: {
        type: "array" as const,
        items: {
          type: "object" as const,
          properties: {
            id: { type: "string" as const },
            description: { type: "string" as const },
            due_date: { type: "string" as const },
            completion_status: {
              type: "boolean" as const,
              description:
                "Task completion status (true = completed, false = not completed)",
            },
          },
        },
      },
    },
  },

  // Pagination Result Schema
  PaginationResult: {
    type: "object" as const,
    properties: {
      total: { type: "integer" as const },
      page: { type: "integer" as const },
      pageSize: { type: "integer" as const },
      totalPages: { type: "integer" as const },
    },
  },
};

export const attendanceExamples = {
  AttendanceExample: {
    summary: "Example attendance record",
    value: {
      id: "123e4567-e89b-12d3-a456-426614174000",
      date: "2024-03-14",
      employee_id: "EMP001",
      status: "present",
      clock_in: "08:00:00",
      clock_out: "17:00:00",
      notes: "Attended meeting at 10 AM",
    },
  },

  createAttendanceExample: {
    summary: "Clock-in with tasks example",
    value: {
      status: "present",
      notes: "Working on the new feature",
      tasks: [
        {
          description: "Implement authentication system",
          due_date: "2023-12-31",
          completion_status: false,
        },
      ],
    },
  },

  clockOutExample: {
    summary: "Clock-out with updated notes example",
    value: {
      status: "present",
      notes: "Completed work on new feature",
      tasks: [
        {
          description: "Review code changes",
          due_date: "2023-12-31",
          completion_status: true,
        },
      ],
    },
  },

  clockInResponseExample: {
    summary: "Successful clock-in response",
    value: {
      success: true,
      message: "Attendance created with clock-in time successfully",
      data: {
        id: "123e4567-e89b-12d3-a456-426614174000",
        date: "2023-12-15",
        employee_id: "emp-123",
        status: "present",
        clock_in: "09:00:00",
        clock_out: null,
        notes: "Working on the new feature",
        created_at: "2023-12-15T02:00:00.000Z",
        updated_at: "2023-12-15T02:00:00.000Z",
        deleted_at: null,
        created_by: "user-123",
        updated_by: "user-123",
      },
    },
  },

  clockOutResponseExample: {
    summary: "Successful clock-out response",
    value: {
      success: true,
      message: "Attendance updated with clock-out time successfully",
      data: {
        id: "123e4567-e89b-12d3-a456-426614174000",
        date: "2023-12-15",
        employee_id: "emp-123",
        status: "present",
        clock_in: "09:00:00",
        clock_out: "17:00:00",
        notes: "Completed work on new feature",
        created_at: "2023-12-15T02:00:00.000Z",
        updated_at: "2023-12-15T10:00:00.000Z",
        deleted_at: null,
        created_by: "user-123",
        updated_by: "user-123",
      },
    },
  },

  existingAttendanceExample: {
    summary: "Existing attendance record",
    value: {
      success: true,
      message: "Today's attendance record retrieved successfully",
      data: {
        id: "123e4567-e89b-12d3-a456-426614174000",
        date: "2023-12-15",
        employee_id: "emp-123",
        status: "present",
        clock_in: "09:00:00",
        clock_out: null,
        notes: "Working on the new feature",
        created_at: "2023-12-15T02:00:00.000Z",
        updated_at: "2023-12-15T02:00:00.000Z",
        deleted_at: null,
        created_by: "user-123",
        updated_by: "user-123",
        tasks: [
          {
            id: "task-123",
            attendance_id: "123e4567-e89b-12d3-a456-426614174000",
            description: "Implement authentication system",
            completion_status: false,
            employee_id: "emp-123",
            due_date: "2023-12-31",
            created_at: "2023-12-15T02:00:00.000Z",
            updated_at: "2023-12-15T02:00:00.000Z",
            deleted_at: null,
            created_by: "user-123",
            updated_by: "user-123",
          },
        ],
      },
    },
  },

  notFoundExample: {
    summary: "No attendance found",
    value: {
      success: false,
      message: "No attendance record found for today",
      data: null,
      error: { code: "NOT_FOUND" },
    },
  },

  missingEmployeeIdExample: {
    summary: "Missing employee ID error",
    value: {
      success: false,
      message: "Employee ID not found in user profile",
      data: null,
      error: { code: "BAD_REQUEST" },
    },
  },

  unauthorizedExample: {
    summary: "Authentication required error",
    value: {
      success: false,
      message: "Authentication required",
      data: null,
      error: { code: "AUTH_REQUIRED" },
    },
  },

  forbiddenExample: {
    summary: "Insufficient permissions error",
    value: {
      success: false,
      message: "You do not have permission to access this resource",
      data: null,
      error: { code: "INSUFFICIENT_ROLE" },
    },
  },

  serverErrorExample: {
    summary: "Server error",
    value: {
      success: false,
      message: "Unexpected error occurred",
      data: null,
      error: { code: "INTERNAL_SERVER_ERROR" },
    },
  },

  attendanceListExample: {
    summary: "Paginated attendance list with tasks",
    value: {
      success: true,
      message: "Attendances fetched successfully",
      data: {
        data: [
          {
            id: "123e4567-e89b-12d3-a456-426614174000",
            date: "2023-12-15",
            employee_id: "emp-123",
            status: "present",
            clock_in: "09:00:00",
            clock_out: "17:00:00",
            notes: "Completed work on new feature",
            created_at: "2023-12-15T02:00:00.000Z",
            updated_at: "2023-12-15T10:00:00.000Z",
            deleted_at: null,
            created_by: "user-123",
            updated_by: "user-123",
            tasks: [
              {
                id: "task-123",
                attendance_id: "123e4567-e89b-12d3-a456-426614174000",
                description: "Implement authentication system",
                completion_status: true,
                employee_id: "emp-123",
                due_date: "2023-12-31",
                created_at: "2023-12-15T02:00:00.000Z",
                updated_at: "2023-12-15T10:00:00.000Z",
                deleted_at: null,
                created_by: "user-123",
                updated_by: "user-123",
              },
            ],
          },
          {
            id: "223e4567-e89b-12d3-a456-426614174001",
            date: "2023-12-14",
            employee_id: "emp-123",
            status: "present",
            clock_in: "09:30:00",
            clock_out: "18:00:00",
            notes: "Team meeting and planning",
            created_at: "2023-12-14T02:30:00.000Z",
            updated_at: "2023-12-14T11:00:00.000Z",
            deleted_at: null,
            created_by: "user-123",
            updated_by: "user-123",
            tasks: [],
          },
        ],
        pagination: {
          total: 2,
          page: 1,
          pageSize: 10,
          totalPages: 1,
        },
      },
    },
  },

  emptyAttendanceListExample: {
    summary: "Empty attendance list",
    value: {
      success: true,
      message: "Attendances fetched successfully",
      data: {
        data: [],
        pagination: {
          total: 0,
          page: 1,
          pageSize: 10,
          totalPages: 0,
        },
      },
    },
  },

  invalidDateFormatExample: {
    summary: "Invalid date format",
    value: {
      success: false,
      message: "Invalid date format. Date should be in YYYY-MM-DD format",
      data: null,
      error: { code: "BAD_REQUEST" },
    },
  },

  invalidDateRangeExample: {
    summary: "Invalid date range",
    value: {
      success: false,
      message: "fromDate must be before or equal to toDate",
      data: null,
      error: { code: "BAD_REQUEST" },
    },
  },

  employeeAttendanceExample: {
    summary: "Employee attendance lookup example",
    value: {
      employee_id: "EMP001",
      fromDate: "2023-01-01",
      toDate: "2023-12-31",
      page: 1,
      pageSize: 10,
    },
  },

  invalidEmployeeIdExample: {
    summary: "Invalid employee ID",
    value: {
      success: false,
      message: "Employee ID is required",
      data: null,
      error: { code: "BAD_REQUEST" },
    },
  },
};
