// Define auth schema examples
export const authExamples = {
  signUpExample: {
    summary: "Example sign-up request",
    value: {
      email: "<EMAIL>",
      password: "securepassword123",
      fullname: "<PERSON>",
      phonenum: "6281234567890",
      role: "Client",
    },
  },
  signInExample: {
    summary: "Example sign-in request",
    value: {
      email: "<EMAIL>",
      password: "securepassword123",
    },
  },
  refreshTokenExample: {
    summary: "Example refresh token request",
    value: {
      refresh_token: "yHVhQTC553L3oTBsEGsDHQ",
    },
  },
};

// Define auth schema components with proper type assertion for OpenAPI
export const authSchemas = {
  SignUpRequest: {
    type: "object" as const,
    properties: {
      email: {
        type: "string" as const,
        format: "email",
        example: "<EMAIL>",
      },
      password: {
        type: "string" as const,
        minLength: 8,
        example: "securepassword123",
      },
      fullname: {
        type: "string" as const,
        example: "<PERSON>",
      },
      phonenum: {
        type: "string" as const,
        example: "6281234567890",
      },
      role: {
        type: "string" as const,
        enum: ["Admin", "Manager", "HR", "Finance", "Operation", "Client"],
        example: "Client",
      },
    },
    required: ["email", "password", "fullname", "phonenum", "role"],
  },
  SignInRequest: {
    type: "object" as const,
    properties: {
      email: {
        type: "string" as const,
        format: "email",
        example: "<EMAIL>",
      },
      password: {
        type: "string" as const,
        minLength: 8,
        example: "securepassword123",
      },
    },
    required: ["email", "password"],
  },
  RefreshTokenRequest: {
    type: "object" as const,
    properties: {
      refresh_token: {
        type: "string" as const,
        example: "yHVhQTC553L3oTBsEGsDHQ",
        description: "Refresh token used to get a new access token",
      },
    },
    required: ["refresh_token"],
  },
};
