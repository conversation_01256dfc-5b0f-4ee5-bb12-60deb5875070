import { BonusSalaryType } from "../../../database/models/bonus-salary.model";

export const bonusSalaryExamples = {
  createBonusExample: {
    summary: "Example create bonus request",
    value: {
      salary_id: "123e4567-e89b-12d3-a456-426614174000",
      amount: 500000,
      bonus_type: BonusSalaryType.KPI,
      notes: "KPI achievement bonus for Q2 2025",
      kpi_id: "223e4567-e89b-12d3-a456-426614174001",
      project_id: null
    },
  },
  getBonusesExample: {
    summary: "Example get bonuses response",
    value: {
      success: true,
      message: "Bonuses retrieved successfully",
      data: [
        {
          id: "323e4567-e89b-12d3-a456-426614174002",
          salary_id: "123e4567-e89b-12d3-a456-426614174000",
          amount: 500000,
          bonus_type: "kpi",
          notes: "KPI achievement bonus for Q2 2025",
          kpi_id: "223e4567-e89b-12d3-a456-426614174001",
          project_id: null,
          created_at: "2025-05-01T00:00:00.000Z",
          created_by: "auth0|123456789",
          updated_at: null,
          updated_by: null,
          deleted_at: null,
          deleted_by: null,
        },
        {
          id: "423e4567-e89b-12d3-a456-426614174003",
          salary_id: "123e4567-e89b-12d3-a456-426614174000",
          amount: 300000,
          bonus_type: "project",
          notes: "Project completion bonus",
          kpi_id: null,
          project_id: "523e4567-e89b-12d3-a456-426614174004",
          created_at: "2025-05-01T00:00:00.000Z",
          created_by: "auth0|123456789",
          updated_at: null,
          updated_by: null,
          deleted_at: null,
          deleted_by: null,
        }
      ]
    },
  },
};

export const bonusSalarySchemas = {
  BonusSalary: {
    type: "object" as const,
    required: [
      "id",
      "salary_id",
      "amount",
      "bonus_type",
      "created_at",
      "created_by",
    ],
    properties: {
      id: {
        type: "string" as const,
        format: "uuid",
        description: "Unique identifier for the bonus",
      },
      salary_id: {
        type: "string" as const,
        format: "uuid",
        description: "ID of the salary this bonus belongs to",
      },
      amount: {
        type: "number" as const,
        minimum: 0,
        description: "Bonus amount",
      },
      bonus_type: {
        type: "string" as const,
        enum: Object.values(BonusSalaryType),
        description: "Type of bonus",
      },
      notes: {
        type: "string" as const,
        nullable: true,
        description: "Additional notes about the bonus",
      },
      kpi_id: {
        type: "string" as const,
        format: "uuid",
        nullable: true,
        description: "ID of the KPI record (required for KPI type bonuses)",
      },
      project_id: {
        type: "string" as const,
        format: "uuid",
        nullable: true,
        description: "ID of the project (for project type bonuses)",
      },
      created_at: {
        type: "string" as const,
        format: "date-time",
        description: "Timestamp when the bonus was created",
      },
      created_by: {
        type: "string" as const,
        description: "ID of the user who created the bonus",
      },
      updated_at: {
        type: "string" as const,
        format: "date-time",
        nullable: true,
        description: "Timestamp when the bonus was last updated",
      },
      updated_by: {
        type: "string" as const,
        nullable: true,
        description: "ID of the user who last updated the bonus",
      },
      deleted_at: {
        type: "string" as const,
        format: "date-time",
        nullable: true,
        description: "Timestamp when the bonus was deleted",
      },
      deleted_by: {
        type: "string" as const,
        nullable: true,
        description: "ID of the user who deleted the bonus",
      },
    },
  },
  CreateBonusSalaryDto: {
    type: "object" as const,
    required: ["salary_id", "amount", "bonus_type"],
    properties: {
      salary_id: {
        type: "string" as const,
        format: "uuid",
        description: "ID of the salary this bonus belongs to",
      },
      amount: {
        type: "number" as const,
        minimum: 0,
        description: "Bonus amount",
      },
      bonus_type: {
        type: "string" as const,
        enum: Object.values(BonusSalaryType),
        description: "Type of bonus",
      },
      notes: {
        type: "string" as const,
        nullable: true,
        description: "Additional notes about the bonus",
      },
      kpi_id: {
        type: "string" as const,
        format: "uuid",
        nullable: true,
        description: "ID of the KPI record (required for KPI type bonuses)",
      },
      project_id: {
        type: "string" as const,
        format: "uuid",
        nullable: true,
        description: "ID of the project (for project type bonuses)",
      },
    },
  },
};
