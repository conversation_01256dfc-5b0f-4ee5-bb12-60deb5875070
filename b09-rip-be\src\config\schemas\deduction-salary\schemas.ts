import { DeductionType } from "../../../database/models/deduction-salary.model";

// Define module schemas for Swagger documentation
export const deductionSalarySchemas = {
  DeductionSalary: {
    type: "object" as const,
    required: ["id", "salary_id", "amount", "deduction_type", "created_at", "created_by"],
    properties: {
      id: {
        type: "string" as const,
        format: "uuid",
        description: "Unique identifier",
      },
      salary_id: {
        type: "string" as const,
        format: "uuid",
        description: "ID of the associated salary record",
      },
      amount: {
        type: "number" as const,
        description: "Amount of the deduction",
      },
      deduction_type: {
        type: "string" as const,
        enum: Object.values(DeductionType),
        description: "Type of deduction",
      },
      notes: {
        type: "string" as const,
        description: "Additional notes about the deduction",
      },
      created_at: {
        type: "string" as const,
        format: "date-time",
        description: "Timestamp when the deduction was created",
      },
      created_by: {
        type: "string" as const,
        format: "uuid",
        description: "ID of the user who created the deduction",
      },
      updated_at: {
        type: "string" as const,
        format: "date-time",
        nullable: true,
        description: "Timestamp when the deduction was last updated",
      },
      updated_by: {
        type: "string" as const,
        format: "uuid",
        nullable: true,
        description: "ID of the user who last updated the deduction",
      },
      deleted_at: {
        type: "string" as const,
        format: "date-time",
        nullable: true,
        description: "Timestamp when the deduction was deleted",
      },
      deleted_by: {
        type: "string" as const,
        format: "uuid",
        nullable: true,
        description: "ID of the user who deleted the deduction",
      },
    },
  },
  CreateDeductionSalaryDto: {
    type: "object" as const,
    required: ["salary_id", "amount", "deduction_type"],
    properties: {
      salary_id: {
        type: "string" as const,
        format: "uuid",
        description: "ID of the associated salary record",
      },
      amount: {
        type: "number" as const,
        description: "Amount of the deduction",
      },
      deduction_type: {
        type: "string" as const,
        enum: Object.values(DeductionType),
        description: "Type of deduction",
      },
      notes: {
        type: "string" as const,
        description: "Additional notes about the deduction",
      },
    },
  },
  UpdateDeductionSalaryDto: {
    type: "object" as const,
    properties: {
      amount: {
        type: "number" as const,
        description: "Amount of the deduction",
      },
      deduction_type: {
        type: "string" as const,
        enum: Object.values(DeductionType),
        description: "Type of deduction",
      },
      notes: {
        type: "string" as const,
        description: "Additional notes about the deduction",
      },
    },
  },
  DeleteDeductionSalaryResponse: {
    type: "object" as const,
    required: ["success", "message", "data"],
    properties: {
      success: {
        type: "boolean" as const,
        description: "Whether the operation was successful",
      },
      message: {
        type: "string" as const,
        description: "Success or error message",
      },
      data: {
        type: "object" as const,
        required: ["id"],
        properties: {
          id: {
            type: "string" as const,
            format: "uuid",
            description: "ID of the deleted deduction",
          },
        },
      },
    },
  },
};

export const deductionSalaryExamples = {
  createDeductionSalaryExample: {
    summary: "Example create deduction salary request",
    value: {
      salary_id: "123e4567-e89b-12d3-a456-************",
      amount: 500000,
      deduction_type: DeductionType.ABSENCE,
      notes: "Deduction for 2 days of absence",
    },
  },
  createDeductionSalaryResponseExample: {
    summary: "Example create deduction salary response",
    value: {
      success: true,
      message: "Deduction created successfully",
      data: {
        id: "323e4567-e89b-12d3-a456-************",
        salary_id: "123e4567-e89b-12d3-a456-************",
        amount: 500000,
        deduction_type: "absence",
        notes: "Deduction for 2 days of absence",
        created_at: "2024-12-01T00:00:00.000Z",
        created_by: "auth0|123456789",
        updated_at: null,
        updated_by: null,
        deleted_at: null,
        deleted_by: null,
      },
    },
  },
  getDeductionSalaryByIdExample: {
    summary: "Example get deduction salary by ID response",
    value: {
      success: true,
      message: "Deduction retrieved successfully",
      data: {
        id: "323e4567-e89b-12d3-a456-************",
        salary_id: "123e4567-e89b-12d3-a456-************",
        amount: 500000,
        deduction_type: "absence",
        notes: "Deduction for 2 days of absence",
        created_at: "2024-12-01T00:00:00.000Z",
        created_by: "auth0|123456789",
        updated_at: null,
        updated_by: null,
        deleted_at: null,
        deleted_by: null,
      },
    },
  },
  getDeductionSalaryBySalaryIdExample: {
    summary: "Example get deduction salary by salary ID response",
    value: {
      success: true,
      message: "Deductions retrieved successfully",
      data: [
        {
          id: "323e4567-e89b-12d3-a456-************",
          salary_id: "123e4567-e89b-12d3-a456-************",
          amount: 500000,
          deduction_type: "absence",
          notes: "Deduction for 2 days of absence",
          created_at: "2024-12-01T00:00:00.000Z",
          created_by: "auth0|123456789",
          updated_at: null,
          updated_by: null,
          deleted_at: null,
          deleted_by: null,
        },
        {
          id: "423e4567-e89b-12d3-a456-426614174003",
          salary_id: "123e4567-e89b-12d3-a456-************",
          amount: 100000,
          deduction_type: "lateness",
          notes: "Deduction for 3 times lateness",
          created_at: "2024-12-01T00:00:00.000Z",
          created_by: "auth0|123456789",
          updated_at: null,
          updated_by: null,
          deleted_at: null,
          deleted_by: null,
        },
      ],
    },
  },
  updateDeductionSalaryExample: {
    summary: "Example update deduction salary request",
    value: {
      amount: 600000,
      deduction_type: DeductionType.ABSENCE,
      notes: "Updated: Deduction for 3 days of absence",
    },
  },
  updateDeductionSalaryResponseExample: {
    summary: "Example update deduction salary response",
    value: {
      success: true,
      message: "Deduction updated successfully",
      data: {
        id: "323e4567-e89b-12d3-a456-************",
        salary_id: "123e4567-e89b-12d3-a456-************",
        amount: 600000,
        deduction_type: "absence",
        notes: "Updated: Deduction for 3 days of absence",
        created_at: "2024-12-01T00:00:00.000Z",
        created_by: "auth0|123456789",
        updated_at: "2024-12-02T00:00:00.000Z",
        updated_by: "auth0|987654321",
        deleted_at: null,
        deleted_by: null,
      },
    },
  },
  deleteDeductionSalaryResponseExample: {
    summary: "Example delete deduction salary response",
    value: {
      success: true,
      message: "Deduction deleted successfully",
      data: {
        id: "323e4567-e89b-12d3-a456-************",
      },
    },
  },
  errorResponseExample: {
    summary: "Example error response",
    value: {
      success: false,
      message: "Deduction not found",
      error: "No deduction found with the provided ID",
    },
  },
  validationErrorExample: {
    summary: "Example validation error response",
    value: {
      success: false,
      message: "Validation error",
      error: "Missing required fields: salary_id, amount, deduction_type",
    },
  },
  unauthorizedErrorExample: {
    summary: "Example unauthorized error response",
    value: {
      success: false,
      message: "Unauthorized",
      error: "Only Managers and Staff Operations can perform this action",
    },
  },
};
