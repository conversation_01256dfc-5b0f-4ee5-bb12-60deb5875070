import {
  Employee,
  EmploymentStatus,
} from "../../../database/models/employee.model";
import { PresenceStatus } from "../../../database/models/attendance.model";
import { UserRole } from "../../../database/models/user-profile.model";

// Example employee data
export const employeeExamples = {
  getAllEmployeesExample: {
    summary: "Example get all employees response with pagination",
    value: {
      success: true,
      message: "Employees retrieved successfully",
      data: {
        items: [
          {
            id: "123e4567-e89b-12d3-a456-************",
            profile_id: "223e4567-e89b-12d3-a456-************",
            dob: "1990-01-01",
            address: "Jl. Contoh No. 123, Jakarta",
            bank_account: "*********0",
            bank_name: "Bank ABC",
            employment_status: EmploymentStatus.FULLTIME,
            presence_status: PresenceStatus.PRESENT,
            department: UserRole.HR,
            start_date: "2024-01-01",
            salary_id: null,
            created_at: "2024-01-01T00:00:00.000Z",
            created_by: "auth0|*********",
            updated_at: null,
            updated_by: null,
            deleted_at: null,
            deleted_by: null,
            user_profiles: {
              fullname: "<PERSON> Doe",
              email: "<EMAIL>",
              phonenum: "08*********0",
            },
          },
        ],
        pagination: {
          total: 1,
          page: 1,
          pageSize: 10,
        },
      },
    },
  },
  getEmployeeExample: {
    summary: "Example get employee response",
    value: {
      success: true,
      message: "Employee retrieved successfully",
      data: {
        id: "123e4567-e89b-12d3-a456-************",
        profile_id: "223e4567-e89b-12d3-a456-************",
        dob: "1990-01-01",
        address: "Jl. Contoh No. 123, Jakarta",
        bank_account: "*********0",
        bank_name: "Bank ABC",
        employment_status: EmploymentStatus.FULLTIME,
        presence_status: PresenceStatus.PRESENT,
        department: UserRole.HR,
        start_date: "2024-01-01",
        salary_id: null,
        created_at: "2024-01-01T00:00:00.000Z",
        created_by: "auth0|*********",
        updated_at: null,
        updated_by: null,
        deleted_at: null,
        deleted_by: null,
        user_profiles: {
          fullname: "John Doe",
          email: "<EMAIL>",
          phonenum: "08*********0",
        },
      },
    },
  },
  updateEmployeeExample: {
    summary: "Example update employee request",
    value: {
      dob: "1990-01-01",
      address: "Jl. Contoh No. 123, Jakarta",
      bank_account: "*********0",
      bank_name: "Bank ABC",
      employment_status: EmploymentStatus.FULLTIME,
      presence_status: PresenceStatus.PRESENT,
      department: UserRole.HR,
      start_date: "2024-01-01",
      salary_id: null,
    },
  },
  updateEmployeeResponseExample: {
    summary: "Example update employee response",
    value: {
      success: true,
      message: "Employee updated successfully",
      data: {
        id: "123e4567-e89b-12d3-a456-************",
        profile_id: "223e4567-e89b-12d3-a456-************",
        dob: "1990-01-01",
        address: "Jl. Contoh No. 123, Jakarta",
        bank_account: "*********0",
        bank_name: "Bank ABC",
        employment_status: EmploymentStatus.FULLTIME,
        presence_status: PresenceStatus.PRESENT,
        department: UserRole.HR,
        start_date: "2024-01-01",
        salary_id: null,
        created_at: "2024-01-01T00:00:00.000Z",
        created_by: "auth0|*********",
        updated_at: "2024-03-15T00:00:00.000Z",
        updated_by: "auth0|*********",
        deleted_at: null,
        deleted_by: null,
      },
    },
  },
  errorResponseExample: {
    summary: "Example error response",
    value: {
      success: false,
      message: "Employee not found",
      data: null,
      error: {
        code: "NOT_FOUND",
        details: null,
      },
    },
  },
};

// Schema definitions for OpenAPI documentation
export const employeeSchemas = {
  Employee: {
    type: "object" as const,
    properties: {
      id: {
        type: "string" as const,
        format: "uuid",
        description: "Unique identifier for the employee",
      },
      profile_id: {
        type: "string" as const,
        format: "uuid",
        description: "The ID of the user profile associated with this employee",
      },
      dob: {
        type: "string" as const,
        format: "date",
        description: "Date of birth (YYYY-MM-DD)",
      },
      address: {
        type: "string" as const,
        minLength: 1,
        maxLength: 255,
        description: "Complete address of the employee",
      },
      bank_account: {
        type: "string" as const,
        minLength: 1,
        maxLength: 50,
        description: "Bank account number",
      },
      bank_name: {
        type: "string" as const,
        minLength: 1,
        maxLength: 50,
        description: "Name of the bank",
      },
      employment_status: {
        type: "string" as const,
        enum: Object.values(EmploymentStatus),
        description: "Employment status (Intern/Fulltime/Not provided)",
      },
      presence_status: {
        type: "string" as const,
        enum: Object.values(PresenceStatus),
        description: "Current presence status",
      },
      department: {
        type: "string" as const,
        enum: Object.values(UserRole),
        description: "Department/role of the employee",
      },
      start_date: {
        type: "string" as const,
        format: "date",
        description: "Employment start date (YYYY-MM-DD)",
      },
      salary_id: {
        type: "string" as const,
        format: "uuid",
        nullable: true,
        description: "The ID of the salary record for this employee",
      },
      created_at: {
        type: "string" as const,
        format: "date-time",
        description: "Creation timestamp",
      },
      created_by: {
        type: "string" as const,
        description: "ID of the user who created the record",
      },
      updated_at: {
        type: "string" as const,
        format: "date-time",
        nullable: true,
        description: "Last update timestamp",
      },
      updated_by: {
        type: "string" as const,
        nullable: true,
        description: "ID of the user who last updated the record",
      },
      deleted_at: {
        type: "string" as const,
        format: "date-time",
        nullable: true,
        description: "Deletion timestamp",
      },
      deleted_by: {
        type: "string" as const,
        nullable: true,
        description: "ID of the user who deleted the record",
      },
      user_profiles: {
        type: "object" as const,
        properties: {
          fullname: {
            type: "string" as const,
            description: "Full name of the employee",
          },
          email: {
            type: "string" as const,
            format: "email",
            description: "Email address of the employee",
          },
          phonenum: {
            type: "string" as const,
            description: "Phone number of the employee",
          },
        },
      },
    },
  },
  GetAllEmployeesQuery: {
    type: "object" as const,
    properties: {
      search: {
        type: "string" as const,
        description: "Search term to filter employees by name, email, or phone",
      },
      page: {
        type: "number" as const,
        minimum: 1,
        description: "Page number for pagination (starts at 1)",
      },
      pageSize: {
        type: "number" as const,
        minimum: 1,
        maximum: 100,
        description: "Number of items per page",
      },
      department: {
        type: "string" as const,
        enum: Object.values(UserRole),
        description: "Filter by department/role",
      },
      employment_status: {
        type: "string" as const,
        enum: Object.values(EmploymentStatus),
        description: "Filter by employment status",
      },
      presence_status: {
        type: "string" as const,
        enum: Object.values(PresenceStatus),
        description: "Filter by presence status",
      },
    },
  },
  UpdateEmployeeDto: {
    type: "object" as const,
    properties: {
      dob: {
        type: "string" as const,
        format: "date",
        description: "Date of birth (YYYY-MM-DD)",
      },
      address: {
        type: "string" as const,
        minLength: 1,
        maxLength: 255,
        description: "Complete address of the employee",
      },
      bank_account: {
        type: "string" as const,
        minLength: 1,
        maxLength: 50,
        description: "Bank account number",
      },
      bank_name: {
        type: "string" as const,
        minLength: 1,
        maxLength: 50,
        description: "Name of the bank",
      },
      employment_status: {
        type: "string" as const,
        enum: Object.values(EmploymentStatus),
        description: "Employment status (Intern/Fulltime/Not provided)",
      },
      presence_status: {
        type: "string" as const,
        enum: Object.values(PresenceStatus),
        description: "Current presence status",
      },
      department: {
        type: "string" as const,
        enum: Object.values(UserRole),
        description: "Department/role of the employee",
      },
      start_date: {
        type: "string" as const,
        format: "date",
        description: "Employment start date (YYYY-MM-DD)",
      },
      salary_id: {
        type: "string" as const,
        format: "uuid",
        nullable: true,
        description: "The ID of the salary record for this employee",
      },
    },
  },
  PaginationResult: {
    type: "object" as const,
    properties: {
      total: {
        type: "number" as const,
        description: "Total number of records",
      },
      page: {
        type: "number" as const,
        description: "Current page number",
      },
      pageSize: {
        type: "number" as const,
        description: "Number of items per page",
      },
    },
  },
};
