import { UserRole } from "../../../database/models/user-profile.model";

export const invoicePaymentProofExamples = {
  // GET examples
  getPaymentProofResponseExample: {
    summary: "Example get payment proof response",
    value: {
      success: true,
      message: "Payment proofs retrieved successfully",
      data: {
        items: [
          {
            id: "123e4567-e89b-12d3-a456-426614174000",
            invoice_id: "123e4567-e89b-12d3-a456-426614174001",
            file_path: "123e4567-e89b-12d3-a456-426614174001/1620000000000_payment_receipt.pdf",
            file_name: "payment_receipt.pdf",
            file_type: "application/pdf",
            file_size: 1024000,
            notes: "Payment receipt for invoice #001/ORDEV/Kasuat/III/2025",
            download_url: "https://example.com/download/signed-url",
            created_at: "2025-03-01T00:00:00.000Z",
            created_by: "auth0|123456789",
            updated_at: null,
            updated_by: null,
            deleted_at: null,
            deleted_by: null,
          }
        ],
        pagination: {
          page: 1,
          pageSize: 10,
          totalItems: 1,
          totalPages: 1
        }
      },
    },
  },
  
  // POST examples
  createPaymentProofExample: {
    summary: "Example create payment proof request",
    value: {
      notes: "Payment receipt for invoice #001/ORDEV/Kasuat/III/2025",
      // Note: The file is sent as a multipart form data
    },
  },
  
  createPaymentProofResponseExample: {
    summary: "Example create payment proof response",
    value: {
      success: true,
      message: "Payment proof uploaded successfully",
      data: {
        id: "123e4567-e89b-12d3-a456-426614174000",
        invoice_id: "123e4567-e89b-12d3-a456-426614174001",
        file_path: "123e4567-e89b-12d3-a456-426614174001/1620000000000_payment_receipt.pdf",
        file_name: "payment_receipt.pdf",
        file_type: "application/pdf",
        file_size: 1024000,
        notes: "Payment receipt for invoice #001/ORDEV/Kasuat/III/2025",
        created_at: "2025-03-01T00:00:00.000Z",
        created_by: "auth0|123456789",
        updated_at: null,
        updated_by: null,
        deleted_at: null,
        deleted_by: null,
      },
    },
  },
  
  // DELETE examples
  deletePaymentProofResponseExample: {
    summary: "Example delete payment proof response",
    value: {
      success: true,
      message: "Payment proof deleted successfully",
      data: null,
    },
  },
};

export const invoicePaymentProofSchemas = {
  InvoicePaymentProof: {
    type: "object" as const,
    properties: {
      id: {
        type: "string" as const,
        format: "uuid",
        description: "Unique identifier for the payment proof",
      },
      invoice_id: {
        type: "string" as const,
        format: "uuid",
        description: "ID of the invoice this payment proof belongs to",
      },
      file_path: {
        type: "string" as const,
        description: "Path to the file in storage",
      },
      file_name: {
        type: "string" as const,
        description: "Name of the uploaded file",
      },
      file_type: {
        type: "string" as const,
        description: "MIME type of the file",
      },
      file_size: {
        type: "number" as const,
        description: "Size of the file in bytes",
      },
      notes: {
        type: "string" as const,
        description: "Additional notes about the payment proof",
      },
      download_url: {
        type: "string" as const,
        description: "Signed URL for downloading the file",
      },
      created_at: {
        type: "string" as const,
        format: "date-time",
        description: "When the record was created",
      },
      created_by: {
        type: "string" as const,
        description: "User ID who created the record",
      },
      updated_at: {
        type: "string" as const,
        format: "date-time",
        nullable: true,
        description: "When the record was last updated",
      },
      updated_by: {
        type: "string" as const,
        nullable: true,
        description: "User ID who last updated the record",
      },
      deleted_at: {
        type: "string" as const,
        format: "date-time",
        nullable: true,
        description: "When the record was soft deleted",
      },
      deleted_by: {
        type: "string" as const,
        nullable: true,
        description: "User ID who soft deleted the record",
      },
    },
  },
};
