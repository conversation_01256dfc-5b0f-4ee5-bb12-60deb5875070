import {
  InvoiceType,
  PaymentMethod,
  PaymentStatus,
  ServiceType,
} from "../../../database/models/invoice.model";

// Examples for invoice operations
export const invoiceExamples = {
  // GET examples
  getInvoiceResponseExample: {
    summary: "Example get invoice response",
    value: {
      success: true,
      message: "Invoice retrieved successfully",
      data: {
        id: "123e4567-e89b-12d3-a456-************",
        invoice_number: "001/ORDEV/Kasuat/III/2025",
        invoice_type: InvoiceType.EXTERNAL,
        service_type: ServiceType.ORDEV,
        recipient_name: "Acme Corporation",
        project_id: "project_id_123",
        project_name: "Website Redesign",
        due_date: "2025-04-15",
        payment_method: PaymentMethod.BANK_TRANSFER,
        payment_status: PaymentStatus.PENDING,
        notes: "Please pay within 30 days",
        total_amount: 2000,
        created_at: "2025-03-01T00:00:00.000Z",
        created_by: "auth0|*********",
        updated_at: null,
        updated_by: null,
        deleted_at: null,
        deleted_by: null,
        items: [
          {
            id: "223e4567-e89b-12d3-a456-************",
            invoice_id: "123e4567-e89b-12d3-a456-************",
            item_name: "Web Design",
            item_amount: 1,
            item_price: 1500,
            total_price: 1500,
            created_at: "2025-03-01T00:00:00.000Z",
            created_by: "auth0|*********",
            updated_at: null,
            updated_by: null,
            deleted_at: null,
            deleted_by: null,
          },
          {
            id: "323e4567-e89b-12d3-a456-************",
            invoice_id: "123e4567-e89b-12d3-a456-************",
            item_name: "Content Creation",
            item_amount: 10,
            item_price: 50,
            total_price: 500,
            created_at: "2025-03-01T00:00:00.000Z",
            created_by: "auth0|*********",
            updated_at: null,
            updated_by: null,
            deleted_at: null,
            deleted_by: null,
          },
        ],
      },
    },
  },
  getInvoicesResponseExample: {
    summary: "Example get all invoices response",
    value: {
      success: true,
      message: "Invoices retrieved successfully",
      data: {
        data: [
          {
            id: "123e4567-e89b-12d3-a456-************",
            invoice_number: "001/ORDEV/Kasuat/III/2025",
            invoice_type: InvoiceType.EXTERNAL,
            service_type: ServiceType.ORDEV,
            recipient_name: "Acme Corporation",
            project_id: "project_id_123",
            project_name: "Website Redesign",
            due_date: "2025-04-15",
            payment_method: PaymentMethod.BANK_TRANSFER,
            payment_status: PaymentStatus.PENDING,
            notes: "Please pay within 30 days",
            total_amount: 2000,
            created_at: "2025-03-01T00:00:00.000Z",
            items: [
              {
                id: "223e4567-e89b-12d3-a456-************",
                invoice_id: "123e4567-e89b-12d3-a456-************",
                item_name: "Web Design",
                item_amount: 1,
                item_price: 1500,
                total_price: 1500,
              },
              {
                id: "323e4567-e89b-12d3-a456-************",
                invoice_id: "123e4567-e89b-12d3-a456-************",
                item_name: "Content Creation",
                item_amount: 10,
                item_price: 50,
                total_price: 500,
              },
            ],
          },
          {
            id: "223e4567-e89b-12d3-a456-************",
            invoice_number: "002/FINANCE/Kasuat/III/2025",
            invoice_type: InvoiceType.INTERNAL,
            service_type: ServiceType.FINANCE,
            recipient_name: "Tech Partners Inc.",
            due_date: "2025-05-01",
            payment_method: PaymentMethod.CREDIT_CARD,
            payment_status: PaymentStatus.PAID,
            total_amount: 3500,
            created_at: "2025-03-05T00:00:00.000Z",
            items: [
              {
                id: "423e4567-e89b-12d3-a456-426614174020",
                invoice_id: "223e4567-e89b-12d3-a456-************",
                item_name: "Financial Consulting",
                item_amount: 5,
                item_price: 700,
                total_price: 3500,
              },
            ],
          },
        ],
        pagination: {
          total: 25,
          page: 1,
          pageSize: 10,
          pageCount: 3,
        },
      },
    },
  },
  // POST and PUT examples
  createInvoiceExample: {
    summary: "Example create invoice request",
    value: {
      invoice_type: InvoiceType.EXTERNAL,
      service_type: ServiceType.ORDEV,
      recipient_name: "Acme Corporation",
      project_id: "project_id_123",
      project_name: "Website Redesign",
      due_date: "2025-04-15",
      payment_method: PaymentMethod.BANK_TRANSFER,
      notes: "Please pay within 30 days",
      items: [
        {
          item_name: "Web Design",
          item_amount: 1,
          item_price: 1500,
        },
        {
          item_name: "Content Creation",
          item_amount: 10,
          item_price: 50,
        },
      ],
    },
  },
  createInvoiceResponseExample: {
    summary: "Example create invoice response",
    value: {
      success: true,
      message: "Invoice created successfully",
      data: {
        id: "123e4567-e89b-12d3-a456-************",
        invoice_number: "001/ORDEV/Kasuat/III/2025",
        invoice_type: InvoiceType.EXTERNAL,
        service_type: ServiceType.ORDEV,
        recipient_name: "Acme Corporation",
        project_id: "project_id_123",
        project_name: "Website Redesign",
        due_date: "2025-04-15",
        payment_method: PaymentMethod.BANK_TRANSFER,
        payment_status: PaymentStatus.PENDING,
        notes: "Please pay within 30 days",
        total_amount: 2000,
        created_at: "2025-03-01T00:00:00.000Z",
        created_by: "auth0|*********",
        updated_at: null,
        updated_by: null,
        deleted_at: null,
        deleted_by: null,
        items: [
          {
            id: "223e4567-e89b-12d3-a456-************",
            invoice_id: "123e4567-e89b-12d3-a456-************",
            item_name: "Web Design",
            item_amount: 1,
            item_price: 1500,
            total_price: 1500,
            created_at: "2025-03-01T00:00:00.000Z",
            created_by: "auth0|*********",
            updated_at: null,
            updated_by: null,
            deleted_at: null,
            deleted_by: null,
          },
          {
            id: "323e4567-e89b-12d3-a456-************",
            invoice_id: "123e4567-e89b-12d3-a456-************",
            item_name: "Content Creation",
            item_amount: 10,
            item_price: 50,
            total_price: 500,
            created_at: "2025-03-01T00:00:00.000Z",
            created_by: "auth0|*********",
            updated_at: null,
            updated_by: null,
            deleted_at: null,
            deleted_by: null,
          },
        ],
      },
    },
  },
  updateInvoiceExample: {
    summary: "Example update invoice request",
    value: {
      invoice_type: InvoiceType.EXTERNAL,
      recipient_name: "Updated Corporation Ltd",
      payment_status: PaymentStatus.PARTIAL,
      notes: "Updated payment received on March 15",
      items: [
        {
          id: "223e4567-e89b-12d3-a456-************", // ID of existing item
          item_name: "Updated Web Design",
          item_amount: 1,
          item_price: 2000,
        },
        {
          // New item without ID (will be created)
          item_name: "Additional Service",
          item_amount: 5,
          item_price: 100,
        },
      ],
    },
  },
  updateInvoiceResponseExample: {
    summary: "Example update invoice response",
    value: {
      success: true,
      message: "Invoice updated successfully",
      data: {
        id: "123e4567-e89b-12d3-a456-************",
        invoice_number: "001/ORDEV/Kasuat/III/2025",
        invoice_type: InvoiceType.EXTERNAL,
        service_type: ServiceType.ORDEV,
        recipient_name: "Updated Corporation Ltd",
        project_id: "project_id_123",
        project_name: "Website Redesign",
        due_date: "2025-04-15",
        payment_method: PaymentMethod.BANK_TRANSFER,
        payment_status: PaymentStatus.PARTIAL,
        notes: "Updated payment received on March 15",
        total_amount: 2500,
        created_at: "2025-03-01T00:00:00.000Z",
        created_by: "auth0|*********",
        updated_at: "2025-03-15T00:00:00.000Z",
        updated_by: "auth0|*********",
        deleted_at: null,
        deleted_by: null,
        items: [
          {
            id: "223e4567-e89b-12d3-a456-************",
            invoice_id: "123e4567-e89b-12d3-a456-************",
            item_name: "Updated Web Design",
            item_amount: 1,
            item_price: 2000,
            total_price: 2000,
            created_at: "2025-03-01T00:00:00.000Z",
            created_by: "auth0|*********",
            updated_at: "2025-03-15T00:00:00.000Z",
            updated_by: "auth0|*********",
            deleted_at: null,
            deleted_by: null,
          },
          {
            id: "423e4567-e89b-12d3-a456-************",
            invoice_id: "123e4567-e89b-12d3-a456-************",
            item_name: "Additional Service",
            item_amount: 5,
            item_price: 100,
            total_price: 500,
            created_at: "2025-03-15T00:00:00.000Z",
            created_by: "auth0|*********",
            updated_at: null,
            updated_by: null,
            deleted_at: null,
            deleted_by: null,
          },
        ],
      },
    },
  },
  deleteInvoiceResponseExample: {
    summary: "Example delete invoice response",
    value: {
      success: true,
      message:
        "Invoice with ID 123e4567-e89b-12d3-a456-************ successfully deleted",
      data: null,
    },
  },
};

// Schema definitions for Invoice operations
export const invoiceSchemas = {
  UpdateInvoiceDto: {
    type: "object" as const,
    properties: {
      invoice_type: {
        type: "string" as const,
        enum: Object.values(InvoiceType),
        description: "The type of the invoice (external or internal)",
      },
      service_type: {
        type: "string" as const,
        enum: Object.values(ServiceType),
        description: "The service type for the invoice",
      },
      recipient_name: {
        type: "string" as const,
        minLength: 1,
        maxLength: 100,
        description: "The name of the recipient",
      },
      project_id: {
        type: "string" as const,
        description: "The ID of the project",
      },
      project_name: {
        type: "string" as const,
        description: "The name of the project",
      },
      due_date: {
        type: "string" as const,
        pattern: "^\\d{4}-\\d{2}-\\d{2}$",
        description: "The due date of the invoice (YYYY-MM-DD format)",
      },
      payment_method: {
        type: "string" as const,
        enum: Object.values(PaymentMethod),
        description: "The method of payment",
      },
      payment_status: {
        type: "string" as const,
        enum: Object.values(PaymentStatus),
        description: "The status of the payment",
      },
      notes: {
        type: "string" as const,
        description: "Additional notes for the invoice",
      },
      items: {
        type: "array" as const,
        items: {
          type: "object" as const,
          properties: {
            id: {
              type: "string" as const,
              format: "uuid",
              description: "The ID of an existing item (when updating)",
            },
            item_name: {
              type: "string" as const,
              description: "The name of the item",
            },
            item_amount: {
              type: "number" as const,
              description: "The amount/quantity of the item",
            },
            item_price: {
              type: "number" as const,
              description: "The price per unit of the item",
            },
          },
        },
      },
    },
  },
  CreateInvoiceDto: {
    type: "object" as const,
    required: [
      "invoice_type",
      "service_type",
      "recipient_name",
      "due_date",
      "payment_method",
      "items",
    ],
    properties: {
      invoice_type: {
        type: "string" as const,
        enum: Object.values(InvoiceType),
        description: "The type of the invoice (external or internal)",
      },
      service_type: {
        type: "string" as const,
        enum: Object.values(ServiceType),
        description: "The service type for the invoice",
      },
      recipient_name: {
        type: "string" as const,
        minLength: 1,
        maxLength: 100,
        description: "The name of the recipient",
      },
      project_id: {
        type: "string" as const,
        description: "The ID of the project (optional)",
      },
      project_name: {
        type: "string" as const,
        description: "The name of the project (optional)",
      },
      due_date: {
        type: "string" as const,
        pattern: "^\\d{4}-\\d{2}-\\d{2}$",
        description: "The due date of the invoice (YYYY-MM-DD format)",
      },
      payment_method: {
        type: "string" as const,
        enum: Object.values(PaymentMethod),
        description: "The method of payment",
      },
      payment_status: {
        type: "string" as const,
        enum: Object.values(PaymentStatus),
        description:
          "The status of the payment (optional, defaults to PENDING)",
      },
      notes: {
        type: "string" as const,
        description: "Additional notes for the invoice (optional)",
      },
      items: {
        type: "array" as const,
        items: {
          type: "object" as const,
          required: ["item_name", "item_amount", "item_price"],
          properties: {
            item_name: {
              type: "string" as const,
              description: "The name of the item",
            },
            item_amount: {
              type: "number" as const,
              description: "The amount/quantity of the item",
            },
            item_price: {
              type: "number" as const,
              description: "The price per unit of the item",
            },
          },
        },
      },
    },
  },
};
