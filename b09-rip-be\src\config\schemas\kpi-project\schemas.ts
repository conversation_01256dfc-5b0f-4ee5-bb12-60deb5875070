// Define KPI Project schema examples
import { KpiStatus } from "../../../database/models/kpi-project.model";

export const kpiProjectExamples = {
  createKpiProjectExample: {
    summary: "Example create KPI Project request",
    value: {
      project_name: "ERP System Development",
      project_id: "PROJ-12345",
      description: "Develop a new ERP system for client XYZ",
      target: "Complete all modules by Q2 2024",
      period: "2024-Q1",
      status: KpiStatus.NOT_STARTED,
      additional_notes: "Initial project setup phase",
    },
  },
  kpiProjectResponseExample: {
    summary: "Example KPI Project response",
    value: {
      success: true,
      message: "KPI Project created successfully",
      data: {
        id: "example-kpi-project-id-12345",
        project_name: "ERP System Development",
        project_id: "PROJ-12345",
        description: "Develop a new ERP system for client XYZ",
        target: "Complete all modules by Q2 2024",
        period: "2024-Q1",
        status: KpiStatus.NOT_STARTED,
        additional_notes: "Initial project setup phase",
        created_at: "2023-12-01T00:00:00.000Z",
        created_by: "example-creator-id-12345",
        updated_at: null,
        updated_by: null,
        deleted_at: null,
        deleted_by: null,
      },
    },
  },
};

// Define KPI Project schema components
export const kpiProjectSchemas = {
  KpiProject: {
    type: "object" as const,
    description: "KPI Project record",
    properties: {
      id: {
        type: "string" as const,
        format: "uuid",
        description: "KPI Project ID (primary key)",
      },
      project_name: {
        type: "string" as const,
        description: "The name of the project",
      },
      project_id: {
        type: "string" as const,
        description: "The ID of the project",
      },
      description: {
        type: "string" as const,
        description: "The description of the KPI project",
      },
      target: {
        type: "string" as const,
        description: "The target of the KPI project",
      },
      period: {
        type: "string" as const,
        description: "The period of the KPI project (e.g., '2024-Q1')",
      },
      status: {
        type: "string" as const,
        description: "The status of the KPI project",
        enum: Object.values(KpiStatus),
      },
      additional_notes: {
        type: "string" as const,
        nullable: true,
        description: "Additional notes for the KPI project",
      },
      created_at: {
        type: "string" as const,
        format: "date-time",
        description: "Creation timestamp",
      },
      created_by: {
        type: "string" as const,
        format: "uuid",
        description: "User ID who created the record",
      },
      updated_at: {
        type: "string" as const,
        format: "date-time",
        nullable: true,
        description: "Last update timestamp",
      },
      updated_by: {
        type: "string" as const,
        format: "uuid",
        nullable: true,
        description: "User ID who last updated the record",
      },
      deleted_at: {
        type: "string" as const,
        format: "date-time",
        nullable: true,
        description: "Soft deletion timestamp",
      },
      deleted_by: {
        type: "string" as const,
        format: "uuid",
        nullable: true,
        description: "User ID who deleted the record",
      },
    },
  },
  // TODO: Add more schema components as needed
};
