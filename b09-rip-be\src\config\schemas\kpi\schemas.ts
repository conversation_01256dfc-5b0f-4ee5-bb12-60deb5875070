// Define KPI schema examples
import { KpiStatus } from "../../../database/models/kpi.model";

export const kpiExamples = {
  getKpisExample: {
    summary: "Example get KPIs response",
    value: {
      success: true,
      message: "KPIs retrieved successfully",
      data: {
        items: [
          {
            id: "example-kpi-id-12345",
            full_name: "<PERSON>",
            employee_id: "EMP-12345",
            description: "Increase sales by 25% in Q1 2024",
            target: "25% increase in sales",
            period: "2024-Q1",
            status: KpiStatus.IN_PROGRESS,
            bonus_received: null,
            additional_notes: "On track to meet target",
            created_at: "2023-12-01T00:00:00.000Z",
            created_by: "example-creator-id-12345",
            updated_at: "2024-01-15T00:00:00.000Z",
            updated_by: "example-updater-id-12345",
            deleted_at: null,
            deleted_by: null,
          },
          {
            id: "example-kpi-id-67890",
            full_name: "<PERSON>",
            employee_id: "EMP-67890",
            description: "Reduce customer support response time to under 2 hours",
            target: "< 2 hour response time",
            period: "2024-Q1",
            status: KpiStatus.COMPLETED_ABOVE_TARGET,
            bonus_received: 5000,
            additional_notes: "Achieved 1.5 hour average response time",
            created_at: "2023-12-01T00:00:00.000Z",
            created_by: "example-creator-id-12345",
            updated_at: "2024-03-01T00:00:00.000Z",
            updated_by: "example-updater-id-12345",
            deleted_at: null,
            deleted_by: null,
          },
        ],
        pagination: {
          total: 6,
          page: 1,
          pageSize: 10,
          pageCount: 1,
        },
      },
    },
  },
  createKpiExample: {
    summary: "Example create KPI request",
    value: {
      full_name: "John Doe",
      employee_id: "EMP-12345",
      description: "Increase sales by 25% in Q1 2024",
      target: "25% increase in sales",
      period: "2024-Q1",
      status: KpiStatus.NOT_STARTED,
      additional_notes: "Initial KPI for sales team lead",
    },
  },
  updateKpiExample: {
    summary: "Example update KPI request",
    value: {
      status: KpiStatus.IN_PROGRESS,
      additional_notes: "Making good progress on target",
    },
  },
  updateKpiStatusExample: {
    summary: "Example update KPI status request",
    value: {
      status: KpiStatus.COMPLETED_ON_TARGET,
    },
  },
  updateKpiBonusExample: {
    summary: "Example update KPI bonus request",
    value: {
      bonus_received: 3500,
    },
  },
  deleteKpiExample: {
    summary: "Example delete KPI response",
    value: {
      success: true,
      message: "KPI deleted successfully",
      data: {
        id: "example-kpi-id-12345",
        deleted: true,
      },
    },
  },
};

// Define KPI schema components
export const kpiSchemas = {
  Kpi: {
    type: "object" as const,
    description: "KPI record",
    properties: {
      id: {
        type: "string" as const,
        format: "uuid",
        description: "KPI ID (primary key)",
      },
      full_name: {
        type: "string" as const,
        description: "The full name of the employee",
      },
      employee_id: {
        type: "string" as const,
        description: "The ID of the employee",
      },
      description: {
        type: "string" as const,
        description: "The description of the KPI",
      },
      target: {
        type: "string" as const,
        description: "The target of the KPI",
      },
      period: {
        type: "string" as const,
        description: "The period of the KPI (e.g., '2024-Q1')",
      },
      status: {
        type: "string" as const,
        description: "The status of the KPI",
        enum: Object.values(KpiStatus),
      },
      bonus_received: {
        type: "number" as const,
        description: "The bonus amount received",
        nullable: true,
      },
      additional_notes: {
        type: "string" as const,
        description: "Additional notes for the KPI",
        nullable: true,
      },
      created_at: {
        type: "string" as const,
        format: "date-time",
      },
      created_by: {
        type: "string" as const,
        format: "uuid",
      },
      updated_at: {
        type: "string" as const,
        format: "date-time",
        nullable: true,
      },
      updated_by: {
        type: "string" as const,
        format: "uuid",
        nullable: true,
      },
      deleted_at: {
        type: "string" as const,
        format: "date-time",
        nullable: true,
      },
      deleted_by: {
        type: "string" as const,
        format: "uuid",
        nullable: true,
      },
    },
  },
  CreateKpiRequest: {
    type: "object" as const,
    description: "Request to create a new KPI",
    properties: {
      full_name: {
        type: "string" as const,
        description: "The full name of the employee",
      },
      employee_id: {
        type: "string" as const,
        description: "The ID of the employee",
      },
      description: {
        type: "string" as const,
        description: "The description of the KPI",
      },
      target: {
        type: "string" as const,
        description: "The target of the KPI",
      },
      period: {
        type: "string" as const,
        description: "The period of the KPI (e.g., '2024-Q1')",
      },
      status: {
        type: "string" as const,
        description: "The status of the KPI",
        enum: Object.values(KpiStatus),
      },
      bonus_received: {
        type: "number" as const,
        description: "The bonus amount received",
        nullable: true,
      },
      additional_notes: {
        type: "string" as const,
        description: "Additional notes for the KPI",
        nullable: true,
      },
    },
    required: ["full_name", "employee_id", "description", "target", "period"],
  },
  UpdateKpiRequest: {
    type: "object" as const,
    description: "Request to update an existing KPI",
    properties: {
      full_name: {
        type: "string" as const,
        description: "The full name of the employee",
      },
      employee_id: {
        type: "string" as const,
        description: "The ID of the employee",
      },
      description: {
        type: "string" as const,
        description: "The description of the KPI",
      },
      target: {
        type: "string" as const,
        description: "The target of the KPI",
      },
      period: {
        type: "string" as const,
        description: "The period of the KPI (e.g., '2024-Q1')",
      },
      status: {
        type: "string" as const,
        description: "The status of the KPI",
        enum: Object.values(KpiStatus),
      },
      bonus_received: {
        type: "number" as const,
        description: "The bonus amount received",
        nullable: true,
      },
      additional_notes: {
        type: "string" as const,
        description: "Additional notes for the KPI",
        nullable: true,
      },
    },
  },
  UpdateKpiStatusRequest: {
    type: "object" as const,
    description: "Request to update a KPI's status",
    properties: {
      status: {
        type: "string" as const,
        description: "The status of the KPI",
        enum: Object.values(KpiStatus),
      },
    },
    required: ["status"],
  },
  UpdateKpiBonusRequest: {
    type: "object" as const,
    description: "Request to update a KPI's bonus amount",
    properties: {
      bonus_received: {
        type: "number" as const,
        description: "The bonus amount received",
        min: 0,
      },
    },
    required: ["bonus_received"],
  },
  KpiListResponse: {
    type: "object" as const,
    description: "Response containing a list of KPIs",
    properties: {
      items: {
        type: "array" as const,
        items: {
          $ref: "#/components/schemas/Kpi",
        },
      },
      pagination: {
        $ref: "#/components/schemas/PaginationResult",
      },
    },
  },
}; 