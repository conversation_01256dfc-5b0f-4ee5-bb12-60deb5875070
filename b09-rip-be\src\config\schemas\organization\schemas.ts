// Define organization schema examples for Swagger documentation
export const organizationExamples = {
  createOrganizationExample: {
    summary: "Example create organization request",
    value: {
      name: "Acme Corporation",
      phone: "628*********0",
      address: "123 Main Street, Metropolis",
      client_type: "Enterprise",
      notes: "Important client with multiple projects",
    },
  },
  updateOrganizationExample: {
    summary: "Example update organization request",
    value: {
      name: "Acme Corporation Updated",
      phone: "628*********0",
      address: "456 New Avenue, Metropolis",
      client_type: "Enterprise Premium",
      notes: "VIP client with dedicated support team",
    },
  },
  getOrganizationsExample: {
    summary: "Example get organizations response",
    value: {
      success: true,
      message: "Organizations retrieved successfully",
      data: {
        items: [
          {
            id: "123e4567-e89b-12d3-a456-426614174000",
            name: "Acme Corporation",
            phone: "628*********0",
            address: "123 Main Street, Metropolis",
            client_type: "Enterprise",
            notes: "Important client with multiple projects",
            created_at: "2023-01-01T00:00:00.000Z",
            created_by: "auth0|*********",
            updated_at: null,
            updated_by: null,
            deleted_at: null,
            deleted_by: null,
          },
          {
            id: "223e4567-e89b-12d3-a456-426614174001",
            name: "TechStart Inc",
            phone: "6287654321098",
            address: "789 Innovation Drive, Silicon Valley",
            client_type: "Startup",
            notes: "Growing tech company",
            created_at: "2023-01-15T00:00:00.000Z",
            created_by: "auth0|*********",
            updated_at: "2023-02-01T00:00:00.000Z",
            updated_by: "auth0|*********",
            deleted_at: null,
            deleted_by: null,
          },
        ],
        pagination: {
          total: 2,
          page: 1,
          pageSize: 10,
          pageCount: 1,
        },
      },
    },
  },
  getOrganizationExample: {
    summary: "Example get organization by ID response",
    value: {
      success: true,
      message: "Organization retrieved successfully",
      data: {
        id: "123e4567-e89b-12d3-a456-426614174000",
        name: "Acme Corporation",
        phone: "628*********0",
        address: "123 Main Street, Metropolis",
        client_type: "Enterprise",
        notes: "Important client with multiple projects",
        created_at: "2023-01-01T00:00:00.000Z",
        created_by: "auth0|*********",
        updated_at: null,
        updated_by: null,
        deleted_at: null,
        deleted_by: null,
      },
    },
  },
  notFoundErrorExample: {
    summary: "Example not found error response",
    value: {
      success: false,
      message: "Organization not found",
      data: null,
      error: {
        code: "NOT_FOUND",
        details: null,
      },
    },
  },
  deleteOrganizationExample: {
    summary: "Example delete organization response",
    value: {
      success: true,
      message: "Organization deleted successfully",
      data: null,
    },
  },
};

// Define organization schemas for Swagger documentation
export const organizationSchemas = {
  Organization: {
    type: "object" as const,
    required: [
      "id",
      "name",
      "phone",
      "address",
      "client_type",
      "created_at",
      "created_by",
    ],
    properties: {
      id: {
        type: "string" as const,
        format: "uuid",
        description: "Unique identifier for the organization",
      },
      name: {
        type: "string" as const,
        minLength: 2,
        maxLength: 100,
        description: "Organization name (2-100 characters)",
      },
      phone: {
        type: "string" as const,
        pattern: "^[0-9]+$",
        minLength: 10,
        description: "Phone number (minimum 10 digits, only numbers allowed)",
      },
      address: {
        type: "string" as const,
        minLength: 5,
        maxLength: 500,
        description: "Organization address (5-500 characters)",
      },
      client_type: {
        type: "string" as const,
        minLength: 2,
        maxLength: 50,
        description: "Type of client organization (2-50 characters)",
      },
      notes: {
        type: "string" as const,
        nullable: true,
        maxLength: 1000,
        description:
          "Optional notes about the organization (max 1000 characters)",
      },
      created_at: {
        type: "string" as const,
        format: "date-time",
        description: "Timestamp when the organization was created",
      },
      created_by: {
        type: "string" as const,
        description: "ID of the user who created the organization",
      },
      updated_at: {
        type: "string" as const,
        format: "date-time",
        nullable: true,
        description: "Timestamp when the organization was last updated",
      },
      updated_by: {
        type: "string" as const,
        nullable: true,
        description: "ID of the user who last updated the organization",
      },
      deleted_at: {
        type: "string" as const,
        format: "date-time",
        nullable: true,
        description:
          "Timestamp when the organization was deleted (soft delete)",
      },
      deleted_by: {
        type: "string" as const,
        nullable: true,
        description: "ID of the user who deleted the organization",
      },
    },
  },
  CreateOrganizationDto: {
    type: "object" as const,
    required: ["name", "phone", "address", "client_type"],
    properties: {
      name: {
        type: "string" as const,
        minLength: 2,
        maxLength: 100,
        description: "Organization name (2-100 characters)",
      },
      phone: {
        type: "string" as const,
        pattern: "^[0-9]+$",
        minLength: 10,
        description: "Phone number (minimum 10 digits, only numbers allowed)",
      },
      address: {
        type: "string" as const,
        minLength: 5,
        maxLength: 500,
        description: "Organization address (5-500 characters)",
      },
      client_type: {
        type: "string" as const,
        minLength: 2,
        maxLength: 50,
        description: "Type of client organization (2-50 characters)",
      },
      notes: {
        type: "string" as const,
        nullable: true,
        maxLength: 1000,
        description:
          "Optional notes about the organization (max 1000 characters)",
      },
    },
  },
  UpdateOrganizationDto: {
    type: "object" as const,
    properties: {
      name: {
        type: "string" as const,
        minLength: 2,
        maxLength: 100,
        description: "Organization name (2-100 characters)",
      },
      phone: {
        type: "string" as const,
        pattern: "^[0-9]+$",
        minLength: 10,
        description: "Phone number (minimum 10 digits, only numbers allowed)",
      },
      address: {
        type: "string" as const,
        minLength: 5,
        maxLength: 500,
        description: "Organization address (5-500 characters)",
      },
      client_type: {
        type: "string" as const,
        minLength: 2,
        maxLength: 50,
        description: "Type of client organization (2-50 characters)",
      },
      notes: {
        type: "string" as const,
        nullable: true,
        maxLength: 1000,
        description:
          "Optional notes about the organization (max 1000 characters)",
      },
    },
  },
};
