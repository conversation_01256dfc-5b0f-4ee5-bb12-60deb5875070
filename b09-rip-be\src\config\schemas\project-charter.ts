import { Project<PERSON><PERSON><PERSON> } from "../../database/models/project-charter.model";

// Define examples for Swagger documentation
export const projectCharterExamples = {
  ProjectCharterExample: {
    summary: "Example project charter",
    value: {
      id: "123e4567-e89b-12d3-a456-426614174000",
      project_id: "123e4567-e89b-12d3-a456-426614174001",
      key_stakeholders: "Project Manager, Client, Development Team",
      project_authority:
        "Project Manager has final authority on project decisions",
      project_description:
        "This project aims to develop a new e-commerce platform",
      objective_and_key_results:
        "Increase sales by 20%, reduce cart abandonment by 15%",
      purpose: "To modernize the client's online shopping experience",
      key_assumption: "The client has provided all necessary requirements",
      assumptions_constrains_risks:
        "Budget constraints may limit feature scope, risk of delay due to third-party integrations",
      high_level_resources: "Development team of 5, UX designer, QA tester",
      high_level_milestones:
        "Requirements gathering (Week 1), Design (Weeks 2-3), Development (Weeks 4-10), Testing (Weeks 11-12)",
      statement_prediction_of_benefit:
        "This project will increase online sales and improve customer satisfaction",
      approval: true,
      created_at: "2023-01-01T00:00:00.000Z",
      created_by: "123e4567-e89b-12d3-a456-426614174002",
      updated_at: null,
      updated_by: null,
      deleted_at: null,
      deleted_by: null,
    },
  },
};

// Define schemas for Swagger documentation
export const projectCharterSchemas = {
  ProjectCharter: {
    type: "object" as const,
    required: [
      "id",
      "project_id",
      "key_stakeholders",
      "project_authority",
      "project_description",
      "objective_and_key_results",
      "purpose",
      "key_assumption",
      "assumptions_constrains_risks",
      "high_level_resources",
      "high_level_milestones",
      "statement_prediction_of_benefit",
      "approval",
      "created_at",
      "created_by",
    ],
    properties: {
      id: {
        type: "string" as const,
        format: "uuid",
        description: "Unique identifier",
      },
      project_id: {
        type: "string" as const,
        format: "uuid",
        description: "Project ID",
      },
      key_stakeholders: {
        type: "string" as const,
        description: "Key stakeholders involved in the project",
      },
      project_authority: {
        type: "string" as const,
        description: "Project authority and governance structure",
      },
      project_description: {
        type: "string" as const,
        description: "Detailed description of the project",
      },
      objective_and_key_results: {
        type: "string" as const,
        description: "Objectives and key results for the project",
      },
      purpose: {
        type: "string" as const,
        description: "Purpose of the project",
      },
      key_assumption: {
        type: "string" as const,
        description: "Key assumptions for the project",
      },
      assumptions_constrains_risks: {
        type: "string" as const,
        description: "Assumptions, constraints, and risks for the project",
      },
      high_level_resources: {
        type: "string" as const,
        description: "High-level resources required for the project",
      },
      high_level_milestones: {
        type: "string" as const,
        description: "High-level milestones for the project",
      },
      statement_prediction_of_benefit: {
        type: "string" as const,
        description: "Statement and prediction of benefits from the project",
      },
      approval: {
        type: "boolean" as const,
        description: "Whether the project charter is approved",
      },
      created_at: {
        type: "string" as const,
        format: "date-time",
        description: "Creation timestamp",
      },
      created_by: {
        type: "string" as const,
        format: "uuid",
        description: "User ID who created the record",
      },
      updated_at: {
        type: "string" as const,
        format: "date-time",
        nullable: true,
        description: "Last update timestamp",
      },
      updated_by: {
        type: "string" as const,
        format: "uuid",
        nullable: true,
        description: "User ID who last updated the record",
      },
      deleted_at: {
        type: "string" as const,
        format: "date-time",
        nullable: true,
        description: "Deletion timestamp (for soft deletes)",
      },
      deleted_by: {
        type: "string" as const,
        format: "uuid",
        nullable: true,
        description: "User ID who deleted the record",
      },
    },
  },
};
