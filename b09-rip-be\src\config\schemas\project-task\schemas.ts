//path: src/config/schemas/project-task/schemas.ts
import { TaskStatus } from "../../../database/models/task.model";

// Define module schemas for Swagger documentation
export const projectTaskSchemas = {
  ProjectTask: {
    type: "object" as const,
    required: [
      "id",
      "assigned_by",
      "description",
      "completion_status",
      "employee_id",
      "initial_date",
      "due_date",
      "project_id",
      "created_at",
      "created_by",
    ],
    properties: {
      id: {
        type: "string" as const,
        format: "uuid",
        description: "Unique identifier",
      },
      assigned_by: {
        type: "string" as const,
        format: "uuid",
        description: "Employee ID who assigned the task",
      },
      description: {
        type: "string" as const,
        description: "Task description",
      },
      completion_status: {
        type: "string" as const,
        enum: Object.values(TaskStatus),
        description:
          "Task completion status (not_completed, on_progress, completed)",
      },
      employee_id: {
        type: "string" as const,
        format: "uuid",
        description: "Employee ID assigned to the task",
      },
      initial_date: {
        type: "string" as const,
        format: "date",
        description: "Start date in YYYY-MM-DD format",
      },
      due_date: {
        type: "string" as const,
        format: "date",
        description: "Due date in YYYY-MM-DD format",
      },
      project_id: {
        type: "string" as const,
        format: "uuid",
        description: "Project ID",
      },
      weekly_log_id: {
        type: "string" as const,
        format: "uuid",
        description: "Optional UUID of the associated weekly log",
        nullable: true,
      },
      created_at: {
        type: "string" as const,
        format: "date-time",
        description: "Creation timestamp",
      },
      created_by: {
        type: "string" as const,
        format: "uuid",
        description: "User ID who created the record",
      },
      updated_at: {
        type: "string" as const,
        format: "date-time",
        description: "Last update timestamp",
        nullable: true,
      },
      updated_by: {
        type: "string" as const,
        format: "uuid",
        description: "User ID who last updated the record",
        nullable: true,
      },
      deleted_at: {
        type: "string" as const,
        format: "date-time",
        description: "Deletion timestamp",
        nullable: true,
      },
      deleted_by: {
        type: "string" as const,
        format: "uuid",
        description: "User ID who deleted the record",
        nullable: true,
      },
    },
  },

  CreateProjectTaskDto: {
    type: "object" as const,
    required: [
      "assigned_by",
      "description",
      "employee_id",
      "initial_date",
      "due_date",
      "project_id",
    ],
    properties: {
      assigned_by: {
        type: "string" as const,
        format: "uuid",
        description: "UUID of the user who assigned the task",
      },
      description: {
        type: "string" as const,
        minLength: 3,
        maxLength: 255,
        description: "Task description",
      },
      completion_status: {
        type: "string" as const,
        enum: Object.values(TaskStatus),
        description: "Task completion status",
        default: "not_completed",
      },
      employee_id: {
        type: "string" as const,
        format: "uuid",
        description: "Employee ID assigned to the task",
      },
      initial_date: {
        type: "string" as const,
        format: "date",
        description: "Start date in YYYY-MM-DD format",
      },
      due_date: {
        type: "string" as const,
        format: "date",
        description: "Due date in YYYY-MM-DD format",
      },
      project_id: {
        type: "string" as const,
        format: "uuid",
        description: "Project ID",
      },
      weekly_log_id: {
        type: "string" as const,
        format: "uuid",
        description: "Optional UUID of the associated weekly log",
        nullable: true,
      },
    },
  },

  UpdateProjectTaskStatusDto: {
    type: "object" as const,
    required: ["completion_status"],
    properties: {
      completion_status: {
        type: "string" as const,
        enum: Object.values(TaskStatus),
        description:
          "Task completion status (not_completed, on_progress, completed)",
      },
    },
  },
};

// Define examples separately
export const projectTaskExamples = {
  createProjectTaskExample: {
    summary: "Create project task example",
    value: {
      assigned_by: "123e4567-e89b-12d3-a456-************", // UUID of the user who assigned the task
      description: "Implement authentication system",
      employee_id: "123e4567-e89b-12d3-a456-************", // UUID of the employee assigned to the task
      initial_date: "2023-12-01", // Start date
      due_date: "2023-12-31", // Due date
      project_id: "123e4567-e89b-12d3-a456-************", // UUID of the associated project
      completion_status: "not_completed", // Task status (default)
      weekly_log_id: "123e4567-e89b-12d3-a456-************", // Optional UUID of the associated weekly log
    },
  },

  updateProjectTaskStatusExample: {
    summary: "Update project task status example",
    value: {
      completion_status: "completed", // New task status
    },
  },

  updateProjectTaskExample: {
    summary: "Update project task example",
    value: {
      description: "Updated authentication system implementation",
      due_date: "2024-01-15", // Extended due date
      employee_id: "123e4567-e89b-12d3-a456-************", // Reassigned to different employee
      completion_status: "on_progress", // Updated status
    },
  },
};
