import {
  ProjectCategory,
  ProjectStatus,
} from "../../../database/models/project.model";
import { KpiStatus } from "../../../database/models/kpi-project.model";
import { TaskStatus } from "../../../database/models/task.model";

// Define module schemas for Swagger documentation
export const projectSchemas = {
  Project: {
    type: "object" as const,
    required: [
      "id",
      "organization_id",
      "project_category",
      "project_name",
      "pic_project",
      "start_project",
      "end_project",
      "status_project",
      "budget_project",
      "objectives",
      "created_at",
      "created_by",
    ],
    properties: {
      id: {
        type: "string" as const,
        format: "uuid",
        description: "Unique identifier",
      },
      organization_id: {
        type: "string" as const,
        format: "uuid",
        description: "Organization ID",
      },
      project_category: {
        type: "string" as const,
        enum: Object.values(ProjectCategory),
        description: "Project category",
      },
      project_name: {
        type: "string" as const,
        description: "Project name",
      },
      pic_project: {
        type: "string" as const,
        format: "uuid",
        description: "Employee ID of the person in charge of the project",
      },
      start_project: {
        type: "string" as const,
        format: "date",
        description: "Project start date (YYYY-MM-DD)",
      },
      end_project: {
        type: "string" as const,
        format: "date",
        description: "Project end date (YYYY-MM-DD)",
      },
      status_project: {
        type: "string" as const,
        enum: Object.values(ProjectStatus),
        description: "Project status",
      },
      budget_project: {
        type: "string" as const,
        description: "Project budget",
      },
      gantt_chart_id: {
        type: "string" as const,
        description: "Gantt chart ID (auto-generated)",
      },
      project_charter_id: {
        type: "string" as const,
        description: "Project charter ID (auto-generated)",
      },
      objectives: {
        type: "string" as const,
        description: "Project objectives",
      },
      created_at: {
        type: "string" as const,
        format: "date-time",
        description: "Timestamp when the project was created",
      },
      created_by: {
        type: "string" as const,
        format: "uuid",
        description: "ID of the user who created the project",
      },
      updated_at: {
        type: "string" as const,
        format: "date-time",
        nullable: true,
        description: "Timestamp when the project was last updated",
      },
      updated_by: {
        type: "string" as const,
        format: "uuid",
        nullable: true,
        description: "ID of the user who last updated the project",
      },
      deleted_at: {
        type: "string" as const,
        format: "date-time",
        nullable: true,
        description: "Timestamp when the project was deleted",
      },
      deleted_by: {
        type: "string" as const,
        format: "uuid",
        nullable: true,
        description: "ID of the user who deleted the project",
      },
    },
  },
  CreateProjectDto: {
    type: "object" as const,
    required: [
      "organization_id",
      "project_category",
      "project_name",
      "pic_project",
      "start_project",
      "end_project",
      "budget_project",
      "objectives",
    ],
    properties: {
      organization_id: {
        type: "string" as const,
        format: "uuid",
        description: "Organization ID",
      },
      project_category: {
        type: "string" as const,
        enum: Object.values(ProjectCategory),
        description: "Project category",
      },
      project_name: {
        type: "string" as const,
        description: "Project name",
      },
      pic_project: {
        type: "string" as const,
        format: "uuid",
        description: "Employee ID of the person in charge of the project",
      },
      start_project: {
        type: "string" as const,
        format: "date",
        description: "Project start date (YYYY-MM-DD)",
      },
      end_project: {
        type: "string" as const,
        format: "date",
        description: "Project end date (YYYY-MM-DD)",
      },
      status_project: {
        type: "string" as const,
        enum: Object.values(ProjectStatus),
        description: "Project status",
      },
      budget_project: {
        type: "string" as const,
        description: "Project budget",
      },
      objectives: {
        type: "string" as const,
        description: "Project objectives",
      },
    },
  },
  UpdateProjectDto: {
    type: "object" as const,
    properties: {
      organization_id: {
        type: "string" as const,
        format: "uuid",
        description: "Organization ID",
      },
      project_category: {
        type: "string" as const,
        enum: Object.values(ProjectCategory),
        description: "Project category",
      },
      project_name: {
        type: "string" as const,
        description: "Project name",
      },
      pic_project: {
        type: "string" as const,
        format: "uuid",
        description: "Employee ID of the person in charge of the project",
      },
      start_project: {
        type: "string" as const,
        format: "date",
        description: "Project start date (YYYY-MM-DD)",
      },
      end_project: {
        type: "string" as const,
        format: "date",
        description: "Project end date (YYYY-MM-DD)",
      },
      status_project: {
        type: "string" as const,
        enum: Object.values(ProjectStatus),
        description: "Project status",
      },
      budget_project: {
        type: "string" as const,
        description: "Project budget",
      },
      objectives: {
        type: "string" as const,
        description: "Project objectives",
      },
    },
  },
  DeleteProjectResponse: {
    type: "object" as const,
    required: ["success", "message", "data"],
    properties: {
      success: {
        type: "boolean" as const,
        description: "Whether the operation was successful",
      },
      message: {
        type: "string" as const,
        description: "Success or error message",
      },
      data: {
        type: "object" as const,
        required: ["id"],
        properties: {
          id: {
            type: "string" as const,
            format: "uuid",
            description: "ID of the deleted project",
          },
        },
      },
    },
  },
  ProjectDashboard: {
    type: "object" as const,
    properties: {
      id: { type: "string" as const, format: "uuid" },
      project_name: { type: "string" as const },
      organization_id: { type: "string" as const, format: "uuid" },
      organization_name: { type: "string" as const },
      pic_project: { type: "string" as const, format: "uuid" },
      pic_name: { type: "string" as const },
      project_category: {
        type: "string" as const,
        enum: Object.values(ProjectCategory),
      },
      start_project: { type: "string" as const },
      end_project: { type: "string" as const },
      status_project: {
        type: "string" as const,
        enum: Object.values(ProjectStatus),
      },
      objectives: { type: "string" as const },
      budget_project: { type: "string" as const },

      progress_percentage: { type: "number" as const },
      days_elapsed: { type: "number" as const },
      days_remaining: { type: "number" as const },
      days_total: { type: "number" as const },

      kpi_status: {
        type: "string" as const,
        enum: Object.values(KpiStatus),
        nullable: true,
      },
      kpi_count: { type: "number" as const },
      kpis: {
        type: "array" as const,
        items: {
          type: "object" as const,
          properties: {
            id: { type: "string" as const, format: "uuid" },
            description: { type: "string" as const },
            target: { type: "string" as const },
            period: { type: "string" as const },
            status: {
              type: "string" as const,
              enum: Object.values(KpiStatus),
            },
          },
        },
      },

      tasks_total: { type: "number" as const },
      tasks_completed: { type: "number" as const },
      tasks_in_progress: { type: "number" as const },
      tasks_not_started: { type: "number" as const },
      recent_tasks: {
        type: "array" as const,
        items: {
          type: "object" as const,
          properties: {
            id: { type: "string" as const, format: "uuid" },
            description: { type: "string" as const },
            completion_status: {
              type: "string" as const,
              enum: Object.values(TaskStatus),
            },
            employee_name: { type: "string" as const },
            due_date: { type: "string" as const },
          },
        },
      },

      weekly_logs_count: { type: "number" as const },
      recent_weekly_logs: {
        type: "array" as const,
        items: {
          type: "object" as const,
          properties: {
            id: { type: "string" as const, format: "uuid" },
            week_number: { type: "number" as const },
            week_start_date: { type: "string" as const },
            week_end_date: { type: "string" as const },
            notes_count: { type: "number" as const },
          },
        },
      },
    },
  },
  DashboardSummary: {
    type: "object" as const,
    properties: {
      projects: {
        type: "object" as const,
        properties: {
          total: { type: "number" as const },
          by_status: {
            type: "object" as const,
            properties: {
              not_started: { type: "number" as const },
              in_progress: { type: "number" as const },
              completed: { type: "number" as const },
              cancelled: { type: "number" as const },
            },
          },
          by_category: {
            type: "object" as const,
            additionalProperties: { type: "number" as const },
          },
          by_pic: {
            type: "array" as const,
            items: {
              type: "object" as const,
              properties: {
                name: { type: "string" as const },
                count: { type: "number" as const },
              },
            },
          },
          recent: {
            type: "array" as const,
            items: {
              type: "object" as const,
              properties: {
                id: { type: "string" as const, format: "uuid" },
                project_name: { type: "string" as const },
                organization_name: { type: "string" as const },
                pic_name: { type: "string" as const },
                status_project: {
                  type: "string" as const,
                  enum: Object.values(ProjectStatus),
                },
                progress_percentage: { type: "number" as const },
                days_remaining: { type: "number" as const },
              },
            },
          },
          upcoming_deadlines: {
            type: "array" as const,
            items: {
              type: "object" as const,
              properties: {
                id: { type: "string" as const, format: "uuid" },
                project_name: { type: "string" as const },
                end_project: { type: "string" as const },
                days_remaining: { type: "number" as const },
                progress_percentage: { type: "number" as const },
              },
            },
          },
        },
      },
      kpis: {
        type: "object" as const,
        properties: {
          total: { type: "number" as const },
          by_status: {
            type: "object" as const,
            properties: {
              not_started: { type: "number" as const },
              in_progress: { type: "number" as const },
              completed_below_target: { type: "number" as const },
              completed_on_target: { type: "number" as const },
              completed_above_target: { type: "number" as const },
            },
          },
          achievement_percentage: { type: "number" as const },
          achieved_count: { type: "number" as const },
          not_achieved_count: { type: "number" as const },
        },
      },
      tasks: {
        type: "object" as const,
        properties: {
          total: { type: "number" as const },
          completed: { type: "number" as const },
          in_progress: { type: "number" as const },
          not_started: { type: "number" as const },
          overdue: { type: "number" as const },
        },
      },
      organization: {
        type: "object" as const,
        nullable: true,
        properties: {
          id: { type: "string" as const, format: "uuid" },
          name: { type: "string" as const },
          projects_count: { type: "number" as const },
          active_projects_count: { type: "number" as const },
          completed_projects_count: { type: "number" as const },
        },
      },
    },
  },
};

export const projectExamples = {
  createProjectExample: {
    summary: "Example create project request",
    value: {
      organization_id: "123e4567-e89b-12d3-a456-************",
      project_category: ProjectCategory.BRAND_STRATEGY,
      project_name: "Brand Refresh 2025",
      pic_project: "223e4567-e89b-12d3-a456-************",
      start_project: "2025-01-01",
      end_project: "2025-06-30",
      status_project: ProjectStatus.NOT_STARTED,
      budget_project: "500000000",
      objectives:
        "Develop and implement a comprehensive brand refresh strategy for 2025, including visual identity, messaging, and market positioning.",
    },
  },
  createProjectResponseExample: {
    summary: "Example create project response",
    value: {
      success: true,
      message: "Project created successfully",
      data: {
        id: "323e4567-e89b-12d3-a456-426614174002",
        organization_id: "123e4567-e89b-12d3-a456-************",
        project_category: "Brand Strategy",
        project_name: "Brand Refresh 2025",
        pic_project: "223e4567-e89b-12d3-a456-************",
        start_project: "2025-01-01",
        end_project: "2025-06-30",
        status_project: "Not Started",
        budget_project: "500000000",
        objectives:
          "Develop and implement a comprehensive brand refresh strategy for 2025, including visual identity, messaging, and market positioning.",
        gantt_chart_id: "TODO-gantt-chart-implementation",
        project_charter_id: "TODO-project-charter-implementation",
        created_at: "2024-12-01T00:00:00.000Z",
        created_by: "auth0|123456789",
        updated_at: null,
        updated_by: null,
        deleted_at: null,
        deleted_by: null,
      },
    },
  },
  getAllProjectsExample: {
    summary: "Example get all projects response",
    value: {
      success: true,
      message: "Projects retrieved successfully",
      data: [
        {
          id: "323e4567-e89b-12d3-a456-426614174002",
          organization_id: "123e4567-e89b-12d3-a456-************",
          project_category: "Brand Strategy",
          project_name: "Brand Refresh 2025",
          pic_project: "223e4567-e89b-12d3-a456-************",
          start_project: "2025-01-01",
          end_project: "2025-06-30",
          status_project: "Not Started",
          budget_project: "500000000",
          objectives:
            "Develop and implement a comprehensive brand refresh strategy for 2025, including visual identity, messaging, and market positioning.",
          gantt_chart_id: "TODO-gantt-chart-implementation",
          project_charter_id: "TODO-project-charter-implementation",
          created_at: "2024-12-01T00:00:00.000Z",
          created_by: "auth0|123456789",
          updated_at: null,
          updated_by: null,
          deleted_at: null,
          deleted_by: null,
        },
        {
          id: "423e4567-e89b-12d3-a456-426614174003",
          organization_id: "123e4567-e89b-12d3-a456-************",
          project_category: "Digital Marketing",
          project_name: "Q1 2025 Social Media Campaign",
          pic_project: "223e4567-e89b-12d3-a456-************",
          start_project: "2025-01-01",
          end_project: "2025-03-31",
          status_project: "In Progress",
          budget_project: "200000000",
          objectives:
            "Launch and manage Q1 2025 social media campaign across all platforms.",
          gantt_chart_id: "TODO-gantt-chart-implementation",
          project_charter_id: "TODO-project-charter-implementation",
          created_at: "2024-11-15T00:00:00.000Z",
          created_by: "auth0|123456789",
          updated_at: "2024-12-01T00:00:00.000Z",
          updated_by: "auth0|987654321",
          deleted_at: null,
          deleted_by: null,
        },
      ],
      result: {
        total: 2,
        page: 1,
        pageSize: 10,
        pageCount: 1,
      },
    },
  },
  getProjectByIdExample: {
    summary: "Example get project by ID response",
    value: {
      success: true,
      message: "Project retrieved successfully",
      data: {
        id: "323e4567-e89b-12d3-a456-426614174002",
        organization_id: "123e4567-e89b-12d3-a456-************",
        project_category: "Brand Strategy",
        project_name: "Brand Refresh 2025",
        pic_project: "223e4567-e89b-12d3-a456-************",
        start_project: "2025-01-01",
        end_project: "2025-06-30",
        status_project: "Not Started",
        budget_project: "500000000",
        objectives:
          "Develop and implement a comprehensive brand refresh strategy for 2025, including visual identity, messaging, and market positioning.",
        gantt_chart_id: "TODO-gantt-chart-implementation",
        project_charter_id: "TODO-project-charter-implementation",
        created_at: "2024-12-01T00:00:00.000Z",
        created_by: "auth0|123456789",
        updated_at: null,
        updated_by: null,
        deleted_at: null,
        deleted_by: null,
      },
    },
  },
  updateProjectExample: {
    summary: "Example update project request",
    value: {
      project_name: "Brand Refresh 2025 - Phase 1",
      status_project: ProjectStatus.IN_PROGRESS,
      budget_project: "550000000",
      objectives:
        "Updated: Develop and implement Phase 1 of the brand refresh strategy for 2025, focusing on visual identity and market positioning.",
    },
  },
  updateProjectResponseExample: {
    summary: "Example update project response",
    value: {
      success: true,
      message: "Project updated successfully",
      data: {
        id: "323e4567-e89b-12d3-a456-426614174002",
        organization_id: "123e4567-e89b-12d3-a456-************",
        project_category: "Brand Strategy",
        project_name: "Brand Refresh 2025 - Phase 1",
        pic_project: "223e4567-e89b-12d3-a456-************",
        start_project: "2025-01-01",
        end_project: "2025-06-30",
        status_project: "In Progress",
        budget_project: "550000000",
        objectives:
          "Updated: Develop and implement Phase 1 of the brand refresh strategy for 2025, focusing on visual identity and market positioning.",
        gantt_chart_id: "TODO-gantt-chart-implementation",
        project_charter_id: "TODO-project-charter-implementation",
        created_at: "2024-12-01T00:00:00.000Z",
        created_by: "auth0|123456789",
        updated_at: "2024-12-02T00:00:00.000Z",
        updated_by: "auth0|987654321",
        deleted_at: null,
        deleted_by: null,
      },
    },
  },
  deleteProjectResponseExample: {
    summary: "Example delete project response",
    value: {
      success: true,
      message: "Project deleted successfully",
      data: {
        id: "323e4567-e89b-12d3-a456-426614174002",
      },
    },
  },
  errorResponseExample: {
    summary: "Example error response",
    value: {
      success: false,
      message: "Project not found",
      error: "No project found with the provided ID",
    },
  },
  validationErrorExample: {
    summary: "Example validation error response",
    value: {
      success: false,
      message: "Validation error",
      error:
        "Missing required fields: project_name, organization_id, pic_project",
    },
  },
  unauthorizedErrorExample: {
    summary: "Example unauthorized error response",
    value: {
      success: false,
      message: "Unauthorized",
      error: "Only Managers and Staff Operations can perform this action",
    },
  },
  dashboardSummaryExample: {
    summary: "Example dashboard summary response",
    value: {
      success: true,
      message: "Dashboard summary retrieved successfully",
      data: {
        projects: {
          total: 15,
          by_status: {
            not_started: 3,
            in_progress: 8,
            completed: 3,
            cancelled: 1,
          },
          by_category: {
            "Digital Marketing": 5,
            "Brand Strategy": 4,
            Monitoring: 3,
            "Preliminary Research": 2,
            "Brand Audit": 1,
          },
          recent: [
            {
              id: "123e4567-e89b-12d3-a456-************",
              project_name: "Website Redesign 2025",
              organization_name: "Acme Corp",
              pic_name: "John Doe",
              status_project: "In Progress",
              progress_percentage: 45,
              days_remaining: 60,
            },
            {
              id: "223e4567-e89b-12d3-a456-************",
              project_name: "Social Media Campaign Q2",
              organization_name: "TechNova Solutions",
              pic_name: "Jane Smith",
              status_project: "Not Started",
              progress_percentage: 0,
              days_remaining: 90,
            },
          ],
          upcoming_deadlines: [
            {
              id: "323e4567-e89b-12d3-a456-426614174002",
              project_name: "Brand Refresh 2025",
              end_project: "2025-03-15",
              days_remaining: 15,
              progress_percentage: 85,
            },
            {
              id: "423e4567-e89b-12d3-a456-426614174003",
              project_name: "Q1 Marketing Analysis",
              end_project: "2025-03-31",
              days_remaining: 30,
              progress_percentage: 60,
            },
          ],
        },
        kpis: {
          total: 25,
          by_status: {
            not_started: 5,
            in_progress: 12,
            completed_below_target: 2,
            completed_on_target: 4,
            completed_above_target: 2,
          },
        },
        tasks: {
          total: 120,
          completed: 75,
          in_progress: 30,
          not_started: 15,
          overdue: 5,
        },
        organization: {
          id: "523e4567-e89b-12d3-a456-426614174004",
          name: "Acme Corp",
          projects_count: 8,
          active_projects_count: 5,
          completed_projects_count: 3,
        },
      },
    },
  },
  projectDashboardExample: {
    summary: "Example project dashboard response",
    value: {
      success: true,
      message: "Project dashboard data retrieved successfully",
      data: {
        id: "323e4567-e89b-12d3-a456-426614174002",
        project_name: "Brand Refresh 2025",
        organization_id: "123e4567-e89b-12d3-a456-************",
        organization_name: "Acme Corp",
        pic_project: "223e4567-e89b-12d3-a456-************",
        pic_name: "John Doe",
        project_category: "Brand Strategy",
        start_project: "2025-01-01",
        end_project: "2025-06-30",
        status_project: "In Progress",
        objectives:
          "Develop and implement a comprehensive brand refresh strategy for 2025, including visual identity, messaging, and market positioning.",
        budget_project: "500000000",

        progress_percentage: 65,
        days_elapsed: 60,
        days_remaining: 120,
        days_total: 180,

        kpi_status: "in_progress",
        kpi_count: 3,
        kpis: [
          {
            id: "523e4567-e89b-12d3-a456-426614174005",
            description: "Complete brand identity redesign",
            target: "New logo, color palette, and typography system",
            period: "2025-Q1",
            status: "completed_on_target",
          },
          {
            id: "623e4567-e89b-12d3-a456-426614174006",
            description: "Develop brand messaging framework",
            target: "Core messaging, value propositions, and tone of voice",
            period: "2025-Q2",
            status: "in_progress",
          },
          {
            id: "723e4567-e89b-12d3-a456-426614174007",
            description: "Launch brand refresh campaign",
            target:
              "Full market rollout with 80% awareness among target audience",
            period: "2025-Q2",
            status: "not_started",
          },
        ],

        tasks_total: 20,
        tasks_completed: 13,
        tasks_in_progress: 5,
        tasks_not_started: 2,
        recent_tasks: [
          {
            id: "823e4567-e89b-12d3-a456-************",
            description: "Finalize logo design",
            completion_status: "completed",
            employee_name: "Jane Smith",
            due_date: "2025-02-15",
          },
          {
            id: "923e4567-e89b-12d3-a456-************",
            description: "Develop brand guidelines document",
            completion_status: "on_progress",
            employee_name: "Bob Johnson",
            due_date: "2025-03-01",
          },
          {
            id: "a23e4567-e89b-12d3-a456-************",
            description: "Create social media templates",
            completion_status: "on_progress",
            employee_name: "Alice Williams",
            due_date: "2025-03-15",
          },
        ],

        weekly_logs_count: 8,
        recent_weekly_logs: [
          {
            id: "b23e4567-e89b-12d3-a456-************",
            week_number: 8,
            week_start_date: "2025-02-17",
            week_end_date: "2025-02-21",
            notes_count: 5,
          },
          {
            id: "c23e4567-e89b-12d3-a456-************",
            week_number: 7,
            week_start_date: "2025-02-10",
            week_end_date: "2025-02-14",
            notes_count: 3,
          },
          {
            id: "d23e4567-e89b-12d3-a456-426614174013",
            week_number: 6,
            week_start_date: "2025-02-03",
            week_end_date: "2025-02-07",
            notes_count: 4,
          },
        ],
      },
    },
  },
};
