import { SalaryPaymentStatus } from "../../../database/models/salary.model";

export const salaryExamples = {
  generateSalaryExample: {
    summary: "Example generate salary request",
    value: {
      employeeId: "8a172b90-b3d5-4f32-b971-3603fb62130a",
      period: "2025-03",
    },
  },
  generateSalaryResponseExample: {
    summary: "Example generate salary response",
    value: {
      success: true,
      message: "Salary generated successfully",
      data: {
        id: "123e4567-e89b-12d3-a456-426614174000",
        employee_id: "8a172b90-b3d5-4f32-b971-3603fb62130a",
        base_salary: 5000000,
        total_bonus: 500000,
        total_deduction: 100000,
        total_allowance: 0,
        total_salary: 5400000,
        payment_status: SalaryPaymentStatus.UNPAID,
        period: "2025-03",
      },
    },
  },
  updateSalaryExample: {
    summary: "Example update salary request",
    value: {
      base_salary: 5500000,
      payment_status: SalaryPaymentStatus.PAID,
    },
  },
  updateSalaryResponseExample: {
    summary: "Example update salary response",
    value: {
      success: true,
      message: "Sal<PERSON> updated successfully",
      data: {
        id: "123e4567-e89b-12d3-a456-426614174000",
        employee_id: "8a172b90-b3d5-4f32-b971-3603fb62130a",
        base_salary: 5500000,
        total_bonus: 500000,
        total_deduction: 100000,
        total_allowance: 0,
        total_salary: 5900000,
        payment_status: SalaryPaymentStatus.PAID,
        period: "2025-03",
      },
    },
  },
};

export const salarySchemas = {
  GenerateSalaryDto: {
    type: "object" as const,
    required: ["employeeId", "period"],
    properties: {
      employeeId: {
        type: "string" as const,
        format: "uuid",
        description: "The employee ID for whom the salary is generated",
      },
      period: {
        type: "string" as const,
        pattern: "^\\d{4}-\\d{2}$",
        description: "Salary period in format YYYY-MM",
      },
    },
  },
  UpdateSalaryDto: {
    type: "object" as const,
    properties: {
      base_salary: {
        type: "number" as const,
        minimum: 0,
        description: "Base salary amount",
      },
      payment_status: {
        type: "string" as const,
        enum: Object.values(SalaryPaymentStatus),
        description: "Payment status of the salary",
      },
    },
  },
};
