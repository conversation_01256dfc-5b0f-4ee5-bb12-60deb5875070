// Note: TaskStatus enum still exists for project tasks

// Define module schemas for Swagger documentation (regular tasks only)
export const taskSchemas = {
  Task: {
    type: "object" as const,
    required: [
      "id",
      "description",
      "completion_status",
      "employee_id",
      "due_date",
      "created_at",
      "created_by",
    ],
    properties: {
      id: {
        type: "string" as const,
        format: "uuid",
        description: "Unique identifier",
      },
      description: {
        type: "string" as const,
        description: "Task description",
      },
      completion_status: {
        type: "boolean" as const,
        description:
          "Task completion status (true = completed, false = not completed)",
      },
      employee_id: {
        type: "string" as const,
        format: "uuid",
        description: "Employee ID assigned to the task",
      },
      due_date: {
        type: "string" as const,
        format: "date",
        description: "Due date in format YYYY-MM-DD",
      },
      attendance_id: {
        type: "string" as const,
        format: "uuid",
        description: "Attendance ID (optional)",
        nullable: true,
      },
      created_at: {
        type: "string" as const,
        format: "date-time",
        description: "Creation timestamp",
      },
      created_by: {
        type: "string" as const,
        format: "uuid",
        description: "User ID who created the record",
      },
      updated_at: {
        type: "string" as const,
        format: "date-time",
        description: "Last update timestamp",
        nullable: true,
      },
      updated_by: {
        type: "string" as const,
        format: "uuid",
        description: "User ID who last updated the record",
        nullable: true,
      },
      deleted_at: {
        type: "string" as const,
        format: "date-time",
        description: "Deletion timestamp",
        nullable: true,
      },
      deleted_by: {
        type: "string" as const,
        format: "uuid",
        description: "User ID who deleted the record",
        nullable: true,
      },
    },
  },

  UpdateTaskStatusDto: {
    type: "object" as const,
    required: ["completion_status"],
    properties: {
      completion_status: {
        type: "boolean" as const,
        description:
          "Task completion status (true = completed, false = not completed)",
      },
    },
  },
};

// Define examples separately
export const taskExamples = {
  updateTaskStatusExample: {
    summary: "Update task status example",
    value: {
      completion_status: true,
    },
  },
};
