// Define weekly log schema examples
export const weeklyLogExamples = {
  getWeeklyLogExample: {
    summary: "Example weekly log response",
    value: {
      success: true,
      message: "Weekly log retrieved successfully",
      data: {
        id: "123e4567-e89b-12d3-a456-426614174000",
        week_number: 42,
        week_start_date: "2023-10-16",
        week_end_date: "2023-10-22",
        project_id: "123e4567-e89b-12d3-a456-426614174001",
        project_name: "Example Project",
        created_at: "2023-10-16T00:00:00.000Z",
        created_by: "123e4567-e89b-12d3-a456-426614174002",
        updated_at: null,
        updated_by: null,
        deleted_at: null,
        deleted_by: null,
        notes_by_day: {
          "1": {
            id: "123e4567-e89b-12d3-a456-426614174003",
            note: "Started implementation of feature X",
            weekly_log_id: "123e4567-e89b-12d3-a456-426614174000",
            day_of_week: 1,
            created_at: "2023-10-16T10:00:00.000Z",
            created_by: "123e4567-e89b-12d3-a456-426614174002",
            updated_at: null,
            updated_by: null,
            deleted_at: null,
            deleted_by: null,
          },
          "2": {
            id: "123e4567-e89b-12d3-a456-426614174004",
            note: "Completed feature X implementation",
            weekly_log_id: "123e4567-e89b-12d3-a456-426614174000",
            day_of_week: 2,
            created_at: "2023-10-17T10:00:00.000Z",
            created_by: "123e4567-e89b-12d3-a456-426614174002",
            updated_at: null,
            updated_by: null,
            deleted_at: null,
            deleted_by: null,
          },
        },
        tasks: [
          {
            id: "123e4567-e89b-12d3-a456-426614174005",
            description: "Implement feature X",
            completion_status: "completed",
            // Other task fields...
          },
        ],
        days_data: {
          "1": {
            date: "2023-10-16",
            day_of_week: 1,
            note: {
              id: "123e4567-e89b-12d3-a456-426614174003",
              note: "Started implementation of feature X",
              weekly_log_id: "123e4567-e89b-12d3-a456-426614174000",
              day_of_week: 1,
              created_at: "2023-10-16T10:00:00.000Z",
              created_by: "123e4567-e89b-12d3-a456-426614174002",
              updated_at: null,
              updated_by: null,
              deleted_at: null,
              deleted_by: null,
            },
            activities: {
              starting: [
                {
                  id: "123e4567-e89b-12d3-a456-426614174005",
                  description: "Implement feature X",
                  completion_status: "not_completed",
                  initial_date: "2023-10-16",
                  due_date: "2023-10-20",
                  // Other task fields...
                },
              ],
              ending: [],
              ongoing: [],
              not_completed: [
                {
                  id: "123e4567-e89b-12d3-a456-426614174005",
                  description: "Implement feature X",
                  completion_status: "not_completed",
                  initial_date: "2023-10-16",
                  due_date: "2023-10-20",
                  // Other task fields...
                },
              ],
              on_progress: [],
              completed: [],
            },
          },
          "2": {
            date: "2023-10-17",
            day_of_week: 2,
            note: {
              id: "123e4567-e89b-12d3-a456-426614174004",
              note: "Completed feature X implementation",
              weekly_log_id: "123e4567-e89b-12d3-a456-426614174000",
              day_of_week: 2,
              created_at: "2023-10-17T10:00:00.000Z",
              created_by: "123e4567-e89b-12d3-a456-426614174002",
              updated_at: null,
              updated_by: null,
              deleted_at: null,
              deleted_by: null,
            },
            activities: {
              starting: [],
              ending: [],
              ongoing: [
                {
                  id: "123e4567-e89b-12d3-a456-426614174005",
                  description: "Implement feature X",
                  completion_status: "on_progress",
                  initial_date: "2023-10-16",
                  due_date: "2023-10-20",
                  // Other task fields...
                },
              ],
              not_completed: [],
              on_progress: [
                {
                  id: "123e4567-e89b-12d3-a456-426614174005",
                  description: "Implement feature X",
                  completion_status: "on_progress",
                  initial_date: "2023-10-16",
                  due_date: "2023-10-20",
                  // Other task fields...
                },
              ],
              completed: [],
            },
          },
        },
      },
    },
  },

  updateNotesExample: {
    summary: "Example batch update notes request",
    value: {
      notes: {
        "1": "Started implementation of feature X", // Monday - create or update
        "2": "Continued work on feature X", // Tuesday - create or update
        "3": "", // Wednesday - delete if exists
        // Thursday and Friday not included - leave unchanged
      },
    },
  },
  batchUpdateResultExample: {
    summary: "Example batch update result",
    value: {
      success: true,
      message: "Notes updated successfully",
      data: {
        created: 1,
        updated: 1,
        deleted: 1,
        unchanged: 2,
      },
    },
  },
  availableWeekNumbersExample: {
    summary: "Example available week numbers response",
    value: {
      success: true,
      message: "Available week numbers retrieved successfully",
      data: {
        week_numbers: [42, 41, 40, 39, 38],
        week_numbers_with_ranges: [
          { week_number: 42, date_range: "Oct 16 - Oct 22, 2023" },
          { week_number: 41, date_range: "Oct 9 - Oct 15, 2023" },
          { week_number: 40, date_range: "Oct 2 - Oct 8, 2023" },
          { week_number: 39, date_range: "Sep 25 - Oct 1, 2023" },
          { week_number: 38, date_range: "Sep 18 - Sep 24, 2023" },
        ],
      },
    },
  },

  // Add new examples for testing
  testGetByProjectAndWeekExample: {
    summary: "Example test response for getByProjectAndWeek",
    value: {
      success: true,
      message: "Weekly log retrieved successfully",
      data: {
        id: "123e4567-e89b-12d3-a456-426614174000",
        week_number: 42,
        week_start_date: "2023-10-16",
        week_end_date: "2023-10-22",
        project_id: "123e4567-e89b-12d3-a456-426614174001",
        project_name: "Test Project",
        notes_by_day: {},
        tasks: [],
        days_data: {
          "1": {
            date: "2023-10-16",
            day_of_week: 1,
            note: null,
            activities: {
              starting: [],
              ending: [],
              ongoing: [],
              not_completed: [],
              on_progress: [],
              completed: []
            }
          }
        }
      }
    }
  },

  testUpdateNotesExample: {
    summary: "Example test request for updateNotes",
    value: {
      notes: {
        "1": "Test note for Monday",
        "2": "Test note for Tuesday",
        "3": "Test note for Wednesday"
      }
    }
  },

  testUpdateNotesResponseExample: {
    summary: "Example test response for updateNotes",
    value: {
      success: true,
      message: "Notes updated successfully",
      data: {
        created: 3,
        updated: 0,
        deleted: 0,
        unchanged: 2
      }
    }
  },

  testGetAvailableWeekNumbersExample: {
    summary: "Example test response for getAvailableWeekNumbers",
    value: {
      success: true,
      message: "Available week numbers retrieved successfully",
      data: {
        week_numbers: [42, 41, 40],
        week_numbers_with_ranges: [
          { week_number: 42, date_range: "Oct 16 - Oct 22, 2023" },
          { week_number: 41, date_range: "Oct 9 - Oct 15, 2023" },
          { week_number: 40, date_range: "Oct 2 - Oct 8, 2023" }
        ]
      }
    }
  }
};

// Define weekly log schema components
export const weeklyLogSchemas = {
  WeeklyLog: {
    type: "object" as const,
    properties: {
      id: {
        type: "string" as const,
        format: "uuid",
        description: "Weekly log ID",
      },
      week_number: {
        type: "integer" as const,
        description: "Project-specific week number (starts from 1)",
      },
      week_start_date: {
        type: "string" as const,
        format: "date",
        description: "Start date of the week (Monday) in YYYY-MM-DD format",
      },
      week_end_date: {
        type: "string" as const,
        format: "date",
        description: "End date of the week (Sunday) in YYYY-MM-DD format",
      },
      project_id: {
        type: "string" as const,
        format: "uuid",
        description: "Project ID",
      },
      created_at: {
        type: "string" as const,
        format: "date-time",
        description: "Creation timestamp",
      },
      created_by: {
        type: "string" as const,
        format: "uuid",
        description: "User ID who created the record",
      },
      updated_at: {
        type: "string" as const,
        format: "date-time",
        description: "Last update timestamp",
        nullable: true,
      },
      updated_by: {
        type: "string" as const,
        format: "uuid",
        description: "User ID who last updated the record",
        nullable: true,
      },
      deleted_at: {
        type: "string" as const,
        format: "date-time",
        description: "Deletion timestamp",
        nullable: true,
      },
      deleted_by: {
        type: "string" as const,
        format: "uuid",
        description: "User ID who deleted the record",
        nullable: true,
      },
    },
  },
  WeeklyLogNote: {
    type: "object" as const,
    properties: {
      id: {
        type: "string" as const,
        format: "uuid",
        description: "Note ID",
      },
      note: {
        type: "string" as const,
        description: "Note text",
      },
      weekly_log_id: {
        type: "string" as const,
        format: "uuid",
        description: "Weekly log ID",
      },
      day_of_week: {
        type: "integer" as const,
        minimum: 1,
        maximum: 5,
        description: "Day of week (1-5, where 1 is Monday, 5 is Friday)",
      },
      created_at: {
        type: "string" as const,
        format: "date-time",
        description: "Creation timestamp",
      },
      created_by: {
        type: "string" as const,
        format: "uuid",
        description: "User ID who created the record",
      },
      updated_at: {
        type: "string" as const,
        format: "date-time",
        description: "Last update timestamp",
        nullable: true,
      },
      updated_by: {
        type: "string" as const,
        format: "uuid",
        description: "User ID who last updated the record",
        nullable: true,
      },
      deleted_at: {
        type: "string" as const,
        format: "date-time",
        description: "Deletion timestamp",
        nullable: true,
      },
      deleted_by: {
        type: "string" as const,
        format: "uuid",
        description: "User ID who deleted the record",
        nullable: true,
      },
    },
  },
  WeeklyLogDayData: {
    type: "object" as const,
    properties: {
      date: {
        type: "string" as const,
        format: "date",
        description: "Date in YYYY-MM-DD format",
      },
      day_of_week: {
        type: "integer" as const,
        minimum: 1,
        maximum: 5,
        description: "Day of week (1-5, where 1 is Monday, 5 is Friday)",
      },
      note: {
        $ref: "#/components/schemas/WeeklyLogNote",
        description: "Note for this day (if any)",
        nullable: true,
      },
      activities: {
        type: "object" as const,
        description: "Tasks categorized by status and timeline",
        properties: {
          starting: {
            type: "array" as const,
            description: "Tasks that start on this day",
            items: {
              $ref: "#/components/schemas/ProjectTask",
            },
          },
          ending: {
            type: "array" as const,
            description: "Tasks that end on this day",
            items: {
              $ref: "#/components/schemas/ProjectTask",
            },
          },
          ongoing: {
            type: "array" as const,
            description: "Tasks that are ongoing on this day",
            items: {
              $ref: "#/components/schemas/ProjectTask",
            },
          },
          not_completed: {
            type: "array" as const,
            description: "Tasks that are not completed",
            items: {
              $ref: "#/components/schemas/ProjectTask",
            },
          },
          on_progress: {
            type: "array" as const,
            description: "Tasks that are in progress",
            items: {
              $ref: "#/components/schemas/ProjectTask",
            },
          },
          completed: {
            type: "array" as const,
            description: "Tasks that are completed",
            items: {
              $ref: "#/components/schemas/ProjectTask",
            },
          },
        },
      },
    },
  },

  WeeklyLogWithNotes: {
    type: "object" as const,
    allOf: [
      { $ref: "#/components/schemas/WeeklyLog" },
      {
        type: "object" as const,
        properties: {
          project_name: {
            type: "string" as const,
            description: "Project name",
          },
          notes_by_day: {
            type: "object" as const,
            description: "Notes grouped by day of week",
            additionalProperties: {
              $ref: "#/components/schemas/WeeklyLogNote",
            },
          },
          tasks: {
            type: "array" as const,
            description: "Project tasks associated with this weekly log",
            items: {
              $ref: "#/components/schemas/ProjectTask",
            },
          },
          days_data: {
            type: "object" as const,
            description: "Structured data for each day of the week",
            additionalProperties: {
              $ref: "#/components/schemas/WeeklyLogDayData",
            },
          },
        },
      },
    ],
  },

  UpdateNotesRequest: {
    type: "object" as const,
    required: ["notes"],
    properties: {
      notes: {
        type: "object" as const,
        description: "Notes for each day of the week",
        additionalProperties: {
          type: "string" as const,
          description:
            "Note text (non-empty to create/update, empty to delete)",
        },
      },
    },
  },
  BatchUpdateResult: {
    type: "object" as const,
    properties: {
      created: {
        type: "integer" as const,
        description: "Number of notes created",
      },
      updated: {
        type: "integer" as const,
        description: "Number of notes updated",
      },
      deleted: {
        type: "integer" as const,
        description: "Number of notes deleted",
      },
      unchanged: {
        type: "integer" as const,
        description: "Number of days with no changes",
      },
    },
  },

  // Add new schemas for testing
  TestWeeklyLogResponse: {
    type: "object" as const,
    properties: {
      success: {
        type: "boolean" as const,
        description: "Whether the request was successful"
      },
      message: {
        type: "string" as const,
        description: "Response message"
      },
      data: {
        $ref: "#/components/schemas/WeeklyLogWithNotes"
      }
    }
  },

  TestUpdateNotesRequest: {
    type: "object" as const,
    required: ["notes"],
    properties: {
      notes: {
        type: "object" as const,
        description: "Test notes for each day of the week",
        additionalProperties: {
          type: "string" as const,
          description: "Test note text"
        }
      }
    }
  },

  TestUpdateNotesResponse: {
    type: "object" as const,
    properties: {
      success: {
        type: "boolean" as const,
        description: "Whether the request was successful"
      },
      message: {
        type: "string" as const,
        description: "Response message"
      },
      data: {
        $ref: "#/components/schemas/BatchUpdateResult"
      }
    }
  },

  TestAvailableWeekNumbersResponse: {
    type: "object" as const,
    properties: {
      success: {
        type: "boolean" as const,
        description: "Whether the request was successful"
      },
      message: {
        type: "string" as const,
        description: "Response message"
      },
      data: {
        type: "object" as const,
        properties: {
          week_numbers: {
            type: "array" as const,
            items: {
              type: "integer" as const
            },
            description: "List of available week numbers"
          },
          week_numbers_with_ranges: {
            type: "array" as const,
            items: {
              type: "object" as const,
              properties: {
                week_number: {
                  type: "integer" as const,
                  description: "Week number"
                },
                date_range: {
                  type: "string" as const,
                  description: "Formatted date range for the week"
                }
              }
            },
            description: "List of week numbers with their date ranges"
          }
        }
      }
    }
  }
};
