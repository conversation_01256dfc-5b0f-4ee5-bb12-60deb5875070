import { swagger } from "@elysiajs/swagger";
import {
  authExamples,
  authSchemas,
  adminExamples,
  adminSchemas,
  invoiceExamples,
  invoiceSchemas,
  salaryExamples,
  salarySchemas,
  organizationExamples,
  organizationSchemas,
  kpiExamples,
  kpiSchemas,
  employeeExamples,
  employeeSchemas,
  attendanceExamples,
  attendanceSchemas,
  bonusSalaryExamples,
  bonusSalarySchemas,
  deductionSalaryExamples,
  deductionSalarySchemas,
  allowanceSalaryExamples,
  allowanceSalarySchemas,
  taskSchemas,
  taskExamples,
  projectTaskSchemas,
  projectTaskExamples,
  kpiProjectSchemas,
  kpiProjectExamples,
  weeklyLogSchemas,
  weeklyLogExamples,
  projectCharterSchemas,
  projectCharterExamples,
} from "./schemas";

// Define the Swagger documentation configuration
export const swaggerConfig = swagger({
  documentation: {
    info: {
      title: "Kasuat API Documentation",
      version: "1.0.0",
      description:
        "API documentation for the Kasuat application. The Organization module serves as a reference implementation that developers should follow when creating new modules.",
    },
    tags: [
      { name: "auth", description: "Authentication endpoints" },
      {
        name: "examples",
        description: "Example endpoints demonstrating RBAC patterns",
      },
      {
        name: "admin",
        description:
          "Admin endpoints for user management and system administration",
      },
      {
        name: "organizations",
        description: "Organization management endpoints",
      },
      {
        name: "invoices",
        description: "Invoice management endpoints",
      },
      {
        name: "employees",
        description: "Employee management endpoints",
      },
      {
        name: "attendances",
        description: "Attendance management endpoints",
      },
      {
        name: "salaries",
        description: "Salary management endpoints",
      },
      {
        name: "kpi",
        description: "KPI management endpoints",
      },
      {
        name: "bonuses",
        description: "Bonus salary management endpoints",
      },
      {
        name: "deductions",
        description: "Deduction salary management endpoints",
      },
      {
        name: "allowances",
        description: "Allowance salary management endpoints",
      },
      {
        name: "Tasks",
        description: "Task management endpoints",
      },
      {
        name: "Project Tasks",
        description: "Project task management endpoints",
      },
      {
        name: "kpi-projects",
        description: "KPI Project management endpoints",
      },
      {
        name: "projects",
        description: "Project management endpoints",
      },
      {
        name: "weekly-logs",
        description: "Weekly log management endpoints",
      },
      {
        name: "project-charters",
        description: "Project Charter management endpoints",
      },
    ],
    components: {
      securitySchemes: {
        bearerAuth: {
          type: "http",
          scheme: "bearer",
          bearerFormat: "JWT",
          description: "JWT Authorization header using the Bearer scheme",
        },
      },
      // Use imported examples and schema definitions
      examples: {
        ...authExamples,
        ...adminExamples,
        ...invoiceExamples,
        ...organizationExamples,
        ...kpiExamples,
        ...employeeExamples,
        ...attendanceExamples,
        ...salaryExamples,
        ...bonusSalaryExamples,
        ...deductionSalaryExamples,
        ...allowanceSalaryExamples,
        ...taskExamples,
        ...projectTaskExamples,
        ...kpiProjectExamples,
        ...weeklyLogExamples,
        ...projectCharterExamples,
      },
      schemas: {
        ...authSchemas,
        ...adminSchemas,
        ...invoiceSchemas,
        ...organizationSchemas,
        ...salarySchemas,
        ...kpiSchemas,
        ...employeeSchemas,
        ...attendanceSchemas,
        ...bonusSalarySchemas,
        ...deductionSalarySchemas,
        ...allowanceSalarySchemas,
        ...taskSchemas,
        ...projectTaskSchemas,
        ...kpiProjectSchemas,
        ...weeklyLogSchemas,
        ...projectCharterSchemas,
      },
    },
    security: [{ bearerAuth: [] }],
  },
  path: "/docs",
  version: "3.1.0",
});
