# Auth User Cleanup Guide

This guide explains how to clean up orphaned auth users in Supabase. An "orphaned auth user" is an entry in the `auth.users` table that doesn't have a corresponding entry in the `public.user_profiles` table.

## Why Clean Up Orphaned Auth Users?

In our application:

1. When users are deleted through the admin panel, only their profile records are hard deleted
2. The corresponding auth.users records are left intact
3. Over time, this can lead to unnecessary entries in the auth.users table

## Available SQL Functions

The following SQL functions are provided in `migrations/009_cleanup_orphaned_auth_users.sql`:

### 1. Find Orphaned Auth Users

```sql
SELECT * FROM find_orphaned_auth_users();
```

This function returns a list of all auth users that don't have corresponding profile records.

### 2. Delete a Specific Auth User

```sql
SELECT delete_auth_user('00000000-0000-0000-0000-000000000000');
```

Replace the UUID with the actual `user_id` of the auth user you want to delete. This function:

- First checks if the user has a profile (as a safety check)
- If no profile exists, it deletes the auth user
- Returns a text message indicating success or failure

### 3. Delete All Orphaned Auth Users

```sql
SELECT * FROM delete_all_orphaned_auth_users();
```

**WARNING: This will delete ALL auth users that don't have corresponding profiles.**
Use with extreme caution, especially in production environments.

## How to Run These Functions

1. Open the Supabase dashboard for your project
2. Navigate to the SQL Editor
3. Enter one of the SQL commands above
4. Click "Run" to execute the command

## Best Practices

1. Always run `find_orphaned_auth_users()` first to check which users will be affected
2. For production environments, prefer deleting users individually with `delete_auth_user()` rather than the bulk delete function
3. Consider performing these operations during low-traffic periods
4. Always have a backup of your database before performing any bulk delete operations

## Troubleshooting

If you encounter errors when running these functions:

1. Check that the SQL functions have been properly created in your database
2. Verify that you have the necessary permissions to delete users from the auth schema
3. Look for any active connections or foreign key constraints that might be preventing deletion
