# Database Setup and Migrations

## Initial Setup

Before running migrations, you need to set up the required functions in your Supabase project:

1. Log in to your Supabase dashboard
2. Go to the SQL Editor
3. Execute the SQL in `setup_functions.sql`

This creates the `exec_sql` function which is necessary for running migrations from your application.

## Supabase Extensions

According to Supabase documentation:

> Currently uuid-ossp extension is enabled by default and cannot be disabled.

This means you don't need to manually enable the uuid-ossp extension - it's already available in your Supabase project.

## Running Migrations

After setting up the functions, you can run migrations with:

```bash
bun db:migrate
```

## Troubleshooting

If you encounter errors:

- **exec_sql function not found**: Make sure you've run the `setup_functions.sql` script in your Supabase SQL editor
- **uuid_generate_v4() not found**: Verify your Supabase instance is properly configured. According to the documentation, this extension should be enabled by default.
- **Permission errors**: Make sure your service role has the appropriate permissions to execute the function

## Migration Files

Migrations are numbered and executed in order. Each migration should be idempotent (can be run multiple times without errors).

- `000_create_extensions.sql`: Verifies required PostgreSQL extensions are available
- `001_create_notes.sql`: Creates the notes table
- `002_add_rls.sql`: Adds row-level security policies
- `003_add_status_to_notes.sql`: Adds status field to notes table
