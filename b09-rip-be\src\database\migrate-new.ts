import { supabase } from "../libs/supabase";
import fs from "fs";
import path from "path";

/**
 * Check if the exec_sql function exists in the database
 */
async function checkExecSqlFunction() {
  try {
    // Try to call the function with a simple statement to check if it exists
    const { error } = await supabase.rpc("exec_sql", { sql: "SELECT 1;" });

    if (error) {
      // If there's an error with code PGRST202, the function doesn't exist
      if (error.code === "PGRST202") {
        console.error(
          "Error: The exec_sql function does not exist in your Supabase database."
        );
        console.error(
          "Please run the setup_functions.sql script in your Supabase SQL editor first."
        );
        process.exit(1);
      }
      // Other errors might be permission issues
      console.error("Error checking exec_sql function:", error.message);
      process.exit(1);
    }

    console.log(
      "exec_sql function is available, proceeding with migrations..."
    );
    return true;
  } catch (error) {
    console.error("Error checking exec_sql function:", error);
    process.exit(1);
  }
}

/**
 * Function to run SQL migrations
 */
async function runMigration() {
  // First check if the exec_sql function exists
  await checkExecSqlFunction();

  const migrationsDir = path.join(__dirname, "migrations-new");
  const migrationFiles = fs
    .readdirSync(migrationsDir)
    .filter((file) => file.endsWith(".sql"))
    .sort();

  console.log("Starting migrations...");

  for (const file of migrationFiles) {
    console.log(`Running migration: ${file}`);
    const sql = fs.readFileSync(path.join(migrationsDir, file), "utf8");

    try {
      // For Supabase, we need a stored procedure to execute SQL
      const { error } = await supabase.rpc("exec_sql", { sql });

      if (error) {
        console.error(`Error: ${error.message}`);
        throw error;
      }

      console.log(`Successfully executed: ${file}`);
    } catch (error) {
      console.error(`Error executing ${file}:`, error);
      process.exit(1);
    }
  }

  console.log("All migrations completed successfully!");
}

// Run the migration
runMigration().catch((error) => {
  console.error("Migration failed:", error);
  process.exit(1);
});
