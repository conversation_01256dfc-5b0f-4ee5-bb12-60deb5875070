-- Create user_role enum if it doesn't exist
DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'user_role') THEN
        CREATE TYPE public.user_role AS ENUM ('Admin', 'Manager', 'HR', 'Finance', 'Operation', 'Client');
    END IF;
END$$;

-- Create user_profiles table
CREATE TABLE IF NOT EXISTS public.user_profiles (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  user_id UUID NOT NULL UNIQUE,
  fullname TEXT NOT NULL,
  phonenum TEXT NOT NULL,
  role user_role NOT NULL,
  org_id UUID,
  employee_id UUID,
  is_active BOOLEAN NOT NULL DEFAULT false,
  created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
  created_by <PERSON><PERSON><PERSON> NOT NULL,
  updated_at TIMESTAMPTZ,
  updated_by <PERSON><PERSON><PERSON>,
  deleted_at TIMESTAMPTZ,
  deleted_by UUID
);

-- <PERSON>reate indexes for better performance
CREATE INDEX IF NOT EXISTS idx_user_profiles_user_id ON public.user_profiles(user_id);
CREATE INDEX IF NOT EXISTS idx_user_profiles_role ON public.user_profiles(role);
