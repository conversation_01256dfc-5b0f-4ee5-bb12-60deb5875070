-- Create presence_status enum if it doesn't exist
DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'presence_status') THEN
        CREATE TYPE public.presence_status AS ENUM ('present', 'absent', 'permit', 'leave');
    END IF;
END$$;

-- Create employees table
CREATE TABLE IF NOT EXISTS public.employees (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  profile_id UUID UNIQUE NOT NULL,
  dob TEXT NOT NULL CHECK (dob ~ '^\d{4}-\d{2}-\d{2}$'),
  address TEXT NOT NULL,
  bank_account TEXT NOT NULL,
  bank_name TEXT NOT NULL,
  employment_status TEXT NOT NULL,
  presence_status presence_status NOT NULL,
  department TEXT NOT NULL,
  start_date TEXT NOT NULL CHECK (start_date ~ '^\d{4}-\d{2}-\d{2}$'),
  created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
  created_by UUID NOT NULL,
  updated_at TIMESTAMPTZ,
  updated_by UUI<PERSON>,
  deleted_at TIMESTAMPTZ,
  deleted_by UUID
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_employees_profile_id ON public.employees(profile_id);
