-- Create salary_payment_status enum if it doesn't exist
DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'salary_payment_status') THEN
        CREATE TYPE public.salary_payment_status AS ENUM ('unpaid', 'paid');
    END IF;
END$$;

-- Create salaries table
CREATE TABLE IF NOT EXISTS public.salaries (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  employee_id UUID NOT NULL,
  base_salary NUMERIC NOT NULL,
  total_bonus NUMERIC NOT NULL DEFAULT 0,
  total_deduction NUMERIC NOT NULL DEFAULT 0,
  total_allowance NUMERIC NOT NULL DEFAULT 0,
  total_salary NUMERIC NOT NULL,
  payment_status salary_payment_status NOT NULL DEFAULT 'unpaid',
  period TEXT NOT NULL CHECK (period ~ '^\d{4}-\d{2}$'),
  created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
  created_by UUID NOT NULL,
  updated_at TIMESTAMPTZ,
  updated_by UUI<PERSON>,
  deleted_at TIMESTAMPTZ,
  deleted_by UUID
);

-- <PERSON><PERSON> indexes for better performance
CREATE INDEX IF NOT EXISTS idx_salaries_employee_id ON public.salaries(employee_id);
CREATE INDEX IF NOT EXISTS idx_salaries_period ON public.salaries(period);
