-- Create kpi_status enum if it doesn't exist
DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'kpi_status') THEN
        CREATE TYPE public.kpi_status AS ENUM ('not_started', 'in_progress', 'completed_below_target', 'completed_on_target', 'completed_above_target');
    END IF;
END$$;

-- Create kpis table
CREATE TABLE IF NOT EXISTS public.kpis (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  full_name TEXT NOT NULL,
  employee_id UUID NOT NULL,
  description TEXT NOT NULL,
  target TEXT NOT NULL,
  period TEXT NOT NULL,
  status kpi_status NOT NULL DEFAULT 'not_started',
  bonus_received NUMERIC,
  additional_notes TEXT,
  created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
  created_by UUID NOT NULL,
  updated_at TIMESTAMPTZ,
  updated_by <PERSON><PERSON><PERSON>,
  deleted_at TIMESTAMPTZ,
  deleted_by UUID
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_kpis_employee_id ON public.kpis(employee_id);
CREATE INDEX IF NOT EXISTS idx_kpis_status ON public.kpis(status);
