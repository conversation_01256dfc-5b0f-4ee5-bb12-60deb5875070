-- Create attendances table
CREATE TABLE IF NOT EXISTS public.attendances (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  employee_id UUID NOT NULL,
  date TEXT NOT NULL CHECK (date ~ '^\d{4}-\d{2}-\d{2}$'),
  clock_in TEXT CHECK (clock_in ~ '^\d{2}:\d{2}:\d{2}$'),
  clock_out TEXT CHECK (clock_out ~ '^\d{2}:\d{2}:\d{2}$'),
  status TEXT NOT NULL,
  notes TEXT,
  created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
  created_by UUID NOT NULL,
  updated_at TIMESTAMPTZ,
  updated_by UUI<PERSON>,
  deleted_at TIMESTAMPTZ,
  deleted_by UUID
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_attendances_employee_id ON public.attendances(employee_id);
CREATE INDEX IF NOT EXISTS idx_attendances_date ON public.attendances(date);

-- Add comments to document relationships
COMMENT ON TABLE public.attendances IS 'Employee attendance records which can have associated tasks';
