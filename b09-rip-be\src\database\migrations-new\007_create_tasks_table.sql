-- Create tasks table
CREATE TABLE IF NOT EXISTS public.tasks (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  assigned_by TEXT NOT NULL,
  description TEXT NOT NULL,
  completion_status BOOLEAN NOT NULL DEFAULT false,
  employee_id UUID NOT NULL,
  due_date TEXT NOT NULL CHECK (due_date ~ '^\d{4}-\d{2}-\d{2}$'),
  attendance_id UUID,
  created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
  created_by UUID NOT NULL,
  updated_at TIMESTAMPTZ,
  updated_by UUI<PERSON>,
  deleted_at TIMESTAMPTZ,
  deleted_by UUID
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_tasks_employee_id ON public.tasks(employee_id);
CREATE INDEX IF NOT EXISTS idx_tasks_attendance_id ON public.tasks(attendance_id);

-- Add comments to document relationships
COMMENT ON TABLE public.tasks IS 'Tasks assigned to employees, which can be optionally associated with attendance records';
COMMENT ON COLUMN public.tasks.attendance_id IS 'Optional reference to an attendance record';
