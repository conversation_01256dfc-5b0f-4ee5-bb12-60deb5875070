-- Create project_category enum if needed
DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'project_category') THEN
        CREATE TYPE public.project_category AS ENUM ('HCM', 'ORDEV', 'BE', 'IT', 'MARKETING', 'FINANCE', 'SALES', 'OTHER');
    END IF;
END$$;

-- Create project_status enum if needed
DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'project_status') THEN
        CREATE TYPE public.project_status AS ENUM ('not_started', 'in_progress', 'completed', 'cancelled');
    END IF;
END$$;

-- Create projects table
CREATE TABLE IF NOT EXISTS public.projects (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  organization_id UUID NOT NULL,
  project_category TEXT NOT NULL,
  project_name TEXT NOT NULL,
  pic_project UUID NOT NULL,
  start_project TEXT NOT NULL CHECK (start_project ~ '^\d{4}-\d{2}-\d{2}$'),
  end_project TEXT NOT NULL CHECK (end_project ~ '^\d{4}-\d{2}-\d{2}$'),
  status_project TEXT NOT NULL,
  budget_project TEXT NOT NULL,
  gantt_chart_id TEXT,
  project_charter_id TEXT,
  objectives TEXT NOT NULL,
  created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
  created_by UUID NOT NULL,
  updated_at TIMESTAMPTZ,
  updated_by UUID,
  deleted_at TIMESTAMPTZ,
  deleted_by UUID
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_projects_organization_id ON public.projects(organization_id);
CREATE INDEX IF NOT EXISTS idx_projects_pic_project ON public.projects(pic_project);
