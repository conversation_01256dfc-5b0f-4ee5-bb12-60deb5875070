-- Create bonus_type enum if needed
DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'bonus_salary_type') THEN
        CREATE TYPE public.bonus_salary_type AS ENUM ('kpi', 'project', 'performance', 'other');
    END IF;
END$$;

-- Create bonus_salaries table
CREATE TABLE IF NOT EXISTS public.bonus_salaries (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  salary_id UUID NOT NULL,
  amount NUMERIC NOT NULL,
  bonus_type TEXT NOT NULL,
  notes TEXT,
  kpi_id UUID,
  project_id UUID,
  created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
  created_by UUID NOT NULL,
  updated_at TIMESTAMPTZ,
  updated_by UUI<PERSON>,
  deleted_at TIMESTAMPTZ,
  deleted_by UUID
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_bonus_salaries_salary_id ON public.bonus_salaries(salary_id);
