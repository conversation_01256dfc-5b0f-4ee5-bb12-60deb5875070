-- Create deduction_type enum if needed
DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'deduction_type') THEN
        CREATE TYPE public.deduction_type AS ENUM ('absence', 'late', 'tax', 'loan', 'other');
    END IF;
END$$;

-- Create deduction_salaries table
CREATE TABLE IF NOT EXISTS public.deduction_salaries (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  salary_id UUID NOT NULL,
  amount NUMERIC NOT NULL,
  deduction_type TEXT NOT NULL,
  notes TEXT,
  created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
  created_by UUID NOT NULL,
  updated_at TIMESTAMPTZ,
  updated_by UUID,
  deleted_at TIMESTAMPTZ,
  deleted_by UUID
);

-- Create index for better performance
CREATE INDEX IF NOT EXISTS idx_deduction_salaries_salary_id ON public.deduction_salaries(salary_id);
