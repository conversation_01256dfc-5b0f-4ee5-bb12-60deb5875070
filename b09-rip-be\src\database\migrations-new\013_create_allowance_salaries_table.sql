-- Create allowance_type enum if needed
DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'allowance_salary_type') THEN
        CREATE TYPE public.allowance_salary_type AS ENUM ('transport', 'meal', 'health', 'position', 'tenure', 'thr', 'other');
    END IF;
END$$;

-- Create allowance_salaries table
CREATE TABLE IF NOT EXISTS public.allowance_salaries (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  salary_id UUID NOT NULL,
  amount NUMERIC NOT NULL,
  allowance_type TEXT NOT NULL,
  notes TEXT,
  created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
  created_by UUID NOT NULL,
  updated_at TIMESTAMPTZ,
  updated_by UUID,
  deleted_at TIMESTAMPTZ,
  deleted_by UUID
);

-- Create index for better performance
CREATE INDEX IF NOT EXISTS idx_allowance_salaries_salary_id ON public.allowance_salaries(salary_id);
