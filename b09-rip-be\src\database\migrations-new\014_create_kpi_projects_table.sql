-- Create kpi_projects table
CREATE TABLE IF NOT EXISTS public.kpi_projects (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  project_name TEXT NOT NULL,
  project_id UUID NOT NULL,
  description TEXT NOT NULL,
  target TEXT NOT NULL,
  period TEXT NOT NULL,
  status kpi_status NOT NULL DEFAULT 'not_started',
  additional_notes TEXT,
  created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
  created_by UUID NOT NULL,
  updated_at TIMESTAMPTZ,
  updated_by UUID,
  deleted_at TIMESTAMPTZ,
  deleted_by UUID
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_kpi_projects_project_id ON public.kpi_projects(project_id);
CREATE INDEX IF NOT EXISTS idx_kpi_projects_status ON public.kpi_projects(status);
