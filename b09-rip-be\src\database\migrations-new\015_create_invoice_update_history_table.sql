-- Create invoice_update_history table
CREATE TABLE IF NOT EXISTS public.invoice_update_history (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  invoice_id UUID NOT NULL,
  change_description TEXT NOT NULL, -- <PERSON>SO<PERSON> string of changes
  created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
  created_by UUID NOT NULL,
  updated_at TIMESTAMPTZ,
  updated_by U<PERSON><PERSON>,
  deleted_at TIMESTAMPTZ,
  deleted_by UUID
);

-- Create index for faster lookups by invoice_id
CREATE INDEX IF NOT EXISTS idx_invoice_update_history_invoice_id ON public.invoice_update_history(invoice_id);
