-- Create salary_update_history table
CREATE TABLE IF NOT EXISTS public.salary_update_history (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  salary_id UUID NOT NULL,
  change_description TEXT NOT NULL, -- J<PERSON><PERSON> string of changes
  created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
  created_by UUID NOT NULL,
  updated_at TIMESTAMPTZ,
  updated_by UUID,
  deleted_at TIMESTAMPTZ,
  deleted_by UUID
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_salary_update_history_salary_id ON public.salary_update_history(salary_id);
