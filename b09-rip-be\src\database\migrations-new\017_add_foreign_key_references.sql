-- Add foreign key references to all tables
-- This migration adds referential integrity constraints after all tables have been created

-- User Profiles references
ALTER TABLE public.user_profiles
ADD CONSTRAINT fk_user_profiles_org_id
FOREIGN KEY (org_id) REFERENCES public.organizations(id)
ON DELETE SET NULL ON UPDATE CASCADE;

-- Employees references
ALTER TABLE public.employees
ADD CONSTRAINT fk_employees_profile_id
FOREIGN KEY (profile_id) REFERENCES public.user_profiles(id)
ON DELETE CASCADE ON UPDATE CASCADE;

-- Salaries references
ALTER TABLE public.salaries
ADD CONSTRAINT fk_salaries_employee_id
FOREIGN KEY (employee_id) REFERENCES public.employees(id)
ON DELETE CASCADE ON UPDATE CASCADE;

-- K<PERSON>s references
ALTER TABLE public.kpis
ADD CONSTRAINT fk_kpis_employee_id
FOREIGN KEY (employee_id) REFERENCES public.employees(id)
ON DELETE CASCADE ON UPDATE CASCADE;

-- Attendances references
ALTER TABLE public.attendances
ADD CONSTRAINT fk_attendances_employee_id
FOREIG<PERSON> (employee_id) REFERENCES public.employees(id)
ON DELETE CASCADE ON UPDATE CASCADE;

-- Tasks references
ALTER TABLE public.tasks
ADD CONSTRAINT fk_tasks_employee_id
FOREIGN KEY (employee_id) REFERENCES public.employees(id)
ON DELETE CASCADE ON UPDATE CASCADE;

ALTER TABLE public.tasks
ADD CONSTRAINT fk_tasks_attendance_id
FOREIGN KEY (attendance_id) REFERENCES public.attendances(id)
ON DELETE SET NULL ON UPDATE CASCADE;

-- Projects references
ALTER TABLE public.projects
ADD CONSTRAINT fk_projects_organization_id
FOREIGN KEY (organization_id) REFERENCES public.organizations(id)
ON DELETE CASCADE ON UPDATE CASCADE;

ALTER TABLE public.projects
ADD CONSTRAINT fk_projects_pic_project
FOREIGN KEY (pic_project) REFERENCES public.employees(id)
ON DELETE CASCADE ON UPDATE CASCADE;

-- Invoices references
ALTER TABLE public.invoices
ADD CONSTRAINT fk_invoices_project_id
FOREIGN KEY (project_id) REFERENCES public.projects(id)
ON DELETE SET NULL ON UPDATE CASCADE;

-- Invoice Items references
ALTER TABLE public.invoice_items
ADD CONSTRAINT fk_invoice_items_invoice_id
FOREIGN KEY (invoice_id) REFERENCES public.invoices(id)
ON DELETE CASCADE ON UPDATE CASCADE;

-- Bonus Salaries references
ALTER TABLE public.bonus_salaries
ADD CONSTRAINT fk_bonus_salaries_salary_id
FOREIGN KEY (salary_id) REFERENCES public.salaries(id)
ON DELETE CASCADE ON UPDATE CASCADE;

ALTER TABLE public.bonus_salaries
ADD CONSTRAINT fk_bonus_salaries_kpi_id
FOREIGN KEY (kpi_id) REFERENCES public.kpis(id)
ON DELETE SET NULL ON UPDATE CASCADE;

ALTER TABLE public.bonus_salaries
ADD CONSTRAINT fk_bonus_salaries_project_id
FOREIGN KEY (project_id) REFERENCES public.projects(id)
ON DELETE SET NULL ON UPDATE CASCADE;

-- Deduction Salaries references
ALTER TABLE public.deduction_salaries
ADD CONSTRAINT fk_deduction_salaries_salary_id
FOREIGN KEY (salary_id) REFERENCES public.salaries(id)
ON DELETE CASCADE ON UPDATE CASCADE;

-- Allowance Salaries references
ALTER TABLE public.allowance_salaries
ADD CONSTRAINT fk_allowance_salaries_salary_id
FOREIGN KEY (salary_id) REFERENCES public.salaries(id)
ON DELETE CASCADE ON UPDATE CASCADE;

-- KPI Projects references
ALTER TABLE public.kpi_projects
ADD CONSTRAINT fk_kpi_projects_project_id
FOREIGN KEY (project_id) REFERENCES public.projects(id)
ON DELETE CASCADE ON UPDATE CASCADE;

-- Invoice Update History references
ALTER TABLE public.invoice_update_history
ADD CONSTRAINT fk_invoice_update_history_invoice_id
FOREIGN KEY (invoice_id) REFERENCES public.invoices(id)
ON DELETE CASCADE ON UPDATE CASCADE;

-- Salary Update History references
ALTER TABLE public.salary_update_history
ADD CONSTRAINT fk_salary_update_history_salary_id
FOREIGN KEY (salary_id) REFERENCES public.salaries(id)
ON DELETE CASCADE ON UPDATE CASCADE;

-- Project Tasks references
ALTER TABLE public.project_tasks
ADD CONSTRAINT fk_project_tasks_employee_id
FOREIGN KEY (employee_id) REFERENCES public.employees(id)
ON DELETE CASCADE ON UPDATE CASCADE;

ALTER TABLE public.project_tasks
ADD CONSTRAINT fk_project_tasks_project_id
FOREIGN KEY (project_id) REFERENCES public.projects(id)
ON DELETE CASCADE ON UPDATE CASCADE;

ALTER TABLE public.project_tasks
ADD CONSTRAINT fk_project_tasks_assigned_by
FOREIGN KEY (assigned_by) REFERENCES public.employees(id)
ON DELETE CASCADE ON UPDATE CASCADE;

-- Note: weekly_logs table doesn't exist yet, so we're not adding a foreign key constraint for it
-- ALTER TABLE public.project_tasks
-- ADD CONSTRAINT fk_project_tasks_weekly_log_id
-- FOREIGN KEY (weekly_log_id) REFERENCES public.weekly_logs(id)
-- ON DELETE SET NULL ON UPDATE CASCADE;

-- Project Charters references
ALTER TABLE public.project_charters
ADD CONSTRAINT fk_project_charters_project_id
FOREIGN KEY (project_id) REFERENCES public.projects(id)
ON DELETE CASCADE ON UPDATE CASCADE;
