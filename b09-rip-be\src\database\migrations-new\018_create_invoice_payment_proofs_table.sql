-- Create invoice_payment_proofs table
CREATE TABLE IF NOT EXISTS public.invoice_payment_proofs (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  invoice_id UUID NOT NULL,
  file_path TEXT NOT NULL,
  file_name TEXT NOT NULL,
  file_type TEXT NOT NULL,
  file_size INTEGER NOT NULL,
  notes TEXT,
  created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
  created_by UUID NOT NULL,
  updated_at TIMESTAMPTZ,
  updated_by UUI<PERSON>,
  deleted_at TIMESTAMPTZ,
  deleted_by UUID
);

-- Create index for faster lookups by invoice_id
CREATE INDEX IF NOT EXISTS idx_invoice_payment_proofs_invoice_id ON public.invoice_payment_proofs(invoice_id);
