-- Create project_tasks table
CREATE TABLE IF NOT EXISTS public.project_tasks (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  assigned_by UUID NOT NULL,
  description TEXT NOT NULL,
  completion_status TEXT NOT NULL DEFAULT 'not_completed',
  employee_id UUID NOT NULL,
  initial_date TEXT NOT NULL CHECK (initial_date ~ '^\d{4}-\d{2}-\d{2}$'),
  due_date TEXT NOT NULL CHECK (due_date ~ '^\d{4}-\d{2}-\d{2}$'),
  project_id UUID NOT NULL,
  weekly_log_id UUID,
  created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
  created_by UUID NOT NULL,
  updated_at TIMESTAMPTZ,
  updated_by <PERSON>UI<PERSON>,
  deleted_at TIMESTAMPTZ,
  deleted_by UUID
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_project_tasks_employee_id ON public.project_tasks(employee_id);
CREATE INDEX IF NOT EXISTS idx_project_tasks_project_id ON public.project_tasks(project_id);
CREATE INDEX IF NOT EXISTS idx_project_tasks_weekly_log_id ON public.project_tasks(weekly_log_id);

-- Add comments to document relationships
COMMENT ON TABLE public.project_tasks IS 'Tasks assigned to employees for specific projects';
COMMENT ON COLUMN public.project_tasks.completion_status IS 'Task status: not_completed, on_progress, completed';
COMMENT ON COLUMN public.project_tasks.weekly_log_id IS 'Optional reference to a weekly log';
