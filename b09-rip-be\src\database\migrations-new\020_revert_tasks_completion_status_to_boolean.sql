-- Revert regular tasks completion_status from enum back to boolean
-- This migration ONLY affects the 'tasks' table, NOT 'project_tasks' table
-- Project tasks will continue using TaskStatus enum

-- Convert existing enum values to boolean for tasks table ONLY
UPDATE public.tasks 
SET completion_status = CASE 
  WHEN completion_status = 'completed' THEN 'true'
  WHEN completion_status = 'not_completed' THEN 'false'
  WHEN completion_status = 'on_progress' THEN 'false'
  ELSE 'false'
END;

-- Change column type back to boolean for tasks table ONLY
ALTER TABLE public.tasks 
ALTER COLUMN completion_status TYPE BOOLEAN USING completion_status::boolean;

-- Update default value for tasks table ONLY
ALTER TABLE public.tasks 
ALTER COLUMN completion_status SET DEFAULT false;

-- Add comment for tasks table ONLY
COMMENT ON COLUMN public.tasks.completion_status IS 'Task completion status: true = completed, false = not completed';

-- DO NOT MODIFY project_tasks table - it keeps using enum values
-- project_tasks.completion_status remains as TEXT with enum values
