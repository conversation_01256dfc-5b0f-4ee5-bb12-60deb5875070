-- Update project_category enum to match the model values

-- First, check if the project_category column is using the enum type
DO $$
DECLARE
    column_type TEXT;
BEGIN
    -- Get the current type of the project_category column
    SELECT data_type INTO column_type
    FROM information_schema.columns
    WHERE table_name = 'projects' AND column_name = 'project_category';
    
    -- If the column is using an enum type, we need to convert it to TEXT first
    IF column_type = 'USER-DEFINED' THEN
        -- Convert the column to TEXT type temporarily
        ALTER TABLE public.projects ALTER COLUMN project_category TYPE TEXT;
    END IF;
END$$;

-- Drop the existing enum type if it exists
DO $$
BEGIN
    IF EXISTS (SELECT 1 FROM pg_type WHERE typname = 'project_category') THEN
        DROP TYPE public.project_category;
    END IF;
END$$;

-- Create the updated enum type with values matching the model
CREATE TYPE public.project_category AS ENUM (
    'Preliminary Research',
    'Administrasi',
    'Monitoring',
    'Digital Marketing',
    'Brand Audit',
    'Brand Strategy',
    'Draft Monthly Report'
);

-- Update existing data to use the new enum values
-- This assumes existing data can be mapped to the new values
-- You may need to adjust this based on your actual data
UPDATE public.projects
SET project_category = 
    CASE project_category
        WHEN 'HCM' THEN 'Administrasi'
        WHEN 'ORDEV' THEN 'Administrasi'
        WHEN 'BE' THEN 'Administrasi'
        WHEN 'IT' THEN 'Administrasi'
        WHEN 'MARKETING' THEN 'Digital Marketing'
        WHEN 'FINANCE' THEN 'Administrasi'
        WHEN 'SALES' THEN 'Administrasi'
        WHEN 'OTHER' THEN 'Administrasi'
        ELSE project_category
    END;

-- Try to convert the column back to use the enum type
-- This may fail if there are values that don't match the enum
DO $$
BEGIN
    BEGIN
        ALTER TABLE public.projects 
        ALTER COLUMN project_category TYPE public.project_category 
        USING project_category::public.project_category;
    EXCEPTION WHEN OTHERS THEN
        -- If conversion fails, keep it as TEXT type and log a message
        RAISE NOTICE 'Could not convert project_category column to enum type. Keeping as TEXT.';
    END;
END$$;

-- Add a comment to document the change
COMMENT ON COLUMN public.projects.project_category IS 'Project category (Preliminary Research, Administrasi, Monitoring, Digital Marketing, Brand Audit, Brand Strategy, Draft Monthly Report)';
