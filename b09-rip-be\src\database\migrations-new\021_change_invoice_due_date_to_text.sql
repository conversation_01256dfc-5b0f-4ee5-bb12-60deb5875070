-- Change invoice due_date column from TIMESTAMPTZ to TEXT with YYYY-MM-DD format check
-- This migration converts the due_date column to store only the date part without time and timezone

-- First, check if the column exists and is of type TIMESTAMPTZ
DO $$
BEGIN
    IF EXISTS (
        SELECT 1
        FROM information_schema.columns
        WHERE table_name = 'invoices'
        AND column_name = 'due_date'
        AND data_type = 'timestamp with time zone'
    ) THEN
        -- Create a temporary column to hold the converted values
        ALTER TABLE public.invoices ADD COLUMN due_date_temp TEXT;
        
        -- Convert existing TIMESTAMPTZ values to TEXT in YYYY-MM-DD format and store in the temporary column
        UPDATE public.invoices SET due_date_temp = to_char(due_date, 'YYYY-MM-DD');
        
        -- Drop the original column
        ALTER TABLE public.invoices DROP COLUMN due_date;
        
        -- Rename the temporary column to the original name
        ALTER TABLE public.invoices RENAME COLUMN due_date_temp TO due_date;
        
        -- Add NOT NULL constraint and pattern check
        ALTER TABLE public.invoices ALTER COLUMN due_date SET NOT NULL;
        ALTER TABLE public.invoices ADD CONSTRAINT due_date_format CHECK (due_date ~ '^\d{4}-\d{2}-\d{2}$');
    END IF;
END
$$;

-- Add a comment to document the change
COMMENT ON COLUMN public.invoices.due_date IS 'Due date for the invoice (YYYY-MM-DD format without time component)';
