-- Create project_charters table
CREATE TABLE IF NOT EXISTS public.project_charters (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  project_id UUID NOT NULL,
  key_stakeholders TEXT NOT NULL,
  project_authority TEXT NOT NULL,
  project_description TEXT NOT NULL,
  objective_and_key_results TEXT NOT NULL,
  purpose TEXT NOT NULL,
  key_assumption TEXT NOT NULL,
  assumptions_constrains_risks TEXT NOT NULL,
  high_level_resources TEXT NOT NULL,
  high_level_milestones TEXT NOT NULL,
  statement_prediction_of_benefit TEXT NOT NULL,
  approval BOOLEAN NOT NULL,
  created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
  created_by UUID NOT NULL,
  updated_at TIMESTAMPTZ,
  updated_by UUI<PERSON>,
  deleted_at TIMESTAMPTZ,
  deleted_by UUID
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_project_charters_project_id ON public.project_charters(project_id);
