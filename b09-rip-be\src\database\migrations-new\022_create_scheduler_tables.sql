-- Create scheduler_locks table
CREATE TABLE IF NOT EXISTS public.scheduler_locks (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  lock_name VARCHAR(255) NOT NULL UNIQUE,
  locked_at TIMESTAMPTZ NOT NULL,
  locked_by VA<PERSON>HAR(255) NOT NULL,
  created_at TIMESTAMPTZ DEFAULT NOW()
);

-- Create index for faster lookups
CREATE INDEX IF NOT EXISTS idx_scheduler_locks_lock_name ON public.scheduler_locks(lock_name);

-- Create scheduler_runs table
CREATE TABLE IF NOT EXISTS public.scheduler_runs (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  scheduler_name VARCHAR(255) NOT NULL,
  run_at TIMESTAMPTZ NOT NULL,
  status VARCHAR(50) NOT NULL,
  details JSONB,
  created_at TIMESTAMPTZ DEFAULT NOW()
);

-- Create indexes for faster lookups
CREATE INDEX IF NOT EXISTS idx_scheduler_runs_scheduler_name ON public.scheduler_runs(scheduler_name);
CREATE INDEX IF NOT EXISTS idx_scheduler_runs_run_at ON public.scheduler_runs(run_at);

-- Add comments for documentation
COMMENT ON TABLE public.scheduler_locks IS 'Locks for preventing concurrent scheduler runs';
COMMENT ON TABLE public.scheduler_runs IS 'History of scheduler runs for auditing and debugging';
