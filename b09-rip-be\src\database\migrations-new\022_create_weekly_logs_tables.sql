-- Create weekly_logs table
CREATE TABLE IF NOT EXISTS public.weekly_logs (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  week_number INTEGER NOT NULL,
  week_start_date TEXT NOT NULL CHECK (week_start_date ~ '^\d{4}-\d{2}-\d{2}$'),
  week_end_date TEXT NOT NULL CHECK (week_end_date ~ '^\d{4}-\d{2}-\d{2}$'),
  project_id UUID NOT NULL,
  created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
  created_by UUID NOT NULL,
  updated_at TIMESTAMPTZ,
  updated_by UUID,
  deleted_at TIMESTAMPTZ,
  deleted_by UUID
);

-- Create weekly_log_notes table
CREATE TABLE IF NOT EXISTS public.weekly_log_notes (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  note TEXT NOT NULL,
  weekly_log_id UUID NOT NULL,
  created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
  created_by <PERSON><PERSON><PERSON> NOT NULL,
  updated_at TIMESTAMPTZ,
  updated_by <PERSON><PERSON><PERSON>,
  deleted_at TIMESTAMPTZ,
  deleted_by UUID
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_weekly_logs_project_id ON public.weekly_logs(project_id);
CREATE INDEX IF NOT EXISTS idx_weekly_log_notes_weekly_log_id ON public.weekly_log_notes(weekly_log_id);

-- Add comments to document relationships
COMMENT ON TABLE public.weekly_logs IS 'Weekly logs for tracking project progress by week';
COMMENT ON TABLE public.weekly_log_notes IS 'Notes associated with weekly logs';
COMMENT ON COLUMN public.weekly_logs.week_number IS 'Project-specific week number (starts from 1)';
COMMENT ON COLUMN public.weekly_logs.week_start_date IS 'Start date of the week (Monday) in YYYY-MM-DD format';
COMMENT ON COLUMN public.weekly_logs.week_end_date IS 'End date of the week (Sunday) in YYYY-MM-DD format';
COMMENT ON COLUMN public.weekly_logs.project_id IS 'Reference to the project this weekly log belongs to';
COMMENT ON COLUMN public.weekly_log_notes.weekly_log_id IS 'Reference to the weekly log this note belongs to';

-- Add foreign key constraints in a separate migration to ensure tables exist first
-- These will be added to the existing foreign key migration file or a new one
-- ALTER TABLE public.weekly_logs ADD CONSTRAINT fk_weekly_logs_project_id FOREIGN KEY (project_id) REFERENCES public.projects(id);
-- ALTER TABLE public.weekly_log_notes ADD CONSTRAINT fk_weekly_log_notes_weekly_log_id FOREIGN KEY (weekly_log_id) REFERENCES public.weekly_logs(id);
-- ALTER TABLE public.project_tasks ADD CONSTRAINT fk_project_tasks_weekly_log_id FOREIGN KEY (weekly_log_id) REFERENCES public.weekly_logs(id);