-- Add foreign key constraints for weekly_logs and weekly_log_notes tables

-- Check if constraints exist before adding them
DO $$
BEGIN
    -- Add foreign key from weekly_logs to projects
    IF NOT EXISTS (
        SELECT 1 FROM pg_constraint WHERE conname = 'fk_weekly_logs_project_id'
    ) THEN
        ALTER TABLE public.weekly_logs
        ADD CONSTRAINT fk_weekly_logs_project_id
        FOREIGN KEY (project_id)
        REFERENCES public.projects(id);
    END IF;

    -- Add foreign key from weekly_log_notes to weekly_logs
    IF NOT EXISTS (
        SELECT 1 FROM pg_constraint WHERE conname = 'fk_weekly_log_notes_weekly_log_id'
    ) THEN
        ALTER TABLE public.weekly_log_notes
        ADD CONSTRAINT fk_weekly_log_notes_weekly_log_id
        FOREIGN KEY (weekly_log_id)
        REFERENCES public.weekly_logs(id);
    END IF;

    -- Add foreign key from project_tasks to weekly_logs
    IF NOT EXISTS (
        SELECT 1 FROM pg_constraint WHERE conname = 'fk_project_tasks_weekly_log_id'
    ) THEN
        ALTER TABLE public.project_tasks
        ADD CONSTRAINT fk_project_tasks_weekly_log_id
        FOREIGN KEY (weekly_log_id)
        REFERENCES public.weekly_logs(id);
    END IF;
END
$$;

-- Add comments to document relationships
COMMENT ON CONSTRAINT fk_weekly_logs_project_id ON public.weekly_logs IS 'Ensures weekly logs are associated with valid projects';
COMMENT ON CONSTRAINT fk_weekly_log_notes_weekly_log_id ON public.weekly_log_notes IS 'Ensures notes are associated with valid weekly logs';
COMMENT ON CONSTRAINT fk_project_tasks_weekly_log_id ON public.project_tasks IS 'Ensures tasks can be optionally associated with valid weekly logs';