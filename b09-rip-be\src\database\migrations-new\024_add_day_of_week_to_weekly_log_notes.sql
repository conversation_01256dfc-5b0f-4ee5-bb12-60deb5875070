-- Add day_of_week field to weekly_log_notes table and ensure uniqueness per weekly log
ALTER TABLE public.weekly_log_notes
ADD COLUMN IF NOT EXISTS day_of_week INTEGER NOT NULL DEFAULT 1
CHECK (day_of_week >= 1 AND day_of_week <= 5);

-- Add unique constraint to ensure each day can only have one note per weekly log
-- Using a partial index instead of a partial unique constraint for better compatibility
CREATE UNIQUE INDEX unique_weekly_log_day_of_week
ON public.weekly_log_notes (weekly_log_id, day_of_week)
WHERE deleted_at IS NULL;

-- Add comment to document the index
COMMENT ON INDEX unique_weekly_log_day_of_week IS
'Ensures each day can only have one note per weekly log';

-- We don't need an additional index since we already have a unique index on these columns
-- CREATE INDEX IF NOT EXISTS idx_weekly_log_notes_day_of_week ON public.weekly_log_notes(weekly_log_id, day_of_week);

-- Add comment to document the field
COMMENT ON COLUMN public.weekly_log_notes.day_of_week IS 'Day of week (1-5, where 1 is Monday, 5 is Friday)';
