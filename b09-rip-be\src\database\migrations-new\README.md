# New Database Migrations

This directory contains reorganized SQL migrations for the Kasuat application database. Each model has its own dedicated migration file for better modularity and maintainability.

## Migration Order

Migrations should be run in the following order:

1. `000_create_extensions.sql` - Verifies UUID functions
2. `001_create_organizations_table.sql` - Creates organizations table
3. `002_create_user_profiles_table.sql` - Creates user_profiles table with its enum
4. `003_create_employees_table.sql` - Creates employees table with its enums
5. `004_create_salaries_table.sql` - Creates salaries table with its enum
6. `005_create_kpis_table.sql` - Creates kpis table with its enum
7. `006_create_attendances_table.sql` - Creates attendances table with its enum
8. `007_create_tasks_table.sql` - Creates tasks table
9. `008_create_projects_table.sql` - Creates projects table with its enums
10. `009_create_invoices_table.sql` - Creates invoices table with its enums
11. `010_create_invoice_items_table.sql` - Creates invoice_items table
12. `011_create_bonus_salaries_table.sql` - Creates bonus_salaries table with its enum
13. `012_create_deduction_salaries_table.sql` - Creates deduction_salaries table with its enum
14. `013_create_allowance_salaries_table.sql` - Creates allowance_salaries table with its enum
15. `014_create_kpi_projects_table.sql` - Creates kpi_projects table with its enum
16. `015_create_invoice_update_history_table.sql` - Creates invoice_update_history table
17. `016_create_salary_update_history_table.sql` - Creates salary_update_history table
18. `017_add_foreign_key_references.sql` - Adds foreign key constraints to all tables
19. `018_create_invoice_payment_proofs_table.sql` - Creates invoice_payment_proofs table
20. `019_create_project_tasks_table.sql` - Creates project_tasks table
21. `020_update_project_category_enum.sql` - Updates project_category enum to match model values

## Key Differences from Original Migrations

1. **One Model Per File**: Each database model has its own dedicated migration file
2. **Modular Foreign Key Constraints**: Foreign key constraints are added in a separate file after all tables are created
3. **No RLS Policies**: Row Level Security policies have been removed as requested
4. **Enums with Models**: Each enum type is created in the same file as the model that uses it
5. **No Circular References**: The circular reference between employees and salaries has been resolved by removing the salary_id field from employees

## Running Migrations

To run these new migrations, use the following command:

```bash
bun db:migrate-new
```

This will execute all migration files in this directory in the correct order.
