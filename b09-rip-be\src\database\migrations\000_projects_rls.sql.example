-- Projects Table Structure (Create first if it doesn't exist)
CREATE TABLE IF NOT EXISTS public.projects (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  name TEXT NOT NULL,
  description TEXT,
  org_id UUID REFERENCES organizations(id) NULL,
  employee_id UUID REFERENCES employees(id) NULL,
  created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
  created_by UUID NOT NULL REFERENCES auth.users(id),
  updated_at TIMESTAMPTZ,
  updated_by UUID REFERENCES auth.users(id),
  deleted_at TIMESTAMPTZ,
  deleted_by UUID REFERENCES auth.users(id)
);

-- Enable Row Level Security
ALTER TABLE public.projects ENABLE ROW LEVEL SECURITY;

-- Disable all default access
ALTER DEFAULT PRIVILEGES IN SCHEMA public REVOKE ALL ON TABLES FROM public;

-- ROLE-BASED SECURITY POLICIES

-- 1. Manager Policy: Managers can see all projects
CREATE POLICY "Managers can view all projects" ON public.projects
  FOR SELECT
  USING (
    auth.uid() IN (
      SELECT user_id FROM public.user_profiles
      WHERE role = 'Manager'
    )
  );

-- 2. Client Policy: Clients can only see projects related to their organization
CREATE POLICY "Clients can only view their organization's projects" ON public.projects
  FOR SELECT
  USING (
    auth.uid() IN (
      SELECT user_id FROM public.user_profiles
      WHERE role = 'Client' AND org_id = projects.org_id
    )
  );

-- 3. Employee Policy: Employees can see projects assigned to them
CREATE POLICY "Employees can only view their assigned projects" ON public.projects
  FOR SELECT
  USING (
    auth.uid() IN (
      SELECT user_id FROM public.user_profiles
      WHERE role IN ('Operation', 'HR', 'Finance') AND employee_id = projects.employee_id
    )
  );

-- 4. Creator Policy: Any authenticated user can see projects they created
CREATE POLICY "Users can view projects they created" ON public.projects
  FOR SELECT
  USING (auth.uid() = created_by AND deleted_at IS NULL);

-- INSERT POLICIES

-- 1. Managers can create any project
CREATE POLICY "Managers can create any project" ON public.projects
  FOR INSERT
  WITH CHECK (
    auth.uid() IN (
      SELECT user_id FROM public.user_profiles
      WHERE role = 'Manager'
    )
  );

-- 2. Operations staff can create projects only for their own employee_id
CREATE POLICY "Operations staff can create projects for themselves" ON public.projects
  FOR INSERT
  WITH CHECK (
    auth.uid() IN (
      SELECT user_id FROM public.user_profiles
      WHERE role = 'Operation' AND employee_id = projects.employee_id
    )
  );

-- 3. Clients can create projects only for their own org_id
CREATE POLICY "Clients can create projects for their organization" ON public.projects
  FOR INSERT
  WITH CHECK (
    auth.uid() IN (
      SELECT user_id FROM public.user_profiles
      WHERE role = 'Client' AND org_id = projects.org_id
    )
  );

-- UPDATE POLICIES

-- 1. Managers can update any project
CREATE POLICY "Managers can update any project" ON public.projects
  FOR UPDATE
  USING (
    auth.uid() IN (
      SELECT user_id FROM public.user_profiles
      WHERE role = 'Manager'
    )
  );

-- 2. Operations can update only their assigned projects
CREATE POLICY "Operations can update their assigned projects" ON public.projects
  FOR UPDATE
  USING (
    auth.uid() IN (
      SELECT user_id FROM public.user_profiles
      WHERE role = 'Operation' AND employee_id = projects.employee_id
    )
  );

-- 3. Clients can only update projects for their organization
CREATE POLICY "Clients can update their organization's projects" ON public.projects
  FOR UPDATE
  USING (
    auth.uid() IN (
      SELECT user_id FROM public.user_profiles
      WHERE role = 'Client' AND org_id = projects.org_id
    )
  );

-- DELETE POLICIES (using soft delete)

-- 1. Only managers can perform hard deletes
CREATE POLICY "Only managers can hard delete projects" ON public.projects
  FOR DELETE
  USING (
    auth.uid() IN (
      SELECT user_id FROM public.user_profiles
      WHERE role = 'Manager'
    )
  );

-- Soft delete is handled via update policies

-- FUNCTIONS FOR HANDLING COMPLEX ACCESS PATTERNS

-- Function to check if user has access to a project based on their role
CREATE OR REPLACE FUNCTION public.user_has_project_access(project_id UUID)
RETURNS BOOLEAN AS $$
DECLARE
  user_role TEXT;
  user_org_id UUID;
  user_emp_id UUID;
  project_org_id UUID;
  project_emp_id UUID;
BEGIN
  -- Get the current user's role and IDs
  SELECT role, org_id, employee_id INTO user_role, user_org_id, user_emp_id
  FROM public.user_profiles
  WHERE user_id = auth.uid();
  
  -- Get the project's org and employee IDs
  SELECT org_id, employee_id INTO project_org_id, project_emp_id
  FROM public.projects
  WHERE id = project_id;
  
  -- Manager can access everything
  IF user_role = 'Manager' THEN
    RETURN TRUE;
  END IF;
  
  -- Client can access their org's projects
  IF user_role = 'Client' AND user_org_id = project_org_id THEN
    RETURN TRUE;
  END IF;
  
  -- Employees can access their assigned projects
  IF user_role IN ('Operation', 'HR', 'Finance') AND user_emp_id = project_emp_id THEN
    RETURN TRUE;
  END IF;
  
  -- Nobody else has access
  RETURN FALSE;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Trigger to ensure proper access control when inserting or updating
CREATE OR REPLACE FUNCTION public.ensure_project_access()
RETURNS TRIGGER AS $$
DECLARE
  user_role TEXT;
  user_org_id UUID;
  user_emp_id UUID;
BEGIN
  -- Get the current user's role and IDs
  SELECT role, org_id, employee_id INTO user_role, user_org_id, user_emp_id
  FROM public.user_profiles
  WHERE user_id = auth.uid();
  
  -- Managers can do anything
  IF user_role = 'Manager' THEN
    RETURN NEW;
  END IF;
  
  -- Clients can only set their own org_id
  IF user_role = 'Client' AND NEW.org_id != user_org_id THEN
    RAISE EXCEPTION 'Clients can only create/update projects for their own organization';
  END IF;
  
  -- Operations can only set their own employee_id
  IF user_role = 'Operation' AND NEW.employee_id != user_emp_id THEN
    RAISE EXCEPTION 'Operation staff can only create/update projects assigned to themselves';
  END IF;
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Attach the trigger to the projects table
CREATE TRIGGER ensure_project_access_trigger
BEFORE INSERT OR UPDATE ON public.projects
FOR EACH ROW EXECUTE FUNCTION public.ensure_project_access(); 