-- Create role-specific views for projects

-- 1. View for managers - shows all projects
CREATE OR REPLACE VIEW manager_projects AS
SELECT p.*
FROM public.projects p
WHERE EXISTS (
  SELECT 1 FROM public.user_profiles
  WHERE user_id = auth.uid() AND role = 'Manager'
);

-- 2. View for clients - shows only their organization's projects
CREATE OR REPLACE VIEW client_projects AS
SELECT p.*
FROM public.projects p
JOIN public.user_profiles up ON up.user_id = auth.uid()
WHERE up.role = 'Client' AND up.org_id = p.org_id;

-- 3. View for operations - shows only projects assigned to them
CREATE OR REPLACE VIEW operation_projects AS
SELECT p.*
FROM public.projects p
JOIN public.user_profiles up ON up.user_id = auth.uid()
WHERE up.role = 'Operation' AND up.employee_id = p.employee_id;

-- 4. View for HR - shows HR-related projects
CREATE OR REPLACE VIEW hr_projects AS
SELECT p.*
FROM public.projects p
JOIN public.user_profiles up ON up.user_id = auth.uid()
WHERE up.role = 'HR' AND up.employee_id = p.employee_id;

-- 5. View for finance - shows finance-related projects
CREATE OR REPLACE VIEW finance_projects AS
SELECT p.*
FROM public.projects p
JOIN public.user_profiles up ON up.user_id = auth.uid()
WHERE up.role = 'Finance' AND up.employee_id = p.employee_id;

-- 6. Smart view that automatically shows the correct projects based on user role
CREATE OR REPLACE FUNCTION get_user_projects()
RETURNS SETOF public.projects AS $$
DECLARE
  user_role TEXT;
BEGIN
  -- Get the current user's role
  SELECT role INTO user_role FROM public.user_profiles WHERE user_id = auth.uid();
  
  -- Return the appropriate projects based on role
  CASE user_role
    WHEN 'Manager' THEN
      RETURN QUERY SELECT * FROM manager_projects;
    WHEN 'Client' THEN
      RETURN QUERY SELECT * FROM client_projects;
    WHEN 'Operation' THEN
      RETURN QUERY SELECT * FROM operation_projects;
    WHEN 'HR' THEN
      RETURN QUERY SELECT * FROM hr_projects;
    WHEN 'Finance' THEN
      RETURN QUERY SELECT * FROM finance_projects;
    ELSE
      -- Default case - return nothing
      RETURN QUERY SELECT * FROM public.projects WHERE 1=0;
  END CASE;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create a view that uses the function
CREATE OR REPLACE VIEW my_projects AS
SELECT * FROM get_user_projects();

-- Helper functions for common queries

-- Get all projects the user can access
CREATE OR REPLACE FUNCTION get_accessible_projects()
RETURNS SETOF public.projects AS $$
BEGIN
  RETURN QUERY 
  SELECT * FROM my_projects
  WHERE deleted_at IS NULL
  ORDER BY created_at DESC;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Get projects by status with role-based filtering
CREATE OR REPLACE FUNCTION get_projects_by_status(status_value TEXT)
RETURNS SETOF public.projects AS $$
BEGIN
  RETURN QUERY 
  SELECT * FROM my_projects
  WHERE status = status_value AND deleted_at IS NULL
  ORDER BY created_at DESC;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Get count of projects by status with role-based filtering
CREATE OR REPLACE FUNCTION get_project_count_by_status(status_value TEXT)
RETURNS INTEGER AS $$
DECLARE
  project_count INTEGER;
BEGIN
  SELECT COUNT(*) INTO project_count
  FROM my_projects
  WHERE status = status_value AND deleted_at IS NULL;
  
  RETURN project_count;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Security definer function to check if the current user is a manager
CREATE OR REPLACE FUNCTION is_manager()
RETURNS BOOLEAN AS $$
DECLARE
  user_role TEXT;
BEGIN
  SELECT role INTO user_role FROM public.user_profiles WHERE user_id = auth.uid();
  RETURN user_role = 'Manager';
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Security definer function to check if the current user can edit a specific project
CREATE OR REPLACE FUNCTION can_edit_project(project_id UUID)
RETURNS BOOLEAN AS $$
DECLARE
  user_role TEXT;
  user_org_id UUID;
  user_emp_id UUID;
  project_org_id UUID;
  project_emp_id UUID;
BEGIN
  -- Get current user's role and IDs
  SELECT role, org_id, employee_id 
  INTO user_role, user_org_id, user_emp_id
  FROM public.user_profiles
  WHERE user_id = auth.uid();
  
  -- Get project details
  SELECT org_id, employee_id 
  INTO project_org_id, project_emp_id
  FROM public.projects
  WHERE id = project_id;
  
  -- Manager can edit anything
  IF user_role = 'Manager' THEN
    RETURN TRUE;
  END IF;
  
  -- Client can edit their org's projects
  IF user_role = 'Client' AND user_org_id = project_org_id THEN
    RETURN TRUE;
  END IF;
  
  -- Employee can edit their assigned projects
  IF user_role IN ('Operation', 'HR', 'Finance') AND user_emp_id = project_emp_id THEN
    RETURN TRUE;
  END IF;
  
  -- Default deny
  RETURN FALSE;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER; 