-- Add Row Level Security policies
ALTER TABLE public.notes <PERSON><PERSON><PERSON>E ROW LEVEL SECURITY;

-- Policy for viewing (exclude soft-deleted items)
CREATE POLICY "Users can view their own notes" ON public.notes
  FOR SELECT USING (auth.uid() = created_by AND deleted_at IS NULL);

-- Policy for inserting
CREATE POLICY "Users can create notes" ON public.notes
  FOR INSERT WITH CHECK (auth.uid() = created_by);

-- Policy for updating (prevent updating deleted items)
CREATE POLICY "Users can update their own notes" ON public.notes
  FOR UPDATE USING (auth.uid() = created_by AND deleted_at IS NULL);

-- Policy for soft deletion (updating the deleted_at field)
CREATE POLICY "Users can soft delete their own notes" ON public.notes
  FOR UPDATE USING (auth.uid() = created_by); 