-- Create enum types
CREATE TYPE public.user_role AS ENUM ('Admin', 'Manager', 'HR', 'Finance', 'Operation', 'Client');
CREATE TYPE public.presence_status AS ENUM ('present', 'absent', 'permit', 'leave');
CREATE TYPE public.invoice_type AS ENUM ('external', 'internal');
CREATE TYPE public.payment_status AS ENUM ('pending', 'partial', 'paid', 'overdue', 'cancelled');
CREATE TYPE public.payment_method AS ENUM ('bank_transfer', 'cash', 'credit_card', 'cheque', 'other');
CREATE TYPE public.salary_payment_status AS ENUM ('unpaid', 'paid');
CREATE TYPE public.kpi_status AS ENUM ('not_started', 'in_progress', 'completed_below_target', 'completed_on_target', 'completed_above_target');

-- Create organizations table
CREATE TABLE IF NOT EXISTS public.organizations (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  name TEXT NOT NULL UNIQUE,
  phone TEXT NOT NULL,
  address TEXT NOT NULL,
  client_type TEXT NOT NULL,
  notes TEXT,
  created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
  created_by UUID NOT NULL REFERENCES auth.users(id),
  updated_at TIMESTAMPTZ,
  updated_by UUID REFERENCES auth.users(id),
  deleted_at TIMESTAMPTZ,
  deleted_by UUID REFERENCES auth.users(id)
);

-- Create user_profiles table
CREATE TABLE IF NOT EXISTS public.user_profiles (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  user_id UUID NOT NULL REFERENCES auth.users(id) UNIQUE,
  fullname TEXT NOT NULL,
  phonenum TEXT NOT NULL,
  role user_role NOT NULL,
  org_id UUID REFERENCES public.organizations(id),
  is_active BOOLEAN NOT NULL DEFAULT false,
  created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
  created_by UUID REFERENCES auth.users(id),
  updated_at TIMESTAMPTZ,
  updated_by UUID REFERENCES auth.users(id),
  deleted_at TIMESTAMPTZ,
  deleted_by UUID REFERENCES auth.users(id)
);

-- Create employees table
CREATE TABLE IF NOT EXISTS public.employees (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  profile_id UUID UNIQUE NOT NULL REFERENCES public.user_profiles(id),
  dob TEXT NOT NULL CHECK (dob ~ '^\d{4}-\d{2}-\d{2}$'),
  address TEXT NOT NULL,
  bank_account TEXT NOT NULL,
  bank_name TEXT NOT NULL,
  employment_status TEXT NOT NULL,
  presence_status presence_status NOT NULL,
  department TEXT NOT NULL,
  start_date TEXT NOT NULL CHECK (start_date ~ '^\d{4}-\d{2}-\d{2}$'),
  created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
  created_by UUID NOT NULL REFERENCES auth.users(id),
  updated_at TIMESTAMPTZ,
  updated_by UUID REFERENCES auth.users(id),
  deleted_at TIMESTAMPTZ,
  deleted_by UUID REFERENCES auth.users(id)
);

-- Add employee_id to user_profiles
ALTER TABLE public.user_profiles 
ADD COLUMN employee_id UUID REFERENCES public.employees(id);

-- Create salaries table
CREATE TABLE IF NOT EXISTS public.salaries (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  employee_id UUID NOT NULL REFERENCES public.employees(id),
  base_salary NUMERIC NOT NULL,
  bonus NUMERIC NOT NULL DEFAULT 0,
  pay_reduction NUMERIC NOT NULL DEFAULT 0,
  total_salary NUMERIC NOT NULL,
  payment_status salary_payment_status NOT NULL DEFAULT 'unpaid',
  period TEXT NOT NULL CHECK (period ~ '^\d{4}-\d{2}$'),
  created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
  created_by UUID NOT NULL REFERENCES auth.users(id),
  updated_at TIMESTAMPTZ,
  updated_by UUID REFERENCES auth.users(id),
  deleted_at TIMESTAMPTZ,
  deleted_by UUID REFERENCES auth.users(id)
);

-- Create kpis table
CREATE TABLE IF NOT EXISTS public.kpis (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  full_name TEXT NOT NULL,
  employee_id UUID NOT NULL REFERENCES public.employees(id),
  description TEXT NOT NULL,
  target TEXT NOT NULL,
  period TEXT NOT NULL,
  status kpi_status NOT NULL DEFAULT 'not_started',
  bonus_received NUMERIC,
  additional_notes TEXT,
  created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
  created_by UUID NOT NULL REFERENCES auth.users(id),
  updated_at TIMESTAMPTZ,
  updated_by UUID REFERENCES auth.users(id),
  deleted_at TIMESTAMPTZ,
  deleted_by UUID REFERENCES auth.users(id)
);

-- Create invoices table
CREATE TABLE IF NOT EXISTS public.invoices (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  invoice_number TEXT NOT NULL UNIQUE,
  invoice_type invoice_type NOT NULL,
  recipient_name TEXT NOT NULL,
  project_id UUID,
  project_name TEXT,
  due_date TIMESTAMPTZ NOT NULL,
  payment_method payment_method NOT NULL,
  payment_status payment_status NOT NULL DEFAULT 'pending',
  notes TEXT,
  total_amount NUMERIC NOT NULL,
  created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
  created_by UUID NOT NULL REFERENCES auth.users(id),
  updated_at TIMESTAMPTZ,
  updated_by UUID REFERENCES auth.users(id),
  deleted_at TIMESTAMPTZ,
  deleted_by UUID REFERENCES auth.users(id)
);

-- Create invoice_items table
CREATE TABLE IF NOT EXISTS public.invoice_items (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  invoice_id UUID NOT NULL REFERENCES public.invoices(id) ON DELETE CASCADE,
  item_name TEXT NOT NULL,
  item_amount INTEGER NOT NULL,
  item_price NUMERIC NOT NULL,
  total_price NUMERIC NOT NULL,
  created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
  created_by UUID NOT NULL REFERENCES auth.users(id),
  updated_at TIMESTAMPTZ,
  updated_by UUID REFERENCES auth.users(id),
  deleted_at TIMESTAMPTZ,
  deleted_by UUID REFERENCES auth.users(id)
);
