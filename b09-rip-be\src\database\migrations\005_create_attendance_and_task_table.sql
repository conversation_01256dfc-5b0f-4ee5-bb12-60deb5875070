-- Create attendance table
CREATE TABLE IF NOT EXISTS public.attendances (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  employee_id UUID NOT NULL REFERENCES public.employees(id),
  date TEXT NOT NULL CHECK (date ~ '^\d{4}-\d{2}-\d{2}$'),
  clock_in TEXT CHECK (clock_in ~ '^\d{2}:\d{2}:\d{2}$'),
  clock_out TEXT CHECK (clock_out ~ '^\d{2}:\d{2}:\d{2}$'),
  status TEXT NOT NULL,
  notes TEXT,
  created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
  created_by UUID NOT NULL REFERENCES auth.users(id),
  updated_at TIMESTAMPTZ,
  updated_by UUID REFERENCES auth.users(id),
  deleted_at TIMESTAMPTZ,
  deleted_by UUID REFERENCES auth.users(id)
);

-- Create tasks table
CREATE TABLE IF NOT EXISTS public.tasks (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  assigned_by TEXT NOT NULL,
  description TEXT NOT NULL,
  completion_status BOOLEAN NOT NULL DEFAULT false,
  employee_id UUID NOT NULL REFERENCES public.employees(id),
  due_date TEXT NOT NULL CHECK (due_date ~ '^\d{4}-\d{2}-\d{2}$'),
  attendance_id UUID REFERENCES public.attendances(id),
  created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
  created_by UUID NOT NULL REFERENCES auth.users(id),
  updated_at TIMESTAMPTZ,
  updated_by UUID REFERENCES auth.users(id),
  deleted_at TIMESTAMPTZ,
  deleted_by UUID REFERENCES auth.users(id)
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_attendances_employee_id ON public.attendances(employee_id);
CREATE INDEX IF NOT EXISTS idx_tasks_employee_id ON public.tasks(employee_id);
CREATE INDEX IF NOT EXISTS idx_tasks_attendance_id ON public.tasks(attendance_id);

-- Add comments to document relationships
COMMENT ON TABLE public.attendances IS 'Employee attendance records which can have associated tasks';
COMMENT ON TABLE public.tasks IS 'Tasks assigned to employees, which can be optionally associated with attendance records';
COMMENT ON COLUMN public.tasks.attendance_id IS 'Optional reference to an attendance record';
