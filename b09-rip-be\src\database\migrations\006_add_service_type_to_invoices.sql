-- Add service_type to invoices table
-- First check if the enum already exists, if not create it
DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'service_type') THEN
        CREATE TYPE public.service_type AS ENUM (
            'HCM', 'ORDEV', 'BE', 'IT', 'MARKETING', 'FINANCE', 'SALES', 'OTHER'
        );
    END IF;
END$$;

-- Add service_type column to invoices table if it doesn't exist
ALTER TABLE public.invoices 
ADD COLUMN IF NOT EXISTS service_type service_type NOT NULL DEFAULT 'OTHER';
