-- Add unique constraint to kpi table for employee_id and period
-- This prevents duplicate KPIs for the same employee in the same period

-- First, check if the constraint already exists, and if it does, drop it
DO $$
BEGIN
    IF EXISTS (
        SELECT 1 FROM information_schema.table_constraints 
        WHERE constraint_name = 'unique_employee_period_kpi'
        AND table_name = 'kpi'
    ) THEN
        ALTER TABLE public.kpi DROP CONSTRAINT unique_employee_period_kpi;
    END IF;
END$$;

-- Add the unique constraint, considering only non-deleted records
-- This allows creating a new KPI for the same employee/period after soft-deleting the previous one
ALTER TABLE public.kpi
ADD CONSTRAINT unique_employee_period_kpi 
UNIQUE (employee_id, period) 
WHERE deleted_at IS NULL;

-- Add an index for better performance on these lookups
CREATE INDEX IF NOT EXISTS idx_kpi_employee_period ON public.kpi(employee_id, period);

-- Add a comment explaining the constraint
COMMENT ON CONSTRAINT unique_employee_period_kpi ON public.kpi IS 
'Prevents creating duplicate KPIs for the same employee in the same period'; 