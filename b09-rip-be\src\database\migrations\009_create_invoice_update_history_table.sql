-- Create invoice_update_history table
CREATE TABLE IF NOT EXISTS public.invoice_update_history (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  invoice_id UUID NOT NULL REFERENCES public.invoices(id) ON DELETE CASCADE,
  change_description TEXT NOT NULL, -- JSO<PERSON> string of changes
  created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
  created_by UUID NOT NULL REFERENCES auth.users(id),
  updated_at TIMESTAMPTZ,
  updated_by UUID REFERENCES auth.users(id),
  deleted_at TIMESTAMPTZ,
  deleted_by UUID REFERENCES auth.users(id)
);

-- Create index for faster lookups by invoice_id
CREATE INDEX IF NOT EXISTS idx_invoice_update_history_invoice_id ON public.invoice_update_history(invoice_id);

-- Add RLS policies
ALTER TABLE public.invoice_update_history ENABLE ROW LEVEL SECURITY;

-- Policy for viewing (exclude soft-deleted items)
CREATE POLICY "Users can view invoice history" ON public.invoice_update_history
  FOR SELECT USING (auth.uid() IN (SELECT created_by FROM public.invoices WHERE id = invoice_id) AND deleted_at IS NULL);

-- Policy for inserting
CREATE POLICY "Users can create invoice history" ON public.invoice_update_history
  FOR INSERT WITH CHECK (auth.uid() = created_by);

-- Policy for updating (prevent updating deleted items)
CREATE POLICY "Users can update their own invoice history" ON public.invoice_update_history
  FOR UPDATE USING (auth.uid() = created_by AND deleted_at IS NULL);

-- Policy for deleting (soft delete)
CREATE POLICY "Users can delete their own invoice history" ON public.invoice_update_history
  FOR UPDATE USING (auth.uid() = created_by AND deleted_at IS NULL)
  WITH CHECK (deleted_at IS NOT NULL);
