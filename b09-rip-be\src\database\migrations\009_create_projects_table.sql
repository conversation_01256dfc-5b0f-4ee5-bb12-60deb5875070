-- Create projects table
CREATE TABLE IF NOT EXISTS public.projects (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  organization_id UUID NOT NULL REFERENCES public.organizations(id),
  project_category TEXT NOT NULL,
  project_name TEXT NOT NULL,
  pic_project UUID NOT NULL REFERENCES public.employees(id), -- Employee ID as person in charge
  start_project TEXT NOT NULL CHECK (start_project ~ '^\d{4}-\d{2}-\d{2}$'),
  end_project TEXT NOT NULL CHECK (end_project ~ '^\d{4}-\d{2}-\d{2}$'),
  status_project TEXT NOT NULL,
  budget_project TEXT NOT NULL,
  gantt_chart_id TEXT, -- Will be automatically generated in the future
  project_charter_id TEXT, -- Will be automatically generated in the future
  objectives TEXT NOT NULL,
  created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
  created_by UUID NOT NULL REFERENCES auth.users(id),
  updated_at TIMESTAMPTZ,
  updated_by UUID REFERENCES auth.users(id),
  deleted_at TIMESTAMPTZ,
  deleted_by UUID REFERENCES auth.users(id)
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_projects_organization_id ON public.projects(organization_id);
CREATE INDEX IF NOT EXISTS idx_projects_pic_project ON public.projects(pic_project);

-- Add Row Level Security policies
ALTER TABLE public.projects ENABLE ROW LEVEL SECURITY;

-- Policy for viewing (exclude soft-deleted items)
-- Managers can see all projects
CREATE POLICY "Managers can view all projects" ON public.projects
  FOR SELECT USING (
    auth.uid() IN (
      SELECT user_id FROM public.user_profiles
      WHERE role = 'Manager'
    ) AND deleted_at IS NULL
  );

-- Staff Operations can see all projects
CREATE POLICY "Staff Operations can view all projects" ON public.projects
  FOR SELECT USING (
    auth.uid() IN (
      SELECT user_id FROM public.user_profiles
      WHERE role = 'Operation'
    ) AND deleted_at IS NULL
  );

-- Policy for inserting
-- Staff Operations can create projects
CREATE POLICY "Staff Operations can create projects" ON public.projects
  FOR INSERT WITH CHECK (
    auth.uid() IN (
      SELECT user_id FROM public.user_profiles
      WHERE role = 'Operation'
    )
  );

-- Managers can create projects
CREATE POLICY "Managers can create projects" ON public.projects
  FOR INSERT WITH CHECK (
    auth.uid() IN (
      SELECT user_id FROM public.user_profiles
      WHERE role = 'Manager'
    )
  );

-- Policy for updating
-- Staff Operations can update projects
CREATE POLICY "Staff Operations can update projects" ON public.projects
  FOR UPDATE USING (
    auth.uid() IN (
      SELECT user_id FROM public.user_profiles
      WHERE role = 'Operation'
    ) AND deleted_at IS NULL
  );

-- Managers can update projects
CREATE POLICY "Managers can update projects" ON public.projects
  FOR UPDATE USING (
    auth.uid() IN (
      SELECT user_id FROM public.user_profiles
      WHERE role = 'Manager'
    ) AND deleted_at IS NULL
  );

-- Policy for deleting
-- Staff Operations can delete projects
CREATE POLICY "Staff Operations can delete projects" ON public.projects
  FOR UPDATE USING (
    auth.uid() IN (
      SELECT user_id FROM public.user_profiles
      WHERE role = 'Operation'
    ) AND deleted_at IS NULL
  );

-- Managers can delete projects
CREATE POLICY "Managers can delete projects" ON public.projects
  FOR UPDATE USING (
    auth.uid() IN (
      SELECT user_id FROM public.user_profiles
      WHERE role = 'Manager'
    ) AND deleted_at IS NULL
  );

-- Add comments to document the table
COMMENT ON TABLE public.projects IS 'Projects managed by Staff Operations and Managers';
COMMENT ON COLUMN public.projects.gantt_chart_id IS 'Will be automatically generated in the future';
COMMENT ON COLUMN public.projects.project_charter_id IS 'Will be automatically generated in the future';
