-- Modify salaries table to use total_bonus, total_deduction, and total_allowance
ALTER TABLE public.salaries 
DROP COLUMN IF EXISTS bonus,
DROP COLUMN IF EXISTS pay_reduction,
ADD COLUMN total_bonus NUMERIC NOT NULL DEFAULT 0,
ADD COLUMN total_deduction NUMERIC NOT NULL DEFAULT 0,
ADD COLUMN total_allowance NUMERIC NOT NULL DEFAULT 0;

-- Create deduction_salaries table
CREATE TABLE IF NOT EXISTS public.deduction_salaries (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  salary_id UUID NOT NULL REFERENCES public.salaries(id),
  amount NUMERIC NOT NULL,
  deduction_type TEXT NOT NULL,
  notes TEXT,
  created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
  created_by UUID NOT NULL REFERENCES auth.users(id),
  updated_at TIMESTAMPTZ,
  updated_by UUID REFERENCES auth.users(id),
  deleted_at TIMESTAMPTZ,
  deleted_by UUID REFERENCES auth.users(id)
);

-- Create index for better performance
CREATE INDEX IF NOT EXISTS idx_deduction_salaries_salary_id ON public.deduction_salaries(salary_id);
