-- Create salary_update_history table
CREATE TABLE IF NOT EXISTS public.salary_update_history (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  salary_id UUID NOT NULL REFERENCES public.salaries(id) ON DELETE CASCADE,
  change_description TEXT NOT NULL, -- <PERSON><PERSON><PERSON> string of changes
  created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
  created_by UUID NOT NULL REFERENCES auth.users(id),
  updated_at TIMESTAMPTZ,
  updated_by UUID REFERENCES auth.users(id),
  deleted_at TIMESTAMPTZ,
  deleted_by UUID REFERENCES auth.users(id)
);

-- Create index for faster lookups
CREATE INDEX IF NOT EXISTS idx_salary_update_history_salary_id ON public.salary_update_history(salary_id);

-- Add RLS policies
ALTER TABLE public.salary_update_history ENABLE ROW LEVEL SECURITY;

-- Policy for viewing (exclude soft-deleted items)
CREATE POLICY "Users can view salary history" ON public.salary_update_history
  FOR SELECT USING (
    auth.uid() IN (SELECT created_by FROM public.salaries WHERE id = salary_id)
    AND deleted_at IS NULL
  );

-- Policy for inserting
CREATE POLICY "Users can create salary history" ON public.salary_update_history
  FOR INSERT WITH CHECK (auth.uid() = created_by);

-- Policy for updating (prevent updating deleted items)
CREATE POLICY "Users can update their own salary history" ON public.salary_update_history
  FOR UPDATE USING (auth.uid() = created_by AND deleted_at IS NULL);

-- Policy for deleting (soft delete)
CREATE POLICY "Users can delete their own salary history" ON public.salary_update_history
  FOR UPDATE USING (auth.uid() = created_by AND deleted_at IS NULL)
  WITH CHECK (deleted_at IS NOT NULL);
