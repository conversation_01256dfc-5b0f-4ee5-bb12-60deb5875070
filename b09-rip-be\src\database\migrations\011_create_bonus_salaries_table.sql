-- Create bonus_salaries table
CREATE TABLE IF NOT EXISTS public.bonus_salaries (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  salary_id UUID NOT NULL REFERENCES public.salaries(id),
  amount NUMERIC NOT NULL,
  bonus_type TEXT NOT NULL,
  notes TEXT,
  kpi_id UUID REFERENCES public.kpis(id),
  project_id UUID,
  created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
  created_by UUID NOT NULL REFERENCES auth.users(id),
  updated_at TIMESTAMPTZ,
  updated_by UUID REFERENCES auth.users(id),
  deleted_at TIMESTAMPTZ,
  deleted_by UUID REFERENCES auth.users(id)
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_bonus_salaries_salary_id ON public.bonus_salaries(salary_id);
CREATE INDEX IF NOT EXISTS idx_bonus_salaries_kpi_id ON public.bonus_salaries(kpi_id);
