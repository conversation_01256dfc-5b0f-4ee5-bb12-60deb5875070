# Database Migrations

This directory contains SQL migrations for the Kasuat application database.

## Migration Order

Migrations should be run in the following order:

1. `000_create_extensions.sql` - Sets up necessary PostgreSQL extensions
2. `001_create_notes.sql` - Creates the notes table
3. `002_add_rls.sql` - Adds Row Level Security policies
4. `003_add_status_to_notes.sql` - Adds status column to notes table
5. `004_create_models_and_relations.sql` - Creates core tables and relationships
6. `005_create_tasks_table.sql` - Creates attendance and tasks tables with their relationships
7. `006_add_service_type_to_invoices.sql` - Adds service type to invoices table
8. `007_add_salary_id_to_employees.sql` - Adds salary_id to employees table
9. `008_add_kpi_unique_constraint.sql` - Adds unique constraint to KPI table

## Running Migrations

Migrations can be applied using the Supabase CLI:

```bash
supabase db reset --db-url postgresql://postgres:postgres@localhost:5432/postgres
```

## Migration Details

### 005_create_tasks_table.sql

This migration creates both the attendance and tasks tables with their relationships:

1. Creates the `attendances` table with:

   - Basic attendance information (date, check-in, check-out, status)
   - Employee relationship
   - Audit fields (created_at, updated_at, etc.)

2. Creates the `tasks` table with:

   - Task information (description, completion status, due date)
   - Employee relationship
   - Optional attendance relationship
   - Audit fields

3. Creates indexes for better performance:

   - `idx_attendances_employee_id`
   - `idx_tasks_employee_id`
   - `idx_tasks_attendance_id`

4. Adds comments to document the relationships between tables
