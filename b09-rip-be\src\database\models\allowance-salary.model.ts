/**
 * Salary Allowance model interfaces
 */
import { BaseRecord } from "../../utils/database.types";

/**
 * Enum for types of salary allowances
 */
export enum AllowanceSalaryType {
  TRANSPORT = "transport",
  MEAL = "meal",
  HEALTH = "health",
  POSITION = "position",
  TENURE = "tenure",
  THR = "thr",
  OTHER = "other",
}

/**
 * Salary Allowance model interface representing the allowance breakdown
 */
export interface AllowanceSalary extends BaseRecord {
  salary_id: string;
  amount: number;
  allowance_type: AllowanceSalaryType;
  notes?: string;
}

/**
 * DTO for creating a new salary allowance entry
 */
export interface CreateAllowanceSalaryDto {
  salary_id: string;
  amount: number;
  allowance_type: AllowanceSalaryType;
  notes?: string;
}

/**
 * DTO for updating an existing salary allowance entry
 */
export interface UpdateAllowanceSalaryDto {
  amount?: number;
  allowance_type?: AllowanceSalaryType;
  notes?: string;
}

/**
 * DTO for deleting a salary allowance entry
 */
export interface DeleteAllowanceSalaryDto {
  id: string;
}
