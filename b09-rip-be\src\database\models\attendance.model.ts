import { BaseRecord } from "../../utils/database.types";
import { Task } from "./task.model";

// Enum untuk status kehadiran
export enum PresenceStatus {
  PRESENT = "present", // Hadir
  ABSENT = "absent", // Tidak Hadir
  PERMIT = "permit", // Izin
  LEAVE = "leave", // Cuti
}

// Interface untuk merepresentasikan Attendance record
export interface Attendance extends BaseRecord {
  date: string; // Format YYYY-MM-DD
  employee_id: string;
  status: PresenceStatus;
  clock_in?: string | null; // Format HH:MM:SS
  clock_out?: string | null; // Format HH:MM:SS
  notes?: string | null;
  // No embedded tasks - they will be queried via the relationship
}

// DTO untuk membuat kehadiran baru
export interface CreateAttendanceDto {
  date: string;
  employee_id: string;
  status: PresenceStatus;
  clock_in?: string | null;
  clock_out?: string | null;
  notes?: string | null;
}

// DTO untuk melihat absensi harian
export interface ViewDailyAbsenceDto {
  date: string;
}

// DTO for retrieving attendance with related tasks
export interface AttendanceWithTasksDto extends Attendance {
  tasks: Task[]; // This will be populated from a join query
  employee_name?: string; // Employee full name from user_profiles
}
