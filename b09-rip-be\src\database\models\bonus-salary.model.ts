import { BaseRecord } from "../../utils/database.types";

/**
 * Enum for bonus types
 */
export enum BonusSalaryType {
  KPI = "kpi", // Bonus based on KPI achievement
  PROJECT = "project", // Bonus based on project
  OTHER = "other", // Custom bonus type with custom description
}

/**
 * Bonus model interface representing the database table
 * Note: For KPI type bonuses:
 * - One employee can have multiple KPI bonuses
 * - Each KPI bonus references a specific KPI record
 * - The bonus amount should match the referenced KPI's bonus_received
 * - The total bonus in salary will be the sum of all individual bonuses
 */
export interface BonusSalary extends BaseRecord {
  salary_id: string;
  amount: number;
  bonus_type: BonusSalaryType;
  notes?: string;
  // Additional fields for specific bonus types
  kpi_id?: string; // Reference to KPI record (required for KPI type)
  project_id?: string; // For project bonus
}

/**
 * DTO for creating a new bonus record
 * Note: For KPI type bonuses:
 * - kpi_id is required
 * - amount should match the referenced KPI's bonus_received
 */
export interface CreateBonusSalaryDto {
  salary_id: string;
  amount: number;
  bonus_type: BonusSalaryType;
  notes?: string;
  // Additional fields for specific bonus types
  kpi_id?: string;
  project_id?: string;
}

/**
 * DTO for updating a bonus record
 */
export interface UpdateBonusSalaryDto {
  amount?: number;
  bonus_type?: BonusSalaryType;
  notes?: string;
  kpi_id?: string;
  project_id?: string;
}

/**
 * DTO for deleting a bonus record
 */
export interface DeleteBonusSalaryDto {
  id: string;
}
