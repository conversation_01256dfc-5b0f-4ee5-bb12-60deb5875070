/**
 * Dashboard model interfaces
 */
import { BaseRecord } from "../../utils/database.types";
import { ProjectStatus, ProjectCategory } from "./project.model";
import { KpiStatus } from "./kpi-project.model";
import { TaskStatus } from "./task.model";

/**
 * Interface for Project Dashboard
 */
export interface ProjectDashboard {
  // Basic project info
  id: string;
  project_name: string;
  organization_id: string;
  organization_name?: string;
  pic_project: string;
  pic_name?: string;
  project_category: ProjectCategory;
  start_project: string;
  end_project: string;
  status_project: ProjectStatus;
  objectives: string;
  budget_project: string;

  // Progress metrics
  progress_percentage: number; // Calculated from tasks
  days_elapsed: number; // Days since project start
  days_remaining: number; // Days until project end
  days_total: number; // Total project duration in days

  // KPI metrics
  kpi_status: KpiStatus | null;
  kpi_count: number;
  kpis: Array<{
    id: string;
    description: string;
    target: string;
    period: string;
    status: KpiStatus;
  }>;

  // Task metrics
  tasks_total: number;
  tasks_completed: number;
  tasks_in_progress: number;
  tasks_not_started: number;
  recent_tasks: Array<{
    id: string;
    description: string;
    completion_status: TaskStatus;
    employee_name?: string;
    due_date: string;
  }>;

  // Weekly log metrics
  weekly_logs_count: number;
  recent_weekly_logs: Array<{
    id: string;
    week_number: number;
    week_start_date: string;
    week_end_date: string;
    notes_count: number;
  }>;
}

/**
 * Interface for Dashboard Summary
 */
export interface DashboardSummary {
  // Project statistics
  projects: {
    total: number;
    by_status: {
      not_started: number;
      in_progress: number;
      completed: number;
      cancelled: number;
    };
    by_category: {
      [key in ProjectCategory]?: number;
    };
    by_pic: Array<{
      name: string;
      count: number;
    }>;
    recent: Array<{
      id: string;
      project_name: string;
      organization_name?: string;
      pic_name?: string;
      status_project: ProjectStatus;
      progress_percentage: number;
      days_remaining: number;
    }>;
    upcoming_deadlines: Array<{
      id: string;
      project_name: string;
      end_project: string;
      days_remaining: number;
      progress_percentage: number;
    }>;
  };

  // KPI statistics
  kpis: {
    total: number;
    by_status: {
      not_started: number;
      in_progress: number;
      completed_below_target: number;
      completed_on_target: number;
      completed_above_target: number;
    };
    achievement_percentage: number;
    achieved_count: number;
    not_achieved_count: number;
  };

  // Task statistics
  tasks: {
    total: number;
    completed: number;
    in_progress: number;
    not_started: number;
    overdue: number;
  };

  // Organization statistics (if user is a client)
  organization?: {
    id: string;
    name: string;
    projects_count: number;
    active_projects_count: number;
    completed_projects_count: number;
  };
}
