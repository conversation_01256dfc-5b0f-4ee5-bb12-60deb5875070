/**
 * Salary Deduction model interfaces
 */
import { BaseRecord } from "../../utils/database.types";

/**
 * Enum for types of salary deductions
 */
export enum DeductionType {
  ABSENCE = "absence",
  LATENESS = "lateness",
  LOAN = "loan",
  OTHER = "other",
}

/**
 * Salary Deduction model interface representing the deduction breakdown
 */
export interface DeductionSalary extends BaseRecord {
  salary_id: string;
  amount: number;
  deduction_type: DeductionType;
  notes?: string;
}

/**
 * DTO for creating a new salary deduction entry
 */
export interface CreateDeductionSalaryDto {
  salary_id: string;
  amount: number;
  deduction_type: DeductionType;
  notes?: string;
}

/**
 * DTO for updating an existing salary deduction entry
 */
export interface UpdateDeductionSalaryDto {
  amount?: number;
  deduction_type?: DeductionType;
  notes?: string;
}

/**
 * DTO for deleting a salary deduction entry
 */
export interface DeleteDeductionSalaryDto {
  id: string;
}
