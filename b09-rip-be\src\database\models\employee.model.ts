/**
 * Employee model interfaces
 */
import { BaseRecord } from "../../utils/database.types";
import { PresenceStatus } from "./attendance.model";

// Enum untuk status kepegawaian
export enum EmploymentStatus {
  INTERN = "Intern",
  FULLTIME = "Fulltime",
  NOT_PROVIDED = "Not provided",
}

export interface Employee extends BaseRecord {
  profile_id: string;
  dob: string;
  address: string;
  bank_account: string;
  bank_name: string;
  employment_status: EmploymentStatus;
  presence_status: PresenceStatus;
  department: string;
  start_date: string;
}

export interface CreateEmployeeDto {
  profile_id: string;
  dob: string;
  address: string;
  bank_account: string;
  bank_name: string;
  employment_status: EmploymentStatus;
  presence_status: PresenceStatus;
  department: string;
  start_date: string;
}

export interface UpdateEmployeeDto {
  dob?: string;
  address?: string;
  bank_account?: string;
  bank_name?: string;
  employment_status?: EmploymentStatus;
  presence_status?: PresenceStatus;
  department?: string;
  start_date?: string;
}
