/**
 * Invoice Payment Proof model interfaces
 */
import { BaseRecord } from "../../utils/database.types";

/**
 * Interface representing an Invoice Payment Proof record
 */
export interface InvoicePaymentProof extends BaseRecord {
  invoice_id: string;
  file_path: string;
  file_name: string;
  file_type: string;
  file_size: number;
  notes?: string | null;
}

/**
 * DTO for creating a new invoice payment proof
 */
export interface CreateInvoicePaymentProofDto {
  invoice_id: string;
  file_path: string;
  file_name: string;
  file_type: string;
  file_size: number;
  notes?: string | null;
}
