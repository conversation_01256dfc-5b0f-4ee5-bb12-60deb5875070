import { BaseRecord } from "../../utils/database.types";

/**
 * Type for a single invoice change item
 * Will be serialized to JSON string when stored
 */
export interface InvoiceChangeItem {
  field: string; // e.g. "amount", "status", "due_date"
  from_value: any; // previous value
  to_value: any; // new value
}

/**
 * Simplified Invoice history model
 * Only tracks essential information with changes stored as JSON string
 * Leverages BaseRecord for tracking who made changes (updated_by) and when (updated_at)
 */

export interface InvoiceUpdateHistory extends BaseRecord {
  invoice_id: string; // Reference to the invoice record being modified

  // Stores a JSON string representation of InvoiceChangeItem[]
  // Example: '[{"field":"amount","from_value":1000,"to_value":1200},{"field":"status","from_value":"pending","to_value":"paid"}]'
  change_description: string;
}

/**
 * DTO for creating a new invoice history record
 */
export interface CreateInvoiceUpdateHistoryDto {
  invoice_id: string;
  change_description: string;
}

/**
 * Helper function to create the change description string
 */
export function createChangeDescription(changes: InvoiceChangeItem[]): string {
  return JSON.stringify(changes);
}

/**
 * Helper function to parse the change description
 */
export function parseChangeDescription(
  description: string
): InvoiceChangeItem[] {
  return JSON.parse(description);
}

/**
 * Helper function to create a complete InvoiceUpdateHistory DTO
 */
export function createInvoiceUpdateHistory(
  invoice_id: string,
  changes: InvoiceChangeItem[]
): CreateInvoiceUpdateHistoryDto {
  return {
    invoice_id,
    change_description: createChangeDescription(changes),
  };
}
