import { BaseRecord } from "../../utils/database.types";

// Enum for invoice types
export enum InvoiceType {
  EXTERNAL = "external",
  INTERNAL = "internal",
}

// Enum for service types
export enum ServiceType {
  HCM = "HCM",
  ORDEV = "ORDEV",
  BE = "BE",
  IT = "IT",
  MARKETING = "MARKETING",
  FINANCE = "FINANCE",
  SALES = "SALES",
  OTHER = "OTHER",
}

// Enum for payment status options
export enum PaymentStatus {
  PENDING = "pending",
  PARTIAL = "partial",
  PAID = "paid",
  OVERDUE = "overdue",
  CANCELLED = "cancelled",
}
// Enum for payment methods
// TODO: Update to include more banks (providers) for payment
export enum PaymentMethod {
  BANK_TRANSFER = "bank_transfer",
  CASH = "cash",
  CREDIT_CARD = "credit_card",
  CHEQUE = "cheque",
  OTHER = "other",
}

//Interface for invoice items
export interface InvoiceItem extends BaseRecord {
  invoice_id: string;
  item_name: string;
  item_amount: number;
  item_price: number;
  total_price: number;
}

// Interface representing an Invoice record
export interface Invoice extends BaseRecord {
  invoice_number: string;
  invoice_type: InvoiceType;
  service_type: ServiceType; // Used for invoice number generation and stored in database
  recipient_name: string;
  project_id?: string;
  project_name?: string;
  due_date: string; // Changed from Date to string for YYYY-MM-DD format
  payment_method: PaymentMethod;
  payment_status: PaymentStatus;
  notes: string | null;
  total_amount: number;
  items?: InvoiceItem[]; // Virtual field, not stored in database directly but joined at runtime
}

// DTO for creating a new invoice item
export interface CreateInvoiceItemDto {
  invoice_id?: string;
  item_name: string;
  item_amount: number;
  item_price: number;
  total_price: number;
}

// DTO for creating a new invoice
export interface CreateInvoiceDto {
  invoice_number: string;
  invoice_type: InvoiceType;
  service_type: ServiceType;
  recipient_name: string;
  project_id?: string;
  project_name?: string;
  due_date: string; // Changed from Date to string for YYYY-MM-DD format
  payment_method: PaymentMethod;
  payment_status?: PaymentStatus; // Default will be PENDING
  notes?: string | null;
  total_amount: number;
  items: CreateInvoiceItemDto[];
}

// DTO for updating an invoice item
export interface UpdateInvoiceItemDto {
  id: string;
  item_name?: string;
  item_amount?: number;
  item_price?: number;
}

// DTO for updating an invoice
export interface UpdateInvoiceDto {
  invoice_number?: string;
  invoice_type?: InvoiceType;
  service_type?: ServiceType;
  recipient_name?: string;
  project_id?: string;
  project_name?: string;
  due_date?: string; // Changed from Date to string for YYYY-MM-DD format
  payment_method?: PaymentMethod;
  payment_status?: PaymentStatus;
  notes?: string | null;
  total_amount?: number;
  items?: UpdateInvoiceItemDto[];
}

// DTO for deleting an invoice
export interface DeleteInvoiceDto {
  id: string;
}

// DTO for deleting an invoice item
export interface DeleteInvoiceItemDto {
  id: string;
}
