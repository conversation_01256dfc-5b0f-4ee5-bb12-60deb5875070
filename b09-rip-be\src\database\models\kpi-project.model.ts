/**
 * KPI Project model interfaces
 */
import { BaseRecord } from "../../utils/database.types";
import { ProjectCategory, ProjectStatus } from "./project.model";

/**
 * Enum for KPI success status
 */
export enum KpiStatus {
  NOT_STARTED = "not_started",
  IN_PROGRESS = "in_progress",
  COMPLETED_BELOW_TARGET = "completed_below_target",
  COMPLETED_ON_TARGET = "completed_on_target",
  COMPLETED_ABOVE_TARGET = "completed_above_target",
}

/**
 * Interface representing a KPI Project record
 */
export interface KpiProject extends BaseRecord {
  project_name: string;
  project_id: string;
  description: string;
  target: string;
  period: string;
  status: KpiStatus;
  additional_notes: string | null;
}

/**
 * DTO for creating a new KPI Project
 */
export interface CreateKpiProjectDto {
  project_name: string;
  project_id: string;
  description: string;
  target: string;
  period: string;
  status?: KpiStatus; // Default is NOT_STARTED
  additional_notes?: string | null;
}

/**
 * DTO for updating an existing KPI Project
 */
export interface UpdateKpiProjectDto {
  project_name?: string;
  project_id?: string;
  description?: string;
  target?: string;
  period?: string;
  status?: KpiStatus;
  additional_notes?: string | null;
}

/**
 * DTO for deleting a KPI Project
 */
export interface DeleteKpiProjectDto {
  id: string;
}

/**
 * Interface for KPI Project with additional project details
 */
export interface KpiProjectWithDetails extends KpiProject {
  projects?: {
    organization_id: string;
    pic_project: string;
    project_name: string;
    project_category: ProjectCategory;
    status_project: ProjectStatus;
  };
}
