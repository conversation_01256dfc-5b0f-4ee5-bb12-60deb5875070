import { BaseRecord } from "../../utils/database.types";

// Enum for KPI success status
export enum KpiStatus {
  NOT_STARTED = "not_started",
  IN_PROGRESS = "in_progress",
  COMPLETED_BELOW_TARGET = "completed_below_target",
  COMPLETED_ON_TARGET = "completed_on_target",
  COMPLETED_ABOVE_TARGET = "completed_above_target",
}

// Interface representing a KPI record
export interface <PERSON><PERSON> extends BaseRecord {
  full_name: string;
  employee_id: string;
  description: string;
  target: string;
  period: string;
  status: KpiStatus;
  bonus_received: number | null;
  additional_notes: string | null;
}

// DTO for creating a new KPI
export interface CreateKpiDto {
  full_name: string;
  employee_id: string;
  description: string;
  target: string;
  period: string;
  status?: KpiStatus; // Default will be NOT_STARTED
  bonus_received?: number | null;
  additional_notes?: string | null;
}

// DTO for updating an existing KPI
export interface UpdateKpiDto {
  full_name?: string;
  employee_id?: string;
  description?: string;
  target?: string;
  period?: string;
  status?: KpiStatus;
  bonus_received?: number | null;
  additional_notes?: string | null;
}

// DTO for deleting a KPI
export interface DeleteKpiDto {
  id: string;
}
