/**
 * Note model interfaces
 */
import { BaseRecord } from "../../utils/database.types";

/**
 * Status enum for notes
 */
export enum NoteStatus {
  NOT_STARTED = "not_started",
  PENDING = "pending",
  COMPLETED = "completed",
}

/**
 * Base Note interface representing the database table
 */
export interface Note extends BaseRecord {
  title: string;
  notes: string | null;
  status: NoteStatus;
}

/**
 * Data transfer object for creating a new note
 */
export interface CreateNoteDto {
  title: string;
  notes?: string | null;
  status?: NoteStatus; // Default will be NOT_STARTED
}

/**
 * Data transfer object for updating an existing note
 */
export interface UpdateNoteDto {
  title?: string;
  notes?: string | null;
  status?: NoteStatus;
}

/**
 * Data transfer object for deleting a note (soft delete)
 */
export interface DeleteNoteDto {
  id: string;
}
