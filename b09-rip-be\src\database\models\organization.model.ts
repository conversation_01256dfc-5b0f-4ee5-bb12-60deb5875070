/**
 * Organization model interfaces
 */
import { BaseRecord } from "../../utils/database.types";

export interface Organization extends BaseRecord {
  name: string;
  phone: string;
  address: string;
  client_type: string;
  notes: string | null;
}

export interface CreateOrganizationDto {
  name: string;
  phone: string;
  address: string;
  client_type: string;
  notes?: string | null;
}

export interface UpdateOrganizationDto {
  name?: string;
  phone?: string;
  address?: string;
  client_type?: string;
  notes?: string | null;
}
