/**
 * Project Charter model interfaces
 */
import { BaseRecord } from "../../utils/database.types";

/**
 * Interface for Project Charter model
 */
export interface ProjectCharter extends BaseRecord {
  project_id: string; // UUID reference to projects table
  key_stakeholders: string;
  project_authority: string;
  project_description: string;
  objective_and_key_results: string;
  purpose: string;
  key_assumption: string;
  assumptions_constrains_risks: string;
  high_level_resources: string;
  high_level_milestones: string;
  statement_prediction_of_benefit: string;
  approval: boolean;
}

/**
 * DTO for creating a project charter
 */
export interface CreateProjectCharterDto {
  project_id: string;
  key_stakeholders: string;
  project_authority: string;
  project_description: string;
  objective_and_key_results: string;
  purpose: string;
  key_assumption: string;
  assumptions_constrains_risks: string;
  high_level_resources: string;
  high_level_milestones: string;
  statement_prediction_of_benefit: string;
  approval: boolean;
}

/**
 * DTO for updating a project charter
 */
export interface UpdateProjectCharterDto {
  key_stakeholders?: string;
  project_authority?: string;
  project_description?: string;
  objective_and_key_results?: string;
  purpose?: string;
  key_assumption?: string;
  assumptions_constrains_risks?: string;
  high_level_resources?: string;
  high_level_milestones?: string;
  statement_prediction_of_benefit?: string;
  approval?: boolean;
}
