import { BaseRecord } from "../../utils/database.types";
import { TaskStatus } from "./task.model"; // Add import for TaskStatus enum

// Project Task Model
export interface ProjectTask extends BaseRecord {
  assigned_by: string; // User ID or name
  description: string;
  completion_status: TaskStatus; // Changed from boolean to TaskStatus enum
  employee_id: string;
  initial_date: string; // Format YYYY-MM-DD
  due_date: string; // Format YYYY-MM-DD
  attendance_id?: string; // Optional reference to an attendance record
  project_id: string; // Reference to the project
  weekly_log_id?: string; // Optional reference to a weekly log
}

export interface CreateProjectTaskDto {
  assigned_by: string;
  description: string;
  completion_status: TaskStatus; // Changed from boolean to TaskStatus enum
  employee_id: string;
  initial_date: string; // Format YYYY-MM-DD
  due_date: string; // Format YYYY-MM-DD
  attendance_id?: string;
  project_id: string;
  weekly_log_id?: string;
}

export interface UpdateProjectTaskDto {
  assigned_by?: string;
  description?: string;
  completion_status?: TaskStatus; // Changed from boolean to TaskStatus enum
  employee_id?: string;
  initial_date: string; // Format YYYY-MM-DD
  due_date?: string; // Format YYYY-MM-DD
  attendance_id?: string;
  project_id?: string;
  weekly_log_id?: string;
}

export interface DeleteProjectTaskDto {
  id: string;
}
