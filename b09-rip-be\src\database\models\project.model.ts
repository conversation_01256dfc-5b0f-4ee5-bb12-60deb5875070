/**
 * Project model interfaces
 */
import { BaseRecord } from "../../utils/database.types";

/**
 * Enum for project status
 */
export enum ProjectStatus {
  NOT_STARTED = "Not Started",
  IN_PROGRESS = "In Progress",
  COMPLETED = "Completed",
  CANCELLED = "Cancelled",
}

/**
 * Enum for project categories
 */
export enum ProjectCategory {
  PRELIMINARY_RESEARCH = "Preliminary Research",
  ADMINISTRASI = "Administrasi",
  MONITORING = "Monitoring",
  DIGITAL_MARKETING = "Digital Marketing",
  BRAND_AUDIT = "Brand Audit",
  BRAND_STRATEGY = "Brand Strategy",
  DRAFT_MONTHLY_REPORT = "Draft Monthly Report",
}

/**
 * Interface for Project model
 */
export interface Project extends BaseRecord {
  organization_id: string;
  project_category: ProjectCategory;
  project_name: string;
  pic_project: string; // Employee ID
  start_project: string; // Format YYYY-MM-DD
  end_project: string; // Format YYYY-MM-DD
  status_project: ProjectStatus;
  budget_project: string;
  gantt_chart_id?: string; // Optional, will be auto-generated in the future
  project_charter_id?: string; // UUID reference to project_charters table
  objectives: string;
}

/**
 * DTO for creating a project
 */
export interface CreateProjectDto {
  organization_id: string;
  project_category: ProjectCategory;
  project_name: string;
  pic_project: string; // Employee ID
  start_project: string; // Format YYYY-MM-DD
  end_project: string; // Format YYYY-MM-DD
  status_project?: ProjectStatus; // Default: NOT_STARTED
  budget_project: string;
  objectives: string;
  // Note: gantt_chart_id and project_charter_id are not included as they will be auto-generated
}

/**
 * DTO for updating a project
 */
export interface UpdateProjectDto {
  organization_id?: string;
  project_category?: ProjectCategory;
  project_name?: string;
  pic_project?: string; // Employee ID
  start_project?: string; // Format YYYY-MM-DD
  end_project?: string; // Format YYYY-MM-DD
  status_project?: ProjectStatus;
  budget_project?: string;
  objectives?: string;
  gantt_chart_id?: string; // Optional, for updates from other modules
  project_charter_id?: string; // Optional, for updates from project charter module
}

/**
 * DTO for deleting a project
 */
export interface DeleteProjectDto {
  id: string;
}
