import { BaseRecord } from "../../utils/database.types";

/**
 * Base types for field values
 */
export type BasicFieldValue = string | number | boolean | null;

/**
 * Base interface for all change items
 */
export interface BaseChangeItem {
  field: string;
  changeType: "field" | "component";
}

/**
 * Interface for simple field changes (e.g., payment_status)
 */
export interface SimpleFieldChangeItem extends BaseChangeItem {
  changeType: "field";
  subType: "simple";
  from_value: BasicFieldValue;
  to_value: BasicFieldValue;
}

/**
 * Interface for base salary changes with total salary tracking
 */
export interface BaseSalaryChangeItem extends BaseChangeItem {
  changeType: "field";
  subType: "base_salary";
  from_value: {
    amount: number;
    total_salary: number;
  };
  to_value: {
    amount: number;
    total_salary: number;
  };
}

/**
 * Interface for component changes (bonus, deduction, allowance)
 */
export interface ComponentChangeItem extends BaseChangeItem {
  changeType: "component";
  componentType: "bonus" | "deduction" | "allowance";
  action: "add" | "update" | "delete";
  from_value: {
    type: string;
    total: number;
    total_salary?: number;
  };
  to_value: {
    type: string;
    amount?: number;
    total: number;
    total_salary?: number;
  };
}

/**
 * Union type for all possible change items
 */
export type SalaryChangeItem =
  | SimpleFieldChangeItem
  | BaseSalaryChangeItem
  | ComponentChangeItem;

/**
 * Simplified Salary history model
 * Only tracks essential information with changes stored as JSON string
 * Leverages BaseRecord for tracking who made changes (updated_by) and when (updated_at)
 */
export interface SalaryHistory extends BaseRecord {
  salary_id: string; // Reference to the salary record being modified

  // Stores a JSON string representation of SalaryChangeItem[]
  // Example: '[{"field":"base_salary","from_value":5000,"to_value":5500},{"field":"bonus","from_value":0,"to_value":200}]'
  change_description: string;
}

/**
 * DTO for creating a new salary history record
 */
export interface CreateSalaryHistoryDto {
  salary_id: string;
  change_description: string;
}

/**
 * Helper function to create the change description string
 */
export function createChangeDescription(changes: SalaryChangeItem[]): string {
  return JSON.stringify(changes);
}

/**
 * Helper function to parse the change description
 * Note: This will parse the JSON but won't validate against the SalaryChangeItem type
 * Additional validation may be needed for type safety
 */
export function parseChangeDescription(
  description: string
): SalaryChangeItem[] {
  const parsed = JSON.parse(description);

  // Convert legacy format to new format if needed
  return parsed.map((item: any) => {
    // If the item already has a changeType, assume it's in the new format
    if (item.changeType) {
      return item;
    }

    // Handle legacy format conversion
    if (
      item.field === "base_salary" &&
      typeof item.from_value === "number" &&
      typeof item.to_value === "number"
    ) {
      // Convert legacy base_salary format to new format
      // Note: We can't determine the total_salary from legacy data, so we use the values as is
      return {
        field: item.field,
        changeType: "field",
        subType: "base_salary",
        from_value: {
          amount: item.from_value,
          total_salary: item.from_value, // Best guess without actual data
        },
        to_value: {
          amount: item.to_value,
          total_salary: item.to_value, // Best guess without actual data
        },
      } as BaseSalaryChangeItem;
    } else if (item.field && item.field.includes("_")) {
      // Try to detect component changes from legacy format
      const [componentType, action] = item.field.split("_");
      if (
        ["bonus", "deduction", "allowance"].includes(componentType) &&
        ["add", "update", "delete"].includes(action)
      ) {
        return {
          field: item.field,
          changeType: "component",
          componentType: componentType as "bonus" | "deduction" | "allowance",
          action: action as "add" | "update" | "delete",
          from_value: item.from_value,
          to_value: item.to_value,
        } as ComponentChangeItem;
      }
    }

    // Default to simple field change for legacy format
    return {
      field: item.field,
      changeType: "field",
      subType: "simple",
      from_value: item.from_value,
      to_value: item.to_value,
    } as SimpleFieldChangeItem;
  });
}

/**
 * Helper function to create a simple field change item
 */
export function createSimpleFieldChange(
  field: string,
  fromValue: BasicFieldValue,
  toValue: BasicFieldValue
): SimpleFieldChangeItem {
  return {
    field,
    changeType: "field",
    subType: "simple",
    from_value: fromValue,
    to_value: toValue,
  };
}

/**
 * Helper function to create a base salary change item with total salary tracking
 */
export function createBaseSalaryChange(
  fromAmount: number,
  toAmount: number,
  fromTotalSalary: number,
  toTotalSalary: number
): BaseSalaryChangeItem {
  return {
    field: "base_salary",
    changeType: "field",
    subType: "base_salary",
    from_value: {
      amount: fromAmount,
      total_salary: fromTotalSalary,
    },
    to_value: {
      amount: toAmount,
      total_salary: toTotalSalary,
    },
  };
}

/**
 * Helper function to create a component change item
 */
export function createComponentChange(
  componentType: "bonus" | "deduction" | "allowance",
  action: "add" | "update" | "delete",
  typeName: string,
  amount: number,
  oldTotal: number,
  newTotal: number,
  oldTotalSalary?: number,
  newTotalSalary?: number
): ComponentChangeItem {
  return {
    field: `${componentType}_${action}`,
    changeType: "component",
    componentType,
    action,
    from_value: {
      type: typeName,
      total: oldTotal,
      total_salary: oldTotalSalary,
    },
    to_value: {
      type: typeName,
      amount,
      total: newTotal,
      total_salary: newTotalSalary,
    },
  };
}

/**
 * Helper function to create a complete SalaryHistory DTO
 */
export function createSalaryHistory(
  salary_id: string,
  changes: SalaryChangeItem[]
): CreateSalaryHistoryDto {
  return {
    salary_id,
    change_description: createChangeDescription(changes),
  };
}
