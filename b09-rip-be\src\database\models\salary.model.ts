import { BaseRecord } from "../../utils/database.types";

/**
 * Enum for salary payment status
 */
export enum SalaryPaymentStatus {
  UNPAID = "unpaid",
  PAID = "paid",
}

/**
 * Salary model interface representing the database table
 */
export interface Salary extends BaseRecord {
  employee_id: string;
  base_salary: number;
  total_bonus: number;
  total_deduction: number;
  total_allowance: number;
  total_salary: number;
  payment_status: SalaryPaymentStatus;
  period: string; // Format YYYY-MM
}

/**
 * Unified DTO for updating salary details
 * Note: Bonus, deduction, and allowance should be managed through their respective modules
 * Role-based permissions are enforced at the service level
 */
export interface UpdateSalaryDto {
  base_salary?: number;
  payment_status?: SalaryPaymentStatus;
}
