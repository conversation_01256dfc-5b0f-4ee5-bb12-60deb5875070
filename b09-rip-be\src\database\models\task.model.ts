import { BaseRecord } from "../../utils/database.types";

// Enum untuk status penyelesaian tugas
export enum TaskStatus {
  NOT_COMPLETED = "not_completed",
  ON_PROGRESS = "on_progress",
  COMPLETED = "completed",
}

// Task Model - Regular tasks use boolean completion_status
export interface Task extends BaseRecord {
  description: string;
  completion_status: boolean; // Changed back to boolean for regular tasks
  employee_id: string;
  due_date: string; // Format YYYY-MM-DD
  attendance_id?: string; // Optional reference to an attendance record
}

export interface CreateTaskDto {
  description: string;
  completion_status: boolean; // Changed back to boolean for regular tasks
  employee_id: string;
  due_date: string; // Format YYYY-MM-DD
  attendance_id?: string; // Optional reference to an attendance record
}

export interface UpdateTaskDto {
  description?: string;
  completion_status?: boolean; // Changed back to boolean for regular tasks
  employee_id?: string;
  due_date?: string; // Format YYYY-MM-DD
  attendance_id?: string; // Optional reference to an attendance record
}

export interface DeleteTaskDto {
  id: string;
}
