/**
 * User Profile model interfaces
 */
import { BaseRecord } from "../../utils/database.types";

/**
 * Define the user roles
 */
export enum UserRole {
  Admin = "Admin",
  Manager = "Manager",
  HR = "HR",
  Finance = "Finance",
  Operation = "Operation",
  Client = "Client",
}

/**
 * Base UserProfile interface representing the database table
 */
export interface UserProfile extends BaseRecord {
  user_id: string;
  fullname: string;
  phonenum: string;
  role: UserRole;
  org_id?: string;
  employee_id?: string;
  is_active?: boolean;
}

/**
 * Data transfer object for creating a new user profile
 */
export interface CreateUserProfileDto {
  user_id: string;
  fullname: string;
  phonenum: string;
  role: UserRole;
  org_id?: string;
  employee_id?: string;
  is_active?: boolean;
}

/**
 * Data transfer object for updating an existing user profile
 */
export interface UpdateUserProfileDto {
  fullname?: string;
  phonenum?: string;
  role?: UserRole;
  org_id?: string;
  employee_id?: string;
  is_active?: boolean;
}

/**
 * Extended user interface with profile data
 */
export interface UserWithProfile {
  id: string;
  email: string;
  profile: UserProfile;
}
