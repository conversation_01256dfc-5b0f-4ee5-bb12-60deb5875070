import { BaseRecord } from "../../utils/database.types";
import { ProjectTask } from "./project-task.model";
import { TaskStatus } from "./task.model";

export interface WeeklyLog extends BaseRecord {
  week_number: number;
  week_start_date: string;
  week_end_date: string;
  project_id: string;
}

export interface WeeklyLogNote extends BaseRecord {
  note: string;
  weekly_log_id: string;
  day_of_week: number; // 1-5 representing Monday-Friday
}

// DTOs for weekly log operations
export interface CreateWeeklyLogDto {
  week_number: number;
  week_start_date: string;
  week_end_date: string;
  project_id: string;
}

// DTO for batch updating notes for a weekly log
export interface UpdateWeeklyLogNotesDto {
  // Notes for each day of the week (1-5 for Monday-Friday)
  // If a day is not included, its note remains unchanged
  // If a day's note is an empty string (""), any existing note for that day will be deleted
  // If a day's note is a non-empty string, it will create or update the note for that day
  notes: {
    [day_of_week: number]: string;
  };
}

// Response type for batch update operation
export interface BatchUpdateResult {
  created: number; // Number of notes created
  updated: number; // Number of notes updated
  deleted: number; // Number of notes deleted
  unchanged: number; // Number of days with no changes
}

// Interface for day-specific data
export interface WeeklyLogDayData {
  date: string; // YYYY-MM-DD format
  day_of_week: number; // 1-5 for Monday-Friday
  note?: WeeklyLogNote; // The note for this day (if any)
  activities: {
    starting: ProjectTask[]; // Tasks that start on this day
    ending: ProjectTask[]; // Tasks that end on this day
    ongoing: ProjectTask[]; // Tasks that are ongoing on this day
    not_completed: ProjectTask[]; // Tasks that are not completed
    on_progress: ProjectTask[]; // Tasks that are in progress
    completed: ProjectTask[]; // Tasks that are completed
  };
}

// Response type for weekly log with notes grouped by day
export interface WeeklyLogWithNotes extends WeeklyLog {
  notes_by_day: {
    [key: number]: WeeklyLogNote & {
      created_by_name?: string;
      updated_by_name?: string;
    }; // Key is day_of_week (1-5), value is a single note with user names
  };
  project_name?: string;
  created_by_name?: string;
  updated_by_name?: string;
  tasks?: (ProjectTask & {
    employee_name?: string;
    assigned_by_name?: string;
    created_by_name?: string;
    updated_by_name?: string;
  })[]; // Project tasks associated with this weekly log with user names
  days_data: {
    [key: number]: WeeklyLogDayData; // Key is day_of_week (1-5)
  };
}

// Commented out legacy interfaces
// export interface WeeklyTaskSummary {
// task_id: string;
// employee_id: string;
// description: string;
// assigned_by: string;
// completion_status: boolean;
// due_date: string;       // Format YYYY-MM-DD
// initial_date: string;   // Format YYYY-MM-DD
// }

// // Model Weekly Log
// export interface WeeklyLog extends BaseRecord {
// week_start_date: string; // Senin - YYYY-MM-DD
// week_end_date: string;   // Minggu - YYYY-MM-DD
// tasks: WeeklyTaskSummary[];
// additional_notes?: string;
// }

// // DTO untuk membuat Weekly Log baru
// export interface CreateWeeklyLogDto {
// week_start_date: string;
// week_end_date: string;
// project_id: string;
// tasks: WeeklyTaskSummary[];
// additional_notes?: string;
// }

// // DTO untuk memperbarui Weekly Log
// export interface UpdateWeeklyLogDto {
// additional_notes?: string;
// }

// // DTO untuk menghapus Weekly Log
// export interface DeleteWeeklyLogDto {
// id: string;
// }
