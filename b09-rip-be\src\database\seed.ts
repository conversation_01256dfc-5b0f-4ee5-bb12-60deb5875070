// src/database/seed.ts
import { supabase } from "../libs/supabase";
import fs from "fs";
import path from "path";

/**
 * Function to run seed data
 */
async function runSeeds() {
  const seedsDir = path.join(__dirname, "seeds");
  const seedFiles = fs
    .readdirSync(seedsDir)
    .filter((file) => file.endsWith(".sql"))
    .sort();

  console.log("Starting seeds...");

  for (const file of seedFiles) {
    console.log(`Running seed: ${file}`);
    const sql = fs.readFileSync(path.join(seedsDir, file), "utf8");

    try {
      // For Supabase, we need a stored procedure to execute SQL
      // You'll need to create the exec_sql function in your Supabase project first
      const { error } = await supabase.rpc("exec_sql", { sql });

      if (error) {
        console.error(`Error: ${error.message}`);
        throw error;
      }

      console.log(`Successfully executed: ${file}`);
    } catch (error) {
      console.error(`Error executing ${file}:`, error);
      process.exit(1);
    }
  }

  console.log("All seeds completed successfully!");
}

// Run the seeds
runSeeds().catch(console.error);
