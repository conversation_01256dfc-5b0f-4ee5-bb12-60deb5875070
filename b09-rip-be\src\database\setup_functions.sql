-- Function to execute arbitrary SQL
-- This needs to be run directly in the Supabase SQL editor before migrations
CREATE OR REPLACE FUNCTION public.exec_sql(sql text)
RETURNS void
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = public
AS $$
BEGIN
  EXECUTE sql;
END;
$$;

-- Comment on function
COMMENT ON FUNCTION public.exec_sql(text) IS 'Execute arbitrary SQL with proper security definer context';

-- Grant execute permission to service role only
-- Note: You may need to adjust permissions based on your security requirements
REVOKE ALL ON FUNCTION public.exec_sql(text) FROM PUBLIC;
GRANT EXECUTE ON FUNCTION public.exec_sql(text) TO authenticated;
GRANT EXECUTE ON FUNCTION public.exec_sql(text) TO service_role; 