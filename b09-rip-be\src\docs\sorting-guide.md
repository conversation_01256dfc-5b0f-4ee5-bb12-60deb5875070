# Sorting Guide for dbUtils

This guide explains how to use the sorting functionality in the dbUtils module.

## Basic Sorting

You can sort query results by specifying a `sort` option in the `QueryOptions` object:

```typescript
import { dbUtils } from "../utils/database";
import { QueryOptions } from "../utils/database.types";

// Sort by created_at in ascending order
const options: QueryOptions = {
  sort: {
    field: "created_at",
    direction: "asc" // "asc" or "desc"
  }
};

const { data, error } = await dbUtils.getAll("your_table", options);
```

## Default Sorting

If no sort option is provided, results are sorted by `created_at` in descending order (newest first).

## Multi-Field Sorting

You can sort by multiple fields by providing an array of sort options:

```typescript
const options: QueryOptions = {
  sort: [
    { field: "status", direction: "asc" },  // Sort by status first
    { field: "created_at", direction: "desc" } // Then by created_at
  ]
};

const { data, error } = await dbUtils.getAll("your_table", options);
```

## Combining with Other Options

Sorting can be combined with other query options like filtering, searching, and pagination:

```typescript
const options: QueryOptions = {
  sort: { field: "due_date", direction: "asc" },
  filters: [
    { field: "status", value: "pending" }
  ],
  pagination: {
    page: 1,
    pageSize: 10
  },
  search: {
    term: "search term",
    fields: ["name", "description"]
  }
};

const { data, error } = await dbUtils.getAll("your_table", options);
```

## Examples

See the `src/examples/sort-example.ts` file for complete examples of how to use the sorting functionality.

## Sort Option Interface

```typescript
interface SortOption {
  /** Field name to sort by */
  field: string;
  /** Sort direction (default: asc) */
  direction?: "asc" | "desc";
}
```

## QueryOptions Interface

```typescript
interface QueryOptions {
  /** Search parameters */
  search?: SearchOptions;
  /** Filter parameters */
  filters?: FilterOption[];
  /** Pagination parameters */
  pagination?: PaginationOptions;
  /** Sort parameters - can be a single sort option or an array for multi-field sorting */
  sort?: SortOption | SortOption[];
  /** Whether to include soft-deleted records */
  includeSoftDeleted?: boolean;
}
```
