import { dbUtils } from "../utils/database";
import { QueryOptions, SortOption } from "../utils/database.types";
import { Invoice } from "../database/models/invoice.model";

/**
 * This file demonstrates how to use the sorting functionality in dbUtils
 */

// Example 1: Sort by a single field in ascending order
async function sortByDueDateAscending() {
  console.log("Example 1: Sort invoices by due_date in ascending order");
  
  const sortOption: SortOption = {
    field: "due_date",
    direction: "asc" // ascending is the default, so this could be omitted
  };
  
  const options: QueryOptions = {
    sort: sortOption,
    pagination: {
      page: 1,
      pageSize: 10
    }
  };
  
  const { data, error } = await dbUtils.getAll<Invoice>("invoices", options);
  
  if (error) {
    console.error("Error:", error);
    return;
  }
  
  console.log(`Found ${data?.length || 0} invoices sorted by due_date (ascending)`);
  data?.forEach(invoice => {
    console.log(`Invoice #${invoice.invoice_number} - Due: ${invoice.due_date}`);
  });
}

// Example 2: Sort by a single field in descending order
async function sortByTotalAmountDescending() {
  console.log("\nExample 2: Sort invoices by total_amount in descending order");
  
  const options: QueryOptions = {
    sort: {
      field: "total_amount",
      direction: "desc"
    },
    pagination: {
      page: 1,
      pageSize: 10
    }
  };
  
  const { data, error } = await dbUtils.getAll<Invoice>("invoices", options);
  
  if (error) {
    console.error("Error:", error);
    return;
  }
  
  console.log(`Found ${data?.length || 0} invoices sorted by total_amount (descending)`);
  data?.forEach(invoice => {
    console.log(`Invoice #${invoice.invoice_number} - Amount: ${invoice.total_amount}`);
  });
}

// Example 3: Sort by multiple fields (payment_status first, then by created_at)
async function sortByMultipleFields() {
  console.log("\nExample 3: Sort invoices by payment_status, then by created_at");
  
  const options: QueryOptions = {
    sort: [
      { field: "payment_status", direction: "asc" },
      { field: "created_at", direction: "desc" }
    ],
    pagination: {
      page: 1,
      pageSize: 10
    }
  };
  
  const { data, error } = await dbUtils.getAll<Invoice>("invoices", options);
  
  if (error) {
    console.error("Error:", error);
    return;
  }
  
  console.log(`Found ${data?.length || 0} invoices sorted by payment_status, then created_at`);
  data?.forEach(invoice => {
    console.log(`Invoice #${invoice.invoice_number} - Status: ${invoice.payment_status}, Created: ${invoice.created_at}`);
  });
}

// Example 4: Combine sorting with filtering
async function sortAndFilter() {
  console.log("\nExample 4: Sort and filter invoices");
  
  const options: QueryOptions = {
    sort: { field: "due_date", direction: "asc" },
    filters: [
      { field: "payment_status", value: "pending" }
    ],
    pagination: {
      page: 1,
      pageSize: 10
    }
  };
  
  const { data, error } = await dbUtils.getAll<Invoice>("invoices", options);
  
  if (error) {
    console.error("Error:", error);
    return;
  }
  
  console.log(`Found ${data?.length || 0} pending invoices sorted by due_date`);
  data?.forEach(invoice => {
    console.log(`Invoice #${invoice.invoice_number} - Due: ${invoice.due_date}, Status: ${invoice.payment_status}`);
  });
}

// Run all examples
async function runExamples() {
  try {
    await sortByDueDateAscending();
    await sortByTotalAmountDescending();
    await sortByMultipleFields();
    await sortAndFilter();
  } catch (error) {
    console.error("Error running examples:", error);
  }
}

// Uncomment to run the examples
// runExamples();

// Export the examples for use in other files
export {
  sortByDueDateAscending,
  sortByTotalAmountDescending,
  sortByMultipleFields,
  sortAndFilter,
  runExamples
};
