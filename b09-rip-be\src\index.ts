import { <PERSON><PERSON> } from "elysia";
import { cors } from "@elysiajs/cors";
import { auth } from "./modules/auth";
import { admin } from "./modules/admin";
import { organizations } from "./modules/organization";
import { invoices } from "./modules/invoice";
import { employee } from "./modules/employee";
import { salaries } from "./modules/salary";
import { attendances } from "./modules/attendance";
import { kpis } from "./modules/kpi";
import { projects } from "./modules/project";
import { deductionSalaries } from "./modules/deduction-salary";
import { allowanceSalaries } from "./modules/allowance-salary";
import { bonusSalaries } from "./modules/bonus-salary";
import { kpiProjects } from "./modules/kpi-project";
import { projectTaskModule } from "./modules/project-task";
import { tasks } from "./modules/task";
import { weeklyLogs } from "./modules/weekly-log";
import invoicePaymentProofModule from "./modules/invoice-payment-proof";
import { projectCharters } from "./modules/project-charter";
import { swaggerConfig } from "./config/swagger";
import { globalErrorHandler, apiResponse } from "./middleware/api-response";
import { requestLogger } from "./middleware/request-logger";

// Define allowed origins
const allowedOrigins =
  process.env.CORS_ALLOWED_ORIGINS === "*"
    ? true // Use boolean true to allow all origins
    : process.env.CORS_ALLOWED_ORIGINS?.split(",") || ["http://localhost:3001"];

const app = new Elysia()
  // Add favicon route first to prevent 404 errors
  .get("/favicon.ico", ({ set }) => {
    set.status = 204; // No Content
    return "";
  })

  // Add request logging
  .use(requestLogger)

  // Update error handler to include request information
  .onError(({ code, error, set, request }) =>
    globalErrorHandler({ error, code, set, request })
  )

  .use(apiResponse)
  .use(swaggerConfig)
  .use(
    cors({
      origin: allowedOrigins,
      methods: "GET,POST,PUT,PATCH,DELETE,HEAD,OPTIONS",
    })
  )
  // Group all API routes under v1 prefix
  .group("/v1", (app) =>
    app
      .use(auth)
      .use(admin)
      .use(employee)
      .use(organizations)
      .use(invoices)
      .use(invoicePaymentProofModule)
      .use(salaries)
      .use(attendances)
      .use(kpis)
      .use(projects)
      .use(deductionSalaries)
      .use(allowanceSalaries)
      .use(bonusSalaries)
      .use(kpiProjects)
      .use(projectTaskModule)
      .use(tasks)
      .use(weeklyLogs)
      .use(projectCharters)
  )
  .get("/", () => "Hello Elysia")
  .listen(3000);

console.log(
  `🦊 Elysia is running at ${app.server?.hostname}:${app.server?.port}`
);
