import { <PERSON><PERSON> } from "elysia";
import {
  ApiResponse,
  successResponse,
  errorResponse,
} from "../utils/api-response";

// -----------------------------------------------------------------------
// 1. STANDARDIZED HTTP STATUS RESPONSES
// -----------------------------------------------------------------------

/**
 * Standardized HTTP response handlers for API middleware
 */
export const HttpResponses = {
  /**
   * Success response (200 OK)
   */
  success: <T>(data: T, message: string, set: any): ApiResponse<T> => {
    set.status = 200;
    set.headers["Content-Type"] = "application/json";
    return successResponse(data, message);
  },

  /**
   * Unauthorized error response (401)
   */
  unauthorized: (
    message: string,
    errorCode: string | undefined,
    set: any
  ): ApiResponse => {
    set.status = 401;
    set.headers["Content-Type"] = "application/json";
    return errorResponse(message, null, errorCode);
  },

  /**
   * Forbidden error response (403)
   */
  forbidden: (
    message: string,
    errorCode: string | undefined,
    set: any
  ): ApiResponse => {
    set.status = 403;
    set.headers["Content-Type"] = "application/json";
    return errorResponse(message, null, errorCode);
  },

  /**
   * Not found error response (404)
   */
  notFound: (
    message: string,
    errorCode: string | undefined,
    set: any
  ): ApiResponse => {
    set.status = 404;
    set.headers["Content-Type"] = "application/json";
    return errorResponse(message, null, errorCode);
  },

  /**
   * Bad request error response (400)
   */
  badRequest: (
    message: string,
    errorCode: string | undefined,
    set: any
  ): ApiResponse => {
    set.status = 400;
    set.headers["Content-Type"] = "application/json";
    return errorResponse(message, null, errorCode);
  },

  /**
   * Internal server error response (500)
   */
  serverError: (
    message: string,
    error: Error | undefined,
    set: any
  ): ApiResponse => {
    set.status = 500;
    set.headers["Content-Type"] = "application/json";

    // We don't log the error here anymore to avoid duplicate logs
    // The globalErrorHandler will handle logging

    return errorResponse(
      message,
      null,
      "INTERNAL_SERVER_ERROR",
      error ? { stack: error.stack } : undefined
    );
  },
};

/**
 * API Response middleware for Elysia
 * Provides utility methods for consistent HTTP responses
 */
export const apiResponse = new Elysia()
  .state("responseHandlersApplied", true)
  .derive({ as: "global" }, ({ set }) => {
    return {
      /**
       * Send a successful response with 200 status code
       */
      success: <T>(data: T, message = "Operation successful") => {
        return HttpResponses.success(data, message, set);
      },

      /**
       * Send an unauthorized error response with 401 status code
       */
      unauthorized: (
        message = "Authentication required",
        errorCode?: string
      ) => {
        return HttpResponses.unauthorized(message, errorCode, set);
      },

      /**
       * Send a forbidden error response with 403 status code
       */
      forbidden: (
        message = "You don't have permission to access this resource",
        errorCode?: string
      ) => {
        return HttpResponses.forbidden(message, errorCode, set);
      },

      /**
       * Send a not found error response with 404 status code
       */
      notFound: (message = "Resource not found", errorCode?: string) => {
        return HttpResponses.notFound(message, errorCode, set);
      },

      /**
       * Send a bad request error response with 400 status code
       */
      badRequest: (message = "Bad Request", errorCode?: string) => {
        return HttpResponses.badRequest(message, errorCode, set);
      },

      /**
       * Send an internal server error response with 500 status code
       */
      serverError: (message = "Internal server error", error?: Error) => {
        return HttpResponses.serverError(message, error, set);
      },
    };
  });

/**
 * Global error handler for Elysia
 * Maps common error types to appropriate HTTP status codes
 */
export function globalErrorHandler({
  error,
  code,
  set,
  request,
}: {
  error: any; // Use any as Elysia errors can be various types
  code: string | number;
  set: any;
  request?: any;
}): ApiResponse {
  // Extract path from URL if request is available
  const url = request?.url ? new URL(request.url) : null;
  const path = url?.pathname || "unknown";

  // Don't log 404 errors for common browser requests
  const commonBrowserPaths = [
    "/favicon.ico",
    "/robots.txt",
    "/apple-touch-icon.png",
  ];
  if (
    code === "NOT_FOUND" &&
    commonBrowserPaths.some((p) => path.includes(p))
  ) {
    set.status = 404;
    return errorResponse("Resource not found", null, "NOT_FOUND");
  }

  // Log other errors with more context
  console.error(`[API Error] ${code}:`, error.message || error);

  // Check for Elysia validation errors first (this is the most specific case)
  if (code === "VALIDATION") {
    set.status = 400; // Always use 400 for validation errors
    set.headers["Content-Type"] = "application/json";

    // Extract error type and specific information about the validation error
    const errorType = error.type || "unknown"; // 'body', 'params', 'query', 'response', etc.

    // Format a more user-friendly error message
    let errorMessage = error.message || "Validation failed";
    if (errorType !== "unknown" && errorType !== "response") {
      errorMessage = `Validation failed for ${errorType}: ${errorMessage}`;
    }

    // Extract validation details if available
    const details = error.all
      ? error.all.map((err: any) => ({
          path: err.path,
          message: err.message,
          received: err.value,
        }))
      : undefined;

    // For debugging in development, include more detail about the failed validation
    const validationDetails = {
      type: errorType,
      errors: details,
      originalError: process.env.NODE_ENV === "development" ? error : undefined,
    };

    return errorResponse(
      errorMessage,
      null,
      "VALIDATION_ERROR",
      validationDetails
    );
  }

  // Custom error handling based on error type or message
  const errorMessage = error.message || error.toString();

  if (
    errorMessage.includes("unauthorized") ||
    errorMessage.includes("unauthenticated")
  ) {
    return HttpResponses.unauthorized(errorMessage, undefined, set);
  }

  if (
    errorMessage.includes("forbidden") ||
    errorMessage.includes("permission")
  ) {
    return HttpResponses.forbidden(errorMessage, undefined, set);
  }

  if (
    errorMessage.includes("not found") ||
    errorMessage.toLowerCase().includes("does not exist")
  ) {
    return HttpResponses.notFound(errorMessage, undefined, set);
  }

  if (
    errorMessage.toLowerCase().includes("bad request") ||
    errorMessage.toLowerCase().includes("invalid input") ||
    errorMessage.toLowerCase().includes("validation failed")
  ) {
    return HttpResponses.badRequest(errorMessage, undefined, set);
  }

  // Default to 500 internal server error
  return HttpResponses.serverError(
    errorMessage || "An unexpected error occurred",
    error instanceof Error ? error : new Error(errorMessage),
    set
  );
}
