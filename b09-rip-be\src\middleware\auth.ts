import { <PERSON><PERSON>, t } from "elysia";
import { supabase } from "../libs/supabase";
import { UserProfileModel } from "../modules/auth/models";
import { UserProfile, UserRole } from "../database/models/user-profile.model";
import { HttpResponses } from "./api-response";
import { EmployeeService } from "../modules/employee/service";

// Export types for use in route handlers
export type AuthUser = NonNullable<
  Awaited<ReturnType<typeof supabase.auth.getUser>>["data"]["user"]
>;

// -----------------------------------------------------------------------
// 1. STANDARDIZED ERROR RESPONSES
// -----------------------------------------------------------------------

/**
 * Standardized error responses for authentication middleware
 */
const AuthErrors = {
  unauthorized: (set: any) => {
    return HttpResponses.unauthorized(
      "Authentication required",
      "AUTH_REQUIRED",
      set
    );
  },

  tokenExpired: (set: any) => {
    return HttpResponses.unauthorized(
      "Authentication token has expired",
      "TOKEN_EXPIRED",
      set
    );
  },

  inactiveAccount: (set: any, userId?: string, role?: string) => {
    return HttpResponses.forbidden(
      "Account not yet activated",
      "ACCOUNT_INACTIVE",
      set
    );
  },

  incompleteProfile: (set: any, incompleteFields?: string[]) => {
    set.headers = set.headers || {};
    set.headers["X-Incomplete-Fields"] = incompleteFields
      ? JSON.stringify(incompleteFields)
      : "";
    return HttpResponses.forbidden(
      "Your profile is incomplete. Please complete your profile first.",
      "INCOMPLETE_PROFILE",
      set
    );
  },

  insufficientRole: (set: any, roles?: UserRole[]) => {
    return HttpResponses.forbidden(
      "You do not have permission to access this resource",
      "INSUFFICIENT_ROLE",
      set
    );
  },

  missingAssociation: (set: any, type: "org" | "employee") => {
    const message =
      type === "org"
        ? "Client profile missing organization association"
        : "Employee profile missing employee association";
    const code = type === "org" ? "MISSING_ORG" : "MISSING_EMPLOYEE";
    return HttpResponses.forbidden(message, code, set);
  },

  adminRequired: (set: any) => {
    return HttpResponses.forbidden(
      "Admin access required",
      "ADMIN_REQUIRED",
      set
    );
  },
};

// -----------------------------------------------------------------------
// 2. CORE AUTHENTICATION MIDDLEWARE
// -----------------------------------------------------------------------

/**
 * Core authentication middleware for Elysia
 *
 * Extracts the JWT token from the Authorization header, verifies it with Supabase,
 * and adds the authenticated user to the request context.
 */
export const requireAuthentication = new Elysia({ name: "auth" })
  .derive({ as: "global" }, async ({ headers, set }) => {
    // Check if Authorization header exists
    const authHeader = headers.authorization;
    if (!authHeader || !authHeader.startsWith("Bearer ")) {
      // Authentication header missing or invalid

      return {
        user: null,
        profile: null,
        token_error: "Missing or invalid Authorization header",
      };
    }

    // Extract the token and check if it's empty
    const token = authHeader.split(" ")[1];
    if (!token || token.trim() === "") {
      // Empty token provided
      return {
        user: null,
        profile: null,
        token_error: "Empty token provided",
      };
    }

    try {
      // Verify the token with Supabase
      const { data: authData, error: authError } = await supabase.auth.getUser(
        token
      );

      // Log detailed error for debugging
      if (authError) {
        // If we have a specific JWT expired error, we should handle it
      }

      if (authError || !authData.user) {
        return {
          user: null,
          profile: null,
          token_error: authError?.message || "Unknown authentication error",
        };
      }

      // Get the role from JWT metadata if available
      const roleFromJwt = authData.user.user_metadata?.role as
        | UserRole
        | undefined;

      // Get user profile data - needed for other profile fields
      const { data: profileData } = await UserProfileModel.getByUserId(
        authData.user.id
      );

      // If we have profile data and no role in JWT, we're in a transition state
      // In this case, log a warning but let it pass - role will be synced on next sign-in
      if (profileData && !roleFromJwt) {
        console.warn(
          `User ${authData.user.id} has no role in JWT token. Will use DB role for now.`
        );
      }

      // Create a merged profile with role from JWT if available, otherwise use DB role
      const mergedProfile = profileData
        ? {
            ...profileData,
            // Prioritize role from JWT if available, otherwise use DB role
            role: roleFromJwt || profileData.role,
          }
        : null;

      // Return the authenticated user and merged profile
      return {
        user: authData.user,
        profile: mergedProfile as UserProfile | null,
      };
    } catch (err) {
      console.error("Auth middleware error:", err);
      return {
        user: null,
        profile: null,
        token_error: err instanceof Error ? err.message : "Unknown error",
      };
    }
  })
  .derive(({ set, user, profile }) => {
    return {
      /**
       * Check if the current user has the specified role
       */
      hasUserRole: (requiredRole: UserRole): boolean => {
        return !!profile && profile.role === requiredRole;
      },

      /**
       * Check if the current user has any of the specified roles
       */
      hasAnyRole: (allowedRoles: UserRole[]): boolean => {
        return !!profile && allowedRoles.includes(profile.role);
      },

      /**
       * Return unauthorized response with 401 status code
       */
      unauthorizedError: (
        message = "Authentication required",
        errorCode = "AUTH_REQUIRED"
      ) => {
        return AuthErrors.unauthorized(set);
      },

      /**
       * Return forbidden response with 403 status code
       */
      forbiddenError: (
        message = "You don't have permission to access this resource",
        errorCode = "FORBIDDEN"
      ) => {
        return HttpResponses.forbidden(message, errorCode, set);
      },
    };
  })
  .onBeforeHandle(({ user, token_error, set, path }) => {
    // If no user is found, return 401 Unauthorized
    if (!user) {
      // If we have a specific token error like expiration, return that instead of generic message
      if (token_error && token_error.includes("expired")) {
        return AuthErrors.tokenExpired(set);
      }

      return AuthErrors.unauthorized(set);
    }
  });

// For backward compatibility
export const auth = requireAuthentication;

/**
 * Core role checking function that can be used in both plugin and route handler contexts
 */
export function checkRoles(roles: UserRole[]) {
  return (context: any) => {
    const { profile, set } = context;

    // Admin always has access to everything
    if (profile?.role === UserRole.Admin) {
      return;
    }

    // For non-admin users, check if they have any of the required roles
    if (!profile || !roles.includes(profile.role)) {
      return HttpResponses.forbidden(
        "You do not have permission to access this resource",
        "INSUFFICIENT_ROLE",
        set
      );
    }
  };
}

/**
 * Require user to be authenticated, active, and have one of the specified roles
 * Use this as a plugin for group-level RBAC
 */
export function requireRole(roles: UserRole[]) {
  return (app: Elysia) =>
    app.use(requireActiveUser).onBeforeHandle(checkRoles(roles));
}

// -----------------------------------------------------------------------
// 4. PREDEFINED SECURITY POLICIES
// -----------------------------------------------------------------------

/**
 * Require user to be authenticated and have an active profile
 * This is a direct implementation to avoid type issues
 */
export function requireActiveUser(app: Elysia) {
  return app.use(requireAuthentication).onBeforeHandle(async (context: any) => {
    const { user, profile, token_error, set, path } = context;

    // First check for authentication issues
    if (!user) {
      // If we have a specific token error, return appropriate response
      if (token_error) {
        if (token_error.includes("expired")) {
          return AuthErrors.tokenExpired(set);
        }
        if (token_error.includes("Missing") || token_error.includes("Empty")) {
          // For missing/empty tokens, it's a basic authentication requirement
          return AuthErrors.unauthorized(set);
        }
        // Other token errors
        return AuthErrors.unauthorized(set);
      }

      // Generic unauthorized if there's no specific token error
      return AuthErrors.unauthorized(set);
    }

    // If no profile exists or user is inactive, block access
    if (!profile) {
      return AuthErrors.inactiveAccount(set, user?.id);
    }

    if (profile.is_active === false) {
      return AuthErrors.inactiveAccount(set, profile.user_id, profile.role);
    }

    // Check profile completeness
    const incompleteFields: string[] = [];

    // Basic profile fields check
    if (!profile.fullname) {
      incompleteFields.push("fullname");
    }

    if (!profile.phonenum) {
      incompleteFields.push("phonenum");
    }

    // Role-specific checks
    if (profile.role === UserRole.Client && !profile.org_id) {
      incompleteFields.push("org_id");
    }

    // For employee roles, check both employee_id and employee profile completeness
    if (
      [
        UserRole.Manager,
        UserRole.HR,
        UserRole.Finance,
        UserRole.Operation,
      ].includes(profile.role as UserRole)
    ) {
      // Check employee_id exists
      if (!profile.employee_id) {
        incompleteFields.push("employee_id");
      } else if (profile.id) {
        // Check employee profile has no placeholders
        try {
          const { isComplete, incompleteFields: employeeIncompleteFields } =
            await EmployeeService.isProfileComplete(profile.id);

          if (!isComplete && employeeIncompleteFields) {
            incompleteFields.push(...employeeIncompleteFields);
          }
        } catch (err) {
          console.error("Error checking employee profile completeness:", err);
        }
      }
    }

    // Block access if profile is incomplete
    if (incompleteFields.length > 0) {
      // Allow incomplete profiles to access certain endpoints to view/edit their own profile
      const allowedEndpoints = [
        "/auth/me",
        "/api/auth/me",
        "/v1/auth/me",
        "/auth/profile/update",
        "/employee/profile/update",
        "/api/employee/update",
        "/api/profile/update",
      ];
      const isAllowedEndpoint = allowedEndpoints.some(
        (endpoint) =>
          path.endsWith(endpoint) ||
          (path.includes("/profile") &&
            (path.includes("/update") || path.includes("/edit")))
      );

      if (!isAllowedEndpoint) {
        return AuthErrors.incompleteProfile(set, incompleteFields);
      } else {
      }
    }
  });
}

/**
 * Legacy name for backward compatibility
 */
export function strictSecurityCheck(app: Elysia) {
  return requireActiveUser(app);
}

/**
 * Require user to be authenticated, active, and have Admin role
 */
export function requireAdminRole(app: Elysia) {
  return app.use(requireActiveUser).onBeforeHandle((context: any) => {
    const { profile, set, path } = context;

    // Profile existence and active status already checked by requireActiveUser
    if (!profile || profile.role !== UserRole.Admin) {
      return AuthErrors.adminRequired(set);
    }
  });
}

/**
 * Legacy name for backward compatibility
 */
export function adminOnly(app: Elysia) {
  return requireAdminRole(app);
}

/**
 * Helper function to protect specific routes with full validation
 * Legacy function for backward compatibility
 */
export function protectRoute(app: Elysia) {
  return requireActiveUser(app);
}

// -----------------------------------------------------------------------
// 5. ROLE-SPECIFIC MIDDLEWARE SHORTCUTS
// -----------------------------------------------------------------------

/**
 * Middleware to restrict access to Managers only
 */
export function managerOnly(app: Elysia) {
  return requireRole([UserRole.Manager])(app);
}

/**
 * Middleware to restrict access to employee-type roles (excluding clients)
 */
export function employeesOnly(app: Elysia) {
  return requireRole([
    UserRole.Manager,
    UserRole.HR,
    UserRole.Finance,
    UserRole.Operation,
  ])(app);
}

/**
 * Middleware to restrict access to Clients only
 */
export function clientsOnly(app: Elysia) {
  return requireRole([UserRole.Client])(app);
}

/**
 * Middleware to restrict access to HR and Managers
 */
export function hrAndManagerOnly(app: Elysia) {
  return requireRole([UserRole.HR, UserRole.Manager])(app);
}

/**
 * Middleware to restrict access to Finance and Managers
 */
export function financeAndManagerOnly(app: Elysia) {
  return requireRole([UserRole.Finance, UserRole.Manager])(app);
}

/**
 * Create custom role-based middleware
 * @param roles Array of roles to allow
 */
export function customRoles(roles: UserRole[]) {
  return requireRole(roles);
}
