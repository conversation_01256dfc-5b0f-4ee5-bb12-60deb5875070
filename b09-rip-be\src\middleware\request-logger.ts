import { logger } from "@rasla/logify";

/**
 * Request logger middleware for Elysia
 * Uses the @rasla/logify plugin for robust request/response logging
 */
export const requestLogger = logger({
  // Common browser requests to ignore in logs
  skip: ["/favicon.ico", "/robots.txt", "/apple-touch-icon.png"],

  // Use a custom log format
  format: "[REQUEST] {method} {path} - {statusCode} ({duration}ms)",

  // Only log requests and responses, not other messages
  level: "info",
});
