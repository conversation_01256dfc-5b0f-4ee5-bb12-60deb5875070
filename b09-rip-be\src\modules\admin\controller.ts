import { AdminService } from "./service";
import { AuthUser } from "../../middleware/auth";
import { FilterOption, QueryOptions } from "../../utils/database.types";
import { UserRole } from "../../database/models/user-profile.model";

/**
 * Helper function to ensure API response functions are available
 */
function ensureResponseFunctions(context: any) {
  // Check if the basic response functions are available
  if (typeof context.success !== "function") {
    // Provide fallback response if middleware functions aren't available
    return {
      success: (data: any, message = "Operation successful") => ({
        success: true,
        message,
        data,
      }),
      forbidden: (message = "Forbidden", errorCode?: string) => ({
        success: false,
        message,
        data: null,
        error: { code: errorCode || "FORBIDDEN" },
      }),
      unauthorized: (message = "Unauthorized", errorCode?: string) => ({
        success: false,
        message,
        data: null,
        error: { code: errorCode || "UNAUTHORIZED" },
      }),
      notFound: (message = "Not found", errorCode?: string) => ({
        success: false,
        message,
        data: null,
        error: { code: errorCode || "NOT_FOUND" },
      }),
      serverError: (message = "Server error", error?: Error) => ({
        success: false,
        message,
        data: null,
        error: {
          code: "INTERNAL_SERVER_ERROR",
          details: error ? { stack: error.stack } : undefined,
        },
      }),
    };
  }

  // If functions are available, return the original context
  return context;
}

// Helper function to verify admin access
function verifyAdminAccess(user: AuthUser, profile: any, forbidden: Function) {
  if (
    !profile ||
    profile.role !== UserRole.Admin ||
    profile.is_active !== true
  ) {
    return forbidden("Admin access required", "INSUFFICIENT_ROLE");
  }
  return null; // No error, access allowed
}

export class AdminController {
  /**
   * Get all users with search, filter, and pagination
   */
  static async getAllUsers(context: any) {
    const {
      user,
      profile,
      query = {},
      success,
      forbidden,
      serverError,
    } = ensureResponseFunctions(context);

    // Secondary security check at the controller level
    const securityCheckResult = verifyAdminAccess(user, profile, forbidden);
    if (securityCheckResult) return securityCheckResult;

    // Build query options from request parameters
    const options: QueryOptions = {};

    // Handle search
    if (query.search) {
      options.search = {
        term: query.search,
        fields: ["fullname", "phonenum", "user_id"], // Searchable fields in the profiles table
        // Note: Email search is handled separately in the service
      };
    }

    // Handle filters
    const filters: FilterOption[] = [];

    if (query.role) {
      const role = query.role as UserRole;
      filters.push({ field: "role", value: role });
    }

    if (query.is_active !== undefined) {
      const isActive = query.is_active === "true";
      filters.push({ field: "is_active", value: isActive });
    }

    if (filters.length > 0) {
      options.filters = filters;
    }

    // Always apply pagination, with default values if not provided
    options.pagination = {
      page: query.page ? parseInt(query.page, 10) : 1,
      pageSize: query.pageSize ? parseInt(query.pageSize, 10) : 10,
    };

    // Call the service with the constructed options
    const { data, error, result } = await AdminService.getAllUsers(options);

    if (error) {
      return serverError(
        error instanceof Error ? error.message : String(error),
        error
      );
    }

    // Include pagination result in the response if available
    return success(
      {
        items: data,
        pagination: result,
      },
      "Users retrieved successfully"
    );
  }

  /**
   * Activate a user
   * NOTE: This endpoint expects the user PROFILE ID, not the auth user ID
   */
  static async activateUser(context: any) {
    const { user, profile, body, success, forbidden, serverError } =
      ensureResponseFunctions(context);

    // Secondary security check at the controller level
    const securityCheckResult = verifyAdminAccess(user, profile, forbidden);
    if (securityCheckResult) return securityCheckResult;

    const { id, org_id } = body;
    const adminUserId = user.id;

    if (!id) {
      return forbidden("Profile ID is required", "MISSING_REQUIRED_FIELD");
    }

    const { data, error } = await AdminService.activateUser(
      id,
      adminUserId,
      org_id
    );

    if (error) {
      return serverError(
        error instanceof Error ? error.message : String(error),
        error
      );
    }

    return success(data, "User activated successfully");
  }

  /**
   * Delete a user profile (only deletes profile record, not auth record)
   * NOTE: This endpoint expects the user PROFILE ID, not the auth user ID
   */
  static async deleteUser(context: any) {
    const { user, profile, params, success, forbidden, serverError } =
      ensureResponseFunctions(context);

    // Secondary security check at the controller level
    const securityCheckResult = verifyAdminAccess(user, profile, forbidden);
    if (securityCheckResult) return securityCheckResult;

    const { id } = params;

    const { data, error } = await AdminService.deleteUser(id);

    if (error) {
      return serverError(
        error instanceof Error ? error.message : String(error),
        error
      );
    }

    return success(
      { id, deleted: true },
      "User profile deleted successfully. Auth record must be deleted separately."
    );
  }
}
