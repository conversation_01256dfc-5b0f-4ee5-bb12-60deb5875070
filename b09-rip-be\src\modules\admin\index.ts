import { <PERSON><PERSON> } from "elysia";
import { adminRoutes } from "./routes";
import { apiResponse } from "../../middleware/api-response";

// Create an instance with the middleware applied
// This ensures middleware is applied directly to this module
const adminApp = new Elysia().use(apiResponse).use(adminRoutes);

// Export the admin module
export const admin = adminApp;

// Re-export for convenience
export * from "./schema";
export * from "./controller";
export * from "./service";
