import { <PERSON><PERSON> } from "elysia";
import { Admin<PERSON><PERSON>roller } from "./controller";
import {
  activateUserSchema,
  deleteUserSchema,
  getUsersQuerySchema,
} from "./schema";
import { requireAdminRole } from "../../middleware/auth";
import { UserRole } from "../../database/models/user-profile.model";
import { apiResponse } from "../../middleware/api-response";

export const adminRoutes = (app: Elysia) =>
  app.group("/admin", (app) => {
    return (
      app
        // Ensure API response middleware is applied first
        // Then apply admin role requirement middleware
        // This checks both active status AND admin role
        .use(requireAdminRole)

        // User management routes
        .group("/users", (app) =>
          app
            .get("/", AdminController.getAllUsers, {
              query: getUsersQuerySchema.query,
              detail: {
                tags: ["admin"],
                summary: "Get all users",
                description:
                  "Retrieve all user profiles with search, filter, and pagination. Returns a paginated list of user profiles.",
                security: [{ bearerAuth: [] }],
                responses: {
                  "200": {
                    description: "Successfully retrieved users",
                    content: {
                      "application/json": {
                        schema: {
                          type: "object",
                          properties: {
                            success: { type: "boolean", example: true },
                            message: { type: "string" },
                            data: {
                              type: "object",
                              properties: {
                                items: {
                                  type: "array",
                                  items: {
                                    $ref: "#/components/schemas/UserWithProfile",
                                  },
                                },
                                pagination: {
                                  $ref: "#/components/schemas/PaginationResult",
                                },
                              },
                            },
                          },
                        },
                        examples: {
                          getUsersExample: {
                            $ref: "#/components/examples/getUsersExample",
                          },
                        },
                      },
                    },
                  },
                  "403": {
                    description: "Forbidden - User does not have admin access",
                    content: {
                      "application/json": {
                        examples: {
                          forbiddenErrorExample: {
                            $ref: "#/components/examples/forbiddenErrorExample",
                          },
                        },
                      },
                    },
                  },
                },
              },
            })
            .patch("/activate", AdminController.activateUser, {
              body: activateUserSchema.body,
              detail: {
                tags: ["admin"],
                summary: "Activate a user",
                description:
                  "Activate a user and create associated entity. NOTE: Requires profile ID (not auth user_id).",
                security: [{ bearerAuth: [] }],
                requestBody: {
                  content: {
                    "application/json": {
                      schema: {
                        $ref: "#/components/schemas/ActivateUserRequest",
                      },
                      examples: {
                        activateUserExample: {
                          $ref: "#/components/examples/activateUserExample",
                        },
                      },
                    },
                  },
                },
                responses: {
                  "200": {
                    description: "Successfully activated user",
                    content: {
                      "application/json": {
                        schema: {
                          $ref: "#/components/schemas/GenericSuccessResponse",
                        },
                      },
                    },
                  },
                },
              },
            })
            .delete("/:id", AdminController.deleteUser, {
              params: deleteUserSchema.params,
              detail: {
                tags: ["admin"],
                summary: "Delete a user profile",
                description:
                  "Delete a user's profile record only. Auth record must be deleted separately via SQL functions. NOTE: Requires profile ID (not auth user_id).",
                security: [{ bearerAuth: [] }],
                responses: {
                  "200": {
                    description: "Successfully deleted user profile",
                    content: {
                      "application/json": {
                        examples: {
                          deleteUserExample: {
                            $ref: "#/components/examples/deleteUserExample",
                          },
                        },
                      },
                    },
                  },
                },
              },
            })
        )
    );
  });
