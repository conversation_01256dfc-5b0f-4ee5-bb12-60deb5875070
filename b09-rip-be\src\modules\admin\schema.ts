import { t } from "elysia";

export const activateUserSchema = {
  body: t.Object({
    id: t.String({
      format: "uuid",
      description:
        "Profile ID to activate (the 'id' field from user_profiles table, not the auth user_id)",
    }),
    org_id: t.Optional(
      t.String({
        format: "uuid",
        description:
          "Organization ID to assign client users (required for client role)",
      })
    ),
  }),
};

export const deleteUserSchema = {
  params: t.Object({
    id: t.String({
      format: "uuid",
      description:
        "Profile ID to delete (the 'id' field from user_profiles table, not the auth user_id)",
    }),
  }),
};

export const getUsersQuerySchema = {
  query: t.Object({
    search: t.Optional(
      t.String({
        description: "Search term to filter users by name, email, or phone",
      })
    ),
    page: t.Optional(
      t.Numeric({
        description: "Page number for pagination (starts at 1)",
      })
    ),
    pageSize: t.Optional(
      t.Numeric({
        description: "Number of items per page",
      })
    ),
    role: t.Optional(
      t.String({
        description:
          "Filter users by role (<PERSON><PERSON>, Manager, HR, Finance, Operation, Client)",
      })
    ),
    is_active: t.Optional(
      t.<PERSON>({
        description: "Filter users by active status",
      })
    ),
  }),
};
