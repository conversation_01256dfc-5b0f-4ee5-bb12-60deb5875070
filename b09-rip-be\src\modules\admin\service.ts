import { UserProfileModel } from "../auth/models";
import { QueryOptions } from "../../utils/database.types";
import { supabase } from "../../libs/supabase";
import { dbUtils } from "../../utils/database";
import {
  UserProfile,
  UserWithProfile,
} from "../../database/models/user-profile.model";

export class AdminService {
  /**
   * Fetch all auth users with pagination
   * @returns Array of auth users
   */
  private static async getAllAuthUsers(): Promise<any[]> {
    const allAuthUsers: any[] = [];
    let page = 1;
    const perPage = 100;
    let hasMore = true;

    // Fetch all auth users with pagination
    while (hasMore) {
      const { data: authData, error: authError } =
        await supabase.auth.admin.listUsers({
          perPage: perPage,
          page: page,
        });

      if (authError) {
        break;
      }

      if (authData && authData.users && authData.users.length > 0) {
        allAuthUsers.push(...authData.users);
        page++;

        // If we got fewer results than perPage, we've reached the end
        if (authData.users.length < perPage) {
          hasMore = false;
        }
      } else {
        hasMore = false;
      }
    }

    return allAuthUsers;
  }

  /**
   * Get all user profiles with search, filter, and pagination
   * @param options Query options
   */
  static async getAllUsers(options: QueryOptions = {}) {
    // Get ALL auth users first - we'll use this data for both search and joining
    const allAuthUsers = await this.getAllAuthUsers();

    // Create a map of user_id -> auth user data for easy lookup
    const authUserMap = new Map<string, any>();
    for (const user of allAuthUsers) {
      if (user.id) {
        // Ensure we have a valid ID
        authUserMap.set(user.id, user);
      }
    }

    // Create a map for email address -> user ID lookup
    const emailMap = new Map<string, string>();

    // Store email -> user ID mappings for quick lookup
    allAuthUsers.forEach((user: any) => {
      if (user.email && user.id) {
        emailMap.set(user.email.toLowerCase(), user.id);
      }
    });

    const searchTerm = options.search?.term;
    const originalOptions = { ...options };
    let profileResult: {
      data: UserProfile[] | null;
      error: any;
      result?: any;
    } = { data: null, error: null };

    // Always try both database fields and email search for any search term
    if (searchTerm) {
      // PART 1: First try with standard database query
      profileResult = await UserProfileModel.getAll(options);

      // PART 2: Also find matching emails for any search term
      const lowercaseSearchTerm = searchTerm.toLowerCase();

      const matchingEmails = Array.from(emailMap.keys()).filter((email) =>
        email.includes(lowercaseSearchTerm)
      );

      if (matchingEmails.length > 0) {
        // Get the user IDs for these emails
        const matchingUserIds = matchingEmails
          .map((email) => emailMap.get(email))
          .filter(Boolean) as string[];

        // Try a direct query by user IDs if we either have no results from the first query
        // OR if we need to add more results from email matches
        if (
          matchingUserIds.length > 0 &&
          (!profileResult.data || profileResult.data.length === 0)
        ) {
          const directQueryOptions: QueryOptions = {
            filters: [
              {
                field: "user_id",
                value: matchingUserIds,
                operator: "in",
              },
            ],
            pagination: originalOptions.pagination,
          };

          const directResult = await UserProfileModel.getAll(
            directQueryOptions
          );

          if (directResult.data && directResult.data.length > 0) {
            // Use direct query results if initial query returned nothing
            profileResult = directResult;
          }
        }
      }
    } else {
      // No search term, just get all profiles
      profileResult = await UserProfileModel.getAll(options);
    }

    // Check for errors or no results
    if (profileResult.error) {
      return profileResult;
    }

    if (!profileResult.data || profileResult.data.length === 0) {
      return profileResult;
    }

    const profiles = profileResult.data;

    // Join profile data with auth data
    const usersWithProfiles: UserWithProfile[] = profiles.map((profile) => {
      const authUser = authUserMap.get(profile.user_id);
      return {
        id: profile.user_id,
        email: authUser?.email || "N/A", // Handle case where auth data might be missing
        profile,
      };
    });

    // Return the combined result
    return {
      data: usersWithProfiles,
      error: null,
      result: profileResult.result,
    };
  }

  /**
   * Activate a user and create associated entity
   * @param profileId The user profile ID to activate
   * @param adminUserId The admin user ID performing the activation
   * @param orgId Optional organization ID for client users
   */
  static async activateUser(
    profileId: string,
    adminUserId: string,
    orgId?: string
  ) {
    try {
      // Get profile by ID
      const { data: profile, error: profileError } =
        await dbUtils.getById<UserProfile>("user_profiles", profileId);

      if (!profile || profileError) {
        return {
          data: null,
          error:
            profileError || new Error(`No profile found with ID: ${profileId}`),
        };
      }

      // Check if already activated
      if (profile.is_active) {
        return {
          data: profile,
          error: new Error("User is already activated"),
        };
      }

      // Use the same activation logic but with the profile we just found
      return UserProfileModel.activate(profile.user_id, adminUserId, orgId);
    } catch (err) {
      return {
        data: null,
        error: err instanceof Error ? err : new Error(String(err)),
      };
    }
  }

  /**
   * Delete a user profile
   * @param profileId The user profile ID to delete
   */
  static async deleteUser(profileId: string) {
    try {
      // Get profile to verify it exists before deleting
      const { data: profile, error: profileError } =
        await dbUtils.getById<UserProfile>("user_profiles", profileId);

      if (!profile || profileError) {
        return {
          data: null,
          error:
            profileError || new Error(`No profile found with ID: ${profileId}`),
        };
      }

      // Check if there's an employee record associated with this profile
      const { data: employeeData, error: employeeError } = await supabase
        .from("employees")
        .select("id")
        .eq("profile_id", profileId)
        .single();

      // Handle circular reference between user_profiles and employees
      // First check if the user profile has an employee_id reference
      if (profile.employee_id) {
        // Update the profile to remove the employee_id reference first
        const { error: updateProfileError } = await supabase
          .from("user_profiles")
          .update({ employee_id: null })
          .eq("id", profileId);

        if (updateProfileError) {
          return {
            data: null,
            error: new Error(
              `Failed to update employee_id reference: ${updateProfileError.message}`
            ),
          };
        }
      }

      // If an employee record exists, delete it
      if (employeeData && !employeeError) {
        // Check if there are any dependent records in other tables

        // Check for dependent records in attendances
        const { data: attendanceData } = await supabase
          .from("attendances")
          .select("id")
          .eq("employee_id", employeeData.id)
          .limit(1);

        if (attendanceData && attendanceData.length > 0) {
          // Delete dependent attendance records
          const { error: deleteAttendanceError } = await supabase
            .from("attendances")
            .delete()
            .eq("employee_id", employeeData.id);

          if (deleteAttendanceError) {
            return {
              data: null,
              error: new Error(
                `Failed to delete associated attendance records: ${deleteAttendanceError.message}`
              ),
            };
          }
        }

        // Check for dependent records in salaries
        const { data: salaryData } = await supabase
          .from("salaries")
          .select("id")
          .eq("employee_id", employeeData.id)
          .limit(1);

        if (salaryData && salaryData.length > 0) {
          // Delete dependent salary records
          const { error: deleteSalaryError } = await supabase
            .from("salaries")
            .delete()
            .eq("employee_id", employeeData.id);

          if (deleteSalaryError) {
            return {
              data: null,
              error: new Error(
                `Failed to delete associated salary records: ${deleteSalaryError.message}`
              ),
            };
          }
        }

        // Check for dependent records in kpis
        const { data: kpiData } = await supabase
          .from("kpis")
          .select("id")
          .eq("employee_id", employeeData.id)
          .limit(1);

        if (kpiData && kpiData.length > 0) {
          // Delete dependent kpi records
          const { error: deleteKpiError } = await supabase
            .from("kpis")
            .delete()
            .eq("employee_id", employeeData.id);

          if (deleteKpiError) {
            return {
              data: null,
              error: new Error(
                `Failed to delete associated KPI records: ${deleteKpiError.message}`
              ),
            };
          }
        }

        // Now we can safely delete the employee record
        const { error: deleteEmployeeError } = await supabase
          .from("employees")
          .delete()
          .eq("id", employeeData.id);

        if (deleteEmployeeError) {
          return {
            data: null,
            error: new Error(
              `Failed to delete associated employee record: ${deleteEmployeeError.message}`
            ),
          };
        }
      }

      // Now that any employee record is removed, we can safely delete the profile
      return dbUtils.hardDelete("user_profiles", profileId);

      // Note: Auth user deletion is handled separately via SQL functions
    } catch (err) {
      return {
        data: null,
        error: err instanceof Error ? err : new Error(String(err)),
      };
    }
  }
}
