import { AllowanceSalaryService } from "./service";
import {
  CreateAllowanceSalaryDto,
  UpdateAllowanceSalaryDto,
} from "../../database/models/allowance-salary.model";

export class AllowanceSalaryController {
  /**
   * Create a new allowance entry
   */
  static async create(context: any) {
    const { body, user, success, badRequest, serverError } = context;

    try {
      const allowanceData: CreateAllowanceSalaryDto = {
        salary_id: body.salary_id,
        amount: body.amount,
        allowance_type: body.allowance_type,
        notes: body.notes,
      };

      const { data, error } = await AllowanceSalaryService.create(
        allowanceData,
        user.id
      );

      if (error) {
        return serverError(error.message, error);
      }

      return success(data, "Allowance created successfully");
    } catch (error: any) {
      return serverError(error.message || "Failed to create allowance", error);
    }
  }

  /**
   * Get all allowances for a salary
   */
  static async getBySalaryId(context: any) {
    const { params, success, notFound, serverError } = context;
    const { salaryId } = params;

    try {
      const { data, error } = await AllowanceSalaryService.getBySalaryId(
        salaryId
      );

      if (error) {
        return serverError(error.message, error);
      }

      if (!data || data.length === 0) {
        return success([], "No allowances found for this salary");
      }

      return success(data, "Allowances retrieved successfully");
    } catch (error: any) {
      return serverError(
        error.message || "Failed to retrieve allowances",
        error
      );
    }
  }

  /**
   * Get an allowance by ID
   */
  static async getById(context: any) {
    const { params, success, notFound, serverError } = context;
    const { id } = params;

    try {
      const { data, error } = await AllowanceSalaryService.getById(id);

      if (error) {
        return serverError(error.message, error);
      }

      if (!data) {
        return notFound("Allowance not found");
      }

      return success(data, "Allowance retrieved successfully");
    } catch (error: any) {
      return serverError(
        error.message || "Failed to retrieve allowance",
        error
      );
    }
  }

  /**
   * Update an allowance
   */
  static async update(context: any) {
    const { body, params, user, success, badRequest, serverError, notFound } =
      context;

    try {
      if (!body.amount && !body.allowance_type && !body.notes) {
        return badRequest("No valid fields to update", "NO_VALID_FIELDS");
      }

      const updateDto: UpdateAllowanceSalaryDto = {};
      if (body.amount !== undefined) updateDto.amount = body.amount;
      if (body.allowance_type !== undefined)
        updateDto.allowance_type = body.allowance_type;
      if (body.notes !== undefined) updateDto.notes = body.notes;

      const { data, error } = await AllowanceSalaryService.update(
        params.id,
        updateDto,
        user.id
      );

      if (error) {
        if (error.message.includes("not found")) {
          return notFound("Allowance not found");
        }
        return serverError(error.message, error);
      }

      return success(data, "Allowance updated successfully");
    } catch (error: any) {
      return serverError(error.message || "Failed to update allowance", error);
    }
  }

  /**
   * Delete an allowance
   */
  static async delete(context: any) {
    const { params, user, success, serverError, notFound } = context;

    try {
      const { data, error } = await AllowanceSalaryService.delete(
        params.id,
        user.id
      );

      if (error) {
        if (error.message.includes("not found")) {
          return notFound("Allowance not found");
        }
        return serverError(error.message, error);
      }

      return success(data, "Allowance deleted successfully");
    } catch (error: any) {
      return serverError(error.message || "Failed to delete allowance", error);
    }
  }
}
