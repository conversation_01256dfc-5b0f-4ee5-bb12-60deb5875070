import { Elysia } from "elysia";
import { apiResponse } from "../../middleware/api-response";
import { allowanceSalaryRoutes } from "./routes";

// Create an instance with the middleware applied
const allowanceSalaryApp = new Elysia()
  .use(apiResponse)
  .use(allowanceSalaryRoutes);

export * from "./service";

// Export the allowance salary module
export const allowanceSalaries = allowanceSalaryApp;

// Re-export for convenience
export * from "./schema";
export * from "./controller";
