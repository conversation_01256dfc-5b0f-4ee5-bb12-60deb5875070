import { <PERSON>sia } from "elysia";
import { AllowanceSalaryController } from "./controller";
import { requireActiveUser, checkRoles } from "../../middleware/auth";
import { UserRole } from "../../database/models/user-profile.model";
import {
  createAllowanceSchema,
  getAllowancesBySalaryIdSchema,
  getAllowanceByIdSchema,
  updateAllowanceSchema,
  deleteAllowanceSchema,
} from "./schema";

export const allowanceSalaryRoutes = (app: Elysia) =>
  app.group("/allowances", (app) =>
    app
      // First ensure users are active
      .use(requireActiveUser)

      // Create a new allowance
      .post("/", AllowanceSalaryController.create, {
        beforeHandle: [checkRoles([UserRole.Finance])],
        ...createAllowanceSchema,
        detail: {
          tags: ["allowances"],
          summary: "Create a new allowance",
          description: "Create a new allowance entry for a salary",
          security: [{ bearerAuth: [] }],
          responses: {
            "200": {
              description: "Allowance created successfully",
              content: {
                "application/json": {
                  example: {
                    success: true,
                    message: "Allowance created successfully",
                    data: {
                      id: "323e4567-e89b-12d3-a456-426614174002",
                      salary_id: "123e4567-e89b-12d3-a456-426614174000",
                      amount: 500000,
                      allowance_type: "transport",
                      notes: "Monthly transport allowance",
                      created_at: "2025-05-10T00:00:00.000Z",
                      created_by: "auth0|123456789",
                      updated_at: null,
                      updated_by: null,
                      deleted_at: null,
                      deleted_by: null,
                    },
                  },
                },
              },
            },
          },
        },
      })

      // Get allowances by salary ID
      .get("/salary/:salaryId", AllowanceSalaryController.getBySalaryId, {
        beforeHandle: [
          checkRoles([UserRole.Finance, UserRole.HR, UserRole.Manager]),
        ],
        ...getAllowancesBySalaryIdSchema,
        detail: {
          tags: ["allowances"],
          summary: "Get allowances by salary ID",
          description: "Retrieve all allowance entries for a specific salary",
          security: [{ bearerAuth: [] }],
          responses: {
            "200": {
              description: "Allowances retrieved successfully",
              content: {
                "application/json": {
                  example: {
                    success: true,
                    message: "Allowances retrieved successfully",
                    data: [
                      {
                        id: "323e4567-e89b-12d3-a456-426614174002",
                        salary_id: "123e4567-e89b-12d3-a456-426614174000",
                        amount: 500000,
                        allowance_type: "transport",
                        notes: "Monthly transport allowance",
                        created_at: "2025-05-10T00:00:00.000Z",
                        created_by: "auth0|123456789",
                        updated_at: null,
                        updated_by: null,
                        deleted_at: null,
                        deleted_by: null,
                      },
                      {
                        id: "323e4567-e89b-12d3-a456-426614174003",
                        salary_id: "123e4567-e89b-12d3-a456-426614174000",
                        amount: 300000,
                        allowance_type: "meal",
                        notes: "Monthly meal allowance",
                        created_at: "2025-05-10T00:00:00.000Z",
                        created_by: "auth0|123456789",
                        updated_at: null,
                        updated_by: null,
                        deleted_at: null,
                        deleted_by: null,
                      },
                    ],
                  },
                },
              },
            },
          },
        },
      })

      // Get allowance by ID
      .get("/:id", AllowanceSalaryController.getById, {
        beforeHandle: [
          checkRoles([UserRole.Finance, UserRole.HR, UserRole.Manager]),
        ],
        params: getAllowanceByIdSchema.params,
        detail: {
          tags: ["allowances"],
          summary: "Get allowance by ID",
          description: "Retrieve a specific allowance by its ID",
          security: [{ bearerAuth: [] }],
          responses: {
            "200": {
              description: "Allowance retrieved successfully",
              content: {
                "application/json": {
                  example: {
                    success: true,
                    message: "Allowance retrieved successfully",
                    data: {
                      id: "323e4567-e89b-12d3-a456-426614174002",
                      salary_id: "123e4567-e89b-12d3-a456-426614174000",
                      amount: 500000,
                      allowance_type: "transport",
                      notes: "Monthly transport allowance",
                      created_at: "2025-05-10T00:00:00.000Z",
                      created_by: "auth0|123456789",
                      updated_at: null,
                      updated_by: null,
                      deleted_at: null,
                      deleted_by: null,
                    },
                  },
                },
              },
            },
            "404": {
              description: "Allowance not found",
              content: {
                "application/json": {
                  example: {
                    success: false,
                    message: "Allowance not found",
                    data: null,
                    error: { code: "NOT_FOUND" },
                  },
                },
              },
            },
          },
        },
      })
      .put("/:id", AllowanceSalaryController.update, {
        beforeHandle: [checkRoles([UserRole.Finance])],
        params: updateAllowanceSchema.params,
        body: updateAllowanceSchema.body,
        detail: {
          tags: ["allowances"],
          summary: "Update allowance",
          description: "Update an existing allowance entry",
          security: [{ bearerAuth: [] }],
          responses: {
            "200": {
              description: "Allowance updated successfully",
              content: {
                "application/json": {
                  example: {
                    success: true,
                    message: "Allowance updated successfully",
                    data: {
                      id: "323e4567-e89b-12d3-a456-426614174002",
                      salary_id: "123e4567-e89b-12d3-a456-426614174000",
                      amount: 600000, // Updated amount
                      allowance_type: "transport",
                      notes: "Updated monthly transport allowance",
                      created_at: "2025-05-10T00:00:00.000Z",
                      created_by: "auth0|123456789",
                      updated_at: "2025-05-15T00:00:00.000Z",
                      updated_by: "auth0|123456789",
                      deleted_at: null,
                      deleted_by: null,
                    },
                  },
                },
              },
            },
            "404": {
              description: "Allowance not found",
              content: {
                "application/json": {
                  example: {
                    success: false,
                    message: "Allowance not found",
                    data: null,
                    error: { code: "NOT_FOUND" },
                  },
                },
              },
            },
          },
        },
      })
      .delete("/:id", AllowanceSalaryController.delete, {
        beforeHandle: [checkRoles([UserRole.Finance])],
        params: deleteAllowanceSchema.params,
        detail: {
          tags: ["allowances"],
          summary: "Delete allowance",
          description: "Delete an existing allowance entry",
          security: [{ bearerAuth: [] }],
          responses: {
            "200": {
              description: "Allowance deleted successfully",
              content: {
                "application/json": {
                  example: {
                    success: true,
                    message: "Allowance deleted successfully",
                    data: {
                      id: "323e4567-e89b-12d3-a456-426614174002",
                      deleted_at: "2025-05-20T00:00:00.000Z",
                      deleted_by: "auth0|123456789",
                    },
                  },
                },
              },
            },
            "404": {
              description: "Allowance not found",
              content: {
                "application/json": {
                  example: {
                    success: false,
                    message: "Allowance not found",
                    data: null,
                    error: { code: "NOT_FOUND" },
                  },
                },
              },
            },
          },
        },
      })
  );
