import { t } from "elysia";
import { AllowanceSalaryType } from "../../database/models/allowance-salary.model";

// Common schema patterns
export const salaryIdSchema = t.String({
  format: "uuid",
  description: "The ID of the salary record",
});

export const amountSchema = t.Number({
  minimum: 0,
  maximum: 1000000000,
  description: "The allowance amount",
});

export const allowanceTypeSchema = t.Enum(AllowanceSalaryType, {
  description: "The type of allowance",
});

export const notesSchema = t.Optional(
  t.String({
    maxLength: 1000,
    description: "Additional notes about the allowance",
  })
);

// Validation schemas
export const createAllowanceSchema = {
  body: t.Object({
    salary_id: salaryIdSchema,
    amount: amountSchema,
    allowance_type: allowanceTypeSchema,
    notes: notesSchema,
  }),
};

export const getAllowancesBySalaryIdSchema = {
  params: t.Object({
    salaryId: salaryIdSchema,
  }),
};

export const getAllowanceByIdSchema = {
  params: t.Object({
    id: t.String({
      format: "uuid",
      description: "The ID of the allowance entry to retrieve",
    }),
  }),
};

export const updateAllowanceSchema = {
  params: t.Object({
    id: t.String({
      format: "uuid",
      description: "The ID of the allowance entry to update",
    }),
  }),
  body: t.Object({
    amount: t.Optional(amountSchema),
    allowance_type: t.Optional(allowanceTypeSchema),
    notes: t.Optional(notesSchema),
  }),
};

export const deleteAllowanceSchema = {
  params: t.Object({
    id: t.String({
      format: "uuid",
      description: "The ID of the allowance entry to delete",
    }),
  }),
};
