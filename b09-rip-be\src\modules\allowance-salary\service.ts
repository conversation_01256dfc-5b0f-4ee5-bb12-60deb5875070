import { dbUtils } from "../../utils/database";
import {
  AllowanceSalary,
  CreateAllowanceSalaryDto,
  UpdateAllowanceSalaryDto,
  AllowanceSalaryType,
} from "../../database/models/allowance-salary.model";
import { Salary } from "../../database/models/salary.model";
import { SalaryUpdateHistoryService } from "../salary/salary-update-history.service";

export class AllowanceSalaryService {
  static readonly TABLE_NAME = "allowance_salaries";

  /**
   * Create a new allowance entry and update the salary's total_allowance
   */
  static async create(data: CreateAllowanceSalaryDto, userId: string) {
    try {
      // Create the allowance entry
      const { data: allowance, error } = await dbUtils.create<AllowanceSalary>(
        this.TABLE_NAME,
        data,
        userId
      );

      if (error) {
        return { data: null, error };
      }

      // Get all allowances for this salary to calculate the total
      const { data: allowances } = await this.getBySalaryId(data.salary_id);

      if (allowances && allowances.length > 0) {
        // Get the current salary to get the old total_allowance value and total salary
        const { data: salary } = await dbUtils.getById<Salary>(
          "salaries",
          data.salary_id
        );
        const oldTotalAllowance = salary?.total_allowance || 0;
        const oldTotalSalary = salary?.total_salary || 0;

        const totalAllowance = allowances.reduce(
          (sum, item) => sum + item.amount,
          0
        );

        // Calculate the new total salary
        const newTotalSalary =
          salary.base_salary +
          salary.total_bonus +
          totalAllowance -
          salary.total_deduction;

        // Track the component update in history
        await SalaryUpdateHistoryService.trackComponentUpdate(
          data.salary_id,
          "allowance",
          "add",
          allowance.allowance_type,
          allowance.amount,
          oldTotalAllowance,
          totalAllowance,
          oldTotalSalary,
          newTotalSalary,
          userId
        );

        // Update the salary's total_allowance and total_salary
        await dbUtils.update<Salary>(
          "salaries",
          data.salary_id,
          {
            total_allowance: totalAllowance,
            total_salary: newTotalSalary,
          },
          userId
        );
      }

      return { data: allowance, error: null };
    } catch (error: any) {
      return {
        data: null,
        error: new Error(`Failed to create allowance: ${error.message}`),
      };
    }
  }

  /**
   * Get all allowances for a salary
   */
  static async getBySalaryId(salaryId: string) {
    try {
      const { data, error } = await dbUtils.getByField<AllowanceSalary>(
        this.TABLE_NAME,
        "salary_id",
        salaryId
      );

      if (error) {
        return { data: null, error };
      }

      return { data, error: null };
    } catch (error: any) {
      return {
        data: null,
        error: new Error(`Failed to get allowances: ${error.message}`),
      };
    }
  }

  /**
   * Get an allowance by ID
   */
  static async getById(id: string) {
    try {
      const { data, error } = await dbUtils.getById<AllowanceSalary>(
        this.TABLE_NAME,
        id
      );

      if (error) {
        return { data: null, error };
      }

      return { data, error: null };
    } catch (error: any) {
      return {
        data: null,
        error: new Error(`Failed to get allowance by ID: ${error.message}`),
      };
    }
  }

  /**
   * Update an allowance and recalculate the salary's total_allowance
   */
  static async update(
    id: string,
    data: UpdateAllowanceSalaryDto,
    userId: string
  ) {
    try {
      // Get the current allowance to get the salary_id
      const { data: currentAllowance, error: getError } = await this.getById(
        id
      );

      if (getError || !currentAllowance) {
        return {
          data: null,
          error: getError || new Error("Allowance not found"),
        };
      }

      // Update the allowance
      const { data: updatedAllowance, error: updateError } =
        await dbUtils.update<AllowanceSalary>(
          this.TABLE_NAME,
          id,
          data,
          userId
        );

      if (updateError) {
        return { data: null, error: updateError };
      }

      // Get the current salary to get the old total_allowance value and total salary
      const { data: currentSalary } = await dbUtils.getById<Salary>(
        "salaries",
        currentAllowance.salary_id
      );
      const oldTotalAllowance = currentSalary?.total_allowance || 0;
      const oldTotalSalary = currentSalary?.total_salary || 0;

      // Get all allowances for this salary to calculate the total
      const { data: allowances } = await this.getBySalaryId(
        currentAllowance.salary_id
      );

      if (allowances && allowances.length > 0) {
        const totalAllowance = allowances.reduce(
          (sum, item) => sum + item.amount,
          0
        );

        // Calculate the new total salary
        const newTotalSalary =
          currentSalary.base_salary +
          currentSalary.total_bonus +
          totalAllowance -
          currentSalary.total_deduction;

        // Track the component update in history
        await SalaryUpdateHistoryService.trackComponentUpdate(
          currentAllowance.salary_id,
          "allowance",
          "update",
          data.allowance_type || currentAllowance.allowance_type,
          currentAllowance.amount,
          oldTotalAllowance,
          totalAllowance,
          oldTotalSalary,
          newTotalSalary,
          userId
        );

        // Update the salary's total_allowance and total_salary
        await dbUtils.update<Salary>(
          "salaries",
          currentAllowance.salary_id,
          {
            total_allowance: totalAllowance,
            total_salary: newTotalSalary,
          },
          userId
        );
      }

      return { data: updatedAllowance, error: null };
    } catch (error: any) {
      return {
        data: null,
        error: new Error(`Failed to update allowance: ${error.message}`),
      };
    }
  }

  /**
   * Delete an allowance and recalculate the salary's total_allowance
   */
  static async delete(id: string, userId: string) {
    try {
      // Get the current allowance to get the salary_id
      const { data: currentAllowance, error: getError } = await this.getById(
        id
      );

      if (getError || !currentAllowance) {
        return {
          data: null,
          error: getError || new Error("Allowance not found"),
        };
      }

      // Get the current salary to get the old total_allowance value and total salary
      const { data: currentSalary } = await dbUtils.getById<Salary>(
        "salaries",
        currentAllowance.salary_id
      );
      const oldTotalAllowance = currentSalary?.total_allowance || 0;
      const oldTotalSalary = currentSalary?.total_salary || 0;

      // Soft delete the allowance
      const { error: deleteError } = await dbUtils.softDelete(
        this.TABLE_NAME,
        id,
        userId
      );

      if (deleteError) {
        return { data: null, error: deleteError };
      }

      // Get all allowances for this salary to calculate the total
      const { data: allowances } = await this.getBySalaryId(
        currentAllowance.salary_id
      );

      // Calculate new total allowance (excluding the deleted one)
      const totalAllowance =
        allowances && allowances.length > 0
          ? allowances.reduce((sum, item) => sum + item.amount, 0)
          : 0;

      // Calculate the new total salary
      const newTotalSalary =
        currentSalary.base_salary +
        currentSalary.total_bonus +
        totalAllowance -
        currentSalary.total_deduction;

      // Track the component update in history after deleting
      await SalaryUpdateHistoryService.trackComponentUpdate(
        currentAllowance.salary_id,
        "allowance",
        "delete",
        currentAllowance.allowance_type,
        currentAllowance.amount,
        oldTotalAllowance,
        totalAllowance,
        oldTotalSalary,
        newTotalSalary,
        userId
      );

      // Update the salary's total_allowance and total_salary
      await dbUtils.update<Salary>(
        "salaries",
        currentAllowance.salary_id,
        {
          total_allowance: totalAllowance,
          total_salary: newTotalSalary,
        },
        userId
      );

      return { data: { id }, error: null };
    } catch (error: any) {
      return {
        data: null,
        error: new Error(`Failed to delete allowance: ${error.message}`),
      };
    }
  }
}
