import { AttendanceService } from "./service";
import {
  C<PERSON><PERSON>ttendanceDto,
  PresenceStatus,
  ViewDailyAbsenceDto,
} from "../../database/models/attendance.model";
import { TaskStatus } from "../../database/models/task.model";
import { FilterOption, QueryOptions } from "../../utils/database.types";
import { AuthUser } from "../../middleware/auth";
import {
  UserProfile,
  UserRole,
} from "../../database/models/user-profile.model";

/**
 * Interface for the controller context
 */
export interface AttendanceContext {
  body: {
    status?: PresenceStatus;
    notes?: string;
    tasks?: TaskInput[];
    employee_id?: string;
    fromDate?: string;
    toDate?: string;
    page?: number;
    pageSize?: number;
    search?: string;
  };
  query: {
    fromDate?: string;
    toDate?: string;
    status?: PresenceStatus;
    search?: string;
    page?: number;
    pageSize?: number;
  };
  // Extended user information including profile
  user: AuthUser & { employee_id?: string; profile?: UserProfile };
  // User profile from middleware
  profile?: UserProfile;
  // Response functions from middleware
  success: (data: any, message?: string) => ApiResponse;
  forbidden: (message?: string, errorCode?: string) => ApiErrorResponse;
  unauthorized: (message?: string, errorCode?: string) => ApiErrorResponse;
  notFound: (message?: string, errorCode?: string) => ApiErrorResponse;
  serverError: (message?: string, error?: Error) => ApiErrorResponse;
  badRequest: (message?: string, errorCode?: string) => ApiErrorResponse;
}

/**
 * Interface for task input data (regular tasks)
 */
interface TaskInput {
  description: string;
  due_date: string;
  completion_status?: boolean; // Changed to boolean for regular tasks
}

/**
 * Interface for API success response
 */
interface ApiResponse {
  success: true;
  message: string;
  data: any;
}

/**
 * Interface for API error response
 */
interface ApiErrorResponse {
  success: false;
  message: string;
  data: null;
  error: {
    code: string;
    details?: any;
  };
}

/**
 * Helper function to ensure API response functions are available
 * @returns Response functions
 */
function ensureResponseFunctions(
  context: Partial<AttendanceContext>
): AttendanceContext {
  if (typeof context.success !== "function") {
    console.error("API Response middleware functions not available in context");

    return {
      ...(context as any),
      success: (data: any, message = "Operation successful") => ({
        success: true,
        message,
        data,
      }),
      forbidden: (message = "Forbidden", errorCode?: string) => ({
        success: false,
        message,
        data: null,
        error: { code: errorCode || "FORBIDDEN" },
      }),
      unauthorized: (message = "Unauthorized", errorCode?: string) => ({
        success: false,
        message,
        data: null,
        error: { code: errorCode || "UNAUTHORIZED" },
      }),
      notFound: (message = "Not found", errorCode?: string) => ({
        success: false,
        message,
        data: null,
        error: { code: errorCode || "NOT_FOUND" },
      }),
      serverError: (message = "Server error", error?: Error) => ({
        success: false,
        message,
        data: null,
        error: {
          code: "INTERNAL_SERVER_ERROR",
          details: error ? { stack: error.stack } : undefined,
        },
      }),
      badRequest: (message = "Bad Request", errorCode?: string) => ({
        success: false,
        message,
        data: null,
        error: { code: errorCode || "BAD_REQUEST" },
      }),
    };
  }

  return context as AttendanceContext;
}

export class AttendanceController {
  /**
   * Create or update attendance record based on daily record existence
   * If no attendance exists for today, this will create a new record with clock-in time
   * If attendance already exists, it will update with clock-out time
   */
  static async createOrUpdate(context: Partial<AttendanceContext>) {
    const ctx = ensureResponseFunctions(context);
    const { body, user, profile } = ctx;
    const { success, serverError, badRequest } = ctx;

    // Validate required inputs
    if (!body.status) {
      return badRequest("Missing required field: status");
    }

    // Validasi status terhadap enum PresenceStatus
    if (
      !Object.values(PresenceStatus).includes(body.status as PresenceStatus)
    ) {
      return badRequest("Invalid status value");
    }

    // Make sure user is authenticated and has employee_id
    if (!user || !user.id) {
      return badRequest("User ID not available");
    }

    // Get the employee_id from the user profile
    const employeeId = profile?.employee_id;
    if (!employeeId) {
      return badRequest("Employee ID not found in user profile");
    }

    try {
      // Call service to create or update attendance
      const { data, error } = await AttendanceService.createOrUpdateAttendance(
        {
          status: body.status as PresenceStatus,
          notes: body.notes,
          tasks: body.tasks || [],
        },
        employeeId,
        user.id
      );

      if (error) {
        console.error(
          "Error in AttendanceService.createOrUpdateAttendance:",
          error
        );
        return serverError(error.message, error);
      }

      // Determine message based on whether this was a clock-in or clock-out
      const message = data?.clock_out
        ? "Attendance updated with clock-out time successfully"
        : "Attendance created with clock-in time successfully";

      return success(data, message);
    } catch (err) {
      console.error(
        "Unexpected error in AttendanceController.createOrUpdate:",
        err
      );
      return serverError(
        "Unexpected error occurred",
        err instanceof Error ? err : undefined
      );
    }
  }

  /**
   * Get today's attendance for the current user
   */
  static async getToday(context: Partial<AttendanceContext>) {
    const ctx = ensureResponseFunctions(context);
    const { user, profile } = ctx;
    const { success, serverError, badRequest, notFound } = ctx;

    // Make sure user is authenticated and has employee_id
    if (!user || !user.id) {
      return badRequest("User ID not available");
    }

    // Get the employee_id from the user profile
    const employeeId = profile?.employee_id;
    if (!employeeId) {
      return badRequest("Employee ID not found in user profile");
    }

    try {
      // Call service to get today's attendance
      const { data, error } = await AttendanceService.getTodayAttendance(
        employeeId
      );

      if (error) {
        console.error("Error in AttendanceService.getTodayAttendance:", error);
        return serverError(error.message, error);
      }

      if (!data) {
        return notFound("No attendance record found for today");
      }

      return success(data, "Today's attendance record retrieved successfully");
    } catch (err) {
      console.error("Unexpected error in AttendanceController.getToday:", err);
      return serverError(
        "Unexpected error occurred",
        err instanceof Error ? err : undefined
      );
    }
  }

  /**
   * Get all attendances with date filtering and pagination
   *
   * Note: This endpoint is kept for backward compatibility and admin access
   * It will show all attendance records with optional date filtering
   */
  static async getAll(context: Partial<AttendanceContext>) {
    const ctx = ensureResponseFunctions(context);
    const { query } = ctx;
    const { success, serverError, badRequest } = ctx;

    try {
      // Transform query parameters into filters
      const filters: FilterOption[] = [];

      if (query.fromDate) {
        filters.push({
          field: "fromDate",
          value: query.fromDate,
        });
      }

      if (query.toDate) {
        filters.push({
          field: "toDate",
          value: query.toDate,
        });
      }

      // Add status filter if provided
      if (query.status) {
        // Validate that status is a valid enum value
        if (!Object.values(PresenceStatus).includes(query.status)) {
          return badRequest(`Invalid status value: ${query.status}`);
        }

        filters.push({
          field: "status",
          value: query.status,
        });
      }

      // Prepare search options if a search term is provided
      const searchOptions = query.search
        ? {
            term: query.search,
            fields: ["notes"],
          }
        : undefined;

      // Call service with transformed options
      const { data, error, result } = await AttendanceService.getAll({
        filters,
        search: searchOptions,
        pagination: {
          page: Number(query.page) || 1,
          pageSize: Number(query.pageSize) || 10,
        },
      });

      if (error) {
        console.error("Error fetching attendances:", error);
        return serverError(error.message, error);
      }

      return success(
        {
          data,
          pagination: result,
        },
        "Attendances fetched successfully"
      );
    } catch (err) {
      console.error("Error in AttendanceController.getAll:", err);
      return serverError(
        "Unexpected error occurred",
        err instanceof Error ? err : undefined
      );
    }
  }

  /**
   * Get attendance records for a specific employee
   * Admin/HR tool to view any employee's attendance history
   */
  static async getEmployeeAttendance(context: Partial<AttendanceContext>) {
    const ctx = ensureResponseFunctions(context);
    const { body, user } = ctx;
    const { success, serverError, badRequest, forbidden } = ctx;

    // Validate employee_id is provided
    if (!body.employee_id) {
      return badRequest("Employee ID is required");
    }

    try {
      // Build filters from body parameters
      const filters: FilterOption[] = [];

      if (body.fromDate) {
        filters.push({ field: "fromDate", value: body.fromDate });
      }

      if (body.toDate) {
        filters.push({ field: "toDate", value: body.toDate });
      }

      // Add status filter if provided
      if (body.status) {
        // Validate that status is a valid enum value
        if (!Object.values(PresenceStatus).includes(body.status)) {
          return badRequest(`Invalid status value: ${body.status}`);
        }

        filters.push({
          field: "status",
          value: body.status,
        });
      }

      // Prepare search options if a search term is provided
      const searchOptions = body.search
        ? {
            term: body.search,
            fields: ["notes"],
          }
        : undefined;

      // Call service with employee_id
      const { data, error, result } = await AttendanceService.getAll(
        {
          filters,
          search: searchOptions,
          pagination: {
            page: Number(body.page) || 1,
            pageSize: Number(body.pageSize) || 10,
          },
        },
        body.employee_id
      ); // Pass employee_id as second parameter

      if (error) {
        console.error("Error fetching employee attendances:", error);
        return serverError(error.message, error);
      }

      return success(
        {
          data,
          pagination: result,
        },
        `Attendance records for employee ${body.employee_id} fetched successfully`
      );
    } catch (err) {
      console.error(
        "Error in AttendanceController.getEmployeeAttendance:",
        err
      );
      return serverError(
        "Unexpected error occurred",
        err instanceof Error ? err : undefined
      );
    }
  }

  /**
   * Get current user's attendance records
   * Employee endpoint to view their own attendance history
   */
  static async getMyAttendance(context: Partial<AttendanceContext>) {
    const ctx = ensureResponseFunctions(context);
    const { query, user, profile } = ctx;
    const { success, serverError, badRequest } = ctx;

    // Security: Extract employee_id from authenticated user context
    const employeeId = profile?.employee_id;

    if (!employeeId) {
      return badRequest("Employee ID not found in user profile");
    }

    try {
      // Build filters from query parameters
      const filters: FilterOption[] = [];

      if (query.fromDate) {
        filters.push({ field: "fromDate", value: query.fromDate });
      }

      if (query.toDate) {
        filters.push({ field: "toDate", value: query.toDate });
      }

      // Add status filter if provided
      if (query.status) {
        // Validate that status is a valid enum value
        if (!Object.values(PresenceStatus).includes(query.status)) {
          return badRequest(`Invalid status value: ${query.status}`);
        }

        filters.push({
          field: "status",
          value: query.status,
        });
      }

      // Prepare search options if a search term is provided
      const searchOptions = query.search
        ? {
            term: query.search,
            fields: ["notes"],
          }
        : undefined;

      // Call service with user's own employee_id
      const { data, error, result } = await AttendanceService.getAll(
        {
          filters,
          search: searchOptions,
          pagination: {
            page: Number(query.page) || 1,
            pageSize: Number(query.pageSize) || 10,
          },
        },
        employeeId
      ); // Pass the user's own employee_id

      if (error) {
        console.error("Error fetching personal attendance records:", error);
        return serverError(error.message, error);
      }

      return success(
        {
          data,
          pagination: result,
        },
        "Your attendance records retrieved successfully"
      );
    } catch (err) {
      console.error("Error in AttendanceController.getMyAttendance:", err);
      return serverError(
        "Unexpected error occurred",
        err instanceof Error ? err : undefined
      );
    }
  }
}
