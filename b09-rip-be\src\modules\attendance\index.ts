import { Elysia } from "elysia";
import { attendanceRoutes } from "./routes";
import { apiResponse } from "../../middleware/api-response";

// Create an instance with the middleware applied
// This ensures middleware is applied directly to this module
const attendanceApp = new Elysia().use(apiResponse).use(attendanceRoutes);

export * from "./service";

// Export the organization module
export const attendances = attendanceApp;

// Re-export for convenience
export * from "./schema";
export * from "./controller";
