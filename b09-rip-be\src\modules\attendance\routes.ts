import { <PERSON><PERSON> } from "elysia";
import { AttendanceController } from "./controller";
import {
  createAttendanceSchema,
  filterAttendanceQuerySchema,
  employeeAttendanceBodySchema,
} from "./schema";
import { requireActiveUser, checkRoles } from "../../middleware/auth";
import { UserRole } from "../../database/models/user-profile.model";

/**
 * Attendance Routes
 *
 * Admin, Managers, HR, Finance, and Operation can manage attendance.
 * All users must be active to access these routes.
 */
export const attendanceRoutes = (app: Elysia) =>
  app.group("/attendances", (app) =>
    app
      .use(requireActiveUser)

      // Create or Update Attendance (POST)
      .post("/", AttendanceController.createOrUpdate, {
        beforeHandle: [
          checkRoles([
            UserRole.Admin,
            UserRole.Manager,
            UserRole.HR,
            UserRole.Finance,
            UserRole.Operation,
          ]),
        ],
        body: createAttendanceSchema.body,
        detail: {
          tags: ["attendances"],
          summary: "Create or update attendance with clock-in/out",
          description:
            "Creates a new attendance record with the current date and clock-in time, or updates an existing record with clock-out time. Tasks can be included in both scenarios.",
          security: [{ bearerAuth: [] }],
          requestBody: {
            content: {
              "application/json": {
                schema: {
                  $ref: "#/components/schemas/CreateAttendanceDto",
                },
                examples: {
                  createAttendanceExample: {
                    $ref: "#/components/examples/createAttendanceExample",
                  },
                  clockOutExample: {
                    $ref: "#/components/examples/clockOutExample",
                  },
                },
              },
            },
          },
          responses: {
            "200": {
              description: "Successfully created or updated attendance",
              content: {
                "application/json": {
                  examples: {
                    clockInResponseExample: {
                      $ref: "#/components/examples/clockInResponseExample",
                    },
                    clockOutResponseExample: {
                      $ref: "#/components/examples/clockOutResponseExample",
                    },
                  },
                },
              },
            },
            "400": {
              description: "Bad request - Invalid input data",
              content: {
                "application/json": {
                  examples: {
                    invalidPresenceStatusExample: {
                      $ref: "#/components/examples/invalidPresenceStatusExample",
                    },
                    missingPresenceStatusExample: {
                      $ref: "#/components/examples/missingPresenceStatusExample",
                    },
                    missingEmployeeIdExample: {
                      $ref: "#/components/examples/missingEmployeeIdExample",
                    },
                  },
                },
              },
            },
            "401": {
              description: "Unauthorized - Authentication required",
              content: {
                "application/json": {
                  example: {
                    $ref: "#/components/examples/unauthorizedExample",
                  },
                },
              },
            },
            "403": {
              description: "Forbidden - Insufficient permissions",
              content: {
                "application/json": {
                  example: {
                    $ref: "#/components/examples/forbiddenExample",
                  },
                },
              },
            },
            "500": {
              description: "Server error",
              content: {
                "application/json": {
                  example: {
                    $ref: "#/components/examples/serverErrorExample",
                  },
                },
              },
            },
          },
        },
      })

      // Get Today's Attendance (GET)
      .get("/today", AttendanceController.getToday, {
        beforeHandle: [
          checkRoles([
            UserRole.Admin,
            UserRole.Manager,
            UserRole.HR,
            UserRole.Finance,
            UserRole.Operation,
          ]),
        ],
        detail: {
          tags: ["attendances"],
          summary: "Get today's attendance for the current user",
          description:
            "Retrieve the attendance record for the current user for today's date (based on Jakarta time).",
          security: [{ bearerAuth: [] }],
          responses: {
            "200": {
              description: "Successfully fetched today's attendance",
              content: {
                "application/json": {
                  examples: {
                    existingAttendanceExample: {
                      $ref: "#/components/examples/existingAttendanceExample",
                    },
                  },
                },
              },
            },
            "404": {
              description: "No attendance record found for today",
              content: {
                "application/json": {
                  example: {
                    $ref: "#/components/examples/notFoundExample",
                  },
                },
              },
            },
            "400": {
              description: "Bad request - Missing employee ID",
              content: {
                "application/json": {
                  example: {
                    $ref: "#/components/examples/missingEmployeeIdExample",
                  },
                },
              },
            },
            "401": {
              description: "Unauthorized - Authentication required",
              content: {
                "application/json": {
                  example: {
                    $ref: "#/components/examples/unauthorizedExample",
                  },
                },
              },
            },
            "403": {
              description: "Forbidden - Insufficient permissions",
              content: {
                "application/json": {
                  example: {
                    $ref: "#/components/examples/forbiddenExample",
                  },
                },
              },
            },
          },
        },
      })

      // Get All Attendances (GET)
      .get("/", AttendanceController.getAll, {
        beforeHandle: [
          checkRoles([
            UserRole.Admin,
            UserRole.Manager,
            UserRole.HR,
            UserRole.Finance,
            UserRole.Operation,
          ]),
        ],
        query: filterAttendanceQuerySchema,
        detail: {
          tags: ["attendances"],
          summary: "Get all attendances",
          description:
            "Retrieve a list of all attendances, filtered by date range and paginated.",
          security: [{ bearerAuth: [] }],
          parameters: [
            {
              name: "fromDate",
              in: "query",
              description:
                "Filter for records from this date (YYYY-MM-DD format)",
              required: false,
              schema: {
                type: "string",
                pattern: "^\\d{4}-\\d{2}-\\d{2}$",
                example: "2023-01-01",
              },
            },
            {
              name: "toDate",
              in: "query",
              description:
                "Filter for records until this date (YYYY-MM-DD format)",
              required: false,
              schema: {
                type: "string",
                pattern: "^\\d{4}-\\d{2}-\\d{2}$",
                example: "2023-12-31",
              },
            },
            {
              name: "page",
              in: "query",
              description: "Page number for pagination (default: 1)",
              required: false,
              schema: {
                type: "integer",
                minimum: 1,
                default: 1,
                example: 1,
              },
            },
            {
              name: "pageSize",
              in: "query",
              description: "Number of records per page (default: 10, max: 100)",
              required: false,
              schema: {
                type: "integer",
                minimum: 1,
                maximum: 100,
                default: 10,
                example: 10,
              },
            },
          ],
          responses: {
            "200": {
              description: "Successfully fetched attendances",
              content: {
                "application/json": {
                  examples: {
                    attendanceListExample: {
                      $ref: "#/components/examples/attendanceListExample",
                    },
                    emptyAttendanceListExample: {
                      $ref: "#/components/examples/emptyAttendanceListExample",
                    },
                  },
                },
              },
            },
            "400": {
              description: "Bad request - Invalid query parameters",
              content: {
                "application/json": {
                  examples: {
                    invalidDateFormatExample: {
                      $ref: "#/components/examples/invalidDateFormatExample",
                    },
                    invalidDateRangeExample: {
                      $ref: "#/components/examples/invalidDateRangeExample",
                    },
                  },
                },
              },
            },
            "401": {
              description: "Unauthorized - Authentication required",
              content: {
                "application/json": {
                  example: {
                    $ref: "#/components/examples/unauthorizedExample",
                  },
                },
              },
            },
            "403": {
              description: "Forbidden - Insufficient permissions",
              content: {
                "application/json": {
                  example: {
                    $ref: "#/components/examples/forbiddenExample",
                  },
                },
              },
            },
          },
        },
      })

      // Get attendance records for a specific employee (admin/hr/manager only)
      .post("/employee", AttendanceController.getEmployeeAttendance, {
        beforeHandle: [
          checkRoles([UserRole.Admin, UserRole.HR, UserRole.Manager]),
        ],
        body: employeeAttendanceBodySchema,
        detail: {
          tags: ["attendances"],
          summary: "Get attendance records for a specific employee",
          description:
            "Retrieves attendance records for a specific employee with optional date filtering and pagination. Admin/HR/Manager only.",
          security: [{ bearerAuth: [] }],
          requestBody: {
            content: {
              "application/json": {
                schema: {
                  $ref: "#/components/schemas/EmployeeAttendanceRequest",
                },
                examples: {
                  employeeAttendanceExample: {
                    $ref: "#/components/examples/employeeAttendanceExample",
                  },
                },
              },
            },
          },
          responses: {
            "200": {
              description: "Employee attendance records retrieved successfully",
              content: {
                "application/json": {
                  schema: {
                    $ref: "#/components/schemas/EmployeeAttendanceResponse",
                  },
                },
              },
            },
            "400": {
              description: "Bad request - missing or invalid data",
              content: {
                "application/json": {
                  examples: {
                    invalidEmployeeIdExample: {
                      $ref: "#/components/examples/invalidEmployeeIdExample",
                    },
                    invalidDateRangeExample: {
                      $ref: "#/components/examples/invalidDateRangeExample",
                    },
                  },
                },
              },
            },
            "401": {
              description: "Unauthorized - invalid or missing token",
              content: {
                "application/json": {
                  example: {
                    $ref: "#/components/examples/unauthorizedExample",
                  },
                },
              },
            },
            "403": {
              description: "Forbidden - insufficient permissions",
              content: {
                "application/json": {
                  example: {
                    $ref: "#/components/examples/forbiddenExample",
                  },
                },
              },
            },
            "500": {
              description: "Internal server error",
              content: {
                "application/json": {
                  example: {
                    $ref: "#/components/examples/serverErrorExample",
                  },
                },
              },
            },
          },
        },
      })

      // Get current user's attendance records
      .get("/me", AttendanceController.getMyAttendance, {
        detail: {
          tags: ["attendances"],
          summary: "Get current user's attendance records",
          description:
            "Retrieves attendance records for the current user with optional date filtering and pagination",
          security: [{ bearerAuth: [] }],
          parameters: [
            {
              name: "fromDate",
              in: "query",
              description:
                "Filter for records from this date (YYYY-MM-DD format)",
              required: false,
              schema: {
                type: "string",
                pattern: "^\\d{4}-\\d{2}-\\d{2}$",
                example: "2023-01-01",
              },
            },
            {
              name: "toDate",
              in: "query",
              description:
                "Filter for records until this date (YYYY-MM-DD format)",
              required: false,
              schema: {
                type: "string",
                pattern: "^\\d{4}-\\d{2}-\\d{2}$",
                example: "2023-12-31",
              },
            },
            {
              name: "page",
              in: "query",
              description: "Page number for pagination (default: 1)",
              required: false,
              schema: {
                type: "integer",
                minimum: 1,
                default: 1,
                example: 1,
              },
            },
            {
              name: "pageSize",
              in: "query",
              description: "Number of records per page (default: 10, max: 100)",
              required: false,
              schema: {
                type: "integer",
                minimum: 1,
                maximum: 100,
                default: 10,
                example: 10,
              },
            },
          ],
          responses: {
            "200": {
              description: "Personal attendance records retrieved successfully",
              content: {
                "application/json": {
                  schema: {
                    $ref: "#/components/schemas/PersonalAttendanceResponse",
                  },
                },
              },
            },
            "400": {
              description: "Bad request - missing or invalid data",
              content: {
                "application/json": {
                  examples: {
                    invalidDateFormatExample: {
                      $ref: "#/components/examples/invalidDateFormatExample",
                    },
                    invalidDateRangeExample: {
                      $ref: "#/components/examples/invalidDateRangeExample",
                    },
                  },
                },
              },
            },
            "401": {
              description: "Unauthorized - invalid or missing token",
              content: {
                "application/json": {
                  example: {
                    $ref: "#/components/examples/unauthorizedExample",
                  },
                },
              },
            },
            "500": {
              description: "Internal server error",
              content: {
                "application/json": {
                  example: {
                    $ref: "#/components/examples/serverErrorExample",
                  },
                },
              },
            },
          },
        },
      })
  );
