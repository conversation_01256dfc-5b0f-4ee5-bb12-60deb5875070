import { t } from "elysia";
import { PresenceStatus } from "../../database/models/attendance.model";
// Note: TaskStatus enum removed for attendance tasks, now using boolean

/**
 * Common schema definitions for attendance module
 */

// Base date schema used throughout the module
export const dateSchema = t.String({
  pattern: "^\\d{4}-\\d{2}-\\d{2}$",
  description: "Date in format YYYY-MM-DD",
});

// Date validation helper function
const validateDateRange = (input: any) => {
  // If both dates are provided, ensure fromDate is before or equal to toDate
  if (input.fromDate && input.toDate) {
    const fromDate = new Date(input.fromDate);
    const toDate = new Date(input.toDate);

    if (isNaN(fromDate.getTime()) || isNaN(toDate.getTime())) {
      return false;
    }

    return fromDate <= toDate;
  }
  return true;
};

// Time schema for clock-in and clock-out times
export const timeSchema = t.Optional(
  t.String({
    pattern: "^\\d{2}:\\d{2}:\\d{2}$",
    description: "Time in format HH:MM:SS",
  })
);

// Employee ID schema
export const employeeIdSchema = t.String({
  description: "Employee ID",
});

// Presence status schema using enum
export const presenceStatusSchema = t.Enum(PresenceStatus, {
  description: "Presence status (present, absent, permit, leave)",
});

// Notes schema for attendance records
export const notesSchema = t.Optional(
  t.String({
    maxLength: 1000,
    description: "Optional notes about attendance (max 1000 characters)",
  })
);

// Pagination schema for query parameters
export const paginationSchema = t.Object({
  page: t.Optional(
    t.Number({
      minimum: 1,
      default: 1,
      description: "Page number for pagination",
    })
  ),
  pageSize: t.Optional(
    t.Number({
      minimum: 1,
      maximum: 100,
      default: 10,
      description: "Number of records per page",
    })
  ),
});

// Schema for employee attendance lookup (Admin/HR tool)
export const employeeAttendanceBodySchema = t.Object(
  {
    employee_id: employeeIdSchema,
    fromDate: t.Optional(
      t.String({
        pattern: "^\\d{4}-\\d{2}-\\d{2}$",
        description: "Start date in YYYY-MM-DD format",
      })
    ),
    toDate: t.Optional(
      t.String({
        pattern: "^\\d{4}-\\d{2}-\\d{2}$",
        description: "End date in YYYY-MM-DD format",
      })
    ),
    status: t.Optional(
      t.Enum(PresenceStatus, {
        description:
          "Filter by attendance status (present, absent, permit, leave)",
      })
    ),
    search: t.Optional(
      t.String({
        description: "Search term to find in attendance notes",
      })
    ),
    page: t.Optional(
      t.Number({
        minimum: 1,
        default: 1,
        description: "Page number for pagination",
      })
    ),
    pageSize: t.Optional(
      t.Number({
        minimum: 1,
        maximum: 100,
        default: 10,
        description: "Number of records per page",
      })
    ),
  },
  {
    validate: validateDateRange,
    error: "fromDate must be before or equal to toDate",
  }
);

// Task schema for complete task records
export const taskSchema = t.Array(
  t.Object({
    description: t.String({
      minLength: 3,
      maxLength: 255,
      description: "Task description (min 3 characters, max 255 characters)",
    }),
    completion_status: t.Boolean({
      description:
        "Task completion status (true = completed, false = not completed)",
    }),
    employee_id: t.String({
      description: "Employee ID assigned to the task",
    }),
    due_date: t.String({
      pattern: "^\\d{4}-\\d{2}-\\d{2}$",
      description: "Due date in format YYYY-MM-DD",
    }),
    created_by: t.String({
      description: "User ID who created the task",
    }),
  }),
  {
    description: "Array of tasks associated with the attendance",
  }
);

// Schema for creating tasks with attendance
export const createTaskSchema = t.Array(
  t.Object({
    description: t.String({
      minLength: 3,
      maxLength: 255,
      description: "Task description (min 3 characters, max 255 characters)",
    }),
    due_date: t.String({
      pattern: "^\\d{4}-\\d{2}-\\d{2}$",
      description: "Due date in format YYYY-MM-DD",
    }),
    completion_status: t.Optional(
      t.Boolean({
        description:
          "Task completion status (true = completed, false = not completed)",
        default: false,
      })
    ),
  }),
  {
    description: "Array of tasks to be created with attendance",
  }
);

// Schema for creating/updating attendance
export const createAttendanceSchema = {
  body: t.Object({
    status: presenceStatusSchema,
    notes: notesSchema,
    tasks: t.Optional(createTaskSchema),
  }),
};

// Schema for filtering attendance with date range and pagination
export const filterAttendanceQuerySchema = t.Object(
  {
    fromDate: t.Optional(
      t.String({
        pattern: "^\\d{4}-\\d{2}-\\d{2}$",
        description: "Start date in YYYY-MM-DD format",
      })
    ),
    toDate: t.Optional(
      t.String({
        pattern: "^\\d{4}-\\d{2}-\\d{2}$",
        description: "End date in YYYY-MM-DD format",
      })
    ),
    status: t.Optional(
      t.Enum(PresenceStatus, {
        description:
          "Filter by attendance status (present, absent, permit, leave)",
      })
    ),
    search: t.Optional(
      t.String({
        description: "Search term to find in attendance notes",
      })
    ),
    page: t.Optional(
      t.Number({
        minimum: 1,
        default: 1,
        description: "Page number for pagination",
      })
    ),
    pageSize: t.Optional(
      t.Number({
        minimum: 1,
        maximum: 100,
        default: 10,
        description: "Number of records per page",
      })
    ),
  },
  {
    validate: validateDateRange,
    error: "fromDate must be before or equal to toDate",
  }
);
