import { dbUtils } from "../../utils/database";
import {
  Attendance,
  AttendanceWithTasksDto,
  PresenceStatus,
} from "../../database/models/attendance.model";
import { Task } from "../../database/models/task.model";
import {
  FieldSearchConfig,
  FieldType,
  FilterOption,
  QueryOptions,
} from "../../utils/database.types";
import { getCurrentJakartaDate, getCurrentJakartaTime } from "../../utils/date";
import { EmployeeService } from "../employee/service";
import { UserProfileModel } from "../auth/models";
// import { v4 as uuidv4 } from "uuid";

/**
 * Interface for attendance input data
 */
interface AttendanceInput {
  status: PresenceStatus;
  notes?: string | null;
  tasks?: TaskInput[];
}

/**
 * Interface for task input data (regular tasks)
 */
interface TaskInput {
  id?: string;
  description: string;
  due_date: string;
  completion_status?: boolean; // Changed to boolean for regular tasks
}

export class AttendanceService {
  private static readonly TABLE_NAME = "attendances";
  private static readonly TASK_TABLE = "tasks";

  /**
   * Get today's attendance record for a specific employee
   * @param employeeId Employee ID to check
   * @returns Today's attendance record if exists
   */
  static async getTodayAttendance(employeeId: string) {
    try {
      const todayDate = getCurrentJakartaDate();

      // Create filter options
      const filters: FilterOption[] = [
        { field: "employee_id", value: employeeId },
        { field: "date", value: todayDate },
      ];

      // Query options
      const options: QueryOptions = { filters };

      // Get matching records
      const { data, error } = await dbUtils.getAll<Attendance>(
        this.TABLE_NAME,
        options
      );

      if (error) {
        console.error("Database error:", error);
        return { data: null, error };
      }

      // Return the first record if found
      const attendance = data && data.length > 0 ? data[0] : null;

      // If attendance is found, fetch related tasks
      if (attendance) {
        // Fetch tasks for this attendance using attendance ID
        const { data: tasks } = await dbUtils
          .query(this.TASK_TABLE)
          .raw.select()
          .eq("attendance_id", attendance.id)
          .is("deleted_at", null);

        // Create the attendance with tasks
        const attendanceWithTasks = {
          ...attendance,
          tasks: tasks || [],
        };

        let alreadyComplete = false;
        let message = null;

        // Check status and determine if record is already complete
        if (attendance.status === PresenceStatus.PRESENT) {
          // For PRESENT status, check if clock-out is filled
          if (attendance.clock_out) {
            alreadyComplete = true;
            message =
              "Attendance for today is already complete with both clock-in and clock-out times";
          } else {
            alreadyComplete = false; // Explicitly set to false for clarity
          }
        } else {
          // For non-PRESENT statuses (absent, permit, leave), always mark as complete
          alreadyComplete = true;
          message = `Attendance for today is already recorded as ${attendance.status}`;
        }

        // If record is marked as complete, add message and flag
        if (alreadyComplete) {
          return {
            data: {
              ...attendanceWithTasks,
              message: message,
              alreadyComplete: true,
            },
            error: null,
            _message: message,
          };
        }

        // Return attendance with tasks
        return {
          data: {
            ...attendanceWithTasks,
            alreadyComplete: false,
          },
          error: null,
        };
      }

      return {
        data: null,
        error: null,
      };
    } catch (err) {
      console.error(
        "Unexpected error in AttendanceService.getTodayAttendance:",
        err
      );
      return {
        data: null,
        error: err instanceof Error ? err : new Error("Unknown error occurred"),
      };
    }
  }

  /**
   * Clock in: Create a new attendance record for today
   * @param data Attendance data with status, notes, and tasks
   * @param employeeId Employee ID
   * @param userId User ID for audit
   */
  static async clockIn(
    data: AttendanceInput,
    employeeId: string,
    userId: string
  ) {
    try {
      // Get the current Jakarta date and time
      const today = getCurrentJakartaDate();
      const currentTime = getCurrentJakartaTime();

      // Prepare the attendance record
      const attendanceData: Attendance = {
        date: today,
        employee_id: employeeId,
        status: data.status,
        clock_in: currentTime,
        clock_out: null,
        notes: data.notes || null,
      };

      // Create attendance in database
      const { data: attendance, error } = await dbUtils.create<Attendance>(
        this.TABLE_NAME,
        attendanceData,
        userId
      );

      if (error) {
        console.error("Database error:", error);
        return { data: null, error };
      }

      // Update employee's presence_status to match the attendance
      const { error: employeeError } = await EmployeeService.update(
        employeeId,
        { presence_status: data.status },
        userId
      );

      if (employeeError) {
        console.error(
          "Error updating employee presence status:",
          employeeError
        );
        // We don't return here as the attendance creation was successful
        // Just log the error and continue with the attendance flow
      }

      let tasks: Task[] = [];
      // Handle tasks if they're provided
      if (data.tasks && data.tasks.length > 0) {
        const tasksResult = await this.addTasksToAttendance(
          employeeId,
          data.tasks,
          userId,
          attendance.id // Pass attendance ID
        );

        if (tasksResult && tasksResult.error) {
          return { data: attendance, error: tasksResult.error };
        }

        if (tasksResult && tasksResult.data) {
          tasks = tasksResult.data;
        }
      }

      // Return attendance with tasks
      return {
        data: {
          ...attendance,
          tasks,
        },
        error: null,
      };
    } catch (err) {
      console.error("Unexpected error in AttendanceService.clockIn:", err);
      return {
        data: null,
        error: err instanceof Error ? err : new Error("Unknown error occurred"),
      };
    }
  }

  /**
   * Clock out: Update attendance record with clock-out time
   * @param attendanceId Attendance ID to update
   * @param data Updated notes and tasks
   * @param userId User ID for audit
   */
  static async clockOut(
    attendanceId: string,
    data: AttendanceInput,
    userId: string
  ) {
    try {
      // Get the current time
      const currentTime = getCurrentJakartaTime();

      // Prepare update data
      const updateData: Partial<Attendance> = {
        clock_out: currentTime,
        notes: data.notes || null, // Update notes if provided
      };

      // Also update status if provided
      if (data.status) {
        updateData.status = data.status;
      }

      // Update attendance record
      const { data: updatedAttendance, error } =
        await dbUtils.update<Attendance>(
          this.TABLE_NAME,
          attendanceId,
          updateData,
          userId
        );

      if (error) {
        console.error("Database error:", error);
        return { data: null, error };
      }

      // Get employee_id from the updated attendance record
      if (updatedAttendance && updatedAttendance.employee_id) {
        // Update employee's presence_status to match the attendance
        const { error: employeeError } = await EmployeeService.update(
          updatedAttendance.employee_id,
          { presence_status: data.status },
          userId
        );

        if (employeeError) {
          console.error(
            "Error updating employee presence status:",
            employeeError
          );
          // We don't return here as the attendance update was successful
          // Just log the error and continue with the attendance flow
        }
      } else {
        console.error("Missing employee_id in the updated attendance record");
      }

      // Handle tasks - first get all existing tasks
      const { data: existingTasks } = await dbUtils
        .query(this.TASK_TABLE)
        .raw.select()
        .eq("attendance_id", attendanceId)
        .is("deleted_at", null);

      // We'll collect all final tasks here
      let finalTasks: Task[] = [];

      // No tasks in request, just return existing ones
      if (!data.tasks || data.tasks.length === 0) {
        finalTasks = existingTasks || [];
      } else {
        // Create maps for faster lookup
        const existingTasksById = new Map();
        const existingTasksByDescription = new Map();

        if (existingTasks && existingTasks.length > 0) {
          existingTasks.forEach((task) => {
            existingTasksById.set(task.id, task);
            existingTasksByDescription.set(task.description, task);
          });
        }

        // Separate tasks that need to be updated vs created
        const tasksToUpdate: TaskInput[] = [];
        const tasksToCreate: TaskInput[] = [];

        data.tasks.forEach((inputTask) => {
          if (inputTask.id) {
            // Task has ID - should be updated
            tasksToUpdate.push(inputTask);
          } else if (existingTasksByDescription.has(inputTask.description)) {
            // Task has no ID but matches existing description - should be updated
            const existingTask = existingTasksByDescription.get(
              inputTask.description
            );
            tasksToUpdate.push({
              ...inputTask,
              id: existingTask.id,
            });
          } else {
            // New task - should be created
            tasksToCreate.push(inputTask);
          }
        });

        // Update existing tasks first
        if (tasksToUpdate.length > 0) {
          const { data: updatedTasks, error: updateError } =
            await this.addTasksToAttendance(
              updatedAttendance.employee_id,
              tasksToUpdate,
              userId,
              attendanceId
            );

          if (updateError) {
            console.error("Error updating tasks:", updateError);
            return { data: updatedAttendance, error: updateError };
          }

          if (updatedTasks) {
            // Add updated tasks to final list
            finalTasks = [...updatedTasks];

            // Create a set of updated task IDs for filtering out existing tasks
            const updatedTaskIds = new Set(updatedTasks.map((task) => task.id));

            // Add any existing tasks that weren't updated to the final list
            if (existingTasks) {
              const untouchedTasks = existingTasks.filter(
                (task) => !updatedTaskIds.has(task.id)
              );
              finalTasks = [...finalTasks, ...untouchedTasks];
            }
          }
        } else if (existingTasks) {
          // No updates, keep all existing tasks
          finalTasks = [...existingTasks];
        }

        // Create new tasks if needed
        if (tasksToCreate.length > 0) {
          const { data: newTasks, error: createError } =
            await this.addTasksToAttendance(
              updatedAttendance.employee_id,
              tasksToCreate,
              userId,
              attendanceId
            );

          if (createError) {
            console.error("Error creating new tasks:", createError);
            return { data: updatedAttendance, error: createError };
          }

          if (newTasks) {
            // Add new tasks to final list
            finalTasks = [...finalTasks, ...newTasks];
          }
        }
      }

      // Return attendance with tasks
      return {
        data: {
          ...updatedAttendance,
          tasks: finalTasks,
        },
        error: null,
      };
    } catch (err) {
      console.error("Unexpected error in AttendanceService.clockOut:", err);
      return {
        data: null,
        error: err instanceof Error ? err : new Error("Unknown error occurred"),
      };
    }
  }

  /**
   * Handle attendance creation or update based on existence
   * Main method for the create attendance endpoint
   * @param data Attendance data with status, notes, and tasks
   * @param employeeId Employee ID
   * @param userId User ID for audit
   */
  static async createOrUpdateAttendance(
    data: AttendanceInput,
    employeeId: string,
    userId: string
  ) {
    try {
      // Check if the employee already has attendance record for today
      const { data: existingAttendance, error: checkError } =
        await this.getTodayAttendance(employeeId);

      if (checkError) {
        return { data: null, error: checkError };
      }

      // If attendance exists
      if (existingAttendance) {
        // Check if record is already marked as complete
        if (existingAttendance.alreadyComplete) {
          // Use a default message
          const message = "Attendance for today is already complete";

          return {
            data: existingAttendance,
            error: null,
            _message: message,
          };
        }

        // If not complete, proceed with clock-out (this will only happen for PRESENT status)
        if (!existingAttendance.id) {
          return { data: null, error: new Error("Attendance ID is missing") };
        }

        // Only allow updates for PRESENT status that needs clock-out
        if (
          existingAttendance.status === PresenceStatus.PRESENT &&
          !existingAttendance.clock_out
        ) {
          return this.clockOut(existingAttendance.id, data, userId);
        } else {
          // This shouldn't normally happen, but handle just in case
          const message = `Cannot update attendance with status ${existingAttendance.status}`;
          return {
            data: existingAttendance,
            error: null,
            _message: message,
          };
        }
      }

      // If no attendance exists, clock in
      return this.clockIn(data, employeeId, userId);
    } catch (err) {
      console.error(
        "Unexpected error in AttendanceService.createOrUpdateAttendance:",
        err
      );
      return {
        data: null,
        error: err instanceof Error ? err : new Error("Unknown error occurred"),
      };
    }
  }

  // Fungsi untuk menambahkan task setelah attendance dibuat
  static async addTasksToAttendance(
    employeeId: string,
    tasks: TaskInput[],
    userId: string,
    attendanceId?: string
  ) {
    try {
      if (!tasks || tasks.length === 0) {
        return { data: [], error: null };
      }

      // Simpan task ke database satu per satu
      const results = [];
      for (const task of tasks) {
        // If task has ID, it's an update
        if (task.id) {
          // First fetch the existing task to get complete record
          const { data: existingTask, error: fetchError } = await dbUtils
            .query(this.TASK_TABLE)
            .raw.select()
            .eq("id", task.id)
            .is("deleted_at", null)
            .single();

          if (fetchError) {
            console.error("Error fetching existing task:", fetchError);
            return { data: null, error: fetchError };
          }

          if (!existingTask) {
            console.error(
              `Task with ID ${task.id} not found or has been deleted`
            );
            continue; // Skip this task and move to the next one
          }

          // Prepare update data - only update specific fields
          const updateData = {
            // Only update these specific fields from input
            description: task.description || existingTask.description,
            due_date: task.due_date || existingTask.due_date,
            completion_status:
              task.completion_status !== undefined
                ? task.completion_status
                : existingTask.completion_status,

            // Preserve these fields
            employee_id: existingTask.employee_id,
            attendance_id: existingTask.attendance_id,
          };

          // Update the task with only the changed fields
          const { data: updatedTask, error: updateError } =
            await dbUtils.update<Task>(
              this.TASK_TABLE,
              task.id,
              updateData,
              userId
            );

          if (updateError) {
            console.error("Database error while updating task:", updateError);
            return { data: null, error: updateError };
          }

          if (updatedTask) {
            results.push(updatedTask);
          }
        } else {
          // For new tasks without ID
          const taskRecord: Task = {
            description: task.description || "",
            completion_status: task.completion_status || false, // Use boolean default
            employee_id: employeeId,
            due_date: task.due_date,
            created_by: userId,
            // Add attendance_id if provided
            ...(attendanceId ? { attendance_id: attendanceId } : {}),
          };

          const { data, error } = await dbUtils.create<Task>(
            this.TASK_TABLE,
            taskRecord,
            userId
          );

          if (error) {
            console.error("Database error while adding task:", error);
            return { data: null, error };
          }

          if (data) {
            results.push(data);
          }
        }
      }

      return { data: results, error: null };
    } catch (err) {
      console.error("Unexpected error in addTasksToAttendance:", err);
      return {
        data: null,
        error: err instanceof Error ? err : new Error("Unknown error occurred"),
      };
    }
  }

  /**
   * Get all attendance records with optional date filtering and pagination
   * @param options Query options including date filters, status filter, search for notes, and pagination
   * @param employeeId Optional employee ID to filter records
   * @returns Attendance records with tasks and pagination info
   */
  static async getAll(options: QueryOptions = {}, employeeId?: string) {
    try {
      // Build filters array
      const filters: FilterOption[] = [];

      // Add employee_id filter if provided
      if (employeeId) {
        filters.push({
          field: "employee_id",
          value: employeeId,
          operator: "eq",
        });
      }

      // Add date range filters if provided
      if (options.filters) {
        options.filters.forEach((filter) => {
          if (filter.field === "fromDate") {
            filters.push({
              field: "date",
              value: filter.value,
              operator: "gte",
            });
          } else if (filter.field === "toDate") {
            filters.push({
              field: "date",
              value: filter.value,
              operator: "lte",
            });
          } else if (filter.field === "status") {
            // Add support for status filtering
            filters.push({
              field: "status",
              value: filter.value,
              operator: "eq",
            });
          } else if (filter.field !== "employee_id") {
            // Skip if it's employee_id from options
            filters.push(filter);
          }
        });
      }

      // Add search configuration if searching notes
      let searchConfig = options.search;
      if (options.search && options.search.term) {
        // If search fields aren't specified, default to searching notes
        if (!options.search.fields || options.search.fields.length === 0) {
          searchConfig = {
            ...options.search,
            fields: ["notes"], // Use string array for simplicity
          };
        } else {
          // Check if we have string[] or FieldSearchConfig[]
          const firstField = options.search.fields[0];
          const isStringArray = typeof firstField === "string";

          // For string array fields
          if (isStringArray) {
            const fields = options.search.fields as string[];
            if (!fields.includes("notes")) {
              searchConfig = {
                ...options.search,
                fields: [...fields, "notes"],
              };
            }
          }
          // For FieldSearchConfig[] fields
          else {
            const fields = options.search.fields as FieldSearchConfig[];
            const hasNotesField = fields.some(
              (field) => field.field === "notes"
            );
            if (!hasNotesField) {
              searchConfig = {
                ...options.search,
                fields: [...fields, { field: "notes", type: FieldType.Text }],
              };
            }
          }
        }
      }

      // Use dbUtils.getAll with updated filters and search configuration
      const { data, error, result } = await dbUtils.getAll<Attendance>(
        this.TABLE_NAME,
        {
          ...options,
          filters,
          search: searchConfig,
        }
      );

      if (error) {
        console.error("Database error:", error);
        return { data: null, error, result: null };
      }

      // If there are no attendance records, return empty result
      if (!data || data.length === 0) {
        return { data, error, result };
      }

      // Step 1: Get all unique employee IDs from the attendance records
      const employeeIds = [...new Set(data.map((a) => a.employee_id))];

      // Step 2: Fetch user profiles for these employee IDs
      const { data: profiles } = await UserProfileModel.getAll({
        filters: [
          {
            field: "employee_id",
            operator: "in",
            value: employeeIds,
          },
        ],
      });

      // Step 3: Create a map of employee_id to fullname
      const employeeNameMap: Record<string, string> = {};
      if (profiles) {
        profiles.forEach((profile) => {
          if (profile.employee_id) {
            employeeNameMap[profile.employee_id] = profile.fullname;
          }
        });
      }

      // Step 4: Fetch tasks for each attendance and add employee name
      const attendancesWithTasksAndNames = await Promise.all(
        data.map(async (attendance) => {
          // Fetch tasks for this attendance using attendance ID
          const { data: tasks } = await dbUtils
            .query(this.TASK_TABLE)
            .raw.select()
            .eq("attendance_id", attendance.id)
            .is("deleted_at", null);

          // Add employee name and tasks to the attendance record
          return {
            ...attendance,
            employee_name: employeeNameMap[attendance.employee_id] || "Unknown",
            tasks: tasks || [],
          };
        })
      );

      return {
        data: attendancesWithTasksAndNames as AttendanceWithTasksDto[],
        error: null,
        result,
      };
    } catch (err) {
      console.error("Error in AttendanceService.getAll:", err);
      return {
        data: null,
        error: err instanceof Error ? err : new Error("Unknown error occurred"),
        result: null,
      };
    }
  }
}

// import { dbUtils } from "../../utils/database";
// import {
//     Attendance,
//     CreateAttendanceDto,
//     ViewDailyAbsenceDto,
// } from "../../database/models/attendance.model";
// import { FilterOption, QueryOptions } from "../../utils/database.types";

// export class AttendanceService {
//     private static readonly TABLE_NAME = "attendance";
//     private static readonly TASK_TABLE = "task";

//     static async create(data: CreateAttendanceDto, userId: string) {
//       try {
//           // Tambahkan log untuk debugging
//           console.log("Creating attendance with data:", JSON.stringify(data));

//           // Simpan data ke dalam database
//           const { data: attendance, error } = await dbUtils.create<Attendance>(
//               this.TABLE_NAME,
//               data,
//               userId
//           );

//           if (error) {
//               console.error("Database error:", error);
//               return {
//                   data: null,
//                   error: error
//               };
//           }

//           if (!attendance) {
//               console.error("No attendance data returned");
//               return {
//                   data: null,
//                   error: new Error("Failed to create attendance")
//               };
//           }

//           return { data: attendance, error: null };
//       } catch (err) {
//           console.error("Unexpected error in AttendanceService.create:", err);
//           return {
//               data: null,
//               error: err instanceof Error ? err : new Error("Unknown error occurred")
//           };
//       }
//   }

//   static async getAllAttendance(options: QueryOptions = {}) {
//     try {
//         console.log("Fetching all attendance records with options:", options);

//         const { data, error } = await dbUtils.getAll<Attendance>(
//             this.TABLE_NAME,
//             options
//         );

//         if (error) {
//             console.error("Database error:", error);
//             return { data: [], error };
//         }

//         return { data: data || [], error: null };
//     } catch (err) {
//         console.error("Unexpected error in AttendanceService.getAllAttendance:", err);
//         return { data: [], error: err instanceof Error ? err : new Error("Unknown error occurred") };
//     }
// }

//   static async getAll(options: QueryOptions = {}) {
//       return dbUtils.getAll<Attendance>("attendance", options);
//   }

// }
