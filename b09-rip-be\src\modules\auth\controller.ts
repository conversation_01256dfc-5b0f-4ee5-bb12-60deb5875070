import { SignInWithPasswordCredentials } from "@supabase/supabase-js";
import { AuthService } from "./service";
import {
  UserProfile,
  UserRole,
} from "../../database/models/user-profile.model";
import { UserProfileModel } from "./models";
import { AuthUser } from "../../middleware/auth";
import { EmployeeService } from "../../modules/employee/service";

/**
 * Helper function to ensure API response functions are available
 */
function ensureResponseFunctions(context: any) {
  // Check if the basic response functions are available
  if (typeof context.success !== "function") {
    console.error("API Response middleware functions not available in context");
    // Provide fallback response if middleware functions aren't available
    return {
      success: (data: any, message = "Operation successful") => ({
        success: true,
        message,
        data,
      }),
      forbidden: (message = "Forbidden", errorCode?: string) => ({
        success: false,
        message,
        data: null,
        error: { code: errorCode || "FORBIDDEN" },
      }),
      unauthorized: (message = "Unauthorized", errorCode?: string) => ({
        success: false,
        message,
        data: null,
        error: { code: errorCode || "UNAUTHORIZED" },
      }),
      notFound: (message = "Not found", errorCode?: string) => ({
        success: false,
        message,
        data: null,
        error: { code: errorCode || "NOT_FOUND" },
      }),
      serverError: (message = "Server error", error?: Error) => ({
        success: false,
        message,
        data: null,
        error: {
          code: "INTERNAL_SERVER_ERROR",
          details: error ? { stack: error.stack } : undefined,
        },
      }),
    };
  }

  // If functions are available, return the original context
  return context;
}

// Extended sign up request interface
interface SignUpRequest {
  body: {
    email: string;
    password: string;
    fullname: string;
    phonenum: string;
    role: UserRole;
  };
}

// Auth user context type
type AuthContext = {
  user: AuthUser;
  profile: UserProfile | null;
};

export class AuthController {
  /**
   * Handle user registration with profile
   */
  static async signUp(context: any) {
    const { body, success, unauthorized, serverError } =
      ensureResponseFunctions(context);

    const { data, error } = await AuthService.signUp(body);

    if (error) {
      return serverError(error.message, error);
    }

    return success(
      {
        user: data?.user,
        profile: data?.profile,
      },
      "User registered successfully"
    );
  }

  /**
   * Handle user authentication
   */
  static async signIn(context: any) {
    const { body, success, unauthorized, serverError } =
      ensureResponseFunctions(context);

    const { data, error } = await AuthService.signIn(body);

    if (error) {
      return unauthorized(error.message, "INVALID_CREDENTIALS");
    }

    // Handle potential null data (this shouldn't happen if there's no error)
    if (!data || !data.user || !data.session) {
      return serverError(
        "Authentication succeeded but no user or session data returned",
        new Error("Missing user or session data")
      );
    }

    return success(
      {
        user: data.user,
        session: data.session,
        profile: data.profile,
      },
      "User authenticated successfully"
    );
  }

  /**
   * Handle user sign out
   */
  static async signOut(context: any) {
    const { headers, success, unauthorized, serverError } =
      ensureResponseFunctions(context);

    if (!headers.authorization) {
      return unauthorized("No authorization token provided", "MISSING_TOKEN");
    }

    // Extract the token
    const token = headers.authorization.replace("Bearer ", "");

    if (!token) {
      return unauthorized("Invalid authorization token", "INVALID_TOKEN");
    }

    const { error } = await AuthService.signOut(token);

    if (error) {
      return serverError(error.message, error);
    }

    return success(null, "User signed out successfully");
  }

  /**
   * Refresh session token
   */
  static async refreshSession(context: any) {
    const { body, success, unauthorized, serverError } =
      ensureResponseFunctions(context);

    const { data, error } = await AuthService.refreshSession(
      body.refresh_token
    );

    if (error) {
      // Check for both error message content and specific error codes/status
      if (
        error.message?.includes("token") ||
        error.message?.includes("session") ||
        error.code === "refresh_token_not_found" ||
        (error.status === 400 && error.message?.includes("refresh token"))
      ) {
        return unauthorized(error.message, "INVALID_REFRESH_TOKEN");
      }
      return serverError(error.message, error);
    }

    return success(
      {
        session: data.session,
        user: data.user,
      },
      "Session refreshed successfully"
    );
  }

  /**
   * Get the current user's profile
   * This is an example of how to use the auth middleware to access the user profile
   */
  static async getCurrentUser(context: any) {
    const { user, profile, token_error, success, unauthorized, serverError } =
      ensureResponseFunctions(context);

    console.log("getCurrentUser called with:", {
      hasUser: !!user,
      hasProfile: !!profile,
      profileIsActive: profile?.is_active,
      tokenError: token_error,
    });

    // Check for authentication errors first
    if (token_error) {
      console.log("Token error detected:", token_error);

      if (token_error.includes("expired")) {
        return unauthorized(
          "Authentication token has expired",
          "TOKEN_EXPIRED"
        );
      }

      return unauthorized("Authentication required", "AUTHENTICATION_REQUIRED");
    }

    if (!user) {
      console.log("Unauthorized: No user found");
      return unauthorized("Authentication required", "AUTHENTICATION_REQUIRED");
    }

    // If the profile wasn't included in the request context,
    // we can fetch it directly using the user ID
    let userProfile = profile;
    if (!userProfile) {
      console.log("No profile in context, fetching from database");
      const { data, error } = await UserProfileModel.getByUserId(user.id);

      if (error) {
        console.log("Error fetching profile:", error);
        return serverError("Failed to fetch user profile", error);
      }

      userProfile = data;
      console.log("Profile fetched directly:", {
        hasProfile: !!userProfile,
        profileRole: userProfile?.role,
      });
    }

    // Verify profile completeness
    let profileComplete = true;
    const incompleteFields: string[] = [];

    if (!userProfile) {
      profileComplete = false;
      incompleteFields.push("profile");
    } else {
      // Check required fields based on role
      if (!userProfile.fullname) {
        profileComplete = false;
        incompleteFields.push("fullname");
      }

      if (!userProfile.phonenum) {
        profileComplete = false;
        incompleteFields.push("phonenum");
      }

      // Role-specific required fields
      if (userProfile.role === UserRole.Client && !userProfile.org_id) {
        profileComplete = false;
        incompleteFields.push("org_id");
      }

      // Check if employee roles have employee_id
      if (
        [
          UserRole.Manager,
          UserRole.HR,
          UserRole.Finance,
          UserRole.Operation,
        ].includes(userProfile.role as UserRole) &&
        !userProfile.employee_id
      ) {
        profileComplete = false;
        incompleteFields.push("employee_id");
      }
    }

    // Check for additional role-specific data
    // For client roles, fetch organization details
    // For employee roles, fetch employee details
    let additionalData = null;

    // For employee roles, check if profile is actually complete (not just placeholders)
    if (
      userProfile &&
      [
        UserRole.Manager,
        UserRole.HR,
        UserRole.Finance,
        UserRole.Operation,
      ].includes(userProfile.role as UserRole) &&
      userProfile.employee_id &&
      userProfile.id
    ) {
      // First check if employee profile is complete with real values (not placeholders)
      const {
        isComplete,
        incompleteFields: employeeIncompleteFields,
        employee,
      } = await EmployeeService.isProfileComplete(
        userProfile.employee_id,
        false
      );

      // Update profile completeness status
      profileComplete = isComplete;

      // Add any incomplete fields
      if (employeeIncompleteFields && employeeIncompleteFields.length > 0) {
        incompleteFields.push(...employeeIncompleteFields);
      }

      // Add employee data to the response
      if (employee) {
        additionalData = { employee };
      } else {
        // Fetch employee details if not returned by isProfileComplete
        const { data: employeeData } = await EmployeeService.getById(
          userProfile.employee_id
        );
        additionalData = { employee: employeeData };
      }
    }
    // For client roles, fetch organization details
    else if (
      userProfile &&
      userProfile.role === UserRole.Client &&
      userProfile.org_id
    ) {
      // In a real app, you'd fetch organization details here
      // const { data: orgData } = await OrganizationService.getById(userProfile.org_id);
      // additionalData = { organization: orgData };
    }

    return success(
      {
        user: {
          id: user.id,
          email: user.email,
        },
        profile: userProfile,
        profileComplete,
        incompleteFields:
          incompleteFields.length > 0 ? incompleteFields : undefined,
        ...(additionalData && { additionalData }),
      },
      "User profile retrieved successfully"
    );
  }
}
