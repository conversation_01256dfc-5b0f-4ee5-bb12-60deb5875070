import { <PERSON>sia } from "elysia";
import { authRoutes } from "./routes";
import { UserProfileModel } from "./models";
import { AuthService } from "./service";
import { apiResponse } from "../../middleware/api-response";

// Create an instance with the middleware applied
// This ensures middleware is applied directly to this module
const authApp = new Elysia().use(apiResponse).use(authRoutes);

// Re-export for convenience
export { UserProfileModel, AuthService };

// Re-export types (now from database/models)
export * from "../../database/models/user-profile.model";

// Export the auth module
export const auth = authApp;
