import { dbUtils } from "../../utils/database";
import {
  UserProfile,
  UserRole,
  CreateUserProfileDto,
  UpdateUserProfileDto,
} from "../../database/models/user-profile.model";
import { OrganizationService } from "../organization";
import { EmployeeService } from "../employee/service";
import { QueryOptions } from "../../utils/database.types";
import { AuthService } from "./service";

const TABLE_NAME = "user_profiles";

export class UserProfileModel {
  /**
   * Create a new user profile
   */
  static async create(data: CreateUserProfileDto, userId?: string) {
    return dbUtils.create<UserProfile>(
      TABLE_NAME,
      data,
      userId || data.user_id
    );
  }

  /**
   * Get a user profile by user_id
   */
  static async getByUserId(userId: string) {
    const { data, error } = await dbUtils
      .query(TABLE_NAME)
      .raw.select("*")
      .eq("user_id", userId)
      .is("deleted_at", null)
      .single();

    return { data: data as UserProfile, error };
  }

  /**
   * Update a user profile
   */
  static async update(userId: string, data: UpdateUserProfileDto) {
    // Get the current profile with all fields
    const { data: profile, error } = await dbUtils
      .query(TABLE_NAME)
      .raw.select("*")
      .eq("user_id", userId)
      .is("deleted_at", null)
      .single();

    if (error || !profile) {
      return { data: null, error: error || new Error("Profile not found") };
    }

    // Check if the role is being updated
    if (data.role && data.role !== profile.role) {
      console.log(
        `User ${userId} role changing from ${profile.role} to ${data.role}`
      );

      // Update the role in the user's JWT metadata
      try {
        const { error: updateRoleError } = await AuthService.updateUserRole(
          userId,
          data.role
        );
        if (updateRoleError) {
          console.error(
            "Failed to update role in user metadata:",
            updateRoleError
          );
          // Continue with database update even if metadata update fails
          // The role will be synced on next sign-in
        } else {
          console.log(
            `Successfully updated role in metadata for user ${userId}`
          );
        }
      } catch (err) {
        console.error("Unexpected error updating role metadata:", err);
        // Don't fail the update if metadata update fails
      }
    }

    // Proceed with the database update
    return dbUtils.update<UserProfile>(TABLE_NAME, profile.id, data, userId);
  }

  /**
   * Delete a user profile (soft delete)
   */
  static async delete(userId: string) {
    const { data: profile, error } = await dbUtils
      .query(TABLE_NAME)
      .raw.select("id")
      .eq("user_id", userId)
      .is("deleted_at", null)
      .single();

    if (error || !profile) {
      return { data: null, error: error || new Error("Profile not found") };
    }

    return dbUtils.softDelete<UserProfile>(TABLE_NAME, profile.id, userId);
  }

  /**
   * Get all user profiles with search, filter, and pagination
   * @param options Query options including search, filters, and pagination
   */
  static async getAll(options: QueryOptions = {}) {
    return dbUtils.getAll<UserProfile>(TABLE_NAME, options);
  }

  /**
   * Find inactive users
   */
  static async findInactive() {
    return dbUtils
      .query(TABLE_NAME)
      .raw.select("*")
      .eq("is_active", false)
      .is("deleted_at", null)
      .order("created_at", { ascending: false });
  }

  /**
   * Send WhatsApp notification to activated user
   * @param userId User ID to notify
   * @param message Notification message
   * @private
   */
  private static async sendWhatsAppNotification(
    userId: string,
    message: string
  ) {
    // TODO: Implement WhatsApp notification API call
    // This will be implemented by connecting to your WhatsApp API service
    console.log(
      `[TODO] Sending WhatsApp notification to user ${userId}: ${message}`
    );

    // Mock implementation - this will be replaced with actual API call
    return {
      success: true,
      message: "WhatsApp notification would be sent here",
    };
  }

  /**
   * Activate a user profile and create/assign associated entity
   * @param userId The user ID to activate
   * @param adminUserId The admin user ID performing the activation
   * @param orgId Optional organization ID for client users
   */
  static async activate(userId: string, adminUserId: string, orgId?: string) {
    // First get the user profile
    const { data: profile, error: profileError } = await this.getByUserId(
      userId
    );

    if (profileError || !profile) {
      return {
        data: null,
        error: profileError || new Error("Profile not found"),
      };
    }

    // Check if already activated
    if (profile.is_active) {
      return {
        data: profile,
        error: new Error("User is already activated"),
      };
    }

    // Create the appropriate associated entity based on role
    let updateData: UpdateUserProfileDto = { is_active: true };

    // For client roles, assign to existing organization
    if (profile.role === UserRole.Client) {
      // For clients, org_id is required
      if (!orgId) {
        return {
          data: null,
          error: new Error(
            "Organization ID is required when activating client users"
          ),
        };
      }

      // Verify organization exists
      const { data: org, error: orgError } = await OrganizationService.getById(
        orgId
      );

      if (orgError || !org) {
        return {
          data: null,
          error: orgError || new Error("Organization not found"),
        };
      }

      updateData.org_id = orgId;
    }
    // For employee roles, create an employee record
    else if (
      [
        UserRole.Manager,
        UserRole.HR,
        UserRole.Finance,
        UserRole.Operation,
      ].includes(profile.role) &&
      !profile.employee_id
    ) {
      // Ensure profile.id exists
      if (!profile.id) {
        return {
          data: null,
          error: new Error("Profile ID is missing or undefined"),
        };
      }

      // Pass both userId for audit trail and profile.id to link records
      const { data: employee, error: empError } =
        await EmployeeService.createEmpty(userId, profile.id);

      if (empError) {
        return { data: null, error: empError };
      }

      updateData.employee_id = employee.id;
    }

    // Update the profile with is_active and the appropriate association
    const result = await this.update(userId, updateData);

    // If activation was successful, send WhatsApp notification
    if (result.data && !result.error) {
      try {
        await this.sendWhatsAppNotification(
          userId,
          "Your account has been activated. Please log in to complete your profile."
        );
      } catch (error) {
        console.error("Failed to send WhatsApp notification:", error);
        // Don't fail the activation if notification fails
      }
    }

    return result;
  }
}
