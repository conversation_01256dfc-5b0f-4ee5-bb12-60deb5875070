import { <PERSON><PERSON>, t } from "elysia";
import { cookie } from "@elysiajs/cookie";
import { AuthController } from "./controller";
import {
  signInSchema,
  signOutSchema,
  signUpSchema,
  refreshTokenSchema,
} from "./schema";
import { protectRoute } from "../../middleware/auth";
import { apiResponse } from "../../middleware/api-response";

// Define response schemas for Swagger documentation
const authResponseSchema = {
  200: t.Object({
    message: t.String(),
    user: t.Object({
      id: t.String(),
      email: t.String(),
    }),
    profile: t.Optional(
      t.Object({
        fullname: t.String(),
        phonenum: t.String(),
        role: t.String(),
      })
    ),
    profileComplete: t.<PERSON>tional(t.Boolean()),
    incompleteFields: t.Optional(t.Array(t.String())),
  }),
  400: t.Object({
    error: t.String(),
    status: t.Number(),
  }),
  401: t.Object({
    error: t.String(),
    status: t.Number(),
  }),
};

const userProfileSchema = {
  200: t.Object({
    user: t.Object({
      id: t.String(),
      email: t.String(),
    }),
    profile: t.Object({
      fullname: t.String(),
      phonenum: t.String(),
      role: t.String(),
      org_id: t.Optional(t.String()),
      employee_id: t.Optional(t.String()),
    }),
    profileComplete: t.Optional(t.Boolean()),
    incompleteFields: t.Optional(t.Array(t.String())),
  }),
  401: t.Object({
    error: t.String(),
    status: t.Number(),
  }),
};

export const authRoutes = (app: Elysia) =>
  app.group("/auth", (app) => {
    return (
      app
        .use(
          cookie({
            httpOnly: true,
            secure: process.env.BUN_ENV === "production",
          })
        )
        // Public routes - no auth required
        .post("/sign-up", AuthController.signUp, {
          body: signUpSchema.body,
          detail: {
            tags: ["auth"],
            summary: "Register a new user",
            description:
              "Create a new user account with email, password, and profile information.\n\n" +
              "Example request body:\n```json\n{\n" +
              '  "email": "<EMAIL>",\n' +
              '  "password": "securepassword123",\n' +
              '  "fullname": "John Doe",\n' +
              '  "phonenum": "*************",\n' +
              '  "role": "Client"\n' +
              "}\n```",
            security: [],
            requestBody: {
              content: {
                "application/json": {
                  schema: {
                    $ref: "#/components/schemas/SignUpRequest",
                  },
                  examples: {
                    signUpExample: {
                      $ref: "#/components/examples/signUpExample",
                    },
                  },
                },
              },
              required: true,
            },
          },
        })
        .post("/sign-in", AuthController.signIn, {
          body: signInSchema.body,
          detail: {
            tags: ["auth"],
            summary: "Authenticate a user",
            description:
              "Sign in with email and password to get authentication token.\n\n" +
              "Example request body:\n```json\n{\n" +
              '  "email": "<EMAIL>",\n' +
              '  "password": "securepassword123"\n' +
              "}\n```",
            security: [],
            requestBody: {
              content: {
                "application/json": {
                  schema: {
                    $ref: "#/components/schemas/SignInRequest",
                  },
                  examples: {
                    signInExample: {
                      $ref: "#/components/examples/signInExample",
                    },
                  },
                },
              },
              required: true,
            },
          },
        })
        .post("/sign-out", AuthController.signOut, {
          detail: {
            tags: ["auth"],
            summary: "Sign out the current user",
            description:
              "Invalidate the current session using JWT token in Authorization header",
            security: [{ bearerAuth: [] }],
          },
        })
        .post("/refresh", AuthController.refreshSession, {
          body: refreshTokenSchema.body,
          detail: {
            tags: ["auth"],
            summary: "Refresh authentication token",
            description:
              "Use a refresh token to get a new authentication token",
            security: [],
            requestBody: {
              content: {
                "application/json": {
                  schema: {
                    $ref: "#/components/schemas/RefreshTokenRequest",
                  },
                  examples: {
                    refreshTokenExample: {
                      $ref: "#/components/examples/refreshTokenExample",
                    },
                  },
                },
              },
              required: true,
            },
          },
        })

        // Protected routes - auth required
        // Note: API response middleware is applied at the module level via index.ts
        .group("/me", (app) => {
          return app.use(protectRoute).get("/", AuthController.getCurrentUser, {
            detail: {
              tags: ["auth"],
              summary: "Get current user profile",
              description:
                "Retrieve the profile information for the authenticated user",
              security: [{ bearerAuth: [] }],
            },
          });
        })
    );
  });
