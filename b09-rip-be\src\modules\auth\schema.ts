import { t } from "elysia";
import { UserRole } from "../../database/models/user-profile.model";

// Common auth schema patterns
export const emailSchema = t.String({
  format: "email",
  description: "User email address in valid email format",
});

export const passwordSchema = t.String({
  minLength: 8,
  description: "Password with minimum 8 characters",
});

export const fullnameSchema = t.String({
  minLength: 2,
  maxLength: 100,
  description: "User's full name (2-100 characters)",
});

export const phonenumSchema = t.String({
  pattern: "^[0-9]+$", // Only numeric digits allowed
  minLength: 10,
  description: "Phone number (minimum 10 digits, only numbers allowed)",
});

export const roleSchema = t.Enum(UserRole, {
  description: "User role (Manager, HR, Finance, Operation, Client)",
});

// Auth request schemas
export const signUpSchema = {
  body: t.Object(
    {
      email: emailSchema,
      password: passwordSchema,
      fullname: fullnameSchema,
      phonenum: phonenumSchema,
      role: roleSchema,
    },
    {
      description: "User registration information",
    }
  ),
  response: {
    200: t.Object({
      message: t.String(),
      user: t.Object({
        id: t.String(),
        email: t.String(),
      }),
      profile: t.Optional(
        t.Object({
          fullname: t.String(),
          phonenum: t.String(),
          role: t.String(),
        })
      ),
    }),
    400: t.Object({
      error: t.String(),
      status: t.Number(),
    }),
  },
};

export const signInSchema = {
  body: t.Object(
    {
      email: emailSchema,
      password: passwordSchema,
    },
    {
      description: "User authentication credentials",
    }
  ),
  response: {
    200: t.Object({
      message: t.String(),
      user: t.Object({
        id: t.String(),
        email: t.String(),
      }),
      profile: t.Optional(
        t.Object({
          fullname: t.String(),
          phonenum: t.String(),
          role: t.String(),
        })
      ),
      token: t.String({
        description: "JWT access token for authentication",
      }),
      refresh_token: t.Optional(
        t.String({
          description: "Refresh token used to get a new access token",
        })
      ),
      profileComplete: t.Optional(
        t.Boolean({
          description:
            "Indicates if the employee has completed their required profile information",
        })
      ),
      incompleteFields: t.Optional(
        t.Array(t.String(), {
          description:
            "List of fields that need to be completed (dob, address, bank_account, bank_name)",
        })
      ),
    }),
  },
};

export const signOutSchema = {
  response: {
    200: t.Object({
      message: t.String(),
    }),
    401: t.Object({
      error: t.String(),
      status: t.Number(),
    }),
    500: t.Object({
      error: t.String(),
      status: t.Number(),
    }),
  },
};

export const refreshTokenSchema = {
  body: t.Object(
    {
      refresh_token: t.String({
        description: "Refresh token used to get a new access token",
      }),
    },
    {
      description: "Token for refreshing user session",
    }
  ),
  response: {
    200: t.Object({
      message: t.String(),
      user: t.Optional(
        t.Object({
          id: t.String(),
          email: t.String(),
        })
      ),
      token: t.String({
        description: "New JWT access token",
      }),
      refresh_token: t.Optional(
        t.String({
          description: "New refresh token",
        })
      ),
    }),
    401: t.Object({
      error: t.String(),
      status: t.Number(),
    }),
  },
};
