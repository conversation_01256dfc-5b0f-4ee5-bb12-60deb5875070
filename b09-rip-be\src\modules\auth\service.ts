import {
  SignUpWithPasswordCredentials,
  SignInWithPasswordCredentials,
} from "@supabase/supabase-js";
import { supabase } from "../../libs/supabase";
import {
  UserProfile,
  UserRole,
} from "../../database/models/user-profile.model";
import { UserProfileModel } from "./models";
import { EmployeeService } from "../employee";

// Extended sign up credentials with profile data
interface ExtendedSignUpCredentials {
  email: string;
  password: string;
  fullname: string;
  phonenum: string;
  role: UserRole;
  options?: {
    data?: object;
    captchaToken?: string;
    emailRedirectTo?: string;
  };
}

export class AuthService {
  /**
   * Register a new user with profile
   */
  static async signUp(credentials: ExtendedSignUpCredentials) {
    // 1. Register the user with Supabase Auth using admin API to avoid email confirmation
    const { data: authData, error: authError } =
      await supabase.auth.admin.createUser({
        email: credentials.email,
        password: credentials.password,
        email_confirm: true, // Explicitly confirm the email without sending verification
        user_metadata: {
          ...credentials.options?.data,
          role: credentials.role, // Include role in metadata so it's in the JWT
        },
      });

    if (authError || !authData.user) {
      return { data: null, error: authError };
    }

    // 2. Create the user profile using the model
    const { data: profileData, error: profileError } =
      await UserProfileModel.create(
        {
          user_id: authData.user.id,
          fullname: credentials.fullname,
          phonenum: credentials.phonenum,
          role: credentials.role,
          is_active: false,
          // These will be set later when the tables are created
          org_id: undefined,
          employee_id: undefined,
        },
        authData.user.id
      );

    if (profileError) {
      // If profile creation fails, we should clean up the auth user
      // but Supabase doesn't expose a delete user API for the client
      // This should be handled by admin functions in production
      console.error("Failed to create user profile", profileError);
      return { data: null, error: profileError };
    }

    // 3. Return the combined user data
    return {
      data: {
        user: authData.user,
        profile: profileData,
      },
      error: null,
    };
  }

  /**
   * Authenticate an existing user and fetch their profile
   */
  static async signIn(credentials: SignInWithPasswordCredentials) {
    // 1. Authenticate the user
    const { data: authData, error: authError } =
      await supabase.auth.signInWithPassword(credentials);

    if (authError || !authData.user) {
      return { data: null, error: authError };
    }

    // 2. Fetch the user profile using the model
    const { data: profileData, error: profileError } =
      await UserProfileModel.getByUserId(authData.user.id);

    if (profileError) {
      console.error("Failed to fetch user profile", profileError);
      // We still return the user but without profile
      return {
        data: {
          user: authData.user,
          session: authData.session,
          profile: null,
        },
        error: null,
      };
    }

    // Check if role in user_metadata matches the role in the database
    // If not, update the user_metadata to keep them in sync
    const userRole = profileData.role;
    const metadataRole = authData.user.user_metadata?.role;

    if (metadataRole !== userRole) {
      console.log(
        `Updating user ${authData.user.id} metadata with role: ${userRole}`
      );
      try {
        // Update user metadata with role from database
        await supabase.auth.admin.updateUserById(authData.user.id, {
          user_metadata: {
            ...authData.user.user_metadata,
            role: userRole,
          },
        });
      } catch (err) {
        console.error("Failed to update user metadata with role:", err);
        // Don't fail the sign-in process, just log the error
      }
    }

    // Only check employee profile completion for employee roles
    let isProfileComplete = true;
    let incompleteFields: string[] = [];

    if (
      profileData &&
      [
        UserRole.Manager,
        UserRole.HR,
        UserRole.Finance,
        UserRole.Operation,
      ].includes(profileData.role) &&
      profileData.employee_id &&
      profileData.id
    ) {
      // 3. Check if employee has completed their profile
      const { isComplete, incompleteFields: fields } =
        await EmployeeService.isProfileComplete(profileData.id);
      isProfileComplete = isComplete;
      incompleteFields = fields || [];
    }

    // 4. Return the combined user data with profile completion status
    return {
      data: {
        user: authData.user,
        profile: profileData,
        session: authData.session,
        profileComplete: isProfileComplete,
        incompleteFields,
      },
      error: null,
    };
  }

  /**
   * Sign out the current user's session
   * Will handle expired tokens gracefully
   */
  static async signOut(token?: string) {
    // If no token is provided, we can't sign out a specific session
    if (!token) {
      return { error: { message: "No authentication token provided" } };
    }

    try {
      // Try to verify the token - but if it's expired, that's okay for sign-out
      const { data: userData, error: verifyError } =
        await supabase.auth.getUser(token);

      // If the token is expired or invalid, we'll log it but still consider the sign-out successful
      if (verifyError) {
        console.log(
          "Token verification error during sign-out:",
          verifyError.message
        );

        // For token expiration errors, we can consider the session already invalid
        if (
          verifyError.message.includes("JWT expired") ||
          verifyError.message.includes("invalid JWT")
        ) {
          console.log("Token already expired, considering sign-out successful");
          return { error: null };
        }

        // For other errors, we'll return the error but with a 401 status
        return {
          error: {
            message: verifyError.message,
            status: 401,
          },
        };
      }

      // If verification succeeded, proceed with normal sign-out
      if (userData.user) {
        // First set the session with the token
        await supabase.auth.setSession({
          access_token: token,
          refresh_token: "", // We don't have this, but we're signing out anyway
        });

        // Then sign out the current session
        const { error } = await supabase.auth.signOut();
        return { error };
      }

      // User not found but no error - still consider it a successful sign-out
      return { error: null };
    } catch (error) {
      console.error("Error during sign-out:", error);
      return {
        error: {
          message:
            error instanceof Error
              ? error.message
              : "Unknown error during sign-out",
          status: 500,
        },
      };
    }
  }

  /**
   * Refresh the user session
   * @param refreshToken Optional refresh token if needed
   */
  static async refreshSession(refreshToken?: string) {
    // If refresh token is provided, use it
    if (refreshToken) {
      return await supabase.auth.refreshSession({
        refresh_token: refreshToken,
      });
    }

    // Otherwise, use the current session
    return await supabase.auth.refreshSession();
  }

  /**
   * Update the user's role in their metadata
   * This should be called whenever a user's role changes in the database
   * to ensure the JWT token has the correct role claim
   */
  static async updateUserRole(userId: string, role: UserRole) {
    try {
      // Get current user metadata first to preserve other values
      const { data: userData, error: userError } =
        await supabase.auth.admin.getUserById(userId);

      if (userError) {
        console.error(`Error fetching user ${userId}:`, userError.message);
        return { error: userError };
      }

      if (!userData || !userData.user) {
        const error = new Error(`User ${userId} not found in auth system`);
        return { error };
      }

      // Preserve existing metadata if any
      const currentMetadata = userData.user.user_metadata || {};

      // Update the role in user metadata
      const { data, error: updateError } =
        await supabase.auth.admin.updateUserById(userId, {
          user_metadata: { ...currentMetadata, role },
        });

      if (updateError) {
        console.error(
          `Error updating role for user ${userId}:`,
          updateError.message
        );
        return { error: updateError };
      }

      return { data, error: null };
    } catch (err) {
      console.error(`Unexpected error updating role for user ${userId}:`, err);
      return {
        error:
          err instanceof Error
            ? err
            : new Error("Unknown error updating user role"),
      };
    }
  }
}
