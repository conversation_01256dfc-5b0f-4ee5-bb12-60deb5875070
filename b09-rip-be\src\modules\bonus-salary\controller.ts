import { BonusSalaryService } from "./service";
import {
  CreateBonusSalaryDto,
  UpdateBonusSalaryDto,
} from "../../database/models/bonus-salary.model";

export class BonusSalaryController {
  /**
   * Create a new bonus entry
   */
  static async create(context: any) {
    const { body, user, success, badRequest, serverError } = context;

    try {
      const bonusData: CreateBonusSalaryDto = {
        salary_id: body.salary_id,
        amount: body.amount,
        bonus_type: body.bonus_type,
        notes: body.notes,
        kpi_id: body.kpi_id,
        project_id: body.project_id,
      };

      const { data, error } = await BonusSalaryService.create(
        bonusData,
        user.id
      );

      if (error) {
        return serverError(error.message, error);
      }

      return success(data, "Bonus created successfully");
    } catch (error: any) {
      return serverError(error.message || "Failed to create bonus", error);
    }
  }

  /**
   * Get all bonuses for a salary
   */
  static async getBySalaryId(context: any) {
    const { params, success, notFound, serverError } = context;
    const { salaryId } = params;

    try {
      const { data, error } = await BonusSalaryService.getBySalaryId(salaryId);

      if (error) {
        return serverError(error.message, error);
      }

      if (!data || data.length === 0) {
        return success([], "No bonuses found for this salary");
      }

      return success(data, "Bonuses retrieved successfully");
    } catch (error: any) {
      return serverError(error.message || "Failed to retrieve bonuses", error);
    }
  }

  /**
   * Get a bonus by ID
   */
  static async getById(context: any) {
    const { params, success, notFound, serverError } = context;
    const { id } = params;

    try {
      const { data, error } = await BonusSalaryService.getById(id);

      if (error) {
        return serverError(error.message, error);
      }

      if (!data) {
        return notFound(`Bonus with the given ID ${id} not found`);
      }

      return success(data, "Bonus retrieved successfully");
    } catch (error: any) {
      return serverError(error.message || "Failed to retrieve bonus", error);
    }
  }

  /**
   * Update a bonus
   */
  static async update(context: any) {
    const { params, body, user, success, notFound, serverError, badRequest } =
      context;
    const { id } = params;

    try {
      // Check if there are any valid fields to update
      if (
        !body.amount &&
        !body.bonus_type &&
        !body.notes &&
        !body.kpi_id &&
        !body.project_id
      ) {
        return badRequest("No valid fields to update", "NO_VALID_FIELDS");
      }

      const { data, error } = await BonusSalaryService.getById(params.id);

      if (error) {
        return serverError(error.message, error);
      }

      if (!data) {
        return notFound(`Bonus with the given ID ${id} not found`);
      }

      const dto: UpdateBonusSalaryDto = {
        amount: body.amount,
        bonus_type: body.bonus_type,
        notes: body.notes,
        kpi_id: body.kpi_id,
        project_id: body.project_id,
      };

      const { data: updatedData, error: updateError } =
        await BonusSalaryService.update(id, dto, user.id);

      if (updateError) {
        return serverError(updateError.message, updateError);
      }

      return success(updatedData, "Bonus updated successfully");
    } catch (error: any) {
      return serverError(error.message || "Failed to update bonus", error);
    }
  }

  /**
   * Delete a bonus
   */
  static async delete(context: any) {
    const { params, user, success, notFound, serverError } = context;
    const { id } = params;

    try {
      const { data, error: fetchError } = await BonusSalaryService.getById(id);

      if (fetchError) {
        return serverError(fetchError.message, fetchError);
      }

      if (!data) {
        return notFound(`Bonus with ID ${id} not found`);
      }

      const { data: deletedBonus, error: deleteError } =
        await BonusSalaryService.delete(id, user.id);

      if (deleteError) {
        return serverError(deleteError.message, deleteError);
      }

      return success(deletedBonus, "Bonus deleted successfully");
    } catch (error: any) {
      return serverError(error.message || "Failed to delete bonus", error);
    }
  }
}
