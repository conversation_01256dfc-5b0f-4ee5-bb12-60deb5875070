import { Elysia } from "elysia";
import { apiResponse } from "../../middleware/api-response";
import { bonusSalaryRoutes } from "./routes";

// Create an instance with the middleware applied
const bonusSalaryApp = new Elysia()
  .use(apiResponse)
  .use(bonusSalaryRoutes);

export * from "./service";

// Export the bonus salary module
export const bonusSalaries = bonusSalaryApp;

// Re-export for convenience
export * from "./schema";
export * from "./controller";
