import { <PERSON><PERSON> } from "elysia";
import { Bonus<PERSON><PERSON>ry<PERSON>ontroller } from "./controller";
import { requireActiveUser, checkRoles } from "../../middleware/auth";
import { UserRole } from "../../database/models/user-profile.model";
import {
  createBonusSchema,
  getBonusesBySalaryIdSchema,
  getBonusByIdSchema,
  updateBonusSchema,
  deleteBonusSchema,
} from "./schema";

export const bonusSalaryRoutes = (app: Elysia) =>
  app.group("/bonuses", (app) =>
    app
      // First ensure users are active
      .use(requireActiveUser)

      // Create a new bonus
      .post("/", BonusSalaryController.create, {
        beforeHandle: [
          checkRoles([UserRole.Finance, UserRole.HR, UserRole.Manager]),
        ],
        ...createBonusSchema,
        detail: {
          tags: ["bonuses"],
          summary: "Create a new bonus",
          description: "Create a new bonus entry for a salary",
          security: [{ bearerAuth: [] }],
          responses: {
            "200": {
              description: "Bonus created successfully",
              content: {
                "application/json": {
                  example: {
                    success: true,
                    message: "Bonus created successfully",
                    data: {
                      id: "323e4567-e89b-12d3-a456-426614174002",
                      salary_id: "123e4567-e89b-12d3-a456-426614174000",
                      amount: 500000,
                      bonus_type: "kpi",
                      notes: "KPI achievement bonus for Q2 2025",
                      kpi_id: "223e4567-e89b-12d3-a456-426614174001",
                      project_id: null,
                    },
                  },
                },
              },
            },
          },
        },
      })

      // Get bonuses by salary ID
      .get("/salary/:salaryId", BonusSalaryController.getBySalaryId, {
        beforeHandle: [
          checkRoles([UserRole.Finance, UserRole.HR, UserRole.Manager]),
        ],
        ...getBonusesBySalaryIdSchema,
        detail: {
          tags: ["bonuses"],
          summary: "Get bonuses by salary ID",
          description: "Retrieve all bonus entries for a specific salary",
          security: [{ bearerAuth: [] }],
          responses: {
            "200": {
              description: "Bonuses retrieved successfully",
              content: {
                "application/json": {
                  example: {
                    success: true,
                    message: "Bonuses retrieved successfully",
                    data: [
                      {
                        id: "323e4567-e89b-12d3-a456-426614174002",
                        salary_id: "123e4567-e89b-12d3-a456-426614174000",
                        amount: 500000,
                        bonus_type: "kpi",
                        notes: "KPI achievement bonus for Q2 2025",
                        kpi_id: "223e4567-e89b-12d3-a456-426614174001",
                        project_id: null,
                      },
                    ],
                  },
                },
              },
            },
          },
        },
      })

      // Get bonus by ID
      .get("/:id", BonusSalaryController.getById, {
        beforeHandle: [
          checkRoles([UserRole.Finance, UserRole.HR, UserRole.Manager]),
        ],
        ...getBonusByIdSchema,
        detail: {
          tags: ["bonuses"],
          summary: "Get bonus by ID",
          description: "Retrieve a specific bonus entry by its ID",
          security: [{ bearerAuth: [] }],
          responses: {
            "200": {
              description: "Bonus retrieved successfully",
              content: {
                "application/json": {
                  example: {
                    success: true,
                    message: "Bonus retrieved successfully",
                    data: {
                      id: "323e4567-e89b-12d3-a456-426614174002",
                      salary_id: "123e4567-e89b-12d3-a456-426614174000",
                      amount: 500000,
                      bonus_type: "kpi",
                      notes: "KPI achievement bonus for Q2 2025",
                      kpi_id: "223e4567-e89b-12d3-a456-426614174001",
                      project_id: null,
                    },
                  },
                },
              },
            },
          },
        },
      })

      // Update a bonus
      .put("/:id", BonusSalaryController.update, {
        beforeHandle: [
          checkRoles([UserRole.Finance, UserRole.HR, UserRole.Manager]),
        ],
        ...updateBonusSchema,
        detail: {
          tags: ["bonuses"],
          summary: "Update a bonus",
          description: "Update an existing bonus entry",
          security: [{ bearerAuth: [] }],
          responses: {
            "200": {
              description: "Bonus updated successfully",
              content: {
                "application/json": {
                  example: {
                    success: true,
                    message: "Bonus updated successfully",
                    data: {
                      id: "323e4567-e89b-12d3-a456-426614174002",
                      salary_id: "123e4567-e89b-12d3-a456-426614174000",
                      amount: 600000,
                      bonus_type: "kpi",
                      notes: "Updated KPI achievement bonus for Q2 2025",
                      kpi_id: "223e4567-e89b-12d3-a456-426614174001",
                      project_id: null,
                    },
                  },
                },
              },
            },
          },
        },
      })

      // Delete a bonus
      .delete("/:id", BonusSalaryController.delete, {
        beforeHandle: [
          checkRoles([UserRole.Finance, UserRole.HR, UserRole.Manager]),
        ],
        ...deleteBonusSchema,
        detail: {
          tags: ["bonuses"],
          summary: "Delete a bonus",
          description: "Delete an existing bonus entry",
          security: [{ bearerAuth: [] }],
          responses: {
            "200": {
              description: "Bonus deleted successfully",
              content: {
                "application/json": {
                  example: {
                    success: true,
                    message: "Bonus deleted successfully",
                    data: {
                      id: "323e4567-e89b-12d3-a456-426614174002",
                      deleted_at: "2025-05-10T00:00:00.000Z",
                      deleted_by: "auth0|123456789",
                    },
                  },
                },
              },
            },
          },
        },
      })
  );
