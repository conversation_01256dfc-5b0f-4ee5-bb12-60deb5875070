import { t } from "elysia";
import { BonusSalaryType } from "../../database/models/bonus-salary.model";

// Common schema patterns
export const salaryIdSchema = t.String({
  format: "uuid",
  description: "The ID of the salary record",
});

export const amountSchema = t.Number({
  minimum: 0,
  maximum: 1000000000,
  description: "The bonus amount",
});

export const bonusTypeSchema = t.Enum(BonusSalaryType, {
  description: "The type of bonus",
});

export const notesSchema = t.Optional(
  t.String({
    maxLength: 1000,
    description: "Additional notes about the bonus",
  })
);

export const kpiIdSchema = t.Optional(
  t.String({
    format: "uuid",
    description: "The ID of the KPI record (required for KPI type bonuses)",
  })
);

export const projectIdSchema = t.Optional(
  t.String({
    format: "uuid",
    description: "The ID of the project (for project type bonuses)",
  })
);

// Validation schemas
export const createBonusSchema = {
  body: t.Object({
    salary_id: salaryIdSchema,
    amount: amountSchema,
    bonus_type: bonusTypeSchema,
    notes: notesSchema,
    kpi_id: kpiIdSchema,
    project_id: projectIdSchema,
  }),
};

export const getBonusesBySalaryIdSchema = {
  params: t.Object({
    salaryId: salaryIdSchema,
  }),
};

export const getBonusByIdSchema = {
  params: t.Object({
    id: t.String({
      format: "uuid",
      description: "The ID of the bonus record",
    }),
  }),
};

export const updateBonusSchema = {
  params: t.Object({
    id: t.String({
      format: "uuid",
      description: "The ID of the bonus record",
    }),
  }),
  body: t.Object({
    amount: t.Optional(amountSchema),
    bonus_type: t.Optional(bonusTypeSchema),
    notes: t.Optional(notesSchema),
    kpi_id: t.Optional(kpiIdSchema),
    project_id: t.Optional(projectIdSchema),
  }),
};

export const deleteBonusSchema = {
  params: t.Object({
    id: t.String({
      format: "uuid",
      description: "The ID of the bonus record",
    }),
  }),
};
