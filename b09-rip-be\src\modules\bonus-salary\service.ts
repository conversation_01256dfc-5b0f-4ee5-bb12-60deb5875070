import { dbUtils } from "../../utils/database";
import {
  BonusSalary,
  CreateBonusSalaryDto,
  BonusSalaryType,
  UpdateBonusSalaryDto,
} from "../../database/models/bonus-salary.model";
import { Salary } from "../../database/models/salary.model";
import { SalaryService } from "../../modules/salary/service";
import { SalaryUpdateHistoryService } from "../../modules/salary/salary-update-history.service";

export class BonusSalaryService {
  static readonly TABLE_NAME = "bonus_salaries";

  /**
   * Create a new bonus entry and update the salary's total_bonus
   */
  static async create(data: CreateBonusSalaryDto, userId: string) {
    try {
      // Validate KPI bonus
      if (data.bonus_type === BonusSalaryType.KPI && !data.kpi_id) {
        return {
          data: null,
          error: new Error("KPI ID is required for KPI type bonuses"),
        };
      }

      // Create the bonus entry
      const { data: bonus, error } = await dbUtils.create<BonusSalary>(
        this.TABLE_NAME,
        data,
        userId
      );

      if (error) {
        return { data: null, error };
      }

      // Get all bonuses for this salary to calculate the total
      const { data: bonuses } = await this.getBySalaryId(data.salary_id);

      if (bonuses && bonuses.length > 0) {
        // Get the current salary to get the old total_bonus value and total salary
        const { data: salary } = await SalaryService.getById(data.salary_id);
        const oldTotalBonus = salary ? salary.total_bonus : 0;
        const oldTotalSalary = salary ? salary.total_salary : 0;

        // Calculate new total bonus
        const totalBonus = bonuses.reduce((sum, item) => sum + item.amount, 0);

        // Calculate the new total salary
        const newTotalSalary =
          salary.base_salary +
          totalBonus +
          salary.total_allowance -
          salary.total_deduction;

        // Track the component update in history
        await SalaryUpdateHistoryService.trackComponentUpdate(
          data.salary_id,
          "bonus",
          "add",
          bonus.bonus_type,
          bonus.amount,
          oldTotalBonus,
          totalBonus,
          oldTotalSalary,
          newTotalSalary,
          userId
        );

        // Update the salary's total_bonus and total_salary
        await dbUtils.update<Salary>(
          "salaries",
          data.salary_id,
          {
            total_bonus: totalBonus,
            total_salary: newTotalSalary,
          },
          userId
        );
      }

      return { data: bonus, error: null };
    } catch (error: any) {
      return {
        data: null,
        error: new Error(`Failed to create bonus: ${error.message}`),
      };
    }
  }

  /**
   * Get all bonuses for a salary
   */
  static async getBySalaryId(salaryId: string) {
    try {
      const { data, error } = await dbUtils.getByField<BonusSalary>(
        this.TABLE_NAME,
        "salary_id",
        salaryId
      );

      if (error) {
        return { data: null, error };
      }

      return { data, error: null };
    } catch (error: any) {
      return {
        data: null,
        error: new Error(`Failed to get bonuses: ${error.message}`),
      };
    }
  }

  /**
   * Get a bonus by ID
   */
  static async getById(id: string) {
    try {
      const { data, error } = await dbUtils.getById<BonusSalary>(
        this.TABLE_NAME,
        id
      );

      if (error) {
        return { data: null, error };
      }

      return { data, error: null };
    } catch (error: any) {
      return {
        data: null,
        error: new Error(`Failed to get bonus by ID: ${error.message}`),
      };
    }
  }

  /**
   * Helper method to recalculate and update the total bonus for a salary
   */
  private static async updateSalaryTotalBonus(
    salaryId: string,
    userId: string,
    componentId: string = "unknown",
    action: string = "update",
    bonusType: BonusSalaryType = BonusSalaryType.OTHER
  ) {
    try {
      // Get the current salary to get the old total_bonus value and total salary
      const { data: salary } = await SalaryService.getById(salaryId);
      const oldTotalBonus = salary ? salary.total_bonus : 0;
      const oldTotalSalary = salary ? salary.total_salary : 0;

      // Get all bonuses for this salary to calculate the total
      const { data: bonuses } = await this.getBySalaryId(salaryId);

      // Calculate the total bonus amount
      const totalBonus =
        bonuses && bonuses.length > 0
          ? bonuses.reduce((sum, item) => sum + item.amount, 0)
          : 0;

      // Calculate the new total salary
      const newTotalSalary =
        salary.base_salary +
        totalBonus +
        salary.total_allowance -
        salary.total_deduction;

      // Track the component update in history
      await SalaryUpdateHistoryService.trackComponentUpdate(
        salaryId,
        "bonus",
        action,
        bonusType,
        0, // We don't know the specific amount in this context
        oldTotalBonus,
        totalBonus,
        oldTotalSalary,
        newTotalSalary,
        userId
      );

      // Update the salary's total_bonus and total_salary
      await dbUtils.update<Salary>(
        "salaries",
        salaryId,
        {
          total_bonus: totalBonus,
          total_salary: newTotalSalary,
        },
        userId
      );

      return { success: true, error: null };
    } catch (error: any) {
      return {
        success: false,
        error: new Error(
          `Failed to update salary total bonus: ${error.message}`
        ),
      };
    }
  }

  /**
   * Update a bonus
   */
  static async update(id: string, data: UpdateBonusSalaryDto, userId: string) {
    try {
      // First get the bonus to get the salary_id
      const { data: existingBonus } = await this.getById(id);

      if (!existingBonus) {
        return { data: null, error: new Error("Bonus not found") };
      }

      const salaryId = existingBonus.salary_id;

      // Update the bonus
      const { data: updatedBonus, error } = await dbUtils.update<BonusSalary>(
        this.TABLE_NAME,
        id,
        data,
        userId
      );

      if (error || !updatedBonus) {
        return {
          data: null,
          error: error || new Error("Failed to update bonus"),
        };
      }

      // Recalculate and update the salary's total_bonus
      await this.updateSalaryTotalBonus(
        salaryId,
        userId,
        id,
        "update",
        updatedBonus.bonus_type
      );

      return { data: updatedBonus, error: null };
    } catch (error: any) {
      return {
        data: null,
        error: new Error(`Failed to update bonus: ${error.message}`),
      };
    }
  }

  /**
   * Delete a bonus
   */
  static async delete(id: string, userId: string) {
    try {
      // First get the bonus to get the salary_id
      const { data: existingBonus } = await this.getById(id);

      if (!existingBonus) {
        return { data: null, error: new Error("Bonus not found") };
      }

      const salaryId = existingBonus.salary_id;

      // Delete the bonus
      const { data: deletedBonus, error } = await dbUtils.softDelete(
        this.TABLE_NAME,
        id,
        userId
      );

      if (error || !deletedBonus) {
        return {
          data: null,
          error: error || new Error("Failed to delete bonus"),
        };
      }

      // Recalculate and update the salary's total_bonus
      await this.updateSalaryTotalBonus(
        salaryId,
        userId,
        id,
        "delete",
        existingBonus.bonus_type
      );

      return { data: deletedBonus, error: null };
    } catch (error: any) {
      return {
        data: null,
        error: new Error(`Failed to delete bonus: ${error.message}`),
      };
    }
  }
}
