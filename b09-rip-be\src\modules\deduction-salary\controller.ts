import { DeductionSalaryService } from "./service";
import { CreateDeductionSalaryDto } from "../../database/models/deduction-salary.model";

export class DeductionSalaryController {
  /**
   * Create a new deduction entry
   */
  static async create(context: any) {
    const { body, user, success, badRequest, serverError } = context;
    
    try {
      const deductionData: CreateDeductionSalaryDto = {
        salary_id: body.salary_id,
        amount: body.amount,
        deduction_type: body.deduction_type,
        notes: body.notes
      };
      
      const { data, error } = await DeductionSalaryService.create(
        deductionData,
        user.id
      );
      
      if (error) {
        return serverError(error.message, error);
      }
      
      return success(data, "Deduction created successfully");
    } catch (error: any) {
      return serverError(
        error.message || "Failed to create deduction",
        error
      );
    }
  }
  
  /**
   * Get all deductions for a salary
   */
  static async getBySalaryId(context: any) {
    const { params, success, notFound, serverError } = context;
    const { salaryId } = params;
    
    try {
      const { data, error } = await DeductionSalaryService.getBySalaryId(salaryId);
      
      if (error) {
        return serverError(error.message, error);
      }
      
      if (!data || data.length === 0) {
        return success([], "No deductions found for this salary");
      }
      
      return success(data, "Deductions retrieved successfully");
    } catch (error: any) {
      return serverError(
        error.message || "Failed to retrieve deductions",
        error
      );
    }
  }
  
  /**
   * Get a deduction by ID
   */
  static async getById(context: any) {
    const { params, success, notFound, serverError } = context;
    const { id } = params;
    
    try {
      const { data, error } = await DeductionSalaryService.getById(id);
      
      if (error) {
        return serverError(error.message, error);
      }
      
      if (!data) {
        return notFound("Deduction not found");
      }
      
      return success(data, "Deduction retrieved successfully");
    } catch (error: any) {
      return serverError(
        error.message || "Failed to retrieve deduction",
        error
      );
    }
  }
  
  /**
   * Update a deduction
   */
  static async update(context: any) {
    const { params, body, user, success, notFound, serverError } = context;
    const { id } = params;
    
    try {
      const { data, error } = await DeductionSalaryService.update(
        id,
        body,
        user.id
      );
      
      if (error) {
        if (error.message === "Deduction not found") {
          return notFound("Deduction not found");
        }
        return serverError(error.message, error);
      }
      
      return success(data, "Deduction updated successfully");
    } catch (error: any) {
      return serverError(
        error.message || "Failed to update deduction",
        error
      );
    }
  }
  
  /**
   * Delete a deduction
   */
  static async delete(context: any) {
    const { params, user, success, notFound, serverError } = context;
    const { id } = params;
    
    try {
      const { data, error } = await DeductionSalaryService.delete(id, user.id);
      
      if (error) {
        if (error.message === "Deduction not found") {
          return notFound("Deduction not found");
        }
        return serverError(error.message, error);
      }
      
      return success(data, "Deduction deleted successfully");
    } catch (error: any) {
      return serverError(
        error.message || "Failed to delete deduction",
        error
      );
    }
  }
}
