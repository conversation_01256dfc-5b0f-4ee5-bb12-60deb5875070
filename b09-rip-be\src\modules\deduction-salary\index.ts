import { Elysia } from "elysia";
import { apiResponse } from "../../middleware/api-response";
import { deductionSalaryRoutes } from "./routes";

// Create an instance with the middleware applied
const deductionSalaryApp = new Elysia()
  .use(apiResponse)
  .use(deductionSalaryRoutes);

export * from "./service";

// Export the deduction salary module
export const deductionSalaries = deductionSalaryApp;

// Re-export for convenience
export * from "./schema";
export * from "./controller";
