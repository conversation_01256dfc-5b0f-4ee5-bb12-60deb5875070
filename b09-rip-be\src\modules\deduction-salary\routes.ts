import { <PERSON><PERSON> } from "elysia";
import { DeductionSalaryController } from "./controller";
import { requireActiveUser, checkRoles } from "../../middleware/auth";
import { UserRole } from "../../database/models/user-profile.model";
import {
  createDeductionSchema,
  getDeductionsBySalaryIdSchema,
  getDeductionByIdSchema,
  updateDeductionSchema,
  deleteDeductionSchema,
} from "./schema";

export const deductionSalaryRoutes = (app: Elysia) =>
  app.group("/deductions", (app) =>
    app
      // First ensure users are active
      .use(requireActiveUser)

      // Implemented endpoints
      .post("/", DeductionSalaryController.create, {
        beforeHandle: [
          checkRoles([UserRole.Finance, UserRole.HR, UserRole.Manager]),
        ],
        ...createDeductionSchema,
        detail: {
          tags: ["deductions"],
          summary: "Create a new salary deduction",
          description: "Create a new deduction entry for a salary record",
        },
      })

      // Get deductions by salary ID
      .get("/salary/:salaryId", DeductionSalaryController.getBySalaryId, {
        beforeHandle: [
          checkRoles([UserRole.Finance, UserRole.HR, UserRole.Manager]),
        ],
        ...getDeductionsBySalaryIdSchema,
        detail: {
          tags: ["deductions"],
          summary: "Get deductions by salary ID",
          description: "Retrieve all deductions for a specific salary record",
        },
      })

      // Get deduction by ID
      .get("/:id", DeductionSalaryController.getById, {
        beforeHandle: [
          checkRoles([UserRole.Finance, UserRole.HR, UserRole.Manager]),
        ],
        ...getDeductionByIdSchema,
        detail: {
          tags: ["deductions"],
          summary: "Get deduction by ID",
          description: "Retrieve a specific deduction by its ID",
        },
      })
      .put("/:id", DeductionSalaryController.update, {
        beforeHandle: [
          checkRoles([UserRole.Finance, UserRole.HR, UserRole.Manager]),
        ],
        ...updateDeductionSchema,
        detail: {
          tags: ["deductions"],
          summary: "Update a deduction",
          description: "Update an existing deduction record",
        },
      })
      .delete("/:id", DeductionSalaryController.delete, {
        beforeHandle: [
          checkRoles([UserRole.Finance, UserRole.HR, UserRole.Manager]),
        ],
        ...deleteDeductionSchema,
        detail: {
          tags: ["deductions"],
          summary: "Delete a deduction",
          description: "Delete a deduction record",
        },
      })
  );
