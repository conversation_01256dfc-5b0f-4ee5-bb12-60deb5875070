import { t } from "elysia";
import { DeductionType } from "../../database/models/deduction-salary.model";

// Common schema patterns
export const salaryIdSchema = t.String({
  format: "uuid",
  description: "The ID of the salary record",
});

export const amountSchema = t.Number({
  minimum: 0,
  maximum: 1000000000,
  description: "The deduction amount",
});

export const deductionTypeSchema = t.Enum(DeductionType, {
  description: "The type of deduction",
});

export const notesSchema = t.Optional(
  t.String({
    maxLength: 1000,
    description: "Additional notes about the deduction",
  })
);

// Validation schemas
export const createDeductionSchema = {
  body: t.Object({
    salary_id: salaryIdSchema,
    amount: amountSchema,
    deduction_type: deductionTypeSchema,
    notes: notesSchema,
  }),
};

export const getDeductionsBySalaryIdSchema = {
  params: t.Object({
    salaryId: salaryIdSchema,
  }),
};

export const getDeductionByIdSchema = {
  params: t.Object({
    id: t.String({
      format: "uuid",
      description: "The ID of the deduction record",
    }),
  }),
};

export const updateDeductionSchema = {
  params: t.Object({
    id: t.String({
      format: "uuid",
      description: "The ID of the deduction record",
    }),
  }),
  body: t.Object({
    amount: t.Optional(amountSchema),
    deduction_type: t.Optional(deductionTypeSchema),
    notes: t.Optional(notesSchema),
  }),
};

export const deleteDeductionSchema = {
  params: t.Object({
    id: t.String({
      format: "uuid",
      description: "The ID of the deduction record",
    }),
  }),
};
