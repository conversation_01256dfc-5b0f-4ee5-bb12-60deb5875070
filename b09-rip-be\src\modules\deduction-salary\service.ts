import { dbUtils } from "../../utils/database";
import {
  DeductionSalary,
  CreateDeductionSalaryDto,
  UpdateDeductionSalaryDto,
  DeductionType,
} from "../../database/models/deduction-salary.model";
import { Salary } from "../../database/models/salary.model";
import { SalaryUpdateHistoryService } from "../salary/salary-update-history.service";

export class DeductionSalaryService {
  static readonly TABLE_NAME = "deduction_salaries";

  /**
   * Create a new deduction entry and update the salary's total_deduction
   */
  static async create(data: CreateDeductionSalaryDto, userId: string) {
    try {
      // Create the deduction entry
      const { data: deduction, error } = await dbUtils.create<DeductionSalary>(
        this.TABLE_NAME,
        data,
        userId
      );

      if (error) {
        return { data: null, error };
      }

      // Get the current salary to get the old total_deduction value and total salary
      const { data: currentSalary } = await dbUtils.getById<Salary>(
        "salaries",
        data.salary_id
      );
      const oldTotalDeduction = currentSalary?.total_deduction || 0;
      const oldTotalSalary = currentSalary?.total_salary || 0;

      // Get all deductions for this salary to calculate the total
      const { data: deductions } = await this.getBySalaryId(data.salary_id);

      if (deductions && deductions.length > 0) {
        const totalDeduction = deductions.reduce(
          (sum, item) => sum + item.amount,
          0
        );

        // Calculate the new total salary
        const newTotalSalary =
          currentSalary.base_salary +
          currentSalary.total_bonus +
          currentSalary.total_allowance -
          totalDeduction;

        // Track the component update in history
        await SalaryUpdateHistoryService.trackComponentUpdate(
          data.salary_id,
          "deduction",
          "add",
          deduction.deduction_type, // Use deduction type instead of ID
          data.amount,
          oldTotalDeduction,
          totalDeduction,
          oldTotalSalary,
          newTotalSalary,
          userId
        );

        // Update the salary's total_deduction and total_salary
        await dbUtils.update<Salary>(
          "salaries",
          data.salary_id,
          {
            total_deduction: totalDeduction,
            total_salary: newTotalSalary,
          },
          userId
        );
      }

      return { data: deduction, error: null };
    } catch (error: any) {
      return {
        data: null,
        error: new Error(`Failed to create deduction: ${error.message}`),
      };
    }
  }

  /**
   * Get all deductions for a salary
   */
  static async getBySalaryId(salaryId: string) {
    try {
      const { data, error } = await dbUtils.getByField<DeductionSalary>(
        this.TABLE_NAME,
        "salary_id",
        salaryId
      );

      if (error) {
        return { data: null, error };
      }

      return { data, error: null };
    } catch (error: any) {
      return {
        data: null,
        error: new Error(`Failed to get deductions: ${error.message}`),
      };
    }
  }

  /**
   * Get a deduction by ID
   */
  static async getById(id: string) {
    try {
      const { data, error } = await dbUtils.getById<DeductionSalary>(
        this.TABLE_NAME,
        id
      );

      if (error) {
        return { data: null, error };
      }

      return { data, error: null };
    } catch (error: any) {
      return {
        data: null,
        error: new Error(`Failed to get deduction: ${error.message}`),
      };
    }
  }

  /**
   * Update a deduction and recalculate the salary's total_deduction
   */
  static async update(
    id: string,
    data: UpdateDeductionSalaryDto,
    userId: string
  ) {
    try {
      // Get the current deduction to get the salary_id
      const { data: currentDeduction, error: getError } = await this.getById(
        id
      );

      if (getError || !currentDeduction) {
        return {
          data: null,
          error: getError || new Error("Deduction not found"),
        };
      }

      // Get the current salary to get the old total_deduction value and total salary
      const { data: currentSalary } = await dbUtils.getById<Salary>(
        "salaries",
        currentDeduction.salary_id
      );
      const oldTotalDeduction = currentSalary?.total_deduction || 0;
      const oldTotalSalary = currentSalary?.total_salary || 0;

      // Update the deduction
      const { data: updatedDeduction, error: updateError } =
        await dbUtils.update<DeductionSalary>(
          this.TABLE_NAME,
          id,
          data,
          userId
        );

      if (updateError) {
        return { data: null, error: updateError };
      }

      // Get all deductions for this salary to calculate the total
      const { data: deductions } = await this.getBySalaryId(
        currentDeduction.salary_id
      );

      if (deductions && deductions.length > 0) {
        const totalDeduction = deductions.reduce(
          (sum, item) => sum + item.amount,
          0
        );

        // Calculate the new total salary
        const newTotalSalary =
          currentSalary.base_salary +
          currentSalary.total_bonus +
          currentSalary.total_allowance -
          totalDeduction;

        // Track the component update in history
        await SalaryUpdateHistoryService.trackComponentUpdate(
          currentDeduction.salary_id,
          "deduction",
          "update",
          data.deduction_type || currentDeduction.deduction_type,
          data.amount || currentDeduction.amount,
          oldTotalDeduction,
          totalDeduction,
          oldTotalSalary,
          newTotalSalary,
          userId
        );

        // Update the salary's total_deduction and total_salary
        await dbUtils.update<Salary>(
          "salaries",
          currentDeduction.salary_id,
          {
            total_deduction: totalDeduction,
            total_salary: newTotalSalary,
          },
          userId
        );
      }

      return { data: updatedDeduction, error: null };
    } catch (error: any) {
      return {
        data: null,
        error: new Error(`Failed to update deduction: ${error.message}`),
      };
    }
  }

  /**
   * Delete a deduction and recalculate the salary's total_deduction
   */
  static async delete(id: string, userId: string) {
    try {
      // Get the current deduction to get the salary_id
      const { data: currentDeduction, error: getError } = await this.getById(
        id
      );

      if (getError || !currentDeduction) {
        return {
          data: null,
          error: getError || new Error("Deduction not found"),
        };
      }

      // Get the current salary to get the old total_deduction value and total salary
      const { data: currentSalary } = await dbUtils.getById<Salary>(
        "salaries",
        currentDeduction.salary_id
      );
      const oldTotalDeduction = currentSalary?.total_deduction || 0;
      const oldTotalSalary = currentSalary?.total_salary || 0;

      // Soft delete the deduction
      const { error: deleteError } = await dbUtils.softDelete(
        this.TABLE_NAME,
        id,
        userId
      );

      if (deleteError) {
        return { data: null, error: deleteError };
      }

      // Get all deductions for this salary to calculate the total
      const { data: deductions } = await this.getBySalaryId(
        currentDeduction.salary_id
      );

      // Calculate new total deduction (excluding the deleted one)
      const totalDeduction =
        deductions && deductions.length > 0
          ? deductions.reduce((sum, item) => sum + item.amount, 0)
          : 0;

      // Calculate the new total salary
      const newTotalSalary =
        currentSalary.base_salary +
        currentSalary.total_bonus +
        currentSalary.total_allowance -
        totalDeduction;

      // Track the component update in history after deleting
      await SalaryUpdateHistoryService.trackComponentUpdate(
        currentDeduction.salary_id,
        "deduction",
        "delete",
        currentDeduction.deduction_type,
        currentDeduction.amount,
        oldTotalDeduction,
        totalDeduction,
        oldTotalSalary,
        newTotalSalary,
        userId
      );

      // Update the salary's total_deduction and total_salary
      await dbUtils.update<Salary>(
        "salaries",
        currentDeduction.salary_id,
        {
          total_deduction: totalDeduction,
          total_salary: newTotalSalary,
        },
        userId
      );

      return { data: { id }, error: null };
    } catch (error: any) {
      return {
        data: null,
        error: new Error(`Failed to delete deduction: ${error.message}`),
      };
    }
  }
}
