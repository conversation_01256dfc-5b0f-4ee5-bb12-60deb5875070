import { EmployeeService } from "./service";
import { QueryOptions } from "../../utils/database.types";
import { defaultEmployeeQueryOptions } from "./schema";

/**
 * Helper function to ensure API response functions are available
 */
function ensureResponseFunctions(context: any) {
  // Check if the basic response functions are available
  if (typeof context.success !== "function") {
    // Provide fallback response if middleware functions aren't available
    return {
      success: (data: any, message = "Operation successful") => ({
        success: true,
        message,
        data,
      }),
      forbidden: (message = "Forbidden", errorCode?: string) => ({
        success: false,
        message,
        data: null,
        error: { code: errorCode || "FORBIDDEN" },
      }),
      unauthorized: (message = "Unauthorized", errorCode?: string) => ({
        success: false,
        message,
        data: null,
        error: { code: errorCode || "UNAUTHORIZED" },
      }),
      notFound: (message = "Not found", errorCode?: string) => ({
        success: false,
        message,
        data: null,
        error: { code: errorCode || "NOT_FOUND" },
      }),
      serverError: (message = "Server error", error?: Error) => ({
        success: false,
        message,
        data: null,
        error: {
          code: "INTERNAL_SERVER_ERROR",
          details: error ? { stack: error.stack } : undefined,
        },
      }),
    };
  }

  // If functions are available, return the original context
  return context;
}

export class EmployeeController {
  /**
   * Get all employees with search and pagination
   */
  static async getAll(context: any) {
    const {
      query = {},
      success,
      serverError,
    } = ensureResponseFunctions(context);

    // Build query options from request parameters
    const options: QueryOptions = {
      ...defaultEmployeeQueryOptions,
    };

    // Handle search
    if (query.search) {
      options.search = {
        term: query.search,
        fields: defaultEmployeeQueryOptions.search!.fields,
      };
    }

    // Handle filters
    if (query.department) {
      options.filters = [
        ...(options.filters || []),
        { field: "department", value: query.department },
      ];
    }

    if (query.employment_status) {
      options.filters = [
        ...(options.filters || []),
        { field: "employment_status", value: query.employment_status },
      ];
    }

    if (query.presence_status) {
      options.filters = [
        ...(options.filters || []),
        { field: "presence_status", value: query.presence_status },
      ];
    }

    // Handle pagination
    if (query.page || query.pageSize) {
      options.pagination = {
        page: query.page ? parseInt(query.page, 10) : 1,
        pageSize: query.pageSize ? parseInt(query.pageSize, 10) : 10,
      };
    }

    // Call the service with the constructed options
    const { data, error, result } = await EmployeeService.getAll(options);

    if (error) {
      return serverError(error.message, error);
    }

    // Include pagination result in the response if available
    return success(
      {
        items: data,
        pagination: result,
      },
      "Employees retrieved successfully"
    );
  }

  /**
   * Get employee by ID
   */
  static async getById(context: any) {
    const { params, success, notFound, serverError } =
      ensureResponseFunctions(context);

    const { id } = params;
    const { data, error } = await EmployeeService.getById(id);

    if (error) {
      if (error.code === "NOT_FOUND") {
        return notFound("Employee not found", error);
      }
      return serverError(error.message, error);
    }

    return success(data, "Employee retrieved successfully");
  }

  /**
   * Update employee information
   */
  static async update(context: any) {
    const { params, body, user, success, notFound, serverError } =
      ensureResponseFunctions(context);

    const { id } = params;
    const { data, error } = await EmployeeService.update(id, body, user?.id);

    if (error) {
      if (error.code === "NOT_FOUND") {
        return notFound("Employee not found", error);
      }
      return serverError(error.message, error);
    }

    return success(data, "Employee updated successfully");
  }
}
