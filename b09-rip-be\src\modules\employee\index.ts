import { Elysia } from "elysia";
import { employeeRoutes } from "./routes";
import { apiResponse } from "../../middleware/api-response";

// Create an instance with the middleware applied
const employeeApp = new Elysia().use(apiResponse).use(employeeRoutes);

// Export the employee module
export const employee = employeeApp;

// Re-export for convenience
export * from "./schema";
export * from "./controller";
export * from "./service";
