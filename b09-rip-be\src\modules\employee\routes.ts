import { <PERSON><PERSON> } from "elysia";
import { Employee<PERSON><PERSON>roller } from "./controller";
import {
  getAllEmployeesSchema,
  getEmployeeSchema,
  updateEmployeeSchema,
} from "./schema";
import { requireActiveUser, checkRoles } from "../../middleware/auth";
import { UserRole } from "../../database/models/user-profile.model";
import { apiResponse } from "../../middleware/api-response";
import { EmployeeService } from "./service";

/**
 * Employee routes
 * All routes require an active user and appropriate role permissions
 */
export const employeeRoutes = (app: Elysia) =>
  app.group("/employees", (app) =>
    app
      // Apply API response middleware first
      .use(apiResponse)
      // Then apply authentication middleware
      .use(requireActiveUser)

      // Get all employees (accessible by HR and Manager)
      .get("/", EmployeeController.getAll, {
        query: getAllEmployeesSchema.query,
        detail: {
          tags: ["employees"],
          summary: "Get all employees",
          description:
            "Retrieve all employee profiles with search, filter, and pagination. Returns a paginated list of employee profiles.",
          security: [{ bearerAuth: [] }],
          responses: {
            "200": {
              description: "Successfully retrieved employees",
              content: {
                "application/json": {
                  schema: {
                    type: "object",
                    properties: {
                      success: { type: "boolean", example: true },
                      message: { type: "string" },
                      data: {
                        type: "object",
                        properties: {
                          items: {
                            type: "array",
                            items: {
                              $ref: "#/components/schemas/Employee",
                            },
                          },
                          pagination: {
                            $ref: "#/components/schemas/PaginationResult",
                          },
                        },
                      },
                    },
                  },
                },
              },
            },
            "403": {
              description: "Forbidden - User does not have required role",
            },
          },
        },
      })

      // Get employee by ID (accessible by HR and Manager)
      .get("/:id", EmployeeController.getById, {
        beforeHandle: [
          (context) => {
            const { user, profile, params } = context;

            // Allow if user is HR or Manager or the employee themselves
            if (
              profile?.role === UserRole.Admin ||
              profile?.role === UserRole.HR ||
              profile?.role === UserRole.Manager ||
              profile?.employee_id === params.id
            ) {
              return;
            }

            // Otherwise, throw unauthorized error
            throw new Error(
              "Unauthorized: You can only update your own profile unless you are HR"
            );
          },
        ],
        params: getEmployeeSchema.params,
        detail: {
          tags: ["employees"],
          summary: "Get employee by ID",
          description:
            "Retrieve detailed information about a specific employee.",
          security: [{ bearerAuth: [] }],
          responses: {
            "200": {
              description: "Successfully retrieved employee",
              content: {
                "application/json": {
                  schema: {
                    type: "object",
                    properties: {
                      success: { type: "boolean", example: true },
                      message: { type: "string" },
                      data: {
                        $ref: "#/components/schemas/Employee",
                      },
                    },
                  },
                },
              },
            },
            "404": {
              description: "Employee not found",
            },
            "403": {
              description: "Forbidden - User does not have required role",
            },
          },
        },
      })

      // Update employee (accessible by HR and the employee themselves)
      .patch("/:id", EmployeeController.update, {
        beforeHandle: [
          (context) => {
            const { user, profile, params } = context;

            // Allow if user is HR or the employee themselves
            if (
              profile?.role === UserRole.Admin ||
              profile?.role === UserRole.HR ||
              profile?.employee_id === params.id
            ) {
              return;
            }

            // Otherwise, throw unauthorized error
            throw new Error(
              "Unauthorized: You can only update your own profile unless you are HR"
            );
          },
        ],
        params: updateEmployeeSchema.params,
        body: updateEmployeeSchema.body,
        detail: {
          tags: ["employees"],
          summary: "Update employee",
          description:
            "Update information for a specific employee. HR can update any employee, while other users can only update their own profile.",
          security: [{ bearerAuth: [] }],
          responses: {
            "200": {
              description: "Successfully updated employee",
              content: {
                "application/json": {
                  schema: {
                    type: "object",
                    properties: {
                      success: { type: "boolean", example: true },
                      message: { type: "string" },
                      data: {
                        $ref: "#/components/schemas/Employee",
                      },
                    },
                  },
                },
              },
            },
            "404": {
              description: "Employee not found",
            },
            "403": {
              description: "Forbidden - User does not have required role",
            },
          },
        },
      })
  );
