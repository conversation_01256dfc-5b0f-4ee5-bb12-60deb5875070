import { t } from "elysia";
import { PresenceStatus } from "../../database/models/attendance.model";
import { EmploymentStatus } from "../../database/models/employee.model";
import { UserRole } from "../../database/models/user-profile.model";
import { FieldType, QueryOptions } from "../../utils/database.types";

/**
 * Schema for getting all employees with search and pagination
 */
export const getAllEmployeesSchema = {
  query: t.Object({
    search: t.Optional(
      t.String({
        description: "Search term to filter employees by name, email, or phone",
      })
    ),
    page: t.Optional(
      t.Number({
        description: "Page number for pagination (starts at 1)",
        minimum: 1,
      })
    ),
    pageSize: t.Optional(
      t.Number({
        description: "Number of items per page",
        minimum: 1,
        maximum: 1000,
      })
    ),
    department: t.Optional(
      t.Enum(UserRole, {
        description: "Filter by department/role",
      })
    ),
    employment_status: t.Optional(
      t.Enum(EmploymentStatus, {
        description: "Filter by employment status",
      })
    ),
    presence_status: t.Optional(
      t.Enum(PresenceStatus, {
        description: "Filter by presence status",
      })
    ),
  }),
};

/**
 * Schema for getting a single employee by ID
 */
export const getEmployeeSchema = {
  params: t.Object({
    id: t.String({
      format: "uuid",
      description: "The ID of the employee to retrieve",
    }),
  }),
};

/**
 * Schema for updating employee information
 */
export const updateEmployeeSchema = {
  params: t.Object({
    id: t.String({
      format: "uuid",
      description: "The ID of the employee to update",
    }),
  }),
  body: t.Object({
    dob: t.Optional(
      t.String({
        format: "date",
        description: "Date of birth (YYYY-MM-DD)",
      })
    ),
    employment_status: t.Optional(
      t.Enum(EmploymentStatus, {
        description: "Employment status (intern/fulltime)",
      })
    ),
    presence_status: t.Optional(
      t.Enum(PresenceStatus, {
        description: "Current presence status",
      })
    ),
    start_date: t.Optional(
      t.String({
        format: "date",
        description: "Employment start date (YYYY-MM-DD)",
      })
    ),
    department: t.Optional(
      t.Enum(UserRole, {
        description: "Department/role of the employee",
      })
    ),
    address: t.Optional(
      t.String({
        minLength: 1,
        maxLength: 255,
        description: "Complete address of the employee",
      })
    ),
    bank_account: t.Optional(
      t.String({
        minLength: 1,
        maxLength: 50,
        description: "Bank account number",
      })
    ),
    bank_name: t.Optional(
      t.String({
        minLength: 1,
        maxLength: 50,
        description: "Name of the bank",
      })
    ),
  }),
};

/**
 * Default query options for employee search
 */
export const defaultEmployeeQueryOptions: QueryOptions = {
  search: {
    term: "",
    fields: [
      { field: "fullname", type: FieldType.Text },
      { field: "email", type: FieldType.Text },
      { field: "phonenum", type: FieldType.Text },
    ],
  },
  pagination: {
    page: 1,
    pageSize: 10,
  },
};
