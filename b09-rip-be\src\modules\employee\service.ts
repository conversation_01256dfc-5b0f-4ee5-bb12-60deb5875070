import { dbUtils } from "../../utils/database";
import {
  QueryOptions,
  FieldType,
  FilterOption,
} from "../../utils/database.types";
import { supabase } from "../../libs/supabase";
import {
  CreateEmployeeDto,
  Employee,
  UpdateEmployeeDto,
  EmploymentStatus,
} from "../../database/models/employee.model";
import { PresenceStatus } from "../../database/models/attendance.model";

export class EmployeeService {
  private static readonly TABLE_NAME = "employees";

  /**
   * Get all employees with search, filter, and pagination
   * @param options Query options including search, filters, and pagination
   */
  static async getAll(options: QueryOptions = {}) {
    try {
      // Get ALL auth users first - we'll use this data for joining emails
      const allAuthUsers: any[] = [];
      let page = 1;
      const perPage = 100;
      let hasMore = true;

      // Fetch all auth users with pagination
      while (hasMore) {
        const { data: authData, error: authError } =
          await supabase.auth.admin.listUsers({
            perPage: perPage,
            page: page,
          });

        if (authError) {
          console.error("Error fetching auth users:", authError);
          break;
        }

        if (authData && authData.users && authData.users.length > 0) {
          allAuthUsers.push(...authData.users);
          page++;

          // If we got fewer results than perPage, we've reached the end
          if (authData.users.length < perPage) {
            hasMore = false;
          }
        } else {
          hasMore = false;
        }
      }

      // Create a map of user_id -> auth user data for easy lookup
      const authUserMap = new Map<string, any>();
      for (const user of allAuthUsers) {
        if (user.id) {
          authUserMap.set(user.id, user);
        }
      }

      // If we have a search term, we need to handle it differently
      let profileIds: string[] = [];
      if (options.search?.term && options.search.term.trim() !== "") {
        const searchTerm = options.search.term.trim();

        // First, find user_profiles that match the search term
        const { data: matchingProfiles, error: profileError } = await supabase
          .from("user_profiles")
          .select("id")
          .or(`fullname.ilike.%${searchTerm}%,phonenum.ilike.%${searchTerm}%`)
          .is("deleted_at", null);

        if (profileError) {
          console.error("Error searching profiles:", profileError);
          return {
            data: null,
            error: {
              message: profileError.message,
              code: profileError.code,
            },
            result: null,
          };
        }

        // Get the matching profile IDs
        profileIds = matchingProfiles?.map((profile) => profile.id) || [];

        // If no profiles match, return empty result immediately
        if (profileIds.length === 0) {
          return {
            data: [],
            error: null,
            result: {
              total: 0,
              page: options.pagination?.page || 1,
              pageSize: options.pagination?.pageSize || 10,
            },
          };
        }
      }

      // Build query to get employees
      const query = supabase
        .from("employees")
        .select(
          `
          *,
          user_profiles!profile_id (
            fullname,
            phonenum,
            role,
            user_id
          )
        `,
          { count: "exact" }
        )
        .is("deleted_at", null);

      // If we have profile IDs from search, filter by them
      if (options.search?.term && profileIds.length > 0) {
        query.in("profile_id", profileIds);
      }

      // Apply filters if provided
      if (options.filters && options.filters.length > 0) {
        options.filters.forEach((filter) => {
          const { field, value, operator = "eq" } = filter;

          switch (operator) {
            case "eq":
              query.eq(field, value);
              break;
            case "neq":
              query.neq(field, value);
              break;
            case "gt":
              query.gt(field, value);
              break;
            case "gte":
              query.gte(field, value);
              break;
            case "lt":
              query.lt(field, value);
              break;
            case "lte":
              query.lte(field, value);
              break;
            case "in":
              query.in(field, Array.isArray(value) ? value : [value]);
              break;
            case "is":
              query.is(field, value);
              break;
          }
        });
      }

      // Apply pagination if provided
      if (options.pagination) {
        const { page = 1, pageSize = 10 } = options.pagination;
        const start = (page - 1) * pageSize;
        query.range(start, start + pageSize - 1);
      }

      // Execute the query
      const { data, error, count } = await query;

      if (error) {
        console.error("Database query error:", error);
        return {
          data: null,
          error: { message: error.message, code: error.code },
          result: null,
        };
      }

      // Process and flatten the data
      const processedData =
        data?.map((employee) => {
          const userProfile = employee.user_profiles || {};
          const authUser = authUserMap.get(userProfile.user_id);

          return {
            id: employee.id,
            email: authUser?.email || null,
            profile: {
              fullname: userProfile.fullname || "",
              phonenum: userProfile.phonenum || "",
              role: userProfile.role || "",
            },
            profile_id: employee.profile_id,
            dob: employee.dob,
            address: employee.address,
            bank_account: employee.bank_account,
            bank_name: employee.bank_name,
            employment_status: employee.employment_status,
            presence_status: employee.presence_status,
            department: employee.department,
            start_date: employee.start_date,
            salary_id: employee.salary_id,
            created_at: employee.created_at,
            created_by: employee.created_by,
            updated_at: employee.updated_at,
            updated_by: employee.updated_by,
            deleted_at: employee.deleted_at,
            deleted_by: employee.deleted_by,
          };
        }) || [];

      return {
        data: processedData,
        error: null,
        result: {
          total: count || processedData.length,
          page: options.pagination?.page || 1,
          pageSize: options.pagination?.pageSize || 10,
        },
      };
    } catch (error) {
      console.error("Error in getAll:", error);
      return {
        data: null,
        error: {
          message:
            error instanceof Error ? error.message : "Unknown error occurred",
          code: "INTERNAL_ERROR",
        },
        result: null,
      };
    }
  }

  /**
   * Get employee by ID with profile data
   * @param id The employee ID
   * @returns The employee with profile data or error
   */
  static async getById(id: string) {
    try {
      // Get auth user data first
      const { data: authData, error: authError } =
        await supabase.auth.admin.listUsers();

      if (authError) {
        console.error("Error fetching auth users:", authError);
        return {
          data: null,
          error: {
            message: "Failed to fetch auth user data",
            code: authError.code,
          },
        };
      }

      // Create a map of user_id -> auth user data
      const authUserMap = new Map<string, any>();
      for (const user of authData.users) {
        if (user.id) {
          authUserMap.set(user.id, user);
        }
      }

      const { data, error } = await supabase
        .from("employees")
        .select(
          `
          *,
          user_profiles!profile_id (
            fullname,
            phonenum,
            role,
            user_id
          )
        `
        )
        .eq("id", id)
        .is("deleted_at", null)
        .single();

      if (error) {
        return {
          data: null,
          error: {
            message: error.message,
            code: error.code === "PGRST116" ? "NOT_FOUND" : error.code,
          },
        };
      }

      if (!data) {
        return {
          data: null,
          error: {
            message: "Employee not found",
            code: "NOT_FOUND",
          },
        };
      }

      const userProfile = data.user_profiles || {};
      const authUser = authUserMap.get(userProfile.user_id);

      // Process and flatten the data to match getAll response structure
      const processedData = {
        id: data.id,
        email: authUser?.email || null,
        profile: {
          fullname: userProfile.fullname,
          phonenum: userProfile.phonenum,
          role: userProfile.role,
        },
        profile_id: data.profile_id,
        dob: data.dob,
        address: data.address,
        bank_account: data.bank_account,
        bank_name: data.bank_name,
        employment_status: data.employment_status,
        presence_status: data.presence_status,
        department: data.department,
        start_date: data.start_date,
        salary_id: data.salary_id,
        created_at: data.created_at,
        created_by: data.created_by,
        updated_at: data.updated_at,
        updated_by: data.updated_by,
        deleted_at: data.deleted_at,
        deleted_by: data.deleted_by,
      };

      return { data: processedData, error: null };
    } catch (error) {
      console.error("Error in getById:", error);
      return {
        data: null,
        error: {
          message:
            error instanceof Error ? error.message : "Unknown error occurred",
          code: "INTERNAL_ERROR",
        },
      };
    }
  }

  /**
   * Create a new employee
   * @param data The employee data to create
   * @param userId The user ID for audit trail
   * @returns The created employee or error
   */
  static async create(data: CreateEmployeeDto, userId: string) {
    return dbUtils.create<Employee>(this.TABLE_NAME, data, userId);
  }

  /**
   * Create an empty employee record for a new employee user
   * @param userId The auth user ID (used for audit trail)
   * @param profileId The profile ID to link to this employee
   * @returns The created employee or error
   */
  static async createEmpty(userId: string, profileId: string) {
    const today = new Date().toISOString().split("T")[0];

    const emptyEmployee: CreateEmployeeDto = {
      profile_id: profileId,
      dob: "1990-01-01", // Default placeholder
      address: "Not provided",
      bank_account: "Not provided",
      bank_name: "Not provided",
      employment_status: EmploymentStatus.NOT_PROVIDED,
      presence_status: PresenceStatus.PRESENT,
      department: "Pending Assignment",
      start_date: today,
    };

    return this.create(emptyEmployee, userId);
  }

  /**
   * Update employee information
   * @param id The employee ID
   * @param data The data to update
   * @param userId The user ID for audit trail
   * @returns The updated employee or error
   */
  static async update(id: string, data: UpdateEmployeeDto, userId: string) {
    try {
      // First check if the employee exists
      const checkResult = await this.getById(id);
      if (checkResult.error || !checkResult.data) {
        return {
          data: null,
          error: {
            message: checkResult.error?.message || "Employee not found",
            code: checkResult.error?.code || "NOT_FOUND",
          },
        };
      }

      // Employee exists, proceed with update
      const result = await dbUtils.update<Employee>(
        this.TABLE_NAME,
        id,
        data,
        userId
      );
      return result;
    } catch (error) {
      console.error("Error updating employee:", error);
      return {
        data: null,
        error: {
          message:
            error instanceof Error ? error.message : "Error updating employee",
          code: "INTERNAL_ERROR",
        },
      };
    }
  }

  /**
   * Get an employee by profile ID
   * @param profileId The profile ID
   * @returns The employee or error
   */
  static async getByProfileId(profileId: string) {
    try {
      // Get auth user data first (needed for email)
      const { data: authData, error: authError } =
        await supabase.auth.admin.listUsers();

      if (authError) {
        console.error("Error fetching auth users:", authError);
        return {
          data: null,
          error: {
            message: "Failed to fetch auth user data",
            code: authError.code,
          },
        };
      }

      // Create a map of user_id -> auth user data
      const authUserMap = new Map<string, any>();
      for (const user of authData.users) {
        if (user.id) {
          authUserMap.set(user.id, user);
        }
      }

      const { data, error } = await supabase
        .from("employees")
        .select(
          `
          *,
          user_profiles!profile_id (
            fullname,
            phonenum,
            role,
            user_id
          )
        `
        )
        .eq("profile_id", profileId)
        .is("deleted_at", null)
        .single();

      if (error) {
        return {
          data: null,
          error: {
            message: error.message,
            code: error.code === "PGRST116" ? "NOT_FOUND" : error.code,
          },
        };
      }

      if (!data) {
        return {
          data: null,
          error: {
            message: "Employee not found",
            code: "NOT_FOUND",
          },
        };
      }

      const userProfile = data.user_profiles || {};
      const authUser = authUserMap.get(userProfile.user_id);

      // Process and flatten the data to match getById response structure
      const processedData = {
        id: data.id,
        email: authUser?.email || null,
        profile: {
          fullname: userProfile.fullname,
          phonenum: userProfile.phonenum,
          role: userProfile.role,
        },
        profile_id: data.profile_id,
        dob: data.dob,
        address: data.address,
        bank_account: data.bank_account,
        bank_name: data.bank_name,
        employment_status: data.employment_status,
        presence_status: data.presence_status,
        department: data.department,
        start_date: data.start_date,
        salary_id: data.salary_id,
        created_at: data.created_at,
        created_by: data.created_by,
        updated_at: data.updated_at,
        updated_by: data.updated_by,
        deleted_at: data.deleted_at,
        deleted_by: data.deleted_by,
      };

      return { data: processedData, error: null };
    } catch (error) {
      console.error("Error in getByProfileId:", error);
      return {
        data: null,
        error: {
          message:
            error instanceof Error ? error.message : "Unknown error occurred",
          code: "INTERNAL_ERROR",
        },
      };
    }
  }

  /**
   * Check if an employee has completed their profile
   * @param id The employee ID or profile ID
   * @param isProfileId Whether the provided ID is a profile ID
   * @returns Object with isComplete status and employee data if exists
   */
  static async isProfileComplete(id: string, isProfileId: boolean = false) {
    try {
      // Get employee data based on ID type
      const response = isProfileId
        ? await this.getByProfileId(id)
        : await this.getById(id);

      const { data: employee, error } = response;

      if (error || !employee) {
        return { isComplete: false, employee: null, error };
      }

      // Check if the four required fields are populated with real values
      const incompleteFields = [];

      if (!employee.address || employee.address === "Not provided") {
        incompleteFields.push("address");
      }
      if (!employee.bank_account || employee.bank_account === "Not provided") {
        incompleteFields.push("bank_account");
      }
      if (!employee.bank_name || employee.bank_name === "Not provided") {
        incompleteFields.push("bank_name");
      }
      if (!employee.dob || employee.dob === "1990-01-01") {
        incompleteFields.push("dob");
      }

      return {
        isComplete: incompleteFields.length === 0,
        employee,
        incompleteFields:
          incompleteFields.length > 0 ? incompleteFields : undefined,
        error: null,
      };
    } catch (error) {
      console.error("Error in isProfileComplete:", error);
      return {
        isComplete: false,
        employee: null,
        error: {
          message:
            error instanceof Error ? error.message : "Unknown error occurred",
          code: "INTERNAL_ERROR",
        },
      };
    }
  }
}
