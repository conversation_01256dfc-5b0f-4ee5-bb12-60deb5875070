import { InvoicePaymentProofService } from "./service";
import { InvoiceService } from "../invoice/service";
import { PaymentStatus } from "../../database/models/invoice.model";
import { InvoiceUpdateHistoryService } from "../invoice/invoice-update-history.service";

export class InvoicePaymentProofController {
  /**
   * Upload a payment proof for an invoice
   */
  static async upload(context: any) {
    const { params, body, user, success, serverError, badRequest, notFound } =
      context;
    const { id } = params;
    const { file, notes } = body;

    // Validate file
    if (!file) {
      return badRequest("No file uploaded", "FILE_REQUIRED");
    }

    // Check if invoice exists
    const { data: invoice, error: invoiceError } = await InvoiceService.getById(
      id
    );

    if (invoiceError) {
      return serverError("Error fetching invoice", invoiceError);
    }

    if (!invoice) {
      return notFound("Invoice not found", "INVOICE_NOT_FOUND");
    }

    // Upload file and create payment proof record
    const { data, error } = await InvoicePaymentProofService.uploadAndCreate(
      file,
      id,
      notes || null,
      user.id
    );

    if (error) {
      return serverError("Failed to upload payment proof", error);
    }

    // Track the upload in history
    try {
      if (data) {
        await InvoiceUpdateHistoryService.trackPaymentProofUpload(
          id,
          data.file_name,
          user.id
        );
      }
    } catch (historyError) {
      console.error(
        "Failed to track payment proof upload in history:",
        historyError
      );
      // Continue with the response even if history tracking fails
    }

    return success(data, "Payment proof uploaded successfully");
  }

  /**
   * Get all payment proofs for an invoice
   */
  static async getByInvoiceId(context: any) {
    const { params, query, success, serverError, notFound } = context;
    const { id } = params;

    // Check if invoice exists
    const { data: invoice, error: invoiceError } = await InvoiceService.getById(
      id
    );

    if (invoiceError) {
      return serverError("Error fetching invoice", invoiceError);
    }

    if (!invoice) {
      return notFound("Invoice not found", "INVOICE_NOT_FOUND");
    }

    // Parse pagination options
    const page = parseInt(query.page) || 1;
    const pageSize = parseInt(query.pageSize) || 10;

    // Get payment proofs
    const { data, error } = await InvoicePaymentProofService.getByInvoiceId(
      id,
      {
        pagination: { page, pageSize },
        sort: { field: "created_at", direction: "desc" },
      }
    );

    if (error) {
      return serverError("Failed to fetch payment proofs", error);
    }

    // Log the response structure to help with debugging
    console.log(`Payment proofs response structure:`, {
      dataType: typeof data,
      isArray: Array.isArray(data),
      hasItems: data && typeof data === "object" && "items" in data,
      dataLength: Array.isArray(data) ? data.length : "not an array",
    });

    // Return the data directly - it's already an array of payment proofs with download URLs
    return success(data, "Payment proofs retrieved successfully");
  }

  /**
   * Delete a payment proof
   */
  static async delete(context: any) {
    const { params, user, success, serverError, notFound } = context;
    const { id, proofId } = params;

    // Check if invoice exists
    const { data: invoice, error: invoiceError } = await InvoiceService.getById(
      id
    );

    if (invoiceError) {
      return serverError("Error fetching invoice", invoiceError);
    }

    if (!invoice) {
      return notFound("Invoice not found", "INVOICE_NOT_FOUND");
    }

    // Delete payment proof
    const { data, error } = await InvoicePaymentProofService.delete(
      proofId,
      user.id
    );

    if (error) {
      return serverError("Failed to delete payment proof", error);
    }

    // Track the deletion in history
    try {
      if (data) {
        await InvoiceUpdateHistoryService.trackPaymentProofDelete(
          id,
          data.file_name,
          user.id
        );
      }
    } catch (historyError) {
      console.error(
        "Failed to track payment proof deletion in history:",
        historyError
      );
      // Continue with the response even if history tracking fails
    }

    return success(null, "Payment proof deleted successfully");
  }
}
