import { <PERSON><PERSON> } from "elysia";
import { invoicePaymentProofRoutes } from "./routes";
import { InvoicePaymentProofService } from "./service";
import { InvoicePaymentProofController } from "./controller";
import { apiResponse } from "../../middleware/api-response";

// Create an instance with the middleware applied
const invoicePaymentProofModule = new Elysia()
  .use(apiResponse)
  .use(invoicePaymentProofRoutes);

// Re-export for convenience
export { InvoicePaymentProofService, InvoicePaymentProofController };

// Export the module
export default invoicePaymentProofModule;
