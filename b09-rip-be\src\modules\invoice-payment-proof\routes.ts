import { <PERSON>sia, t } from "elysia";
import { InvoicePaymentProofController } from "./controller";
import { requireActiveUser, checkRoles } from "../../middleware/auth";
import { UserRole } from "../../database/models/user-profile.model";

export const invoicePaymentProofRoutes = (app: Elysia) =>
  app.group("/invoices", (app) =>
    app
      // First ensure users are active
      .use(requireActiveUser)

      // Upload a payment proof for an invoice
      .post("/:id/payment-proofs", InvoicePaymentProofController.upload, {
        beforeHandle: [
          checkRoles([UserRole.Admin, UserRole.Finance, UserRole.Manager]),
        ],
        body: t.Object({
          file: t.File(),
          notes: t.Optional(t.String()),
        }),
        detail: {
          tags: ["invoices"],
          summary: "Upload a payment proof for an invoice",
          description: "Upload a payment proof document for an invoice",
          security: [{ bearerAuth: [] }],
        },
      })

      // Get all payment proofs for an invoice
      .get(
        "/:id/payment-proofs",
        InvoicePaymentProofController.getByInvoiceId,
        {
          beforeHandle: [
            checkRoles([UserRole.Admin, UserRole.Finance, UserRole.Manager]),
          ],
          detail: {
            tags: ["invoices"],
            summary: "Get all payment proofs for an invoice",
            description: "Get all payment proof documents for an invoice",
            security: [{ bearerAuth: [] }],
          },
        }
      )

      // Delete a payment proof
      .delete(
        "/:id/payment-proofs/:proofId",
        InvoicePaymentProofController.delete,
        {
          beforeHandle: [
            checkRoles([UserRole.Admin, UserRole.Finance, UserRole.Manager]),
          ],
          detail: {
            tags: ["invoices"],
            summary: "Delete a payment proof",
            description: "Delete a payment proof document",
            security: [{ bearerAuth: [] }],
          },
        }
      )
  );

export default invoicePaymentProofRoutes;
