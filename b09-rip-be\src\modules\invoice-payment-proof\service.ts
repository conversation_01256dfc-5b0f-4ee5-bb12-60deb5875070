import { dbUtils } from "../../utils/database";
import {
  CreateInvoicePaymentProofDto,
  InvoicePaymentProof,
} from "../../database/models/invoice-payment-proof.model";
import { QueryOptions } from "../../utils/database.types";
import { supabase } from "../../libs/supabase";

export class InvoicePaymentProofService {
  private static readonly TABLE_NAME = "invoice_payment_proofs";
  private static readonly STORAGE_BUCKET = "invoice-payment-proofs";

  /**
   * Upload a file to Supabase Storage and create a payment proof record
   * @param file The file to upload
   * @param invoiceId The ID of the invoice
   * @param notes Optional notes about the payment proof
   * @param userId The ID of the user creating the record
   */
  static async uploadAndCreate(
    file: File,
    invoiceId: string,
    notes: string | null,
    userId: string
  ) {
    try {
      // Generate a unique file path to avoid collisions
      const timestamp = new Date().getTime();
      const filePath = `${invoiceId}/${timestamp}_${file.name}`;

      // Upload the file to Supabase Storage
      const { data: storageData, error: storageError } = await supabase.storage
        .from(this.STORAGE_BUCKET)
        .upload(filePath, file, {
          cacheControl: "3600",
          upsert: false,
        });

      if (storageError) {
        return { data: null, error: storageError };
      }

      // Create the payment proof record in the database
      const paymentProofData: CreateInvoicePaymentProofDto = {
        invoice_id: invoiceId,
        file_path: filePath,
        file_name: file.name,
        file_type: file.type,
        file_size: file.size,
        notes: notes,
      };

      return await dbUtils.create<InvoicePaymentProof>(
        this.TABLE_NAME,
        paymentProofData,
        userId
      );
    } catch (error) {
      return {
        data: null,
        error:
          error instanceof Error ? error : new Error("Unknown error occurred"),
      };
    }
  }

  /**
   * Get all payment proofs for an invoice
   * @param invoiceId The ID of the invoice
   * @param options Query options
   */
  static async getByInvoiceId(invoiceId: string, options: QueryOptions = {}) {
    const filterOptions = [
      ...(options.filters || []),
      { field: "invoice_id", value: invoiceId },
    ];

    const {
      data: records,
      error,
      result,
    } = await dbUtils.getAll<InvoicePaymentProof>(this.TABLE_NAME, {
      ...options,
      filters: filterOptions,
    });

    if (error) {
      return { data: null, error };
    }

    // Handle case where no records are found
    if (!records || records.length === 0) {
      console.log(`No payment proofs found for invoice ${invoiceId}`);
      return { data: [], error: null };
    }

    console.log(
      `Found ${records.length} payment proofs for invoice ${invoiceId}`
    );

    // Generate signed URLs for each payment proof
    try {
      const itemsWithUrls = await Promise.all(
        records.map(async (item) => {
          console.log(`Generating signed URL for file: ${item.file_path}`);

          try {
            const { data: urlData, error: urlError } = await supabase.storage
              .from(this.STORAGE_BUCKET)
              .createSignedUrl(item.file_path, 60 * 60); // 1 hour expiry

            if (urlError) {
              console.error(
                `Error generating signed URL for ${item.file_path}:`,
                urlError
              );

              // Try to get a public URL as fallback
              const publicUrl = supabase.storage
                .from(this.STORAGE_BUCKET)
                .getPublicUrl(item.file_path);

              console.log(
                `Fallback public URL: ${publicUrl?.data?.publicUrl || "null"}`
              );

              return {
                ...item,
                download_url: publicUrl?.data?.publicUrl || null,
              };
            } else {
              console.log(
                `Generated signed URL: ${urlData?.signedUrl || "null"}`
              );

              return {
                ...item,
                download_url: urlData?.signedUrl || null,
              };
            }
          } catch (err) {
            console.error(
              `Exception generating URL for ${item.file_path}:`,
              err
            );
            return {
              ...item,
              download_url: null,
            };
          }
        })
      );

      // Return the array of items with download URLs
      return {
        data: itemsWithUrls,
        error: null,
      };
    } catch (error) {
      console.error("Error generating URLs:", error);
      // Return the original records without download URLs as fallback
      return { data: records, error: null };
    }
  }

  /**
   * Delete a payment proof (hard delete)
   * @param id The ID of the payment proof
   * @param userId The ID of the user deleting the record (for audit purposes only)
   */
  static async delete(id: string, userId: string) {
    // First get the payment proof to get the file path
    const { data: paymentProof, error: getError } =
      await dbUtils.getById<InvoicePaymentProof>(this.TABLE_NAME, id);

    if (getError || !paymentProof) {
      return {
        data: null,
        error: getError || new Error("Payment proof not found"),
      };
    }

    // Delete the file from storage
    const { error: storageError } = await supabase.storage
      .from(this.STORAGE_BUCKET)
      .remove([paymentProof.file_path]);

    if (storageError) {
      return { data: null, error: storageError };
    }

    // Hard delete the record from the database
    const { data, error: deleteError } = await dbUtils.hardDelete(
      this.TABLE_NAME,
      id
    );

    if (deleteError) {
      return { data: null, error: deleteError };
    }

    // Return the original payment proof data for consistency
    return { data: paymentProof, error: null };
  }
}
