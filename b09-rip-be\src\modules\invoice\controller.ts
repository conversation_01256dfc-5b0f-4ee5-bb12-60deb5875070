import { InvoiceService } from "./service";
import {
  CreateInvoiceDto,
  ServiceType,
  UpdateInvoiceDto,
} from "../../database/models/invoice.model";
import { AuthUser } from "../../middleware/auth";
import { FilterOption, QueryOptions } from "../../utils/database.types";
import { InvoiceUpdateHistoryService } from "./invoice-update-history.service";
import { parseChangeDescription } from "../../database/models/invoice-update-history.model";

/**
 * Converts a 0-indexed month number (0-11) to Roman numeral
 * For example: 0 -> I (January), 11 -> XII (December)
 */
function monthToRoman(month: number): string {
  const romanNumerals = [
    "I",
    "II",
    "III",
    "IV",
    "V",
    "VI",
    "VII",
    "VIII",
    "IX",
    "X",
    "XI",
    "XII",
  ];

  // Validate month is in valid range
  if (month < 0 || month > 11) {
    throw new Error(
      `Invalid month index: ${month}. Month must be between 0-11`
    );
  }

  return romanNumerals[month];
}

/**
 * Generates invoice number in the format: Number/ServiceType/Kasuat/Month(Roman)/Year
 * Example: 007/HCM/Kasuat/II/2025
 */
async function generateInvoiceNumber(
  serviceType: ServiceType
): Promise<string> {
  const currentDate = new Date();
  const year = currentDate.getFullYear();
  const month = currentDate.getMonth(); // 0-indexed (0 = January)

  // Get the current count of invoices for sequential numbering
  const { data: latestInvoices } = await InvoiceService.getLatestInvoices(3);

  // Determine the sequence number (default to 1 if no invoices exist)
  let sequenceNumber = 1;
  if (latestInvoices && latestInvoices.length > 0) {
    // Try to extract the sequence number from the latest invoice
    for (const invoice of latestInvoices) {
      if (invoice.invoice_number) {
        const match = invoice.invoice_number.match(/^(\d+)\//);
        if (match) {
          const lastNumber = parseInt(match[1], 10);
          if (!isNaN(lastNumber) && lastNumber >= sequenceNumber) {
            sequenceNumber = lastNumber + 1;
          }
        }
      }
    }
  }

  // Format the sequence number with leading zeros (e.g., 007)
  const formattedSequence = String(sequenceNumber).padStart(3, "0");
  const romanMonth = monthToRoman(month);

  // Construct the invoice number
  return `${formattedSequence}/${serviceType}/Kasuat/${romanMonth}/${year}`;
}

/**
 * Helper function to ensure API response functions are available
 */
function ensureResponseFunctions(context: any) {
  // Check if the basic response functions are available
  if (typeof context.success !== "function") {
    console.error("API Response middleware functions not available in context");
    // Provide fallback response if middleware functions aren't available
    return {
      success: (data: any, message = "Operation successful") => ({
        success: true,
        message,
        data,
      }),
      forbidden: (message = "Forbidden", errorCode?: string) => ({
        success: false,
        message,
        data: null,
        error: { code: errorCode || "FORBIDDEN" },
      }),
      unauthorized: (message = "Unauthorized", errorCode?: string) => ({
        success: false,
        message,
        data: null,
        error: { code: errorCode || "UNAUTHORIZED" },
      }),
      notFound: (message = "Not found", errorCode?: string) => ({
        success: false,
        message,
        data: null,
        error: { code: errorCode || "NOT_FOUND" },
      }),
      serverError: (message = "Server error", error?: Error) => ({
        success: false,
        message,
        data: null,
        error: {
          code: "INTERNAL_SERVER_ERROR",
          details: error ? { stack: error.stack } : undefined,
        },
      }),
      badRequest: (message = "Bad request", errorCode?: string) => ({
        success: false,
        message,
        data: null,
        error: { code: errorCode || "BAD_REQUEST" },
      }),
    };
  }

  // If functions are available, return the original context
  return context;
}

export class InvoiceController {
  /**
   * Get all invoices with search, filter, and pagination
   */
  static async getAll(context: any) {
    const {
      query = {},
      success,
      serverError,
    } = ensureResponseFunctions(context);

    try {
      // Build query options from request parameters
      const options: QueryOptions = {};

      // Handle search
      if (query.search) {
        options.search = {
          term: query.search,
          fields: ["invoice_number", "recipient_name", "project_name", "notes"], // Searchable fields
        };
      }

      // Handle filters
      const filters: FilterOption[] = [];

      // Filter by payment_status if provided
      if (query.payment_status) {
        filters.push({ field: "payment_status", value: query.payment_status });
      }

      if (filters.length > 0) {
        options.filters = filters;
      }

      // Always apply pagination, with default values if not provided
      options.pagination = {
        page: query.page ? parseInt(query.page, 10) : 1,
        pageSize: query.pageSize ? parseInt(query.pageSize, 10) : 10,
      };

      // Call the service with the constructed options
      const { data, error, result } = await InvoiceService.getAll(options);

      if (error) {
        return serverError(error.message, error);
      }

      return success(
        {
          items: data,
          pagination: result,
        },
        "Invoices retrieved successfully"
      );
    } catch (error: any) {
      console.error("Error fetching invoices:", error);
      return serverError(error.message || "Failed to fetch invoices", error);
    }
  }

  /**
   * Get invoice by ID
   */
  static async getById(context: any) {
    const { params, success, notFound, serverError } =
      ensureResponseFunctions(context);
    const { id } = params;

    try {
      const { data, error } = await InvoiceService.getById(id);

      if (error) {
        return serverError(error.message, error);
      }

      if (!data) {
        return notFound(`Invoice with ID ${id} not found`);
      }

      return success(data, "Invoice retrieved successfully");
    } catch (error: any) {
      console.error("Error fetching invoice:", error);
      return serverError(error.message || "Failed to fetch invoice", error);
    }
  }
  /**
   * Update an existing invoice
   */
  static async update(context: any) {
    // Ensure the response functions are available and get user
    const { body, user, params, success, notFound, serverError, badRequest } =
      ensureResponseFunctions(context);

    try {
      // First, check if the invoice exists
      const { data: existingInvoice, error: fetchError } =
        await InvoiceService.getById(params.id);

      if (fetchError) {
        return serverError(fetchError.message, fetchError);
      }

      if (!existingInvoice) {
        return notFound(`Invoice with ID ${params.id} not found`);
      }

      // Process items if provided
      let items;
      let totalAmount = existingInvoice.total_amount; // Default to current total

      if (body.items && body.items.length > 0) {
        // Validate items
        for (const item of body.items) {
          if (item.id) {
            // For existing items, ensure ID is a valid UUID
            if (
              !item.id.match(
                /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i
              )
            ) {
              return badRequest(
                `Invalid item ID format: ${item.id}`,
                "INVALID_ITEM_ID"
              );
            }
          } else {
            // For new items, ensure required fields are present
            if (
              !item.item_name ||
              item.item_amount === undefined ||
              item.item_price === undefined
            ) {
              return badRequest(
                "New items must include name, amount, and price",
                "MISSING_ITEM_FIELDS"
              );
            }

            // Validate numeric fields
            if (typeof item.item_amount !== "number" || item.item_amount <= 0) {
              return badRequest(
                "Item amount must be a positive number",
                "INVALID_ITEM_AMOUNT"
              );
            }

            if (typeof item.item_price !== "number" || item.item_price < 0) {
              return badRequest(
                "Item price must be a non-negative number",
                "INVALID_ITEM_PRICE"
              );
            }
          }
        }

        // Process items for the database
        items = body.items.map(
          (item: {
            id?: string;
            item_name?: string;
            item_amount?: number;
            item_price?: number;
          }) => {
            return {
              id: item.id, // Include ID for existing items
              item_name: item.item_name,
              item_amount: item.item_amount,
              item_price: item.item_price,
              // We'll let the service calculate this based on the new values
            };
          }
        );

        // Calculate the new total amount if items were provided
        totalAmount = items.reduce(
          (
            acc: number,
            item: {
              item_amount?: number;
              item_price?: number;
            }
          ) => {
            const amount = item.item_amount ?? 0;
            const price = item.item_price ?? 0;
            return acc + amount * price;
          },
          0
        );
      }

      // Format due date as YYYY-MM-DD if provided
      let formattedDueDate;
      if (body.due_date) {
        if (body.due_date.match(/^\d{4}-\d{2}-\d{2}$/)) {
          formattedDueDate = body.due_date;
        } else {
          try {
            formattedDueDate = new Date(body.due_date)
              .toISOString()
              .split("T")[0];
          } catch (error) {
            return badRequest(
              "Invalid due date format. Expected YYYY-MM-DD",
              "INVALID_DATE_FORMAT"
            );
          }
        }
      }

      // Prepare the update DTO
      const dto: UpdateInvoiceDto = {
        // Only include fields that are provided in the request body
        ...(body.invoice_type !== undefined && {
          invoice_type: body.invoice_type,
        }),
        ...(body.service_type !== undefined && {
          service_type: body.service_type,
        }),
        ...(body.recipient_name !== undefined && {
          recipient_name: body.recipient_name,
        }),
        ...(body.project_id !== undefined && { project_id: body.project_id }),
        ...(body.project_name !== undefined && {
          project_name: body.project_name,
        }),
        ...(formattedDueDate && { due_date: formattedDueDate }),
        ...(body.payment_method !== undefined && {
          payment_method: body.payment_method,
        }),
        ...(body.payment_status !== undefined && {
          payment_status: body.payment_status,
        }),
        ...(body.notes !== undefined && { notes: body.notes }),
        ...(totalAmount !== existingInvoice.total_amount && {
          total_amount: totalAmount,
        }),
        ...(items && { items: items }),
      };

      const { data, error } = await InvoiceService.update(
        params.id,
        dto,
        user.id
      );

      if (error) {
        return serverError(error.message, error);
      }

      return success(data, "Invoice updated successfully");
    } catch (error: any) {
      console.error("Error updating invoice:", error);
      return serverError(error.message || "Failed to update invoice", error);
    }
  }
  /**
   * Get update history for an invoice
   */
  static async getHistory(context: any) {
    const {
      params,
      query = {},
      success,
      notFound,
      serverError,
    } = ensureResponseFunctions(context);
    const { id } = params;

    try {
      // First check if the invoice exists
      const { data: existingInvoice, error: fetchError } =
        await InvoiceService.getById(id);

      if (fetchError) {
        return serverError(fetchError.message, fetchError);
      }

      if (!existingInvoice) {
        return notFound(`Invoice with ID ${id} not found`);
      }

      // Build query options from request parameters
      const options: QueryOptions = {};

      // Always apply pagination, with default values if not provided
      options.pagination = {
        page: query.page ? parseInt(query.page, 10) : 1,
        pageSize: query.pageSize ? parseInt(query.pageSize, 10) : 10,
      };

      // Get the history records
      const { data, error, result } =
        await InvoiceUpdateHistoryService.getByInvoiceId(id, options);

      if (error) {
        return serverError(error.message, error);
      }

      // Parse the change descriptions for easier client-side use
      const parsedHistory = data
        ? data.map((record) => ({
            ...record,
            parsed_changes: parseChangeDescription(record.change_description),
          }))
        : [];

      return success(
        {
          items: parsedHistory,
          pagination: result,
        },
        "Invoice history retrieved successfully"
      );
    } catch (error: any) {
      console.error("Error fetching invoice history:", error);
      return serverError(
        error.message || "Failed to fetch invoice history",
        error
      );
    }
  }

  /**
   * Delete an invoice by ID
   */
  static async delete(context: any) {
    const { params, user, success, notFound, serverError } =
      ensureResponseFunctions(context);
    const { id } = params;

    try {
      // First check if the invoice exists
      const { data: existingInvoice, error: fetchError } =
        await InvoiceService.getById(id);

      if (fetchError) {
        return serverError(fetchError.message, fetchError);
      }

      if (!existingInvoice) {
        return notFound(`Invoice with ID ${id} not found`);
      }

      // Delete the invoice and its items
      const { error: deleteError } = await InvoiceService.delete(id, user.id);

      if (deleteError) {
        return serverError(deleteError.message, deleteError);
      }

      return success(null, `Invoice with ID ${id} successfully deleted`);
    } catch (error: any) {
      console.error("Error deleting invoice:", error);
      return serverError(error.message || "Failed to delete invoice", error);
    }
  }

  /**
   * Create a new invoice
   */
  static async create(context: any) {
    // Ensure the response functions are available and get user
    const { body, user, success, serverError, badRequest } =
      ensureResponseFunctions(context);

    try {
      // Calculate total price for each item
      const items = body.items.map(
        (item: {
          item_name: string;
          item_amount: number;
          item_price: number;
        }) => ({
          item_name: item.item_name,
          item_amount: item.item_amount,
          item_price: item.item_price,
          // Calculate total price on the server side
          total_price: item.item_amount * item.item_price,
        })
      );

      // Calculate the total amount from the items
      const totalAmount = items.reduce(
        (acc: number, item: { total_price: number }) => acc + item.total_price,
        0
      );

      // Generate invoice number
      const invoiceNumber = await generateInvoiceNumber(body.service_type);

      // Format due date as YYYY-MM-DD
      let formattedDueDate = body.due_date;
      // Ensure the date is in YYYY-MM-DD format
      if (!formattedDueDate.match(/^\d{4}-\d{2}-\d{2}$/)) {
        try {
          formattedDueDate = new Date(body.due_date)
            .toISOString()
            .split("T")[0];
        } catch (error) {
          return badRequest(
            "Invalid due date format. Expected YYYY-MM-DD",
            "INVALID_DATE_FORMAT"
          );
        }
      }

      const dto: CreateInvoiceDto = {
        invoice_number: invoiceNumber,
        invoice_type: body.invoice_type,
        service_type: body.service_type,
        recipient_name: body.recipient_name,
        project_id: body.project_id,
        project_name: body.project_name,
        due_date: formattedDueDate,
        payment_method: body.payment_method,
        payment_status: body.payment_status,
        notes: body.notes,
        total_amount: totalAmount,
        items: items,
      };

      const { data, error } = await InvoiceService.create(dto, user.id);

      if (error) {
        return serverError(error.message, error);
      }

      // Add back the service_type to the response
      const responseData = {
        ...data,
        service_type: body.service_type,
      };

      return success(responseData, "Invoice created successfully");
    } catch (error: any) {
      console.error("Error creating invoice:", error);
      return serverError(error.message || "Failed to create invoice", error);
    }
  }
}
