import { dbUtils } from "../../utils/database";
import {
  CreateInvoiceUpdateHistoryDto,
  InvoiceChangeItem,
  InvoiceUpdateHistory,
  createChangeDescription,
  parseChangeDescription,
} from "../../database/models/invoice-update-history.model";
import { Invoice, UpdateInvoiceDto } from "../../database/models/invoice.model";
import { QueryOptions } from "../../utils/database.types";

export class InvoiceUpdateHistoryService {
  private static readonly TABLE_NAME = "invoice_update_history";

  /**
   * Create a new invoice update history record
   * @param data History data to insert
   * @param userId ID of the user making the change
   * @returns The created history record and any error
   */
  static async create(data: CreateInvoiceUpdateHistoryDto, userId: string) {
    return dbUtils.create<InvoiceUpdateHistory>(this.TABLE_NAME, data, userId);
  }

  /**
   * Get all history records for a specific invoice
   * @param invoiceId ID of the invoice to get history for
   * @param options Query options including pagination
   * @returns History records, pagination data, and any error
   */
  static async getByInvoiceId(invoiceId: string, options: QueryOptions = {}) {
    // Add filter for invoice_id
    const filters = [
      ...(options.filters || []),
      { field: "invoice_id", value: invoiceId },
    ];

    // Set default sort to created_at ascending if no sort option is provided
    const updatedOptions = {
      ...options,
      filters,
      // Only set sort if not already provided in options
      sort: options.sort || { field: "created_at", direction: "asc" },
    };

    // Return with filters and sort applied
    return dbUtils.getAll<InvoiceUpdateHistory>(
      this.TABLE_NAME,
      updatedOptions
    );
  }

  /**
   * Compare original invoice with updated data to generate change items
   * @param originalInvoice The original invoice before update
   * @param updateData The data used to update the invoice
   * @returns Array of change items or null if no changes
   */
  static compareInvoiceChanges(
    originalInvoice: Invoice,
    updateData: UpdateInvoiceDto
  ): InvoiceChangeItem[] | null {
    const changes: InvoiceChangeItem[] = [];

    // Check each field in updateData and compare with original
    Object.entries(updateData).forEach(([key, newValue]) => {
      // Skip items array as it's handled separately
      if (key === "items") return;

      // Get the original value
      const originalValue = originalInvoice[key as keyof Invoice];

      // Only add to changes if the value is different
      if (newValue !== undefined && newValue !== originalValue) {
        changes.push({
          field: key,
          from_value: originalValue,
          to_value: newValue,
        });
      }
    });

    // Return null if no changes were found
    return changes.length > 0 ? changes : null;
  }

  /**
   * Create a history record for invoice update if there are changes
   * @param invoiceId ID of the invoice being updated
   * @param originalInvoice The original invoice before update
   * @param updateData The data used to update the invoice
   * @param userId ID of the user making the update
   * @returns The created history record and any error, or null if no changes
   */
  static async trackInvoiceUpdate(
    invoiceId: string,
    originalInvoice: Invoice,
    updateData: UpdateInvoiceDto,
    userId: string
  ) {
    try {
      // Compare changes
      const changes = this.compareInvoiceChanges(originalInvoice, updateData);

      // If no changes, return null
      if (!changes) {
        return { data: null, error: null };
      }

      // Create history record
      const historyData: CreateInvoiceUpdateHistoryDto = {
        invoice_id: invoiceId,
        change_description: createChangeDescription(changes),
      };

      return this.create(historyData, userId);
    } catch (error) {
      console.error(
        `Error tracking invoice update for invoice ${invoiceId}:`,
        error
      );
      return {
        data: null,
        error: new Error(
          `Failed to track invoice update: ${
            error instanceof Error ? error.message : String(error)
          }`
        ),
      };
    }
  }

  /**
   * Parse change descriptions in history records for display
   * @param historyRecords Array of history records
   * @returns Array of history records with parsed change descriptions
   */
  static parseHistoryRecords(historyRecords: InvoiceUpdateHistory[]) {
    return historyRecords.map((record) => ({
      ...record,
      parsed_changes: parseChangeDescription(record.change_description),
    }));
  }

  /**
   * Retry tracking invoice update history
   * This can be called manually or by a background job to fix failed history tracking
   * @param invoiceId ID of the invoice
   * @param userId ID of the user making the retry
   */
  static async retryTrackInvoiceUpdate(invoiceId: string, userId: string) {
    try {
      // Get the invoice
      const { data: invoice, error: fetchError } = await dbUtils
        .query("invoices")
        .raw.select()
        .eq("id", invoiceId)
        .is("deleted_at", null)
        .single();

      if (fetchError || !invoice) {
        return {
          data: null,
          error:
            fetchError || new Error(`Invoice with ID ${invoiceId} not found`),
        };
      }

      // Get the latest history record for this invoice
      const { data: historyRecords, error: historyError } =
        await this.getByInvoiceId(invoiceId, {
          pagination: { page: 1, pageSize: 1 },
          sort: { field: "created_at", direction: "desc" },
        });

      if (historyError) {
        return { data: null, error: historyError };
      }

      // If there's no history record, create one for the current state
      if (!historyRecords || historyRecords.length === 0) {
        // Create a history record with null previous values
        const changes: InvoiceChangeItem[] = Object.entries(invoice)
          .filter(
            ([key]) =>
              key !== "id" &&
              key !== "items" &&
              key !== "created_at" &&
              key !== "created_by" &&
              key !== "updated_at" &&
              key !== "updated_by" &&
              key !== "deleted_at" &&
              key !== "deleted_by"
          )
          .map(([key, value]) => ({
            field: key,
            from_value: null,
            to_value: value,
          }));

        const historyData: CreateInvoiceUpdateHistoryDto = {
          invoice_id: invoiceId,
          change_description: createChangeDescription(changes),
        };

        return this.create(historyData, userId);
      }

      return {
        data: null,
        error: new Error("Invoice already has history records"),
      };
    } catch (error) {
      console.error(
        `Error retrying history tracking for invoice ${invoiceId}:`,
        error
      );
      return {
        data: null,
        error: new Error(
          `Failed to retry history tracking: ${
            error instanceof Error ? error.message : String(error)
          }`
        ),
      };
    }
  }

  /**
   * Track payment proof upload in invoice history
   * @param invoiceId ID of the invoice
   * @param fileName Name of the uploaded file
   * @param userId ID of the user uploading the proof
   */
  static async trackPaymentProofUpload(
    invoiceId: string,
    fileName: string,
    userId: string
  ) {
    try {
      // Create a change item for the upload
      const change: InvoiceChangeItem = {
        field: "payment_proof_upload",
        from_value: null,
        to_value: fileName,
      };

      // Create history record
      const historyData: CreateInvoiceUpdateHistoryDto = {
        invoice_id: invoiceId,
        change_description: createChangeDescription([change]),
      };

      return this.create(historyData, userId);
    } catch (error) {
      console.error(
        `Error tracking payment proof upload for invoice ${invoiceId}:`,
        error
      );
      return {
        data: null,
        error: new Error(
          `Failed to track payment proof upload: ${
            error instanceof Error ? error.message : String(error)
          }`
        ),
      };
    }
  }

  /**
   * Track payment proof deletion in invoice history
   * @param invoiceId ID of the invoice
   * @param fileName Name of the deleted file
   * @param userId ID of the user deleting the proof
   */
  static async trackPaymentProofDelete(
    invoiceId: string,
    fileName: string,
    userId: string
  ) {
    try {
      // Create a change item for the deletion
      const change: InvoiceChangeItem = {
        field: "payment_proof_delete",
        from_value: fileName,
        to_value: null,
      };

      // Create history record
      const historyData: CreateInvoiceUpdateHistoryDto = {
        invoice_id: invoiceId,
        change_description: createChangeDescription([change]),
      };

      return this.create(historyData, userId);
    } catch (error) {
      console.error(
        `Error tracking payment proof deletion for invoice ${invoiceId}:`,
        error
      );
      return {
        data: null,
        error: new Error(
          `Failed to track payment proof deletion: ${
            error instanceof Error ? error.message : String(error)
          }`
        ),
      };
    }
  }
}
