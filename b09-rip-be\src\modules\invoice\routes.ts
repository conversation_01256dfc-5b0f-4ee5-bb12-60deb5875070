import { Ely<PERSON> } from "elysia";
import { InvoiceController } from "./controller";
import {
  createInvoiceSchema,
  updateInvoiceSchema,
  getInvoiceSchema,
  getAllInvoicesSchema,
  deleteInvoiceSchema,
  getInvoiceHistorySchema,
} from "./schema";
import { requireActiveUser, checkRoles } from "../../middleware/auth";
import { UserRole } from "../../database/models/user-profile.model";
import { apiResponse } from "../../middleware/api-response";
import { HttpResponses } from "../../middleware/api-response";

/**
 * Invoice routes
 */

export const invoiceRoutes = (app: Elysia) =>
  app.group("/invoices", (app) =>
    app
      // First ensure users are active
      .use(requireActiveUser)

      // Get all invoices with pagination, filtering and search
      .get("/", InvoiceController.getAll, {
        beforeHandle: [
          checkRoles([UserRole.Admin, UserRole.Manager, UserRole.Finance]),
        ],
        query: getAllInvoicesSchema.query,
        detail: {
          tags: ["invoices"],
          summary: "Get all invoices",
          description:
            "Retrieve all invoices with search, filter, and pagination. Returns a paginated list of invoices.",
          security: [{ bearerAuth: [] }],
          responses: {
            "200": {
              description: "Successfully retrieved invoices",
              content: {
                "application/json": {
                  schema: {
                    type: "object",
                    properties: {
                      success: { type: "boolean", example: true },
                      message: { type: "string" },
                      data: {
                        type: "object",
                        properties: {
                          items: {
                            type: "array",
                            items: {
                              $ref: "#/components/schemas/Invoice",
                            },
                          },
                          pagination: {
                            $ref: "#/components/schemas/PaginationResult",
                          },
                        },
                      },
                    },
                  },
                  examples: {
                    getInvoicesResponseExample: {
                      $ref: "#/components/examples/getInvoicesResponseExample",
                    },
                  },
                },
              },
            },
            "403": {
              description: "Forbidden - User does not have required role",
            },
          },
        },
      })
      // Get a single invoice by ID
      .get("/:id", InvoiceController.getById, {
        beforeHandle: [
          checkRoles([UserRole.Admin, UserRole.Manager, UserRole.Finance]),
        ],
        params: getInvoiceSchema.params,
        detail: {
          tags: ["invoices"],
          summary: "Get invoice by ID",
          description: "Retrieve a specific invoice by ID with its items",
          security: [{ bearerAuth: [] }],
          responses: {
            "200": {
              description: "Successfully retrieved invoice",
              content: {
                "application/json": {
                  examples: {
                    getInvoiceResponseExample: {
                      $ref: "#/components/examples/getInvoiceResponseExample",
                    },
                  },
                },
              },
            },
            "404": {
              description: "Invoice not found",
            },
          },
        },
      })

      // Get invoice update history
      .get("/:id/history", InvoiceController.getHistory, {
        beforeHandle: [
          checkRoles([UserRole.Admin, UserRole.Manager, UserRole.Finance]),
        ],
        params: getInvoiceHistorySchema.params,
        query: getInvoiceHistorySchema.query,
        detail: {
          tags: ["invoices"],
          summary: "Get invoice update history",
          description: "Retrieve the update history for a specific invoice",
          security: [{ bearerAuth: [] }],
          responses: {
            "200": {
              description: "Successfully retrieved invoice history",
              content: {
                "application/json": {
                  schema: {
                    type: "object",
                    properties: {
                      success: { type: "boolean", example: true },
                      message: { type: "string" },
                      data: {
                        type: "object",
                        properties: {
                          items: {
                            type: "array",
                            items: {
                              type: "object",
                              properties: {
                                id: { type: "string" },
                                invoice_id: { type: "string" },
                                change_description: { type: "string" },
                                parsed_changes: {
                                  type: "array",
                                  items: {
                                    type: "object",
                                    properties: {
                                      field: { type: "string" },
                                      from_value: { type: "string" },
                                      to_value: { type: "string" },
                                    },
                                  },
                                },
                                created_at: { type: "string" },
                                created_by: { type: "string" },
                              },
                            },
                          },
                          pagination: {
                            $ref: "#/components/schemas/PaginationResult",
                          },
                        },
                      },
                    },
                  },
                },
              },
            },
            "404": {
              description: "Invoice not found",
            },
          },
        },
      })

      // DELETE endpoint for deleting invoices
      .delete("/:id", InvoiceController.delete, {
        beforeHandle: [
          checkRoles([UserRole.Admin, UserRole.Manager, UserRole.Finance]),
        ],
        params: deleteInvoiceSchema.params,
        detail: {
          tags: ["invoices"],
          summary: "Delete invoice",
          description: "Delete an invoice and its items",
          security: [{ bearerAuth: [] }],
          responses: {
            "200": {
              description: "Successfully deleted invoice",
              content: {
                "application/json": {
                  examples: {
                    deleteInvoiceResponseExample: {
                      $ref: "#/components/examples/deleteInvoiceResponseExample",
                    },
                  },
                },
              },
            },
            "404": {
              description: "Invoice not found",
            },
          },
        },
      })
      .post("/", InvoiceController.create, {
        beforeHandle: [
          checkRoles([UserRole.Admin, UserRole.Manager, UserRole.Finance]),
        ],
        body: createInvoiceSchema.body,
        detail: {
          tags: ["invoices"],
          summary: "Create invoice",
          description: "Create a new invoice with items",
          security: [{ bearerAuth: [] }],
          requestBody: {
            content: {
              "application/json": {
                schema: {
                  $ref: "#/components/schemas/CreateInvoiceDto",
                },
                examples: {
                  createInvoiceExample: {
                    $ref: "#/components/examples/createInvoiceExample",
                  },
                },
              },
            },
          },
          responses: {
            "200": {
              description: "Successfully created invoice",
              content: {
                "application/json": {
                  examples: {
                    createInvoiceResponseExample: {
                      $ref: "#/components/examples/createInvoiceResponseExample",
                    },
                  },
                },
              },
            },
          },
        },
      })
      .put("/:id", InvoiceController.update, {
        beforeHandle: [
          checkRoles([UserRole.Admin, UserRole.Manager, UserRole.Finance]),
        ],
        params: updateInvoiceSchema.params,
        body: updateInvoiceSchema.body,
        detail: {
          tags: ["invoices"],
          summary: "Update invoice",
          description: "Update an existing invoice and its items",
          security: [{ bearerAuth: [] }],
          requestBody: {
            content: {
              "application/json": {
                schema: {
                  $ref: "#/components/schemas/UpdateInvoiceDto",
                },
                examples: {
                  updateInvoiceExample: {
                    $ref: "#/components/examples/updateInvoiceExample",
                  },
                },
              },
            },
          },
          responses: {
            "200": {
              description: "Successfully updated invoice",
              content: {
                "application/json": {
                  examples: {
                    updateInvoiceResponseExample: {
                      $ref: "#/components/examples/updateInvoiceResponseExample",
                    },
                  },
                },
              },
            },
            "404": {
              description: "Invoice not found",
            },
          },
        },
      })
  );
