import { t } from "elysia";
import {
  InvoiceType,
  PaymentMethod,
  PaymentStatus,
  ServiceType,
} from "../../database/models/invoice.model";

export const itemNameSchema = t.String({
  minLength: 1,
  maxLength: 100,
  description: "The name of the item",
});

export const itemAmountSchema = t.Number({
  min: 1,
  max: 1000000,
  description: "The amount of the item",
});

export const itemPriceSchema = t.Number({
  min: 0,
  max: 1000000,
  description: "The price of the item",
});

export const itemTotalPriceSchema = t.Number({
  min: 0,
  max: 1000000,
  description: "The total price of the item",
});

export const invoiceNumberSchema = t.String({
  minLength: 1,
  maxLength: 100,
  description: "The number of the invoice",
});

export const invoiceTypeSchema = t.Enum(InvoiceType, {
  description: "The type of the invoice",
});

export const serviceTypeSchema = t.Enum(ServiceType, {
  description: "The service type for the invoice",
});

export const recipientNameSchema = t.String({
  minLength: 1,
  maxLength: 100,
  description: "The name of the recipient",
});

export const projectIdSchema = t.String({
  minLength: 1,
  maxLength: 100,
  description: "The id of the project",
});

export const projectNameSchema = t.String({
  minLength: 1,
  maxLength: 100,
  description: "The name of the project",
});

export const dueDateSchema = t.String({
  pattern: "^\\d{4}-\\d{2}-\\d{2}$",
  description: "The due date of the invoice (YYYY-MM-DD format)",
});

export const paymentMethodSchema = t.Enum(PaymentMethod, {
  description: "The method of the payment",
});

export const paymentStatusSchema = t.Enum(PaymentStatus, {
  description: "The status of the payment",
});

export const notesSchema = t.String({
  description: "The notes of the invoice",
});

export const totalAmountSchema = t.Number({
  min: 0,
  max: 1000000,
  description: "The total amount of the invoice",
});

export const invoiceItemSchema = t.Object({
  item_name: itemNameSchema,
  item_amount: itemAmountSchema,
  item_price: itemPriceSchema,
});

export const createInvoiceSchema = {
  body: t.Object({
    invoice_type: invoiceTypeSchema,
    service_type: serviceTypeSchema,
    recipient_name: recipientNameSchema,
    project_id: t.Optional(projectIdSchema),
    project_name: t.Optional(projectNameSchema),
    due_date: dueDateSchema,
    payment_method: paymentMethodSchema,
    payment_status: t.Optional(paymentStatusSchema),
    notes: t.Optional(notesSchema),
    items: t.Array(invoiceItemSchema),
  }),
};

export const updateInvoiceItemSchema = t.Object({
  id: t.Optional(
    t.String({
      format: "uuid",
      description: "The ID of the existing item (if updating)",
    })
  ),
  item_name: t.Optional(itemNameSchema),
  item_amount: t.Optional(itemAmountSchema),
  item_price: t.Optional(itemPriceSchema),
});

export const updateInvoiceSchema = {
  params: t.Object({
    id: t.String({
      format: "uuid",
      description: "The ID of the invoice to update",
    }),
  }),
  body: t.Object({
    invoice_type: t.Optional(invoiceTypeSchema),
    service_type: t.Optional(serviceTypeSchema),
    recipient_name: t.Optional(recipientNameSchema),
    project_id: t.Optional(projectIdSchema),
    project_name: t.Optional(projectNameSchema),
    due_date: t.Optional(dueDateSchema),
    payment_method: t.Optional(paymentMethodSchema),
    payment_status: t.Optional(paymentStatusSchema),
    notes: t.Optional(notesSchema),
    items: t.Optional(t.Array(updateInvoiceItemSchema)),
  }),
};

export const deleteInvoiceSchema = {
  params: t.Object({
    id: t.String({
      format: "uuid",
      description: "The ID of the invoice to delete",
    }),
  }),
};

export const getInvoiceSchema = {
  params: t.Object({
    id: t.String({
      format: "uuid",
      description: "The id of the invoice",
    }),
  }),
};

export const getInvoiceHistorySchema = {
  params: t.Object({
    id: t.String({
      format: "uuid",
      description: "The id of the invoice to get history for",
    }),
  }),
  query: t.Object({
    page: t.Optional(
      t.Number({
        description: "The page number for pagination (starts at 1)",
      })
    ),
    pageSize: t.Optional(
      t.Number({
        description: "Number of items per page (default: 10)",
      })
    ),
  }),
};

export const getAllInvoicesSchema = {
  query: t.Object({
    search: t.Optional(
      t.String({
        description: "The search term",
      })
    ),
    page: t.Optional(
      t.Number({
        description: "The page number for pagination (starts at 1)",
      })
    ),
    pageSize: t.Optional(
      t.Number({
        description: "Number of items per page (default: 10)",
      })
    ),
    payment_status: t.Optional(
      t.Enum(PaymentStatus, {
        description: "The payment status",
      })
    ),
  }),
};
