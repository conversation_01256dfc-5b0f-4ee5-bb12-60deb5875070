import { dbUtils } from "../../utils/database";
import {
  CreateInvoiceDto,
  Invoice,
  UpdateInvoiceDto,
  InvoiceItem,
  CreateInvoiceItemDto,
} from "../../database/models/invoice.model";
import { QueryOptions } from "../../utils/database.types";
import { InvoiceUpdateHistoryService } from "./invoice-update-history.service";

export class InvoiceService {
  private static readonly TABLE_NAME = "invoices";
  private static readonly ITEMS_TABLE_NAME = "invoice_items";

  /**
   * Get all invoices with search, filter, and pagination
   * @param options Query options including search, filters, and pagination
   */
  static async getAll(options: QueryOptions = {}) {
    // Use dbUtils.getAll to retrieve invoices with pagination and filtering
    const { data, error, result } = await dbUtils.getAll<Invoice>(
      this.TABLE_NAME,
      options
    );

    if (error) {
      return { data: null, error, result: null };
    }

    // If there are invoices, fetch their items
    if (data && data.length > 0) {
      const invoicesWithItems = await Promise.all(
        data.map(async (invoice) => {
          // Get the invoice items
          const { data: items, error: itemsError } = await dbUtils
            .query(this.ITEMS_TABLE_NAME)
            .raw.select()
            .eq("invoice_id", invoice.id)
            .is("deleted_at", null);

          if (itemsError) {
            return invoice;
          }

          return {
            ...invoice,
            items: items || [],
          };
        })
      );

      return { data: invoicesWithItems, error: null, result };
    }

    return { data, error, result };
  }

  /**
   * Retrieve an invoice by ID
   * @param id Invoice ID
   */
  static async getById(id: string) {
    // Get the invoice record
    const { data: invoice, error } = await dbUtils
      .query(this.TABLE_NAME)
      .raw.select()
      .eq("id", id)
      .is("deleted_at", null)
      .single();

    if (error) {
      return { data: null, error };
    }

    if (!invoice) {
      return { data: null, error: null };
    }

    // Get the invoice items
    const { data: items, error: itemsError } = await dbUtils
      .query(this.ITEMS_TABLE_NAME)
      .raw.select()
      .eq("invoice_id", id)
      .is("deleted_at", null);

    if (itemsError) {
      return { data: null, error: itemsError };
    }

    // Return the complete invoice with items
    return {
      data: {
        ...invoice,
        items: items || [],
      },
      error: null,
    };
  }

  /**
   * Retrieve the latest invoices ordered by creation date
   * @param limit Number of invoices to retrieve
   */
  static async getLatestInvoices(limit = 5) {
    const { data, error } = await dbUtils
      .query(this.TABLE_NAME)
      .raw.select()
      .is("deleted_at", null)
      .order("created_at", { ascending: false })
      .limit(limit);

    if (error) {
      return { data: null, error };
    }

    return { data: data as Invoice[], error: null };
  }

  /**
   * Create a new invoice with its associated items
   */
  static async create(data: CreateInvoiceDto, userId: string) {
    // Extract items and service_type from the DTO
    const { items, service_type, ...invoiceData } = data;

    // Create the invoice record first (with all data including service_type)
    const { data: invoice, error } = await dbUtils.create<Invoice>(
      this.TABLE_NAME,
      invoiceData,
      userId
    );

    if (error || !invoice) {
      return {
        data: null,
        error: error || new Error("Failed to create invoice"),
      };
    }

    // Create each invoice item with the new invoice ID
    const createdItems: InvoiceItem[] = [];
    let itemError = null;

    for (const item of items) {
      const itemData: CreateInvoiceItemDto = {
        ...item,
        invoice_id: invoice.id,
      };

      const { data: createdItem, error: createItemError } =
        await dbUtils.create<InvoiceItem>(
          this.ITEMS_TABLE_NAME,
          itemData,
          userId
        );

      if (createItemError || !createdItem) {
        itemError =
          createItemError || new Error("Failed to create invoice item");
        break;
      }

      createdItems.push(createdItem);
    }

    // If there was an error creating items, return the error
    if (itemError) {
      return { data: null, error: itemError };
    }

    // Return the complete invoice with items
    return {
      data: {
        ...invoice,
        items: createdItems,
      },
      error: null,
    };
  }

  /**
   * Update an existing invoice and its items
   * @param invoiceId ID of the invoice to update
   * @param data Updated invoice data
   * @param userId ID of the user making the update
   */
  static async update(
    invoiceId: string,
    data: UpdateInvoiceDto,
    userId: string
  ) {
    // First, get the original invoice to track changes
    const { data: originalInvoice, error: fetchError } = await this.getById(
      invoiceId
    );

    if (fetchError) {
      return { data: null, error: fetchError };
    }

    if (!originalInvoice) {
      return {
        data: null,
        error: new Error(`Invoice with ID ${invoiceId} not found`),
      };
    }

    // Extract items from the DTO
    const { items, ...invoiceData } = data;

    // Update the invoice record
    const { data: updatedInvoice, error } = await dbUtils.update<Invoice>(
      this.TABLE_NAME,
      invoiceId,
      invoiceData,
      userId
    );

    if (error || !updatedInvoice) {
      return {
        data: null,
        error: error || new Error("Failed to update invoice"),
      };
    }

    // Track changes in history
    try {
      const { error: historyError } =
        await InvoiceUpdateHistoryService.trackInvoiceUpdate(
          invoiceId,
          originalInvoice,
          data,
          userId
        );

      if (historyError) {
        console.error(
          `Failed to track history for invoice ${invoiceId}:`,
          historyError
        );
        // Log error but continue with the update
      }
    } catch (error) {
      console.error(
        `Exception in history tracking for invoice ${invoiceId}:`,
        error
      );
      // Log error but continue with the update
    }

    // If there are items to update
    if (items && items.length > 0) {
      // Get existing items to determine which to update, delete, or create
      const { data: existingItems, error: itemsError } = await dbUtils
        .query(this.ITEMS_TABLE_NAME)
        .raw.select()
        .eq("invoice_id", invoiceId)
        .is("deleted_at", null);

      if (itemsError) {
        return { data: null, error: itemsError };
      }

      const existingItemsMap = new Map();
      existingItems.forEach((item: InvoiceItem) => {
        existingItemsMap.set(item.id, item);
      });

      // Track which existing items are being kept
      const updatedItemIds = new Set();

      // Process each item in the update request
      const updatedItems: InvoiceItem[] = [];
      let itemError = null;

      // Update or create items
      for (const item of items) {
        // If item has an ID, it's an update to an existing item
        if (item.id) {
          updatedItemIds.add(item.id);

          // Skip if this item doesn't belong to this invoice
          if (!existingItemsMap.has(item.id)) {
            continue;
          }

          // Calculate total_price for the item
          const itemData: Partial<InvoiceItem> = {};

          if (item.item_name !== undefined) {
            itemData.item_name = item.item_name;
          }
          if (item.item_amount !== undefined) {
            itemData.item_amount = item.item_amount;
          }
          if (item.item_price !== undefined) {
            itemData.item_price = item.item_price;
          }

          // Calculate total_price only if both amount and price are provided
          if (item.item_amount !== undefined && item.item_price !== undefined) {
            itemData.total_price = item.item_amount * item.item_price;
          }

          // Update the existing item
          const { data: updatedItem, error: updateItemError } =
            await dbUtils.update<InvoiceItem>(
              this.ITEMS_TABLE_NAME,
              item.id,
              itemData,
              userId
            );

          if (updateItemError || !updatedItem) {
            itemError =
              updateItemError ||
              new Error(`Failed to update invoice item ${item.id}`);
            break;
          }

          updatedItems.push(updatedItem);
        } else {
          // This is a new item to be created
          // Ensure we have all required fields for a new item
          if (
            !item.item_name ||
            item.item_amount === undefined ||
            item.item_price === undefined
          ) {
            itemError = new Error(
              "Missing required fields for new invoice item"
            );
            break;
          }

          const newItemData: CreateInvoiceItemDto = {
            invoice_id: invoiceId,
            item_name: item.item_name,
            item_amount: item.item_amount,
            item_price: item.item_price,
            total_price: item.item_amount * item.item_price,
          };

          const { data: newItem, error: createItemError } =
            await dbUtils.create<InvoiceItem>(
              this.ITEMS_TABLE_NAME,
              newItemData,
              userId
            );

          if (createItemError || !newItem) {
            itemError =
              createItemError || new Error("Failed to create invoice item");
            break;
          }

          updatedItems.push(newItem);
        }
      }

      // If there was an error processing items, return early
      if (itemError) {
        return { data: null, error: itemError };
      }

      // Soft delete any items that were removed (in the existing items but not in updated items)
      for (const [itemId, item] of existingItemsMap.entries()) {
        if (!updatedItemIds.has(itemId)) {
          const { error: deleteError } = await dbUtils.softDelete(
            this.ITEMS_TABLE_NAME,
            itemId,
            userId
          );

          if (deleteError) {
            return { data: null, error: deleteError };
          }
        }
      }

      // Recalculate the total amount
      const total = updatedItems.reduce(
        (sum, item) => sum + item.total_price,
        0
      );

      // Update the invoice with the new total
      const { data: finalInvoice, error: totalUpdateError } =
        await dbUtils.update<Invoice>(
          this.TABLE_NAME,
          invoiceId,
          { total_amount: total },
          userId
        );

      if (totalUpdateError || !finalInvoice) {
        return {
          data: null,
          error:
            totalUpdateError || new Error("Failed to update invoice total"),
        };
      }

      // Return the complete updated invoice with items
      return {
        data: {
          ...finalInvoice,
          items: updatedItems,
        },
        error: null,
      };
    }

    // If no items were provided in the update, just retrieve the existing items
    const { data: currentItems, error: retrieveError } = await dbUtils
      .query(this.ITEMS_TABLE_NAME)
      .raw.select()
      .eq("invoice_id", invoiceId)
      .is("deleted_at", null);

    if (retrieveError) {
      return { data: null, error: retrieveError };
    }

    // Return the invoice with its current items
    return {
      data: {
        ...updatedInvoice,
        items: currentItems,
      },
      error: null,
    };
  }

  /**
   * Delete an invoice and its associated items
   * @param invoiceId ID of the invoice to delete
   * @param userId ID of the user performing the delete operation
   */
  static async delete(invoiceId: string, userId: string) {
    try {
      // First get all invoice items to delete
      const { data: items, error: itemsError } = await dbUtils
        .query(this.ITEMS_TABLE_NAME)
        .raw.select()
        .eq("invoice_id", invoiceId)
        .is("deleted_at", null);

      if (itemsError) {
        return { data: null, error: itemsError };
      }

      // Soft delete each item
      for (const item of items) {
        const { error: deleteItemError } = await dbUtils.softDelete(
          this.ITEMS_TABLE_NAME,
          item.id,
          userId
        );

        if (deleteItemError) {
          return { data: null, error: deleteItemError };
        }
      }

      // Now soft delete the invoice itself
      const { data, error } = await dbUtils.softDelete<Invoice>(
        this.TABLE_NAME,
        invoiceId,
        userId
      );

      if (error) {
        return { data: null, error };
      }

      return { data, error: null };
    } catch (error: any) {
      return {
        data: null,
        error: new Error(`Failed to delete invoice: ${error.message}`),
      };
    }
  }
}
