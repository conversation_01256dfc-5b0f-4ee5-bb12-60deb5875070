//controller.ts

import { KpiProjectService } from "./service";
import { CreateKpiProjectDto, KpiStatus } from "../../database/models/kpi-project.model";
import { FilterOption, QueryOptions } from "../../utils/database.types";

// Helper function to ensure response functions are available
const ensureResponseFunctions = (context: any) => {
  const { success, serverError, notFound, badRequest } = context;
  if (!success || !serverError || !notFound || !badRequest) {
    throw new Error("Response functions not available");
  }
  return context;
};

export class KpiProjectController {
  /**
   * Create a new KPI Project
   */
  static async create(context: any) {
    const { body, user, success, serverError, badRequest } =
      ensureResponseFunctions(context);

    try {
      // Validate required fields
      if (
        !body.project_name ||
        !body.project_id ||
        !body.description ||
        !body.target ||
        !body.period
      ) {
        return badRequest("Missing required fields", "MISSING_FIELDS");
      }

      // Check for duplicate KPI Project
      const kpiProjectService = new KpiProjectService();
      const hasDuplicate = await kpiProjectService.checkDuplicateKpiProject(
        body.project_id,
        body.period
      );
      if (hasDuplicate) {
        return badRequest(
          "A KPI Project already exists for this project in the specified period",
          "DUPLICATE_KPI_PROJECT"
        );
      }

      const kpiProject = await kpiProjectService.createKpiProject(
        body as CreateKpiProjectDto,
        user?.id
      );
      return success(kpiProject, "KPI Project created successfully");
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : "Failed to create KPI Project";
      return serverError(errorMessage);
    }
  }

  /**
   * Create a KPI Project from a regular project
   * This method is intended to be called by the project service when a new project is created
   */
  static async createFromProject(context: any) {
    const { body, user, success, serverError, badRequest } =
      ensureResponseFunctions(context);

    try {
      // Validate required fields
      if (
        !body.project_id ||
        !body.project_name ||
        !body.objectives ||
        !body.start_date ||
        !body.end_date
      ) {
        return badRequest("Missing required project fields", "MISSING_FIELDS");
      }

      const kpiProjectService = new KpiProjectService();
      const kpiProject = await kpiProjectService.createKpiProjectFromProject(
        body.project_id,
        body.project_name,
        body.objectives,
        body.start_date,
        body.end_date,
        user?.id
      );

      return success(
        kpiProject,
        "KPI Project automatically created from project"
      );
    } catch (error) {
      const errorMessage =
        error instanceof Error
          ? error.message
          : "Failed to create KPI Project from project";

      // If it's a duplicate, return a more specific error
      if (errorMessage.includes("already exists")) {
        return badRequest(errorMessage, "DUPLICATE_KPI_PROJECT");
      }

      return serverError(errorMessage);
    }
  }

  /**
   * Get all KPI Projects
   */
  // static async getAll(context: any) {
  //   const { query, success, serverError } = ensureResponseFunctions(context);

  //   try {
  //     const kpiProjectService = new KpiProjectService();
  //     const kpiProjects = await kpiProjectService.getAllKpiProjects(query);

  //     return success(
  //       {
  //         data: kpiProjects.data,
  //         pagination: kpiProjects.result,
  //       },
  //       "KPI Projects retrieved successfully"
  //     );
  //   } catch (error) {
  //     return serverError(
  //       error instanceof Error ? error.message : "Failed to get KPI Projects"
  //     );
  //   }
  // }

  /**
 * Get all KPI Projects with search, filter, and pagination
 */
static async getAll(context: any) {
  const { query = {}, success, serverError} = ensureResponseFunctions(context);

  try {
    // Build standard query options
    const options: QueryOptions = {};

    // Handle filters
    const filters: FilterOption[] = [];

    if (query.search) {
      options.search = {
        term: query.search,
        fields: ['project_name', 'description', 'period']
      };
    }

    if (query.status) {
      filters.push({ field: 'status', value: query.status });
    }

    if (query.project_name) { // Filter by project name
      filters.push({ field: 'project_name', value: query.project_name });  // Ensure partial matching works
    }

    if (filters.length > 0) {
      options.filters = filters;
    }

    // Apply pagination
    options.pagination = {
      page: query.page ? parseInt(query.page, 10) : 1,
      pageSize: query.pageSize ? parseInt(query.pageSize, 10) : 10,
    };

    // Get KPI Projects from the service
    const { data, result } = await new KpiProjectService().getKpis(options);

    if (!data) {
      return serverError("No data found", "NO_DATA_FOUND");
    }

    // Return standardized response with pagination and items
    return success(
      {
        data: data,
        pagination: result,
      },
      "KPI Projects retrieved successfully"
    );
  } catch (error) {
    return serverError(error instanceof Error ? error.message : "Failed to get KPI Projects");
  }
}


  /**
   * Get a KPI Project by ID
   */
  static async getById(context: any) {
    const { params, success, serverError, notFound } =
      ensureResponseFunctions(context);

    try {
      const kpiProjectService = new KpiProjectService();
      const kpiProject = await kpiProjectService.getKpiProject(params.id);

      if (!kpiProject) {
        return notFound("KPI Project not found");
      }

      return success(kpiProject, "KPI Project retrieved successfully");
    } catch (error) {
      return serverError(
        error instanceof Error ? error.message : "Failed to get KPI Project"
      );
    }
  }

  /**
   * Update a KPI Project
   */
  static async update(context: any) {
    const { body, params, user, success, serverError, badRequest } =
      ensureResponseFunctions(context);

    try {
      // Validate required fields
      if (!body.project_name && !body.target && !body.status) {
        return badRequest("No valid fields to update", "NO_VALID_FIELDS");
      }

      const kpiProjectService = new KpiProjectService();
      const kpiProject = await kpiProjectService.updateKpiProject(
        params.id,
        body,
        user?.id
      );

      return success(kpiProject, "KPI Project updated successfully");
    } catch (error) {
      return serverError(
        error instanceof Error ? error.message : "Failed to update KPI Project"
      );
    }
  }

  /**
   * Delete a KPI Project
   */
  static async delete(context: any) {
    const { params, user, success, serverError } =
      ensureResponseFunctions(context);

    try {
      const kpiProjectService = new KpiProjectService();
      await kpiProjectService.deleteKpiProject(params.id, user?.id);

      return success(null, "KPI Project deleted successfully");
    } catch (error) {
      return serverError(
        error instanceof Error ? error.message : "Failed to delete KPI Project"
      );
    }
  }

  /**
   * Get KPI Projects by project ID
   */
  static async getByProjectId(context: any) {
    const { params, success, serverError } = ensureResponseFunctions(context);

    try {
      const kpiProjectService = new KpiProjectService();
      const kpiProjects = await kpiProjectService.getKpiProjectsByProjectId(
        params.project_id
      );

      return success(
        kpiProjects,
        "KPI Projects by project ID retrieved successfully"
      );
    } catch (error) {
      return serverError(
        error instanceof Error
          ? error.message
          : "Failed to get KPI Projects by project ID"
      );
    }
  }

    /**
   * Update the status of a KPI Project
   */
    static async updateStatus(context: any) {
      const { params, body, user, success, serverError, badRequest, notFound } =
        ensureResponseFunctions(context);

      try {
        // Validate status field
        if (!body.status) {
          return badRequest("Status is required", "MISSING_STATUS");
        }

        // Ensure the status is valid
        if (!Object.values(KpiStatus).includes(body.status)) {
          return badRequest("Invalid KPI Project status", "INVALID_STATUS");
        }

        // Call the service method to update the status
        const kpiProjectService = new KpiProjectService();
        const kpiProject = await kpiProjectService.updateKpiProjectStatus(
          params.id,
          body.status,
          user?.id
        );

        return success(kpiProject, "KPI Project status updated successfully");
      } catch (error) {
        return serverError(error instanceof Error ? error.message : "Failed to update KPI Project status");
      }
    }

  
    /**
 * Get KPI Projects with organization and PIC details
 */
static async getWithDetails(context: any) {
  const { query = {}, user, profile, success, serverError, forbidden } = ensureResponseFunctions(context);

  try {
    // Ensure user has a valid profile
    if (!profile) {
      return forbidden("User profile not found", "PROFILE_NOT_FOUND");
    }

    // Build query options
    const options: QueryOptions = {};
    
    // Add search, filters, pagination options
    // [Implementation details for building options]
    
    const kpiProjectService = new KpiProjectService();
    const { data, error, result } = await kpiProjectService.getKpiProjectsWithDetails(
      options,
      {
        role: profile.role,
        org_id: profile.org_id,
        employee_id: profile.employee_id
      }
    );
    
    if (error) {
      return serverError(error.message);
    }
    
    return success(
      {
        data,
        pagination: result
      },
      "KPI Projects with details retrieved successfully"
    );
  } catch (error) {
    return serverError(
      error instanceof Error ? error.message : "Failed to get KPI Projects with details"
    );
  }
}


  /**
   * Get all KPI Projects by project ID with filtering, search, and pagination
   */
  static async getAllByProject(context: any) {
    const { params, query = {}, success, serverError } = ensureResponseFunctions(context);

    try {
      // Build query options
      const options: QueryOptions = {};

      // Handle filters
    const filters: FilterOption[] = [];

      // Handle search
      if (query.search) {
        options.search = {
          term: query.search,
          fields: ['project_name', 'description', 'period'] // Search on project name, description, and period
        };
      }
      if (query.status) {
        filters.push({ field: 'status', value: query.status });
      }

      if (query.project_name) { // Filter by project name
        filters.push({ field: 'project_name', value: query.project_name });  // Ensure partial matching works
      }

      if (filters.length > 0) {
        options.filters = filters;
      }

      // Apply pagination
      options.pagination = {
        page: query.page ? parseInt(query.page, 10) : 1,
        pageSize: query.pageSize ? parseInt(query.pageSize, 10) : 10
      };

      const kpiProjectService = new KpiProjectService();
      const { data, error, result } = await kpiProjectService.getAllKpisByProject(
        params.project_id,
        options
      );

      if (error) {
        return serverError(error.message);
      }

      return success(
        {
          data,
          pagination: result
        },
        "KPI Projects by project ID retrieved successfully"
      );
    } catch (error) {
      return serverError(
        error instanceof Error ? error.message : "Failed to get KPI Projects by project ID"
      );
    }
  }
}