// routes.ts
import { <PERSON><PERSON>, t } from "elysia";
import { KpiProjectController } from "./controller";
import {
  createKpiProjectSchema,
  getAllKpiProjectsSchema,
  getKpiProjectSchema,
  updateKpiProjectSchema,
  deleteKpiProjectSchema,
  projectIdSchema,
  updateKpiProjectStatusSchema,
  getKpiProjectsWithDetailsSchema,
  getAllKpisByProjectSchema,
} from "./schema";
import { requireActiveUser, checkRoles } from "../../middleware/auth";
import { UserRole } from "../../database/models/user-profile.model";

/**
 * KPI Project routes
 */
export const kpiProjectRoutes = (app: Elysia) =>
  app.group("/kpi-projects", (app) =>
    app
      // First ensure users are active
      .use(requireActiveUser)

      // Create new KPI Project
      .post("/", KpiProjectController.create, {
        beforeHandle: [checkRoles([UserRole.Operation, UserRole.Manager])],
        body: createKpiProjectSchema.body,
        detail: {
          tags: ["kpi-projects"],
          summary: "Create a new KPI Project",
          description: "Create a new KPI Project with the provided details",
          security: [{ bearerAuth: [] }],
        },
      })

      // Create KPI Project from a regular project
      .post("/from-project", KpiProjectController.createFromProject, {
        beforeHandle: [checkRoles([UserRole.Operation, UserRole.Manager])],
        body: t.Object({
          project_id: t.String({
            minLength: 1,
            description: "The ID of the project",
          }),
          project_name: t.String({
            minLength: 1,
            description: "The name of the project",
          }),
          objectives: t.String({
            minLength: 1,
            description: "The objectives of the project",
          }),
          start_date: t.String({
            pattern: "^\\d{4}-\\d{2}-\\d{2}$",
            description: "The start date of the project (YYYY-MM-DD)",
          }),
          end_date: t.String({
            pattern: "^\\d{4}-\\d{2}-\\d{2}$",
            description: "The end date of the project (YYYY-MM-DD)",
          }),
        }),
        detail: {
          tags: ["kpi-projects"],
          summary: "Create a KPI Project from a regular project",
          description:
            "Automatically create a KPI Project when a regular project is created",
          security: [{ bearerAuth: [] }],
        },
      })

      // Get all KPI Projects
      .get("/", KpiProjectController.getAll, {
        beforeHandle: [
          checkRoles([
            UserRole.Operation,
            UserRole.Manager,
            UserRole.Client,
            UserRole.Finance,
            UserRole.HR,
          ]),
        ],
        query: getAllKpiProjectsSchema.query,
        detail: {
          tags: ["kpi-projects"],
          summary: "Get all KPI Projects",
          description:
            "Retrieve all KPI Projects with search (by project name), filter, and pagination",
          security: [{ bearerAuth: [] }],
        },
      })

      // .get("/", KpiProjectController.getAll, {
      //   beforeHandle: [checkRoles([UserRole.Operation, UserRole.Manager])],
      //   query: getAllKpiProjectsSchema.query,
      //   detail: {
      //     tags: ["kpi-projects"],
      //     summary: "Get all KPI Projects",
      //     description:
      //       "Retrieve all KPI Projects with search, filter, and pagination",
      //     security: [{ bearerAuth: [] }],
      //   },
      // })

      // Get KPI Project by ID
      .get("/:id", KpiProjectController.getById, {
        beforeHandle: [
          checkRoles([
            UserRole.Operation,
            UserRole.Manager,
            UserRole.Client,
            UserRole.Finance,
            UserRole.HR,
          ]),
        ],
        params: getKpiProjectSchema.params,
        detail: {
          tags: ["kpi-projects"],
          summary: "Get KPI Project by ID",
          description: "Retrieve a specific KPI Project by its ID",
          security: [{ bearerAuth: [] }],
        },
      })

      // Update a KPI Project
      .put("/:id", KpiProjectController.update, {
        beforeHandle: [checkRoles([UserRole.Operation, UserRole.Manager])],
        params: updateKpiProjectSchema.params,
        body: updateKpiProjectSchema.body,
        detail: {
          tags: ["kpi-projects"],
          summary: "Update a KPI Project",
          description: "Update an existing KPI Project",
          security: [{ bearerAuth: [] }],
        },
      })

      // Delete a KPI Project
      .delete("/:id", KpiProjectController.delete, {
        beforeHandle: [checkRoles([UserRole.Operation, UserRole.Manager])],
        params: deleteKpiProjectSchema.params,
        detail: {
          tags: ["kpi-projects"],
          summary: "Delete a KPI Project",
          description: "Delete an existing KPI Project",
          security: [{ bearerAuth: [] }],
        },
      })

      .get("/project/:project_id", KpiProjectController.getByProjectId, {
        beforeHandle: [checkRoles([UserRole.Operation, UserRole.Manager])],
        params: t.Object({
          project_id: projectIdSchema,
        }),
        detail: {
          tags: ["kpi-projects"],
          summary: "Get KPI Projects by Project ID",
          description:
            "Retrieve all KPI Projects related to a specific project",
          security: [{ bearerAuth: [] }],
        },
      })

      // Update KPI Project status
      .patch("/:id/status", KpiProjectController.updateStatus, {
        beforeHandle: [checkRoles([UserRole.Operation, UserRole.Manager])],
        params: updateKpiProjectStatusSchema.params,
        body: updateKpiProjectStatusSchema.body,
        detail: {
          tags: ["kpi-projects"],
          summary: "Update KPI Project status",
          description: "Update the status of a specific KPI Project",
          security: [{ bearerAuth: [] }],
        },
      })

      .get("/with-details", KpiProjectController.getWithDetails, {
        beforeHandle: [
          checkRoles([
            UserRole.Operation,
            UserRole.Manager,
            UserRole.Client,
            UserRole.HR,
            UserRole.Finance,
          ]),
        ],
        query: getKpiProjectsWithDetailsSchema.query,
        detail: {
          tags: ["kpi-projects"],
          summary: "Get KPI Projects with organization and PIC details",
          description:
            "Retrieve KPI Projects with organization and PIC information, filtered by role. Organization representatives and project PICs can see their project-specific KPIs.",
          security: [{ bearerAuth: [] }],
        },
      })

      .get(
        "/all-by-project/:project_id",
        KpiProjectController.getAllByProject,
        {
          beforeHandle: [
            checkRoles([
              UserRole.Operation,
              UserRole.Manager,
              UserRole.Client,
              UserRole.HR,
              UserRole.Finance,
            ]),
          ],
          params: getAllKpisByProjectSchema.params,
          query: getAllKpisByProjectSchema.query,
          detail: {
            tags: ["kpi-projects"],
            summary: "Get all KPI Projects by project ID",
            description:
              "Retrieve all KPI Projects for a specific project with filtering, search, and pagination",
            security: [{ bearerAuth: [] }],
          },
        }
      )
  );
