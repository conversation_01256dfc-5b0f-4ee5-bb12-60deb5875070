// schema.ts

import { t } from "elysia";
import { KpiStatus } from "../../database/models/kpi-project.model";

// Common schema patterns
export const projectNameSchema = t.String({
  minLength: 1,
  maxLength: 100,
  description: "The name of the project",
});

export const projectIdSchema = t.String({
  minLength: 1,
  maxLength: 50,
  description: "The ID of the project",
});

export const descriptionSchema = t.String({
  minLength: 1,
  maxLength: 500,
  description: "The description of the KPI project",
});

export const targetSchema = t.String({
  minLength: 1,
  maxLength: 200,
  description: "The target of the KPI project",
});

export const periodSchema = t.String({
  minLength: 1,
  maxLength: 50,
  description: "The period of the KPI project (e.g., '2024-Q1')",
});

export const kpiStatusSchema = t.Enum(KpiStatus, {
  description: "The status of the KPI project",
});

export const additionalNotesSchema = t.Optional(
  t.String({
    maxLength: 1000,
    description: "Additional notes about the KPI project",
  })
);

// Validation schemas
export const createKpiProjectSchema = {
  body: t.Object({
    project_name: projectNameSchema,
    project_id: projectIdSchema,
    description: descriptionSchema,
    target: targetSchema,
    period: periodSchema,
    status: t.Optional(kpiStatusSchema),
    additional_notes: additionalNotesSchema,
  }),
};

// export const getAllKpiProjectsSchema = {
//   query: t.Object({
//     limit: t.Optional(t.Number({ min: 1, max: 100, default: 10 })),
//     offset: t.Optional(t.Number({ min: 0, default: 0 })),
//     project_name: t.Optional(
//       t.String({
//         minLength: 1,
//         description: "The project name to search for KPI Projects",
//       })
//     ),
//   }),
// };

export const getAllKpiProjectsSchema = {
  query: t.Object({
    search: t.Optional(
      t.String({
        description: "Search for projects based on project name, description, or period",
      })
    ),
    status: t.Optional(
      t.Enum(KpiStatus, {
        description: "Filter by KPI status (e.g., 'in_progress')",
      })
    ),
    project_name: t.Optional(
      t.String({
        description: "Filter by project name (partial matching allowed)",
      })
    ),
    pagination: t.Optional(
      t.Object({
        page: t.Number(),
        pageSize: t.Number(),
      })
    ),
  }),
};


export const getKpiProjectSchema = {
  params: t.Object({
    id: projectIdSchema,
  }),
};

export const updateKpiProjectSchema = {
  params: t.Object({
    id: projectIdSchema,
  }),
  body: t.Object({
    project_name: t.Optional(projectNameSchema),
    description: t.Optional(descriptionSchema),
    target: t.Optional(targetSchema),
    period: t.Optional(periodSchema),
    status: t.Optional(kpiStatusSchema),
    additional_notes: t.Optional(additionalNotesSchema),
  }),
};

export const deleteKpiProjectSchema = {
  params: t.Object({
    id: projectIdSchema,
  }),
};

// View KPI Projects by Project ID
export const getKpiProjectsByProjectIdSchema = {
  params: t.Object({
    project_id: projectIdSchema,
  }),
};

export const updateKpiProjectStatusSchema = {
  params: t.Object({
    id: projectIdSchema,
  }),
  body: t.Object({
    status: kpiStatusSchema, // Use the existing KpiStatus enum for validation
  }),
};

export const getKpiProjectsWithDetailsSchema = {
  query: t.Object({
    search: t.Optional(
      t.String({
        description: "Search for projects based on project name or description",
      })
    ),
    status: t.Optional(
      t.Enum(KpiStatus, {
        description: "Filter by KPI status (e.g., 'in_progress')",
      })
    ),
    page: t.Optional(
      t.Number({
        minimum: 1,
        default: 1,
        description: "Page number for pagination",
      })
    ),
    pageSize: t.Optional(
      t.Number({
        minimum: 1,
        maximum: 100,
        default: 10,
        description: "Number of items per page",
      })
    ),
  }),
};

export const getAllKpisByProjectSchema = {
  params: t.Object({
    project_id: projectIdSchema,
  }),
  query: t.Object({
    search: t.Optional(
      t.String({
        description: "Search for KPIs based on project name or description",
      })
    ),
    status: t.Optional(
      t.Enum(KpiStatus, {
        description: "Filter by KPI status (e.g., 'in_progress')",
      })
    ),
    page: t.Optional(
      t.Number({
        minimum: 1,
        default: 1,
        description: "Page number for pagination",
      })
    ),
    pageSize: t.Optional(
      t.Number({
        minimum: 1,
        maximum: 100,
        default: 10,
        description: "Number of items per page",
      })
    ),
  }),
};