// service.ts

import {
  CreateKpiProjectDto,
  KpiProject,
  KpiProjectWithDetails,
  KpiStatus,
} from "../../database/models/kpi-project.model";
import { supabase } from "../../libs/supabase";
import { dbUtils } from "../../utils/database";
import { PaginatedResponse, QueryOptions } from "../../utils/database.types";

const TABLE_NAME = "kpi_projects";
const TABLE_PROJECTS = "projects";

export class KpiProjectService {
  /**
   * Create a new KPI Project
   * @param data KPI Project data
   * @param userId User ID for audit
   * @returns Created KPI Project
   */
  async createKpiProject(
    data: CreateKpiProjectDto,
    userId?: string
  ): Promise<KpiProject> {
    const { data: kpiProject, error } = await dbUtils.create<KpiProject>(
      TABLE_NAME,
      { ...data, status: data.status || KpiStatus.NOT_STARTED },
      userId
    );

    if (error) {
      throw new Error(error.message);
    }

    return kpiProject;
  }

  /**
   * Check if a KPI Project already exists for a project and period
   * @param projectId Project ID to check
   * @param period Period to check
   * @returns True if a duplicate exists, false otherwise
   */
  /**
   * Check if a KPI Project already exists for a project in a specific period
   * @param projectId The ID of the project
   * @param period The period to check (e.g., '2024-Q1')
   * @returns True if a duplicate exists, false otherwise
   */
  async checkDuplicateKpiProject(
    projectId: string,
    period: string
  ): Promise<boolean> {
    const { data } = await dbUtils.getAll<KpiProject>(TABLE_NAME, {
      filters: [
        { field: "project_id", value: projectId },
        { field: "period", value: period },
      ],
    });

    return !!(data && data.length > 0);
  }

  /**
   * Automatically create a KPI Project when a regular project is created
   * This method should be called by the project service when a new project is created
   *
   * @param projectId The ID of the created project
   * @param projectName The name of the created project
   * @param objectives The objectives of the created project
   * @param startDate The start date of the project (YYYY-MM-DD)
   * @param endDate The end date of the project (YYYY-MM-DD)
   * @param userId The ID of the user creating the project
   * @returns The created KPI Project
   */
  async createKpiProjectFromProject(
    projectId: string,
    projectName: string,
    objectives: string,
    startDate: string,
    endDate: string,
    userId?: string
  ): Promise<KpiProject> {
    // Extract year and quarter from start date
    const startYear = startDate.substring(0, 4);
    const startMonth = parseInt(startDate.substring(5, 7));
    const quarter = Math.ceil(startMonth / 3);
    const period = `${startYear}-Q${quarter}`;

    // Check if a KPI Project already exists for this project and period
    const hasDuplicate = await this.checkDuplicateKpiProject(projectId, period);
    if (hasDuplicate) {
      throw new Error(
        `A KPI Project already exists for project ${projectId} in period ${period}`
      );
    }

    // Create the KPI Project
    const kpiProjectData: CreateKpiProjectDto = {
      project_id: projectId,
      project_name: projectName,
      description: `KPI for project: ${projectName}`,
      target: objectives,
      period,
      status: KpiStatus.NOT_STARTED,
      additional_notes: `Automatically created when project was created. Project runs from ${startDate} to ${endDate}.`,
    };

    return this.createKpiProject(kpiProjectData, userId);
  }

  /**
   * Get all KPI Projects with optional filtering and pagination
   * @param options Query options including project_name for filtering
   * @returns Paginated KPI Projects
   */
  // async getAllKpiProjects(options: any = {}) {
  //   const filters = options.filters || [];

  //   // Filter by project name if provided
  //   if (options.project_name) {
  //     filters.push({
  //       field: "project_name",
  //       value: options.project_name, // Partial search
  //       operator: "ilike", // Case-insensitive search
  //     });
  //   }

  //   const { data, error, result } = await dbUtils.getAll<KpiProject>(
  //     TABLE_NAME,
  //     {
  //       filters,
  //       pagination: options.pagination,
  //     }
  //   );

  //   if (error) {
  //     throw new Error(error.message);
  //   }

  //   return {
  //     data: data,
  //     result,
  //   };
  // }

  async getKpis(options: QueryOptions): Promise<PaginatedResponse<KpiProject>> {
    // Use dbUtils.getAll for standardized handling of search, filters, and pagination
    const { data, error, result } = await dbUtils.getAll<KpiProject>(
      TABLE_NAME,
      options
    );

    return {
      data: data || [],
      error,
      result,
    };
  }

  /**
   * Get a KPI Project by ID
   * @param id KPI Project ID
   * @returns KPI Project or null if not found
   */
  async getKpiProject(id: string) {
    const { data, error } = await dbUtils.getById<KpiProject>(TABLE_NAME, id);

    if (error) {
      if (error.code === "PGRST116") {
        return null; // Record not found
      }
      throw new Error(error.message);
    }

    return data;
  }

  /**
   * Update a KPI Project
   * @param id KPI Project ID
   * @param data Update data
   * @param userId User ID for audit
   * @returns Updated KPI Project
   * @throws Error if KPI Project not found or update fails
   */
  async updateKpiProject(id: string, data: any, userId?: string) {
    const existingKpiProject = await this.getKpiProject(id);
    if (!existingKpiProject) {
      throw new Error("KPI Project not found");
    }

    const { data: kpiProject, error } = await dbUtils.update<KpiProject>(
      TABLE_NAME,
      id,
      data,
      userId
    );
    if (error) {
      throw new Error(error.message);
    }

    return kpiProject;
  }

  /**
   * Delete a KPI Project (soft delete)
   * @param id KPI Project ID
   * @param userId User ID for audit
   * @returns Success message
   * @throws Error if KPI Project not found or delete fails
   */
  async deleteKpiProject(id: string, userId?: string) {
    const existingKpiProject = await this.getKpiProject(id);
    if (!existingKpiProject) {
      throw new Error("KPI Project not found");
    }

    const { error } = await dbUtils.softDelete(TABLE_NAME, id, userId);

    if (error) {
      throw new Error(error.message);
    }

    return { message: "KPI Project deleted successfully" };
  }

  /**
   * Get KPI Projects by Project ID
   * @param projectId The ID of the project
   * @returns KPI Projects list for that project
   */
  async getKpiProjectsByProjectId(projectId: string): Promise<KpiProject[]> {
    const { data, error } = await dbUtils.getAll<KpiProject>(TABLE_NAME, {
      filters: [{ field: "project_id", value: projectId }],
    });

    if (error) {
      throw new Error(error.message);
    }

    return data || [];
  }

  /**
   * Update KPI Project status
   * @param id The ID of the KPI Project
   * @param status The new status to update
   * @param userId The user ID for audit
   * @returns Updated KPI Project
   */
  async updateKpiProjectStatus(
    id: string,
    status: KpiStatus,
    userId?: string
  ): Promise<KpiProject> {
    const existingKpiProject = await this.getKpiProject(id);
    if (!existingKpiProject) {
      throw new Error("KPI Project not found");
    }

    // Perform update operation
    const { data: updatedKpiProject, error } = await dbUtils.update<KpiProject>(
      TABLE_NAME,
      id,
      { status },
      userId
    );

    if (error) {
      throw new Error(error.message);
    }

    return updatedKpiProject;
  }

  /**
   * Get KPI Projects with organization and PIC information, respecting role-based access
   */
  async getKpiProjectsWithDetails(
    options: QueryOptions = {},
    user: { role: string; org_id?: string; employee_id?: string }
  ): Promise<PaginatedResponse<KpiProjectWithDetails>> {
    // First, get the project IDs that match our criteria
    let projectQuery;

    if (user.role === "Client" && user.org_id) {
      // For clients, get projects from their organization
      projectQuery = supabase
        .from("projects")
        .select("id")
        .eq("organization_id", user.org_id)
        .is("deleted_at", null);
    } else if (
      (user.role === "Operation" ||
        user.role === "HR" ||
        user.role === "Finance" ||
        user.role === "Admin" ||
        user.role === "Manager") &&
      user.employee_id
    ) {
      // For operation staff, get projects where they are the PIC
      projectQuery = supabase
        .from("projects")
        .select("id")
        .eq("pic_project", user.employee_id)
        .is("deleted_at", null);
    }

    // For managers, we don't need to filter projects
    let projectIds: string[] = [];

    // Only execute the query if we have one (for clients and operation staff)
    if (projectQuery && user.role !== "Manager") {
      const { data: projects, error: projectError } = await projectQuery;

      if (projectError) {
        return {
          data: [],
          error: projectError,
          result: {
            total: 0,
            page: options.pagination?.page || 1,
            pageSize: options.pagination?.pageSize || 10,
            pageCount: 0,
          },
        };
      }

      projectIds = projects.map((project) => project.id);

      // If no projects match, return empty result immediately
      if (projectIds.length === 0) {
        return {
          data: [],
          error: null,
          result: {
            total: 0,
            page: options.pagination?.page || 1,
            pageSize: options.pagination?.pageSize || 10,
            pageCount: 0,
          },
        };
      }
    }

    // Now, get KPI projects with details
    let query = supabase
      .from(TABLE_NAME)
      .select(
        `
        *,
        projects:project_id (
          organization_id,
          pic_project,
          project_name,
          project_category,
          status_project
        )
      `,
        { count: "exact" }
      )
      .is("deleted_at", null);

    // Filter by project IDs if we have any (for clients and operation staff)
    if (projectIds.length > 0 && user.role !== "Manager") {
      query = query.in("project_id", projectIds);
    }

    // Apply search if provided
    if (options.search?.term) {
      query = query.or(
        `project_name.ilike.%${options.search.term}%,description.ilike.%${options.search.term}%`
      );
    }

    // Apply status filter if provided
    if (options.filters) {
      options.filters.forEach((filter) => {
        if (filter.field === "status") {
          query = query.eq("status", filter.value);
        }
      });
    }

    // Apply pagination
    if (options.pagination) {
      const { page, pageSize } = options.pagination;
      const from = (page - 1) * pageSize;
      const to = from + pageSize - 1;
      query = query.range(from, to);
    }

    // Apply sorting
    if (options.sort) {
      const { field, direction } = Array.isArray(options.sort)
        ? options.sort[0]
        : options.sort;
      query = query.order(field, { ascending: direction === "asc" });
    } else {
      // Default sorting by created_at in descending order
      query = query.order("created_at", { ascending: false });
    }

    // Execute the query
    const { data, error, count } = await query;

    return {
      data: data || [],
      error,
      result: {
        total: count || 0,
        page: options.pagination?.page || 1,
        pageSize: options.pagination?.pageSize || 10,
        pageCount: Math.ceil(
          (count || 0) / (options.pagination?.pageSize || 10)
        ),
      },
    };
  }

  /**
   * Get all KPI Projects by project ID with filtering, search, and pagination
   * @param projectId The ID of the project
   * @param options Query options including search, filters, and pagination
   * @returns Paginated KPI Projects for the specified project
   */
  async getAllKpisByProject(
    projectId: string,
    options: QueryOptions = {}
  ): Promise<PaginatedResponse<KpiProject>> {
    // Create a new options object to avoid modifying the input
    const queryOptions: QueryOptions = { ...options };

    // Ensure we have a filters array
    if (!queryOptions.filters) {
      queryOptions.filters = [];
    }

    // Add the project_id filter
    queryOptions.filters.push({ field: "project_id", value: projectId });

    // Use dbUtils.getAll for standardized handling of search, filters, and pagination
    const { data, error, result } = await dbUtils.getAll<KpiProject>(
      TABLE_NAME,
      queryOptions
    );

    return {
      data: data || [],
      error,
      result,
    };
  }
}
