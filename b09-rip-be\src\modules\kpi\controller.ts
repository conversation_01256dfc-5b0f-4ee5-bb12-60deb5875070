// cotnroller.ts
import { KpiService } from "./service";
import {
    CreateKpiDto, KpiStatus, UpdateKpiDto
} from "../../database/models/kpi.model";
import { AuthUser } from "../../middleware/auth";
import { FilterOption, QueryOptions } from "../../utils/database.types";

/**
 * Helper function to ensure API response functions are available
 */
function ensureResponseFunctions(context: any) {
    // Check if the basic response functions are available
    if (typeof context.success !== "function") {
      console.error("API Response middleware functions not available in context");
      // Provide fallback response if middleware functions aren't available
      return {
        success: (data: any, message = "Operation successful") => ({
          success: true,
          message,
          data,
        }),
        forbidden: (message = "Forbidden", errorCode?: string) => ({
          success: false,
          message,
          data: null,
          error: { code: errorCode || "FORBIDDEN" },
        }),
        unauthorized: (message = "Unauthorized", errorCode?: string) => ({
          success: false,
          message,
          data: null,
          error: { code: errorCode || "UNAUTHORIZED" },
        }),
        notFound: (message = "Not found", errorCode?: string) => ({
          success: false,
          message,
          data: null,
          error: { code: errorCode || "NOT_FOUND" },
        }),
        serverError: (message = "Server error", error?: Error) => ({
          success: false,
          message,
          data: null,
          error: {
            code: "INTERNAL_SERVER_ERROR",
            details: error ? { stack: error.stack } : undefined,
          },
        }),
        badRequest: (message = "Bad request", errorCode?: string) => ({
          success: false,
          message,
          data: null,
          error: { code: errorCode || "BAD_REQUEST" },
        }),
      };
    }

    // If functions are available, return the original context
    return context;
  }

export class KpiController {

  /**
   * Create a new KPI
   */
  static async create(context: any) {
    const { body, user, success, serverError, badRequest } = ensureResponseFunctions(context);

    try {
      // Validate required fields
      if (!body.full_name || !body.description || !body.target) {
        return badRequest("Missing required fields", "MISSING_FIELDS");
      }

      const kpi = await new KpiService().createKpi(body as CreateKpiDto, user?.id);
      return success(kpi, "KPI created successfully");
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : "Failed to create KPI";

      // Better error handling based on error message
      if (errorMessage.includes("Employee not found")) {
        return badRequest(errorMessage, "INVALID_EMPLOYEE");
      } else if (errorMessage.includes("already exists")) {
        return badRequest(errorMessage, "DUPLICATE_KPI");
      }

      return serverError(errorMessage);
    }
  }

  /**
   * Get all KPIs with search, filter, and pagination
   */
  static async getAll(context: any) {
    const { query = {}, success, serverError } = ensureResponseFunctions(context);

    try {
      // Build standard query options
      const options: QueryOptions = {};

      // Handle search
      if (query.search) {
        options.search = {
          term: query.search,
          fields: ['full_name', 'description', 'period']
        };
      }

      // Handle filters
      const filters: FilterOption[] = [];
      if (query.status) {
        filters.push({ field: 'status', value: query.status });
      }
      if (query.employee_id) {
        filters.push({ field: 'employee_id', value: query.employee_id });
      }
      if (query.period) {
        filters.push({ field: 'period', value: query.period });
      }

      if (filters.length > 0) {
        options.filters = filters;
      }

      // Apply pagination
      options.pagination = {
        page: query.page ? parseInt(query.page, 10) : 1,
        pageSize: query.pageSize ? parseInt(query.pageSize, 10) : 10,
      };

      const { data, error, result } = await new KpiService().getKpis(options);

      if (error) {
        return serverError(error instanceof Error ? error.message : String(error), error);
      }

      // Return standardized response
      return success(
        {
          items: data,
          pagination: result,
        },
        "KPIs retrieved successfully"
      );
    } catch (error) {
      return serverError(error instanceof Error ? error.message : "Failed to get KPIs");
    }
  }

  /**
   * Update a KPI by ID
   */
  static async update(context: any) {
    const { params, body, user, success, serverError, notFound, badRequest } = ensureResponseFunctions(context);

    try {
      const kpi = await new KpiService().updateKpi(params.id, body as UpdateKpiDto, user?.id);
      return success(kpi, "KPI updated successfully");
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : "Failed to update KPI";

      if (errorMessage.includes("not found")) {
        return notFound(errorMessage, "KPI_NOT_FOUND");
      } else if (errorMessage.includes("duplicate")) {
        return badRequest(errorMessage, "DUPLICATE_KPI");
      } else if (errorMessage.includes("Employee not found")) {
        return badRequest(errorMessage, "INVALID_EMPLOYEE");
      }

      return serverError(errorMessage);
    }
  }

  /**
   * Delete a KPI by ID
   */
  static async delete(context: any) {
    const { params, user, success, serverError, notFound } = ensureResponseFunctions(context);

    try {
      await new KpiService().deleteKpi(params.id, user?.id);
      return success(null, "KPI deleted successfully");
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : "Failed to delete KPI";

      if (errorMessage.includes("not found")) {
        return notFound(errorMessage, "KPI_NOT_FOUND");
      }

      return serverError(errorMessage);
    }
  }

  /**
   * Update KPI status
   */
  static async updateStatus(context: any) {
    const { params, body, user, success, serverError, notFound, badRequest } = ensureResponseFunctions(context);

    try {
      const { status } = body as { status: KpiStatus };

      // Validate status
      if (!Object.values(KpiStatus).includes(status)) {
        return badRequest("Invalid KPI status", "INVALID_STATUS");
      }

      const kpi = await new KpiService().updateKpiStatus(params.id, status, user?.id);
      return success(kpi, "KPI status updated successfully");
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : "Failed to update KPI status";

      if (errorMessage.includes("not found")) {
        return notFound(errorMessage, "KPI_NOT_FOUND");
      }

      return serverError(errorMessage);
    }
  }

  /**
   * Update KPI bonus
   */
  static async updateBonus(context: any) {
    const { params, body, user, success, serverError, notFound, badRequest } = ensureResponseFunctions(context);

    try {
      const { bonus_received } = body as { bonus_received: number };

      // Validate bonus
      if (typeof bonus_received !== "number" || bonus_received < 0) {
        return badRequest("Bonus must be a positive number", "INVALID_BONUS");
      }

      const kpi = await new KpiService().updateKpiBonus(params.id, bonus_received, user?.id);
      return success(kpi, "KPI bonus updated successfully");
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : "Failed to update KPI bonus";

      if (errorMessage.includes("not found")) {
        return notFound(errorMessage, "KPI_NOT_FOUND");
      }

      return serverError(errorMessage);
    }
  }

  /**
   * Get a KPI by ID
   */
  static async getById(context: any) {
    const { params, success, serverError, notFound } = ensureResponseFunctions(context);

    try {
      const kpi = await new KpiService().getKpi(params.id);
      return success(kpi, "KPI retrieved successfully");
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : "Failed to retrieve KPI";

      if (errorMessage.includes("not found")) {
        return notFound(errorMessage, "KPI_NOT_FOUND");
      }

      return serverError(errorMessage);
    }
  }

  // Add this new method to KpiController class in controller.ts

/**
 * Get all KPIs for a specific employee
 */
static async getByEmployeeId(context: any) {
  const { params, query = {}, success, serverError, notFound } = ensureResponseFunctions(context);

  try {
    // Validate that employee ID is provided
    if (!params.employeeId) {
      return notFound("Employee ID is required", "MISSING_EMPLOYEE_ID");
    }

    // Build standard query options
    const options: QueryOptions = {};

    // Handle search
    if (query.search) {
      options.search = {
        term: query.search,
        fields: ['full_name', 'description', 'period']
      };
    }

    // Handle filters (status and period)
    const filters: FilterOption[] = [];
    if (query.status) {
      filters.push({ field: 'status', value: query.status });
    }
    if (query.period) {
      filters.push({ field: 'period', value: query.period });
    }

    if (filters.length > 0) {
      options.filters = filters;
    }

    // Apply pagination
    options.pagination = {
      page: query.page ? parseInt(query.page, 10) : 1,
      pageSize: query.pageSize ? parseInt(query.pageSize, 10) : 10,
    };

    // Get KPIs for the employee
    const { data, error, result } = await new KpiService().getKpisByEmployeeId(params.employeeId, options);

    if (error) {
      return serverError(error instanceof Error ? error.message : String(error), error);
    }

    // Return standardized response
    return success(
      {
        items: data,
        pagination: result,
      },
      "Employee KPIs retrieved successfully"
    );
  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : "Failed to get employee KPIs";

    if (errorMessage.includes("Employee not found")) {
      return notFound(errorMessage, "EMPLOYEE_NOT_FOUND");
    }

    return serverError(errorMessage);
  }
}

/**
 * Get KPIs for the currently logged-in user
 */
static async getMyKpis(context: any) {
  const { profile, query = {}, success, serverError, notFound, badRequest } = ensureResponseFunctions(context);

  try {
    // Check if user has a profile with employee_id
    if (!profile?.employee_id) {
      return badRequest(
        "You don't have an associated employee profile",
        "NO_EMPLOYEE_PROFILE"
      );
    }

    // Build standard query options
    const options: QueryOptions = {};

    // Handle search
    if (query.search) {
      options.search = {
        term: query.search,
        fields: ['full_name', 'description', 'period']
      };
    }

    // Handle filters (status and period)
    const filters: FilterOption[] = [];
    if (query.status) {
      filters.push({ field: 'status', value: query.status });
    }
    if (query.period) {
      filters.push({ field: 'period', value: query.period });
    }

    if (filters.length > 0) {
      options.filters = filters;
    }

    // Apply pagination
    options.pagination = {
      page: query.page ? parseInt(query.page, 10) : 1,
      pageSize: query.pageSize ? parseInt(query.pageSize, 10) : 10,
    };

    // Get KPIs for the current user's employee_id
    const { data, error, result } = await new KpiService().getKpisByEmployeeId(profile.employee_id, options);

    if (error) {
      return serverError(error instanceof Error ? error.message : String(error), error);
    }

    // Return standardized response
    return success(
      {
        items: data,
        pagination: result,
      },
      "My KPIs retrieved successfully"
    );
  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : "Failed to get my KPIs";

    if (errorMessage.includes("Employee not found")) {
      return notFound("Your employee profile was not found", "EMPLOYEE_NOT_FOUND");
    }

    return serverError(errorMessage);
  }
}
}

