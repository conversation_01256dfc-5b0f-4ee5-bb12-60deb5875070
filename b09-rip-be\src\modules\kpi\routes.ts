//routes.ts
import { <PERSON><PERSON> } from "elysia";
import { <PERSON><PERSON><PERSON>ontroller } from "./controller";
import {
  createKpiSchema,
  updateKpiSchema,
  getKpiSchema,
  getAllKpisSchema,
  deleteKpiSchema,
  updateKpiStatusSchema,
  updateKpiBonusSchema,
  getKpisByEmployeeIdSchema,
  getMyKpisSchema,
} from "./schema";

import { requireActiveUser, checkRoles } from "../../middleware/auth";
import { UserRole } from "../../database/models/user-profile.model";

/**
 * KPI routes
 */

export const kpiRoutes = (app: Elysia) =>
  app.group("/kpi", (app) =>
    app
      // First ensure users are active
      .use(requireActiveUser)

      // Get all KPIs
      .get("/", KpiController.getAll, {
        beforeHandle: [checkRoles([UserRole.HR, UserRole.Manager])],
        query: getAllKpisSchema.query,
        detail: {
          tags: ["kpi"],
          summary: "Get all KPIs",
          description: "Retrieve all KPIs with search, filter, and pagination",
          security: [{ bearerAuth: [] }],
        },
      })

      // Get KPI by ID
      .get("/:id", KpiController.getById, {
        beforeHandle: [checkRoles([UserRole.HR, UserRole.Manager])],
        params: getKpiSchema.params,
        detail: {
          tags: ["kpi"],
          summary: "Get KPI by ID",
          description: "Retrieve a specific KPI by its ID",
          security: [{ bearerAuth: [] }],
        },
      })

      // Create new KPI
      .post("/", KpiController.create, {
        beforeHandle: [checkRoles([UserRole.HR, UserRole.Manager])],
        body: createKpiSchema.body,
        detail: {
          tags: ["kpi"],
          summary: "Create a new KPI",
          security: [{ bearerAuth: [] }],
        },
      })

      // Update KPI
      .put("/:id", KpiController.update, {
        beforeHandle: [checkRoles([UserRole.HR, UserRole.Manager])],
        params: updateKpiSchema.params,
        body: updateKpiSchema.body,
        detail: {
          tags: ["kpi"],
          summary: "Update a KPI",
          security: [{ bearerAuth: [] }],
        },
      })

      // Delete KPI
      .delete("/:id", KpiController.delete, {
        beforeHandle: [checkRoles([UserRole.HR, UserRole.Manager])],
        params: deleteKpiSchema.params,
        detail: {
          tags: ["kpi"],
          summary: "Delete a KPI",
          security: [{ bearerAuth: [] }],
        },
      })

      // Update KPI status
      .patch("/:id/status", KpiController.updateStatus, {
        beforeHandle: [checkRoles([UserRole.HR, UserRole.Manager])],
        params: updateKpiStatusSchema.params,
        body: updateKpiStatusSchema.body,
        detail: {
          tags: ["kpi"],
          summary: "Update KPI status",
          security: [{ bearerAuth: [] }],
        },
      })

      // Update KPI bonus
      .patch("/:id/bonus", KpiController.updateBonus, {
        beforeHandle: [checkRoles([UserRole.HR, UserRole.Manager])],
        params: updateKpiBonusSchema.params,
        body: updateKpiBonusSchema.body,
        detail: {
          tags: ["kpi"],
          summary: "Update KPI bonus",
          security: [{ bearerAuth: [] }],
        },
      })

    // Get KPIs by employee ID
  .get("/employee/:employeeId", KpiController.getByEmployeeId, {
    beforeHandle: [checkRoles([UserRole.HR, UserRole.Manager, UserRole.Operation])],
    params: getKpisByEmployeeIdSchema.params,
    query: getKpisByEmployeeIdSchema.query,
    detail: {
      tags: ["kpi"],
      summary: "Get KPIs by employee ID",
      description: "Retrieve all KPIs for a specific employee with optional filtering and pagination",
      security: [{ bearerAuth: [] }],
    },
  })

  // Get KPIs for the currently logged-in user
  .get("/my-kpis", KpiController.getMyKpis, {
    // No role check needed as we're getting the user's own KPIs
    query: getMyKpisSchema.query,
    detail: {
      tags: ["kpi"],
      summary: "Get my KPIs",
      description: "Retrieve all KPIs associated with your employee ID",
      security: [{ bearerAuth: [] }],
    },
  })

);