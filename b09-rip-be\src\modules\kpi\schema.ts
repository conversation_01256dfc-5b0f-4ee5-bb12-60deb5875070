import { t } from "elysia";
import { KpiStatus } from "../../database/models/kpi.model";

export const fullNameSchema = t.String({
  minLength: 1,
  maxLength: 100,
  description: "The full name of the employee",
});

export const employeeIdSchema = t.String({
  minLength: 1,
  maxLength: 50,
  description: "The ID of the employee",
});

export const descriptionSchema = t.String({
  minLength: 1,
  maxLength: 500,
  description: "The description of the KPI",
});

export const targetSchema = t.String({
  minLength: 1,
  maxLength: 200,
  description: "The target of the KPI",
});

export const periodSchema = t.String({
  minLength: 1,
  maxLength: 50,
  description: "The period of the KPI (e.g., '2024-Q1')",
});

export const kpiStatusSchema = t.Enum(KpiStatus, {
  description: "The status of the KPI",
});

export const bonusReceivedSchema = t.Number({
  min: 0,
  description: "The bonus amount received",
});

export const additionalNotesSchema = t.String({
  description: "Additional notes for the KPI",
});

export const createKpiSchema = {
  body: t.Object({
    full_name: fullNameSchema,
    employee_id: employeeIdSchema,
    description: descriptionSchema,
    target: targetSchema,
    period: periodSchema,
    status: t.Optional(kpiStatusSchema),
    bonus_received: t.Optional(bonusReceivedSchema),
    additional_notes: t.Optional(additionalNotesSchema),
  }),
};

export const updateKpiSchema = {
  params: t.Object({
    id: t.String({
      format: "uuid",
      description: "The ID of the KPI to update",
    }),
  }),
  body: t.Object({
    full_name: t.Optional(fullNameSchema),
    employee_id: t.Optional(employeeIdSchema),
    description: t.Optional(descriptionSchema),
    target: t.Optional(targetSchema),
    period: t.Optional(periodSchema),
    status: t.Optional(kpiStatusSchema),
    bonus_received: t.Optional(bonusReceivedSchema),
    additional_notes: t.Optional(additionalNotesSchema),
  }),
};

export const deleteKpiSchema = {
  params: t.Object({
    id: t.String({
      format: "uuid",
      description: "The ID of the KPI to delete",
    }),
  }),
};

export const getKpiSchema = {
  params: t.Object({
    id: t.String({
      format: "uuid",
      description: "The ID of the KPI",
    }),
  }),
};

export const getAllKpisSchema = {
  query: t.Object({
    search: t.Optional(t.String({
      description: "Search term for full_name, description, or period",
    })),
    employee_id: t.Optional(employeeIdSchema),
    period: t.Optional(periodSchema),
    status: t.Optional(kpiStatusSchema),
    page: t.Optional(
      t.Number({
        description: "The page number",
      })
    ),
    pageSize: t.Optional(
      t.Number({
        description: "The page size",
      })
    ),
  }),
};

export const updateKpiStatusSchema = {
  params: t.Object({
    id: t.String({
      format: "uuid",
      description: "The ID of the KPI",
    }),
  }),
  body: t.Object({
    status: kpiStatusSchema,
  }),
};

export const updateKpiBonusSchema = {
  params: t.Object({
    id: t.String({
      format: "uuid",
      description: "The ID of the KPI",
    }),
  }),
  body: t.Object({
    bonus_received: bonusReceivedSchema,
  }),
};

// Add this to schema.ts first
export const getKpisByEmployeeIdSchema = {
  params: t.Object({
    employeeId: t.String({
      description: "The ID of the employee",
    }),
  }),
  query: t.Object({
    search: t.Optional(t.String({
      description: "Search term for full_name, description, or period",
    })),
    period: t.Optional(periodSchema),
    status: t.Optional(kpiStatusSchema),
    page: t.Optional(
      t.Number({
        description: "The page number",
      })
    ),
    pageSize: t.Optional(
      t.Number({
        description: "The page size",
      })
    ),
  }),
};

// Schema for getting current user's KPIs
export const getMyKpisSchema = {
  query: t.Object({
    search: t.Optional(t.String({
      description: "Search term for full_name, description, or period",
    })),
    period: t.Optional(periodSchema),
    status: t.Optional(kpiStatusSchema),
    page: t.Optional(
      t.Number({
        description: "The page number",
      })
    ),
    pageSize: t.Optional(
      t.Number({
        description: "The page size",
      })
    ),
  }),
};