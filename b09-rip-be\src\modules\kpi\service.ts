//service.ts
import { C<PERSON>KpiDto, Kpi, KpiStatus, UpdateKpiDto } from "../../database/models/kpi.model";
import { supabase } from "../../libs/supabase";
import { HttpResponses } from "../../middleware/api-response";
import { QueryOptions, PaginatedResponse, FilterOption } from "../../utils/database.types";
import { dbUtils } from "../../utils/database";

const TABLE_NAME = "kpis";
const EMPLOYEES_TABLE = "employees";

export class KpiService {
  /**
   * Validates if an employee exists in the system
   * @param employeeId Employee ID to validate
   * @returns True if employee exists, false otherwise
   */
  async validateEmployee(employeeId: string): Promise<boolean> {
    const { data, error } = await dbUtils.getById(EMPLOYEES_TABLE, employeeId);
    return !!(data && !error);
  }

  /**
   * Checks if a KPI already exists for the given employee and period
   * @param employeeId Employee ID to check
   * @param period Period to check
   * @returns True if a duplicate exists, false otherwise
   */
  async checkDuplicateKpi(employeeId: string, period: string): Promise<boolean> {
    const { data, error } = await dbUtils.getAll(TABLE_NAME, {
      filters: [
        { field: 'employee_id', value: employeeId },
        { field: 'period', value: period }
      ]
    });
    
    // If we found any records, a KPI already exists
    return !!(data && data.length > 0);
  }

  async getKpiById(id: string): Promise<Kpi | null> {
    const { data, error } = await dbUtils.getById<Kpi>(TABLE_NAME, id);

    if (error) {
      if (error.code === "PGRST116") {
        return null; // Record not found
      }
      throw new Error(error.message);
    }

    return data;
  }

  async createKpi(data: CreateKpiDto, userId?: string): Promise<Kpi> {
    // Validate employee existence
    const employeeExists = await this.validateEmployee(data.employee_id);
    if (!employeeExists) {
      throw new Error("Employee not found. Cannot create KPI for non-existent employee.");
    }
    
    // Check for duplicate KPI
    const hasDuplicate = await this.checkDuplicateKpi(data.employee_id, data.period);
    if (hasDuplicate) {
      throw new Error("A KPI already exists for this employee in the specified period.");
    }
    
    const { data: kpi, error } = await dbUtils.create<Kpi>(
      TABLE_NAME,
      { ...data, status: data.status || KpiStatus.NOT_STARTED },
      userId || data.employee_id // Use userId if available, otherwise fallback to employee_id
    );

    if (error) {
      throw new Error(error.message);
    }

    return kpi;
  }

  async getKpi(id: string): Promise<Kpi> {
    const kpi = await this.getKpiById(id);
    
    if (!kpi) {
      throw new Error("KPI not found");
    }

    return kpi;
  }

  async getKpis(options: QueryOptions): Promise<PaginatedResponse<Kpi>> {
    // Use dbUtils.getAll for standardized handling of search, filters, and pagination
    const { data, error, result } = await dbUtils.getAll<Kpi>(TABLE_NAME, options);

    return {
      data: data || [],
      error,
      result,
    };
  }

  async updateKpi(id: string, data: UpdateKpiDto, userId?: string): Promise<Kpi> {
    // Get existing KPI first (for validation)
    const existingKpi = await this.getKpiById(id);
    if (!existingKpi) {
      throw new Error("KPI not found");
    }
    
    // Check for potential duplicates if employee_id or period is changing
    if ((data.employee_id && data.employee_id !== existingKpi.employee_id) || 
        (data.period && data.period !== existingKpi.period)) {
      
      const employeeId = data.employee_id || existingKpi.employee_id;
      const period = data.period || existingKpi.period;
      
      // Check if this would create a duplicate (exclude current ID from check)
      const { data: existingRecords } = await dbUtils.getAll(TABLE_NAME, {
        filters: [
          { field: 'employee_id', value: employeeId },
          { field: 'period', value: period },
          { field: 'id', value: id, operator: 'neq' }
        ]
      });
      
      if (existingRecords && existingRecords.length > 0) {
        throw new Error("Update would create a duplicate KPI for this employee/period");
      }
    }
    
    // If employee_id is changing, validate new employee exists
    if (data.employee_id && data.employee_id !== existingKpi.employee_id) {
      const employeeExists = await this.validateEmployee(data.employee_id);
      if (!employeeExists) {
        throw new Error("Employee not found. Cannot update KPI with non-existent employee.");
      }
    }

    // Use dbUtils for standardized update with audit fields
    const { data: kpi, error } = await dbUtils.update<Kpi>(TABLE_NAME, id, data, userId);

    if (error) {
      throw new Error(error.message);
    }

    return kpi;
  }

  async deleteKpi(id: string, userId?: string): Promise<void> {
    // Check if KPI exists first
    const existingKpi = await this.getKpiById(id);
    if (!existingKpi) {
      throw new Error("KPI not found");
    }
    
    // Perform soft delete using dbUtils
    const { error } = await dbUtils.softDelete(TABLE_NAME, id, userId);

    if (error) {
      throw new Error(error.message);
    }
  }

  async updateKpiStatus(id: string, status: KpiStatus, userId?: string): Promise<Kpi> {
    // Check if KPI exists
    const existingKpi = await this.getKpiById(id);
    if (!existingKpi) {
      throw new Error("KPI not found");
    }
    
    // Update the status using dbUtils
    return this.updateKpi(id, { status }, userId || existingKpi.employee_id);
  }

  async updateKpiBonus(id: string, bonusReceived: number, userId?: string): Promise<Kpi> {
    // Check if KPI exists
    const existingKpi = await this.getKpiById(id);
    if (!existingKpi) {
      throw new Error("KPI not found");
    }
    
    // Update the bonus using dbUtils
    return this.updateKpi(id, { bonus_received: bonusReceived }, userId || existingKpi.employee_id);
  }

/**
 * Get all KPIs for a specific employee
 * @param employeeId The ID of the employee
 * @param options Optional query options for filtering and pagination
 * @returns Paginated list of KPIs for the employee
 */
async getKpisByEmployeeId(employeeId: string, options: QueryOptions = {}): Promise<PaginatedResponse<Kpi>> {
  // Validate employee exists
  const employeeExists = await this.validateEmployee(employeeId);
  if (!employeeExists) {
    throw new Error("Employee not found");
  }
  
  // Ensure we always filter by employee_id
  const employeeFilter: FilterOption = { field: 'employee_id', value: employeeId };
  
  // Add employee filter to existing filters if any
  if (options.filters) {
    options.filters.push(employeeFilter);
  } else {
    options.filters = [employeeFilter];
  }
  
  // Get KPIs with the employee filter applied
  return this.getKpis(options);
}
} 