import { OrganizationService } from "./service";
import {
  CreateOrganizationDto,
  UpdateOrganizationDto,
} from "../../database/models/organization.model";
import { AuthUser } from "../../middleware/auth";
import { FilterOption, QueryOptions } from "../../utils/database.types";

/**
 * Helper function to ensure API response functions are available
 */
function ensureResponseFunctions(context: any) {
  // Check if the basic response functions are available
  if (typeof context.success !== "function") {
    console.error("API Response middleware functions not available in context");
    // Provide fallback response if middleware functions aren't available
    return {
      success: (data: any, message = "Operation successful") => ({
        success: true,
        message,
        data,
      }),
      forbidden: (message = "Forbidden", errorCode?: string) => ({
        success: false,
        message,
        data: null,
        error: { code: errorCode || "FORBIDDEN" },
      }),
      unauthorized: (message = "Unauthorized", errorCode?: string) => ({
        success: false,
        message,
        data: null,
        error: { code: errorCode || "UNAUTHORIZED" },
      }),
      notFound: (message = "Not found", errorCode?: string) => ({
        success: false,
        message,
        data: null,
        error: { code: errorCode || "NOT_FOUND" },
      }),
      serverError: (message = "Server error", error?: Error) => ({
        success: false,
        message,
        data: null,
        error: {
          code: "INTERNAL_SERVER_ERROR",
          details: error ? { stack: error.stack } : undefined,
        },
      }),
    };
  }

  // If functions are available, return the original context
  return context;
}

export class OrganizationController {
  /**
   * Get all organizations with search, filter, and pagination
   */
  static async getAll(context: any) {
    const {
      query = {},
      success,
      serverError,
    } = ensureResponseFunctions(context);

    // Build query options from request parameters
    const options: QueryOptions = {};

    // Handle search
    if (query.search) {
      options.search = {
        term: query.search,
        fields: ["name", "phone", "address", "client_type"], // Searchable fields
      };
    }

    // Handle filters
    const filters: FilterOption[] = [];

    // Only filter by client_type
    if (query.client_type) {
      filters.push({ field: "client_type", value: query.client_type });
    }

    if (filters.length > 0) {
      options.filters = filters;
    }

    // Always apply pagination, with default values if not provided
    options.pagination = {
      page: query.page ? parseInt(query.page, 10) : 1,
      pageSize: query.pageSize ? parseInt(query.pageSize, 10) : 10,
    };

    // Call the model with the constructed options
    const { data, error, result } = await OrganizationService.getAll(options);

    if (error) {
      return serverError(error.message, error);
    }

    // Include pagination result in the response if available
    return success(
      {
        items: data,
        pagination: result,
      },
      "Organizations retrieved successfully"
    );
  }

  /**
   * Get organization by ID
   */
  static async getById(context: any) {
    const { params, success, notFound, serverError } =
      ensureResponseFunctions(context);
    const { id } = params;

    const { data, error } = await OrganizationService.getById(id);

    if (error) {
      return serverError(error.message, error);
    }

    if (!data) {
      return notFound("Organization not found");
    }

    return success(data, "Organization retrieved successfully");
  }

  /**
   * Create a new organization
   */
  static async create(context: any) {
    const { body, user, success, serverError } =
      ensureResponseFunctions(context);

    const { data, error } = await OrganizationService.create(body, user.id);

    if (error) {
      return serverError(error.message, error);
    }

    return success(data, "Organization created successfully");
  }

  /**
   * Update an organization
   */
  static async update(context: any) {
    const { params, body, user, success, notFound, serverError } =
      ensureResponseFunctions(context);
    const { id } = params;

    const { data, error } = await OrganizationService.update(id, body, user.id);

    if (error) {
      return serverError(error.message, error);
    }

    if (!data) {
      return notFound("Organization not found");
    }

    return success(data, "Organization updated successfully");
  }

  /**
   * Delete an organization
   */
  static async delete(context: any) {
    const { params, user, success, notFound, serverError } =
      ensureResponseFunctions(context);
    const { id } = params;

    const { data, error } = await OrganizationService.delete(id, user.id);

    if (error) {
      return serverError(error.message, error);
    }

    if (!data) {
      return notFound("Organization not found");
    }

    return success(null, "Organization deleted successfully");
  }
}
