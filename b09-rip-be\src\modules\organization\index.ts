import { Ely<PERSON> } from "elysia";
import { organizationRoutes } from "./routes";
import { apiResponse } from "../../middleware/api-response";

// Create an instance with the middleware applied
// This ensures middleware is applied directly to this module
const organizationApp = new Elysia().use(apiResponse).use(organizationRoutes);

export * from "./service";

// Export the organization module
export const organizations = organizationApp;

// Re-export for convenience
export * from "./schema";
export * from "./controller";
