import { Ely<PERSON> } from "elysia";
import { OrganizationController } from "./controller";
import {
  createOrganizationSchema,
  updateOrganizationSchema,
  getOrganizationSchema,
  deleteOrganizationSchema,
  getOrganizationsQuerySchema,
} from "./schema";
import { requireRole, requireActiveUser } from "../../middleware/auth";
import { UserRole } from "../../database/models/user-profile.model";
import { apiResponse } from "../../middleware/api-response";

/**
 * Organizations routes
 *
 * Admin and Managers can manage organizations
 * All users must be active to access these routes
 * Note: API response middleware is applied at the module level via index.ts
 */
export const organizationRoutes = (app: Elysia) =>
  app.group("/organizations", (app) =>
    app
      // First ensure users are active
      .use(requireActiveUser)

      // Then apply role-based access control for active users
      .use(
        requireRole([
          UserRole.Admin,
          UserRole.Manager,
          UserRole.Operation,
          UserRole.Finance,
          UserRole.HR,
        ])
      )

      // CRUD operations
      .get("/", OrganizationController.getAll, {
        query: getOrganizationsQuerySchema.query,
        detail: {
          tags: ["organizations"],
          summary: "Get all organizations",
          description:
            "Retrieve all organizations with search, filter, and pagination",
          security: [{ bearerAuth: [] }],
          responses: {
            "200": {
              description: "Successfully retrieved organizations",
              content: {
                "application/json": {
                  schema: {
                    $ref: "#/components/schemas/Organization",
                  },
                  examples: {
                    getOrganizationsExample: {
                      $ref: "#/components/examples/getOrganizationsExample",
                    },
                  },
                },
              },
            },
            "403": {
              description: "Forbidden - User does not have required role",
              content: {
                "application/json": {
                  examples: {
                    notFoundErrorExample: {
                      $ref: "#/components/examples/notFoundErrorExample",
                    },
                  },
                },
              },
            },
          },
        },
      })
      .get("/:id", OrganizationController.getById, {
        params: getOrganizationSchema.params,
        detail: {
          tags: ["organizations"],
          summary: "Get organization by ID",
          description: "Retrieve a specific organization by ID",
          security: [{ bearerAuth: [] }],
          responses: {
            "200": {
              description: "Successfully retrieved organization",
              content: {
                "application/json": {
                  schema: {
                    $ref: "#/components/schemas/Organization",
                  },
                  examples: {
                    getOrganizationExample: {
                      $ref: "#/components/examples/getOrganizationExample",
                    },
                  },
                },
              },
            },
            "404": {
              description: "Organization not found",
              content: {
                "application/json": {
                  examples: {
                    notFoundErrorExample: {
                      $ref: "#/components/examples/notFoundErrorExample",
                    },
                  },
                },
              },
            },
          },
        },
      })
      .post("/", OrganizationController.create, {
        body: createOrganizationSchema.body,
        detail: {
          tags: ["organizations"],
          summary: "Create organization",
          description: "Create a new organization",
          security: [{ bearerAuth: [] }],
          requestBody: {
            content: {
              "application/json": {
                schema: {
                  $ref: "#/components/schemas/CreateOrganizationDto",
                },
                examples: {
                  createOrganizationExample: {
                    $ref: "#/components/examples/createOrganizationExample",
                  },
                },
              },
            },
          },
          responses: {
            "200": {
              description: "Successfully created organization",
              content: {
                "application/json": {
                  schema: {
                    $ref: "#/components/schemas/Organization",
                  },
                },
              },
            },
          },
        },
      })
      .put("/:id", OrganizationController.update, {
        body: updateOrganizationSchema.body,
        params: updateOrganizationSchema.params,
        detail: {
          tags: ["organizations"],
          summary: "Update organization",
          description: "Update an existing organization",
          security: [{ bearerAuth: [] }],
          requestBody: {
            content: {
              "application/json": {
                schema: {
                  $ref: "#/components/schemas/UpdateOrganizationDto",
                },
                examples: {
                  updateOrganizationExample: {
                    $ref: "#/components/examples/updateOrganizationExample",
                  },
                },
              },
            },
          },
          responses: {
            "200": {
              description: "Successfully updated organization",
              content: {
                "application/json": {
                  schema: {
                    $ref: "#/components/schemas/Organization",
                  },
                },
              },
            },
            "404": {
              description: "Organization not found",
              content: {
                "application/json": {
                  examples: {
                    notFoundErrorExample: {
                      $ref: "#/components/examples/notFoundErrorExample",
                    },
                  },
                },
              },
            },
          },
        },
      })
      .delete("/:id", OrganizationController.delete, {
        params: deleteOrganizationSchema.params,
        detail: {
          tags: ["organizations"],
          summary: "Delete organization",
          description: "Delete an organization (soft delete)",
          security: [{ bearerAuth: [] }],
          responses: {
            "200": {
              description: "Successfully deleted organization",
              content: {
                "application/json": {
                  examples: {
                    deleteOrganizationExample: {
                      $ref: "#/components/examples/deleteOrganizationExample",
                    },
                  },
                },
              },
            },
            "404": {
              description: "Organization not found",
              content: {
                "application/json": {
                  examples: {
                    notFoundErrorExample: {
                      $ref: "#/components/examples/notFoundErrorExample",
                    },
                  },
                },
              },
            },
          },
        },
      })
  );
