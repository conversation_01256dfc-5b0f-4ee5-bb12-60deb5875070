import { t } from "elysia";

// Common organization schema patterns
export const nameSchema = t.String({
  minLength: 2,
  maxLength: 100,
  description: "Organization name (2-100 characters)",
});

export const phoneSchema = t.String({
  pattern: "^[0-9]+$",
  minLength: 10,
  description: "Phone number (minimum 10 digits, only numbers allowed)",
});

export const addressSchema = t.String({
  minLength: 5,
  maxLength: 500,
  description: "Organization address (5-500 characters)",
});

export const clientTypeSchema = t.String({
  minLength: 2,
  maxLength: 50,
  description: "Type of client organization (2-50 characters)",
});

export const notesSchema = t.Optional(
  t.String({
    maxLength: 1000,
    description: "Optional notes about the organization (max 1000 characters)",
  })
);

// Organization validation schemas
export const createOrganizationSchema = {
  body: t.Object({
    name: nameSchema,
    phone: phoneSchema,
    address: addressSchema,
    client_type: clientTypeSchema,
    notes: t.<PERSON>tional(notesSchema),
  }),
};

export const updateOrganizationSchema = {
  params: t.Object({
    id: t.String({
      format: "uuid",
      description: "Organization ID",
    }),
  }),
  body: t.Object({
    name: t.Optional(nameSchema),
    phone: t.Optional(phoneSchema),
    address: t.Optional(addressSchema),
    client_type: t.Optional(clientTypeSchema),
    notes: t.Optional(notesSchema),
  }),
};

export const getOrganizationSchema = {
  params: t.Object({
    id: t.String({
      format: "uuid",
      description: "Organization ID",
    }),
  }),
};

export const deleteOrganizationSchema = {
  params: t.Object({
    id: t.String({
      format: "uuid",
      description: "Organization ID",
    }),
  }),
};

// Schema for GET /organizations query parameters
export const getOrganizationsQuerySchema = {
  query: t.Object({
    search: t.Optional(
      t.String({
        description:
          "Search term to filter organizations by name, phone, address, or client type",
      })
    ),
    page: t.Optional(
      t.Numeric({
        description: "Page number for pagination (starts at 1)",
      })
    ),
    pageSize: t.Optional(
      t.Numeric({
        description: "Number of items per page",
      })
    ),
    client_type: t.Optional(
      t.String({
        description: "Filter organizations by client type",
      })
    ),
  }),
};
