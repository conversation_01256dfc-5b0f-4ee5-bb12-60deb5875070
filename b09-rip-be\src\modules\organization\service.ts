import { dbUtils } from "../../utils/database";
import {
  CreateOrganizationDto,
  Organization,
  UpdateOrganizationDto,
} from "../../database/models/organization.model";
import { QueryOptions } from "../../utils/database.types";

export class OrganizationService {
  private static readonly TABLE_NAME = "organizations";

  /**
   * Create a new organization
   */
  static async create(data: CreateOrganizationDto, userId: string) {
    return dbUtils.create<Organization>(this.TABLE_NAME, data, userId);
  }

  /**
   * Get an organization by ID
   */
  static async getById(id: string) {
    return dbUtils.getById<Organization>(this.TABLE_NAME, id);
  }

  /**
   * Get all organizations with search, filter, and pagination
   * @param options Query options including search, filters, and pagination
   */
  static async getAll(options: QueryOptions = {}) {
    return dbUtils.getAll<Organization>(this.TABLE_NAME, options);
  }

  /**
   * Update an organization
   */
  static async update(id: string, data: UpdateOrganizationDto, userId: string) {
    return dbUtils.update<Organization>(this.TABLE_NAME, id, data, userId);
  }

  /**
   * Delete an organization (soft delete)
   */
  static async delete(id: string, userId: string) {
    return dbUtils.softDelete<Organization>(this.TABLE_NAME, id, userId);
  }

  /**
   * Create an empty organization for a new client
   * @param userId The user ID who is creating the organization
   */
  static async createEmpty(userId: string) {
    const emptyOrg: CreateOrganizationDto = {
      name: `New Organization (${new Date().toISOString().split("T")[0]})`,
      phone: "0000000000",
      address: "Not provided",
      client_type: "New Client",
      notes: "Automatically created for new client user",
    };

    return this.create(emptyOrg, userId);
  }
}
