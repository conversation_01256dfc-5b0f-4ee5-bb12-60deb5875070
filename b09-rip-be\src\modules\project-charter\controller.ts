/**
 * Project Charter Controller
 *
 * This handles all the project charter stuff - creating them, fetching them, etc.
 *
 * Pretty standard approach here - we check the incoming data, call the right
 * service methods to do the actual work, and handle any errors that pop up.
 */
import { ProjectCharterService } from "./service";
import {
  CreateProjectCharterDto,
  UpdateProjectCharterDto,
} from "../../database/models/project-charter.model";
import { AuthUser } from "../../middleware/auth";
import { FilterOption, QueryOptions } from "../../utils/database.types";

/**
 * Makes sure we have all our response helpers ready to go
 *
 * Each function formats the response in a standard way - success/error flag, message,
 * and the data (or error details if something went wrong).
 */
const ensureResponseFunctions = (context: any) => {
  return {
    ...(context as any),
    success: (data: any, message = "Operation successful") => ({
      success: true,
      message,
      data,
    }),
    badRequest: (message = "Bad request", errorCode?: string) => ({
      success: false,
      message,
      data: null,
      error: { code: errorCode || "BAD_REQUEST" },
    }),
    forbidden: (message = "Forbidden", errorCode?: string) => ({
      success: false,
      message,
      data: null,
      error: { code: errorCode || "FORBIDDEN" },
    }),
    unauthorized: (message = "Unauthorized", errorCode?: string) => ({
      success: false,
      message,
      data: null,
      error: { code: errorCode || "UNAUTHORIZED" },
    }),
    notFound: (message = "Not found", errorCode?: string) => ({
      success: false,
      message,
      data: null,
      error: { code: errorCode || "NOT_FOUND" },
    }),
    serverError: (message = "Server error", error?: Error) => ({
      success: false,
      message,
      data: null,
      error: {
        code: "INTERNAL_SERVER_ERROR",
        details: error ? { stack: error.stack } : undefined,
      },
    }),
  };
};

/**
 * Where we handle all the project charter requests
 *
 * This class has methods for creating and getting project charters.
 * Nothing fancy - we validate the data, call the service to do the work,
 * and send back a nice response.
 */
export class ProjectCharterController {
  /**
   * Create a new project charter
   *
   * First we check if all the required fields are there - we need quite a few
   * for a valid charter. If anything's missing, we send back an error.
   * Pretty straightforward stuff.
   */
  static async create(context: any) {
    const { body, user, success, badRequest, serverError } =
      ensureResponseFunctions(context);

    try {
      // Validate required fields
      if (
        !body.project_id ||
        !body.key_stakeholders ||
        !body.project_authority ||
        !body.project_description ||
        !body.objective_and_key_results ||
        !body.purpose ||
        !body.key_assumption ||
        !body.assumptions_constrains_risks ||
        !body.high_level_resources ||
        !body.high_level_milestones ||
        !body.statement_prediction_of_benefit ||
        body.approval === undefined
      ) {
        return badRequest("Missing required fields", "MISSING_REQUIRED_FIELDS");
      }

      // Create the project charter
      const { data, error } = await ProjectCharterService.create(
        body,
        user.id
      );

      if (error) {
        return serverError(error.message, error);
      }

      return success(data, "Project charter created successfully");
    } catch (error: any) {
      return serverError(error.message, error);
    }
  }

  /**
   * Get a project charter by ID
   *
   * Grabs a project charter using its ID. We pull the ID from the URL params,
   * ask the service to find it, and return it if it exists.
   *
   * If we can't find it, we just return a "not found" response.
   * Simple as that.
   */
  static async getById(context: any) {
    const { params, success, notFound, serverError } =
      ensureResponseFunctions(context);
    const { id } = params;

    try {
      const { data, error } = await ProjectCharterService.getById(id);

      if (error) {
        return serverError(error.message, error);
      }

      if (!data) {
        return notFound("Project charter not found");
      }

      return success(data, "Project charter retrieved successfully");
    } catch (error: any) {
      return serverError(error.message, error);
    }
  }

  /**
   * Get a project charter by project ID
   *
   * Sometimes you know the project ID but not the charter ID - this handles that case.
   * We grab the project ID from the URL, find its charter, and return it.
   *
   * Useful when you're working with a project and need to see its charter details.
   */
  static async getByProjectId(context: any) {
    const { params, success, notFound, serverError } =
      ensureResponseFunctions(context);
    const { projectId } = params;

    try {
      const { data, error } = await ProjectCharterService.getByProjectId(projectId);

      if (error) {
        return serverError(error.message, error);
      }

      if (!data) {
        return notFound("Project charter not found for this project");
      }

      return success(data, "Project charter retrieved successfully");
    } catch (error: any) {
      return serverError(error.message, error);
    }
  }

  /**
   * Update a project charter
   *
   * Updates an existing project charter with new data.
   * We check if there are any fields to update, then call the service.
   * If the charter doesn't exist, we return a not found response.
   */
  static async update(context: any) {
    const { params, body, user, success, notFound, badRequest, serverError } =
      ensureResponseFunctions(context);
    const { id } = params;

    try {
      // Check if there are any valid fields to update
      if (
        !body.key_stakeholders &&
        !body.project_authority &&
        !body.project_description &&
        !body.objective_and_key_results &&
        !body.purpose &&
        !body.key_assumption &&
        !body.assumptions_constrains_risks &&
        !body.high_level_resources &&
        !body.high_level_milestones &&
        !body.statement_prediction_of_benefit &&
        body.approval === undefined
      ) {
        return badRequest("No valid fields to update", "NO_VALID_FIELDS");
      }

      // Update the project charter
      const { data, error } = await ProjectCharterService.update(
        id,
        body,
        user.id
      );

      if (error) {
        if (error.message.includes("not found")) {
          return notFound("Project charter not found");
        }
        return serverError(error.message, error);
      }

      return success(data, "Project charter updated successfully");
    } catch (error: any) {
      return serverError(error.message, error);
    }
  }
}
