import { Elysia } from "elysia";
import { projectCharterRoutes } from "./routes";
import { apiResponse } from "../../middleware/api-response";

// Create an instance with the middleware applied
const projectCharterApp = new Elysia().use(apiResponse).use(projectCharterRoutes);

export * from "./service";

// Export the project charter module
export const projectCharters = projectCharterApp;

// Re-export for convenience
export * from "./schema";
export * from "./controller";
