import { Elysia, t } from "elysia";
import { ProjectCharterController } from "./controller";
import {
  createProjectCharterSchema,
  getProjectCharterSchema,
  getAllProjectChartersSchema,
  updateProjectCharterSchema,
} from "./schema";
import { requireActiveUser, checkRoles } from "../../middleware/auth";
import { UserRole } from "../../database/models/user-profile.model";

export const projectCharterRoutes = (app: Elysia) =>
  app.group(
    "/project-charters",
    (app) =>
      app
        // First ensure users are active
        .use(requireActiveUser)

        // Create a new project charter - only Staff Operations and Managers can create
        .post("/", ProjectCharterController.create, {
          body: createProjectCharterSchema.body,
          beforeHandle: [checkRoles([UserRole.Operation, UserRole.Manager])],
          detail: {
            tags: ["project-charters"],
            summary: "Create a new project charter",
            description: "Create a new project charter with the provided data",
            security: [{ bearerAuth: [] }],
          },
        })

        // Get a project charter by ID
        .get("/:id", ProjectCharterController.getById, {
          params: getProjectCharterSchema.params,
          detail: {
            tags: ["project-charters"],
            summary: "Get a project charter by ID",
            description: "Get a project charter by its unique identifier",
            security: [{ bearerAuth: [] }],
          },
        })

        // Get a project charter by project ID
        .get("/project/:projectId", ProjectCharterController.getByProjectId, {
          params: t.Object({
            projectId: t.String({
              format: "uuid",
              description: "Project ID",
            }),
          }),
          detail: {
            tags: ["project-charters"],
            summary: "Get a project charter by project ID",
            description:
              "Get a project charter associated with a specific project",
            security: [{ bearerAuth: [] }],
          },
        })

        // Update a project charter - only Staff Operations and Managers can update
        .put("/:id", ProjectCharterController.update, {
          params: updateProjectCharterSchema.params,
          body: updateProjectCharterSchema.body,
          beforeHandle: [checkRoles([UserRole.Operation, UserRole.Manager])],
          detail: {
            tags: ["project-charters"],
            summary: "Update a project charter",
            description: "Update an existing project charter with the provided data",
            security: [{ bearerAuth: [] }],
          },
        })

    // TODO: Add routes for getAll and delete
  );
