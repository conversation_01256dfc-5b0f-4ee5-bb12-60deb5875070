import { t } from "elysia";

// Common schema patterns
export const projectIdSchema = t.String({
  format: "uuid",
  description: "Project ID",
});

export const keyStakeholdersSchema = t.String({
  minLength: 2,
  maxLength: 1000,
  description: "Key stakeholders involved in the project",
});

export const projectAuthoritySchema = t.String({
  minLength: 2,
  maxLength: 1000,
  description: "Project authority and governance structure",
});

export const projectDescriptionSchema = t.String({
  minLength: 2,
  maxLength: 2000,
  description: "Detailed description of the project",
});

export const objectiveAndKeyResultsSchema = t.String({
  minLength: 2,
  maxLength: 2000,
  description: "Objectives and key results for the project",
});

export const purposeSchema = t.String({
  minLength: 2,
  maxLength: 1000,
  description: "Purpose of the project",
});

export const keyAssumptionSchema = t.String({
  minLength: 2,
  maxLength: 1000,
  description: "Key assumptions for the project",
});

export const assumptionsConstrainsRisksSchema = t.String({
  minLength: 2,
  maxLength: 2000,
  description: "Assumptions, constraints, and risks for the project",
});

export const highLevelResourcesSchema = t.String({
  minLength: 2,
  maxLength: 1000,
  description: "High-level resources required for the project",
});

export const highLevelMilestonesSchema = t.String({
  minLength: 2,
  maxLength: 1000,
  description: "High-level milestones for the project",
});

export const statementPredictionOfBenefitSchema = t.String({
  minLength: 2,
  maxLength: 2000,
  description: "Statement and prediction of benefits from the project",
});

export const approvalSchema = t.Boolean({
  description: "Whether the project charter is approved",
});

// Project Charter validation schemas
export const createProjectCharterSchema = {
  body: t.Object({
    project_id: projectIdSchema,
    key_stakeholders: keyStakeholdersSchema,
    project_authority: projectAuthoritySchema,
    project_description: projectDescriptionSchema,
    objective_and_key_results: objectiveAndKeyResultsSchema,
    purpose: purposeSchema,
    key_assumption: keyAssumptionSchema,
    assumptions_constrains_risks: assumptionsConstrainsRisksSchema,
    high_level_resources: highLevelResourcesSchema,
    high_level_milestones: highLevelMilestonesSchema,
    statement_prediction_of_benefit: statementPredictionOfBenefitSchema,
    approval: approvalSchema,
  }),
};

// Schemas for getting and updating project charters
export const getProjectCharterSchema = {
  params: t.Object({
    id: t.String({
      format: "uuid",
      description: "Project Charter ID",
    }),
  }),
};

export const getAllProjectChartersSchema = {
  query: t.Object({
    page: t.Optional(
      t.Numeric({
        minimum: 1,
        default: 1,
        description: "Page number (1-based)",
      })
    ),
    pageSize: t.Optional(
      t.Numeric({
        minimum: 1,
        maximum: 100,
        default: 10,
        description: "Number of items per page",
      })
    ),
    search: t.Optional(
      t.String({
        description: "Search term to filter results",
      })
    ),
  }),
};

export const updateProjectCharterSchema = {
  params: t.Object({
    id: t.String({
      format: "uuid",
      description: "Project Charter ID",
    }),
  }),
  body: t.Object({
    key_stakeholders: t.Optional(keyStakeholdersSchema),
    project_authority: t.Optional(projectAuthoritySchema),
    project_description: t.Optional(projectDescriptionSchema),
    objective_and_key_results: t.Optional(objectiveAndKeyResultsSchema),
    purpose: t.Optional(purposeSchema),
    key_assumption: t.Optional(keyAssumptionSchema),
    assumptions_constrains_risks: t.Optional(assumptionsConstrainsRisksSchema),
    high_level_resources: t.Optional(highLevelResourcesSchema),
    high_level_milestones: t.Optional(highLevelMilestonesSchema),
    statement_prediction_of_benefit: t.Optional(statementPredictionOfBenefitSchema),
    approval: t.Optional(approvalSchema),
  }),
};
