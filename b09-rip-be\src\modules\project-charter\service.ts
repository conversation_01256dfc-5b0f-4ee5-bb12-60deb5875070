/**
 * Project Charter Service
 *
 * This is where we do all the actual work with project charters - creating them,
 * fetching them from the database, etc.
 *
 * We use those handy dbUtils to keep the database code clean and consistent.
 * Everything returns a nice { data, error } object so it's easy to handle
 * success and failure cases.
 */
import { dbUtils } from "../../utils/database";
import { QueryOptions } from "../../utils/database.types";
import {
  ProjectCharter,
  CreateProjectCharterDto,
  UpdateProjectCharterDto,
} from "../../database/models/project-charter.model";
import { ProjectService } from "../project/service";

/**
 * All the project charter database operations live here
 *
 * Just a collection of static methods to work with project charters.
 */
export class ProjectCharterService {
  /**
   * The database table we're working with
   */
  private static readonly TABLE_NAME = "project_charters";

  /**
   * Create a new project charter
   *
   * First we check if the project actually exists - no point creating a charter
   * for a project that doesn't exist!
   *
   * Then we create the charter and update the project to point to it.
   * This keeps everything connected properly in both directions.
   *
   * We track who created it with the userId.
   */
  static async create(data: CreateProjectCharterDto, userId: string) {
    try {
      // First check if project exists
      const { data: project, error: projectError } = await ProjectService.getById(
        data.project_id
      );

      if (projectError || !project) {
        return {
          data: null,
          error: projectError || new Error("Project not found"),
        };
      }

      // Create the project charter
      const { data: projectCharter, error } = await dbUtils.create<ProjectCharter>(
        this.TABLE_NAME,
        data,
        userId
      );

      if (error) {
        return { data: null, error };
      }

      // Update the project with the project charter ID
      if (projectCharter) {
        await ProjectService.update(
          data.project_id,
          { project_charter_id: projectCharter.id },
          userId
        );
      }

      return { data: projectCharter, error: null };
    } catch (error: any) {
      return {
        data: null,
        error: new Error(`Failed to create project charter: ${error.message}`),
      };
    }
  }

  /**
   * Get a project charter by ID
   *
   * Simple one - just looks up a charter by its ID.
   * We use the dbUtils.getById helper to keep things clean.
   *
   * Returns null if we can't find it, or if something goes wrong.
   */
  static async getById(id: string) {
    try {
      const { data, error } = await dbUtils.getById<ProjectCharter>(
        this.TABLE_NAME,
        id
      );

      if (error) {
        return { data: null, error };
      }

      return { data, error: null };
    } catch (error: any) {
      return {
        data: null,
        error: new Error(`Failed to get project charter: ${error.message}`),
      };
    }
  }

  /**
   * Get a project charter by project ID
   *
   * When you know the project ID but not the charter ID, use this.
   *
   * We just filter the charters by project_id and grab the first one we find.
   * A project should only have one charter anyway.
   */
  static async getByProjectId(projectId: string) {
    try {
      const { data, error } = await dbUtils.getAll<ProjectCharter>(
        this.TABLE_NAME,
        {
          filters: [{ field: "project_id", value: projectId }],
        }
      );

      if (error) {
        return { data: null, error };
      }

      // Return the first project charter found (there should only be one)
      return { data: data && data.length > 0 ? data[0] : null, error: null };
    } catch (error: any) {
      return {
        data: null,
        error: new Error(`Failed to get project charter: ${error.message}`),
      };
    }
  }

  /**
   * Update a project charter
   *
   * First checks if the charter exists, then updates it with the new data.
   * Only updates the fields that are provided in the data object.
   *
   * @param id The project charter ID
   * @param data The data to update
   * @param userId The user ID for audit trail
   * @returns The updated project charter or error
   */
  static async update(id: string, data: UpdateProjectCharterDto, userId: string) {
    try {
      // First check if project charter exists
      const { data: existingCharter, error: getError } = await this.getById(id);

      if (getError || !existingCharter) {
        return {
          data: null,
          error: getError || new Error("Project charter not found"),
        };
      }

      // Update the project charter
      const { data: updatedCharter, error: updateError } =
        await dbUtils.update<ProjectCharter>(this.TABLE_NAME, id, data, userId);

      if (updateError) {
        return { data: null, error: updateError };
      }

      return { data: updatedCharter, error: null };
    } catch (error: any) {
      return {
        data: null,
        error: new Error(`Failed to update project charter: ${error.message}`),
      };
    }
  }
}
