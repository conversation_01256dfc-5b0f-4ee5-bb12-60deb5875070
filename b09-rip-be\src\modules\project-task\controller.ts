import { projectTaskService, ProjectTaskService } from "./service";
import { IProjectTaskService } from "./service.interface";
import {
  CreateProjectTaskDto,
  UpdateProjectTaskDto,
} from "../../database/models/project-task.model";
import {
  UserRole,
  UserProfile,
} from "../../database/models/user-profile.model";
import { TaskStatus } from "../../database/models/task.model";
import { FilterOption, QueryOptions } from "../../utils/database.types";
import { AuthUser } from "../../middleware/auth";

/**
 * Interface for the controller context
 */
export interface ProjectTaskContext {
  params: {
    id?: string;
    projectId?: string;
  };
  body: {
    description?: string;
    initial_date?: string;
    due_date?: string;
    project_id?: string;
    employee_id?: string;
    assigned_by?: string;
    completion_status?: TaskStatus;
    weekly_log_id?: string;
  };
  query: {
    page?: number | string;
    limit?: number | string;
    sort_by?: string;
    sort_order?: string;
    project_id?: string;
    employee_id?: string;
    assigned_by?: string;
    completion_status?: string;
    weekly_log_id?: string;
  };
  // Extended user information including profile
  user: AuthUser & { employee_id?: string; profile?: UserProfile };
  // User profile from middleware
  profile?: UserProfile;
  // Response functions from middleware
  success: (data: any, message?: string) => ApiResponse;
  forbidden: (message?: string, errorCode?: string) => ApiErrorResponse;
  unauthorized: (message?: string, errorCode?: string) => ApiErrorResponse;
  notFound: (message?: string, errorCode?: string) => ApiErrorResponse;
  serverError: (message?: string, error?: Error) => ApiErrorResponse;
  badRequest: (message?: string, errorCode?: string) => ApiErrorResponse;
}

/**
 * Interface for API success response
 */
interface ApiResponse {
  success: true;
  message: string;
  data: any;
}

/**
 * Interface for API error response
 */
interface ApiErrorResponse {
  success: false;
  message: string;
  data: null;
  error: {
    code: string;
    details?: any;
  };
}

/**
 * Helper function to ensure API response functions are available
 * @returns Response functions
 */
function ensureResponseFunctions(
  context: Partial<ProjectTaskContext>
): ProjectTaskContext {
  if (typeof context.success !== "function") {
    console.error("API Response middleware functions not available in context");

    return {
      ...(context as any),
      success: (data: any, message = "Operation successful") => ({
        success: true,
        message,
        data,
      }),
      forbidden: (message = "Forbidden", errorCode?: string) => ({
        success: false,
        message,
        data: null,
        error: { code: errorCode || "FORBIDDEN" },
      }),
      unauthorized: (message = "Unauthorized", errorCode?: string) => ({
        success: false,
        message,
        data: null,
        error: { code: errorCode || "UNAUTHORIZED" },
      }),
      notFound: (message = "Not found", errorCode?: string) => ({
        success: false,
        message,
        data: null,
        error: { code: errorCode || "NOT_FOUND" },
      }),
      serverError: (message = "Server error", error?: Error) => ({
        success: false,
        message,
        data: null,
        error: {
          code: "INTERNAL_SERVER_ERROR",
          details: error ? { stack: error.stack } : undefined,
        },
      }),
      badRequest: (message = "Bad Request", errorCode?: string) => ({
        success: false,
        message,
        data: null,
        error: { code: errorCode || "BAD_REQUEST" },
      }),
    };
  }

  return context as ProjectTaskContext;
}

/**
 * Process data to handle empty strings properly
 * @param data The input data object
 * @returns Processed data with empty strings converted to null for specific fields
 */
function processRequestData<T extends Record<string, any>>(data: T): T {
  const result = { ...data };
  
  // Handle weekly_log_id field
  if (Object.prototype.hasOwnProperty.call(result, 'weekly_log_id') && result.weekly_log_id === '') {
    (result as unknown as { weekly_log_id: string | null }).weekly_log_id = null;
  }
  
  return result;
}

export class ProjectTaskController {
  constructor(private readonly projectTaskService: IProjectTaskService) {}

  /**
   * Create a new project task
   */
  async create(context: Partial<ProjectTaskContext>) {
    const ctx = ensureResponseFunctions(context);
    const { body, user, profile } = ctx;
    const { success, serverError, badRequest } = ctx;

    // Validate user authentication
    if (!user || !user.id) {
      return badRequest("User ID not available", "USER_NOT_AUTHENTICATED");
    }

    // Validate required fields
    if (
      !body.description ||
      !body.project_id ||
      !body.employee_id ||
      !body.initial_date ||
      !body.due_date ||
      !body.assigned_by
    ) {
      return badRequest("Missing required fields", "MISSING_REQUIRED_FIELDS");
    }

    try {
      // Process request data
      const processedData = {
        ...body,
        weekly_log_id: undefined // Will be assigned by scheduler
      } as CreateProjectTaskDto;
      
      // Send processed data to service
      const { data, error } = await this.projectTaskService.create(
        processedData,
        user.id
      );

      if (error) {
        console.error("Error in ProjectTaskService.create:", error);
        return serverError(error.message, error);
      }

      return success(data, "Project task created successfully");
    } catch (err) {
      console.error("Unexpected error in ProjectTaskController.create:", err);
      return serverError(
        "Unexpected error occurred",
        err instanceof Error ? err : undefined
      );
    }
  }

  /**
   * Get all project tasks with search, filter, and pagination
   */
  async getAll(context: Partial<ProjectTaskContext>) {
    const ctx = ensureResponseFunctions(context);
    const { query = {} } = ctx;
    const { success, serverError } = ctx;

    try {
      // Build query options from request parameters
      const options: QueryOptions = {};

      // Handle pagination
      options.pagination = {
        page: Number(query.page) || 1,
        pageSize: Number(query.limit) || 10,
      };
      // Handle sorting
      if (query.sort_by) {
        options.sort = {
          field: query.sort_by,
          direction:
            query.sort_order === "asc" || query.sort_order === "desc"
              ? query.sort_order
              : "asc",
        };
      }

      // Handle filters
      const filters: FilterOption[] = [];

      // Add filters for any provided query parameters
      if (query.project_id) {
        filters.push({
          field: "project_id",
          operator: "eq",
          value: query.project_id,
        });
      }

      if (query.employee_id) {
        filters.push({
          field: "employee_id",
          operator: "eq",
          value: query.employee_id,
        });
      }

      if (query.assigned_by) {
        filters.push({
          field: "assigned_by",
          operator: "eq",
          value: query.assigned_by,
        });
      }

      if (query.completion_status) {
        filters.push({
          field: "completion_status",
          operator: "eq",
          value: query.completion_status,
        });
      }
      
      // Handle weekly_log_id filter
      if (query.weekly_log_id) {
        if (query.weekly_log_id === '') {
          // If empty string, filter for null values
          filters.push({
            field: "weekly_log_id",
            operator: "is",
            value: null,
          });
        } else {
          // Otherwise, filter for the specified value
          filters.push({
            field: "weekly_log_id",
            operator: "eq",
            value: query.weekly_log_id,
          });
        }
      }

      if (filters.length > 0) {
        options.filters = filters;
      }

      const { data, error, pagination } = await this.projectTaskService.getAll(
        options
      );

      if (error) {
        console.error("Error in ProjectTaskService.getAll:", error);
        return serverError(error.message, error);
      }

      return success(
        {
          items: data,
          pagination,
        },
        "Project tasks retrieved successfully"
      );
    } catch (err) {
      console.error("Error in ProjectTaskController.getAll:", err);
      return serverError(
        "Unexpected error occurred",
        err instanceof Error ? err : undefined
      );
    }
  }

  /**
   * Get project task by ID
   */
  async getById(context: Partial<ProjectTaskContext>) {
    const ctx = ensureResponseFunctions(context);
    const { params } = ctx;
    const { success, serverError, notFound, badRequest } = ctx;

    try {
      const { id } = params;

      if (!id) {
        return badRequest("Task ID is required", "MISSING_TASK_ID");
      }

      const { data, error } = await this.projectTaskService.getById(id);

      if (error) {
        console.error("Error in ProjectTaskService.getById:", error);
        return serverError(error.message, error);
      }

      if (!data) {
        return notFound("Project task not found", "TASK_NOT_FOUND");
      }

      return success(data, "Project task retrieved successfully");
    } catch (err) {
      console.error("Error in ProjectTaskController.getById:", err);
      return serverError(
        "Unexpected error occurred",
        err instanceof Error ? err : undefined
      );
    }
  }

  /**
   * Get project tasks by project ID
   */
  async getByProjectId(context: Partial<ProjectTaskContext>) {
    const ctx = ensureResponseFunctions(context);
    const { params, query = {} } = ctx;
    const { success, serverError, notFound, badRequest } = ctx;

    try {
      const { projectId } = params;

      if (!projectId) {
        return badRequest("Project ID is required", "PROJECT_ID_REQUIRED");
      }

      // Build query options from request parameters
      const options: QueryOptions = {};

      // Handle pagination
      options.pagination = {
        page: Number(query.page) || 1,
        pageSize: Number(query.limit) || 10,
      };
      // Handle sorting
      if (query.sort_by) {
        options.sort = {
          field: query.sort_by,
          direction:
            query.sort_order === "asc" || query.sort_order === "desc"
              ? query.sort_order
              : "asc",
        };
      }

      const { data, error, pagination } =
        await this.projectTaskService.getByProjectId(projectId, options);

      if (error) {
        console.error("Error in ProjectTaskService.getByProjectId:", error);
        return serverError(error.message, error);
      }

      return success(
        {
          items: data,
          pagination,
        },
        "Project tasks retrieved successfully"
      );
    } catch (err) {
      console.error("Error in ProjectTaskController.getByProjectId:", err);
      return serverError(
        "Unexpected error occurred",
        err instanceof Error ? err : undefined
      );
    }
  }

  /**
   * Update a project task (including status updates)
   */
  async update(context: Partial<ProjectTaskContext>) {
    const ctx = ensureResponseFunctions(context);
    const { params, body, user } = ctx;
    const { success, serverError, notFound, badRequest } = ctx;

    // Validate user authentication
    if (!user || !user.id) {
      return badRequest("User ID not available", "USER_NOT_AUTHENTICATED");
    }

    try {
      const { id } = params;

      if (!id) {
        return badRequest("Task ID is required", "MISSING_TASK_ID");
      }

      // Validate request body
      if (Object.keys(body || {}).length === 0) {
        return badRequest("No update data provided", "NO_UPDATE_DATA");
      }

      // Check if this is a status-only update
      const isStatusUpdate = Object.keys(body).length === 1 && body.completion_status;
      
      // Process request data to handle empty strings for weekly_log_id
      const processedData = processRequestData(body as UpdateProjectTaskDto);

      // For all updates, use the update service method
      const { data, error } = await this.projectTaskService.update(
        id,
        processedData,
        user.id
      );
      if (error) {
        if (error.message.includes("not found")) {
          return notFound("Project task not found", "TASK_NOT_FOUND");
        }
        console.error("Error in ProjectTaskService.update:", error);
        return serverError(error.message, error);
      }

      if (!data) {
        return notFound("Project task not found", "TASK_NOT_FOUND");
      }

      return success(data, "Project task updated successfully");
    } catch (err) {
      console.error("Error in ProjectTaskController.update:", err);
      return serverError(
        "Unexpected error occurred",
        err instanceof Error ? err : undefined
      );
    }
  }

  /**
   * Delete a project task
   */
  async delete(context: Partial<ProjectTaskContext>) {
    const ctx = ensureResponseFunctions(context);
    const { params, user } = ctx;
    const { success, serverError, notFound, badRequest } = ctx;

    // Validate user authentication
    if (!user || !user.id) {
      return badRequest("User ID not available", "USER_NOT_AUTHENTICATED");
    }

    try {
      const { id } = params;

      if (!id) {
        return badRequest("Task ID is required", "MISSING_TASK_ID");
      }

      const { data, error } = await this.projectTaskService.delete(id, user.id);

      if (error) {
        if (error.message.includes("not found")) {
          return notFound("Project task not found", "TASK_NOT_FOUND");
        }
        console.error("Error in ProjectTaskService.delete:", error);
        return serverError(error.message, error);
      }

      if (!data) {
        return notFound("Project task not found", "TASK_NOT_FOUND");
      }

      return success(null, "Project task deleted successfully");
    } catch (err) {
      console.error("Error in ProjectTaskController.delete:", err);
      return serverError(
        "Unexpected error occurred",
        err instanceof Error ? err : undefined
      );
    }
  }
}

// Create a controller instance with the default service for backward compatibility
export const projectTaskController = new ProjectTaskController(
  projectTaskService
);

// For backward compatibility with existing code
export const ProjectTaskControllerStatic = {
  create: projectTaskController.create.bind(projectTaskController),
  getById: projectTaskController.getById.bind(projectTaskController),
  getAll: projectTaskController.getAll.bind(projectTaskController),
  getByProjectId: projectTaskController.getByProjectId.bind(
    projectTaskController
  ),
  update: projectTaskController.update.bind(projectTaskController),
  delete: projectTaskController.delete.bind(projectTaskController),
};

// Factory function to create a controller instance
export function createProjectTaskController(service: IProjectTaskService) {
  return new ProjectTaskController(service);
}
