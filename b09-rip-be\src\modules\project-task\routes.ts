import { Elysia } from "elysia";
import {
  projectT<PERSON><PERSON><PERSON><PERSON><PERSON>,
  ProjectTaskControllerStatic,
} from "./controller";
import {
  createProjectTaskSchema,
  getProjectTaskByIdSchema,
  getProjectTasksByProjectIdSchema,
  updateProjectTaskSchema,
  deleteProjectTaskSchema,
  getAllProjectTasksSchema,
} from "./schema";
import { auth } from "../../middleware/auth";

export const projectTaskRoutes = new Elysia({ prefix: "/project-tasks" })
  .use(auth)
  
  // Create a project task
  .post("/", (context) => projectTaskController.create(context), {
    body: createProjectTaskSchema,
    detail: {
      summary: "Create a new project task",
      tags: ["Project Tasks"],
    },
  })
  
  // Get all project tasks with filtering and pagination
  .get("/", (context) => projectTaskController.getAll(context), {
    query: getAllProjectTasksSchema,
    detail: {
      summary: "Get all project tasks",
      tags: ["Project Tasks"],
    },
  })
  
  // Get project task by ID
  .get("/:id", (context) => projectTaskController.getById(context), {
    params: getProjectTaskByIdSchema,
    detail: {
      summary: "Get project task by ID",
      tags: ["Project Tasks"],
    },
  })
  
  // Get project tasks by project ID
  .get(
    "/project/:projectId",
    (context) => projectTaskController.getByProjectId(context),
    {
      params: getProjectTasksByProjectIdSchema.params,
      query: getProjectTasksByProjectIdSchema.query,
      detail: {
        summary: "Get project tasks by project ID",
        tags: ["Project Tasks"],
      },
    }
  )
  
  // Update a project task (including status updates)
  .put("/:id", (context) => projectTaskController.update(context), {
    params: updateProjectTaskSchema.params,
    body: updateProjectTaskSchema.body,
    detail: {
      summary: "Update a project task",
      tags: ["Project Tasks"],
    },
  })
  
  // Delete a project task
  .delete("/:id", (context) => projectTaskController.delete(context), {
    params: deleteProjectTaskSchema,
    detail: {
      summary: "Delete a project task (soft delete)",
      tags: ["Project Tasks"],
    },
  });