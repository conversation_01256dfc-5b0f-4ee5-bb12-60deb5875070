import { t } from "elysia";
import { TaskStatus } from "../../database/models/task.model";

// Helper function to handle empty strings in weekly_log_id
const createWeeklyLogIdField = () => {
  return t.Optional(
    t.Any({ 
      description: "Weekly log ID (optional, UUID format or empty string)",
      transform: (value: string) => value === "" ? null : value
    })
  );
};

// Schema for creating a project task
export const createProjectTaskSchema = t.Object({
  description: t.String({
    minLength: 3,
    maxLength: 255,
    description: "Task description (min 3 characters, max 255 characters)",
  }),
  initial_date: t.String({
    pattern: "^\\d{4}-\\d{2}-\\d{2}$",
    description: "Initial date in format YYYY-MM-DD",
  }),
  due_date: t.String({
    pattern: "^\\d{4}-\\d{2}-\\d{2}$",
    description: "Due date in format YYYY-MM-DD",
  }),
  project_id: t.String({
    format: "uuid",
    description: "Project ID (UUID format)",
  }),
  employee_id: t.String({
    format: "uuid",
    description: "Employee ID assigned to the task (UUID format)",
  }),
  assigned_by: t.String({
    format: "uuid",
    description: "User ID who assigned the task (UUID format)",
  }),
  completion_status: t.Optional(
    t.Enum(TaskStatus, {
      description:
        "Task completion status (not_completed, on_progress, completed)",
      default: TaskStatus.NOT_COMPLETED,
    })
  ),
});

// Schema for getting a project task by ID
export const getProjectTaskByIdSchema = t.Object({
  id: t.String({
    format: "uuid",
    description: "The ID of the project task to retrieve",
  }),
});

// Schema for getting project tasks by project ID
export const getProjectTasksByProjectIdSchema = {
  params: t.Object({
    projectId: t.String({
      format: "uuid",
      description: "The ID of the project",
    }),
  }),
  query: t.Object({
    page: t.Optional(
      t.Numeric({
        description: "Page number for pagination",
        default: 1,
      })
    ),
    limit: t.Optional(
      t.Numeric({
        description: "Number of items per page",
        default: 10,
      })
    ),
    sort_by: t.Optional(
      t.String({
        description: "Field to sort by",
      })
    ),
    sort_order: t.Optional(
      t.String({
        description: "Sort direction (asc or desc)",
        default: "asc",
      })
    ),
  }),
};

// Schema for getting all project tasks
export const getAllProjectTasksSchema = t.Object({
  page: t.Optional(
    t.Numeric({
      description: "Page number for pagination",
      default: 1,
    })
  ),
  limit: t.Optional(
    t.Numeric({
      description: "Number of items per page",
      default: 10,
    })
  ),
  sort_by: t.Optional(
    t.String({
      description: "Field to sort by",
    })
  ),
  sort_order: t.Optional(
    t.String({
      enum: ["asc", "desc"],
      description: "Sort direction (asc or desc)",
      default: "asc",
    })
  ),
  project_id: t.Optional(
    t.String({
      format: "uuid",
      description: "Filter by project ID (UUID format)",
    })
  ),
  employee_id: t.Optional(
    t.String({
      format: "uuid",
      description: "Filter by employee ID (UUID format)",
    })
  ),
  assigned_by: t.Optional(
    t.String({
      format: "uuid",
      description: "Filter by assigned by ID (UUID format)",
    })
  ),
  completion_status: t.Optional(
    t.Enum(TaskStatus, {
      description:
        "Filter by completion status (not_completed, on_progress, completed)",
    })
  ),
  weekly_log_id: createWeeklyLogIdField(),
});

// Schema for updating a project task (including status updates)
export const updateProjectTaskSchema = {
  params: t.Object({
    id: t.String({
      format: "uuid",
      description: "The ID of the project task",
    }),
  }),
  body: t.Object({
    description: t.Optional(
      t.String({
        minLength: 3,
        maxLength: 255,
        description: "Task description (min 3 characters, max 255 characters)",
      })
    ),
    initial_date: t.Optional(
      t.String({
        pattern: "^\\d{4}-\\d{2}-\\d{2}$",
        description: "Initial date in format YYYY-MM-DD",
      })
    ),
    due_date: t.Optional(
      t.String({
        pattern: "^\\d{4}-\\d{2}-\\d{2}$",
        description: "Due date in format YYYY-MM-DD",
      })
    ),
    employee_id: t.Optional(
      t.String({
        format: "uuid",
        description: "Employee ID assigned to the task (UUID format)",
      })
    ),
    assigned_by: t.Optional(
      t.String({
        format: "uuid",
        description: "User ID who assigned the task (UUID format)",
      })
    ),
    completion_status: t.Optional(
      t.Enum(TaskStatus, {
        description:
          "Task completion status (not_completed, on_progress, completed)",
      })
    ),
    weekly_log_id: createWeeklyLogIdField(),
    project_id: t.Optional(
      t.String({
        format: "uuid",
        description: "Project ID (UUID format)",
      })
    ),
  }),
};

// Schema for deleting a project task
export const deleteProjectTaskSchema = t.Object({
  id: t.String({
    format: "uuid",
    description: "The ID of the project task to delete",
  }),
});