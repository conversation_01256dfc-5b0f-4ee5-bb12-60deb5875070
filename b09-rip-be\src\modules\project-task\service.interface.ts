import {
  CreateProjectTaskDto,
  UpdateProjectTaskDto,
  ProjectTask
} from "../../database/models/project-task.model";
import { TaskStatus } from "../../database/models/task.model";
import { QueryOptions } from "../../utils/database.types";

// Enhanced project task interface that extends the original model
export interface EnhancedProjectTask extends ProjectTask {
  employee_name?: string;
  employee_role?: string;
  employee_department?: string;
  assigned_by_name?: string;
  assigned_by_role?: string;
  assigned_by_department?: string;
  project_name?: string;
}

export interface IProjectTaskService {
  create(data: CreateProjectTaskDto, userId: string): Promise<{
    data: EnhancedProjectTask | null;
    error: Error | null
  }>;
  
  getById(id: string): Promise<{
    data: EnhancedProjectTask | null;
    error: Error | null
  }>;
  
  getAll(options?: QueryOptions): Promise<{
    data: { items: EnhancedProjectTask[] } | null;
    pagination?: {
      page: number;
      pageSize: number;
      totalItems: number;
      totalPages: number;
    };
    error: Error | null
  }>;
  
  getByProjectId(projectId: string, options?: QueryOptions): Promise<{
    data: { items: EnhancedProjectTask[] } | null;
    pagination?: {
      page: number;
      pageSize: number;
      totalItems: number;
      totalPages: number;
    };
    error: Error | null
  }>;
  
  update(id: string, data: UpdateProjectTaskDto, userId: string): Promise<{
    data: EnhancedProjectTask | null;
    error: Error | null
  }>;
  
  delete(id: string, userId: string): Promise<{
    data: EnhancedProjectTask | null;
    error: Error | null
  }>;
}