import { dbUtils } from "../../utils/database";
import {
  ProjectTask,
  CreateProjectTaskDto,
  UpdateProjectTaskDto,
} from "../../database/models/project-task.model";
import { FilterOption, QueryOptions } from "../../utils/database.types";
import { TaskStatus } from "../../database/models/task.model";
import { IProjectTaskService } from "./service.interface";
import { UserProfile } from "../../database/models/user-profile.model";

// Define enhanced response type without modifying the original model
interface ProjectTaskResponse extends ProjectTask {
  employee_name?: string;
  employee_role?: string;
  employee_department?: string;
  assigned_by_name?: string;
  assigned_by_role?: string;
  assigned_by_department?: string;
  project_name?: string;
}

export class ProjectTaskServiceImpl implements IProjectTaskService {
  private readonly TABLE_NAME = "project_tasks";
  private readonly USER_PROFILES_TABLE = "user_profiles";
  private readonly PROJECTS_TABLE = "projects";
  private readonly EMPLOYEES_TABLE = "employees";

  /**
   * Create a new project task
   * @param data Project task data
   * @param userId User ID for audit
   * @returns Created project task
   */
  async create(data: CreateProjectTaskDto, userId: string) {
    try {
      const result = await dbUtils.create<ProjectTask>(this.TABLE_NAME, data, userId);
      
      if (result.data && !result.error) {
        // Enhance the newly created task with related data
        const enhancedData = await this.enhanceWithRelatedData(result.data);
        return { data: enhancedData, error: null };
      }
      
      return result;
    } catch (error) {
      return {
        data: null,
        error:
          error instanceof Error
            ? error
            : new Error("Failed to create project task"),
      };
    }
  }

  /**
   * Get a project task by ID
   * @param id Project task ID
   * @returns Project task
   */
  async getById(id: string) {
    try {
      console.log('Getting project task by ID:', id);
      
      const { data, error } = await dbUtils.getById<ProjectTask>(
        this.TABLE_NAME,
        id
      );

      console.log('Raw data from database:', data ? 'Data found' : 'No data found');
      if (error) {
        console.error('Error getting task by ID:', error);
      }

      if (data && !error) {
        // Enhance with related data
        console.log('Enhancing project task with related data...');
        const enhancedData = await this.enhanceWithRelatedData(data);
        console.log('Enhanced data result:', JSON.stringify({
          id: enhancedData.id,
          employee_name: enhancedData.employee_name,
          employee_role: enhancedData.employee_role,
          employee_department: enhancedData.employee_department,
          assigned_by_name: enhancedData.assigned_by_name,
          assigned_by_role: enhancedData.assigned_by_role,
          assigned_by_department: enhancedData.assigned_by_department,
          project_name: enhancedData.project_name
        }));
        
        return { data: enhancedData, error: null };
      }

      return { data, error };
    } catch (error) {
      console.error('Error in getById:', error);
      return {
        data: null,
        error:
          error instanceof Error
            ? error
            : new Error("Failed to get project task"),
      };
    }
  }

  /**
   * Get all project tasks with optional filtering and pagination
   * @param options Query options
   * @returns Paginated project tasks
   */
  async getAll(options: QueryOptions = {}) {
    try {
      const result = await dbUtils.getAll<ProjectTask>(
        this.TABLE_NAME,
        options
      );

      // Transform the result to match the interface
      if (result.data) {
        // Enhance each task with related data
        const enhancedItems = await Promise.all(
          result.data.map(item => this.enhanceWithRelatedData(item))
        );

        return {
          data: { items: enhancedItems },
          pagination: result.result
            ? {
                page: result.result.page,
                pageSize: result.result.pageSize,
                totalItems: result.result.total,
                totalPages: result.result.pageCount,
              }
            : undefined,
          error: null,
        };
      }

      return {
        data: null,
        pagination: undefined,
        error: result.error,
      };
    } catch (error) {
      return {
        data: null,
        error:
          error instanceof Error
            ? error
            : new Error("Failed to get project tasks"),
      };
    }
  }

  /**
   * Get project tasks by project ID
   * @param projectId Project ID
   * @param options Query options
   * @returns Project tasks for the specified project
   */
  async getByProjectId(projectId: string, options: QueryOptions = {}) {
    try {
      const filter: FilterOption = {
        field: "project_id",
        operator: "eq",
        value: projectId,
      };

      const queryOptions: QueryOptions = {
        ...options,
        filters: options.filters ? [...options.filters, filter] : [filter],
      };

      // Use the getAll method to leverage the transformation logic
      return await this.getAll(queryOptions);
    } catch (error) {
      return {
        data: null,
        error:
          error instanceof Error
            ? error
            : new Error("Failed to get project tasks by project ID"),
      };
    }
  }

  /**
   * Update a project task
   * @param id Project task ID
   * @param data Project task data
   * @param userId User ID for audit
   * @returns Updated project task
   */
  async update(id: string, data: UpdateProjectTaskDto, userId: string) {
    try {
      // First check if the task exists
      const { data: task, error: getError } =
        await dbUtils.getById<ProjectTask>(this.TABLE_NAME, id);

      if (getError || !task) {
        return {
          data: null,
          error: getError || new Error("Project task not found"),
        };
      }

      // Update the task with new data
      const { data: updatedTask, error } = await dbUtils.update<ProjectTask>(
        this.TABLE_NAME,
        id,
        data,
        userId
      );

      if (updatedTask && !error) {
        // Enhance with related data
        const enhancedData = await this.enhanceWithRelatedData(updatedTask);
        return { data: enhancedData, error: null };
      }

      return { data: updatedTask, error };
    } catch (error) {
      return {
        data: null,
        error:
          error instanceof Error
            ? error
            : new Error("Failed to update project task"),
      };
    }
  }

  /**
   * Delete a project task (soft delete)
   * @param id Project task ID
   * @param userId User ID for audit
   * @returns Deleted project task
   */
  async delete(id: string, userId: string) {
    try {
      // First check if the task exists
      const { data: task, error: getError } =
        await dbUtils.getById<ProjectTask>(this.TABLE_NAME, id);

      if (getError || !task) {
        return {
          data: null,
          error: getError || new Error("Project task not found"),
        };
      }

      // Soft delete the task
      const { data, error } = await dbUtils.softDelete<ProjectTask>(
        this.TABLE_NAME,
        id,
        userId
      );

      if (data && !error) {
        // Enhance with related data
        const enhancedData = await this.enhanceWithRelatedData(data);
        return { data: enhancedData, error: null };
      }

      return { data, error };
    } catch (error) {
      return {
        data: null,
        error:
          error instanceof Error
            ? error
            : new Error("Failed to delete project task"),
      };
    }
  }
  
  /**
   * Enhance a project task with related data
   * @param task The project task to enhance
   * @returns Enhanced project task with related data
   */
  private async enhanceWithRelatedData(task: ProjectTask): Promise<ProjectTaskResponse> {
    const enhancedTask: ProjectTaskResponse = { ...task };
    
    try {
      console.log('Enhancing task with ID:', task.id);
      
      // Get employee details (from employees table first, then user_profiles)
      if (task.employee_id) {
        console.log('Fetching employee data for employee ID:', task.employee_id);
        const { data: employee, error: employeeError } = await dbUtils
          .query(this.EMPLOYEES_TABLE)
          .raw.select(
            `
            id,
            profile_id,
            department
            `
          )
          .eq("id", task.employee_id)
          .is("deleted_at", null)
          .single();
        
        if (employee && !employeeError) {
          console.log('Found employee data:', employee);
          enhancedTask.employee_department = employee.department;
          
          // Now get the user profile info for this employee
          if (employee.profile_id) {
            const { data: userProfile, error: userProfileError } = await dbUtils
              .query(this.USER_PROFILES_TABLE)
              .raw.select(
                `
                id,
                fullname,
                role
                `
              )
              .eq("id", employee.profile_id)
              .is("deleted_at", null)
              .single();
            
            if (userProfile && !userProfileError) {
              console.log('Found user profile data for employee:', userProfile);
              enhancedTask.employee_name = userProfile.fullname;
              enhancedTask.employee_role = userProfile.role;
            } else {
              console.log('Error or no data found for user profile:', userProfileError);
            }
          }
        } else {
          console.log('Error or no data found for employee:', employeeError);
        }
      }
      
      // Get assigned_by details (from employees table first, then user_profiles)
      if (task.assigned_by) {
        console.log('Fetching employee data for assigned_by ID:', task.assigned_by);
        const { data: employee, error: employeeError } = await dbUtils
          .query(this.EMPLOYEES_TABLE)
          .raw.select(
            `
            id,
            profile_id,
            department
            `
          )
          .eq("id", task.assigned_by)
          .is("deleted_at", null)
          .single();
        
        if (employee && !employeeError) {
          console.log('Found employee data for assigned_by:', employee);
          enhancedTask.assigned_by_department = employee.department;
          
          // Now get the user profile info for this assigned_by
          if (employee.profile_id) {
            const { data: userProfile, error: userProfileError } = await dbUtils
              .query(this.USER_PROFILES_TABLE)
              .raw.select(
                `
                id,
                fullname,
                role
                `
              )
              .eq("id", employee.profile_id)
              .is("deleted_at", null)
              .single();
            
            if (userProfile && !userProfileError) {
              console.log('Found user profile data for assigned_by:', userProfile);
              enhancedTask.assigned_by_name = userProfile.fullname;
              enhancedTask.assigned_by_role = userProfile.role;
            } else {
              console.log('Error or no data found for user profile:', userProfileError);
            }
          }
        } else {
          console.log('Error or no data found for employee:', employeeError);
        }
      }
      
      // Get project name
      if (task.project_id) {
        console.log('Fetching project data for ID:', task.project_id);
        const { data: project, error: projectError } = await dbUtils
          .query(this.PROJECTS_TABLE)
          .raw.select(
            `
            id,
            project_name
            `
          )
          .eq("id", task.project_id)
          .is("deleted_at", null)
          .single();
          
        if (project && !projectError) {
          console.log('Found project data:', project);
          enhancedTask.project_name = project.project_name;
        } else {
          console.log('Error or no data found for project:', projectError);
        }
      }
      
      console.log('Enhanced task result:', JSON.stringify(enhancedTask));
    } catch (error) {
      console.error('Error fetching related data:', error);
      // Continue with partial or no related data
    }
    
    return enhancedTask;
  }
}

// Create a singleton instance for backward compatibility
export const projectTaskService = new ProjectTaskServiceImpl();

// For backward compatibility with existing code
export const ProjectTaskService = {
  create: (data: CreateProjectTaskDto, userId: string) => projectTaskService.create(data, userId),
  getById: (id: string) => projectTaskService.getById(id),
  getAll: (options: QueryOptions = {}) => projectTaskService.getAll(options),
  getByProjectId: (projectId: string, options: QueryOptions = {}) => projectTaskService.getByProjectId(projectId, options),
  update: (id: string, data: UpdateProjectTaskDto, userId: string) => projectTaskService.update(id, data, userId),
  delete: (id: string, userId: string) => projectTaskService.delete(id, userId),
};