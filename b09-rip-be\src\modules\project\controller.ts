import { ProjectService } from "./service";
import { QueryOptions } from "../../utils/database.types";
import { UserRole } from "../../database/models/user-profile.model";

export class ProjectController {
  /**
   * Create a new project and automatically create a corresponding KPI Project
   */
  static async create(context: any) {
    const { body, user, success, serverError, badRequest } = context;

    try {
      // Validate required fields
      if (
        !body.project_name ||
        !body.organization_id ||
        !body.pic_project ||
        !body.objectives
      ) {
        return badRequest("Missing required fields", "MISSING_REQUIRED_FIELDS");
      }

      // Role validation is now handled at the route level

      // Create the project (gantt_chart_id and project_charter_id will be auto-generated in the service)
      const { data, error } = await ProjectService.create(body, user.id);

      if (error) {
        return serverError(error.message, error);
      }

      return success(data, "Project created successfully with KPI Project");
    } catch (error: any) {
      console.error("Unexpected error in ProjectController.create:", error);
      return serverError(
        "An unexpected error occurred while creating the project",
        error instanceof Error ? error : undefined
      );
    }
  }

  /**
   * Get all projects with search, filter, and pagination
   */
  static async getAll(context: any) {
    const { query, profile, success, serverError } = context;

    // Role validation is now handled at the route level

    // Build query options
    const options: QueryOptions = {
      search: query.search
        ? {
            term: query.search,
            fields: ["project_name", "objectives"],
          }
        : undefined,
      filters: [],
      // Always apply pagination, with default values if not provided
      pagination: {
        page: query.page ? Number(query.page) : 1,
        pageSize: query.pageSize ? Number(query.pageSize) : 10,
      },
      sort: query.sort
        ? {
            field: query.sort,
            direction: query.direction || "asc",
          }
        : undefined,
    };

    // If user is a client, filter by their organization ID
    if (profile && profile.role === UserRole.Client && profile.org_id) {
      options.filters?.push({
        field: "organization_id",
        value: profile.org_id,
      });
    }
    // For Finance and HR roles - filter by PIC
    else if (
      profile &&
      (profile.role === UserRole.Finance || profile.role === UserRole.HR) &&
      profile.employee_id
    ) {
      options.filters?.push({
        field: "pic_project",
        value: profile.employee_id,
      });
    }
    // Add organization filter if provided in query params (for non-filtered roles)
    else if (query.organization_id) {
      options.filters?.push({
        field: "organization_id",
        value: query.organization_id,
      });
    }

    // Add status filter if provided
    if (query.status_project) {
      options.filters?.push({
        field: "status_project",
        value: query.status_project,
      });
    }

    // Add category filter if provided
    if (query.project_category) {
      options.filters?.push({
        field: "project_category",
        value: query.project_category,
      });
    }

    const { data, error, result } = await ProjectService.getAll(options);

    if (error) {
      return serverError(error.message, error);
    }

    return success(
      {
        items: data,
        pagination: result,
      },
      "Projects retrieved successfully"
    );
  }

  /**
   * Get project by ID
   */
  static async getById(context: any) {
    const { params, success, notFound, serverError } = context;
    const { id } = params;

    // Role validation is now handled at the route level

    const { data, error } = await ProjectService.getById(id);

    if (error) {
      return serverError(error.message, error);
    }

    if (!data) {
      return notFound("Project not found");
    }

    return success(data, "Project retrieved successfully");
  }

  /**
   * Update a project
   */
  static async update(context: any) {
    const { params, body, user, success, notFound, serverError } = context;
    const { id } = params;

    // Role validation is now handled at the route level

    const { data, error } = await ProjectService.update(id, body, user.id);

    if (error) {
      if (error.message === "Project not found") {
        return notFound("Project not found");
      }
      return serverError(error.message, error);
    }

    return success(data, "Project updated successfully");
  }

  /**
   * Delete a project
   */
  static async delete(context: any) {
    const { params, user, success, notFound, serverError } = context;
    const { id } = params;

    // Role validation is now handled at the route level

    const { data, error } = await ProjectService.delete(id, user.id);

    if (error) {
      if (error.message === "Project not found") {
        return notFound("Project not found");
      }
      return serverError(error.message, error);
    }

    return success(data, "Project deleted successfully");
  }

  /**
   * Get dashboard data for a specific project
   */
  static async getDashboard(context: any) {
    const { params, user, success, notFound, serverError } = context;
    const { id } = params;

    // Role validation is now handled at the route level

    const { data, error } = await ProjectService.getProjectDashboard(
      id,
      user.id
    );

    if (error) {
      if (error.message === "Project not found") {
        return notFound("Project not found");
      }
      return serverError(error.message, error);
    }

    return success(data, "Project dashboard data retrieved successfully");
  }

  /**
   * Get dashboard summary for all projects
   */
  static async getDashboardSummary(context: any) {
    const { user, success, serverError } = context;

    // Role validation is handled at the route level

    const { data, error } = await ProjectService.getDashboardSummary(user.id);

    if (error) {
      return serverError(error.message, error);
    }

    return success(data, "Dashboard summary retrieved successfully");
  }
}
