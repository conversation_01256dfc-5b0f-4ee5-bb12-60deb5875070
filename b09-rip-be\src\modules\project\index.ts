import { Elysia } from "elysia";
import { projectRoutes } from "./routes";
import { apiResponse } from "../../middleware/api-response";

// Create an instance with the middleware applied
const projectApp = new Elysia().use(apiResponse).use(projectRoutes);

export * from "./service";

// Export the project module
export const projects = projectApp;

// Re-export for convenience
export * from "./schema";
export * from "./controller";
