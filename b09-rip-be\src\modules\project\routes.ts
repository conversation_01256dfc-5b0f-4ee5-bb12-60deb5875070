import { <PERSON><PERSON> } from "elysia";
import { ProjectController } from "./controller";
import {
  createProjectSchema,
  updateProjectSchema,
  getProjectSchema,
  getAllProjectsSchema,
} from "./schema";
import { requireActiveUser, checkRoles } from "../../middleware/auth";
import { UserRole } from "../../database/models/user-profile.model";

export const projectRoutes = (app: Elysia) =>
  app.group("/projects", (app) =>
    app
      // First ensure users are active
      .use(requireActiveUser)

      // Create a new project - only Staff Operations and Managers can create
      .post("/", ProjectController.create, {
        body: createProjectSchema.body,
        beforeHandle: [checkRoles([UserRole.Operation, UserRole.Manager])],
        detail: {
          tags: ["projects"],
          summary: "Create a new project",
          description: "Create a new project with the provided data",
          security: [{ bearerAuth: [] }],
        },
      })

      // Get all projects - both Managers and Staff Operations can view
      .get("/", ProjectController.getAll, {
        query: getAllProjectsSchema.query,
        beforeHandle: [
          checkRoles([
            UserRole.Manager,
            UserRole.Operation,
            UserRole.Client,
            UserRole.HR,
            UserRole.Finance,
          ]),
        ],
        detail: {
          tags: ["projects"],
          summary: "Get all projects",
          description:
            "Retrieve all projects with search, filter, and pagination",
          security: [{ bearerAuth: [] }],
        },
      })

      // Get project by ID - both Managers and Staff Operations can view
      .get("/:id", ProjectController.getById, {
        params: getProjectSchema.params,
        beforeHandle: [
          checkRoles([
            UserRole.Manager,
            UserRole.Operation,
            UserRole.Client,
            UserRole.HR,
            UserRole.Finance,
          ]),
        ],
        detail: {
          tags: ["projects"],
          summary: "Get project by ID",
          description: "Retrieve a specific project by its ID",
          security: [{ bearerAuth: [] }],
        },
      })

      // Update a project - only Staff Operations and Managers can update
      .put("/:id", ProjectController.update, {
        params: updateProjectSchema.params,
        body: updateProjectSchema.body,
        beforeHandle: [checkRoles([UserRole.Operation, UserRole.Manager])],
        detail: {
          tags: ["projects"],
          summary: "Update a project",
          description: "Update an existing project with the provided data",
          security: [{ bearerAuth: [] }],
        },
      })

      // Delete a project - only Managers can delete
      .delete("/:id", ProjectController.delete, {
        params: getProjectSchema.params,
        beforeHandle: [checkRoles([UserRole.Manager])],
        detail: {
          tags: ["projects"],
          summary: "Delete a project",
          description: "Soft delete a project",
          security: [{ bearerAuth: [] }],
        },
      })

      // Get project dashboard - accessible to Admin, Manager, Operation (all projects), Client (org projects), HR/Finance (PIC projects)
      .get("/:id/dashboard", ProjectController.getDashboard, {
        params: getProjectSchema.params,
        beforeHandle: [
          checkRoles([
            UserRole.Admin,
            UserRole.Manager,
            UserRole.Operation,
            UserRole.Client,
            UserRole.HR,
            UserRole.Finance,
          ]),
        ],
        detail: {
          tags: ["projects"],
          summary: "Get project dashboard",
          description:
            "Retrieve statistical recap including progress percentage, KPI status, and overall project status",
          security: [{ bearerAuth: [] }],
        },
      })

      // Get dashboard summary for all projects
      .get("/dashboard", ProjectController.getDashboardSummary, {
        beforeHandle: [
          checkRoles([
            UserRole.Admin,
            UserRole.Manager,
            UserRole.Operation,
            UserRole.Client,
            UserRole.HR,
            UserRole.Finance,
          ]),
        ],
        detail: {
          tags: ["projects"],
          summary: "Get dashboard summary for all projects",
          description:
            "Retrieve statistical recap of all projects including counts by status, recent projects, and upcoming deadlines",
          security: [{ bearerAuth: [] }],
        },
      })
  );
