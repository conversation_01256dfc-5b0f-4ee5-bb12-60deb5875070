import { t } from "elysia";
import {
  ProjectCategory,
  ProjectStatus,
} from "../../database/models/project.model";

// Common schema patterns
export const projectNameSchema = t.String({
  minLength: 2,
  maxLength: 100,
  description: "Project name (2-100 characters)",
});

export const picProjectSchema = t.String({
  format: "uuid",
  description: "Employee ID of the person in charge of the project",
});

export const dateSchema = t.String({
  pattern: "^\\d{4}-\\d{2}-\\d{2}$",
  description: "Date in YYYY-MM-DD format",
});

export const objectivesSchema = t.String({
  minLength: 2,
  maxLength: 1000,
  description: "Project objectives (2-1000 characters)",
});

export const budgetSchema = t.String({
  description: "Project budget",
});

export const projectCategorySchema = t.Enum(ProjectCategory, {
  description: "Project category",
});

export const projectStatusSchema = t.Enum(ProjectStatus, {
  description: "Project status",
});

// Project validation schemas
export const createProjectSchema = {
  body: t.Object({
    organization_id: t.String({
      format: "uuid",
      description: "Organization ID",
    }),
    project_category: projectCategorySchema,
    project_name: projectNameSchema,
    pic_project: picProjectSchema,
    start_project: dateSchema,
    end_project: dateSchema,
    status_project: t.Optional(projectStatusSchema),
    budget_project: budgetSchema,
    objectives: objectivesSchema,
    // Note: gantt_chart_id and project_charter_id are not included as they will be auto-generated
  }),
};

export const updateProjectSchema = {
  params: t.Object({
    id: t.String({
      format: "uuid",
      description: "Project ID",
    }),
  }),
  body: t.Object({
    organization_id: t.Optional(
      t.String({
        format: "uuid",
        description: "Organization ID",
      })
    ),
    project_category: t.Optional(projectCategorySchema),
    project_name: t.Optional(projectNameSchema),
    pic_project: t.Optional(picProjectSchema),
    start_project: t.Optional(dateSchema),
    end_project: t.Optional(dateSchema),
    status_project: t.Optional(projectStatusSchema),
    budget_project: t.Optional(budgetSchema),
    objectives: t.Optional(objectivesSchema),
    // Note: gantt_chart_id and project_charter_id are not included as they will be auto-generated
  }),
};

export const getProjectSchema = {
  params: t.Object({
    id: t.String({
      format: "uuid",
      description: "Project ID",
    }),
  }),
};

export const getAllProjectsSchema = {
  query: t.Object({
    search: t.Optional(t.String()),
    page: t.Optional(t.Numeric()),
    pageSize: t.Optional(t.Numeric()),
    project_category: t.Optional(t.String()),
    status_project: t.Optional(t.String()),
    organization_id: t.Optional(
      t.String({
        format: "uuid",
        description: "Organization ID for filtering projects",
      })
    ),
  }),
};
