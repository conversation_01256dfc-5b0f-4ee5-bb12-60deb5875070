import { dbUtils } from "../../utils/database";
import { QueryOptions } from "../../utils/database.types";
import {
  Project,
  CreateProjectDto,
  UpdateProjectDto,
  ProjectStatus,
  ProjectCategory,
} from "../../database/models/project.model";
import { KpiStatus } from "../../database/models/kpi-project.model";
import { TaskStatus } from "../../database/models/task.model";
import {
  ProjectDashboard,
  DashboardSummary,
} from "../../database/models/dashboard.model";
import { WeeklyLogService } from "../weekly-log/service";
import { addDays, format, differenceInDays } from "date-fns";
import { supabase } from "../../libs/supabase";
import { UserRole } from "../../database/models/user-profile.model";

// Get API URL from environment or use default
const API_URL = process.env.API_URL || "http://localhost:3000";

export class ProjectService {
  private static readonly TABLE_NAME = "projects";

  /**
   * Create a new project and automatically create a corresponding KPI Project
   * @param data Project data
   * @param userId User ID for audit
   * @returns Created project or error
   */
  static async create(data: CreateProjectDto, userId: string) {
    try {
      // Set default status if not provided
      const projectData = {
        ...data,
        status_project: data.status_project || ProjectStatus.NOT_STARTED,
        // TODO: In the future, these IDs will be generated by creating actual resources
        gantt_chart_id: "TODO-gantt-chart-implementation",
        project_charter_id: "TODO-project-charter-implementation",
      };

      // Create the project
      const { data: project, error } = await dbUtils.create<Project>(
        this.TABLE_NAME,
        projectData,
        userId
      );

      // If project creation was successful and status is In Progress, create a weekly log
      if (
        project &&
        !error &&
        project.status_project === ProjectStatus.IN_PROGRESS
      ) {
        try {
          await this.createInitialWeeklyLog(project, userId);
        } catch (weeklyLogError) {
          // Log the error but don't fail the project creation
          console.error("Error creating initial weekly log:", weeklyLogError);
        }
      }

      // If project creation was successful, create a KPI Project
      if (project && !error) {
        try {
          // Call the KPI Project createFromProject endpoint
          await fetch(`${API_URL}/v1/kpi-projects/from-project`, {
            method: "POST",
            headers: {
              "Content-Type": "application/json",
              // No Authorization header needed as this is an internal call
            },
            body: JSON.stringify({
              project_id: project.id,
              project_name: project.project_name,
              objectives: project.objectives,
              start_date: project.start_project,
              end_date: project.end_project,
            }),
          });

          // We don't need to wait for the response or handle errors
          // as we don't want project creation to fail if KPI creation fails
          console.log(
            `KPI Project creation triggered for project ${project.id}`
          );
        } catch (kpiError) {
          // Log the error but don't fail the project creation
          console.error("Error creating KPI Project:", kpiError);
        }
      }

      return { data: project, error };
    } catch (error: any) {
      return {
        data: null,
        error: new Error(`Failed to create project: ${error.message}`),
      };
    }
  }

  /**
   * Get a project by ID
   */
  static async getById(id: string) {
    try {
      const { data, error } = await dbUtils.getById<Project>(
        this.TABLE_NAME,
        id
      );

      if (error) {
        return { data: null, error };
      }

      return { data, error: null };
    } catch (error: any) {
      return {
        data: null,
        error: new Error(`Failed to get project: ${error.message}`),
      };
    }
  }

  /**
   * Get all projects with search, filter, and pagination
   */
  static async getAll(options: QueryOptions = {}) {
    try {
      // Add default sorting by created_at if not specified
      if (!options.sort) {
        options.sort = { field: "created_at", direction: "desc" };
      }

      const { data, error, result } = await dbUtils.getAll<Project>(
        this.TABLE_NAME,
        options
      );

      if (error) {
        return { data: null, error, result: null };
      }

      return { data, error: null, result };
    } catch (error: any) {
      return {
        data: null,
        error: new Error(`Failed to get projects: ${error.message}`),
        result: null,
      };
    }
  }

  /**
   * Update a project
   */
  static async update(id: string, data: UpdateProjectDto, userId: string) {
    try {
      // First check if project exists
      const { data: existingProject, error: getError } = await this.getById(id);

      if (getError || !existingProject) {
        return {
          data: null,
          error: getError || new Error("Project not found"),
        };
      }

      // If status is changing to In Progress, create a weekly log
      if (
        data.status_project === ProjectStatus.IN_PROGRESS &&
        existingProject.status_project !== ProjectStatus.IN_PROGRESS
      ) {
        try {
          await this.createInitialWeeklyLog(existingProject, userId);
        } catch (weeklyLogError) {
          // Log the error but don't fail the project update
          console.error("Error creating initial weekly log:", weeklyLogError);
        }
      }

      // Update the project
      const { data: updatedProject, error: updateError } =
        await dbUtils.update<Project>(this.TABLE_NAME, id, data, userId);

      if (updateError) {
        return { data: null, error: updateError };
      }

      return { data: updatedProject, error: null };
    } catch (error: any) {
      return {
        data: null,
        error: new Error(`Failed to update project: ${error.message}`),
      };
    }
  }

  /**
   * Create initial weekly log for a project
   * @param project Project data
   * @param userId User ID for audit
   */
  private static async createInitialWeeklyLog(
    project: Project,
    userId: string
  ) {
    try {
      // Calculate week number and dates
      const weekNumber = 1; // Always start with week 1
      const startDate = new Date(project.start_project);
      // Find the Monday of the week that includes the start date
      const startDayOfWeek = startDate.getDay();
      const daysToMonday = startDayOfWeek === 0 ? -6 : 1 - startDayOfWeek;
      const firstMonday = addDays(startDate, daysToMonday);
      // Calculate the Sunday of the week
      const weekEnd = addDays(firstMonday, 6);

      // Format dates to YYYY-MM-DD
      const weekStartDate = format(firstMonday, "yyyy-MM-dd");
      const weekEndDate = format(weekEnd, "yyyy-MM-dd");

      // Ensure project has a valid ID
      if (!project.id) {
        throw new Error("Project ID is required for creating weekly log");
      }

      // Create the weekly log
      const { data: weeklyLog, error } = await WeeklyLogService.create(
        {
          project_id: project.id,
          week_number: weekNumber,
          week_start_date: weekStartDate,
          week_end_date: weekEndDate,
        },
        userId
      );

      if (error) {
        console.error("Error creating initial weekly log:", error);
        throw error;
      }

      return weeklyLog;
    } catch (error) {
      console.error("Error in createInitialWeeklyLog:", error);
      throw error;
    }
  }

  /**
   * Delete a project (soft delete)
   */
  static async delete(id: string, userId: string) {
    try {
      // First check if project exists
      const { data: existingProject, error: getError } = await this.getById(id);

      if (getError || !existingProject) {
        return {
          data: null,
          error: getError || new Error("Project not found"),
        };
      }

      // Soft delete the project
      const { error: deleteError } = await dbUtils.softDelete(
        this.TABLE_NAME,
        id,
        userId
      );

      if (deleteError) {
        return { data: null, error: deleteError };
      }

      return { data: { id }, error: null };
    } catch (error: any) {
      return {
        data: null,
        error: new Error(`Failed to delete project: ${error.message}`),
      };
    }
  }

  /**
   * Get dashboard data for a specific project
   * @param id Project ID
   * @param userId User ID for access control
   * @returns Project dashboard data
   */
  static async getProjectDashboard(id: string, userId: string) {
    try {
      // 1. Get the project
      const { data: project, error: projectError } = await this.getById(id);

      if (projectError || !project) {
        return {
          data: null,
          error: projectError || new Error("Project not found"),
        };
      }

      // 2. Get user profile to determine access rights
      const { data: userProfile } = await supabase
        .from("user_profiles")
        .select("role, org_id, employee_id")
        .eq("user_id", userId)
        .single();

      if (!userProfile) {
        return {
          data: null,
          error: new Error("User profile not found"),
        };
      }

      // 3. Check access permissions based on role
      const hasAccess = (() => {
        // Admin, Manager, Operation have full access
        if (
          userProfile.role === UserRole.Admin ||
          userProfile.role === UserRole.Manager ||
          userProfile.role === UserRole.Operation
        ) {
          return true;
        }

        // Client can only access projects from their organization
        if (userProfile.role === UserRole.Client && userProfile.org_id) {
          return project.organization_id === userProfile.org_id;
        }

        // HR and Finance can only access projects where they are the PIC
        if (
          (userProfile.role === UserRole.HR ||
            userProfile.role === UserRole.Finance) &&
          userProfile.employee_id
        ) {
          return project.pic_project === userProfile.employee_id;
        }

        return false;
      })();

      if (!hasAccess) {
        return {
          data: null,
          error: new Error(
            "Access denied: You don't have permission to view this project dashboard"
          ),
        };
      }

      // 4. Get organization name
      const { data: organization } = await supabase
        .from("organizations")
        .select("name")
        .eq("id", project.organization_id)
        .single();

      // 5. Get PIC name
      const { data: employee } = await supabase
        .from("employees")
        .select("fullname")
        .eq("id", project.pic_project)
        .single();

      // 6. Get KPIs for the project
      const { data: kpis } = await supabase
        .from("kpi_projects")
        .select("id, description, target, period, status")
        .eq("project_id", id)
        .is("deleted_at", null);

      // 7. Get tasks for the project
      const { data: tasks } = await supabase
        .from("project_tasks")
        .select("*")
        .eq("project_id", id)
        .is("deleted_at", null);

      // Get employee names for tasks if there are any tasks
      let tasksWithEmployeeNames = [];
      if (tasks && tasks.length > 0) {
        // Get unique employee IDs
        const employeeIds = [...new Set(tasks.map((task) => task.employee_id))];

        // Fetch employee names
        const { data: employees } = await supabase
          .from("employees")
          .select("id, fullname")
          .in("id", employeeIds)
          .is("deleted_at", null);

        // Create a map of employee IDs to names
        const employeeMap = new Map();
        if (employees) {
          employees.forEach((emp) => employeeMap.set(emp.id, emp.fullname));
        }

        // Add employee names to tasks
        tasksWithEmployeeNames = tasks.map((task) => ({
          ...task,
          employees: { fullname: employeeMap.get(task.employee_id) || null },
        }));
      }

      // 8. Get weekly logs for the project
      const { data: weeklyLogs } = await supabase
        .from("weekly_logs")
        .select(
          `
          id,
          week_number,
          week_start_date,
          week_end_date
        `
        )
        .eq("project_id", id)
        .is("deleted_at", null)
        .order("week_number", { ascending: false });

      // 9. Get weekly log notes count for each log
      const weeklyLogIds = weeklyLogs?.map((log) => log.id) || [];
      const { data: weeklyLogNotes } = await supabase
        .from("weekly_log_notes")
        .select("weekly_log_id, id")
        .in("weekly_log_id", weeklyLogIds)
        .is("deleted_at", null);

      // Group notes by weekly log ID
      const notesByLogId = new Map();
      weeklyLogNotes?.forEach((note) => {
        if (!notesByLogId.has(note.weekly_log_id)) {
          notesByLogId.set(note.weekly_log_id, []);
        }
        notesByLogId.get(note.weekly_log_id).push(note);
      });

      // 10. Calculate progress percentage
      const tasksArray =
        tasksWithEmployeeNames.length > 0
          ? tasksWithEmployeeNames
          : tasks || [];
      const tasksTotal = tasksArray.length;
      const tasksCompleted =
        tasksArray.filter((t) => t.completion_status === TaskStatus.COMPLETED)
          .length || 0;
      const tasksInProgress =
        tasksArray.filter((t) => t.completion_status === TaskStatus.ON_PROGRESS)
          .length || 0;
      const tasksNotStarted =
        tasksArray.filter(
          (t) => t.completion_status === TaskStatus.NOT_COMPLETED
        ).length || 0;

      let progressPercentage = 0;
      if (tasksTotal > 0) {
        // Give partial credit (50%) for in-progress tasks
        progressPercentage = Math.round(
          ((tasksCompleted + tasksInProgress * 0.5) / tasksTotal) * 100
        );
      }

      // 11. Calculate days metrics
      const startDate = new Date(project.start_project);
      const endDate = new Date(project.end_project);
      const currentDate = new Date();

      const daysTotal = Math.ceil(differenceInDays(endDate, startDate));
      const daysElapsed = Math.max(
        0,
        Math.ceil(differenceInDays(currentDate, startDate))
      );
      const daysRemaining = Math.max(
        0,
        Math.ceil(differenceInDays(endDate, currentDate))
      );

      // 12. Determine overall KPI status
      let kpiStatus = null;
      if (kpis && kpis.length > 0) {
        // Logic to determine overall KPI status based on individual KPI statuses
        // This is a simplified approach - could be more sophisticated
        const kpiStatusCounts = {
          [KpiStatus.NOT_STARTED]: 0,
          [KpiStatus.IN_PROGRESS]: 0,
          [KpiStatus.COMPLETED_BELOW_TARGET]: 0,
          [KpiStatus.COMPLETED_ON_TARGET]: 0,
          [KpiStatus.COMPLETED_ABOVE_TARGET]: 0,
        };

        kpis.forEach((kpi) => {
          const status = kpi.status as KpiStatus;
          kpiStatusCounts[status]++;
        });

        // Determine predominant status
        if (kpiStatusCounts[KpiStatus.COMPLETED_ABOVE_TARGET] > 0) {
          kpiStatus = KpiStatus.COMPLETED_ABOVE_TARGET;
        } else if (kpiStatusCounts[KpiStatus.COMPLETED_ON_TARGET] > 0) {
          kpiStatus = KpiStatus.COMPLETED_ON_TARGET;
        } else if (kpiStatusCounts[KpiStatus.COMPLETED_BELOW_TARGET] > 0) {
          kpiStatus = KpiStatus.COMPLETED_BELOW_TARGET;
        } else if (kpiStatusCounts[KpiStatus.IN_PROGRESS] > 0) {
          kpiStatus = KpiStatus.IN_PROGRESS;
        } else {
          kpiStatus = KpiStatus.NOT_STARTED;
        }
      }

      // 13. Format weekly log data with note counts
      const formattedWeeklyLogs =
        weeklyLogs?.map((log) => ({
          id: log.id,
          week_number: log.week_number,
          week_start_date: log.week_start_date,
          week_end_date: log.week_end_date,
          notes_count: notesByLogId.get(log.id)?.length || 0,
        })) || [];

      // 14. Format recent tasks with employee names
      const recentTasks =
        tasksArray.slice(0, 5).map((task) => ({
          id: task.id,
          description: task.description,
          completion_status: task.completion_status,
          employee_name: task.employees?.fullname,
          due_date: task.due_date,
        })) || [];

      // 15. Construct and return the dashboard data
      // Ensure project has a valid ID for dashboard
      if (!project.id) {
        return {
          data: null,
          error: new Error("Project ID is required for dashboard construction"),
        };
      }

      const dashboard: ProjectDashboard = {
        id: project.id,
        project_name: project.project_name,
        organization_id: project.organization_id,
        organization_name: organization?.name,
        pic_project: project.pic_project,
        pic_name: employee?.fullname,
        project_category: project.project_category,
        start_project: project.start_project,
        end_project: project.end_project,
        status_project: project.status_project,
        objectives: project.objectives,
        budget_project: project.budget_project,

        progress_percentage: progressPercentage,
        days_elapsed: daysElapsed,
        days_remaining: daysRemaining,
        days_total: daysTotal,

        kpi_status: kpiStatus,
        kpi_count: kpis?.length || 0,
        kpis: kpis || [],

        tasks_total: tasksTotal,
        tasks_completed: tasksCompleted,
        tasks_in_progress: tasksInProgress,
        tasks_not_started: tasksNotStarted,
        recent_tasks: recentTasks,

        weekly_logs_count: weeklyLogs?.length || 0,
        recent_weekly_logs: formattedWeeklyLogs,
      };

      return { data: dashboard, error: null };
    } catch (error) {
      console.error("Error in ProjectService.getProjectDashboard:", error);
      return {
        data: null,
        error:
          error instanceof Error
            ? error
            : new Error("Failed to get project dashboard"),
      };
    }
  }

  /**
   * Get dashboard summary for all projects
   * @param userId User ID for access control
   * @returns Dashboard summary data
   */
  static async getDashboardSummary(userId: string) {
    try {
      // Get user profile to determine role and organization
      const { data: userProfile } = await supabase
        .from("user_profiles")
        .select("role, org_id, employee_id")
        .eq("user_id", userId)
        .single();

      // Initialize query for projects based on user role
      let projectQuery = supabase
        .from("projects")
        .select("*")
        .is("deleted_at", null);

      // Apply role-based filtering
      if (userProfile?.role === UserRole.Client && userProfile.org_id) {
        // Clients can only see projects for their organization
        projectQuery = projectQuery.eq("organization_id", userProfile.org_id);
      } else if (
        (userProfile?.role === UserRole.Operation ||
          userProfile?.role === UserRole.HR ||
          userProfile?.role === UserRole.Finance) &&
        userProfile.employee_id
      ) {
        // Operation, HR, and Finance staff can only see projects where they are the PIC
        projectQuery = projectQuery.eq("pic_project", userProfile.employee_id);
      }
      // Admin and Manager can see all projects

      // Execute the query
      const { data: projects, error: projectsError } = await projectQuery;

      if (projectsError) {
        return { data: null, error: projectsError };
      }

      // Get all project IDs
      const projectIds = projects?.map((project) => project.id) || [];

      // Get KPIs for these projects
      const { data: kpis, error: kpisError } = await supabase
        .from("kpi_projects")
        .select("*")
        .in("project_id", projectIds)
        .is("deleted_at", null);

      if (kpisError) {
        return { data: null, error: kpisError };
      }

      // Get tasks for these projects
      const { data: tasks, error: tasksError } = await supabase
        .from("project_tasks")
        .select("*")
        .in("project_id", projectIds)
        .is("deleted_at", null);

      if (tasksError) {
        return { data: null, error: tasksError };
      }

      // Get organization names for these projects
      const organizationIds = [
        ...new Set(projects?.map((project) => project.organization_id) || []),
      ];
      const { data: organizations } = await supabase
        .from("organizations")
        .select("id, name")
        .in("id", organizationIds)
        .is("deleted_at", null);

      // Create a map of organization IDs to names
      const organizationMap = new Map();
      organizations?.forEach((org) => organizationMap.set(org.id, org.name));

      // Get employee names for PICs
      const employeeIds = [
        ...new Set(projects?.map((project) => project.pic_project) || []),
      ];

      // Create a map of employee IDs to names
      const employeeMap = new Map();

      if (employeeIds.length > 0) {
        // First, get the employee records with their profile IDs
        const { data: employees } = await supabase
          .from("employees")
          .select("id, profile_id")
          .in("id", employeeIds)
          .is("deleted_at", null);

        if (employees && employees.length > 0) {
          // Extract profile IDs
          const profileIds = employees
            .map((employee) => employee.profile_id)
            .filter((id) => id);

          if (profileIds.length > 0) {
            // Fetch user profiles for these profile IDs
            const { data: profiles } = await supabase
              .from("user_profiles")
              .select("id, fullname")
              .in("id", profileIds)
              .is("deleted_at", null);

            // Create a map of profile ID to fullname
            const profileNameMap = new Map();
            profiles?.forEach((profile) => {
              profileNameMap.set(profile.id, profile.fullname);
            });

            // Map employee IDs to names via their profile
            employees.forEach((employee) => {
              const name =
                profileNameMap.get(employee.profile_id) ||
                `Employee ${employee.id.substring(0, 8)}`;
              employeeMap.set(employee.id, name);
            });
          }
        }
      }

      // Calculate project statistics
      const projectsByStatus = {
        not_started:
          projects?.filter(
            (p) => p.status_project === ProjectStatus.NOT_STARTED
          ).length || 0,
        in_progress:
          projects?.filter(
            (p) => p.status_project === ProjectStatus.IN_PROGRESS
          ).length || 0,
        completed:
          projects?.filter((p) => p.status_project === ProjectStatus.COMPLETED)
            .length || 0,
        cancelled:
          projects?.filter((p) => p.status_project === ProjectStatus.CANCELLED)
            .length || 0,
      };

      // Calculate projects by category
      const projectsByCategory: { [key in ProjectCategory]?: number } = {};
      projects?.forEach((project) => {
        const category = project.project_category as ProjectCategory;
        projectsByCategory[category] = (projectsByCategory[category] || 0) + 1;
      });

      // Calculate KPI statistics
      const kpisByStatus = {
        not_started:
          kpis?.filter((k) => k.status === KpiStatus.NOT_STARTED).length || 0,
        in_progress:
          kpis?.filter((k) => k.status === KpiStatus.IN_PROGRESS).length || 0,
        completed_below_target:
          kpis?.filter((k) => k.status === KpiStatus.COMPLETED_BELOW_TARGET)
            .length || 0,
        completed_on_target:
          kpis?.filter((k) => k.status === KpiStatus.COMPLETED_ON_TARGET)
            .length || 0,
        completed_above_target:
          kpis?.filter((k) => k.status === KpiStatus.COMPLETED_ABOVE_TARGET)
            .length || 0,
      };

      // Calculate KPI achievement metrics
      // Only consider completed KPIs (on target, above target, and below target)
      const completedKpis =
        kpisByStatus.completed_on_target +
        kpisByStatus.completed_above_target +
        kpisByStatus.completed_below_target;

      // KPIs that met or exceeded targets
      const kpiAchieved =
        kpisByStatus.completed_on_target + kpisByStatus.completed_above_target;

      // KPIs that didn't meet targets (only count completed_below_target)
      const kpiNotAchieved = kpisByStatus.completed_below_target;

      // Calculate percentage based only on completed KPIs
      const kpiAchievementPercentage =
        completedKpis > 0 ? Math.round((kpiAchieved / completedKpis) * 100) : 0;

      // Calculate projects by PIC
      interface ProjectByPic {
        name: string;
        count: number;
      }
      const projectsByPic: ProjectByPic[] = [];
      const picCounts: Record<string, number> = {};

      // First, ensure we have all employee data for PICs
      if (
        projects?.length &&
        projects.some((p) => !employeeMap.has(p.pic_project))
      ) {
        // Get all PIC IDs that we don't have names for
        const missingPicIds = projects
          .filter((p) => !employeeMap.has(p.pic_project))
          .map((p) => p.pic_project);

        // Fetch missing employee data if needed
        if (missingPicIds.length > 0) {
          const { data: missingEmployees } = await supabase
            .from("employees")
            .select("id, fullname")
            .in("id", missingPicIds)
            .is("deleted_at", null);

          // Add to our map
          missingEmployees?.forEach((emp) =>
            employeeMap.set(emp.id, emp.fullname)
          );
        }
      }

      // Now count projects by PIC with better name handling
      projects?.forEach((project) => {
        // Only include PICs that have a proper name
        const picName = employeeMap.get(project.pic_project);
        if (picName) {
          picCounts[picName] = (picCounts[picName] || 0) + 1;
        }
      });

      // Convert to array format for easier frontend consumption
      Object.entries(picCounts).forEach(([name, count]) => {
        // Skip entries with "Unknown" or empty names
        if (name && name !== "Unknown") {
          projectsByPic.push({ name, count });
        }
      });

      // Sort by count descending
      projectsByPic.sort((a, b) => b.count - a.count);

      // Calculate task statistics
      const currentDate = new Date();
      const tasksCompleted =
        tasks?.filter((t) => t.completion_status === TaskStatus.COMPLETED)
          .length || 0;
      const tasksInProgress =
        tasks?.filter((t) => t.completion_status === TaskStatus.ON_PROGRESS)
          .length || 0;
      const tasksNotStarted =
        tasks?.filter((t) => t.completion_status === TaskStatus.NOT_COMPLETED)
          .length || 0;
      const tasksOverdue =
        tasks?.filter((t) => {
          const dueDate = new Date(t.due_date);
          return (
            t.completion_status !== TaskStatus.COMPLETED &&
            dueDate < currentDate
          );
        }).length || 0;

      // Calculate progress percentage for each project
      const projectsWithProgress = projects?.map((project) => {
        const projectTasks =
          tasks?.filter((t) => t.project_id === project.id) || [];
        const totalTasks = projectTasks.length;
        const completedTasks = projectTasks.filter(
          (t) => t.completion_status === TaskStatus.COMPLETED
        ).length;
        const inProgressTasks = projectTasks.filter(
          (t) => t.completion_status === TaskStatus.ON_PROGRESS
        ).length;

        let progressPercentage = 0;
        if (totalTasks > 0) {
          progressPercentage = Math.round(
            ((completedTasks + inProgressTasks * 0.5) / totalTasks) * 100
          );
        }

        // Calculate days remaining
        const endDate = new Date(project.end_project);
        const daysRemaining = Math.max(
          0,
          Math.ceil(differenceInDays(endDate, currentDate))
        );

        return {
          ...project,
          progress_percentage: progressPercentage,
          days_remaining: daysRemaining,
          organization_name: organizationMap.get(project.organization_id),
          pic_name: employeeMap.get(project.pic_project),
        };
      });

      // Get recent projects (last 5 created)
      const recentProjects = [...(projectsWithProgress || [])]
        .sort(
          (a, b) =>
            new Date(b.created_at).getTime() - new Date(a.created_at).getTime()
        )
        .slice(0, 5)
        .map((project) => ({
          id: project.id,
          project_name: project.project_name,
          organization_name: project.organization_name,
          pic_name: project.pic_name,
          status_project: project.status_project,
          progress_percentage: project.progress_percentage,
          days_remaining: project.days_remaining,
        }));

      // Get upcoming deadlines (next 5 projects to end)
      const upcomingDeadlines = [...(projectsWithProgress || [])]
        .filter(
          (project) => project.status_project === ProjectStatus.IN_PROGRESS
        )
        .sort((a, b) => a.days_remaining - b.days_remaining)
        .slice(0, 5)
        .map((project) => ({
          id: project.id,
          project_name: project.project_name,
          end_project: project.end_project,
          days_remaining: project.days_remaining,
          progress_percentage: project.progress_percentage,
        }));

      // Prepare organization data if user is a client
      let organizationData = undefined;
      if (userProfile?.role === UserRole.Client && userProfile.org_id) {
        const orgId = userProfile.org_id;
        const orgProjects =
          projects?.filter((p) => p.organization_id === orgId) || [];

        organizationData = {
          id: orgId,
          name: organizationMap.get(orgId) || "Unknown Organization",
          projects_count: orgProjects.length,
          active_projects_count: orgProjects.filter(
            (p) => p.status_project === ProjectStatus.IN_PROGRESS
          ).length,
          completed_projects_count: orgProjects.filter(
            (p) => p.status_project === ProjectStatus.COMPLETED
          ).length,
        };
      }

      // Construct the dashboard summary
      const dashboardSummary: DashboardSummary = {
        projects: {
          total: projects?.length || 0,
          by_status: projectsByStatus,
          by_category: projectsByCategory,
          by_pic: projectsByPic,
          recent: recentProjects,
          upcoming_deadlines: upcomingDeadlines,
        },
        kpis: {
          total: kpis?.length || 0,
          by_status: kpisByStatus,
          achievement_percentage: kpiAchievementPercentage,
          achieved_count: kpiAchieved,
          not_achieved_count: kpiNotAchieved,
        },
        tasks: {
          total: tasks?.length || 0,
          completed: tasksCompleted,
          in_progress: tasksInProgress,
          not_started: tasksNotStarted,
          overdue: tasksOverdue,
        },
        organization: organizationData,
      };

      return { data: dashboardSummary, error: null };
    } catch (error) {
      console.error("Error in ProjectService.getDashboardSummary:", error);
      return {
        data: null,
        error:
          error instanceof Error
            ? error
            : new Error("Failed to get dashboard summary"),
      };
    }
  }
}
