import { SalaryService } from "./service";
import {
  SalaryPaymentStatus,
  UpdateSalaryDto,
} from "../../database/models/salary.model";
import { FilterOption, QueryOptions } from "../../utils/database.types";
import { SalaryUpdateHistoryService } from "./salary-update-history.service";
import { UserRole } from "../../database/models/user-profile.model";

function ensureResponseFunctions(context: any) {
  if (typeof context.success !== "function") {
    console.error("API Response middleware functions not available in context");
    return {
      success: (data: any, message = "Operation successful") => ({
        success: true,
        message,
        data,
      }),
      forbidden: (message = "Forbidden", errorCode?: string) => ({
        success: false,
        message,
        data: null,
        error: { code: errorCode || "FORBIDDEN" },
      }),
      unauthorized: (message = "Unauthorized", errorCode?: string) => ({
        success: false,
        message,
        data: null,
        error: { code: errorCode || "UNAUTHORIZED" },
      }),
      notFound: (message = "Not found", errorCode?: string) => ({
        success: false,
        message,
        data: null,
        error: { code: errorCode || "NOT_FOUND" },
      }),
      serverError: (message = "Server error", error?: Error) => ({
        success: false,
        message,
        data: null,
        error: {
          code: "INTERNAL_SERVER_ERROR",
          details: error ? { stack: error.stack } : undefined,
        },
      }),
    };
  }

  return context;
}

export class SalaryController {
  static async getAll(context: any) {
    const {
      query = {},
      success,
      serverError,
    } = ensureResponseFunctions(context);

    try {
      const options: QueryOptions = {};

      if (query.search) {
        options.search = {
          term: query.search,
          fields: ["employee_id", "period"],
        };
      }

      const filters: FilterOption[] = [];

      if (query.paymentStatus) {
        filters.push({ field: "payment_status", value: query.paymentStatus });
      }

      if (query.employeeId) {
        filters.push({ field: "employee_id", value: query.employeeId });
      }

      if (query.period) {
        filters.push({ field: "period", value: query.period });
      }

      if (filters.length > 0) {
        options.filters = filters;
      }

      if (query.page || query.pageSize) {
        options.pagination = {
          page: query.page ? parseInt(query.page, 10) : 1,
          pageSize: query.pageSize ? parseInt(query.pageSize, 10) : 10,
        };
      }

      const { data, error, result } = await SalaryService.getAll(options);

      if (error) {
        return serverError(error.message, error);
      }

      return success(
        {
          items: data,
          pagination: result,
        },
        "Salary records retrieved successfully"
      );
    } catch (error: any) {
      console.error("Error fetching salaries:", error);
      return serverError(
        error.message || "Failed to fetch salary records",
        error
      );
    }
  }

  static async getById(context: any) {
    const { params, success, notFound, serverError } =
      ensureResponseFunctions(context);
    const { id } = params;

    try {
      const { data, error } = await SalaryService.getById(id);

      // Check for "not found" errors and return a 404 response
      if (error) {
        if (
          error.message &&
          (error.message.toLowerCase().includes("not found") ||
            error.message.toLowerCase().includes("no salary found") ||
            error.message.toLowerCase().includes("does not exist"))
        ) {
          return notFound(`Salary record with ID ${id} not found`);
        }

        // For other types of errors, return server error
        return serverError(error.message, error);
      }

      if (!data) {
        return notFound(`Salary record with ID ${id} not found`);
      }

      return success(data, "Salary record retrieved successfully");
    } catch (error: any) {
      console.error("Error fetching salary record:", error);

      // Check if the error message indicates a "not found" error
      if (
        error.message &&
        (error.message.toLowerCase().includes("not found") ||
          error.message.toLowerCase().includes("no salary found") ||
          error.message.toLowerCase().includes("does not exist"))
      ) {
        return notFound(`Salary record with ID ${id} not found`);
      }

      return serverError(
        error.message || "Failed to fetch salary record",
        error
      );
    }
  }

  static async getByEmployeeId(context: any) {
    const { params, success, serverError, notFound } =
      ensureResponseFunctions(context);
    const { employeeId } = params;

    try {
      const { data, error } = await SalaryService.getByEmployeeId(employeeId);

      if (error) {
        // Check for "not found" message in the error to properly use notFound response
        if (
          error.message.includes("not found") ||
          error.message.includes("Not found")
        ) {
          return notFound(error.message);
        }

        return serverError(error.message, error);
      }

      return success(
        data,
        `Salary records for employee ${employeeId} retrieved successfully`
      );
    } catch (error: any) {
      console.error("Error fetching employee salary records:", error);
      return serverError(
        error.message || "Failed to fetch employee salary records",
        error
      );
    }
  }

  // Deprecated updateByHR method removed

  /**
   * Unified method to update a salary record with role-based permissions
   */
  static async updateSalary(context: any) {
    const {
      body,
      user,
      profile,
      params,
      success,
      notFound,
      serverError,
      forbidden,
    } = ensureResponseFunctions(context);
    const { id } = params;

    // Check if profile and role exist
    if (!profile || !profile.role) {
      return forbidden("User profile or role is missing");
    }

    try {
      const updateDto: UpdateSalaryDto = {};

      if (body.base_salary !== undefined) {
        updateDto.base_salary = body.base_salary;
      }

      if (body.payment_status !== undefined) {
        updateDto.payment_status = body.payment_status;
      }

      const { data, error } = await SalaryService.updateSalary(
        id,
        updateDto,
        user.id,
        profile.role
      );

      if (error) {
        if (error.message.includes("not found")) {
          return notFound(error.message);
        }
        if (error.message.includes("cannot update")) {
          return forbidden(error.message);
        }
        return serverError(error.message, error);
      }

      return success(data, "Salary record updated successfully");
    } catch (error: any) {
      console.error("Error updating salary record:", error);
      return serverError(
        error.message || "Failed to update salary record",
        error
      );
    }
  }

  // Deprecated updateByFinance method removed

  /**
   * Get update history for a salary
   */
  static async getHistory(context: any) {
    const {
      params,
      query = {},
      success,
      notFound,
      serverError,
    } = ensureResponseFunctions(context);
    const { id } = params;

    try {
      // First check if the salary exists
      const { data: existingSalary, error: fetchError } =
        await SalaryService.getById(id);

      if (fetchError) {
        return serverError(fetchError.message, fetchError);
      }

      if (!existingSalary) {
        return notFound(`Salary with ID ${id} not found`);
      }

      const options: QueryOptions = {};

      // Apply pagination if provided
      if (query.page || query.pageSize) {
        options.pagination = {
          page: query.page ? parseInt(query.page, 10) : 1,
          pageSize: query.pageSize ? parseInt(query.pageSize, 10) : 10,
        };
      }

      const { data, error } = await SalaryUpdateHistoryService.getBySalaryId(
        id,
        options
      );

      if (error) {
        return serverError(error.message, error);
      }

      // Parse the change descriptions for easier client-side use
      const parsedHistory = data
        ? await SalaryUpdateHistoryService.parseHistoryRecords(data)
        : [];

      return success(
        {
          items: parsedHistory,
        },
        "Salary history retrieved successfully"
      );
    } catch (error: any) {
      console.error("Error fetching salary history:", error);
      return serverError(
        error.message || "Failed to fetch salary history",
        error
      );
    }
  }
}

export default SalaryController;
