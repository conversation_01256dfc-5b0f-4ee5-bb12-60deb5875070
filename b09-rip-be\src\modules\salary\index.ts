import { Elysia } from "elysia";
import { salaryRoutes } from "./routes";
import { apiResponse } from "../../middleware/api-response";
import { SalaryService } from "./service";

async function generateMonthlySalaries() {
  try {
    const now = new Date();
    const currentMonth = now.getMonth() + 1;
    const currentYear = now.getFullYear();
    const period = `${currentYear}-${currentMonth.toString().padStart(2, '0')}`;
    
    const { data: existingSalaries, error } = await SalaryService.getByPeriod(period);
    
    if (error) {
      console.error("Error checking existing salaries:", error);
      console.warn("Continuing with salary generation assuming no existing records");
    }
    
    if (existingSalaries && existingSalaries.length > 0) {
      console.log(`Found ${existingSalaries.length} existing salary records for period ${period}`);
    }
    
    const systemUserId = "7d6efce6-f439-45ae-88ad-813db3a74cdc";
    
    const { data, error: genError } = await SalaryService.generateMonthlySalaries(systemUserId);
    
    if (genError) {
      console.error("Error generating monthly salaries:", genError);
      console.warn("Application will continue without completing salary generation");
      return;
    }
    
    if (data && data.length > 0) {
      console.log(`Successfully generated ${data.length} salary records for period ${period}`);
    } else {
      console.log(`No new salary records needed for period ${period}`);
    }
  } catch (error) {
    console.error("Unexpected error during automatic salary generation:", error);
    console.warn("Application will continue without completing salary generation");
  }
}

const salaryApp = new Elysia()
  .use(apiResponse)
  .use(salaryRoutes);

setTimeout(() => {
  generateMonthlySalaries().catch(error => {
    console.error("Failed to run automatic salary generation:", error);
    console.warn("Application will continue without salary generation");
  });
}, 3000);

export const salaries = salaryApp;

export * from "./service";
export * from "./schema";
export * from "./controller";