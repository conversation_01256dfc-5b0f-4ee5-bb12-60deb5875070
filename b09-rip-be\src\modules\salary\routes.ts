import { <PERSON><PERSON> } from "elysia";
import { <PERSON><PERSON><PERSON><PERSON>roller } from "./controller";
import { requireActiveUser, checkRoles } from "../../middleware/auth";
import { UserRole } from "../../database/models/user-profile.model";
import {
  getSalarySchema,
  getAllSalariesSchema,
  getByEmployeeIdSchema,
  updateSalarySchema,
} from "./schema";

/**
 * Salary routes
 */
export const salaryRoutes = (app: Elysia) =>
  app.group(
    "/salaries",
    (app) =>
      app
        .use(requireActiveUser)
        // Get all salaries with filtering and pagination
        // This will automatically generate salaries for the current month if needed
        .get("/", SalaryController.getAll, {
          beforeHandle: [
            checkRoles([
              UserRole.Admin,
              UserRole.Finance,
              UserRole.Manager,
              UserRole.HR,
            ]),
          ],
          query: getAllSalariesSchema.query,
          detail: {
            tags: ["salaries"],
            summary: "Get all salary records",
            description:
              "Retrieve all salary records with optional filters. Automatically generates salaries for the current month if needed.",
            security: [{ bearerAuth: [] }],
            responses: {
              "200": {
                description: "Successfully retrieved salary records",
                content: {
                  "application/json": {
                    example: {
                      success: true,
                      message: "Salary records retrieved successfully",
                      data: {
                        items: [
                          {
                            id: "123e4567-e89b-12d3-a456-426614174000",
                            employee_id: "8a172b90-b3d5-4f32-b971-3603fb62130a",
                            base_salary: 9000000,
                            bonus: 1800000,
                            pay_reduction: 0,
                            total_salary: 10800000,
                            payment_status: "unpaid",
                            period: "2025-03",
                          },
                        ],
                        pagination: {
                          total: 5,
                          page: 1,
                          pageSize: 10,
                        },
                      },
                    },
                  },
                },
              },
            },
          },
        })
        // Get salaries by employee ID
        .get("/employee/:employeeId", SalaryController.getByEmployeeId, {
          beforeHandle: [
            checkRoles([
              UserRole.Admin,
              UserRole.HR,
              UserRole.Finance,
              UserRole.Manager,
            ]),
          ],
          params: getByEmployeeIdSchema.params,
          detail: {
            tags: ["salaries"],
            summary: "Get salaries by employee ID",
            description: "Retrieve all salary records for a specific employee",
            security: [{ bearerAuth: [] }],
          },
        })
        // Get salary by ID
        .get("/:id", SalaryController.getById, {
          beforeHandle: [
            checkRoles([
              UserRole.Admin,
              UserRole.HR,
              UserRole.Finance,
              UserRole.Manager,
            ]),
          ],
          params: getSalarySchema.params,
          detail: {
            tags: ["salaries"],
            summary: "Get salary by ID",
            description: "Retrieve a specific salary record by its ID",
            security: [{ bearerAuth: [] }],
            responses: {
              "200": {
                description: "Successfully retrieved salary record",
                content: {
                  "application/json": {
                    example: {
                      success: true,
                      message: "Salary record retrieved successfully",
                      data: {
                        id: "123e4567-e89b-12d3-a456-426614174000",
                        employee_id: "8a172b90-b3d5-4f32-b971-3603fb62130a",
                        base_salary: 9000000,
                        bonus: 1800000,
                        pay_reduction: 0,
                        total_salary: 10800000,
                        payment_status: "unpaid",
                        period: "2025-03",
                      },
                    },
                  },
                },
              },
            },
          },
        })

        // Get salary update history
        .get("/:id/history", SalaryController.getHistory, {
          beforeHandle: [
            checkRoles([
              UserRole.Admin,
              UserRole.HR,
              UserRole.Finance,
              UserRole.Manager,
            ]),
          ],
          params: getSalarySchema.params,
          detail: {
            tags: ["salaries"],
            summary: "Get salary update history",
            description: "Retrieve the update history for a specific salary",
            security: [{ bearerAuth: [] }],
            responses: {
              "200": {
                description: "Successfully retrieved salary history",
                content: {
                  "application/json": {
                    example: {
                      success: true,
                      message: "Salary history retrieved successfully",
                      data: {
                        items: [
                          {
                            id: "123e4567-e89b-12d3-a456-426614174000",
                            salary_id: "8a172b90-b3d5-4f32-b971-3603fb62130a",
                            change_description:
                              '[{"field":"base_salary","from_value":9000000,"to_value":9500000}]',
                            parsed_changes: [
                              {
                                field: "base_salary",
                                from_value: 9000000,
                                to_value: 9500000,
                              },
                            ],
                            created_at: "2023-04-01T12:00:00Z",
                            created_by: "user-123",
                          },
                        ],
                      },
                    },
                  },
                },
              },
              "404": {
                description: "Salary not found",
              },
            },
          },
        })

        // Unified salary update endpoint with role-based permissions
        .patch("/:id", SalaryController.updateSalary, {
          beforeHandle: [
            checkRoles([UserRole.Manager, UserRole.HR, UserRole.Finance]),
          ],
          params: getSalarySchema.params,
          body: updateSalarySchema.body,
          detail: {
            tags: ["salaries"],
            summary: "Update salary",
            description:
              "Update a salary record with role-based permissions. HR can update base_salary for unpaid salaries. Finance can update base_salary and payment_status. Managers can do both.",
            security: [{ bearerAuth: [] }],
            responses: {
              "200": {
                description: "Successfully updated salary record",
                content: {
                  "application/json": {
                    example: {
                      success: true,
                      message: "Salary record updated successfully",
                      data: {
                        id: "123e4567-e89b-12d3-a456-426614174000",
                        employee_id: "8a172b90-b3d5-4f32-b971-3603fb62130a",
                        base_salary: 9500000,
                        total_bonus: 2000000,
                        total_deduction: 100000,
                        total_allowance: 300000,
                        total_salary: 11700000,
                        payment_status: "paid",
                        period: "2025-03",
                      },
                    },
                  },
                },
              },
              "403": {
                description:
                  "Forbidden - User doesn't have permission for the requested update",
              },
              "404": {
                description: "Salary record not found",
              },
            },
          },
        })

    // Deprecated endpoints removed
  );

export default salaryRoutes;
