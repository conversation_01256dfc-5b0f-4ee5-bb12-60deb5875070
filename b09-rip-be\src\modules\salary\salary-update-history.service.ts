import { dbUtils } from "../../utils/database";
import {
  CreateSalaryHistoryDto,
  SalaryChangeItem,
  SimpleFieldChangeItem,
  BaseSalaryChangeItem,
  ComponentChangeItem,
  SalaryHistory,
  createChangeDescription,
  parseChangeDescription,
  createSimpleFieldChange,
  createBaseSalaryChange,
  createComponentChange,
} from "../../database/models/salary-update-history.model";
import { Salary } from "../../database/models/salary.model";
import { QueryOptions } from "../../utils/database.types";
import { UserProfile } from "../../database/models/user-profile.model";

/**
 * Interface for component update parameters
 */
export interface ComponentUpdateParams {
  salaryId: string;
  componentType: "bonus" | "deduction" | "allowance";
  action: "add" | "update" | "delete";
  componentTypeName: string;
  amount: number;
  oldTotalValue: number;
  newTotalValue: number;
  oldTotalSalary?: number;
  newTotalSalary?: number;
  userId: string;
}

export class SalaryUpdateHistoryService {
  private static readonly TABLE_NAME = "salary_update_history";

  /**
   * Create a new salary update history record
   * @param data History data to insert
   * @param userId ID of the user making the change
   * @returns The created history record and any error
   */
  static async create(data: CreateSalaryHistoryDto, userId: string) {
    return dbUtils.create<SalaryHistory>(this.TABLE_NAME, data, userId);
  }

  /**
   * Get all history records for a specific salary
   * @param salaryId ID of the salary to get history for
   * @param options Query options including pagination
   * @returns History records and any error
   */
  static async getBySalaryId(salaryId: string, options: QueryOptions = {}) {
    try {
      // Create pagination options if provided
      const queryOptions: any = {};

      if (options.pagination) {
        queryOptions.pagination = options.pagination;
      }

      // Get history records for the salary
      const { data, error } = await dbUtils.getByField<SalaryHistory>(
        this.TABLE_NAME,
        "salary_id",
        salaryId,
        queryOptions
      );

      if (error) {
        return { data: null, error };
      }

      return { data, error: null };
    } catch (error: any) {
      return {
        data: null,
        error: new Error(`Failed to get salary history: ${error.message}`),
      };
    }
  }

  // Employee ID is no longer tracked in salary update history

  /**
   * Compare original salary with updated data to generate change items
   * @param originalSalary The original salary before update
   * @param updateData The data used to update the salary
   * @returns Array of change items or null if no changes
   */
  static compareSalaryChanges(
    originalSalary: Salary,
    updateData: any
  ): SalaryChangeItem[] | null {
    const changes: SalaryChangeItem[] = [];

    // Check each field in updateData and compare with original
    Object.entries(updateData).forEach(([key, newValue]) => {
      // Skip if the value is undefined
      if (newValue === undefined) return;

      // Get the original value
      const originalValue = originalSalary[key as keyof Salary];

      // Only add to changes if the value is different
      if (newValue !== originalValue) {
        if (key === "base_salary") {
          // For base_salary, include total salary information
          changes.push(
            createBaseSalaryChange(
              originalValue as number,
              newValue as number,
              originalSalary.total_salary,
              // Calculate new total salary based on the change in base_salary
              originalSalary.total_salary -
                (originalValue as number) +
                (newValue as number)
            )
          );
        } else {
          // For other fields, use the simple format
          changes.push(createSimpleFieldChange(key, originalValue, newValue));
        }
      }
    });

    // Return null if no changes were found
    return changes.length > 0 ? changes : null;
  }

  /**
   * Create a history record for salary update if there are changes
   * @param salaryId ID of the salary being updated
   * @param originalSalary The original salary before update
   * @param updateData The data used to update the salary
   * @param userId ID of the user making the update
   * @returns The created history record and any error, or null if no changes
   */
  static async trackSalaryUpdate(
    salaryId: string,
    originalSalary: Salary,
    updateData: any,
    userId: string
  ) {
    // Compare changes
    const changes = this.compareSalaryChanges(originalSalary, updateData);

    // If no changes, return null
    if (!changes) {
      return { data: null, error: null };
    }

    // Create history record
    const historyData: CreateSalaryHistoryDto = {
      salary_id: salaryId,
      change_description: createChangeDescription(changes),
    };

    return this.create(historyData, userId);
  }

  /**
   * Helper function to format currency values
   */
  private static formatCurrency(value: number): string {
    return new Intl.NumberFormat("id-ID", {
      style: "currency",
      currency: "IDR",
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(value);
  }

  /**
   * Helper function to format field names for display
   */
  private static formatFieldName(field: string): string {
    return field
      .split("_")
      .map((word) => word.charAt(0).toUpperCase() + word.slice(1))
      .join(" ");
  }

  /**
   * Helper function to format values for display
   */
  private static formatValue(value: any): string {
    if (value === null || value === undefined) {
      return "None";
    }
    if (typeof value === "number") {
      return this.formatCurrency(value);
    }
    if (typeof value === "boolean") {
      return value ? "Yes" : "No";
    }
    return String(value);
  }

  /**
   * Helper function to capitalize first letter
   */
  private static capitalizeFirst(str: string): string {
    return str.charAt(0).toUpperCase() + str.slice(1);
  }

  /**
   * Parse change descriptions in history records for display
   * Also resolves user IDs to names
   * @param historyRecords Array of history records
   * @returns Array of history records with parsed change descriptions and user names
   */
  static async parseHistoryRecords(historyRecords: SalaryHistory[]) {
    // For tests, just parse the change descriptions without user name resolution
    if (process.env.NODE_ENV === "test") {
      return historyRecords.map((record) => ({
        ...record,
        parsed_changes: parseChangeDescription(record.change_description),
        formatted_changes: this.formatChangesForDisplay(
          parseChangeDescription(record.change_description)
        ),
        created_by_name: "Test User",
      }));
    }

    try {
      // Get all unique user IDs from the created_by fields
      const userIds = [
        ...new Set(
          historyRecords.map((record) => record.created_by).filter(Boolean)
        ),
      ];

      // Fetch user profiles for these IDs
      let userProfiles = [];
      if (userIds.length > 0) {
        try {
          const { data, error } = await dbUtils
            .query("user_profiles")
            .raw.select("user_id, fullname")
            .in("user_id", userIds)
            .is("deleted_at", null);

          if (!error && data) {
            userProfiles = data;
          }
        } catch (error) {
          console.error("Error fetching user profiles:", error);
        }
      }

      // Create a map of user ID to user name
      const userNameMap = new Map();
      userProfiles.forEach((profile) => {
        userNameMap.set(profile.user_id, profile.fullname);
      });

      // Add user names to the history records
      return historyRecords.map((record) => {
        const parsedChanges = parseChangeDescription(record.change_description);
        return {
          ...record,
          parsed_changes: parsedChanges,
          formatted_changes: this.formatChangesForDisplay(parsedChanges),
          created_by_name: userNameMap.get(record.created_by) || "Unknown User",
        };
      });
    } catch (error) {
      console.error("Error parsing history records:", error);
      // Return the original records if there's an error
      return historyRecords.map((record) => {
        const parsedChanges = parseChangeDescription(record.change_description);
        return {
          ...record,
          parsed_changes: parsedChanges,
          formatted_changes: this.formatChangesForDisplay(parsedChanges),
        };
      });
    }
  }

  /**
   * Format changes for display
   * @param changes Array of parsed change items
   * @returns Array of formatted change items with display text
   */
  private static formatChangesForDisplay(changes: SalaryChangeItem[]) {
    return changes.map((change) => {
      // Handle different change types
      if (change.changeType === "field") {
        if (change.subType === "base_salary") {
          // Format base_salary changes with total salary info
          return {
            ...change,
            displayText: `Base salary changed from ${this.formatCurrency(
              change.from_value.amount
            )} to ${this.formatCurrency(
              change.to_value.amount
            )}, affecting total salary from ${this.formatCurrency(
              change.from_value.total_salary
            )} to ${this.formatCurrency(change.to_value.total_salary)}`,
          };
        } else {
          // Format simple field changes
          return {
            ...change,
            displayText: `${this.formatFieldName(
              change.field
            )} changed from ${this.formatValue(
              change.from_value
            )} to ${this.formatValue(change.to_value)}`,
          };
        }
      } else if (change.changeType === "component") {
        // Format component changes
        const actionText =
          change.action === "add"
            ? "added"
            : change.action === "update"
            ? "updated"
            : "removed";

        let displayText = `${this.capitalizeFirst(
          change.componentType
        )} ${actionText}: ${change.to_value.type}`;

        if (
          change.action !== "delete" &&
          change.to_value.amount !== undefined
        ) {
          displayText += ` (${this.formatCurrency(change.to_value.amount)})`;
        }

        if (
          change.from_value.total_salary !== undefined &&
          change.to_value.total_salary !== undefined
        ) {
          displayText += `, affecting total salary from ${this.formatCurrency(
            change.from_value.total_salary
          )} to ${this.formatCurrency(change.to_value.total_salary)}`;
        }

        return {
          ...change,
          displayText,
        };
      } else {
        // Legacy format or unknown type
        return {
          ...change,
          displayText: `${this.formatFieldName(change.field)} changed`,
        };
      }
    });
  }

  /**
   * Track component updates (bonus, deduction, allowance) in the salary history
   * @param params Object containing all parameters for the component update
   * @returns The created history record and any error
   */
  static async trackComponentUpdate(params: ComponentUpdateParams | any) {
    // Handle legacy method signature for backward compatibility
    if (typeof params === "string") {
      // Old signature: trackComponentUpdate(salaryId, componentType, action, componentTypeName, amount, oldTotalValue, newTotalValue, oldTotalSalaryOrUserId, newTotalSalary, userId)
      const [
        salaryId,
        componentType,
        action,
        componentTypeName,
        amount,
        oldTotalValue,
        newTotalValue,
        oldTotalSalaryOrUserId,
        newTotalSalary,
        userId,
      ] = arguments as unknown as [
        string,
        string,
        string,
        string,
        number,
        number,
        number,
        number | string,
        number | undefined,
        string | undefined
      ];

      // Handle the case where userId is passed as the 8th parameter (old signature)
      let actualUserId = userId;
      let actualOldTotalSalary = oldTotalSalaryOrUserId;
      let actualNewTotalSalary = newTotalSalary;

      if (typeof oldTotalSalaryOrUserId === "string" && userId === undefined) {
        actualUserId = oldTotalSalaryOrUserId;
        actualOldTotalSalary = undefined;
        actualNewTotalSalary = undefined;
      }

      // Convert to new format
      params = {
        salaryId,
        componentType,
        action,
        componentTypeName,
        amount,
        oldTotalValue,
        newTotalValue,
        oldTotalSalary:
          typeof actualOldTotalSalary === "number"
            ? actualOldTotalSalary
            : undefined,
        newTotalSalary: actualNewTotalSalary,
        userId: actualUserId,
      };
    }

    const {
      salaryId,
      componentType,
      action,
      componentTypeName,
      amount,
      oldTotalValue,
      newTotalValue,
      oldTotalSalary,
      newTotalSalary,
      userId,
    } = params;

    // Create component change using helper function
    const change = createComponentChange(
      componentType as "bonus" | "deduction" | "allowance",
      action as "add" | "update" | "delete",
      componentTypeName,
      amount,
      oldTotalValue,
      newTotalValue,
      oldTotalSalary,
      newTotalSalary
    );

    // Create history record
    const historyData: CreateSalaryHistoryDto = {
      salary_id: salaryId,
      change_description: createChangeDescription([change]),
    };

    return this.create(historyData, userId);
  }
}
