import { t } from "elysia";
import { SalaryPaymentStatus } from "../../database/models/salary.model";

export const employeeIdSchema = t.String({
  format: "uuid",
  description: "The ID of the employee",
});

export const baseSalarySchema = t.Number({
  minimum: 0,
  maximum: 1000000000,
  description: "The base salary amount",
});

export const totalBonusSchema = t.Number({
  minimum: 0,
  maximum: 1000000000,
  description: "The total bonus amount",
});

export const totalDeductionSchema = t.Number({
  minimum: 0,
  maximum: 1000000000,
  description: "The total deduction amount",
});

export const totalAllowanceSchema = t.Number({
  minimum: 0,
  maximum: 1000000000,
  description: "The total allowance amount",
});

export const payReductionSchema = t.Number({
  minimum: 0,
  maximum: 1000000000,
  description: "The pay reduction amount",
});

export const totalSalarySchema = t.Number({
  minimum: 0,
  maximum: 1000000000,
  description: "The calculated total salary amount",
});

export const paymentStatusSchema = t.Enum(SalaryPaymentStatus, {
  description: "The status of the salary payment",
});

export const periodSchema = t.String({
  minLength: 7,
  maxLength: 7,
  pattern: "^\\d{4}-\\d{2}$",
  description: "The salary period in YYYY-MM format",
});

// Schema for creating a new salary record
export const createSalarySchema = {
  body: t.Object({
    employeeId: employeeIdSchema,
    baseSalary: baseSalarySchema,
    total_bonus: t.Optional(totalBonusSchema),
    total_deduction: t.Optional(totalDeductionSchema),
    total_allowance: t.Optional(totalAllowanceSchema),
    payment_status: t.Optional(paymentStatusSchema),
    period: periodSchema,
  }),
};

// Unified schema for updating a salary record
// Note: Bonus, deduction, and allowance should be managed through their respective modules
export const updateSalarySchema = {
  body: t.Object({
    base_salary: t.Optional(baseSalarySchema),
    payment_status: t.Optional(paymentStatusSchema),
  }),
};

// Deprecated schemas removed

// Schema for getting a salary record by ID - Updated to use UUID format
export const getSalarySchema = {
  params: t.Object({
    id: t.String({
      format: "uuid",
      description: "The ID of the salary record",
    }),
  }),
};

// Schema for getting all salary records with filtering
export const getAllSalariesSchema = {
  query: t.Object({
    employeeId: t.Optional(
      t.String({
        format: "uuid",
        description: "Filter by employee ID",
      })
    ),
    period: t.Optional(
      t.String({
        description: "Filter by period (YYYY-MM)",
        pattern: "^\\d{4}-\\d{2}$",
      })
    ),
    paymentStatus: t.Optional(
      t.Enum(SalaryPaymentStatus, {
        description: "Filter by payment status",
      })
    ),
    search: t.Optional(
      t.String({
        description: "Search term for searching across different fields",
      })
    ),
    page: t.Optional(
      t.Number({
        minimum: 1,
        description: "The page number",
      })
    ),
    pageSize: t.Optional(
      t.Number({
        minimum: 1,
        maximum: 100,
        description: "The page size",
      })
    ),
  }),
};

// Schema for getting salaries by employee ID
export const getByEmployeeIdSchema = {
  params: t.Object({
    employeeId: t.String({
      format: "uuid",
      description: "The ID of the employee",
    }),
  }),
};

// Schema for getting salaries by period
export const getByPeriodSchema = {
  params: t.Object({
    period: t.String({
      minLength: 7,
      maxLength: 7,
      pattern: "^\\d{4}-\\d{2}$",
      description: "The salary period in YYYY-MM format",
    }),
  }),
};

// TODO: Schema for getting salary update history
// This is a placeholder and will be implemented in the future
/*
export const getSalaryHistorySchema = {
  params: t.Object({
    id: t.String({
      format: "uuid",
      description: "The ID of the salary record to get history for",
    }),
  }),
  query: t.Object({
    page: t.Optional(
      t.Number({
        description: "The page number for pagination (starts at 1)",
      })
    ),
    pageSize: t.Optional(
      t.Number({
        description: "Number of items per page (default: 10)",
      })
    ),
  }),
};
*/
