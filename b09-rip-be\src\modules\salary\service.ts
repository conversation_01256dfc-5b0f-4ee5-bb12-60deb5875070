import { dbUtils } from "../../utils/database";
import {
  <PERSON>ary,
  SalaryPaymentStatus,
  UpdateSalaryDto,
} from "../../database/models/salary.model";
import { QueryOptions } from "../../utils/database.types";
import { randomUUID } from "crypto";
import { UserRole } from "../../database/models/user-profile.model";
import { SalaryUpdateHistoryService } from "./salary-update-history.service";
import { BonusSalaryService } from "../bonus-salary/service";
import { DeductionSalaryService } from "../deduction-salary/service";
import { BonusSalaryType } from "../../database/models/bonus-salary.model";
import { DeductionType } from "../../database/models/deduction-salary.model";

const BASE_SALARIES: Record<string, number> = {
  Manager: 10000000,
  HR: 7500000,
  Finance: 8000000,
  Operation: 7000000,
  Admin: 6000000,
};

const ATTENDANCE_DEDUCTIONS = {
  present: 0,
  permit: 0.5,
  leave: 1,
  absent: 1,
  missing: 1,
};

export class SalaryService {
  private static readonly TABLE_NAME = "salaries";
  private static readonly EMPLOYEE_TABLE_NAME = "employees";
  private static readonly ATTENDANCE_TABLE_NAME = "attendances";
  private static readonly USER_PROFILES_TABLE_NAME = "user_profiles";

  private static generateSalaryId(): string {
    return randomUUID();
  }

  private static formatToTwoDecimals(value: number): number {
    return Number(value.toFixed(2));
  }

  private static async getEmployees(): Promise<any[]> {
    try {
      const { data: employees, error } = await dbUtils
        .query(this.EMPLOYEE_TABLE_NAME)
        .raw.select(
          `
        id,
        profile_id,
        department,
        bank_account,
        bank_name,
        presence_status
        `
        )
        .is("deleted_at", null);

      if (error || !employees || employees.length === 0) {
        return [];
      }

      const profileIds = employees.map((emp) => emp.profile_id);

      const { data: profiles, error: profilesError } = await dbUtils
        .query(this.USER_PROFILES_TABLE_NAME)
        .raw.select(
          `
        id,
        role,
        fullname,
        is_active
        `
        )
        .in("id", profileIds)
        .is("deleted_at", null);

      if (profilesError) {
        return [];
      }

      const profileMap = new Map();
      if (profiles) {
        profiles.forEach((profile) => {
          profileMap.set(profile.id, profile);
        });
      }

      const validEmployees = employees
        .map((employee) => {
          const profile = profileMap.get(employee.profile_id) || {};

          const isActive = profile.is_active === true;
          const role = profile.role || "";
          const hasValidRole = Object.keys(BASE_SALARIES)
            .map((r) => r.toLowerCase())
            .includes(role.toLowerCase());

          return {
            ...employee,
            role: profile.role || "",
            fullname: profile.fullname || "Unknown",
            isActive,
            hasValidRole,
          };
        })
        .filter((employee) => employee.isActive && employee.hasValidRole);

      return validEmployees;
    } catch (error: any) {
      return [];
    }
  }

  private static async fetchAttendanceData(
    year: number,
    month: number
  ): Promise<any[]> {
    try {
      const period = `${year}-${month.toString().padStart(2, "0")}`;

      const { data, error } = await dbUtils
        .query(this.ATTENDANCE_TABLE_NAME)
        .raw.select(
          `
        id,
        date,
        employee_id,
        status,
        clock_in,
        clock_out,
        notes
        `
        )
        .like("date", `${period}%`)
        .is("deleted_at", null);

      if (error || !data) {
        return [];
      }

      return data;
    } catch (error: any) {
      return [];
    }
  }

  private static async fetchTodayAttendanceData(): Promise<any[]> {
    try {
      const now = new Date();
      const year = now.getFullYear();
      const month = (now.getMonth() + 1).toString().padStart(2, "0");
      const day = now.getDate().toString().padStart(2, "0");
      const today = `${year}-${month}-${day}`;

      const { data, error } = await dbUtils
        .query(this.ATTENDANCE_TABLE_NAME)
        .raw.select(
          `
        id,
        date,
        employee_id,
        status,
        clock_in,
        clock_out,
        notes
        `
        )
        .eq("date", today)
        .is("deleted_at", null);

      if (error || !data) {
        return [];
      }

      return data;
    } catch (error: any) {
      return [];
    }
  }

  private static calculateWorkingDays(year: number, month: number): number {
    const daysInMonth = new Date(year, month, 0).getDate();
    let workingDays = 0;

    for (let day = 1; day <= daysInMonth; day++) {
      const date = new Date(year, month - 1, day);
      const dayOfWeek = date.getDay();

      if (dayOfWeek !== 0 && dayOfWeek !== 6) {
        workingDays++;
      }
    }

    return workingDays;
  }

  private static calculateWorkingDaysUntilToday(
    year: number,
    month: number
  ): string[] {
    const now = new Date();
    const currentYear = now.getFullYear();
    const currentMonth = now.getMonth() + 1;

    if (year > currentYear || (year === currentYear && month > currentMonth)) {
      return [];
    }

    if (year < currentYear || (year === currentYear && month < currentMonth)) {
      const daysInMonth = new Date(year, month, 0).getDate();
      return this.getWorkingDaysInDateRange(year, month, 1, daysInMonth);
    }

    const today = now.getDate();
    return this.getWorkingDaysInDateRange(year, month, 1, today);
  }

  private static getWorkingDaysInDateRange(
    year: number,
    month: number,
    startDay: number,
    endDay: number
  ): string[] {
    const workingDays: string[] = [];
    const monthStr = month.toString().padStart(2, "0");

    for (let day = startDay; day <= endDay; day++) {
      const date = new Date(year, month - 1, day);
      const dayOfWeek = date.getDay();

      if (dayOfWeek !== 0 && dayOfWeek !== 6) {
        const dayStr = day.toString().padStart(2, "0");
        workingDays.push(`${year}-${monthStr}-${dayStr}`);
      }
    }

    return workingDays;
  }

  private static calculatePayReduction(
    employeeId: string,
    attendances: any[],
    baseSalary: number,
    year: number,
    month: number
  ): number {
    const workingDaysInMonth = this.calculateWorkingDaysUntilToday(year, month);
    const totalWorkingDays = this.calculateWorkingDays(year, month);

    if (workingDaysInMonth.length === 0) {
      return 0;
    }

    const dailyRate = baseSalary / totalWorkingDays;
    const employeeAttendances = attendances.filter(
      (att) => att.employee_id === employeeId
    );
    let totalDeduction = 0;

    for (const workingDay of workingDaysInMonth) {
      const dayAttendance = employeeAttendances.find(
        (att) => att.date === workingDay
      );

      if (!dayAttendance) {
        totalDeduction += dailyRate * ATTENDANCE_DEDUCTIONS["missing"];
        continue;
      }

      const status =
        dayAttendance.status.toLowerCase() as keyof typeof ATTENDANCE_DEDUCTIONS;

      if (status in ATTENDANCE_DEDUCTIONS) {
        const deductionRate = ATTENDANCE_DEDUCTIONS[status];
        const deduction = dailyRate * deductionRate;

        if (deduction > 0) {
          totalDeduction += deduction;
        }
      } else {
        totalDeduction += dailyRate * ATTENDANCE_DEDUCTIONS["missing"];
      }
    }

    return this.formatToTwoDecimals(totalDeduction);
  }

  private static checkPerfectAttendance(
    employeeId: string,
    attendances: any[],
    year: number,
    month: number
  ): boolean {
    if (attendances.length === 0) {
      return false;
    }

    const workingDaysInMonth = this.calculateWorkingDaysUntilToday(year, month);

    if (workingDaysInMonth.length === 0) {
      return true;
    }

    const employeeAttendances = attendances.filter(
      (att) => att.employee_id === employeeId
    );

    if (employeeAttendances.length === 0) {
      return false;
    }

    for (const workingDay of workingDaysInMonth) {
      const dayAttendance = employeeAttendances.find(
        (att) => att.date === workingDay
      );

      if (!dayAttendance || dayAttendance.status.toLowerCase() !== "present") {
        return false;
      }
    }

    return true;
  }

  private static getBaseSalaryForRole(role: string): number | null {
    for (const [keyRole, salary] of Object.entries(BASE_SALARIES)) {
      if (keyRole.toLowerCase() === role.toLowerCase()) {
        return salary;
      }
    }
    return null;
  }

  // Deprecated helper methods removed

  /**
   * Recalculates the total salary based on its components
   * This method should be called after any component (bonus, deduction, allowance) is updated
   * @param salaryId The ID of the salary to recalculate
   * @param userId The ID of the user making the change
   * @returns The updated salary or error
   */
  static async recalculateTotalSalary(salaryId: string, userId: string) {
    try {
      // Validate inputs
      if (!salaryId || salaryId.trim() === "") {
        return { data: null, error: new Error("Salary ID is required") };
      }

      if (!userId || userId.trim() === "") {
        return { data: null, error: new Error("User ID is required") };
      }

      // Get the current salary record
      const { data: existingSalary, error: fetchError } = await this.getById(
        salaryId
      );

      if (fetchError) {
        return { data: null, error: fetchError };
      }

      if (!existingSalary) {
        return {
          data: null,
          error: new Error(`Salary with ID ${salaryId} not found`),
        };
      }

      // Calculate the new total salary
      const totalSalary = this.formatToTwoDecimals(
        existingSalary.base_salary +
          existingSalary.total_bonus +
          existingSalary.total_allowance -
          existingSalary.total_deduction
      );

      // Only update if the total salary has changed
      if (Math.abs(totalSalary - existingSalary.total_salary) > 0.01) {
        const { error } = await dbUtils.update<Salary>(
          this.TABLE_NAME,
          salaryId,
          { total_salary: totalSalary },
          userId
        );

        if (error) {
          return { data: null, error };
        }

        return this.getById(salaryId);
      }

      return { data: existingSalary, error: null };
    } catch (error: any) {
      return {
        data: null,
        error: new Error(
          `Failed to recalculate total salary: ${error.message}`
        ),
      };
    }
  }

  static async getAll(options: QueryOptions = {}) {
    const now = new Date();
    const currentYear = now.getFullYear();
    const currentMonth = now.getMonth() + 1;
    const period = `${currentYear}-${currentMonth.toString().padStart(2, "0")}`;

    const employees = await this.getEmployees();

    // Check if employees exist
    if (!employees || employees.length === 0) {
      return {
        data: null,
        error: new Error("No active employees found in the system"),
        result: null,
      };
    }

    const { data: existingSalaries } = await this.getByPeriod(period);

    const employeesWithSalaries = new Set();
    if (existingSalaries && existingSalaries.length > 0) {
      existingSalaries.forEach((salary) => {
        employeesWithSalaries.add(salary.employee_id);
      });
    }

    const needSalaryGeneration = employees.some(
      (emp) => !employeesWithSalaries.has(emp.id)
    );

    if (needSalaryGeneration) {
      await this.updateSalaryPayReductionsFromTodayAttendance(
        "7d6efce6-f439-45ae-88ad-813db3a74cdc"
      );
    }

    const { data, error, result } = await dbUtils.getAll<Salary>(
      this.TABLE_NAME,
      options
    );

    if (error) {
      return { data: null, error, result: null };
    }

    if (!data || data.length === 0) {
      return {
        data: [],
        error: null,
        result: { count: 0 },
      };
    }

    if (data && data.length > 0) {
      const salariesWithEmployeeDetails = await Promise.all(
        data.map(async (salary) => {
          try {
            const { data: employee, error: employeeError } = await dbUtils
              .query(this.EMPLOYEE_TABLE_NAME)
              .raw.select(
                `
                id,
                profile_id,
                department,
                bank_account,
                bank_name
            `
              )
              .eq("id", salary.employee_id)
              .is("deleted_at", null)
              .single();

            if (employeeError || !employee) {
              return {
                ...salary,
                employee_details: {
                  department: "Not found",
                  bank_account: "Not found",
                  bank_name: "Not found",
                  role: "Not found",
                  fullname: "Employee not found",
                },
              };
            }

            const { data: profile } = await dbUtils
              .query(this.USER_PROFILES_TABLE_NAME)
              .raw.select(
                `
                role,
                fullname
            `
              )
              .eq("id", employee.profile_id)
              .is("deleted_at", null)
              .single();

            return {
              ...salary,
              employee_details: {
                department: employee.department,
                bank_account: employee.bank_account,
                bank_name: employee.bank_name,
                role: profile?.role || "Unknown",
                fullname: profile?.fullname || "Unknown",
              },
            };
          } catch (error) {
            return {
              ...salary,
              employee_details: {
                department: "Error",
                bank_account: "Error",
                bank_name: "Error",
                role: "Error",
                fullname: "Error retrieving employee details",
              },
            };
          }
        })
      );

      return { data: salariesWithEmployeeDetails, error: null, result };
    }

    return { data, error, result };
  }

  static async getById(id: string) {
    try {
      // Check if id is provided and valid
      if (!id || id.trim() === "") {
        return {
          data: null,
          error: {
            message: "Salary ID is required",
            type: "BAD_REQUEST",
          },
        };
      }

      const { data: salary, error } = await dbUtils
        .query(this.TABLE_NAME)
        .raw.select()
        .eq("id", id)
        .is("deleted_at", null)
        .single();

      if (error) {
        return {
          data: null,
          error: {
            message: `Salary with ID ${id} not found`,
            type: "NOT_FOUND",
          },
        };
      }

      if (!salary) {
        return {
          data: null,
          error: {
            message: `Salary with ID ${id} not found`,
            type: "NOT_FOUND",
          },
        };
      }

      return this.enrichSalaryWithEmployeeDetails(salary);
    } catch (error: any) {
      return {
        data: null,
        error: {
          message: `Failed to retrieve salary: ${error.message}`,
          type: "INTERNAL_ERROR",
        },
      };
    }
  }

  static async getByEmployeeId(employeeId: string) {
    try {
      // Check if employeeId is provided and valid
      if (!employeeId || employeeId.trim() === "") {
        return {
          data: null,
          error: {
            message: "Employee ID is required",
            type: "BAD_REQUEST",
          },
        };
      }

      // First check if employee exists
      const { data: employee, error: employeeError } = await dbUtils
        .query(this.EMPLOYEE_TABLE_NAME)
        .raw.select(`id`)
        .eq("id", employeeId)
        .is("deleted_at", null)
        .single();

      if (employeeError || !employee) {
        return {
          data: null,
          error: {
            message: `Employee with ID ${employeeId} not found`,
            type: "NOT_FOUND",
          },
        };
      }

      const { data, error } = await dbUtils
        .query(this.TABLE_NAME)
        .raw.select()
        .eq("employee_id", employeeId)
        .is("deleted_at", null)
        .order("period", { ascending: false });

      if (error) {
        return {
          data: null,
          error: {
            message: error.message || "Database error occurred",
            type: "DATABASE_ERROR",
          },
        };
      }

      if (!data || data.length === 0) {
        return { data: [], error: null };
      }

      const salariesWithEmployeeDetails = await Promise.all(
        data.map(async (salary) => {
          const enriched = await this.enrichSalaryWithEmployeeDetails(salary);
          return enriched.data;
        })
      );

      return { data: salariesWithEmployeeDetails, error: null };
    } catch (error: any) {
      return {
        data: null,
        error: {
          message: `Failed to retrieve employee salaries: ${error.message}`,
          type: "INTERNAL_ERROR",
        },
      };
    }
  }

  private static async enrichSalaryWithEmployeeDetails(salary: Salary) {
    try {
      const { data: employee, error: employeeError } = await dbUtils
        .query(this.EMPLOYEE_TABLE_NAME)
        .raw.select(
          `
        id,
        profile_id,
        department,
        bank_account,
        bank_name
        `
        )
        .eq("id", salary.employee_id)
        .is("deleted_at", null)
        .single();

      if (employeeError || !employee) {
        return { data: salary, error: null };
      }

      const { data: profile } = await dbUtils
        .query(this.USER_PROFILES_TABLE_NAME)
        .raw.select(
          `
        role,
        fullname
        `
        )
        .eq("id", employee.profile_id)
        .is("deleted_at", null)
        .single();

      return {
        data: {
          ...salary,
          employee_details: {
            department: employee.department,
            bank_account: employee.bank_account,
            bank_name: employee.bank_name,
            role: profile?.role || "Unknown",
            fullname: profile?.fullname || "Unknown",
          },
        },
        error: null,
      };
    } catch (error) {
      return { data: salary, error: null };
    }
  }

  static async getByPeriod(period: string) {
    const { data, error } = await dbUtils
      .query(this.TABLE_NAME)
      .raw.select()
      .eq("period", period)
      .is("deleted_at", null);

    if (error) {
      return { data: null, error };
    }

    return { data, error };
  }
  // Deprecated updateByHR method removed

  /**
   * Unified method to update a salary record with role-based permissions
   * @param salaryId ID of the salary to update
   * @param updateDto Data to update the salary with
   * @param userId ID of the user making the update
   * @param userRole Role of the user making the update
   * @returns The updated salary or error
   */
  static async updateSalary(
    salaryId: string,
    updateDto: UpdateSalaryDto,
    userId: string,
    userRole: UserRole
  ) {
    // Validate inputs
    if (!salaryId || salaryId.trim() === "") {
      return { data: null, error: new Error("Salary ID is required") };
    }

    if (!userId || userId.trim() === "") {
      return { data: null, error: new Error("User ID is required") };
    }

    // Get existing salary
    const { data: existingSalary, error: fetchError } = await this.getById(
      salaryId
    );

    if (fetchError) {
      return { data: null, error: fetchError };
    }

    if (!existingSalary) {
      return {
        data: null,
        error: new Error(`Salary with ID ${salaryId} not found`),
      };
    }

    // Role-based validation
    if (userRole === UserRole.HR) {
      // HR can only update base_salary and not for paid salaries
      if (updateDto.payment_status !== undefined) {
        return {
          data: null,
          error: new Error("HR cannot update payment status"),
        };
      }

      if (existingSalary.payment_status === SalaryPaymentStatus.PAID) {
        return {
          data: null,
          error: new Error("Cannot update salary that has been paid"),
        };
      }
    }

    // Create update object
    const dbUpdateData: Partial<Salary> = {};

    if (updateDto.base_salary !== undefined) {
      dbUpdateData.base_salary = this.formatToTwoDecimals(
        updateDto.base_salary
      );
    }

    if (
      updateDto.payment_status !== undefined &&
      (userRole === UserRole.Finance ||
        userRole === UserRole.Manager ||
        userRole === UserRole.Admin)
    ) {
      dbUpdateData.payment_status = updateDto.payment_status;
    }

    // If no properties to update, return early
    if (Object.keys(dbUpdateData).length === 0) {
      return { data: existingSalary, error: null };
    }

    // Track changes in history before updating
    await SalaryUpdateHistoryService.trackSalaryUpdate(
      salaryId,
      existingSalary,
      dbUpdateData,
      userId
    );

    const { error } = await dbUtils.update<Salary>(
      this.TABLE_NAME,
      salaryId,
      dbUpdateData,
      userId
    );

    if (error) {
      return { data: null, error };
    }

    // Get the updated salary record
    return this.getById(salaryId);
  }

  // Deprecated updateByFinance method removed

  static async updateSalaryPayReductionsFromTodayAttendance(
    userId: string
  ): Promise<{ success: boolean; updated: number; error?: Error }> {
    try {
      const now = new Date();
      const currentYear = now.getFullYear();
      const currentMonth = now.getMonth() + 1;
      const period = `${currentYear}-${currentMonth
        .toString()
        .padStart(2, "0")}`;

      const todayAttendances = await this.fetchTodayAttendanceData();

      if (todayAttendances.length === 0) {
        return { success: true, updated: 0 };
      }

      const monthlyAttendances = await this.fetchAttendanceData(
        currentYear,
        currentMonth
      );

      const { data: existingSalaries, error: periodError } =
        await this.getByPeriod(period);

      if (periodError) {
        return { success: false, updated: 0, error: periodError };
      }

      if (!existingSalaries || existingSalaries.length === 0) {
        await this.generateMonthlySalaries(userId);
        return { success: true, updated: 0 };
      }

      const employees = await this.getEmployees();
      const employeeMap = new Map();
      employees.forEach((emp) => {
        employeeMap.set(emp.id, emp);
      });

      const employeesWithTodayAttendance = new Set(
        todayAttendances.map((a) => a.employee_id)
      );

      let updatedCount = 0;

      for (const salary of existingSalaries) {
        try {
          if (!employeesWithTodayAttendance.has(salary.employee_id)) {
            continue;
          }

          const employee = employeeMap.get(salary.employee_id);

          if (!employee) {
            continue;
          }

          const baseSalary = this.getBaseSalaryForRole(employee.role);

          if (baseSalary === null) {
            continue;
          }

          // Calculate new deduction based on attendance
          const newPayReduction = this.calculatePayReduction(
            employee.id,
            monthlyAttendances,
            baseSalary,
            currentYear,
            currentMonth
          );

          // Check for perfect attendance
          const hasPerfectAttendance = this.checkPerfectAttendance(
            employee.id,
            monthlyAttendances,
            currentYear,
            currentMonth
          );

          // Calculate bonus amount
          const bonusPercentage = hasPerfectAttendance ? 0.2 : 0;
          const newBonusAmount = this.formatToTwoDecimals(
            baseSalary * bonusPercentage
          );

          // Get existing bonus and deduction records
          const { data: existingBonuses } =
            await BonusSalaryService.getBySalaryId(salary.id);
          const { data: existingDeductions } =
            await DeductionSalaryService.getBySalaryId(salary.id);

          // Find attendance-related records
          const attendanceBonus = existingBonuses?.find(
            (b) =>
              b.bonus_type === BonusSalaryType.OTHER &&
              b.notes?.includes("Perfect attendance")
          );

          const attendanceDeduction = existingDeductions?.find(
            (d) =>
              d.deduction_type === DeductionType.ABSENCE &&
              d.notes?.includes("Attendance-based")
          );

          let updated = false;

          // Update or create bonus record if needed
          if (hasPerfectAttendance) {
            if (attendanceBonus && attendanceBonus.id) {
              // Update existing bonus if amount changed
              if (Math.abs(attendanceBonus.amount - newBonusAmount) > 0.01) {
                await BonusSalaryService.update(
                  attendanceBonus.id,
                  { amount: newBonusAmount },
                  userId
                );
                updated = true;
              }
            } else {
              // Create new bonus record
              await BonusSalaryService.create(
                {
                  salary_id: salary.id,
                  amount: newBonusAmount,
                  bonus_type: BonusSalaryType.OTHER,
                  notes: "Perfect attendance bonus",
                },
                userId
              );
              updated = true;
            }
          } else if (attendanceBonus && attendanceBonus.id) {
            // Remove bonus if no longer has perfect attendance
            await BonusSalaryService.delete(attendanceBonus.id, userId);
            updated = true;
          }

          // Update or create deduction record if needed
          if (newPayReduction > 0) {
            if (attendanceDeduction && attendanceDeduction.id) {
              // Update existing deduction if amount changed
              if (
                Math.abs(attendanceDeduction.amount - newPayReduction) > 0.01
              ) {
                await DeductionSalaryService.update(
                  attendanceDeduction.id,
                  { amount: newPayReduction },
                  userId
                );
                updated = true;
              }
            } else {
              // Create new deduction record
              await DeductionSalaryService.create(
                {
                  salary_id: salary.id,
                  amount: newPayReduction,
                  deduction_type: DeductionType.ABSENCE,
                  notes: "Attendance-based deduction",
                },
                userId
              );
              updated = true;
            }
          } else if (attendanceDeduction && attendanceDeduction.id) {
            // Remove deduction if no longer needed
            await DeductionSalaryService.delete(attendanceDeduction.id, userId);
            updated = true;
          }

          if (updated) {
            updatedCount++;
          }
        } catch (error: any) {
          continue;
        }
      }

      return { success: true, updated: updatedCount };
    } catch (error: any) {
      return {
        success: false,
        updated: 0,
        error: new Error(
          `Failed to update salary pay reductions: ${error.message}`
        ),
      };
    }
  }

  static async generateMonthlySalaries(
    userId: string,
    forceGeneration = false
  ): Promise<{ data: Salary[] | null; error: Error | null }> {
    try {
      const employees = await this.getEmployees();

      if (employees.length === 0) {
        return { data: [], error: null };
      }

      const now = new Date();
      const currentYear = now.getFullYear();
      const currentMonth = now.getMonth() + 1;

      const attendances = await this.fetchAttendanceData(
        currentYear,
        currentMonth
      );
      const period = `${currentYear}-${currentMonth
        .toString()
        .padStart(2, "0")}`;

      const { data: existingSalaries } = await this.getByPeriod(period);

      const employeesWithSalaries = new Set();

      if (existingSalaries && existingSalaries.length > 0) {
        existingSalaries.forEach((salary) => {
          employeesWithSalaries.add(salary.employee_id);
        });
      }

      const createdSalaries: Salary[] = [];

      for (const employee of employees) {
        try {
          if (employeesWithSalaries.has(employee.id) && !forceGeneration) {
            continue;
          }

          const employeeRole = employee.role;
          const baseSalary = this.getBaseSalaryForRole(employeeRole);

          if (baseSalary === null) {
            continue;
          }

          // Check if we're updating an existing salary or creating a new one
          let salaryId: string;
          let existingSalary: Salary | undefined;

          if (forceGeneration && employeesWithSalaries.has(employee.id)) {
            // Update existing salary
            existingSalary = existingSalaries?.find(
              (s) => s.employee_id === employee.id
            );

            if (existingSalary && existingSalary.id) {
              salaryId = existingSalary.id;

              // Update only the base salary
              const { error } = await dbUtils.update<Salary>(
                this.TABLE_NAME,
                salaryId,
                { base_salary: baseSalary },
                userId
              );

              if (error) {
                continue;
              }
            } else {
              continue; // Skip if we can't find the existing salary
            }
          } else {
            // Create a new salary record with just the base salary
            salaryId = this.generateSalaryId();

            const salaryData = {
              id: salaryId,
              employee_id: employee.id,
              base_salary: baseSalary,
              total_bonus: 0, // Will be updated by BonusSalaryService
              total_deduction: 0, // Will be updated by DeductionSalaryService
              total_allowance: 0,
              total_salary: baseSalary, // Initial value is just the base salary
              payment_status: SalaryPaymentStatus.UNPAID,
              period: period,
            };

            const { data: createdSalary, error } = await dbUtils.create<Salary>(
              this.TABLE_NAME,
              salaryData,
              userId
            );

            if (error || !createdSalary) {
              continue;
            }

            if (createdSalary) {
              createdSalaries.push(createdSalary);
            }
          }

          // Now add the components using their respective services

          // 1. Add attendance-based bonus if applicable
          const hasPerfectAttendance = this.checkPerfectAttendance(
            employee.id,
            attendances,
            currentYear,
            currentMonth
          );

          if (hasPerfectAttendance) {
            const bonusAmount = this.formatToTwoDecimals(baseSalary * 0.2);

            // Create a bonus record
            await BonusSalaryService.create(
              {
                salary_id: salaryId,
                amount: bonusAmount,
                bonus_type: BonusSalaryType.OTHER,
                notes: "Perfect attendance bonus",
              },
              userId
            );
          }

          // 2. Add attendance-based deductions if applicable
          const payReduction = this.calculatePayReduction(
            employee.id,
            attendances,
            baseSalary,
            currentYear,
            currentMonth
          );

          if (payReduction > 0) {
            // Create a deduction record
            await DeductionSalaryService.create(
              {
                salary_id: salaryId,
                amount: payReduction,
                deduction_type: DeductionType.ABSENCE,
                notes: "Attendance-based deduction",
              },
              userId
            );
          }

          // The total salary will be automatically recalculated by the component services

          // Get the updated salary to add to our result
          const { data: updatedSalary } = await this.getById(salaryId);
          if (
            updatedSalary &&
            !createdSalaries.some((s) => s.id === salaryId)
          ) {
            createdSalaries.push(updatedSalary);
          }
        } catch (error: any) {
          continue;
        }
      }

      return { data: createdSalaries, error: null };
    } catch (error: any) {
      return {
        data: null,
        error: new Error(
          `Failed to generate monthly salaries: ${error.message}`
        ),
      };
    }
  }

  static setupEndOfDayUpdates() {
    const runUpdate = async () => {
      try {
        await this.generateMonthlySalaries(
          "7d6efce6-f439-45ae-88ad-813db3a74cdc"
        );
      } catch (error) {}
    };

    const scheduleNextUpdate = () => {
      const now = new Date();
      const nextRun = new Date(
        now.getFullYear(),
        now.getMonth(),
        now.getDate(),
        23,
        59
      );

      if (now >= nextRun) {
        nextRun.setDate(nextRun.getDate() + 1);
      }

      const timeUntilNextRun = nextRun.getTime() - now.getTime();

      setTimeout(() => {
        runUpdate();
        scheduleNextUpdate();
      }, timeUntilNextRun);
    };

    scheduleNextUpdate();
  }
}

export default SalaryService;
