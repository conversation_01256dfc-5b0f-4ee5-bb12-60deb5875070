import { TaskService } from "./service";
import { TaskStatus } from "../../database/models/task.model";

export class TaskController {
  /**
   * Update task status
   */
  static async updateStatus(context: any) {
    const { params, body, user, success, serverError, notFound, badRequest } =
      context;

    try {
      const { id } = params;
      const { completion_status } = body;

      // Validate boolean status for regular tasks
      if (typeof completion_status !== "boolean") {
        return badRequest(
          "Invalid task status - must be boolean (true/false)",
          "INVALID_STATUS"
        );
      }

      const { data, error } = await TaskService.updateStatus(
        id,
        completion_status,
        user.id
      );

      if (error) {
        if (error.message.includes("not found")) {
          return notFound("Task not found", "TASK_NOT_FOUND");
        }
        return serverError(error.message, error);
      }

      return success(data, "Task status updated successfully");
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : "Failed to update task status";
      return serverError(errorMessage);
    }
  }
}
