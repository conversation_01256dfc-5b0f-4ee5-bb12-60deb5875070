import { <PERSON><PERSON> } from "elysia";
import { taskRoutes } from "./routes";
import { apiResponse } from "../../middleware/api-response";

// Create an instance with the middleware applied
const taskApp = new Elysia().use(apiResponse).use(taskRoutes);

export * from "./service";

// Export the task module
export const tasks = taskApp;

// Re-export for convenience
export * from "./schema";
export * from "./controller";
