import { <PERSON><PERSON> } from "elysia";
import { TaskController } from "./controller";
import { updateTaskStatusSchema } from "./schema";
import { auth } from "../../middleware/auth";

export const taskRoutes = new Elysia({ prefix: "/tasks" })
  .use(auth)
  
  // Update task status
  .patch(
    "/:id/status",
    TaskController.updateStatus,
    {
      params: updateTaskStatusSchema.params,
      body: updateTaskStatusSchema.body,
      detail: {
        summary: "Update task status",
        tags: ["Tasks"],
      },
    }
  );
