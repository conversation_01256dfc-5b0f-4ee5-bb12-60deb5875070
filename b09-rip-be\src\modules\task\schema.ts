import { t } from "elysia";
// Note: TaskStatus enum still exists for project tasks, but not used here

// Schema for updating regular task status
export const updateTaskStatusSchema = {
  params: t.Object({
    id: t.String({
      format: "uuid",
      description: "The ID of the task",
    }),
  }),
  body: t.Object({
    completion_status: t.<PERSON>({
      description:
        "Task completion status (true = completed, false = not completed)",
    }),
  }),
};
