import { dbUtils } from "../../utils/database";
import { Task, TaskStatus } from "../../database/models/task.model";

export class TaskService {
  private static readonly TABLE_NAME = "tasks";

  /**
   * Update task status (for regular tasks only)
   * @param id Task ID
   * @param status New task status (boolean)
   * @param userId User ID for audit
   * @returns Updated task
   */
  static async updateStatus(id: string, status: boolean, userId: string) {
    // First check if the task exists
    try {
      const { data: task, error: getError } = await dbUtils.getById<Task>(
        this.TABLE_NAME,
        id
      );

      if (getError || !task) {
        return {
          data: null,
          error: getError || new Error("Task not found"),
        };
      }

      // Update only the status
      const { data, error } = await dbUtils.update<Task>(
        this.TABLE_NAME,
        id,
        { completion_status: status },
        userId
      );

      return { data, error };
    } catch (error) {
      return {
        data: null,
        error:
          error instanceof Error
            ? error
            : new Error("Failed to update task status"),
      };
    }
  }
}
