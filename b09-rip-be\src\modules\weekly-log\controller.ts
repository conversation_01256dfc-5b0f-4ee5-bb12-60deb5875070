import { WeeklyLogService } from "./service";
import { UpdateWeeklyLogNotesDto } from "../../database/models/weekly-log.model";
import { QueryOptions } from "../../utils/database.types";

// Helper function to ensure response functions are available
const ensureResponseFunctions = (context: any) => {
  const { success, serverError, notFound, badRequest } = context;
  if (!success || !serverError || !notFound || !badRequest) {
    throw new Error("Response functions not available");
  }
  return context;
};

export class WeeklyLogController {
  /**
   * Get weekly log by project ID and week number
   */
  static async getByProjectAndWeek(context: any) {
    // Destructure outside try block so it's accessible in catch block
    const {
      params = {},
      success,
      serverError,
      notFound,
      badRequest,
    } = ensureResponseFunctions(context);

    try {
      // Extract project ID and week number from path parameters
      const { projectId, weekNumber } = params;

      // Validate project ID
      if (!projectId) {
        return badRequest("Project ID is required");
      }

      // Convert week number to number if provided
      let weekNumberNum: number | undefined;
      if (weekNumber) {
        weekNumberNum = Number(weekNumber);
        // Only check lower bound - no upper bound on week numbers as per requirements
        if (isNaN(weekNumberNum) || weekNumberNum < 1) {
          return badRequest("Week number must be a positive integer");
        }
      }

      // Call the service method
      const { data, error } = await WeeklyLogService.getByProjectAndWeek(
        projectId,
        weekNumberNum
      );

      if (error) {
        if (
          error.message === "Weekly log not found" ||
          error.message === "No weekly logs found for this project"
        ) {
          return notFound(error.message);
        }
        return serverError(error.message, error);
      }

      // Return success response with the weekly log data
      return success(data, "Weekly log retrieved successfully");
    } catch (err) {
      console.error("Error in WeeklyLogController.getByProjectAndWeek:", err);
      return serverError(
        "Unexpected error occurred",
        err instanceof Error ? err : undefined
      );
    }
  }

  /**
   * Get all weekly logs with optional filtering and pagination
   */
  static async getAll(context: any) {
    // Destructure outside try block so it's accessible in catch block
    const {
      query = {},
      success,
      serverError,
      badRequest,
    } = ensureResponseFunctions(context);

    try {
      // Build query options
      const options: QueryOptions = {
        filters: [],
        // Always apply pagination, with default values if not provided
        pagination: {
          page: query.page ? Number(query.page) : 1,
          pageSize: query.limit ? Number(query.limit) : 10,
        },
      };

      // Add project_id filter if provided
      if (query.project_id) {
        options.filters!.push({
          field: "project_id",
          value: query.project_id,
        });
      }

      // Add week_number filter if provided
      if (query.week_number) {
        const weekNumber = Number(query.week_number);
        // Only check lower bound - no upper bound on week numbers as per requirements
        if (isNaN(weekNumber) || weekNumber < 1) {
          return badRequest("Week number must be a positive integer");
        }
        options.filters!.push({
          field: "week_number",
          value: weekNumber,
        });
      }

      // Call the service with the constructed options
      const { data, error, result } = await WeeklyLogService.getAll(options);

      if (error) {
        return serverError(error.message, error);
      }

      // Include pagination result in the response if available
      return success(
        {
          items: data,
          pagination: result,
        },
        "Weekly logs retrieved successfully"
      );
    } catch (err) {
      console.error("Error in WeeklyLogController.getAll:", err);
      return serverError(
        "Unexpected error occurred",
        err instanceof Error ? err : undefined
      );
    }
  }

  /**
   * Manually trigger weekly log creation for testing
   * This endpoint should be protected and only accessible by admins
   */
  static async triggerWeeklyLogCreation(context: any) {
    // Destructure outside try block so it's accessible in catch block
    const { user, success, serverError, badRequest } =
      ensureResponseFunctions(context);

    try {
      // 1. Extract user ID from the request
      if (!user || !user.id) {
        return badRequest("User ID is required");
      }

      // 2. Call the service method
      const { data, error } = await WeeklyLogService.createWeeklyLogs(user.id);

      if (error) {
        return serverError(error.message, error);
      }

      // 3. Handle the response
      return success(data, "Weekly logs created successfully");
    } catch (err) {
      console.error(
        "Error in WeeklyLogController.triggerWeeklyLogCreation:",
        err
      );
      return serverError(
        "Unexpected error occurred",
        err instanceof Error ? err : undefined
      );
    }
  }

  /**
   * Batch update notes for a weekly log
   */
  static async updateNotes(context: any) {
    // Destructure outside try block so it's accessible in catch block
    const {
      params = {},
      body = {},
      user = {},
      success,
      serverError,
      notFound,
      badRequest,
    } = ensureResponseFunctions(context);

    try {
      // 1. Extract data from the request
      const weeklyLogId = params.id;
      const { notes } = body;
      const userId = user.id;

      // 2. Validate input data
      if (!weeklyLogId) {
        return badRequest("Weekly log ID is required");
      }

      if (!notes || typeof notes !== "object") {
        return badRequest("Notes data is required and must be an object");
      }

      // Validate each key and value in notes
      for (const dayStr in notes) {
        const day = Number(dayStr);
        if (isNaN(day) || day < 1 || day > 5) {
          return badRequest("Day of week must be between 1 and 5");
        }

        if (typeof notes[day] !== "string") {
          return badRequest("Note text must be a string");
        }
      }

      if (!userId) {
        return badRequest("User ID is required");
      }

      // 3. Call the service method
      const { data, error } = await WeeklyLogService.updateNotes(
        weeklyLogId,
        { notes },
        userId
      );

      // 4. Handle the response
      if (error) {
        if (error.message === "Weekly log not found") {
          return notFound(error.message);
        }
        if (
          error.message.includes("Invalid") ||
          error.message.includes("required")
        ) {
          return badRequest(error.message);
        }
        return serverError(error.message, error);
      }

      // Return success response with the update result
      return success(data, "Notes updated successfully");
    } catch (err) {
      console.error("Error in WeeklyLogController.updateNotes:", err);
      return serverError(
        "Unexpected error occurred",
        err instanceof Error ? err : undefined
      );
    }
  }

  /**
   * Get all available week numbers for a specific project
   */
  static async getAvailableWeekNumbers(context: any) {
    // Destructure outside try block so it's accessible in catch block
    const {
      params = {},
      success,
      serverError,
      badRequest,
    } = ensureResponseFunctions(context);

    try {
      // Extract project_id from path parameters
      const { projectId } = params;

      // Validate project ID
      if (!projectId) {
        return badRequest("Project ID is required");
      }

      // Call the service method
      const { data, data_with_ranges, error } =
        await WeeklyLogService.getAvailableWeekNumbers(projectId);

      if (error) {
        return serverError(error.message, error);
      }

      // Return success response with the list of week numbers and date ranges
      return success(
        {
          week_numbers: data,
          week_numbers_with_ranges: data_with_ranges,
        },
        "Available week numbers retrieved successfully"
      );
    } catch (err) {
      console.error(
        "Error in WeeklyLogController.getAvailableWeekNumbers:",
        err
      );
      return serverError(
        "Unexpected error occurred",
        err instanceof Error ? err : undefined
      );
    }
  }

  /**
   * Get weekly log by ID
   */
  static async getById(context: any) {
    // Destructure outside try block so it's accessible in catch block
    const {
      params = {},
      success,
      serverError,
      notFound,
      badRequest,
    } = ensureResponseFunctions(context);

    try {
      // Extract weekly log ID from path parameters
      const { id } = params;

      // Validate weekly log ID
      if (!id) {
        return badRequest("Weekly log ID is required");
      }

      // Call the service method
      const { data, error } = await WeeklyLogService.getById(id);

      if (error) {
        return serverError(error.message, error);
      }

      if (!data) {
        return notFound("Weekly log not found");
      }

      // Return success response with the weekly log data
      return success(data, "Weekly log retrieved successfully");
    } catch (err) {
      console.error("Error in WeeklyLogController.getById:", err);
      return serverError(
        "Unexpected error occurred",
        err instanceof Error ? err : undefined
      );
    }
  }
}
