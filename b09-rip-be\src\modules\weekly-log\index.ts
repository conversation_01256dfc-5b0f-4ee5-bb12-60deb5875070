import { Elysia } from "elysia";
import { weeklyLogRoutes } from "./routes";
import { apiResponse } from "../../middleware/api-response";
import { WeeklyLogService } from "./service";

// Create an instance with the middleware applied
const weeklyLogApp = new Elysia().use(apiResponse).use(weeklyLogRoutes);

// Set up scheduler for automatic weekly log creation with error handling
try {
  WeeklyLogService.setupWeeklyLogScheduler();
  console.log("Weekly log scheduler set up successfully");
} catch (error) {
  console.error("Failed to set up weekly log scheduler:", error);
}

export * from "./service";

// Export the weekly log module
export const weeklyLogs = weeklyLogApp;

// Re-export for convenience
export * from "./schema";
export * from "./controller";
