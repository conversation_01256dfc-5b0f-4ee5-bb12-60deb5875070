import { Elysia } from "elysia";
import { WeeklyLogController } from "./controller";
import { requireActiveUser, checkRoles } from "../../middleware/auth";
import { UserRole } from "../../database/models/user-profile.model";
import {
  getWeeklyLogByProjectAndWeekSchema,
  getAllWeeklyLogsSchema,
  updateWeeklyLogNotesSchema,
  getAvailableWeekNumbersSchema,
  getWeeklyLogByIdSchema,
} from "./schema";
import { t } from "elysia";

export const weeklyLogRoutes = (app: Elysia) =>
  app.group("/weekly-logs", (app) =>
    app
      // First ensure users are active
      .use(requireActiveUser)

      // Get all weekly logs with filtering and pagination
      .get("/", WeeklyLogController.getAll, {
        query: getAllWeeklyLogsSchema.query,
        detail: {
          tags: ["weekly-logs"],
          summary: "Get all weekly logs",
          description: "Retrieve all weekly logs with filtering and pagination",
          security: [{ bearerAuth: [] }],
        },
      })

      // Get weekly log by project ID and week number
      .get("/:projectId/:weekNumber", WeeklyLogController.getByProjectAndWeek, {
        params: getWeeklyLogByProjectAndWeekSchema.params,
        detail: {
          tags: ["weekly-logs"],
          summary: "Get weekly log by project and week",
          description: "Retrieve a weekly log by project ID and week number",
          security: [{ bearerAuth: [] }],
        },
      })

      // Get weekly log by project ID (uses latest week)
      .get("/:projectId", WeeklyLogController.getByProjectAndWeek, {
        params: t.Object({
          projectId: t.String({
            format: "uuid",
            description: "Project ID",
          }),
        }),
        detail: {
          tags: ["weekly-logs"],
          summary: "Get latest weekly log by project",
          description: "Retrieve the latest weekly log for a project",
          security: [{ bearerAuth: [] }],
        },
      })

      // Batch update notes for a weekly log
      .put("/:id/notes", WeeklyLogController.updateNotes, {
        params: updateWeeklyLogNotesSchema.params,
        body: updateWeeklyLogNotesSchema.body,
        detail: {
          tags: ["weekly-logs"],
          summary: "Batch update notes for a weekly log",
          description: "Update notes for multiple days in a weekly log",
          security: [{ bearerAuth: [] }],
        },
      })

      // Get weekly log by ID
      .get("/id/:id", WeeklyLogController.getById, {
        params: getWeeklyLogByIdSchema.params,
        detail: {
          tags: ["weekly-logs"],
          summary: "Get weekly log by ID",
          description: "Retrieve a weekly log by its ID with enhanced data",
          security: [{ bearerAuth: [] }],
        },
      })

      // Get available week numbers for a project
      .get(
        "/project/:projectId/weeks",
        WeeklyLogController.getAvailableWeekNumbers,
        {
          params: getAvailableWeekNumbersSchema.params,
          detail: {
            tags: ["weekly-logs"],
            summary: "Get available week numbers for a project",
            description:
              "Retrieve all available week numbers for a specific project",
            security: [{ bearerAuth: [] }],
          },
        }
      )

      // Manually trigger weekly log creation (admin only)
      .post("/trigger-creation", WeeklyLogController.triggerWeeklyLogCreation, {
        beforeHandle: [checkRoles([UserRole.Admin])],
        detail: {
          tags: ["weekly-logs"],
          summary: "Trigger weekly log creation",
          description: "Manually trigger weekly log creation for testing",
          security: [{ bearerAuth: [] }],
        },
      })
  );
