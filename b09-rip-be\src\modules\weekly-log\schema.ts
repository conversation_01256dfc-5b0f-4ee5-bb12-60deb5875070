import { t } from "elysia";

// Schema for getting weekly log by project ID and week number
export const getWeeklyLogByProjectAndWeekSchema = {
  params: t.Object({
    projectId: t.String({
      format: "uuid",
      description: "Project ID",
    }),
    weekNumber: t.Number({
      description: "Project-specific week number (starts from 1)",
      minimum: 1,
    }),
  }),
};

// Schema for getting available week numbers for a project
export const getAvailableWeekNumbersSchema = {
  params: t.Object({
    projectId: t.String({
      format: "uuid",
      description: "Project ID",
    }),
  }),
};

// Schema for getting weekly log by ID
export const getWeeklyLogByIdSchema = {
  params: t.Object({
    id: t.String({
      format: "uuid",
      description: "Weekly log ID",
    }),
  }),
};

// Schema for getting all weekly logs
export const getAllWeeklyLogsSchema = {
  query: t.Object({
    page: t.Optional(
      t.Number({
        minimum: 1,
        default: 1,
        description: "Page number",
      })
    ),
    limit: t.Optional(
      t.Number({
        minimum: 1,
        maximum: 100,
        default: 10,
        description: "Items per page",
      })
    ),
    project_id: t.Optional(
      t.String({
        format: "uuid",
        description: "Filter by project ID",
      })
    ),
    week_number: t.Optional(
      t.Number({
        minimum: 1,
        description: "Filter by project-specific week number (starts from 1)",
      })
    ),
  }),
};

// Schema for batch updating notes for a weekly log
export const updateWeeklyLogNotesSchema = {
  params: t.Object({
    id: t.String({
      format: "uuid",
      description: "Weekly log ID",
    }),
  }),
  body: t.Object({
    notes: t.Record(
      t.Number({
        minimum: 1,
        maximum: 5,
        description: "Day of week (1-5, where 1 is Monday, 5 is Friday)",
      }),
      t.String({
        description: "Note text (non-empty to create/update, empty to delete)",
      })
    ),
  }),
};
