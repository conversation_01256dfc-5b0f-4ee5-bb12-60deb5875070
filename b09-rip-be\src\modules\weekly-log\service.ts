import { dbUtils } from "../../utils/database";
import {
  WeeklyLog,
  WeeklyLogNote,
  CreateWeeklyLogDto,
  WeeklyLogWithNotes,
  UpdateWeeklyLogNotesDto,
  BatchUpdateResult,
  WeeklyLogDayData,
} from "../../database/models/weekly-log.model";
import { ProjectTask } from "../../database/models/project-task.model";
import { TaskStatus } from "../../database/models/task.model";
import { QueryOptions } from "../../utils/database.types";
import { supabase } from "../../libs/supabase";
import {
  getCurrentJakartaDate,
  getCurrentDayOfWeek,
  isSaturday,
  getProjectWeekNumber,
  getProjectWeekStartDate,
  getProjectWeekEndDate,
  formatWeekDateRange,
} from "../../utils/date";
import {
  acquireSchedulerLock,
  releaseSchedulerLock,
  recordSchedulerRun,
} from "../../utils/scheduler";

// Helper function to check if a string looks like a UUID
function looksLikeUuid(str: string): boolean {
  const uuidRegex =
    /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i;
  return uuidRegex.test(str);
}

export class WeeklyLogService {
  private static readonly TABLE_NAME = "weekly_logs";
  private static readonly NOTES_TABLE_NAME = "weekly_log_notes";
  private static readonly PROJECTS_TABLE = "projects";
  private static readonly PROJECT_TASKS_TABLE = "project_tasks";
  private static readonly USER_PROFILES_TABLE = "user_profiles";
  private static readonly EMPLOYEES_TABLE = "employees";

  /**
   * Helper function to fetch user names for a list of user IDs
   * @param userIds Array of user IDs
   * @returns Map of user ID to user name
   */
  private static async getUserNames(
    userIds: string[]
  ): Promise<Map<string, string>> {
    // Remove duplicates and null/undefined values
    const uniqueUserIds = [...new Set(userIds.filter((id) => id))];

    if (uniqueUserIds.length === 0) {
      return new Map();
    }

    try {
      // Fetch user profiles for these IDs
      const { data, error } = await supabase
        .from(this.USER_PROFILES_TABLE)
        .select("user_id, fullname")
        .in("user_id", uniqueUserIds)
        .is("deleted_at", null);

      if (error || !data) {
        console.error("Error fetching user profiles:", error);
        return new Map();
      }

      // Create a map of user ID to user name
      const userNameMap = new Map<string, string>();
      data.forEach((profile) => {
        userNameMap.set(profile.user_id, profile.fullname);
      });

      return userNameMap;
    } catch (error) {
      console.error("Error in getUserNames:", error);
      return new Map();
    }
  }

  /**
   * Helper function to fetch employee names for a list of employee IDs
   * @param employeeIds Array of employee IDs
   * @returns Map of employee ID to employee name
   */
  private static async getEmployeeNames(
    employeeIds: string[]
  ): Promise<Map<string, string>> {
    // Remove duplicates and null/undefined values
    const uniqueEmployeeIds = [...new Set(employeeIds.filter((id) => id))];

    if (uniqueEmployeeIds.length === 0) {
      return new Map();
    }

    try {
      // Fetch employees with their profile information
      const { data, error } = await supabase
        .from(this.EMPLOYEES_TABLE)
        .select(
          `
          id,
          profile_id
        `
        )
        .in("id", uniqueEmployeeIds)
        .is("deleted_at", null);

      if (error || !data) {
        console.error("Error fetching employee data:", error);
        return new Map();
      }

      // Extract profile IDs
      const profileIds = data
        .map((employee) => employee.profile_id)
        .filter((id) => id);

      if (profileIds.length === 0) {
        return new Map();
      }

      // Fetch user profiles for these profile IDs
      const { data: profilesData, error: profilesError } = await supabase
        .from(this.USER_PROFILES_TABLE)
        .select("id, fullname")
        .in("id", profileIds)
        .is("deleted_at", null);

      if (profilesError || !profilesData) {
        console.error("Error fetching user profiles:", profilesError);
        return new Map();
      }

      // Create a map of profile ID to fullname
      const profileNameMap = new Map<string, string>();
      profilesData.forEach((profile) => {
        profileNameMap.set(profile.id, profile.fullname);
      });

      // Create a map of employee ID to employee name
      const employeeNameMap = new Map<string, string>();
      data.forEach((employee) => {
        const name =
          profileNameMap.get(employee.profile_id) || "Unknown Employee";
        employeeNameMap.set(employee.id, name);
      });

      return employeeNameMap;
    } catch (error) {
      console.error("Error in getEmployeeNames:", error);
      return new Map();
    }
  }

  /**
   * Create a new weekly log
   * @param data Weekly log data
   * @param userId User ID for audit
   * @returns Created weekly log
   */
  static async create(data: CreateWeeklyLogDto, userId: string) {
    try {
      // 1. Validate input data
      // - Ensure data.project_id is a valid UUID
      if (!data.project_id || !looksLikeUuid(data.project_id)) {
        return {
          data: null,
          error: new Error("Invalid project ID"),
        };
      }

      // - Ensure data.week_number is a positive number
      if (!data.week_number || data.week_number < 1) {
        return {
          data: null,
          error: new Error("Week number must be a positive integer"),
        };
      }

      // - Ensure data.week_start_date and data.week_end_date are valid dates in YYYY-MM-DD format
      const dateRegex = /^\d{4}-\d{2}-\d{2}$/;
      if (!data.week_start_date || !dateRegex.test(data.week_start_date)) {
        return {
          data: null,
          error: new Error("Invalid week start date format. Use YYYY-MM-DD"),
        };
      }
      if (!data.week_end_date || !dateRegex.test(data.week_end_date)) {
        return {
          data: null,
          error: new Error("Invalid week end date format. Use YYYY-MM-DD"),
        };
      }

      // - Ensure week_start_date is a Monday and week_end_date is a Sunday
      const startDate = new Date(data.week_start_date);
      const endDate = new Date(data.week_end_date);

      if (startDate.getDay() !== 1) {
        // 0 is Sunday, 1 is Monday
        return {
          data: null,
          error: new Error("Week start date must be a Monday"),
        };
      }
      if (endDate.getDay() !== 0) {
        // 0 is Sunday
        return {
          data: null,
          error: new Error("Week end date must be a Sunday"),
        };
      }

      // - Ensure week_start_date and week_end_date are 7 days apart
      const diffTime = Math.abs(endDate.getTime() - startDate.getTime());
      const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
      if (diffDays !== 6) {
        // 6 days difference means 7 days inclusive
        return {
          data: null,
          error: new Error("Week start and end dates must be 7 days apart"),
        };
      }

      // 2. Check if a weekly log already exists for this project and week
      const { data: existingLog } = await supabase
        .from(this.TABLE_NAME)
        .select("id")
        .eq("project_id", data.project_id)
        .eq("week_number", data.week_number)
        .is("deleted_at", null)
        .single();

      if (existingLog) {
        return {
          data: null,
          error: new Error(
            "Weekly log already exists for this project and week"
          ),
        };
      }

      // 3. Create the weekly log
      const { data: createdLog, error } = await dbUtils.create<WeeklyLog>(
        this.TABLE_NAME,
        {
          project_id: data.project_id,
          week_number: data.week_number,
          week_start_date: data.week_start_date,
          week_end_date: data.week_end_date,
        },
        userId
      );

      if (error) {
        return { data: null, error };
      }

      // 4. Return the created weekly log
      return { data: createdLog, error: null };
    } catch (error: any) {
      console.error("Error in WeeklyLogService.create:", error);
      return {
        data: null,
        error: new Error(`Failed to create weekly log: ${error.message}`),
      };
    }
  }

  /**
   * Get weekly log by ID with enhanced data
   * @param id Weekly log ID
   * @returns Enhanced weekly log with notes grouped by day and day-specific data
   */
  static async getById(id: string) {
    try {
      // 1. Validate input data
      if (!id) {
        return {
          data: null,
          error: new Error("Weekly log ID is required"),
        };
      }

      // 2. Get the weekly log
      const { data: weeklyLog, error: weeklyLogError } = await supabase
        .from(this.TABLE_NAME)
        .select("*")
        .eq("id", id)
        .is("deleted_at", null)
        .single();

      if (weeklyLogError) {
        return {
          data: null,
          error: new Error(
            `Failed to get weekly log: ${weeklyLogError.message}`
          ),
        };
      }

      if (!weeklyLog) {
        return {
          data: null,
          error: new Error("Weekly log not found"),
        };
      }

      // 3. Get the project name
      const { data: project, error: projectError } = await supabase
        .from(this.PROJECTS_TABLE)
        .select("project_name")
        .eq("id", weeklyLog.project_id)
        .is("deleted_at", null)
        .single();

      if (projectError) {
        console.error(
          `Error getting project name for weekly log ${id}:`,
          projectError
        );
      }

      const projectName = project?.project_name || "Unknown Project";

      // 4. Get notes for the weekly log
      const { data: notesData, error: notesError } = await supabase
        .from(this.NOTES_TABLE_NAME)
        .select("*")
        .eq("weekly_log_id", id)
        .is("deleted_at", null)
        .order("day_of_week", { ascending: true });

      if (notesError) {
        console.error(`Error getting notes for weekly log ${id}:`, notesError);
      }

      // 5. Group notes by day_of_week
      const notes_by_day: { [key: number]: WeeklyLogNote } = {};
      notesData?.forEach((note: WeeklyLogNote) => {
        notes_by_day[note.day_of_week] = note;
      });

      // 6. Get tasks associated with the weekly log
      const { data: tasksData, error: tasksError } = await supabase
        .from(this.PROJECT_TASKS_TABLE)
        .select("*")
        .eq("weekly_log_id", id)
        .is("deleted_at", null);

      if (tasksError) {
        console.error(`Error getting tasks for weekly log ${id}:`, tasksError);
      }

      // 7. Collect all user IDs and employee IDs for name resolution
      const userIds: string[] = [];
      if (weeklyLog.created_by) userIds.push(weeklyLog.created_by);
      if (weeklyLog.updated_by) userIds.push(weeklyLog.updated_by);
      if (weeklyLog.deleted_by) userIds.push(weeklyLog.deleted_by);

      const employeeIds: string[] = [];

      // Add user IDs from notes
      if (notesData) {
        notesData.forEach((note: WeeklyLogNote) => {
          if (note.created_by) userIds.push(note.created_by);
          if (note.updated_by) userIds.push(note.updated_by);
          if (note.deleted_by) userIds.push(note.deleted_by);
        });
      }

      // Add user IDs and employee IDs from tasks
      if (tasksData) {
        tasksData.forEach((task: ProjectTask) => {
          if (task.created_by) userIds.push(task.created_by);
          if (task.updated_by) userIds.push(task.updated_by);
          if (task.deleted_by) userIds.push(task.deleted_by);
          if (task.employee_id) employeeIds.push(task.employee_id);
          if (task.assigned_by) employeeIds.push(task.assigned_by);
        });
      }

      // 8. Get user names and employee names
      const userNameMap = await this.getUserNames(userIds);
      const employeeNameMap = await this.getEmployeeNames(employeeIds);

      // 9. Enhance tasks with names
      const enhancedTasks =
        tasksData?.map((task: ProjectTask) => {
          return {
            ...task,
            employee_name:
              (task.employee_id
                ? employeeNameMap.get(task.employee_id)
                : undefined) || "Unknown Employee",
            assigned_by_name:
              (task.assigned_by
                ? employeeNameMap.get(task.assigned_by)
                : undefined) || "Unknown User",
            created_by_name:
              (task.created_by
                ? userNameMap.get(task.created_by)
                : undefined) || "Unknown User",
            updated_by_name:
              (task.updated_by
                ? userNameMap.get(task.updated_by)
                : undefined) || "Unknown User",
          };
        }) || [];

      // 10. Enhance notes with names
      const enhancedNotesByDay: { [key: number]: any } = {};
      Object.entries(notes_by_day).forEach(([day, note]) => {
        enhancedNotesByDay[Number(day)] = {
          ...note,
          created_by_name:
            (note.created_by ? userNameMap.get(note.created_by) : undefined) ||
            "Unknown User",
          updated_by_name:
            (note.updated_by ? userNameMap.get(note.updated_by) : undefined) ||
            "Unknown User",
        };
      });

      // 11. Prepare day-specific data
      const days_data: { [key: number]: WeeklyLogDayData } = {};

      // Only process weekdays (1-5, Monday-Friday)
      for (let day = 1; day <= 5; day++) {
        // Calculate the date for this day
        const date = new Date(weeklyLog.week_start_date);

        // Check if week_start_date is a Monday
        if (date.getDay() !== 1) {
          // 0 is Sunday, 1 is Monday
          // Find the Monday of the week
          const dayOfWeek = date.getDay();
          const daysToMonday = dayOfWeek === 0 ? 1 : (8 - dayOfWeek) % 7;
          date.setDate(date.getDate() + daysToMonday);
          console.warn(
            `week_start_date ${
              weeklyLog.week_start_date
            } is not a Monday. Adjusted to ${date.toISOString().split("T")[0]}`
          );
        }

        // Now add days for the current day of week
        date.setDate(date.getDate() + day - 1); // day 1 is Monday
        const dateStr = date.toISOString().split("T")[0]; // YYYY-MM-DD format

        // Find the note for this day
        const note = enhancedNotesByDay[day];

        // Categorize tasks for this day
        const starting =
          enhancedTasks.filter(
            (task: ProjectTask) => task.initial_date === dateStr
          ) || [];
        const ending =
          enhancedTasks.filter(
            (task: ProjectTask) => task.due_date === dateStr
          ) || [];
        const ongoing =
          enhancedTasks.filter(
            (task: ProjectTask) =>
              task.initial_date <= dateStr && task.due_date >= dateStr
          ) || [];
        const not_completed =
          enhancedTasks.filter(
            (task: ProjectTask) =>
              task.completion_status === TaskStatus.NOT_COMPLETED
          ) || [];
        const on_progress =
          enhancedTasks.filter(
            (task: ProjectTask) =>
              task.completion_status === TaskStatus.ON_PROGRESS
          ) || [];
        const completed =
          enhancedTasks.filter(
            (task: ProjectTask) =>
              task.completion_status === TaskStatus.COMPLETED
          ) || [];

        // Add this day's data
        days_data[day] = {
          date: dateStr,
          day_of_week: day,
          note,
          activities: {
            starting,
            ending,
            ongoing,
            not_completed,
            on_progress,
            completed,
          },
        };
      }

      // 12. Return the enhanced weekly log
      const enhancedWeeklyLog: WeeklyLogWithNotes = {
        ...weeklyLog,
        project_name: projectName,
        created_by_name:
          (weeklyLog.created_by
            ? userNameMap.get(weeklyLog.created_by)
            : undefined) || "Unknown User",
        updated_by_name:
          (weeklyLog.updated_by
            ? userNameMap.get(weeklyLog.updated_by)
            : undefined) || "Unknown User",
        notes_by_day: enhancedNotesByDay,
        tasks: enhancedTasks || [],
        days_data,
      };

      return { data: enhancedWeeklyLog, error: null };
    } catch (error: any) {
      console.error("Error in WeeklyLogService.getById:", error);
      return {
        data: null,
        error: new Error(`Failed to get weekly log: ${error.message}`),
      };
    }
  }

  /**
   * Get weekly log by project ID and week number
   * @param projectId Project ID
   * @param weekNumber Week number
   * @returns Weekly log with notes grouped by day and day-specific data
   */
  static async getByProjectAndWeek(projectId: string, weekNumber?: number) {
    try {
      // 1. Validate input data
      if (!projectId || !looksLikeUuid(projectId)) {
        return {
          data: null,
          error: new Error("Invalid project ID"),
        };
      }

      // Only check lower bound - no upper bound on week numbers as per requirements
      if (weekNumber !== undefined && weekNumber < 1) {
        return {
          data: null,
          error: new Error("Week number must be a positive integer"),
        };
      }

      // 2. If weekNumber is not provided, get the latest week
      if (weekNumber === undefined) {
        const { data: availableWeeks, error: weeksError } =
          await this.getAvailableWeekNumbers(projectId);

        if (weeksError) {
          return {
            data: null,
            error: weeksError,
          };
        }

        if (!availableWeeks || availableWeeks.length === 0) {
          return {
            data: null,
            error: new Error("No weekly logs found for this project"),
          };
        }

        // Get the latest (highest) week number
        weekNumber = Math.max(...availableWeeks);
      }

      // 3. Get the weekly log
      const { data: weeklyLog, error: weeklyLogError } = await supabase
        .from(this.TABLE_NAME)
        .select("*")
        .eq("project_id", projectId)
        .eq("week_number", weekNumber)
        .is("deleted_at", null)
        .single();

      if (weeklyLogError) {
        return {
          data: null,
          error: new Error(
            `Failed to get weekly log: ${weeklyLogError.message}`
          ),
        };
      }

      if (!weeklyLog) {
        return {
          data: null,
          error: new Error("Weekly log not found"),
        };
      }

      // 4. Get the project name
      const { data: project, error: projectError } = await supabase
        .from(this.PROJECTS_TABLE)
        .select("project_name")
        .eq("id", projectId)
        .is("deleted_at", null)
        .single();

      if (projectError) {
        console.error(
          `Error getting project name for weekly log ${weeklyLog.id}:`,
          projectError
        );
      }

      const projectName = project?.project_name || "Unknown Project";

      // 5. Get notes for the weekly log
      const { data: notesData, error: notesError } = await supabase
        .from(this.NOTES_TABLE_NAME)
        .select("*")
        .eq("weekly_log_id", weeklyLog.id)
        .is("deleted_at", null)
        .order("day_of_week", { ascending: true });

      if (notesError) {
        console.error(
          `Error getting notes for weekly log ${weeklyLog.id}:`,
          notesError
        );
      }

      // 6. Group notes by day_of_week
      const notes_by_day: { [key: number]: WeeklyLogNote } = {};
      notesData?.forEach((note: WeeklyLogNote) => {
        notes_by_day[note.day_of_week] = note;
      });

      // 7. Get tasks associated with the weekly log
      const { data: tasksData, error: tasksError } = await supabase
        .from(this.PROJECT_TASKS_TABLE)
        .select("*")
        .eq("project_id", projectId)
        .is("deleted_at", null)
        .or(
          `weekly_log_id.eq.${weeklyLog.id},and(weekly_log_id.is.null,initial_date.lte.${weeklyLog.week_end_date},due_date.gte.${weeklyLog.week_start_date})`
        );

      if (tasksError) {
        console.error(
          `Error getting tasks for weekly log ${weeklyLog.id}:`,
          tasksError
        );
      }

      // 8. Collect all user IDs and employee IDs for name resolution
      const userIds: string[] = [];
      if (weeklyLog.created_by) userIds.push(weeklyLog.created_by);
      if (weeklyLog.updated_by) userIds.push(weeklyLog.updated_by);
      if (weeklyLog.deleted_by) userIds.push(weeklyLog.deleted_by);

      const employeeIds: string[] = [];

      // Add user IDs from notes
      if (notesData) {
        notesData.forEach((note: WeeklyLogNote) => {
          if (note.created_by) userIds.push(note.created_by);
          if (note.updated_by) userIds.push(note.updated_by);
          if (note.deleted_by) userIds.push(note.deleted_by);
        });
      }

      // Add user IDs and employee IDs from tasks
      if (tasksData) {
        tasksData.forEach((task: ProjectTask) => {
          if (task.created_by) userIds.push(task.created_by);
          if (task.updated_by) userIds.push(task.updated_by);
          if (task.deleted_by) userIds.push(task.deleted_by);
          if (task.employee_id) employeeIds.push(task.employee_id);
          if (task.assigned_by) employeeIds.push(task.assigned_by);
        });
      }

      // 9. Get user names and employee names
      const userNameMap = await this.getUserNames(userIds);
      const employeeNameMap = await this.getEmployeeNames(employeeIds);

      // 10. Enhance tasks with names
      const enhancedTasks =
        tasksData?.map((task: ProjectTask) => {
          return {
            ...task,
            employee_name:
              (task.employee_id
                ? employeeNameMap.get(task.employee_id)
                : undefined) || "Unknown Employee",
            assigned_by_name:
              (task.assigned_by
                ? employeeNameMap.get(task.assigned_by)
                : undefined) || "Unknown User",
            created_by_name:
              (task.created_by
                ? userNameMap.get(task.created_by)
                : undefined) || "Unknown User",
            updated_by_name:
              (task.updated_by
                ? userNameMap.get(task.updated_by)
                : undefined) || "Unknown User",
          };
        }) || [];

      // 11. Enhance notes with names
      const enhancedNotesByDay: { [key: number]: any } = {};
      Object.entries(notes_by_day).forEach(([day, note]) => {
        enhancedNotesByDay[Number(day)] = {
          ...note,
          created_by_name:
            (note.created_by ? userNameMap.get(note.created_by) : undefined) ||
            "Unknown User",
          updated_by_name:
            (note.updated_by ? userNameMap.get(note.updated_by) : undefined) ||
            "Unknown User",
        };
      });

      // 12. Prepare day-specific data
      const days_data: { [key: number]: WeeklyLogDayData } = {};

      // Only process weekdays (1-5, Monday-Friday)
      for (let day = 1; day <= 5; day++) {
        // Calculate the date for this day
        const date = new Date(weeklyLog.week_start_date);

        // Check if week_start_date is a Monday
        if (date.getDay() !== 1) {
          // 0 is Sunday, 1 is Monday
          // Find the Monday of the week
          const dayOfWeek = date.getDay();
          const daysToMonday = dayOfWeek === 0 ? 1 : (8 - dayOfWeek) % 7;
          date.setDate(date.getDate() + daysToMonday);
          console.warn(
            `week_start_date ${
              weeklyLog.week_start_date
            } is not a Monday. Adjusted to ${date.toISOString().split("T")[0]}`
          );
        }

        // Now add days for the current day of week
        date.setDate(date.getDate() + day - 1); // day 1 is Monday
        const dateStr = date.toISOString().split("T")[0]; // YYYY-MM-DD format

        // Find the note for this day
        const note = enhancedNotesByDay[day];

        // Categorize tasks for this day
        const starting =
          enhancedTasks.filter(
            (task: ProjectTask) => task.initial_date === dateStr
          ) || [];
        const ending =
          enhancedTasks.filter(
            (task: ProjectTask) => task.due_date === dateStr
          ) || [];
        const ongoing =
          enhancedTasks.filter(
            (task: ProjectTask) =>
              task.initial_date <= dateStr && task.due_date >= dateStr
          ) || [];
        const not_completed =
          enhancedTasks.filter(
            (task: ProjectTask) =>
              task.completion_status === TaskStatus.NOT_COMPLETED &&
              task.initial_date <= dateStr &&
              task.due_date >= dateStr
          ) || [];
        const on_progress =
          enhancedTasks.filter(
            (task: ProjectTask) =>
              task.completion_status === TaskStatus.ON_PROGRESS &&
              task.initial_date <= dateStr &&
              task.due_date >= dateStr
          ) || [];
        const completed =
          enhancedTasks.filter(
            (task: ProjectTask) =>
              task.completion_status === TaskStatus.COMPLETED &&
              task.initial_date <= dateStr &&
              task.due_date >= dateStr
          ) || [];

        // Add this day's data
        days_data[day] = {
          date: dateStr,
          day_of_week: day,
          note,
          activities: {
            starting,
            ending,
            ongoing,
            not_completed,
            on_progress,
            completed,
          },
        };
      }

      // 13. Return the enhanced weekly log
      const enhancedWeeklyLog: WeeklyLogWithNotes = {
        ...weeklyLog,
        project_name: projectName,
        created_by_name:
          (weeklyLog.created_by
            ? userNameMap.get(weeklyLog.created_by)
            : undefined) || "Unknown User",
        updated_by_name:
          (weeklyLog.updated_by
            ? userNameMap.get(weeklyLog.updated_by)
            : undefined) || "Unknown User",
        notes_by_day: enhancedNotesByDay,
        tasks: enhancedTasks || [],
        days_data,
      };

      return { data: enhancedWeeklyLog, error: null };
    } catch (error: any) {
      console.error("Error in WeeklyLogService.getByProjectAndWeek:", error);
      return {
        data: null,
        error: new Error(`Failed to get weekly log: ${error.message}`),
      };
    }
  }

  /**
   * Get all weekly logs with optional filtering and pagination
   * @param options Query options
   * @returns Paginated weekly logs with enhanced data
   */
  static async getAll(options: QueryOptions = {}) {
    try {
      // Add default sorting by week_number if not specified
      if (!options.sort) {
        options.sort = { field: "week_number", direction: "desc" };
      }

      // Use dbUtils.getAll to retrieve weekly logs with pagination and filtering
      const {
        data: weeklyLogs,
        error,
        result,
      } = await dbUtils.getAll<WeeklyLog>(this.TABLE_NAME, options);

      if (error) {
        return { data: null, error, result: null };
      }

      if (!weeklyLogs || weeklyLogs.length === 0) {
        return { data: [], error: null, result };
      }

      // Extract all weekly log IDs and project IDs
      const weeklyLogIds = weeklyLogs.map((log) => log.id);
      const projectIds = [...new Set(weeklyLogs.map((log) => log.project_id))];

      // Fetch all project names in a single query
      const { data: projectsData } = await supabase
        .from(this.PROJECTS_TABLE)
        .select("id, project_name")
        .in("id", projectIds)
        .is("deleted_at", null);

      // Create a map of project ID to project name
      const projectNameMap = new Map();
      projectsData?.forEach((project) => {
        projectNameMap.set(project.id, project.project_name);
      });

      // Fetch all notes for all weekly logs in a single query
      const { data: allNotesData } = await supabase
        .from(this.NOTES_TABLE_NAME)
        .select("*")
        .in("weekly_log_id", weeklyLogIds)
        .is("deleted_at", null);

      // Create a map of weekly log ID to notes
      const notesMap = new Map();
      allNotesData?.forEach((note) => {
        if (!notesMap.has(note.weekly_log_id)) {
          notesMap.set(note.weekly_log_id, []);
        }
        notesMap.get(note.weekly_log_id).push(note);
      });

      // Fetch all tasks for all weekly logs in a single query
      const { data: allTasksData } = await supabase
        .from(this.PROJECT_TASKS_TABLE)
        .select("*")
        .in("weekly_log_id", weeklyLogIds)
        .is("deleted_at", null);

      // Create a map of weekly log ID to tasks
      const tasksMap = new Map();
      allTasksData?.forEach((task) => {
        if (!tasksMap.has(task.weekly_log_id)) {
          tasksMap.set(task.weekly_log_id, []);
        }
        tasksMap.get(task.weekly_log_id).push(task);
      });

      // Enhance weekly logs with additional data
      const enhancedWeeklyLogs: WeeklyLogWithNotes[] = weeklyLogs.map(
        (weeklyLog) => {
          try {
            // Get project name from map
            const projectName = projectNameMap.get(weeklyLog.project_id);

            // Get notes for this weekly log from map
            const notesData = notesMap.get(weeklyLog.id) || [];

            // Get tasks for this weekly log from map
            const tasksData = tasksMap.get(weeklyLog.id) || [];

            // Group notes by day_of_week
            const notes_by_day: { [key: number]: WeeklyLogNote } = {};
            notesData.forEach((note: WeeklyLogNote) => {
              notes_by_day[note.day_of_week] = note;
            });

            // Prepare day-specific data
            const days_data: { [key: number]: WeeklyLogDayData } = {};

            // Only process weekdays (1-5, Monday-Friday)
            for (let day = 1; day <= 5; day++) {
              // Calculate the date for this day
              const date = new Date(weeklyLog.week_start_date);

              // Check if week_start_date is a Monday
              if (date.getDay() !== 1) {
                // 0 is Sunday, 1 is Monday
                // Find the Monday of the week
                const dayOfWeek = date.getDay();
                const daysToMonday = dayOfWeek === 0 ? 1 : (8 - dayOfWeek) % 7;
                date.setDate(date.getDate() + daysToMonday);
                console.warn(
                  `week_start_date ${
                    weeklyLog.week_start_date
                  } is not a Monday. Adjusted to ${
                    date.toISOString().split("T")[0]
                  }`
                );
              }

              // Now add days for the current day of week
              date.setDate(date.getDate() + day - 1); // day 1 is Monday
              const dateStr = date.toISOString().split("T")[0]; // YYYY-MM-DD format

              // Find the note for this day
              const note = notes_by_day[day];

              // Categorize tasks for this day
              const starting =
                tasksData.filter(
                  (task: ProjectTask) => task.initial_date === dateStr
                ) || [];
              const ending =
                tasksData.filter(
                  (task: ProjectTask) => task.due_date === dateStr
                ) || [];
              const ongoing =
                tasksData.filter(
                  (task: ProjectTask) =>
                    task.initial_date <= dateStr && task.due_date >= dateStr
                ) || [];
              const not_completed =
                tasksData.filter(
                  (task: ProjectTask) =>
                    task.completion_status === TaskStatus.NOT_COMPLETED
                ) || [];
              const on_progress =
                tasksData.filter(
                  (task: ProjectTask) =>
                    task.completion_status === TaskStatus.ON_PROGRESS
                ) || [];
              const completed =
                tasksData.filter(
                  (task: ProjectTask) =>
                    task.completion_status === TaskStatus.COMPLETED
                ) || [];

              // Add this day's data
              days_data[day] = {
                date: dateStr,
                day_of_week: day,
                note,
                activities: {
                  starting,
                  ending,
                  ongoing,
                  not_completed,
                  on_progress,
                  completed,
                },
              };
            }

            // Return enhanced weekly log
            return {
              ...weeklyLog,
              project_name: projectName,
              notes_by_day,
              tasks: tasksData || [],
              days_data,
            } as WeeklyLogWithNotes;
          } catch (err) {
            console.error(`Error enhancing weekly log ${weeklyLog.id}:`, err);
            // Return basic weekly log if enhancement fails
            return {
              ...weeklyLog,
              notes_by_day: {},
              tasks: [],
              days_data: {},
            } as WeeklyLogWithNotes;
          }
        }
      );

      // Return enhanced data with pagination result
      return { data: enhancedWeeklyLogs, error: null, result };
    } catch (error: any) {
      console.error("Error in WeeklyLogService.getAll:", error);
      return {
        data: null,
        error: new Error(`Failed to get weekly logs: ${error.message}`),
        result: null,
      };
    }
  }

  /**
   * Batch update notes for a weekly log
   * @param weeklyLogId Weekly log ID
   * @param data Notes data for each day
   * @param userId User ID for audit
   * @returns Summary of update operations
   */
  static async updateNotes(
    weeklyLogId: string,
    data: UpdateWeeklyLogNotesDto,
    userId: string
  ): Promise<{ data: BatchUpdateResult | null; error: Error | null }> {
    try {
      // 1. Validate input data
      if (!weeklyLogId || !looksLikeUuid(weeklyLogId)) {
        return {
          data: null,
          error: new Error("Invalid weekly log ID"),
        };
      }

      if (!data || !data.notes || typeof data.notes !== "object") {
        return {
          data: null,
          error: new Error("Notes data is required and must be an object"),
        };
      }

      // Validate each key and value in data.notes
      for (const dayStr in data.notes) {
        const day = Number(dayStr);
        if (isNaN(day) || day < 1 || day > 5) {
          return {
            data: null,
            error: new Error("Day of week must be between 1 and 5"),
          };
        }

        if (typeof data.notes[day] !== "string") {
          return {
            data: null,
            error: new Error("Note text must be a string"),
          };
        }
      }

      // 2. Validate weekly log exists
      const { data: weeklyLog, error: weeklyLogError } = await supabase
        .from(this.TABLE_NAME)
        .select("id")
        .eq("id", weeklyLogId)
        .is("deleted_at", null)
        .single();

      if (weeklyLogError) {
        return {
          data: null,
          error: new Error(
            `Failed to get weekly log: ${weeklyLogError.message}`
          ),
        };
      }

      if (!weeklyLog) {
        return {
          data: null,
          error: new Error("Weekly log not found"),
        };
      }

      // 3. Get existing notes for this weekly log
      const { data: existingNotesData, error: notesError } = await supabase
        .from(this.NOTES_TABLE_NAME)
        .select("*")
        .eq("weekly_log_id", weeklyLogId)
        .is("deleted_at", null);

      if (notesError) {
        return {
          data: null,
          error: new Error(
            `Failed to get existing notes: ${notesError.message}`
          ),
        };
      }

      // 4. Initialize counters for the result
      let created = 0;
      let updated = 0;
      let deleted = 0;
      let unchanged = 0;

      // 5. Process each day's note
      for (let day = 1; day <= 5; day++) {
        // a. Check if the day is included in data.notes
        if (!(day in data.notes)) {
          unchanged++;
          continue;
        }

        // b. Get the note text for the day
        const noteText = data.notes[day];

        // c. Find existing note for this day
        const existingNote = existingNotesData?.find(
          (note: WeeklyLogNote) => note.day_of_week === day
        );

        // d. If note text is empty string (""), delete any existing note
        if (noteText === "") {
          if (existingNote) {
            // Soft delete the note
            const { error: deleteError } = await supabase
              .from(this.NOTES_TABLE_NAME)
              .update({
                deleted_at: new Date().toISOString(),
                deleted_by: userId,
              })
              .eq("id", existingNote.id);

            if (deleteError) {
              console.error(`Error deleting note for day ${day}:`, deleteError);
              return {
                data: null,
                error: new Error(
                  `Failed to delete note: ${deleteError.message}`
                ),
              };
            }

            deleted++;
          } else {
            // No existing note to delete
            unchanged++;
          }
        }
        // e. If note text is non-empty, create or update the note
        else {
          if (existingNote) {
            // Update the existing note
            const { error: updateError } = await supabase
              .from(this.NOTES_TABLE_NAME)
              .update({
                note: noteText,
                updated_at: new Date().toISOString(),
                updated_by: userId,
              })
              .eq("id", existingNote.id);

            if (updateError) {
              console.error(`Error updating note for day ${day}:`, updateError);
              return {
                data: null,
                error: new Error(
                  `Failed to update note: ${updateError.message}`
                ),
              };
            }

            updated++;
          } else {
            // Create a new note
            const { error: createError } = await supabase
              .from(this.NOTES_TABLE_NAME)
              .insert({
                note: noteText,
                weekly_log_id: weeklyLogId,
                day_of_week: day,
                created_by: userId,
              });

            if (createError) {
              console.error(`Error creating note for day ${day}:`, createError);
              return {
                data: null,
                error: new Error(
                  `Failed to create note: ${createError.message}`
                ),
              };
            }

            created++;
          }
        }
      }

      // 6. Return a summary of operations
      return {
        data: { created, updated, deleted, unchanged },
        error: null,
      };
    } catch (error: any) {
      console.error("Error in WeeklyLogService.updateNotes:", error);
      return {
        data: null,
        error: new Error(`Failed to update notes: ${error.message}`),
      };
    }
  }

  /**
   * Automatically create weekly logs for all active projects
   * This will run every Saturday at 00:00
   */
  static async createWeeklyLogs(userId: string) {
    try {
      // Get current date in Jakarta timezone
      const currentDate = getCurrentJakartaDate();

      // 1. Get all active projects with their start dates and end dates
      const { data: projects, error: projectsError } = await supabase
        .from(this.PROJECTS_TABLE)
        .select("id, start_project, end_project, project_name, status_project")
        .is("deleted_at", null)
        .eq("status_project", "In Progress");

      if (projectsError) {
        return {
          data: null,
          error: new Error(
            `Failed to get active projects: ${projectsError.message}`
          ),
        };
      }

      if (!projects || projects.length === 0) {
        return {
          data: {
            count: 0,
            tasks_updated: 0,
          },
          error: null,
        };
      }

      // 2. For each project, check if it needs a weekly log
      let createdCount = 0;
      const weeklyLogIds = new Map<string, string>();

      for (const project of projects) {
        // Skip projects that haven't started yet or have ended
        const projectStartDate = new Date(project.start_project);
        const projectEndDate = new Date(project.end_project);
        const currentDateObj = new Date(currentDate);

        if (
          projectStartDate > currentDateObj ||
          projectEndDate < currentDateObj
        ) {
          console.log(
            `Project ${project.id} (${project.project_name}) is not active in current period, skipping`
          );
          continue;
        }

        // Calculate project-specific week number using the current date
        const weekNumber = getProjectWeekNumber(
          project.start_project,
          currentDate
        );

        // Calculate week start and end dates
        const weekStartDate = getProjectWeekStartDate(
          project.start_project,
          weekNumber
        );
        const weekEndDate = getProjectWeekEndDate(
          project.start_project,
          weekNumber
        );

        // Check if weekly log exists for this project and week
        const { data: existingLog } = await supabase
          .from(this.TABLE_NAME)
          .select("id")
          .eq("project_id", project.id)
          .eq("week_number", weekNumber)
          .is("deleted_at", null)
          .single();

        if (existingLog) {
          // Weekly log already exists, store its ID
          weeklyLogIds.set(project.id, existingLog.id);
          continue;
        }

        // Create new weekly log
        const { data: createdLog, error: createError } =
          await dbUtils.create<WeeklyLog>(
            this.TABLE_NAME,
            {
              project_id: project.id,
              week_number: weekNumber,
              week_start_date: weekStartDate,
              week_end_date: weekEndDate,
            },
            userId
          );

        if (createError) {
          console.error(
            `Failed to create weekly log for project ${project.id}:`,
            createError
          );
          continue;
        }

        if (createdLog && createdLog.id) {
          createdCount++;
          weeklyLogIds.set(project.id, createdLog.id);
        }
      }

      // 3. Update project tasks' weekly_log_id for tasks in the current week
      let tasksUpdatedCount = 0;

      // For each project, get tasks that fall within its current week and don't have a weekly_log_id
      for (const project of projects) {
        // Skip projects that haven't started yet or don't have a weekly log ID
        if (
          new Date(project.start_project) > new Date(currentDate) ||
          !weeklyLogIds.has(project.id)
        ) {
          continue;
        }

        // Get the weekly log ID for this project
        const weeklyLogId = weeklyLogIds.get(project.id);

        // Calculate project-specific week number and dates
        const weekNumber = getProjectWeekNumber(
          project.start_project,
          currentDate
        );
        const weekStartDate = getProjectWeekStartDate(
          project.start_project,
          weekNumber
        );
        const weekEndDate = getProjectWeekEndDate(
          project.start_project,
          weekNumber
        );

        // Get tasks for this project that fall within the current week
        const { data: tasks, error: tasksError } = await supabase
          .from(this.PROJECT_TASKS_TABLE)
          .select("id")
          .eq("project_id", project.id)
          .is("weekly_log_id", null)
          .is("deleted_at", null)
          .gte("due_date", weekStartDate)
          .lte("due_date", weekEndDate);

        if (tasksError) {
          console.error(
            `Failed to get tasks for project ${project.id}:`,
            tasksError
          );
          continue;
        }

        if (!tasks || tasks.length === 0) {
          continue;
        }

        // Get task IDs
        const taskIds = tasks.map((task) => task.id);

        // Update tasks with the weekly log ID
        const { error: updateError } = await supabase
          .from(this.PROJECT_TASKS_TABLE)
          .update({ weekly_log_id: weeklyLogId })
          .in("id", taskIds);

        if (updateError) {
          console.error(
            `Failed to update tasks for project ${project.id}:`,
            updateError
          );
          continue;
        }

        tasksUpdatedCount += taskIds.length;
      }

      return {
        data: {
          count: createdCount,
          tasks_updated: tasksUpdatedCount,
        },
        error: null,
      };
    } catch (error: any) {
      console.error("Error in WeeklyLogService.createWeeklyLogs:", error);
      return {
        data: null,
        error: new Error(`Failed to create weekly logs: ${error.message}`),
      };
    }
  }

  /**
   * Get all available week numbers for a specific project
   * @param projectId Project ID to filter by
   * @returns List of available week numbers in descending order
   */
  static async getAvailableWeekNumbers(projectId: string) {
    try {
      // Validate input
      if (!projectId) {
        return {
          data: null,
          error: new Error("Project ID is required"),
        };
      }

      // Query to get distinct week numbers with date ranges for the specified project
      const { data, error } = await supabase
        .from(this.TABLE_NAME)
        .select("week_number, week_start_date, week_end_date")
        .eq("project_id", projectId)
        .is("deleted_at", null)
        .order("week_number", { ascending: false });

      if (error) {
        return {
          data: null,
          error: new Error(
            `Failed to get available week numbers: ${error.message}`
          ),
        };
      }

      // Extract week numbers and add date ranges for UI clarity
      const weekNumbersWithRanges = data.map((item) => ({
        week_number: item.week_number,
        date_range: formatWeekDateRange(
          item.week_start_date,
          item.week_end_date
        ),
      }));

      // Extract just the week numbers for backward compatibility
      const weekNumbers = data.map((item) => item.week_number);

      // Remove duplicates (just in case)
      const uniqueWeekNumbers = [...new Set(weekNumbers)];

      return {
        data: uniqueWeekNumbers,
        data_with_ranges: weekNumbersWithRanges,
        error: null,
      };
    } catch (error: any) {
      console.error(
        "Error in WeeklyLogService.getAvailableWeekNumbers:",
        error
      );
      return {
        data: null,
        error: new Error(
          `Failed to get available week numbers: ${error.message}`
        ),
      };
    }
  }

  /**
   * Set up scheduler to automatically create weekly logs
   */
  static setupWeeklyLogScheduler() {
    console.log("Setting up weekly log scheduler");

    // Generate a unique ID for this scheduler instance
    const schedulerId = `scheduler-${Date.now()}-${Math.random()
      .toString(36)
      .substring(2, 15)}`;

    // Function to check if weekly logs need to be created
    const checkAndCreateWeeklyLogs = async () => {
      const lockName = "weekly_log_creation";
      let lockAcquired = false;

      try {
        // Try to acquire a lock
        lockAcquired = await acquireSchedulerLock(lockName, schedulerId);

        if (!lockAcquired) {
          console.log(
            "Another scheduler instance is already running, skipping this run"
          );
          return;
        }

        console.log(
          "Weekly log scheduler running at",
          new Date().toISOString()
        );

        // Check if today is Saturday using our timezone-aware function
        if (isSaturday()) {
          console.log("Creating weekly logs for the current week");

          // Use a system user ID for automatic creation
          const systemUserId = process.env.SYSTEM_USER_ID || "system";

          const { data, error } = await WeeklyLogService.createWeeklyLogs(
            systemUserId
          );

          if (error) {
            console.error("Failed to create weekly logs:", error);
            await recordSchedulerRun("weekly_log_creation", "failure", {
              error: error.message,
            });
          } else {
            console.log(`Created ${data?.count} weekly logs`);
            await recordSchedulerRun("weekly_log_creation", "success", data);
          }
        } else {
          console.log(
            `Skipping weekly log creation on day ${getCurrentDayOfWeek()} (not Saturday)`
          );
        }
      } catch (error) {
        console.error("Error in weekly log scheduler:", error);
        await recordSchedulerRun("weekly_log_creation", "failure", {
          error: String(error),
        });
      } finally {
        // Release the lock if it was acquired
        if (lockAcquired) {
          await releaseSchedulerLock(lockName, schedulerId);
        }
      }
    };

    // Run the check immediately on startup (optional, can be disabled)
    if (process.env.RUN_SCHEDULER_ON_STARTUP === "true") {
      console.log("Running scheduler immediately on startup");
      checkAndCreateWeeklyLogs();
    }

    // Schedule the job to run at midnight every day
    const checkInterval = 24 * 60 * 60 * 1000; // 24 hours
    setInterval(checkAndCreateWeeklyLogs, checkInterval);

    console.log("Weekly log scheduler set up successfully");
  }
}
