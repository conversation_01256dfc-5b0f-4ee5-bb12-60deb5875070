import { describe, expect, it, mock, beforeEach, afterEach } from "bun:test";
import { AllowanceSalaryController } from "../../modules/allowance-salary/controller";
import { AllowanceSalaryService } from "../../modules/allowance-salary/service";
import { AllowanceSalaryType } from "../../database/models/allowance-salary.model";
import { createMockAllowance, createMockContext, createMockFn } from "./test-utils";

// Mock the Supabase client first to prevent real initialization
mock.module("../../libs/supabase", () => {
  return {
    supabase: {
      from: () => ({}),
    },
  };
});

describe("AllowanceSalaryController", () => {
  // Store original methods to restore after tests
  let originalCreate: typeof AllowanceSalaryService.create;
  let originalGetById: typeof AllowanceSalaryService.getById;
  let originalGetBySalaryId: typeof AllowanceSalaryService.getBySalaryId;
  let originalUpdate: typeof AllowanceSalaryService.update;
  let originalDelete: typeof AllowanceSalaryService.delete;

  beforeEach(() => {
    // Store original methods
    originalCreate = AllowanceSalaryService.create;
    originalGetById = AllowanceSalaryService.getById;
    originalGetBySalaryId = AllowanceSalaryService.getBySalaryId;
    originalUpdate = AllowanceSalaryService.update;
    originalDelete = AllowanceSalaryService.delete;
  });

  afterEach(() => {
    // Restore original methods
    AllowanceSalaryService.create = originalCreate;
    AllowanceSalaryService.getById = originalGetById;
    AllowanceSalaryService.getBySalaryId = originalGetBySalaryId;
    AllowanceSalaryService.update = originalUpdate;
    AllowanceSalaryService.delete = originalDelete;
  });

  describe("create", () => {
    it("should create an allowance successfully", async () => {
      // ARRANGE
      const mockContext = createMockContext({
        body: {
          salary_id: "test-salary-id",
          amount: 200000,
          allowance_type: AllowanceSalaryType.TRANSPORT,
          notes: "Test allowance",
        },
      });

      // Mock the service method
      AllowanceSalaryService.create = createMockFn(() => 
        Promise.resolve({
          data: createMockAllowance(),
          error: null,
        })
      );

      // ACT
      const result = await AllowanceSalaryController.create(mockContext);

      // ASSERT
      expect(result.success).toBe(true);
      expect(result.data).toBeDefined();
      expect(result.message).toContain("created successfully");
    });

    it("should handle errors when creating an allowance", async () => {
      // ARRANGE
      const mockContext = createMockContext({
        body: {
          salary_id: "test-salary-id",
          amount: 200000,
          allowance_type: AllowanceSalaryType.TRANSPORT,
          notes: "Test allowance",
        },
      });

      // Mock the service method to return an error
      AllowanceSalaryService.create = createMockFn(() => 
        Promise.resolve({
          data: null,
          error: new Error("Failed to create allowance"),
        })
      );

      // ACT
      const result = await AllowanceSalaryController.create(mockContext);

      // ASSERT
      expect(result.success).toBe(false);
      expect(result.message).toContain("Failed to create allowance");
    });
  });

  describe("getBySalaryId", () => {
    it("should get allowances by salary ID successfully", async () => {
      // ARRANGE
      const mockContext = createMockContext({
        params: { salaryId: "test-salary-id" },
      });

      // Mock the service method
      AllowanceSalaryService.getBySalaryId = createMockFn(() => 
        Promise.resolve({
          data: [createMockAllowance()],
          error: null,
        })
      );

      // ACT
      const result = await AllowanceSalaryController.getBySalaryId(mockContext);

      // ASSERT
      expect(result.success).toBe(true);
      expect(result.data).toBeDefined();
      expect(result.data.length).toBe(1);
      expect(result.data[0].salary_id).toBe("test-salary-id");
    });

    it("should return empty array when no allowances found", async () => {
      // ARRANGE
      const mockContext = createMockContext({
        params: { salaryId: "test-salary-id" },
      });

      // Mock the service method to return empty array
      AllowanceSalaryService.getBySalaryId = createMockFn(() => 
        Promise.resolve({
          data: [],
          error: null,
        })
      );

      // ACT
      const result = await AllowanceSalaryController.getBySalaryId(mockContext);

      // ASSERT
      expect(result.success).toBe(true);
      expect(result.data).toEqual([]);
      expect(result.message).toContain("No allowances found");
    });

    it("should handle errors when getting allowances", async () => {
      // ARRANGE
      const mockContext = createMockContext({
        params: { salaryId: "test-salary-id" },
      });

      // Mock the service method to return an error
      AllowanceSalaryService.getBySalaryId = createMockFn(() => 
        Promise.resolve({
          data: null,
          error: new Error("Failed to get allowances"),
        })
      );

      // ACT
      const result = await AllowanceSalaryController.getBySalaryId(mockContext);

      // ASSERT
      expect(result.success).toBe(false);
      expect(result.message).toContain("Failed to get allowances");
    });
  });

  describe("getById", () => {
    it("should get an allowance by ID successfully", async () => {
      // ARRANGE
      const mockContext = createMockContext({
        params: { id: "test-allowance-id" },
      });

      // Mock the service method
      AllowanceSalaryService.getById = createMockFn(() => 
        Promise.resolve({
          data: createMockAllowance(),
          error: null,
        })
      );

      // ACT
      const result = await AllowanceSalaryController.getById(mockContext);

      // ASSERT
      expect(result.success).toBe(true);
      expect(result.data).toBeDefined();
      expect(result.data.id).toBe("test-allowance-id");
    });

    it("should return not found when allowance doesn't exist", async () => {
      // ARRANGE
      const mockContext = createMockContext({
        params: { id: "test-allowance-id" },
      });

      // Mock the service method to return null data
      AllowanceSalaryService.getById = createMockFn(() => 
        Promise.resolve({
          data: null,
          error: null,
        })
      );

      // ACT
      const result = await AllowanceSalaryController.getById(mockContext);

      // ASSERT
      expect(result.success).toBe(false);
      expect(result.status).toBe(404);
      expect(result.message).toContain("not found");
    });
  });

  describe("update", () => {
    it("should update an allowance successfully", async () => {
      // ARRANGE
      const mockContext = createMockContext({
        params: { id: "test-allowance-id" },
        body: {
          amount: 250000,
          allowance_type: AllowanceSalaryType.MEAL,
        },
      });

      // Mock the service method
      AllowanceSalaryService.update = createMockFn(() => 
        Promise.resolve({
          data: createMockAllowance({
            amount: 250000,
            allowance_type: AllowanceSalaryType.MEAL,
          }),
          error: null,
        })
      );

      // ACT
      const result = await AllowanceSalaryController.update(mockContext);

      // ASSERT
      expect(result.success).toBe(true);
      expect(result.data).toBeDefined();
      expect(result.data.amount).toBe(250000);
      expect(result.data.allowance_type).toBe(AllowanceSalaryType.MEAL);
    });

    it("should return bad request when no valid fields to update", async () => {
      // ARRANGE
      const mockContext = createMockContext({
        params: { id: "test-allowance-id" },
        body: {},
      });

      // ACT
      const result = await AllowanceSalaryController.update(mockContext);

      // ASSERT
      expect(result.success).toBe(false);
      expect(result.status).toBe(400);
      expect(result.message).toContain("No valid fields to update");
    });

    it("should return not found when allowance doesn't exist", async () => {
      // ARRANGE
      const mockContext = createMockContext({
        params: { id: "test-allowance-id" },
        body: { amount: 250000 },
      });

      // Mock the service method to return not found error
      AllowanceSalaryService.update = createMockFn(() => 
        Promise.resolve({
          data: null,
          error: new Error("Allowance not found"),
        })
      );

      // ACT
      const result = await AllowanceSalaryController.update(mockContext);

      // ASSERT
      expect(result.success).toBe(false);
      expect(result.status).toBe(404);
      expect(result.message).toContain("not found");
    });
  });

  describe("delete", () => {
    it("should delete an allowance successfully", async () => {
      // ARRANGE
      const mockContext = createMockContext({
        params: { id: "test-allowance-id" },
      });

      // Mock the service method
      AllowanceSalaryService.delete = createMockFn(() => 
        Promise.resolve({
          data: { id: "test-allowance-id" },
          error: null,
        })
      );

      // ACT
      const result = await AllowanceSalaryController.delete(mockContext);

      // ASSERT
      expect(result.success).toBe(true);
      expect(result.data).toBeDefined();
      expect(result.data.id).toBe("test-allowance-id");
      expect(result.message).toContain("deleted successfully");
    });

    it("should return not found when allowance doesn't exist", async () => {
      // ARRANGE
      const mockContext = createMockContext({
        params: { id: "test-allowance-id" },
      });

      // Mock the service method to return not found error
      AllowanceSalaryService.delete = createMockFn(() => 
        Promise.resolve({
          data: null,
          error: new Error("Allowance not found"),
        })
      );

      // ACT
      const result = await AllowanceSalaryController.delete(mockContext);

      // ASSERT
      expect(result.success).toBe(false);
      expect(result.status).toBe(404);
      expect(result.message).toContain("not found");
    });
  });
});
