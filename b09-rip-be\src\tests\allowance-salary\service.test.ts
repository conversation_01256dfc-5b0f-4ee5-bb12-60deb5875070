import { describe, expect, it, afterEach } from "bun:test";
import { AllowanceSalaryService } from "../../modules/allowance-salary/service";
import { AllowanceSalaryType } from "../../database/models/allowance-salary.model";
import { createMockAllowance, setupMocks } from "./test-utils";

describe("AllowanceSalaryService", () => {
  // Reset mocks after each test
  let resetMocks: () => void;

  afterEach(() => {
    // Reset mocks to prevent test interference
    if (resetMocks) {
      resetMocks();
    }
  });

  // Test create method
  describe("create", () => {
    it("should create an allowance successfully", async () => {
      // ARRANGE
      const mockAllowance = createMockAllowance();
      // Mock salary object with necessary fields for total salary calculation
      const mockSalary = {
        id: "test-salary-id",
        base_salary: 5000000,
        total_bonus: 500000,
        total_allowance: 0, // Will be updated by the service
        total_deduction: 100000,
        total_salary: 5400000, // Will be updated by the service
      };

      resetMocks = setupMocks({
        create: () => Promise.resolve({ data: mockAllowance, error: null }),
        getByField: () =>
          Promise.resolve({ data: [mockAllowance], error: null }),
        update: () => Promise.resolve({ data: null, error: null }),
        getById: () => Promise.resolve({ data: mockSalary, error: null }),
      });

      const allowanceData = {
        salary_id: "test-salary-id",
        amount: 200000,
        allowance_type: AllowanceSalaryType.TRANSPORT,
        notes: "Test allowance",
      };

      // ACT
      const result = await AllowanceSalaryService.create(
        allowanceData,
        "test-user-id"
      );

      // ASSERT
      expect(result.data).toBeDefined();
      expect(result.error).toBeNull();
      expect(result.data?.amount).toBe(200000);
      expect(result.data?.allowance_type).toBe(AllowanceSalaryType.TRANSPORT);
    });

    it("should handle database errors when creating", async () => {
      // ARRANGE
      resetMocks = setupMocks({
        create: () =>
          Promise.resolve({
            data: null,
            error: new Error("Database error"),
          }),
      });

      const allowanceData = {
        salary_id: "test-salary-id",
        amount: 200000,
        allowance_type: AllowanceSalaryType.TRANSPORT,
        notes: "Test allowance",
      };

      // ACT
      const result = await AllowanceSalaryService.create(
        allowanceData,
        "test-user-id"
      );

      // ASSERT
      expect(result.data).toBeNull();
      expect(result.error).toBeDefined();
      expect(result.error?.message).toContain("Database error");
    });
  });

  // Test getById method
  describe("getById", () => {
    it("should get an allowance by ID successfully", async () => {
      // ARRANGE
      const mockAllowance = createMockAllowance();
      resetMocks = setupMocks({
        getById: () => Promise.resolve({ data: mockAllowance, error: null }),
      });

      // ACT
      const result = await AllowanceSalaryService.getById("test-allowance-id");

      // ASSERT
      expect(result.data).toBeDefined();
      expect(result.error).toBeNull();
      expect(result.data?.id).toBe("test-allowance-id");
    });

    it("should handle database errors when getting by ID", async () => {
      // ARRANGE
      resetMocks = setupMocks({
        getById: () =>
          Promise.resolve({
            data: null,
            error: new Error("Database error"),
          }),
      });

      // ACT
      const result = await AllowanceSalaryService.getById("test-allowance-id");

      // ASSERT
      expect(result.data).toBeNull();
      expect(result.error).toBeDefined();
      expect(result.error?.message).toContain("Database error");
    });
  });

  // Test getBySalaryId method
  describe("getBySalaryId", () => {
    it("should get allowances by salary ID successfully", async () => {
      // ARRANGE
      const mockAllowance = createMockAllowance();
      resetMocks = setupMocks({
        getByField: () =>
          Promise.resolve({ data: [mockAllowance], error: null }),
      });

      // ACT
      const result = await AllowanceSalaryService.getBySalaryId(
        "test-salary-id"
      );

      // ASSERT
      expect(result.data).toBeDefined();
      expect(result.error).toBeNull();
      expect(result.data?.length).toBe(1);
      expect(result.data?.[0].salary_id).toBe("test-salary-id");
    });

    it("should handle database errors when getting by salary ID", async () => {
      // ARRANGE
      resetMocks = setupMocks({
        getByField: () =>
          Promise.resolve({
            data: null,
            error: new Error("Database error"),
          }),
      });

      // ACT
      const result = await AllowanceSalaryService.getBySalaryId(
        "test-salary-id"
      );

      // ASSERT
      expect(result.data).toBeNull();
      expect(result.error).toBeDefined();
      expect(result.error?.message).toContain("Database error");
    });
  });

  // Test update method
  describe("update", () => {
    it("should update an allowance successfully", async () => {
      // ARRANGE
      const mockAllowance = createMockAllowance();
      const updatedAllowance = {
        ...mockAllowance,
        amount: 250000,
        allowance_type: AllowanceSalaryType.MEAL,
      };

      // Mock salary object with necessary fields for total salary calculation
      const mockSalary = {
        id: "test-salary-id",
        base_salary: 5000000,
        total_bonus: 500000,
        total_allowance: 200000, // Will be updated by the service
        total_deduction: 100000,
        total_salary: 5600000, // Will be updated by the service
      };

      resetMocks = setupMocks({
        getById: (id) => {
          // Always return the appropriate object based on the ID
          return Promise.resolve({
            data: id === "test-allowance-id" ? mockAllowance : mockSalary,
            error: null,
          });
        },
        update: () => Promise.resolve({ data: updatedAllowance, error: null }),
        getByField: () =>
          Promise.resolve({ data: [updatedAllowance], error: null }),
      });

      const updateData = {
        amount: 250000,
        allowance_type: AllowanceSalaryType.MEAL,
      };

      // ACT
      const result = await AllowanceSalaryService.update(
        "test-allowance-id",
        updateData,
        "test-user-id"
      );

      // ASSERT
      expect(result.data).toBeDefined();
      expect(result.error).toBeNull();
      expect(result.data?.amount).toBe(250000);
      expect(result.data?.allowance_type).toBe(AllowanceSalaryType.MEAL);
    });

    it("should return error when allowance not found", async () => {
      // ARRANGE
      resetMocks = setupMocks({
        getById: () => Promise.resolve({ data: null, error: null }),
      });

      const updateData = {
        amount: 250000,
      };

      // ACT
      const result = await AllowanceSalaryService.update(
        "test-allowance-id",
        updateData,
        "test-user-id"
      );

      // ASSERT
      expect(result.data).toBeNull();
      expect(result.error).toBeDefined();
      expect(result.error?.message).toContain("not found");
    });
  });

  // Test delete method
  describe("delete", () => {
    it("should delete an allowance successfully", async () => {
      // ARRANGE
      const mockAllowance = createMockAllowance();

      // Mock salary object with necessary fields for total salary calculation
      const mockSalary = {
        id: "test-salary-id",
        base_salary: 5000000,
        total_bonus: 500000,
        total_allowance: 200000, // Will be updated by the service
        total_deduction: 100000,
        total_salary: 5600000, // Will be updated by the service
      };

      resetMocks = setupMocks({
        getById: (id) => {
          // Always return the appropriate object based on the ID
          return Promise.resolve({
            data: id === "test-allowance-id" ? mockAllowance : mockSalary,
            error: null,
          });
        },
        softDelete: () => Promise.resolve({ data: null, error: null }),
        getByField: () => Promise.resolve({ data: [], error: null }),
        update: () => Promise.resolve({ data: null, error: null }),
      });

      // ACT
      const result = await AllowanceSalaryService.delete(
        "test-allowance-id",
        "test-user-id"
      );

      // ASSERT
      expect(result.data).toBeDefined();
      expect(result.error).toBeNull();
      expect(result.data?.id).toBe("test-allowance-id");
    });

    it("should return error when allowance not found", async () => {
      // ARRANGE
      resetMocks = setupMocks({
        getById: () => Promise.resolve({ data: null, error: null }),
      });

      // ACT
      const result = await AllowanceSalaryService.delete(
        "test-allowance-id",
        "test-user-id"
      );

      // ASSERT
      expect(result.data).toBeNull();
      expect(result.error).toBeDefined();
      expect(result.error?.message).toContain("not found");
    });
  });
});
