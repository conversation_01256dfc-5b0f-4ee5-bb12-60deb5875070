import { describe, expect, it } from "bun:test";

/**
 * Test to verify that regular tasks use boolean completion_status
 * while project tasks continue to use TaskStatus enum
 */
describe("Task Completion Status Types", () => {
  it("should verify TaskStatus enum still exists for project tasks", () => {
    // Import TaskStatus enum - should still exist for project tasks
    const { TaskStatus } = require("../../database/models/task.model");
    
    expect(TaskStatus.NOT_COMPLETED).toBe("not_completed");
    expect(TaskStatus.ON_PROGRESS).toBe("on_progress");
    expect(TaskStatus.COMPLETED).toBe("completed");
  });

  it("should verify Task interface uses boolean completion_status", () => {
    // This test verifies the type structure at runtime
    const { Task } = require("../../database/models/task.model");
    
    // Create a mock task with boolean completion_status
    const mockTask = {
      id: "test-id",
      description: "Test task",
      completion_status: true, // Boolean value
      employee_id: "emp-123",
      due_date: "2024-01-01",
      created_at: new Date().toISOString(),
      created_by: "user-123",
    };

    // Verify boolean values are accepted
    expect(typeof mockTask.completion_status).toBe("boolean");
    expect(mockTask.completion_status).toBe(true);
    
    // Test false value
    mockTask.completion_status = false;
    expect(mockTask.completion_status).toBe(false);
  });

  it("should verify attendance task input accepts boolean", () => {
    // Test the TaskInput interface used in attendance service
    const taskInput = {
      description: "Test attendance task",
      due_date: "2024-01-01",
      completion_status: false, // Boolean value
    };

    expect(typeof taskInput.completion_status).toBe("boolean");
    expect(taskInput.completion_status).toBe(false);
    
    // Test true value
    taskInput.completion_status = true;
    expect(taskInput.completion_status).toBe(true);
  });

  it("should verify project task still uses enum", () => {
    // Import ProjectTask model
    const { TaskStatus } = require("../../database/models/task.model");
    
    // Create a mock project task with enum completion_status
    const mockProjectTask = {
      id: "test-id",
      assigned_by: "user-123",
      description: "Test project task",
      completion_status: TaskStatus.NOT_COMPLETED, // Enum value
      employee_id: "emp-123",
      initial_date: "2024-01-01",
      due_date: "2024-01-31",
      project_id: "proj-123",
      created_at: new Date().toISOString(),
      created_by: "user-123",
    };

    // Verify enum values are used
    expect(mockProjectTask.completion_status).toBe("not_completed");
    
    // Test other enum values
    mockProjectTask.completion_status = TaskStatus.COMPLETED;
    expect(mockProjectTask.completion_status).toBe("completed");
  });
});
