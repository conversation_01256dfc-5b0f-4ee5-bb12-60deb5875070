import { describe, expect, it, mock, beforeEach, afterEach } from "bun:test";
import { BonusSalaryController } from "../../modules/bonus-salary/controller";
import { BonusSalaryService } from "../../modules/bonus-salary/service";
import { BonusSalaryType } from "../../database/models/bonus-salary.model";
import { createMockBonus, createMockContext, createMockFn } from "./test-utils";

// Mock the Supabase client first to prevent real initialization
mock.module("../../libs/supabase", () => {
  return {
    supabase: {
      from: () => ({}),
    },
  };
});

describe("BonusSalaryController", () => {
  // Store original methods to restore after tests
  let originalCreate: typeof BonusSalaryService.create;
  let originalGetById: typeof BonusSalaryService.getById;
  let originalGetBySalaryId: typeof BonusSalaryService.getBySalaryId;
  let originalUpdate: typeof BonusSalaryService.update;
  let originalDelete: typeof BonusSalaryService.delete;

  beforeEach(() => {
    // Store original methods
    originalCreate = BonusSalaryService.create;
    originalGetById = BonusSalaryService.getById;
    originalGetBySalaryId = BonusSalaryService.getBySalaryId;
    originalUpdate = BonusSalaryService.update;
    originalDelete = BonusSalaryService.delete;
  });

  afterEach(() => {
    // Restore original methods
    BonusSalaryService.create = originalCreate;
    BonusSalaryService.getById = originalGetById;
    BonusSalaryService.getBySalaryId = originalGetBySalaryId;
    BonusSalaryService.update = originalUpdate;
    BonusSalaryService.delete = originalDelete;
  });

  describe("create", () => {
    it("should create a bonus successfully", async () => {
      // ARRANGE
      const mockContext = createMockContext({
        body: {
          salary_id: "test-salary-id",
          amount: 500000,
          bonus_type: BonusSalaryType.KPI,
          notes: "Test bonus",
          kpi_id: "test-kpi-id",
          project_id: null,
        },
      });

      // Mock the service method
      BonusSalaryService.create = createMockFn(() =>
        Promise.resolve({
          data: createMockBonus(),
          error: null,
        })
      );

      // ACT
      const result = await BonusSalaryController.create(mockContext);

      // ASSERT
      expect(result.success).toBe(true);
      expect(result.data).toBeDefined();
      expect(result.message).toContain("created successfully");
    });

    it("should handle errors when creating a bonus", async () => {
      // ARRANGE
      const mockContext = createMockContext({
        body: {
          salary_id: "test-salary-id",
          amount: 500000,
          bonus_type: BonusSalaryType.KPI,
          notes: "Test bonus",
          kpi_id: "test-kpi-id",
          project_id: null,
        },
      });

      // Mock the service method to return an error
      BonusSalaryService.create = createMockFn(() =>
        Promise.resolve({
          data: null,
          error: new Error("Failed to create bonus"),
        })
      );

      // ACT
      const result = await BonusSalaryController.create(mockContext);

      // ASSERT
      expect(result.success).toBe(false);
      expect(result.message).toContain("Failed to create bonus");
    });
  });

  describe("getBySalaryId", () => {
    it("should get bonuses by salary ID successfully", async () => {
      // ARRANGE
      const mockContext = createMockContext({
        params: { salaryId: "test-salary-id" },
      });

      // Mock the service method
      BonusSalaryService.getBySalaryId = createMockFn(() =>
        Promise.resolve({
          data: [createMockBonus()],
          error: null,
        })
      );

      // ACT
      const result = await BonusSalaryController.getBySalaryId(mockContext);

      // ASSERT
      expect(result.success).toBe(true);
      expect(result.data).toBeDefined();
      expect(result.data.length).toBe(1);
      expect(result.data[0].salary_id).toBe("test-salary-id");
    });

    it("should return empty array when no bonuses found", async () => {
      // ARRANGE
      const mockContext = createMockContext({
        params: { salaryId: "test-salary-id" },
      });

      // Mock the service method to return empty array
      BonusSalaryService.getBySalaryId = createMockFn(() =>
        Promise.resolve({
          data: [],
          error: null,
        })
      );

      // ACT
      const result = await BonusSalaryController.getBySalaryId(mockContext);

      // ASSERT
      expect(result.success).toBe(true);
      expect(result.data).toEqual([]);
      expect(result.message).toContain("No bonuses found");
    });

    it("should handle errors when getting bonuses", async () => {
      // ARRANGE
      const mockContext = createMockContext({
        params: { salaryId: "test-salary-id" },
      });

      // Mock the service method to return an error
      BonusSalaryService.getBySalaryId = createMockFn(() =>
        Promise.resolve({
          data: null,
          error: new Error("Failed to get bonuses"),
        })
      );

      // ACT
      const result = await BonusSalaryController.getBySalaryId(mockContext);

      // ASSERT
      expect(result.success).toBe(false);
      expect(result.message).toContain("Failed to get bonuses");
    });
  });

  describe("getById", () => {
    it("should get a bonus by ID successfully", async () => {
      // ARRANGE
      const mockContext = createMockContext({
        params: { id: "test-bonus-id" },
      });

      // Mock the service method
      BonusSalaryService.getById = createMockFn(() =>
        Promise.resolve({
          data: createMockBonus(),
          error: null,
        })
      );

      // ACT
      const result = await BonusSalaryController.getById(mockContext);

      // ASSERT
      expect(result.success).toBe(true);
      expect(result.data).toBeDefined();
      expect(result.data.id).toBe("test-bonus-id");
    });

    it("should return not found when bonus doesn't exist", async () => {
      // ARRANGE
      const mockContext = createMockContext({
        params: { id: "test-bonus-id" },
      });

      // Mock the service method to return null data
      BonusSalaryService.getById = createMockFn(() =>
        Promise.resolve({
          data: null,
          error: null,
        })
      );

      // ACT
      const result = await BonusSalaryController.getById(mockContext);

      // ASSERT
      expect(result.success).toBe(false);
      expect(result.status).toBe(404);
      expect(result.message).toContain("not found");
    });
  });

  describe("update", () => {
    it("should update a bonus successfully", async () => {
      // ARRANGE
      const mockContext = createMockContext({
        params: { id: "test-bonus-id" },
        body: {
          amount: 600000,
          bonus_type: BonusSalaryType.PROJECT,
          notes: "Updated test bonus",
        },
      });

      // Mock the getById service method
      BonusSalaryService.getById = createMockFn(() =>
        Promise.resolve({
          data: createMockBonus(),
          error: null,
        })
      );

      // Mock the update service method
      BonusSalaryService.update = createMockFn(() =>
        Promise.resolve({
          data: createMockBonus({
            amount: 600000,
            bonus_type: BonusSalaryType.PROJECT,
            notes: "Updated test bonus",
          }),
          error: null,
        })
      );

      // ACT
      const result = await BonusSalaryController.update(mockContext);

      // ASSERT
      expect(result.success).toBe(true);
      expect(result.data).toBeDefined();
      expect(result.message).toContain("updated successfully");
    });

    it("should handle validation errors when updating", async () => {
      // ARRANGE
      const mockContext = createMockContext({
        params: { id: "test-bonus-id" },
        body: {},
      });

      // Add badRequest method to context
      mockContext.badRequest = (message: string, error = null) => ({
        success: false,
        message,
        error,
        status: 400,
      });

      // ACT
      const result = await BonusSalaryController.update(mockContext);

      // ASSERT
      expect(result.success).toBe(false);
      expect(result.status).toBe(400);
      expect(result.message).toContain("No valid fields to update");
    });

    it("should handle not found errors when updating", async () => {
      // ARRANGE
      const mockContext = createMockContext({
        params: { id: "test-bonus-id" },
        body: { amount: 600000 },
      });

      // Mock the getById service method to return null
      BonusSalaryService.getById = createMockFn(() =>
        Promise.resolve({
          data: null,
          error: null,
        })
      );

      // ACT
      const result = await BonusSalaryController.update(mockContext);

      // ASSERT
      expect(result.success).toBe(false);
      expect(result.status).toBe(404);
      expect(result.message).toContain("not found");
    });
  });

  describe("delete", () => {
    it("should delete a bonus successfully", async () => {
      // ARRANGE
      const mockContext = createMockContext({
        params: { id: "test-bonus-id" },
      });

      // Mock the getById service method
      BonusSalaryService.getById = createMockFn(() =>
        Promise.resolve({
          data: createMockBonus(),
          error: null,
        })
      );

      // Mock the delete service method
      BonusSalaryService.delete = createMockFn(() =>
        Promise.resolve({
          data: { id: "test-bonus-id" },
          error: null,
        })
      );

      // ACT
      const result = await BonusSalaryController.delete(mockContext);

      // ASSERT
      expect(result.success).toBe(true);
      expect(result.data).toBeDefined();
      expect(result.message).toContain("deleted successfully");
    });

    it("should handle not found errors when deleting", async () => {
      // ARRANGE
      const mockContext = createMockContext({
        params: { id: "test-bonus-id" },
      });

      // Mock the getById service method to return null
      BonusSalaryService.getById = createMockFn(() =>
        Promise.resolve({
          data: null,
          error: null,
        })
      );

      // ACT
      const result = await BonusSalaryController.delete(mockContext);

      // ASSERT
      expect(result.success).toBe(false);
      expect(result.status).toBe(404);
      expect(result.message).toContain("not found");
    });
  });
});
