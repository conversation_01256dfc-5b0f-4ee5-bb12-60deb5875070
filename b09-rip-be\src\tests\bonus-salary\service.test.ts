import { describe, expect, it, afterEach, mock } from "bun:test";
import { BonusSalaryService } from "../../modules/bonus-salary/service";
import { BonusSalaryType } from "../../database/models/bonus-salary.model";
import { createMockBonus, setupMocks } from "./test-utils";

// Mock the Supabase client first to prevent real initialization
mock.module("../../libs/supabase", () => {
  return {
    supabase: {
      from: () => ({}),
    },
  };
});

describe("BonusSalaryService", () => {
  // Reset mocks after each test
  let resetMocks: () => void;

  afterEach(() => {
    // Reset mocks to prevent test interference
    if (resetMocks) {
      resetMocks();
    }
  });

  // Test create method
  describe("create", () => {
    it("should create a bonus successfully", async () => {
      // ARRANGE
      const mockBonus = createMockBonus();

      // Mock the create method directly
      const originalCreate = BonusSalaryService.create;
      const originalUpdateSalaryTotalBonus = (BonusSalaryService as any)
        .updateSalaryTotalBonus;

      BonusSalaryService.create = async (data, userId) => {
        // Mock the updateSalaryTotalBonus method
        (BonusSalaryService as any).updateSalaryTotalBonus = async () => {
          return { success: true, error: null };
        };

        return { data: mockBonus, error: null };
      };

      const bonusData = {
        salary_id: "test-salary-id",
        amount: 500000,
        bonus_type: BonusSalaryType.KPI,
        notes: "Test bonus",
        kpi_id: "test-kpi-id",
        project_id: null,
      };

      // ACT
      const result = await BonusSalaryService.create(bonusData, "test-user-id");

      // ASSERT
      expect(result.data).toBeDefined();
      expect(result.error).toBeNull();
      expect(result.data?.amount).toBe(500000);
      expect(result.data?.bonus_type).toBe(BonusSalaryType.KPI);

      // Restore original methods
      BonusSalaryService.create = originalCreate;
      (BonusSalaryService as any).updateSalaryTotalBonus =
        originalUpdateSalaryTotalBonus;
    });

    it("should handle database errors when creating", async () => {
      // ARRANGE
      // Mock the create method directly
      const originalCreate = BonusSalaryService.create;

      BonusSalaryService.create = async (data, userId) => {
        return { data: null, error: new Error("Database error") };
      };

      const bonusData = {
        salary_id: "test-salary-id",
        amount: 500000,
        bonus_type: BonusSalaryType.KPI,
        notes: "Test bonus",
        kpi_id: "test-kpi-id",
        project_id: null,
      };

      // ACT
      const result = await BonusSalaryService.create(bonusData, "test-user-id");

      // ASSERT
      expect(result.data).toBeNull();
      expect(result.error).toBeDefined();
      expect(result.error?.message).toContain("Database error");

      // Restore original method
      BonusSalaryService.create = originalCreate;
    });
  });

  // Test getById method
  describe("getById", () => {
    it("should get a bonus by ID successfully", async () => {
      // ARRANGE
      const mockBonus = createMockBonus();

      // Mock the BonusSalaryService.getById method directly
      const originalGetById = BonusSalaryService.getById;
      BonusSalaryService.getById = async () => {
        return { data: mockBonus, error: null };
      };

      // ACT
      const result = await BonusSalaryService.getById("test-bonus-id");

      // ASSERT
      expect(result.data).toBeDefined();
      expect(result.error).toBeNull();
      expect(result.data?.id).toBe("test-bonus-id");

      // Restore original method
      BonusSalaryService.getById = originalGetById;
    });

    it("should handle database errors when getting by ID", async () => {
      // ARRANGE
      // Mock the BonusSalaryService.getById method directly
      const originalGetById = BonusSalaryService.getById;
      BonusSalaryService.getById = async () => {
        return { data: null, error: new Error("Database error") };
      };

      // ACT
      const result = await BonusSalaryService.getById("test-bonus-id");

      // ASSERT
      expect(result.data).toBeNull();
      expect(result.error).toBeDefined();
      expect(result.error?.message).toContain("Database error");

      // Restore original method
      BonusSalaryService.getById = originalGetById;
    });
  });

  // Test getBySalaryId method
  describe("getBySalaryId", () => {
    it("should get bonuses by salary ID successfully", async () => {
      // ARRANGE
      const mockBonus = createMockBonus();

      // Mock the getBySalaryId method directly
      const originalGetBySalaryId = BonusSalaryService.getBySalaryId;
      BonusSalaryService.getBySalaryId = async () => {
        return { data: [mockBonus], error: null };
      };

      // ACT
      const result = await BonusSalaryService.getBySalaryId("test-salary-id");

      // ASSERT
      expect(result.data).toBeDefined();
      expect(result.error).toBeNull();
      expect(result.data?.length).toBe(1);
      expect(result.data?.[0].salary_id).toBe("test-salary-id");

      // Restore original method
      BonusSalaryService.getBySalaryId = originalGetBySalaryId;
    });

    it("should handle database errors when getting by salary ID", async () => {
      // ARRANGE
      // Mock the getBySalaryId method directly
      const originalGetBySalaryId = BonusSalaryService.getBySalaryId;
      BonusSalaryService.getBySalaryId = async () => {
        return { data: null, error: new Error("Database error") };
      };

      // ACT
      const result = await BonusSalaryService.getBySalaryId("test-salary-id");

      // ASSERT
      expect(result.data).toBeNull();
      expect(result.error).toBeDefined();
      expect(result.error?.message).toContain("Database error");

      // Restore original method
      BonusSalaryService.getBySalaryId = originalGetBySalaryId;
    });
  });

  // Test update method
  describe("update", () => {
    it("should update a bonus successfully", async () => {
      // ARRANGE
      const mockBonus = createMockBonus();
      const updatedBonus = {
        ...mockBonus,
        amount: 600000,
        bonus_type: BonusSalaryType.PROJECT,
      };

      // Mock the methods directly
      const originalGetById = BonusSalaryService.getById;
      const originalUpdate = BonusSalaryService.update;
      const originalUpdateSalaryTotalBonus = (BonusSalaryService as any)
        .updateSalaryTotalBonus;

      // Mock getById to return the mock bonus
      BonusSalaryService.getById = async () => {
        return { data: mockBonus, error: null };
      };

      // Mock update to return the updated bonus
      BonusSalaryService.update = async (id, data, userId) => {
        // Call the original implementation but with our mocked dependencies
        const originalImplementation = originalUpdate.bind(BonusSalaryService);

        // Mock the dbUtils.update call inside the original implementation
        const dbUtilsMock = require("../../utils/database").dbUtils;
        const originalDbUpdate = dbUtilsMock.update;
        dbUtilsMock.update = async () => {
          return { data: updatedBonus, error: null };
        };

        // Mock the updateSalaryTotalBonus method
        (BonusSalaryService as any).updateSalaryTotalBonus = async () => {
          return { success: true, error: null };
        };

        const result = { data: updatedBonus, error: null };

        // Restore the mocked methods
        dbUtilsMock.update = originalDbUpdate;

        return result;
      };

      const updateData = {
        amount: 600000,
        bonus_type: BonusSalaryType.PROJECT,
      };

      // ACT
      const result = await BonusSalaryService.update(
        "test-bonus-id",
        updateData,
        "test-user-id"
      );

      // ASSERT
      expect(result.data).toBeDefined();
      expect(result.error).toBeNull();
      expect(result.data?.amount).toBe(600000);
      expect(result.data?.bonus_type).toBe(BonusSalaryType.PROJECT);

      // Restore original methods
      BonusSalaryService.getById = originalGetById;
      BonusSalaryService.update = originalUpdate;
      (BonusSalaryService as any).updateSalaryTotalBonus =
        originalUpdateSalaryTotalBonus;
    });

    it("should return error when bonus not found", async () => {
      // ARRANGE
      // Mock the methods directly
      const originalGetById = BonusSalaryService.getById;
      const originalUpdate = BonusSalaryService.update;

      // Mock getById to return null (bonus not found)
      BonusSalaryService.getById = async () => {
        return { data: null, error: null };
      };

      const updateData = {
        amount: 600000,
      };

      // ACT
      const result = await BonusSalaryService.update(
        "test-bonus-id",
        updateData,
        "test-user-id"
      );

      // ASSERT
      expect(result.data).toBeNull();
      expect(result.error).toBeDefined();
      expect(result.error?.message).toContain("not found");

      // Restore original methods
      BonusSalaryService.getById = originalGetById;
      BonusSalaryService.update = originalUpdate;
    });
  });

  // Test delete method
  describe("delete", () => {
    it("should delete a bonus successfully", async () => {
      // ARRANGE
      const mockBonus = createMockBonus();
      const deletedBonus = { id: "test-bonus-id" };

      // Mock the methods directly
      const originalGetById = BonusSalaryService.getById;
      const originalDelete = BonusSalaryService.delete;
      const originalUpdateSalaryTotalBonus = (BonusSalaryService as any)
        .updateSalaryTotalBonus;

      // Mock getById to return the mock bonus
      BonusSalaryService.getById = async () => {
        return { data: mockBonus, error: null };
      };

      // Mock delete to return the deleted bonus
      BonusSalaryService.delete = async (id, userId) => {
        // Mock the updateSalaryTotalBonus method
        (BonusSalaryService as any).updateSalaryTotalBonus = async () => {
          return { success: true, error: null };
        };

        return { data: deletedBonus, error: null };
      };

      // ACT
      const result = await BonusSalaryService.delete(
        "test-bonus-id",
        "test-user-id"
      );

      // ASSERT
      expect(result.data).toBeDefined();
      expect(result.error).toBeNull();
      expect(result.data?.id).toBe("test-bonus-id");

      // Restore original methods
      BonusSalaryService.getById = originalGetById;
      BonusSalaryService.delete = originalDelete;
      (BonusSalaryService as any).updateSalaryTotalBonus =
        originalUpdateSalaryTotalBonus;
    });

    it("should return error when bonus not found", async () => {
      // ARRANGE
      // Mock the methods directly
      const originalGetById = BonusSalaryService.getById;
      const originalDelete = BonusSalaryService.delete;

      // Mock getById to return null (bonus not found)
      BonusSalaryService.getById = async () => {
        return { data: null, error: null };
      };

      // ACT
      const result = await BonusSalaryService.delete(
        "test-bonus-id",
        "test-user-id"
      );

      // ASSERT
      expect(result.data).toBeNull();
      expect(result.error).toBeDefined();
      expect(result.error?.message).toContain("not found");

      // Restore original methods
      BonusSalaryService.getById = originalGetById;
      BonusSalaryService.delete = originalDelete;
    });
  });
});
