import { describe, expect, it, mock, beforeEach, afterEach } from "bun:test";
import { DeductionSalaryController } from "../../modules/deduction-salary/controller";
import { DeductionSalaryService } from "../../modules/deduction-salary/service";
import { DeductionType } from "../../database/models/deduction-salary.model";
import { createMockDeduction, createMockContext, createMockFn } from "./test-utils";

// Mock the Supabase client first to prevent real initialization
mock.module("../../libs/supabase", () => {
  return {
    supabase: {
      from: () => ({}),
    },
  };
});

describe("DeductionSalaryController", () => {
  // Store original methods to restore after tests
  let originalCreate: typeof DeductionSalaryService.create;
  let originalGetById: typeof DeductionSalaryService.getById;
  let originalGetBySalaryId: typeof DeductionSalaryService.getBySalaryId;
  let originalUpdate: typeof DeductionSalaryService.update;
  let originalDelete: typeof DeductionSalaryService.delete;

  beforeEach(() => {
    // Store original methods
    originalCreate = DeductionSalaryService.create;
    originalGetById = DeductionSalaryService.getById;
    originalGetBySalaryId = DeductionSalaryService.getBySalaryId;
    originalUpdate = DeductionSalaryService.update;
    originalDelete = DeductionSalaryService.delete;
  });

  afterEach(() => {
    // Restore original methods
    DeductionSalaryService.create = originalCreate;
    DeductionSalaryService.getById = originalGetById;
    DeductionSalaryService.getBySalaryId = originalGetBySalaryId;
    DeductionSalaryService.update = originalUpdate;
    DeductionSalaryService.delete = originalDelete;
  });

  describe("create", () => {
    it("should create a deduction successfully", async () => {
      // ARRANGE
      const mockContext = createMockContext({
        body: {
          salary_id: "test-salary-id",
          amount: 100000,
          deduction_type: DeductionType.ABSENCE,
          notes: "Test deduction",
        },
      });

      // Mock the service method
      DeductionSalaryService.create = createMockFn(() => 
        Promise.resolve({
          data: createMockDeduction(),
          error: null,
        })
      );

      // ACT
      const result = await DeductionSalaryController.create(mockContext);

      // ASSERT
      expect(result.success).toBe(true);
      expect(result.data).toBeDefined();
      expect(result.message).toContain("created successfully");
    });

    it("should handle server error", async () => {
      // ARRANGE
      const mockContext = createMockContext({
        body: {
          salary_id: "test-salary-id",
          amount: 100000,
          deduction_type: DeductionType.ABSENCE,
          notes: "Test deduction",
        },
      });

      // Mock the service method
      DeductionSalaryService.create = createMockFn(() => 
        Promise.resolve({
          data: null,
          error: new Error("Test error"),
        })
      );

      // ACT
      const result = await DeductionSalaryController.create(mockContext);

      // ASSERT
      expect(result.success).toBe(false);
      expect(result.error).toBeDefined();
      expect(result.error.message).toContain("Test error");
    });
  });

  describe("getBySalaryId", () => {
    it("should get deductions by salary ID successfully", async () => {
      // ARRANGE
      const mockContext = createMockContext({
        params: { salaryId: "test-salary-id" },
      });

      // Mock the service method
      DeductionSalaryService.getBySalaryId = createMockFn(() => 
        Promise.resolve({
          data: [createMockDeduction()],
          error: null,
        })
      );

      // ACT
      const result = await DeductionSalaryController.getBySalaryId(mockContext);

      // ASSERT
      expect(result.success).toBe(true);
      expect(result.data).toBeDefined();
      expect(result.data.length).toBe(1);
      expect(result.data[0].salary_id).toBe("test-salary-id");
    });

    it("should handle empty results", async () => {
      // ARRANGE
      const mockContext = createMockContext({
        params: { salaryId: "test-salary-id" },
      });

      // Mock the service method
      DeductionSalaryService.getBySalaryId = createMockFn(() => 
        Promise.resolve({
          data: [],
          error: null,
        })
      );

      // ACT
      const result = await DeductionSalaryController.getBySalaryId(mockContext);

      // ASSERT
      expect(result.success).toBe(true);
      expect(result.data).toEqual([]);
      expect(result.message).toContain("No deductions found");
    });

    it("should handle server error", async () => {
      // ARRANGE
      const mockContext = createMockContext({
        params: { salaryId: "test-salary-id" },
      });

      // Mock the service method
      DeductionSalaryService.getBySalaryId = createMockFn(() => 
        Promise.resolve({
          data: null,
          error: new Error("Test error"),
        })
      );

      // ACT
      const result = await DeductionSalaryController.getBySalaryId(mockContext);

      // ASSERT
      expect(result.success).toBe(false);
      expect(result.error).toBeDefined();
      expect(result.error.message).toContain("Test error");
    });
  });

  describe("getById", () => {
    it("should get a deduction by ID successfully", async () => {
      // ARRANGE
      const mockContext = createMockContext({
        params: { id: "test-deduction-id" },
      });

      // Mock the service method
      DeductionSalaryService.getById = createMockFn(() => 
        Promise.resolve({
          data: createMockDeduction(),
          error: null,
        })
      );

      // ACT
      const result = await DeductionSalaryController.getById(mockContext);

      // ASSERT
      expect(result.success).toBe(true);
      expect(result.data).toBeDefined();
      expect(result.data.id).toBe("test-deduction-id");
    });

    it("should handle not found error", async () => {
      // ARRANGE
      const mockContext = createMockContext({
        params: { id: "test-deduction-id" },
      });

      // Mock the service method
      DeductionSalaryService.getById = createMockFn(() => 
        Promise.resolve({
          data: null,
          error: null,
        })
      );

      // ACT
      const result = await DeductionSalaryController.getById(mockContext);

      // ASSERT
      expect(result.success).toBe(false);
      expect(result.message).toContain("not found");
    });

    it("should handle server error", async () => {
      // ARRANGE
      const mockContext = createMockContext({
        params: { id: "test-deduction-id" },
      });

      // Mock the service method
      DeductionSalaryService.getById = createMockFn(() => 
        Promise.resolve({
          data: null,
          error: new Error("Test error"),
        })
      );

      // ACT
      const result = await DeductionSalaryController.getById(mockContext);

      // ASSERT
      expect(result.success).toBe(false);
      expect(result.error).toBeDefined();
      expect(result.error.message).toContain("Test error");
    });
  });

  describe("update", () => {
    it("should update a deduction successfully", async () => {
      // ARRANGE
      const mockContext = createMockContext({
        params: { id: "test-deduction-id" },
        body: {
          amount: 150000,
          notes: "Updated test deduction",
        },
      });

      // Mock the service method
      DeductionSalaryService.update = createMockFn(() => 
        Promise.resolve({
          data: createMockDeduction({ amount: 150000, notes: "Updated test deduction" }),
          error: null,
        })
      );

      // ACT
      const result = await DeductionSalaryController.update(mockContext);

      // ASSERT
      expect(result.success).toBe(true);
      expect(result.data).toBeDefined();
      expect(result.data.amount).toBe(150000);
      expect(result.data.notes).toBe("Updated test deduction");
    });

    it("should handle not found error", async () => {
      // ARRANGE
      const mockContext = createMockContext({
        params: { id: "test-deduction-id" },
        body: { amount: 150000 },
      });

      // Mock the service method
      DeductionSalaryService.update = createMockFn(() => 
        Promise.resolve({
          data: null,
          error: new Error("Deduction not found"),
        })
      );

      // ACT
      const result = await DeductionSalaryController.update(mockContext);

      // ASSERT
      expect(result.success).toBe(false);
      expect(result.message).toContain("not found");
    });

    it("should handle server error", async () => {
      // ARRANGE
      const mockContext = createMockContext({
        params: { id: "test-deduction-id" },
        body: { amount: 150000 },
      });

      // Mock the service method
      DeductionSalaryService.update = createMockFn(() => 
        Promise.resolve({
          data: null,
          error: new Error("Test error"),
        })
      );

      // ACT
      const result = await DeductionSalaryController.update(mockContext);

      // ASSERT
      expect(result.success).toBe(false);
      expect(result.error).toBeDefined();
      expect(result.error.message).toContain("Test error");
    });
  });

  describe("delete", () => {
    it("should delete a deduction successfully", async () => {
      // ARRANGE
      const mockContext = createMockContext({
        params: { id: "test-deduction-id" },
      });

      // Mock the service method
      DeductionSalaryService.delete = createMockFn(() => 
        Promise.resolve({
          data: { id: "test-deduction-id" },
          error: null,
        })
      );

      // ACT
      const result = await DeductionSalaryController.delete(mockContext);

      // ASSERT
      expect(result.success).toBe(true);
      expect(result.data).toBeDefined();
      expect(result.data.id).toBe("test-deduction-id");
    });

    it("should handle not found error", async () => {
      // ARRANGE
      const mockContext = createMockContext({
        params: { id: "test-deduction-id" },
      });

      // Mock the service method
      DeductionSalaryService.delete = createMockFn(() => 
        Promise.resolve({
          data: null,
          error: new Error("Deduction not found"),
        })
      );

      // ACT
      const result = await DeductionSalaryController.delete(mockContext);

      // ASSERT
      expect(result.success).toBe(false);
      expect(result.message).toContain("not found");
    });

    it("should handle server error", async () => {
      // ARRANGE
      const mockContext = createMockContext({
        params: { id: "test-deduction-id" },
      });

      // Mock the service method
      DeductionSalaryService.delete = createMockFn(() => 
        Promise.resolve({
          data: null,
          error: new Error("Test error"),
        })
      );

      // ACT
      const result = await DeductionSalaryController.delete(mockContext);

      // ASSERT
      expect(result.success).toBe(false);
      expect(result.error).toBeDefined();
      expect(result.error.message).toContain("Test error");
    });
  });
});
