import { describe, expect, it, afterEach } from "bun:test";
import { DeductionSalaryService } from "../../modules/deduction-salary/service";
import { DeductionType } from "../../database/models/deduction-salary.model";
import { createMockDeduction, setupMocks } from "./test-utils";

describe("DeductionSalaryService", () => {
  // Reset mocks after each test
  let resetMocks: () => void;

  afterEach(() => {
    // Reset mocks to prevent test interference
    if (resetMocks) {
      resetMocks();
    }
  });

  // Test create method
  describe("create", () => {
    it("should create a deduction successfully", async () => {
      // ARRANGE
      const mockDeduction = createMockDeduction();
      // Mock salary object with necessary fields for total salary calculation
      const mockSalary = {
        id: "test-salary-id",
        base_salary: 5000000,
        total_bonus: 500000,
        total_allowance: 300000,
        total_deduction: 0, // Will be updated by the service
        total_salary: 5800000, // Will be updated by the service
      };

      resetMocks = setupMocks({
        create: () => Promise.resolve({ data: mockDeduction, error: null }),
        getByField: () =>
          Promise.resolve({ data: [mockDeduction], error: null }),
        update: () => Promise.resolve({ data: null, error: null }),
        getById: () => Promise.resolve({ data: mockSalary, error: null }),
      });

      const deductionData = {
        salary_id: "test-salary-id",
        amount: 100000,
        deduction_type: DeductionType.ABSENCE,
        notes: "Test deduction",
      };

      // ACT
      const result = await DeductionSalaryService.create(
        deductionData,
        "test-user-id"
      );

      // ASSERT
      expect(result.data).toBeDefined();
      expect(result.error).toBeNull();
      expect(result.data?.amount).toBe(100000);
      expect(result.data?.deduction_type).toBe(DeductionType.ABSENCE);
    });

    it("should handle errors when creating a deduction", async () => {
      // ARRANGE
      resetMocks = setupMocks({
        create: () =>
          Promise.resolve({ data: null, error: new Error("Test error") }),
      });

      const deductionData = {
        salary_id: "test-salary-id",
        amount: 100000,
        deduction_type: DeductionType.ABSENCE,
        notes: "Test deduction",
      };

      // ACT
      const result = await DeductionSalaryService.create(
        deductionData,
        "test-user-id"
      );

      // ASSERT
      expect(result.data).toBeNull();
      expect(result.error).toBeDefined();
      expect(result.error?.message).toContain("Test error");
    });
  });

  // Test getById method
  describe("getById", () => {
    it("should get a deduction by ID successfully", async () => {
      // ARRANGE
      const mockDeduction = createMockDeduction();
      resetMocks = setupMocks({
        getById: () => Promise.resolve({ data: mockDeduction, error: null }),
      });

      // ACT
      const result = await DeductionSalaryService.getById("test-deduction-id");

      // ASSERT
      expect(result.data).toBeDefined();
      expect(result.error).toBeNull();
      expect(result.data?.id).toBe("test-deduction-id");
    });

    it("should handle errors when getting a deduction", async () => {
      // ARRANGE
      resetMocks = setupMocks({
        getById: () =>
          Promise.resolve({ data: null, error: new Error("Test error") }),
      });

      // ACT
      const result = await DeductionSalaryService.getById("test-deduction-id");

      // ASSERT
      expect(result.data).toBeNull();
      expect(result.error).toBeDefined();
      expect(result.error?.message).toContain("Test error");
    });
  });

  // Test getBySalaryId method
  describe("getBySalaryId", () => {
    it("should get deductions by salary ID successfully", async () => {
      // ARRANGE
      const mockDeduction = createMockDeduction();
      resetMocks = setupMocks({
        getByField: () =>
          Promise.resolve({ data: [mockDeduction], error: null }),
      });

      // ACT
      const result = await DeductionSalaryService.getBySalaryId(
        "test-salary-id"
      );

      // ASSERT
      expect(result.data).toBeDefined();
      expect(result.error).toBeNull();
      expect(result.data?.length).toBe(1);
      expect(result.data?.[0].salary_id).toBe("test-salary-id");
    });

    it("should handle errors when getting deductions by salary ID", async () => {
      // ARRANGE
      resetMocks = setupMocks({
        getByField: () =>
          Promise.resolve({ data: null, error: new Error("Test error") }),
      });

      // ACT
      const result = await DeductionSalaryService.getBySalaryId(
        "test-salary-id"
      );

      // ASSERT
      expect(result.data).toBeNull();
      expect(result.error).toBeDefined();
      expect(result.error?.message).toContain("Test error");
    });
  });

  // Test update method
  describe("update", () => {
    it("should update a deduction successfully", async () => {
      // ARRANGE
      const mockDeduction = createMockDeduction();
      const updatedDeduction = createMockDeduction({ amount: 150000 });

      // Mock salary object with necessary fields for total salary calculation
      const mockSalary = {
        id: "test-salary-id",
        base_salary: 5000000,
        total_bonus: 500000,
        total_allowance: 300000,
        total_deduction: 100000, // Will be updated by the service
        total_salary: 5700000, // Will be updated by the service
      };

      resetMocks = setupMocks({
        getById: (id) => {
          // Always return the appropriate object based on the ID
          return Promise.resolve({
            data: id === "test-deduction-id" ? mockDeduction : mockSalary,
            error: null,
          });
        },
        update: () => Promise.resolve({ data: updatedDeduction, error: null }),
        getByField: () =>
          Promise.resolve({ data: [updatedDeduction], error: null }),
      });

      const updateData = {
        amount: 150000,
        notes: "Updated test deduction",
      };

      // ACT
      const result = await DeductionSalaryService.update(
        "test-deduction-id",
        updateData,
        "test-user-id"
      );

      // ASSERT
      expect(result.data).toBeDefined();
      expect(result.error).toBeNull();
      expect(result.data?.amount).toBe(150000);
    });

    it("should handle deduction not found error", async () => {
      // ARRANGE
      resetMocks = setupMocks({
        getById: () =>
          Promise.resolve({
            data: null,
            error: new Error("Deduction not found"),
          }),
      });

      const updateData = {
        amount: 150000,
      };

      // ACT
      const result = await DeductionSalaryService.update(
        "test-deduction-id",
        updateData,
        "test-user-id"
      );

      // ASSERT
      expect(result.data).toBeNull();
      expect(result.error).toBeDefined();
      expect(result.error?.message).toContain("Deduction not found");
    });
  });

  // Test delete method
  describe("delete", () => {
    it("should delete a deduction successfully", async () => {
      // ARRANGE
      const mockDeduction = createMockDeduction();

      // Mock salary object with necessary fields for total salary calculation
      const mockSalary = {
        id: "test-salary-id",
        base_salary: 5000000,
        total_bonus: 500000,
        total_allowance: 300000,
        total_deduction: 100000, // Will be updated by the service
        total_salary: 5700000, // Will be updated by the service
      };

      resetMocks = setupMocks({
        getById: (id) => {
          // Always return the appropriate object based on the ID
          return Promise.resolve({
            data: id === "test-deduction-id" ? mockDeduction : mockSalary,
            error: null,
          });
        },
        softDelete: () => Promise.resolve({ error: null }),
        getByField: () => Promise.resolve({ data: [], error: null }),
        update: () => Promise.resolve({ data: null, error: null }),
      });

      // ACT
      const result = await DeductionSalaryService.delete(
        "test-deduction-id",
        "test-user-id"
      );

      // ASSERT
      expect(result.data).toBeDefined();
      expect(result.error).toBeNull();
      expect(result.data?.id).toBe("test-deduction-id");
    });

    it("should handle deduction not found error", async () => {
      // ARRANGE
      resetMocks = setupMocks({
        getById: () =>
          Promise.resolve({
            data: null,
            error: new Error("Deduction not found"),
          }),
      });

      // ACT
      const result = await DeductionSalaryService.delete(
        "test-deduction-id",
        "test-user-id"
      );

      // ASSERT
      expect(result.data).toBeNull();
      expect(result.error).toBeDefined();
      expect(result.error?.message).toContain("Deduction not found");
    });
  });
});
