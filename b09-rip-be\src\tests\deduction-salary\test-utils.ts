import { DeductionType } from "../../database/models/deduction-salary.model";

/**
 * Creates a mock deduction object for testing
 * @param overrides Properties to override in the default mock
 * @returns A mock deduction object
 */
export function createMockDeduction(overrides = {}) {
  return {
    id: "test-deduction-id",
    salary_id: "test-salary-id",
    amount: 100000,
    deduction_type: DeductionType.ABSENCE,
    notes: "Test deduction",
    created_at: new Date().toISOString(),
    created_by: "test-user-id",
    updated_at: null,
    updated_by: null,
    deleted_at: null,
    deleted_by: null,
    ...overrides,
  };
}

/**
 * Creates a mock function with call tracking
 * @param implementation Optional implementation function
 * @returns A mock function with call tracking
 */
export function createMockFn<T extends (...args: any[]) => any>(
  implementation?: T
): { (...args: Parameters<T>): Promise<any>; mock: { calls: any[][] } } {
  const calls: any[][] = [];
  const fn = (...args: Parameters<T>) => {
    calls.push(args);
    // Ensure we return a Promise because the service methods return Promises
    const result = implementation?.(...args);
    return result instanceof Promise ? result : Promise.resolve(result);
  };
  fn.mock = { calls };
  return fn;
}

/**
 * Sets up mocks for database utilities
 * @param mocks Optional mock overrides
 * @returns A function to reset the mocks
 */
export function setupMocks(mocks: Record<string, any> = {}) {
  // Import the mock module
  const mockModule = require("bun:test").mock;

  // Create mock database utilities
  const dbUtilsMock = {
    create: createMockFn(
      mocks.create || (() => Promise.resolve({ data: null, error: null }))
    ),
    getById: createMockFn(
      mocks.getById || (() => Promise.resolve({ data: null, error: null }))
    ),
    getAll: createMockFn(
      mocks.getAll ||
        (() => Promise.resolve({ data: [], error: null, result: null }))
    ),
    getByField: createMockFn(
      mocks.getByField || (() => Promise.resolve({ data: [], error: null }))
    ),
    update: createMockFn(
      mocks.update || (() => Promise.resolve({ data: null, error: null }))
    ),
    softDelete: createMockFn(
      mocks.softDelete || (() => Promise.resolve({ data: null, error: null }))
    ),
    query: createMockFn(
      mocks.query ||
        (() => ({
          raw: {
            select: () => ({
              eq: () => ({
                is: () => ({
                  order: () => Promise.resolve({ data: [], error: null }),
                }),
              }),
            }),
          },
        }))
    ),
  };

  // Mock the database utilities module
  mockModule.module("../../utils/database", () => ({
    dbUtils: dbUtilsMock,
  }));

  // Return a function to reset the mocks
  return () => {
    // Reset the mocks by re-mocking the module
    mockModule.module("../../utils/database", () => ({
      dbUtils: dbUtilsMock,
    }));
  };
}

/**
 * Creates a mock context for controller tests
 * @param overrides Properties to override in the default mock
 * @returns A mock context object
 */
export function createMockContext(overrides = {}) {
  return {
    params: {},
    query: {},
    body: {},
    user: { id: "test-user-id", profile: { role: "Finance" } },
    success: (data: any, message = "Success") => ({
      success: true,
      data,
      message,
    }),
    error: (message: string, error = null) => ({
      success: false,
      message,
      error,
    }),
    badRequest: (message: string, error = null) => ({
      success: false,
      message,
      error,
      status: 400,
    }),
    notFound: (message = "Not found") => ({
      success: false,
      message,
      status: 404,
    }),
    serverError: (message = "Server error", error = null) => ({
      success: false,
      message,
      error,
      status: 500,
    }),
    forbidden: (message = "Forbidden", errorCode = null) => ({
      success: false,
      message,
      error: { code: errorCode || "FORBIDDEN" },
      status: 403,
    }),
    ...overrides,
  };
}
