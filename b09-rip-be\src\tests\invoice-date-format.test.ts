import { describe, it, expect } from "bun:test";

describe("Invoice Date Format Tests", () => {
  it("should handle YYYY-MM-DD date format", () => {
    // Test the date format validation in the controller
    const validDate = "2025-12-31";
    expect(validDate.match(/^\d{4}-\d{2}-\d{2}$/)).not.toBeNull();

    const invalidDate = "invalid-date";
    expect(invalidDate.match(/^\d{4}-\d{2}-\d{2}$/)).toBeNull();
  });

  it("should convert ISO date to YYYY-MM-DD", () => {
    const isoDate = new Date("2025-12-31").toISOString();
    const formattedDate = isoDate.split("T")[0];
    expect(formattedDate).toBe("2025-12-31");
  });

  it("should handle date conversion in create method", () => {
    // Create a test function that mimics the controller's date handling
    function formatDate(dateInput: string): string {
      let formattedDate = dateInput;
      if (!formattedDate.match(/^\d{4}-\d{2}-\d{2}$/)) {
        try {
          formattedDate = new Date(dateInput).toISOString().split("T")[0];
        } catch (error) {
          throw new Error("Invalid date format");
        }
      }
      return formattedDate;
    }

    // Test with valid YYYY-MM-DD format
    expect(formatDate("2025-12-31")).toBe("2025-12-31");

    // Test with ISO string
    const isoDate = new Date("2025-12-31").toISOString();
    expect(formatDate(isoDate)).toBe("2025-12-31");

    // Test with invalid date
    expect(() => formatDate("invalid-date")).toThrow();
  });
});
