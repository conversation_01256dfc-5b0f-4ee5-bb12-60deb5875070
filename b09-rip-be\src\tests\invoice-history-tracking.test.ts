import { describe, it, expect, mock } from "bun:test";
import { InvoiceUpdateHistoryService } from "../modules/invoice/invoice-update-history.service";
import { Invoice, UpdateInvoiceDto } from "../database/models/invoice.model";
import { dbUtils } from "../utils/database";
import {
  CreateInvoiceUpdateHistoryDto,
  InvoiceUpdateHistory,
} from "../database/models/invoice-update-history.model";

// Create custom mock function
function createMockFn<T extends (...args: any[]) => any>(
  implementation?: T
): { (...args: Parameters<T>): Promise<any>; mock: { calls: any[][] } } {
  const calls: any[][] = [];
  const fn = (...args: Parameters<T>) => {
    calls.push(args);
    // Ensure we return a Promise because the service methods return Promises
    const result = implementation?.(...args);
    return result instanceof Promise ? result : Promise.resolve(result);
  };
  fn.mock = { calls };
  return fn;
}

// Mock the database utilities
mock.module("../utils/database", () => {
  return {
    dbUtils: {
      create: createMockFn(() =>
        Promise.resolve({
          data: {
            id: "test-history-id",
            invoice_id: "test-invoice-id",
            change_description:
              "Updated recipient_name from 'Original Company' to 'Updated Company'",
            created_at: new Date().toISOString(),
            created_by: "test-user-id",
          },
          error: null,
        })
      ),
      getAll: createMockFn(() =>
        Promise.resolve({
          data: {
            items: [
              {
                id: "test-history-id-1",
                invoice_id: "test-invoice-id",
                change_description:
                  "Updated recipient_name from 'Original Company' to 'Updated Company'",
                created_at: new Date().toISOString(),
                created_by: "test-user-id",
              },
              {
                id: "test-history-id-2",
                invoice_id: "test-invoice-id",
                change_description:
                  "Updated payment_status from 'PENDING' to 'PAID'",
                created_at: new Date().toISOString(),
                created_by: "test-user-id",
              },
            ],
            pagination: {
              page: 1,
              pageSize: 10,
              totalItems: 2,
              totalPages: 1,
            },
          },
          error: null,
        })
      ),
    },
  };
});

describe("Invoice History Tracking Tests", () => {
  it("should detect changes between original and updated invoice", () => {
    // Create a sample original invoice
    const originalInvoice = {
      id: "test-invoice-id",
      invoice_number: "001/TEST/2025",
      recipient_name: "Original Company",
      notes: "Original notes",
      // other fields...
    } as Invoice;

    // Create update data
    const updateData = {
      recipient_name: "Updated Company",
      notes: "Updated notes",
    } as UpdateInvoiceDto;

    // Call the compareInvoiceChanges method
    const changes = InvoiceUpdateHistoryService.compareInvoiceChanges(
      originalInvoice,
      updateData
    );

    // Verify changes were detected
    expect(changes).not.toBeNull();
    expect(changes?.length).toBe(2);

    // Check recipient_name change
    const nameChange = changes?.find(
      (change) => change.field === "recipient_name"
    );
    expect(nameChange).toBeDefined();
    expect(nameChange?.from_value).toBe("Original Company");
    expect(nameChange?.to_value).toBe("Updated Company");

    // Check notes change
    const notesChange = changes?.find((change) => change.field === "notes");
    expect(notesChange).toBeDefined();
    expect(notesChange?.from_value).toBe("Original notes");
    expect(notesChange?.to_value).toBe("Updated notes");
  });

  it("should return null if no changes are detected", () => {
    // Create a sample original invoice
    const originalInvoice = {
      id: "test-invoice-id",
      invoice_number: "001/TEST/2025",
      recipient_name: "Test Company",
      notes: "Test notes",
      // other fields...
    } as Invoice;

    // Create update data with the same values
    const updateData = {
      recipient_name: "Test Company",
      notes: "Test notes",
    } as UpdateInvoiceDto;

    // Call the compareInvoiceChanges method
    const changes = InvoiceUpdateHistoryService.compareInvoiceChanges(
      originalInvoice,
      updateData
    );

    // Verify no changes were detected
    expect(changes).toBeNull();
  });

  it("should ignore items array in change detection", () => {
    // Create a sample original invoice
    const originalInvoice = {
      id: "test-invoice-id",
      invoice_number: "001/TEST/2025",
      recipient_name: "Test Company",
      // other fields...
    } as Invoice;

    // Create update data with only items
    const updateData = {
      items: [{ item_name: "New Item", item_amount: 1, item_price: 100 }],
    } as UpdateInvoiceDto;

    // Call the compareInvoiceChanges method
    const changes = InvoiceUpdateHistoryService.compareInvoiceChanges(
      originalInvoice,
      updateData
    );

    // Verify no changes were detected (items are ignored)
    expect(changes).toBeNull();
  });

  // Now we can implement this test with our mocking setup
  it("should handle error in trackInvoiceUpdate method", async () => {
    // Save the original method
    const originalCompareInvoiceChanges =
      InvoiceUpdateHistoryService.compareInvoiceChanges;

    // Override the method to throw an error
    InvoiceUpdateHistoryService.compareInvoiceChanges = () => {
      throw new Error("Test error");
    };

    // Create test data
    const originalInvoice = {
      id: "test-invoice-id",
      invoice_number: "001/TEST/2025",
      recipient_name: "Original Company",
    } as Invoice;

    const updateData = {
      recipient_name: "Updated Company",
    } as UpdateInvoiceDto;

    // Call the method
    const result = await InvoiceUpdateHistoryService.trackInvoiceUpdate(
      "test-invoice-id",
      originalInvoice,
      updateData,
      "test-user-id"
    );

    // Verify error handling
    expect(result.data).toBeNull();
    expect(result.error).toBeDefined();
    expect(result.error?.message).toContain("Test error");

    // Restore the original method
    InvoiceUpdateHistoryService.compareInvoiceChanges =
      originalCompareInvoiceChanges;
  });

  it("should create a history record", async () => {
    // Create test data
    const historyData: CreateInvoiceUpdateHistoryDto = {
      invoice_id: "test-invoice-id",
      change_description:
        "Updated recipient_name from 'Original Company' to 'Updated Company'",
    };

    // Call the method
    const result = await InvoiceUpdateHistoryService.create(
      historyData,
      "test-user-id"
    );

    // Verify result
    expect(result.data).toBeDefined();
    expect(result.error).toBeNull();
    expect(result.data?.invoice_id).toBe("test-invoice-id");
    expect(result.data?.change_description).toContain("Updated recipient_name");
  });

  it("should get history records for an invoice", async () => {
    // Call the method
    const result = await InvoiceUpdateHistoryService.getByInvoiceId(
      "test-invoice-id"
    );

    // Verify result
    expect(result.data).toBeDefined();
    expect(result.error).toBeNull();
    expect(result.data?.items).toBeDefined();
    expect(result.data?.items?.length).toBe(2);
    expect(result.data?.pagination).toBeDefined();
  });

  it("should track invoice updates with changes", async () => {
    // Create test data
    const originalInvoice = {
      id: "test-invoice-id",
      invoice_number: "001/TEST/2025",
      recipient_name: "Original Company",
      notes: "Original notes",
    } as Invoice;

    const updateData = {
      recipient_name: "Updated Company",
      notes: "Updated notes",
    } as UpdateInvoiceDto;

    // Call the method
    const result = await InvoiceUpdateHistoryService.trackInvoiceUpdate(
      "test-invoice-id",
      originalInvoice,
      updateData,
      "test-user-id"
    );

    // Verify result
    expect(result.data).toBeDefined();
    expect(result.error).toBeNull();
    expect(result.data?.invoice_id).toBe("test-invoice-id");
    expect(result.data?.change_description).toBeDefined();
  });

  it("should not create history record when no changes", async () => {
    // Create test data
    const originalInvoice = {
      id: "test-invoice-id",
      invoice_number: "001/TEST/2025",
      recipient_name: "Test Company",
      notes: "Test notes",
    } as Invoice;

    const updateData = {
      recipient_name: "Test Company",
      notes: "Test notes",
    } as UpdateInvoiceDto;

    // Call the method
    const result = await InvoiceUpdateHistoryService.trackInvoiceUpdate(
      "test-invoice-id",
      originalInvoice,
      updateData,
      "test-user-id"
    );

    // Verify result
    expect(result.data).toBeNull();
    expect(result.error).toBeNull();
  });
});
