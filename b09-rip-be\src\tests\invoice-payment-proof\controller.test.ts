import { describe, expect, it, mock } from "bun:test";
import { InvoicePaymentProofController } from "../../modules/invoice-payment-proof/controller";
import { InvoicePaymentProofService } from "../../modules/invoice-payment-proof/service";
import { InvoiceService } from "../../modules/invoice/service";
import { PaymentStatus } from "../../database/models/invoice.model";

// Create custom mock function with proper TypeScript typing
type MockFunction<T extends (...args: any[]) => any> = {
  (...args: Parameters<T>): Promise<ReturnType<T>>;
  mock: { calls: any[][] };
};

function createMockFn<T extends (...args: any[]) => any>(
  implementation?: T
): MockFunction<T> {
  const calls: any[][] = [];
  const fn = ((...args: Parameters<T>) => {
    calls.push(args);
    // Ensure we return a Promise because the service methods return Promises
    const result = implementation?.(...args);
    return result instanceof Promise ? result : Promise.resolve(result);
  }) as MockFunction<T>;

  fn.mock = { calls };
  return fn;
}

// Create a mock context factory
function createMockContext(overrides = {}) {
  return {
    params: { id: "test-invoice-id", proofId: "test-payment-proof-id" },
    body: {
      file: new File(["test content"], "test-file.pdf", {
        type: "application/pdf",
      }),
      notes: "Test notes",
    },
    user: { id: "test-user-id" },
    success: (data: any, message = "") => ({ success: true, data, message }),
    serverError: (message = "", error = null) => ({
      success: false,
      error: { message, code: "SERVER_ERROR", details: error },
    }),
    badRequest: (message = "", code = "BAD_REQUEST") => ({
      success: false,
      error: { message, code },
    }),
    notFound: (message = "", code = "NOT_FOUND") => ({
      success: false,
      error: { message, code },
    }),
    query: {},
    ...overrides,
  };
}

// Mock the InvoicePaymentProofService
mock.module("../../modules/invoice-payment-proof/service", () => {
  return {
    InvoicePaymentProofService: {
      uploadAndCreate: createMockFn(() => ({
        data: {
          id: "test-payment-proof-id",
          invoice_id: "test-invoice-id",
          file_path: "test-invoice-id/test-file.pdf",
          file_name: "test-file.pdf",
          file_type: "application/pdf",
          file_size: 1024,
          notes: "Test notes",
          created_at: new Date().toISOString(),
          created_by: "test-user-id",
        },
        error: null,
      })),
      getByInvoiceId: createMockFn(() => ({
        data: {
          items: [
            {
              id: "test-payment-proof-id",
              invoice_id: "test-invoice-id",
              file_path: "test-invoice-id/test-file.pdf",
              file_name: "test-file.pdf",
              file_type: "application/pdf",
              file_size: 1024,
              notes: "Test notes",
              download_url: "https://test-signed-url.com",
              created_at: new Date().toISOString(),
              created_by: "test-user-id",
            },
          ],
          pagination: {
            page: 1,
            pageSize: 10,
            totalItems: 1,
            totalPages: 1,
          },
        },
        error: null,
      })),
      delete: createMockFn(() => ({
        data: true,
        error: null,
      })),
    },
  };
});

// Mock the InvoiceService
mock.module("../../modules/invoice/service", () => {
  return {
    InvoiceService: {
      getById: createMockFn((id) => {
        if (id === "test-invoice-id") {
          return {
            data: {
              id: "test-invoice-id",
              invoice_number: "001/TEST/2023",
              payment_status: PaymentStatus.PENDING,
            },
            error: null,
          };
        }
        return { data: null, error: null };
      }),
      update: createMockFn(() => ({
        data: {
          id: "test-invoice-id",
          invoice_number: "001/TEST/2023",
          payment_status: PaymentStatus.PAID,
        },
        error: null,
      })),
    },
  };
});

describe("InvoicePaymentProofController", () => {
  // Test upload method
  describe("upload", () => {
    it("should upload a payment proof successfully", async () => {
      const mockContext = createMockContext();

      const result = await InvoicePaymentProofController.upload(mockContext);

      expect(result.success).toBe(true);
      expect(result.data).toBeDefined();
      expect(result.data.id).toBe("test-payment-proof-id");
      expect(result.message).toContain("uploaded successfully");
    });

    it("should return error when no file is provided", async () => {
      const mockContext = createMockContext({
        body: { notes: "Test notes" }, // No file
      });

      const result = await InvoicePaymentProofController.upload(mockContext);

      expect(result.success).toBe(false);
      expect(result.error.code).toBe("FILE_REQUIRED");
    });

    it("should return not found when invoice doesn't exist", async () => {
      // Override the mock for InvoiceService.getById to return null
      const originalGetById = InvoiceService.getById;
      InvoiceService.getById = createMockFn(() => ({
        data: null,
        error: null,
      }));

      const mockContext = createMockContext();

      const result = await InvoicePaymentProofController.upload(mockContext);

      expect(result.success).toBe(false);
      expect(result.error.code).toBe("INVOICE_NOT_FOUND");

      // Restore the original mock
      InvoiceService.getById = originalGetById;
    });

    it("should handle service error when uploading file", async () => {
      // Override the mock for InvoicePaymentProofService.uploadAndCreate to return an error
      const originalUploadAndCreate =
        InvoicePaymentProofService.uploadAndCreate;
      InvoicePaymentProofService.uploadAndCreate = createMockFn(() => ({
        data: null,
        error: new Error("Upload failed"),
      }));

      const mockContext = createMockContext();

      const result = await InvoicePaymentProofController.upload(mockContext);

      expect(result.success).toBe(false);
      expect(result.error.message).toContain("Failed to upload");

      // Restore the original mock
      InvoicePaymentProofService.uploadAndCreate = originalUploadAndCreate;
    });
  });

  // Test getByInvoiceId method
  describe("getByInvoiceId", () => {
    it("should get all payment proofs for an invoice", async () => {
      const mockContext = createMockContext({
        query: { page: "1", pageSize: "10" },
      });

      const result = await InvoicePaymentProofController.getByInvoiceId(
        mockContext
      );

      expect(result.success).toBe(true);
      expect(result.data).toBeDefined();
      expect(result.data.items).toBeDefined();
      expect(result.data.items.length).toBeGreaterThan(0);
      expect(result.data.items[0].download_url).toBeDefined();
    });

    it("should return not found when invoice doesn't exist", async () => {
      // Override the mock for InvoiceService.getById to return null
      const originalGetById = InvoiceService.getById;
      InvoiceService.getById = createMockFn(() => ({
        data: null,
        error: null,
      }));

      const mockContext = createMockContext();

      const result = await InvoicePaymentProofController.getByInvoiceId(
        mockContext
      );

      expect(result.success).toBe(false);
      expect(result.error.code).toBe("INVOICE_NOT_FOUND");

      // Restore the original mock
      InvoiceService.getById = originalGetById;
    });

    it("should handle service error when fetching payment proofs", async () => {
      // Override the mock for InvoicePaymentProofService.getByInvoiceId to return an error
      const originalGetByInvoiceId = InvoicePaymentProofService.getByInvoiceId;
      InvoicePaymentProofService.getByInvoiceId = createMockFn(() => ({
        data: null,
        error: new Error("Fetch failed"),
      }));

      const mockContext = createMockContext();

      const result = await InvoicePaymentProofController.getByInvoiceId(
        mockContext
      );

      expect(result.success).toBe(false);
      expect(result.error.message).toContain("Failed to fetch");

      // Restore the original mock
      InvoicePaymentProofService.getByInvoiceId = originalGetByInvoiceId;
    });
  });

  // Test delete method
  describe("delete", () => {
    it("should delete a payment proof successfully", async () => {
      const mockContext = createMockContext();

      const result = await InvoicePaymentProofController.delete(mockContext);

      expect(result.success).toBe(true);
      expect(result.message).toContain("deleted successfully");
    });

    it("should return not found when invoice doesn't exist", async () => {
      // Override the mock for InvoiceService.getById to return null
      const originalGetById = InvoiceService.getById;
      InvoiceService.getById = createMockFn(() => ({
        data: null,
        error: null,
      }));

      const mockContext = createMockContext();

      const result = await InvoicePaymentProofController.delete(mockContext);

      expect(result.success).toBe(false);
      expect(result.error.code).toBe("INVOICE_NOT_FOUND");

      // Restore the original mock
      InvoiceService.getById = originalGetById;
    });

    it("should handle service error when deleting payment proof", async () => {
      // Override the mock for InvoicePaymentProofService.delete to return an error
      const originalDelete = InvoicePaymentProofService.delete;
      InvoicePaymentProofService.delete = createMockFn(() => ({
        data: null,
        error: new Error("Delete failed"),
      }));

      const mockContext = createMockContext();

      const result = await InvoicePaymentProofController.delete(mockContext);

      expect(result.success).toBe(false);
      expect(result.error.message).toContain("Failed to delete");

      // Restore the original mock
      InvoicePaymentProofService.delete = originalDelete;
    });
  });
});
