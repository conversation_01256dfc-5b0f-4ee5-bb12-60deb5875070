import { describe, expect, it, mock } from "bun:test";
import { InvoicePaymentProofService } from "../../modules/invoice-payment-proof/service";

// Create a custom mock function since there might be TS issues with mock.fn()
function createMockFn<T extends (...args: any[]) => any>(
  implementation?: T
): { (...args: Parameters<T>): Promise<any>; mock: { calls: any[][] } } {
  const calls: any[][] = [];
  const fn = (...args: Parameters<T>) => {
    calls.push(args);
    // Ensure we return a Promise because the service methods return Promises
    const result = implementation?.(...args);
    return result instanceof Promise ? result : Promise.resolve(result);
  };
  fn.mock = { calls };
  return fn;
}

// Mock the Supabase client
mock.module("../../libs/supabase", () => {
  return {
    supabase: {
      storage: {
        from: () => ({
          upload: () => Promise.resolve({ data: {}, error: null }),
          remove: () => Promise.resolve({ error: null }),
          createSignedUrl: () =>
            Promise.resolve({
              data: { signedUrl: "https://test-signed-url.com" },
              error: null,
            }),
        }),
      },
    },
  };
});

// Mock the database utilities
mock.module("../../utils/database", () => {
  return {
    dbUtils: {
      create: createMockFn(() =>
        Promise.resolve({
          data: { id: "test-payment-proof-id" },
          error: null,
        })
      ),
      getById: createMockFn(() =>
        Promise.resolve({
          data: {
            id: "test-payment-proof-id",
            file_path: "test-invoice-id/test-file.pdf",
          },
          error: null,
        })
      ),
      getAll: createMockFn(() =>
        Promise.resolve({
          data: [
            {
              id: "test-payment-proof-id",
              file_path: "test-invoice-id/test-file.pdf",
            },
          ],
          error: null,
        })
      ),
      hardDelete: createMockFn(() =>
        Promise.resolve({ data: true, error: null })
      ),
    },
  };
});

describe("InvoicePaymentProofService", () => {
  // Test uploadAndCreate method
  it("should upload a file and create a payment proof record", async () => {
    // Create a mock file
    const mockFile = new File(["test content"], "test-file.pdf", {
      type: "application/pdf",
    });

    // Call the service method
    const result = await InvoicePaymentProofService.uploadAndCreate(
      mockFile,
      "test-invoice-id",
      "Test notes",
      "test-user-id"
    );

    // Verify the result
    expect(result.data).toBeDefined();
    expect(result.error).toBeNull();
  });

  // Test getByInvoiceId method
  it("should get all payment proofs for an invoice with signed URLs", async () => {
    // Call the service method
    const result = await InvoicePaymentProofService.getByInvoiceId(
      "test-invoice-id"
    );

    // Verify the result
    expect(result.data).toBeDefined();
    expect(Array.isArray(result.data)).toBe(true);
    expect(result.data?.[0]?.download_url).toBe("https://test-signed-url.com");
    expect(result.error).toBeNull();
  });

  // Test delete method
  it("should delete a payment proof (hard delete)", async () => {
    // Call the service method
    const result = await InvoicePaymentProofService.delete(
      "test-payment-proof-id",
      "test-user-id"
    );

    // Verify the result
    expect(result.data).toBeDefined();
    expect(result.data.id).toBe("test-payment-proof-id");
    expect(result.data.file_path).toBe("test-invoice-id/test-file.pdf");
    expect(result.error).toBeNull();
  });
});
