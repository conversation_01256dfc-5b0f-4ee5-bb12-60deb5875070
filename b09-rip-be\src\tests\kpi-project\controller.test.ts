import { describe, expect, it, mock } from "bun:test";

// Mock the supabase module
mock.module("../../libs/supabase", () => {
  return {
    supabase: {
      from: () => ({
        select: () => ({
          eq: () => ({
            single: () => Promise.resolve({ data: null, error: null }),
          }),
        }),
        insert: () => Promise.resolve({ data: null, error: null }),
        update: () => Promise.resolve({ data: null, error: null }),
        delete: () => Promise.resolve({ data: null, error: null }),
      }),
    },
  };
});

import { KpiProjectController } from "../../modules/kpi-project/controller";
import { KpiStatus } from "../../database/models/kpi-project.model";
import { createMockKpiProject, createMockContext } from "./test-utils";

// Helper function to mock the KPI Project service
function mockKpiProjectService() {
  const mockKpiProject = createMockKpiProject();

  // Mock the KpiProjectService methods
  mock.module("../../modules/kpi-project/service", () => {
    return {
      KpiProjectService: class {
        async createKpiProject() {
          return mockKpiProject;
        }

        async checkDuplicateKpiProject() {
          return false;
        }

        async createKpiProjectFromProject(
          projectId: string,
          projectName: string,
          objectives: string,
          startDate: string,
          endDate: string,
          userId?: string
        ) {
          return mockKpiProject;
        }

        async getAllKpiProjects() {
          return {
            data: [mockKpiProject],
            result: {
              page: 1,
              pageSize: 10,
              total: 1,
              pageCount: 1,
            },
          };
        }

        async getKpiProject(id: string) {
          if (id === "test-kpi-id") {
            return mockKpiProject;
          }
          return null;
        }

        async updateKpiProject() {
          return { ...mockKpiProject, description: "Updated description" };
        }

        async deleteKpiProject() {
          return { message: "KPI Project deleted successfully" };
        }
      },
    };
  });

  return mockKpiProject;
}

describe("KpiProjectController", () => {
  // Test create method
  describe("create", () => {
    it("should create a KPI project successfully", async () => {
      // ARRANGE
      const mockKpiProject = mockKpiProjectService();
      const context = createMockContext();

      // ACT
      const result = await KpiProjectController.create(context);

      // ASSERT
      expect(result.success).toBe(true);
      expect(result.data).toBeDefined();
      expect(result.message).toContain("created successfully");
    });

    it("should return bad request when required fields are missing", async () => {
      // ARRANGE
      mockKpiProjectService();
      const context = createMockContext({
        body: {
          // Missing required fields
          project_name: "Test Project",
          // No project_id, description, target, period
        },
      });

      // ACT
      const result = await KpiProjectController.create(context);

      // ASSERT
      expect(result.success).toBe(false);
      expect(result.error).toBeDefined();
      expect(result.error.code).toBe("MISSING_FIELDS");
    });

    it("should return server error when service throws an error", async () => {
      // ARRANGE
      mock.module("../../modules/kpi-project/service", () => {
        return {
          KpiProjectService: class {
            async checkDuplicateKpiProject() {
              throw new Error("Service error");
            }
            async createKpiProject() {
              throw new Error("Service error");
            }
          },
        };
      });

      const context = createMockContext();

      // ACT
      const result = await KpiProjectController.create(context);

      // ASSERT
      expect(result.success).toBe(false);
      expect(result.error).toBeDefined();
      expect(result.error.message).toContain("Service error");
    });
  });

  // Test createFromProject method
  describe("createFromProject", () => {
    it("should create a KPI project from project data successfully", async () => {
      // ARRANGE
      const mockKpiProject = mockKpiProjectService();
      const context = createMockContext({
        body: {
          project_id: "test-project-id",
          project_name: "Test Project",
          objectives: "Test objectives",
          start_date: "2023-01-01",
          end_date: "2023-12-31",
        },
      });

      // ACT
      const result = await KpiProjectController.createFromProject(context);

      // ASSERT
      expect(result.success).toBe(true);
      expect(result.data).toBeDefined();
      expect(result.message).toContain("automatically created");
    });

    it("should return bad request when required fields are missing", async () => {
      // ARRANGE
      mockKpiProjectService();
      const context = createMockContext({
        body: {
          // Missing required fields
          project_id: "test-project-id",
          // No project_name, objectives, start_date, end_date
        },
      });

      // ACT
      const result = await KpiProjectController.createFromProject(context);

      // ASSERT
      expect(result.success).toBe(false);
      expect(result.error).toBeDefined();
      expect(result.error.code).toBe("MISSING_FIELDS");
    });

    it("should return server error when service throws an error", async () => {
      // ARRANGE
      mock.module("../../modules/kpi-project/service", () => {
        return {
          KpiProjectService: class {
            async createKpiProjectFromProject(
              projectId: string,
              projectName: string,
              objectives: string,
              startDate: string,
              endDate: string,
              userId?: string
            ) {
              throw new Error("Service error");
            }
          },
        };
      });

      const context = createMockContext({
        body: {
          project_id: "test-project-id",
          project_name: "Test Project",
          objectives: "Test objectives",
          start_date: "2023-01-01",
          end_date: "2023-12-31",
        },
      });

      // ACT
      const result = await KpiProjectController.createFromProject(context);

      // ASSERT
      expect(result.success).toBe(false);
      expect(result.error).toBeDefined();
      expect(result.error.message).toContain("Service error");
    });
  });

  // Test getAll method
  describe("getAll", () => {
    it("should get all KPI projects successfully", async () => {
      // ARRANGE
      const mockKpiProject = mockKpiProjectService();
      const context = createMockContext();

      // ACT
      const result = await KpiProjectController.getAll(context);

      // ASSERT
      expect(result.success).toBe(true);
      expect(result.data).toBeDefined();
      expect(result.data.data).toBeInstanceOf(Array);
      expect(result.message).toContain("retrieved successfully");
    });

    it("should handle query parameters", async () => {
      // ARRANGE
      const mockKpiProject = mockKpiProjectService();
      const context = createMockContext({
        query: {
          project_name: "Test",
          limit: 20,
          offset: 0,
        },
      });

      // ACT
      const result = await KpiProjectController.getAll(context);

      // ASSERT
      expect(result.success).toBe(true);
      expect(result.data).toBeDefined();
    });

    it("should return server error when service throws an error", async () => {
      // ARRANGE
      mock.module("../../modules/kpi-project/service", () => {
        return {
          KpiProjectService: class {
            async getAllKpiProjects() {
              throw new Error("Service error");
            }
          },
        };
      });

      const context = createMockContext();

      // ACT
      const result = await KpiProjectController.getAll(context);

      // ASSERT
      expect(result.success).toBe(false);
      expect(result.error).toBeDefined();
      expect(result.error.message).toContain("Service error");
    });
  });

  // Test getById method
  describe("getById", () => {
    it("should get a KPI project by ID successfully", async () => {
      // ARRANGE
      const mockKpiProject = mockKpiProjectService();
      const context = createMockContext();

      // ACT
      const result = await KpiProjectController.getById(context);

      // ASSERT
      expect(result.success).toBe(true);
      expect(result.data).toBeDefined();
      expect(result.data.id).toBe("test-kpi-id");
      expect(result.message).toContain("retrieved successfully");
    });

    it("should return not found when KPI project doesn't exist", async () => {
      // ARRANGE
      mockKpiProjectService();
      const context = createMockContext({
        params: { id: "non-existent-id" },
      });

      // ACT
      const result = await KpiProjectController.getById(context);

      // ASSERT
      expect(result.success).toBe(false);
      expect(result.error).toBeDefined();
      expect(result.error.code).toBe("NOT_FOUND");
    });

    it("should return server error when service throws an error", async () => {
      // ARRANGE
      mock.module("../../modules/kpi-project/service", () => {
        return {
          KpiProjectService: class {
            async getKpiProject() {
              throw new Error("Service error");
            }
          },
        };
      });

      const context = createMockContext();

      // ACT
      const result = await KpiProjectController.getById(context);

      // ASSERT
      expect(result.success).toBe(false);
      expect(result.error).toBeDefined();
      expect(result.error.message).toContain("Service error");
    });
  });

  // Test update method
  describe("update", () => {
    it("should update a KPI project successfully", async () => {
      // ARRANGE
      const mockKpiProject = mockKpiProjectService();
      const context = createMockContext({
        body: {
          description: "Updated description",
          target: "Updated target",
        },
      });

      // ACT
      const result = await KpiProjectController.update(context);

      // ASSERT
      expect(result.success).toBe(true);
      expect(result.data).toBeDefined();
      expect(result.message).toContain("updated successfully");
    });

    it("should return bad request when no valid fields to update", async () => {
      // ARRANGE
      mockKpiProjectService();
      const context = createMockContext({
        body: {
          // No valid fields to update
        },
      });

      // ACT
      const result = await KpiProjectController.update(context);

      // ASSERT
      expect(result.success).toBe(false);
      expect(result.error).toBeDefined();
      expect(result.error.code).toBe("NO_VALID_FIELDS");
    });

    it("should return server error when service throws an error", async () => {
      // ARRANGE
      mock.module("../../modules/kpi-project/service", () => {
        return {
          KpiProjectService: class {
            async getKpiProject() {
              throw new Error("Service error");
            }
            async updateKpiProject() {
              throw new Error("Service error");
            }
          },
        };
      });

      const context = createMockContext({
        body: {
          project_name: "Updated name",
          target: "Updated target",
          status: KpiStatus.IN_PROGRESS,
        },
      });

      // ACT
      const result = await KpiProjectController.update(context);

      // ASSERT
      expect(result.success).toBe(false);
      expect(result.error).toBeDefined();
      expect(result.error.message).toContain("Service error");
    });
  });

  // Test delete method
  describe("delete", () => {
    it("should delete a KPI project successfully", async () => {
      // ARRANGE
      const mockKpiProject = mockKpiProjectService();
      const context = createMockContext();

      // ACT
      const result = await KpiProjectController.delete(context);

      // ASSERT
      expect(result.success).toBe(true);
      expect(result.message).toContain("deleted successfully");
    });

    it("should return server error when service throws an error", async () => {
      // ARRANGE
      mock.module("../../modules/kpi-project/service", () => {
        return {
          KpiProjectService: class {
            async deleteKpiProject() {
              throw new Error("Service error");
            }
          },
        };
      });

      const context = createMockContext();

      // ACT
      const result = await KpiProjectController.delete(context);

      // ASSERT
      expect(result.success).toBe(false);
      expect(result.error).toBeDefined();
      expect(result.error.message).toContain("Service error");
    });
  });
});
