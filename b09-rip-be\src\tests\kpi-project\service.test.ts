import { describe, expect, it, beforeEach, afterEach } from "bun:test";
import { KpiProjectService } from "../../modules/kpi-project/service";
import { KpiStatus } from "../../database/models/kpi-project.model";
import { createMockKpiProject, DatabaseError } from "./test-utils";

describe("KpiProjectService", () => {
  // Store original methods to restore after tests
  let originalCreateKpiProject: typeof KpiProjectService.prototype.createKpiProject;
  let originalCreateKpiProjectFromProject: typeof KpiProjectService.prototype.createKpiProjectFromProject;
  let originalGetAllKpiProjects: typeof KpiProjectService.prototype.getAllKpiProjects;
  let originalGetKpiProject: typeof KpiProjectService.prototype.getKpiProject;
  let originalUpdateKpiProject: typeof KpiProjectService.prototype.updateKpiProject;
  let originalDeleteKpiProject: typeof KpiProjectService.prototype.deleteKpiProject;
  let originalCheckDuplicateKpiProject: typeof KpiProjectService.prototype.checkDuplicateKpiProject;
  let originalGetKpiProjectsByProjectId: typeof KpiProjectService.prototype.getKpiProjectsByProjectId;

  beforeEach(() => {
    // Store original methods
    originalCreateKpiProject = KpiProjectService.prototype.createKpiProject;
    originalCreateKpiProjectFromProject = KpiProjectService.prototype.createKpiProjectFromProject;
    originalGetAllKpiProjects = KpiProjectService.prototype.getAllKpiProjects;
    originalGetKpiProject = KpiProjectService.prototype.getKpiProject;
    originalUpdateKpiProject = KpiProjectService.prototype.updateKpiProject;
    originalDeleteKpiProject = KpiProjectService.prototype.deleteKpiProject;
    originalCheckDuplicateKpiProject = KpiProjectService.prototype.checkDuplicateKpiProject;
    originalGetKpiProjectsByProjectId = KpiProjectService.prototype.getKpiProjectsByProjectId;
  });

  afterEach(() => {
    // Restore original methods
    KpiProjectService.prototype.createKpiProject = originalCreateKpiProject;
    KpiProjectService.prototype.createKpiProjectFromProject = originalCreateKpiProjectFromProject;
    KpiProjectService.prototype.getAllKpiProjects = originalGetAllKpiProjects;
    KpiProjectService.prototype.getKpiProject = originalGetKpiProject;
    KpiProjectService.prototype.updateKpiProject = originalUpdateKpiProject;
    KpiProjectService.prototype.deleteKpiProject = originalDeleteKpiProject;
    KpiProjectService.prototype.checkDuplicateKpiProject = originalCheckDuplicateKpiProject;
    KpiProjectService.prototype.getKpiProjectsByProjectId = originalGetKpiProjectsByProjectId;
  });

  // Test createKpiProject method
  describe("createKpiProject", () => {
    it("should create a KPI project successfully", async () => {
      // ARRANGE
      const mockKpiProject = createMockKpiProject();
      KpiProjectService.prototype.createKpiProject = async () => mockKpiProject;

      const kpiProjectData = {
        project_name: "Test Project",
        project_id: "test-project-id",
        description: "Test KPI project description",
        target: "Complete 100% of tasks",
        period: "2023-Q1",
        status: KpiStatus.NOT_STARTED,
        additional_notes: "Additional test notes",
      };

      // ACT
      const kpiProjectService = new KpiProjectService();
      const result = await kpiProjectService.createKpiProject(kpiProjectData, "test-user-id");

      // ASSERT
      expect(result).toBeDefined();
      expect(result.project_name).toBe("Test Project");
      expect(result.project_id).toBe("test-project-id");
    });

    it("should handle database errors during creation", async () => {
      // ARRANGE
      KpiProjectService.prototype.createKpiProject = async () => {
        throw new Error("Database connection error");
      };

      const kpiProjectData = {
        project_name: "Test Project",
        project_id: "test-project-id",
        description: "Test KPI project description",
        target: "Complete 100% of tasks",
        period: "2023-Q1",
      };

      // ACT & ASSERT
      const kpiProjectService = new KpiProjectService();
      await expect(kpiProjectService.createKpiProject(kpiProjectData, "test-user-id"))
        .rejects.toThrow("Database connection error");
    });
  });

  // Test createKpiProjectFromProject method
  describe("createKpiProjectFromProject", () => {
    it("should create a KPI project from project data successfully", async () => {
      // ARRANGE
      const mockKpiProject = createMockKpiProject();
      KpiProjectService.prototype.checkDuplicateKpiProject = async () => false;
      KpiProjectService.prototype.createKpiProjectFromProject = async () => mockKpiProject;

      const projectData = {
        project_id: "test-project-id",
        project_name: "Test Project",
        objectives: "Test objectives",
        start_date: "2023-01-01",
        end_date: "2023-12-31",
      };

      // ACT
      const kpiProjectService = new KpiProjectService();
      const result = await kpiProjectService.createKpiProjectFromProject(
        projectData.project_id,
        projectData.project_name,
        projectData.objectives,
        projectData.start_date,
        projectData.end_date,
        "test-user-id"
      );

      // ASSERT
      expect(result).toBeDefined();
      expect(result.project_name).toBe("Test Project");
      expect(result.project_id).toBe("test-project-id");
    });

    it("should handle duplicate KPI projects", async () => {
      // ARRANGE
      KpiProjectService.prototype.checkDuplicateKpiProject = async () => true;
      KpiProjectService.prototype.createKpiProjectFromProject = async (
        projectId: string,
        projectName: string,
        objectives: string,
        startDate: string,
        endDate: string,
        userId?: string
      ) => {
        throw new Error("A KPI Project already exists for project");
      };

      const projectData = {
        project_id: "test-project-id",
        project_name: "Test Project",
        objectives: "Test objectives",
        start_date: "2023-01-01",
        end_date: "2023-12-31",
      };

      // ACT & ASSERT
      const kpiProjectService = new KpiProjectService();
      await expect(kpiProjectService.createKpiProjectFromProject(
        projectData.project_id,
        projectData.project_name,
        projectData.objectives,
        projectData.start_date,
        projectData.end_date,
        "test-user-id"
      )).rejects.toThrow("A KPI Project already exists for project");
    });
  });

  // Test getAllKpiProjects method
  describe("getAllKpiProjects", () => {
    it("should get all KPI projects successfully", async () => {
      // ARRANGE
      const mockKpiProject = createMockKpiProject();
      KpiProjectService.prototype.getAllKpiProjects = async () => {
        return {
          data: [mockKpiProject],
          result: {
            page: 1,
            pageSize: 10,
            total: 1,
            pageCount: 1,
          },
        };
      };

      // ACT
      const kpiProjectService = new KpiProjectService();
      const result = await kpiProjectService.getAllKpiProjects();

      // ASSERT
      expect(result).toBeDefined();
      expect(result.data).toHaveLength(1);
      expect(result.data[0].project_name).toBe("Test Project");
      expect(result.result.total).toBe(1);
    });

    it("should handle database errors", async () => {
      // ARRANGE
      KpiProjectService.prototype.getAllKpiProjects = async () => {
        throw new Error("Database error");
      };

      // ACT & ASSERT
      const kpiProjectService = new KpiProjectService();
      await expect(kpiProjectService.getAllKpiProjects())
        .rejects.toThrow("Database error");
    });
  });

  // Test getKpiProject method
  describe("getKpiProject", () => {
    it("should get a KPI project by ID successfully", async () => {
      // ARRANGE
      const mockKpiProject = createMockKpiProject();
      KpiProjectService.prototype.getKpiProject = async () => mockKpiProject;

      // ACT
      const kpiProjectService = new KpiProjectService();
      const result = await kpiProjectService.getKpiProject("test-kpi-id");

      // ASSERT
      expect(result).toBeDefined();
      expect(result.id).toBe("test-kpi-id");
      expect(result.project_name).toBe("Test Project");
    });

    it("should return null when KPI project doesn't exist", async () => {
      // ARRANGE
      KpiProjectService.prototype.getKpiProject = async () => null;

      // ACT
      const kpiProjectService = new KpiProjectService();
      const result = await kpiProjectService.getKpiProject("non-existent-id");

      // ASSERT
      expect(result).toBeNull();
    });

    it("should handle database errors", async () => {
      // ARRANGE
      KpiProjectService.prototype.getKpiProject = async () => {
        throw new Error("Database error");
      };

      // ACT & ASSERT
      const kpiProjectService = new KpiProjectService();
      await expect(kpiProjectService.getKpiProject("test-kpi-id"))
        .rejects.toThrow("Database error");
    });
  });

  // Test updateKpiProject method
  describe("updateKpiProject", () => {
    it("should update a KPI project successfully", async () => {
      // ARRANGE
      const mockKpiProject = createMockKpiProject();
      const updatedKpiProject = createMockKpiProject({
        description: "Updated description",
        target: "Updated target",
      });

      KpiProjectService.prototype.getKpiProject = async () => mockKpiProject;
      KpiProjectService.prototype.updateKpiProject = async () => updatedKpiProject;

      const updateData = {
        description: "Updated description",
        target: "Updated target",
      };

      // ACT
      const kpiProjectService = new KpiProjectService();
      const result = await kpiProjectService.updateKpiProject(
        "test-kpi-id",
        updateData,
        "test-user-id"
      );

      // ASSERT
      expect(result).toBeDefined();
      expect(result.description).toBe("Updated description");
      expect(result.target).toBe("Updated target");
    });

    it("should throw error when KPI project doesn't exist", async () => {
      // ARRANGE
      KpiProjectService.prototype.getKpiProject = async () => null;
      KpiProjectService.prototype.updateKpiProject = async (
        id: string,
        data: any,
        userId?: string
      ) => {
        throw new Error("KPI Project not found");
      };

      const updateData = {
        description: "Updated description",
      };

      // ACT & ASSERT
      const kpiProjectService = new KpiProjectService();
      await expect(kpiProjectService.updateKpiProject(
        "non-existent-id",
        updateData,
        "test-user-id"
      )).rejects.toThrow("KPI Project not found");
    });

    it("should handle database errors during update", async () => {
      // ARRANGE
      const mockKpiProject = createMockKpiProject();
      
      KpiProjectService.prototype.getKpiProject = async () => mockKpiProject;
      KpiProjectService.prototype.updateKpiProject = async () => {
        throw new Error("Database error during update");
      };

      const updateData = {
        description: "Updated description",
      };

      // ACT & ASSERT
      const kpiProjectService = new KpiProjectService();
      await expect(kpiProjectService.updateKpiProject(
        "test-kpi-id",
        updateData,
        "test-user-id"
      )).rejects.toThrow("Database error during update");
    });
  });

  // Test deleteKpiProject method
  describe("deleteKpiProject", () => {
    it("should delete a KPI project successfully", async () => {
      // ARRANGE
      const mockKpiProject = createMockKpiProject();
      
      KpiProjectService.prototype.getKpiProject = async () => mockKpiProject;
      KpiProjectService.prototype.deleteKpiProject = async () => true;

      // ACT
      const kpiProjectService = new KpiProjectService();
      const result = await kpiProjectService.deleteKpiProject("test-kpi-id", "test-user-id");

      // ASSERT
      expect(result).toBe(true);
    });

    it("should throw error when KPI project doesn't exist", async () => {
      // ARRANGE
      KpiProjectService.prototype.getKpiProject = async () => null;
      KpiProjectService.prototype.deleteKpiProject = async (
        id: string,
        userId?: string
      ) => {
        throw new Error("KPI Project not found");
      };

      // ACT & ASSERT
      const kpiProjectService = new KpiProjectService();
      await expect(kpiProjectService.deleteKpiProject(
        "non-existent-id",
        "test-user-id"
      )).rejects.toThrow("KPI Project not found");
    });

    it("should handle database errors during deletion", async () => {
      // ARRANGE
      const mockKpiProject = createMockKpiProject();
      
      KpiProjectService.prototype.getKpiProject = async () => mockKpiProject;
      KpiProjectService.prototype.deleteKpiProject = async () => {
        throw new Error("Database error during deletion");
      };

      // ACT & ASSERT
      const kpiProjectService = new KpiProjectService();
      await expect(kpiProjectService.deleteKpiProject(
        "test-kpi-id",
        "test-user-id"
      )).rejects.toThrow("Database error during deletion");
    });
  });

  // Test checkDuplicateKpiProject method
  describe("checkDuplicateKpiProject", () => {
    it("should return true when duplicate exists", async () => {
      // ARRANGE
      const mockKpiProject = createMockKpiProject();
      
      KpiProjectService.prototype.getKpiProjectsByProjectId = async () => [mockKpiProject];
      KpiProjectService.prototype.checkDuplicateKpiProject = async () => true;

      // ACT
      const kpiProjectService = new KpiProjectService();
      const result = await kpiProjectService.checkDuplicateKpiProject("test-project-id");

      // ASSERT
      expect(result).toBe(true);
    });

    it("should return false when no duplicate exists", async () => {
      // ARRANGE
      KpiProjectService.prototype.getKpiProjectsByProjectId = async () => [];
      KpiProjectService.prototype.checkDuplicateKpiProject = async () => false;

      // ACT
      const kpiProjectService = new KpiProjectService();
      const result = await kpiProjectService.checkDuplicateKpiProject("test-project-id");

      // ASSERT
      expect(result).toBe(false);
    });
  });
});
