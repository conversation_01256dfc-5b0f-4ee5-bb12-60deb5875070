import { describe, expect, it } from "bun:test";
import { KpiStatus } from "../../database/models/kpi-project.model";

describe("KPI Project Module", () => {
  it("should have the correct KPI status enum values", () => {
    expect(KpiStatus.NOT_STARTED).toBe("not_started");
    expect(KpiStatus.IN_PROGRESS).toBe("in_progress");
    expect(KpiStatus.COMPLETED_BELOW_TARGET).toBe("completed_below_target");
    expect(KpiStatus.COMPLETED_ON_TARGET).toBe("completed_on_target");
    expect(KpiStatus.COMPLETED_ABOVE_TARGET).toBe("completed_above_target");
  });

  it("should pass a simple test", () => {
    expect(1 + 1).toBe(2);
  });
});
