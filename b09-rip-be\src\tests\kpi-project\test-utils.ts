import { KpiProject, KpiStatus } from "../../database/models/kpi-project.model";

/**
 * Custom error class with code property for testing
 */
export class DatabaseError extends Error {
  code?: string;

  constructor(message: string, code?: string) {
    super(message);
    this.code = code;
    this.name = "DatabaseError";
  }
}

/**
 * Creates a mock KPI project for testing
 * @param overrides Optional properties to override in the mock KPI project
 * @returns A mock KPI project
 */
export function createMockKpiProject(overrides = {}): KpiProject {
  return {
    id: "test-kpi-id",
    project_name: "Test Project",
    project_id: "test-project-id",
    description: "Test KPI project description",
    target: "Complete 100% of tasks",
    period: "2023-Q1",
    status: KpiStatus.NOT_STARTED,
    additional_notes: "Additional test notes",
    created_at: new Date().toISOString(),
    created_by: "test-user-id",
    updated_at: null,
    updated_by: null,
    deleted_at: null,
    deleted_by: null,
    ...overrides,
  };
}

/**
 * Creates a mock function with call tracking
 * @param implementation Optional implementation function
 * @returns A mock function with call tracking
 */
export function createMockFn<T extends (...args: any[]) => any>(
  implementation?: T
): { (...args: Parameters<T>): Promise<any>; mock: { calls: any[][] } } {
  const calls: any[][] = [];
  const fn = (...args: Parameters<T>) => {
    calls.push(args);
    // Ensure we return a Promise because the service methods return Promises
    const result = implementation?.(...args);
    return result instanceof Promise ? result : Promise.resolve(result);
  };
  fn.mock = { calls };
  return fn;
}

/**
 * Creates a mock context for controller tests
 * @param overrides Properties to override in the default mock
 * @returns A mock context object
 */
export function createMockContext(overrides = {}) {
  return {
    params: { id: "test-kpi-id" },
    query: {},
    body: {
      project_name: "Test Project",
      project_id: "test-project-id",
      description: "Test KPI project description",
      target: "Complete 100% of tasks",
      period: "2023-Q1",
      status: KpiStatus.NOT_STARTED,
      additional_notes: "Additional test notes",
    },
    user: { id: "test-user-id", profile: { role: "Manager" } },
    success: (data: any, message = "Success") => ({
      success: true,
      data,
      message,
    }),
    badRequest: (message: string, code = "MISSING_FIELDS") => ({
      success: false,
      error: { message, code },
    }),
    notFound: (message = "Not found") => ({
      success: false,
      error: { message, code: "NOT_FOUND" },
    }),
    serverError: (message = "Server error", error = null) => ({
      success: false,
      error: { message, code: "SERVER_ERROR", details: error },
    }),
    ...overrides,
  };
}
