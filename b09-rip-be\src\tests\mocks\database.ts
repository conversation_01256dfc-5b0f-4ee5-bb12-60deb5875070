// src/tests/mocks/database.ts
import { mock } from "bun:test";
import { TaskStatus } from "../../database/models/task.model";
import { ProjectTask } from "../../database/models/project-task.model";

// Sample project task data for testing
const mockProjectTask: ProjectTask = {
  id: "test-task-id",
  assigned_by: "test-user-id",
  description: "Test project task",
  completion_status: TaskStatus.NOT_COMPLETED,
  employee_id: "test-employee-id",
  initial_date: "2023-01-01",
  due_date: "2023-01-31",
  project_id: "test-project-id",
  weekly_log_id: "test-weekly-log-id",
  created_at: new Date().toISOString(),
  created_by: "test-user-id",
  updated_at: null,
  updated_by: null,
  deleted_at: null,
  deleted_by: null,
};

// Mock the database utils module
mock.module("../../utils/database", () => {
  return {
    dbUtils: {
      create: async (table: string, data: any, userId?: string) => {
        return { data: mockProjectTask, error: null };
      },
      getById: async (table: string, id: string) => {
        if (id === "test-task-id") {
          return { data: mockProjectTask, error: null };
        }
        return { data: null, error: null };
      },
      getAll: async (table: string, options: any = {}) => {
        return {
          data: [mockProjectTask],
          error: null,
          result: {
            total: 1,
            page: 1,
            pageSize: 10,
            pageCount: 1,
          },
        };
      },
      update: async (table: string, id: string, data: any, userId?: string) => {
        return {
          data: { ...mockProjectTask, ...data, updated_by: userId, updated_at: new Date().toISOString() },
          error: null,
        };
      },
      softDelete: async (table: string, id: string, userId?: string) => {
        return {
          data: { ...mockProjectTask, deleted_by: userId, deleted_at: new Date().toISOString() },
          error: null,
        };
      },
      query: (table: string) => ({
        getSoftDeleted: async () => ({ data: [], error: null }),
        restore: async (id: string, userId?: string) => ({ data: mockProjectTask, error: null }),
        raw: {},
      }),
    },
  };
});
