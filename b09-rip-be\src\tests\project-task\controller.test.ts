import { describe, expect, it, mock, beforeAll } from "bun:test";

// Mock the supabase module
mock.module("../../libs/supabase", () => {
  return {
    supabase: {
      from: () => ({
        select: () => ({
          eq: () => ({
            single: () => Promise.resolve({ data: null, error: null }),
          }),
        }),
        insert: () => Promise.resolve({ data: null, error: null }),
        update: () => Promise.resolve({ data: null, error: null }),
        delete: () => Promise.resolve({ data: null, error: null }),
      }),
    },
  };
});
import { ProjectTaskController } from "../../modules/project-task/controller";
import { IProjectTaskService } from "../../modules/project-task/service.interface";
import { TaskStatus } from "../../database/models/task.model";
import { ProjectTask } from "../../database/models/project-task.model";

// Create custom mock function with proper TypeScript typing
type MockFunction<T extends (...args: any[]) => any> = {
  (...args: Parameters<T>): Promise<ReturnType<T>>;
  mock: { calls: any[][] };
};

function createMockFn<T extends (...args: any[]) => any>(
  implementation?: T
): MockFunction<T> {
  const calls: any[][] = [];
  const fn = ((...args: Parameters<T>) => {
    calls.push(args);
    // Ensure we return a Promise because the service methods return Promises
    const result = implementation?.(...args);
    return result instanceof Promise ? result : Promise.resolve(result);
  }) as MockFunction<T>;

  fn.mock = { calls };
  return fn;
}

// Sample project task data for testing
const mockProjectTask: ProjectTask = {
  id: "test-task-id",
  assigned_by: "test-user-id",
  description: "Test project task",
  completion_status: TaskStatus.NOT_COMPLETED,
  employee_id: "test-employee-id",
  initial_date: "2023-01-01",
  due_date: "2023-01-31",
  project_id: "test-project-id",
  weekly_log_id: "test-weekly-log-id",
  created_at: new Date().toISOString(),
  created_by: "test-user-id",
  updated_at: null,
  updated_by: null,
  deleted_at: null,
  deleted_by: null,
};

// Create a mock context factory
function createMockContext(overrides = {}) {
  return {
    params: { id: "test-task-id" },
    body: {
      description: "Test project task",
      assigned_by: "test-user-id",
      employee_id: "test-employee-id",
      initial_date: "2023-01-01",
      due_date: "2023-01-31",
      project_id: "test-project-id",
      completion_status: TaskStatus.NOT_COMPLETED,
    },
    query: {},
    user: { id: "test-user-id" },
    profile: { role: "admin" },
    success: (data: any, message: string) => ({ success: true, data, message }),
    serverError: (message: string, error?: any) => ({
      success: false,
      error: { message, code: "SERVER_ERROR", details: error },
    }),
    notFound: (message: string, code?: string) => ({
      success: false,
      error: { message, code: code || "NOT_FOUND" },
    }),
    badRequest: (message: string, code?: string) => ({
      success: false,
      error: { message, code: code || "BAD_REQUEST" },
    }),
    ...overrides,
  };
}

// Create a mock service
const mockProjectTaskService: IProjectTaskService = {
  create: createMockFn(() =>
    Promise.resolve({
      data: mockProjectTask,
      error: null,
    })
  ),
  updateStatus: createMockFn(() =>
    Promise.resolve({
      data: { ...mockProjectTask, completion_status: TaskStatus.COMPLETED },
      error: null,
    })
  ),
  getById: createMockFn((id: string) => {
    if (id === "test-task-id") {
      return Promise.resolve({
        data: mockProjectTask,
        error: null,
      });
    }
    return Promise.resolve({
      data: null,
      error: { message: "Project task not found" },
    });
  }),
  getAll: createMockFn(() =>
    Promise.resolve({
      data: {
        items: [mockProjectTask],
      },
      pagination: {
        page: 1,
        pageSize: 10,
        totalItems: 1,
        totalPages: 1,
      },
      error: null,
    })
  ),
  getByProjectId: createMockFn(() =>
    Promise.resolve({
      data: {
        items: [mockProjectTask],
      },
      pagination: {
        page: 1,
        pageSize: 10,
        totalItems: 1,
        totalPages: 1,
      },
      error: null,
    })
  ),
  update: createMockFn(() =>
    Promise.resolve({
      data: { ...mockProjectTask, description: "Updated description" },
      error: null,
    })
  ),
  delete: createMockFn(() =>
    Promise.resolve({
      data: { ...mockProjectTask, deleted_at: new Date().toISOString() },
      error: null,
    })
  ),
};

describe("ProjectTaskController", () => {
  // Create a controller instance with the mock service
  const controller = new ProjectTaskController(mockProjectTaskService);

  describe("create", () => {
    it("should create a project task successfully", async () => {
      const context = createMockContext();
      const result = await controller.create(context);

      expect(result.success).toBe(true);
      expect(result.data).toBeDefined();
      expect(result.message).toContain("created successfully");
    });

    it("should return bad request when required fields are missing", async () => {
      const context = createMockContext({
        body: {
          // Missing required fields
          description: "Test project task",
          // No assigned_by, employee_id, etc.
        },
      });

      const result = await controller.create(context);

      expect(result.success).toBe(false);
      expect(result.error).toBeDefined();
      expect(result.error.code).toBe("MISSING_REQUIRED_FIELDS");
    });

    it("should return bad request when user is not authenticated", async () => {
      const context = createMockContext({
        user: null, // No user
      });

      const result = await controller.create(context);

      expect(result.success).toBe(false);
      expect(result.error).toBeDefined();
      expect(result.error.code).toBe("USER_NOT_AUTHENTICATED");
    });

    it("should return server error when service throws an error", async () => {
      // Create a new mock service with an error for create
      const errorMockService = {
        ...mockProjectTaskService,
        create: createMockFn(() =>
          Promise.resolve({
            data: null,
            error: new Error("Service error"),
          })
        ),
      };

      // Create a new controller with the error mock service
      const errorController = new ProjectTaskController(errorMockService);

      const context = createMockContext();
      const result = await errorController.create(context);

      expect(result.success).toBe(false);
      expect(result.error).toBeDefined();
      expect(result.error.message).toContain("Service error");
    });
  });

  describe("updateStatus", () => {
    it("should update a project task status successfully", async () => {
      const context = createMockContext({
        params: { id: "test-task-id" },
        body: { completion_status: TaskStatus.COMPLETED },
      });

      const result = await controller.updateStatus(context);

      expect(result.success).toBe(true);
      expect(result.data).toBeDefined();
      expect(result.message).toContain("status updated successfully");
    });

    it("should return bad request when status is invalid", async () => {
      const context = createMockContext({
        params: { id: "test-task-id" },
        body: { completion_status: "invalid-status" as TaskStatus },
      });

      const result = await controller.updateStatus(context);

      expect(result.success).toBe(false);
      expect(result.error).toBeDefined();
      expect(result.error.code).toBe("INVALID_STATUS");
    });

    it("should return bad request when task ID is missing", async () => {
      const context = createMockContext({
        params: {}, // No ID
        body: { completion_status: TaskStatus.COMPLETED },
      });

      const result = await controller.updateStatus(context);

      expect(result.success).toBe(false);
      expect(result.error).toBeDefined();
      expect(result.error.code).toBe("MISSING_TASK_ID");
    });

    it("should return not found when task doesn't exist", async () => {
      // Create a new mock service with a not found error for updateStatus
      const notFoundMockService = {
        ...mockProjectTaskService,
        updateStatus: createMockFn(() =>
          Promise.resolve({
            data: null,
            error: new Error("Project task not found"),
          })
        ),
      };

      // Create a new controller with the not found mock service
      const notFoundController = new ProjectTaskController(notFoundMockService);

      const context = createMockContext({
        params: { id: "non-existent-id" },
        body: { completion_status: TaskStatus.COMPLETED },
      });

      const result = await notFoundController.updateStatus(context);

      expect(result.success).toBe(false);
      expect(result.error).toBeDefined();
      expect(result.error.code).toBe("TASK_NOT_FOUND");
    });

    it("should return server error when service throws an error", async () => {
      // Create a new mock service with an error for updateStatus
      const errorMockService = {
        ...mockProjectTaskService,
        updateStatus: createMockFn(() =>
          Promise.resolve({
            data: null,
            error: new Error("Service error"),
          })
        ),
      };

      // Create a new controller with the error mock service
      const errorController = new ProjectTaskController(errorMockService);

      const context = createMockContext({
        params: { id: "test-task-id" },
        body: { completion_status: TaskStatus.COMPLETED },
      });

      const result = await errorController.updateStatus(context);

      expect(result.success).toBe(false);
      expect(result.error).toBeDefined();
      expect(result.error.message).toContain("Service error");
    });
  });

  describe("getById", () => {
    it("should get a project task by ID successfully", async () => {
      const context = createMockContext({
        params: { id: "test-task-id" },
      });

      const result = await controller.getById(context);

      expect(result.success).toBe(true);
      expect(result.data).toBeDefined();
      expect(result.data.id).toBe("test-task-id");
    });

    it("should return not found when task doesn't exist", async () => {
      // Create a new mock service with null data for getById
      const notFoundMockService = {
        ...mockProjectTaskService,
        getById: createMockFn(() =>
          Promise.resolve({
            data: null,
            error: null,
          })
        ),
      };

      // Create a new controller with the not found mock service
      const notFoundController = new ProjectTaskController(notFoundMockService);

      const context = createMockContext({
        params: { id: "non-existent-id" },
      });

      const result = await notFoundController.getById(context);

      expect(result.success).toBe(false);
      expect(result.error).toBeDefined();
      expect(result.error.code).toBe("TASK_NOT_FOUND");
    });

    it("should return server error when service throws an error", async () => {
      // Create a new mock service that throws an error for getById
      const errorMockService = {
        ...mockProjectTaskService,
        getById: createMockFn(() => {
          throw new Error("Unexpected error");
        }),
      };

      // Create a new controller with the error mock service
      const errorController = new ProjectTaskController(errorMockService);

      const context = createMockContext({
        params: { id: "test-task-id" },
      });

      const result = await errorController.getById(context);

      expect(result.success).toBe(false);
      expect(result.error).toBeDefined();
      expect(result.error.message).toContain("Unexpected error");
    });
  });

  describe("getAll", () => {
    it("should get all project tasks successfully", async () => {
      const context = createMockContext({
        query: { page: 1, limit: 10 },
      });

      const result = await controller.getAll(context);

      expect(result.success).toBe(true);
      expect(result.data).toBeDefined();
      expect(result.data.items).toBeDefined();
      expect(result.data.pagination).toBeDefined();
    });

    it("should handle query parameters", async () => {
      const context = createMockContext({
        query: {
          page: 2,
          limit: 20,
          project_id: "test-project-id",
          employee_id: "test-employee-id",
          completion_status: TaskStatus.NOT_COMPLETED,
        },
      });

      const result = await controller.getAll(context);

      expect(result.success).toBe(true);
      expect(result.data).toBeDefined();
    });

    it("should return server error when service throws an error", async () => {
      // Create a new mock service with an error for getAll
      const errorMockService = {
        ...mockProjectTaskService,
        getAll: createMockFn(() =>
          Promise.resolve({
            data: null,
            error: new Error("Service error"),
          })
        ),
      };

      // Create a new controller with the error mock service
      const errorController = new ProjectTaskController(errorMockService);

      const context = createMockContext();
      const result = await errorController.getAll(context);

      expect(result.success).toBe(false);
      expect(result.error).toBeDefined();
      expect(result.error.message).toContain("Service error");
    });
  });

  describe("getByProjectId", () => {
    it("should get project tasks by project ID successfully", async () => {
      const context = createMockContext({
        params: { projectId: "test-project-id" },
        query: { page: 1, limit: 10 },
      });

      const result = await controller.getByProjectId(context);

      expect(result.success).toBe(true);
      expect(result.data).toBeDefined();
      expect(result.data.items).toBeDefined();
    });

    it("should handle query parameters", async () => {
      const context = createMockContext({
        params: { projectId: "test-project-id" },
        query: {
          page: 2,
          limit: 20,
          sort_by: "due_date",
          sort_order: "desc",
        },
      });

      const result = await controller.getByProjectId(context);

      expect(result.success).toBe(true);
      expect(result.data).toBeDefined();
    });

    it("should return server error when service throws an error", async () => {
      // Create a new mock service with an error for getByProjectId
      const errorMockService = {
        ...mockProjectTaskService,
        getByProjectId: createMockFn(() =>
          Promise.resolve({
            data: null,
            error: new Error("Service error"),
          })
        ),
      };

      // Create a new controller with the error mock service
      const errorController = new ProjectTaskController(errorMockService);

      const context = createMockContext({
        params: { projectId: "test-project-id" },
      });

      const result = await errorController.getByProjectId(context);

      expect(result.success).toBe(false);
      expect(result.error).toBeDefined();
      expect(result.error.message).toContain("Service error");
    });
  });

  describe("update", () => {
    it("should update a project task successfully", async () => {
      const context = createMockContext({
        params: { id: "test-task-id" },
        body: {
          description: "Updated description",
          due_date: "2023-02-28",
        },
      });

      const result = await controller.update(context);

      expect(result.success).toBe(true);
      expect(result.data).toBeDefined();
      expect(result.message).toContain("updated successfully");
    });

    it("should return bad request when user is not authenticated", async () => {
      const context = createMockContext({
        params: { id: "test-task-id" },
        body: { description: "Updated description" },
        user: null, // No user
      });

      const result = await controller.update(context);

      expect(result.success).toBe(false);
      expect(result.error).toBeDefined();
      expect(result.error.code).toBe("USER_NOT_AUTHENTICATED");
    });

    it("should return bad request when task ID is missing", async () => {
      const context = createMockContext({
        params: {}, // No ID
        body: { description: "Updated description" },
      });

      const result = await controller.update(context);

      expect(result.success).toBe(false);
      expect(result.error).toBeDefined();
      expect(result.error.code).toBe("MISSING_TASK_ID");
    });

    it("should return bad request when no update data is provided", async () => {
      const context = createMockContext({
        params: { id: "test-task-id" },
        body: {}, // Empty body
      });

      const result = await controller.update(context);

      expect(result.success).toBe(false);
      expect(result.error).toBeDefined();
      expect(result.error.code).toBe("NO_UPDATE_DATA");
    });

    it("should return not found when task doesn't exist", async () => {
      // Create a new mock service with a not found error for update
      const notFoundMockService = {
        ...mockProjectTaskService,
        update: createMockFn(() =>
          Promise.resolve({
            data: null,
            error: new Error("Project task not found"),
          })
        ),
      };

      // Create a new controller with the not found mock service
      const notFoundController = new ProjectTaskController(notFoundMockService);

      const context = createMockContext({
        params: { id: "non-existent-id" },
        body: { description: "Updated description" },
      });

      const result = await notFoundController.update(context);

      expect(result.success).toBe(false);
      expect(result.error).toBeDefined();
      expect(result.error.code).toBe("TASK_NOT_FOUND");
    });

    it("should return server error when service throws an error", async () => {
      // Create a new mock service that throws an error for update
      const errorMockService = {
        ...mockProjectTaskService,
        update: createMockFn(() => {
          throw new Error("Unexpected error");
        }),
      };

      // Create a new controller with the error mock service
      const errorController = new ProjectTaskController(errorMockService);

      const context = createMockContext({
        params: { id: "test-task-id" },
        body: { description: "Updated description" },
      });

      const result = await errorController.update(context);

      expect(result.success).toBe(false);
      expect(result.error).toBeDefined();
      expect(result.error.message).toContain("Unexpected error");
    });
  });

  describe("delete", () => {
    it("should delete a project task successfully", async () => {
      const context = createMockContext({
        params: { id: "test-task-id" },
      });

      const result = await controller.delete(context);

      expect(result.success).toBe(true);
      expect(result.message).toContain("deleted successfully");
    });

    it("should return bad request when user is not authenticated", async () => {
      const context = createMockContext({
        params: { id: "test-task-id" },
        user: null, // No user
      });

      const result = await controller.delete(context);

      expect(result.success).toBe(false);
      expect(result.error).toBeDefined();
      expect(result.error.code).toBe("USER_NOT_AUTHENTICATED");
    });

    it("should return bad request when task ID is missing", async () => {
      const context = createMockContext({
        params: {}, // No ID
      });

      const result = await controller.delete(context);

      expect(result.success).toBe(false);
      expect(result.error).toBeDefined();
      expect(result.error.code).toBe("MISSING_TASK_ID");
    });

    it("should return not found when task doesn't exist", async () => {
      // Create a new mock service with a not found error for delete
      const notFoundMockService = {
        ...mockProjectTaskService,
        delete: createMockFn(() =>
          Promise.resolve({
            data: null,
            error: new Error("Project task not found"),
          })
        ),
      };

      // Create a new controller with the not found mock service
      const notFoundController = new ProjectTaskController(notFoundMockService);

      const context = createMockContext({
        params: { id: "non-existent-id" },
      });

      const result = await notFoundController.delete(context);

      expect(result.success).toBe(false);
      expect(result.error).toBeDefined();
      expect(result.error.code).toBe("TASK_NOT_FOUND");
    });

    it("should return server error when service throws an error", async () => {
      // Create a new mock service that throws an error for delete
      const errorMockService = {
        ...mockProjectTaskService,
        delete: createMockFn(() => {
          throw new Error("Unexpected error");
        }),
      };

      // Create a new controller with the error mock service
      const errorController = new ProjectTaskController(errorMockService);

      const context = createMockContext({
        params: { id: "test-task-id" },
      });

      const result = await errorController.delete(context);

      expect(result.success).toBe(false);
      expect(result.error).toBeDefined();
      expect(result.error.message).toContain("Unexpected error");
    });
  });
});
