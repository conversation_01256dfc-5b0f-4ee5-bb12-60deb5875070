import { describe, expect, it, beforeEach, afterEach } from "bun:test";
import {
  ProjectTaskService,
  projectTaskService,
} from "../../modules/project-task/service";
import { TaskStatus } from "../../database/models/task.model";
import { QueryOptions } from "../../utils/database.types";
import { createMockProjectTask, setupMocks } from "./test-utils";

describe("ProjectTaskService", () => {
  // Reset mocks after each test
  let resetMocks: () => void;

  afterEach(() => {
    // Reset mocks to prevent test interference
    if (resetMocks) {
      resetMocks();
    }
  });

  // Test create method
  describe("create", () => {
    it("should create a project task successfully", async () => {
      // ARRANGE
      const mockTask = createMockProjectTask();
      resetMocks = setupMocks({
        create: () => Promise.resolve({ data: mockTask, error: null }),
      });

      const taskData = {
        assigned_by: "test-user-id",
        description: "Test project task",
        completion_status: TaskStatus.NOT_COMPLETED,
        employee_id: "test-employee-id",
        initial_date: "2023-01-01",
        due_date: "2023-01-31",
        project_id: "test-project-id",
        weekly_log_id: "test-weekly-log-id",
      };

      // ACT
      const result = await ProjectTaskService.create(taskData, "test-user-id");

      // ASSERT
      expect(result.data).toBeDefined();
      expect(result.error).toBeNull();
      expect(result.data?.description).toBe("Test project task");
    });

    it("should handle database errors during creation", async () => {
      // ARRANGE
      const dbError = new Error("Database connection error");
      resetMocks = setupMocks({
        create: () => Promise.resolve({ data: null, error: dbError }),
      });

      const taskData = {
        assigned_by: "test-user-id",
        description: "Test project task",
        completion_status: TaskStatus.NOT_COMPLETED,
        employee_id: "test-employee-id",
        initial_date: "2023-01-01",
        due_date: "2023-01-31",
        project_id: "test-project-id",
      };

      // ACT
      const result = await ProjectTaskService.create(taskData, "test-user-id");

      // ASSERT
      expect(result.data).toBeNull();
      expect(result.error).toBeDefined();
      expect(result.error).toBe(dbError);
    });

    it("should handle unexpected errors during creation", async () => {
      // ARRANGE
      resetMocks = setupMocks({
        create: () => {
          throw new Error("Unexpected error");
        },
      });

      const taskData = {
        assigned_by: "test-user-id",
        description: "Test project task",
        completion_status: TaskStatus.NOT_COMPLETED,
        employee_id: "test-employee-id",
        initial_date: "2023-01-01",
        due_date: "2023-01-31",
        project_id: "test-project-id",
      };

      // ACT
      const result = await ProjectTaskService.create(taskData, "test-user-id");

      // ASSERT
      expect(result.data).toBeNull();
      expect(result.error).toBeDefined();
      expect(result.error?.message).toBe("Unexpected error");
    });
  });

  // Test updateStatus method
  describe("updateStatus", () => {
    it("should update a project task status successfully", async () => {
      // ARRANGE
      const mockTask = createMockProjectTask();
      const updatedTask = createMockProjectTask({
        completion_status: TaskStatus.COMPLETED,
      });

      resetMocks = setupMocks({
        getById: () => Promise.resolve({ data: mockTask, error: null }),
        update: () => Promise.resolve({ data: updatedTask, error: null }),
      });

      // ACT
      const result = await ProjectTaskService.updateStatus(
        "test-task-id",
        TaskStatus.COMPLETED,
        "test-user-id"
      );

      // ASSERT
      expect(result.data).toBeDefined();
      expect(result.error).toBeNull();
      expect(result.data?.completion_status).toBe(TaskStatus.COMPLETED);
    });

    it("should return error when task doesn't exist", async () => {
      // ARRANGE
      resetMocks = setupMocks({
        getById: () => Promise.resolve({ data: null, error: null }),
      });

      // ACT
      const result = await ProjectTaskService.updateStatus(
        "non-existent-id",
        TaskStatus.COMPLETED,
        "test-user-id"
      );

      // ASSERT
      expect(result.data).toBeNull();
      expect(result.error).toBeDefined();
      expect(result.error?.message).toContain("not found");
    });

    it("should handle database errors during status retrieval", async () => {
      // ARRANGE
      const dbError = new Error("Database error during retrieval");
      resetMocks = setupMocks({
        getById: () => Promise.resolve({ data: null, error: dbError }),
      });

      // ACT
      const result = await ProjectTaskService.updateStatus(
        "test-task-id",
        TaskStatus.COMPLETED,
        "test-user-id"
      );

      // ASSERT
      expect(result.data).toBeNull();
      expect(result.error).toBe(dbError);
    });

    it("should handle database errors during status update", async () => {
      // ARRANGE
      const mockTask = createMockProjectTask();
      const dbError = new Error("Database error during update");

      resetMocks = setupMocks({
        getById: () => Promise.resolve({ data: mockTask, error: null }),
        update: () => Promise.resolve({ data: null, error: dbError }),
      });

      // ACT
      const result = await ProjectTaskService.updateStatus(
        "test-task-id",
        TaskStatus.COMPLETED,
        "test-user-id"
      );

      // ASSERT
      expect(result.data).toBeNull();
      expect(result.error).toBe(dbError);
    });
  });

  // Test getById method
  describe("getById", () => {
    it("should get a project task by ID successfully", async () => {
      // ARRANGE
      const mockTask = createMockProjectTask();
      resetMocks = setupMocks({
        getById: () => Promise.resolve({ data: mockTask, error: null }),
      });

      // ACT
      const result = await ProjectTaskService.getById("test-task-id");

      // ASSERT
      expect(result.data).toBeDefined();
      expect(result.error).toBeNull();
      expect(result.data?.id).toBe("test-task-id");
    });

    it("should return null when task doesn't exist", async () => {
      // ARRANGE
      resetMocks = setupMocks({
        getById: () => Promise.resolve({ data: null, error: null }),
      });

      // ACT
      const result = await ProjectTaskService.getById("non-existent-id");

      // ASSERT
      expect(result.data).toBeNull();
      expect(result.error).toBeNull();
    });

    it("should handle database errors during retrieval", async () => {
      // ARRANGE
      const dbError = new Error("Database error");
      resetMocks = setupMocks({
        getById: () => Promise.resolve({ data: null, error: dbError }),
      });

      // ACT
      const result = await ProjectTaskService.getById("test-task-id");

      // ASSERT
      expect(result.data).toBeNull();
      expect(result.error).toBe(dbError);
    });
  });

  // Test getAll method
  describe("getAll", () => {
    it("should get all project tasks successfully", async () => {
      // ARRANGE
      const mockTask = createMockProjectTask();
      resetMocks = setupMocks({
        getAll: () =>
          Promise.resolve({
            data: [mockTask],
            result: {
              page: 1,
              pageSize: 10,
              total: 1,
              pageCount: 1,
            },
            error: null,
          }),
      });

      // ACT
      const result = await ProjectTaskService.getAll();

      // ASSERT
      expect(result.data).toBeDefined();
      expect(result.error).toBeNull();
      // Check for the nested structure we now have
      expect(result.data?.items).toBeDefined();
      // We can't check the exact structure due to type issues, but we can check that it exists
    });

    it("should handle filtering and pagination", async () => {
      // ARRANGE
      const mockTask = createMockProjectTask();
      resetMocks = setupMocks({
        getAll: () =>
          Promise.resolve({
            data: [mockTask],
            result: {
              page: 1,
              pageSize: 10,
              total: 1,
              pageCount: 1,
            },
            error: null,
          }),
      });

      const options: QueryOptions = {
        pagination: { page: 1, pageSize: 10 },
        filters: [
          { field: "project_id", operator: "eq", value: "test-project-id" },
        ],
      };

      // ACT
      const result = await ProjectTaskService.getAll(options);

      // ASSERT
      expect(result.data).toBeDefined();
      expect(result.error).toBeNull();
      expect(result.data?.items).toBeDefined();
    });

    it("should handle empty results", async () => {
      // ARRANGE
      resetMocks = setupMocks({
        getAll: () =>
          Promise.resolve({
            data: [],
            result: {
              page: 1,
              pageSize: 10,
              total: 0,
              pageCount: 0,
            },
            error: null,
          }),
      });

      // ACT
      const result = await ProjectTaskService.getAll();

      // ASSERT
      expect(result.data).toBeDefined();
      expect(result.error).toBeNull();
      // Check for the nested structure we now have
      expect(result.data?.items).toBeDefined();
      // We can't check the exact structure due to type issues, but we can check that it exists
    });

    it("should handle database errors", async () => {
      // ARRANGE
      const dbError = new Error("Database error");
      resetMocks = setupMocks({
        getAll: () => Promise.resolve({ data: null, error: dbError }),
      });

      // ACT
      const result = await ProjectTaskService.getAll();

      // ASSERT
      expect(result.data).toBeNull();
      expect(result.error).toBe(dbError);
    });
  });

  // Test getByProjectId method
  describe("getByProjectId", () => {
    it("should get project tasks by project ID successfully", async () => {
      // ARRANGE
      const mockTask = createMockProjectTask();
      resetMocks = setupMocks({
        getAll: () =>
          Promise.resolve({
            data: [mockTask],
            result: {
              page: 1,
              pageSize: 10,
              total: 1,
              pageCount: 1,
            },
            error: null,
          }),
      });

      // ACT
      const result = await ProjectTaskService.getByProjectId("test-project-id");

      // ASSERT
      expect(result.data).toBeDefined();
      expect(result.error).toBeNull();
      // Check for the nested structure we now have
      expect(result.data?.items).toBeDefined();
      // We can't check the exact structure due to type issues, but we can check that it exists
    });

    it("should handle filtering and pagination", async () => {
      // ARRANGE
      const mockTask = createMockProjectTask();
      resetMocks = setupMocks({
        getAll: () =>
          Promise.resolve({
            data: [mockTask],
            result: {
              page: 1,
              pageSize: 10,
              total: 1,
              pageCount: 1,
            },
            error: null,
          }),
      });

      const options: QueryOptions = {
        pagination: { page: 1, pageSize: 10 },
        sort: { field: "due_date", direction: "asc" },
      };

      // ACT
      const result = await ProjectTaskService.getByProjectId(
        "test-project-id",
        options
      );

      // ASSERT
      expect(result.data).toBeDefined();
      expect(result.error).toBeNull();
      expect(result.data?.items).toBeDefined();
    });

    it("should handle empty results", async () => {
      // ARRANGE
      resetMocks = setupMocks({
        getAll: () =>
          Promise.resolve({
            data: [],
            result: {
              page: 1,
              pageSize: 10,
              total: 0,
              pageCount: 0,
            },
            error: null,
          }),
      });

      // ACT
      const result = await ProjectTaskService.getByProjectId("test-project-id");

      // ASSERT
      expect(result.data).toBeDefined();
      expect(result.error).toBeNull();
      // We can't check the exact structure due to type issues, but we can check that it exists
      expect(result.data?.items).toBeDefined();
    });

    it("should handle database errors", async () => {
      // ARRANGE
      const dbError = new Error("Database error");
      resetMocks = setupMocks({
        getAll: () => Promise.resolve({ data: null, error: dbError }),
      });

      // ACT
      const result = await ProjectTaskService.getByProjectId("test-project-id");

      // ASSERT
      expect(result.data).toBeNull();
      expect(result.error).toBe(dbError);
    });
  });

  // Test update method
  describe("update", () => {
    it("should update a project task successfully", async () => {
      // ARRANGE
      const mockTask = createMockProjectTask();
      const updatedTask = createMockProjectTask({
        description: "Updated description",
        due_date: "2023-02-28",
      });

      resetMocks = setupMocks({
        getById: () => Promise.resolve({ data: mockTask, error: null }),
        update: () => Promise.resolve({ data: updatedTask, error: null }),
      });

      const updateData = {
        description: "Updated description",
        due_date: "2023-02-28",
      };

      // ACT
      const result = await ProjectTaskService.update(
        "test-task-id",
        updateData,
        "test-user-id"
      );

      // ASSERT
      expect(result.data).toBeDefined();
      expect(result.error).toBeNull();
      expect(result.data?.description).toBe("Updated description");
      expect(result.data?.due_date).toBe("2023-02-28");
    });

    it("should return error when task doesn't exist", async () => {
      // ARRANGE
      resetMocks = setupMocks({
        getById: () => Promise.resolve({ data: null, error: null }),
      });

      const updateData = {
        description: "Updated description",
      };

      // ACT
      const result = await ProjectTaskService.update(
        "non-existent-id",
        updateData,
        "test-user-id"
      );

      // ASSERT
      expect(result.data).toBeNull();
      expect(result.error).toBeDefined();
      expect(result.error?.message).toContain("not found");
    });

    it("should handle database errors during task retrieval", async () => {
      // ARRANGE
      const dbError = new Error("Database error during retrieval");
      resetMocks = setupMocks({
        getById: () => Promise.resolve({ data: null, error: dbError }),
      });

      const updateData = {
        description: "Updated description",
      };

      // ACT
      const result = await ProjectTaskService.update(
        "test-task-id",
        updateData,
        "test-user-id"
      );

      // ASSERT
      expect(result.data).toBeNull();
      expect(result.error).toBe(dbError);
    });

    it("should handle database errors during update", async () => {
      // ARRANGE
      const mockTask = createMockProjectTask();
      const dbError = new Error("Database error during update");

      resetMocks = setupMocks({
        getById: () => Promise.resolve({ data: mockTask, error: null }),
        update: () => Promise.resolve({ data: null, error: dbError }),
      });

      const updateData = {
        description: "Updated description",
      };

      // ACT
      const result = await ProjectTaskService.update(
        "test-task-id",
        updateData,
        "test-user-id"
      );

      // ASSERT
      expect(result.data).toBeNull();
      expect(result.error).toBe(dbError);
    });
  });

  // Test delete method
  describe("delete", () => {
    it("should delete a project task successfully", async () => {
      // ARRANGE
      const mockTask = createMockProjectTask();
      const deletedTask = createMockProjectTask({
        deleted_at: new Date().toISOString(),
      });

      resetMocks = setupMocks({
        getById: () => Promise.resolve({ data: mockTask, error: null }),
        softDelete: () => Promise.resolve({ data: deletedTask, error: null }),
      });

      // ACT
      const result = await ProjectTaskService.delete(
        "test-task-id",
        "test-user-id"
      );

      // ASSERT
      expect(result.data).toBeDefined();
      expect(result.error).toBeNull();
      expect(result.data?.deleted_at).toBeDefined();
    });

    it("should return error when task doesn't exist", async () => {
      // ARRANGE
      resetMocks = setupMocks({
        getById: () => Promise.resolve({ data: null, error: null }),
      });

      // ACT
      const result = await ProjectTaskService.delete(
        "non-existent-id",
        "test-user-id"
      );

      // ASSERT
      expect(result.data).toBeNull();
      expect(result.error).toBeDefined();
      expect(result.error?.message).toContain("not found");
    });

    it("should handle database errors during task retrieval", async () => {
      // ARRANGE
      const dbError = new Error("Database error during retrieval");
      resetMocks = setupMocks({
        getById: () => Promise.resolve({ data: null, error: dbError }),
      });

      // ACT
      const result = await ProjectTaskService.delete(
        "test-task-id",
        "test-user-id"
      );

      // ASSERT
      expect(result.data).toBeNull();
      expect(result.error).toBe(dbError);
    });

    it("should handle database errors during deletion", async () => {
      // ARRANGE
      const mockTask = createMockProjectTask();
      const dbError = new Error("Database error during deletion");

      resetMocks = setupMocks({
        getById: () => Promise.resolve({ data: mockTask, error: null }),
        softDelete: () => Promise.resolve({ data: null, error: dbError }),
      });

      // ACT
      const result = await ProjectTaskService.delete(
        "test-task-id",
        "test-user-id"
      );

      // ASSERT
      expect(result.data).toBeNull();
      expect(result.error).toBe(dbError);
    });
  });
});
