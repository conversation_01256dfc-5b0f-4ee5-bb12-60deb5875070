import { describe, expect, it } from "bun:test";
import { ProjectTaskController } from "../../modules/project-task/controller";
import { IProjectTaskService } from "../../modules/project-task/service.interface";
import { TaskStatus } from "../../database/models/task.model";
import { ProjectTask } from "../../database/models/project-task.model";

// Sample project task data for testing
const mockProjectTask: ProjectTask = {
  id: "test-task-id",
  assigned_by: "test-user-id",
  description: "Test project task",
  completion_status: TaskStatus.NOT_COMPLETED,
  employee_id: "test-employee-id",
  initial_date: "2023-01-01",
  due_date: "2023-01-31",
  project_id: "test-project-id",
  weekly_log_id: "test-weekly-log-id",
  created_at: new Date().toISOString(),
  created_by: "test-user-id",
  updated_at: null,
  updated_by: null,
  deleted_at: null,
  deleted_by: null,
};

// Create a mock service
const mockProjectTaskService: IProjectTaskService = {
  create: async () => ({ data: mockProjectTask, error: null }),
  updateStatus: async () => ({
    data: { ...mockProjectTask, completion_status: TaskStatus.COMPLETED },
    error: null,
  }),
  getById: async () => ({ data: mockProjectTask, error: null }),
  getAll: async () => ({
    data: { items: [mockProjectTask] },
    pagination: {
      page: 1,
      pageSize: 10,
      totalItems: 1,
      totalPages: 1,
    },
    error: null,
  }),
  getByProjectId: async () => ({
    data: { items: [mockProjectTask] },
    pagination: {
      page: 1,
      pageSize: 10,
      totalItems: 1,
      totalPages: 1,
    },
    error: null,
  }),
  update: async () => ({
    data: { ...mockProjectTask, description: "Updated description" },
    error: null,
  }),
  delete: async () => ({
    data: { ...mockProjectTask, deleted_at: new Date().toISOString() },
    error: null,
  }),
};

// Helper function to create a mock context
const createMockContext = (overrides = {}) => {
  return {
    params: { id: "test-task-id" },
    body: {
      description: "Test project task",
      assigned_by: "test-user-id",
      employee_id: "test-employee-id",
      initial_date: "2023-01-01",
      due_date: "2023-01-31",
      project_id: "test-project-id",
      completion_status: TaskStatus.NOT_COMPLETED,
    },
    query: {},
    user: { id: "test-user-id" },
    profile: { role: "STAFF_OPERATIONS" },
    // Response functions
    success: (data: any, message = "") => ({
      success: true,
      data,
      message,
    }),
    badRequest: (message: string, code = "") => ({
      success: false,
      error: { message, code },
    }),
    serverError: (message: string, error?: Error) => ({
      success: false,
      error: { message, ...(error ? { details: error.message } : {}) },
    }),
    notFound: (message: string, code = "") => ({
      success: false,
      error: { message, code },
    }),
    ...overrides,
  };
};

describe("ProjectTaskController with Dependency Injection", () => {
  // Create a controller instance with the mock service
  const controller = new ProjectTaskController(mockProjectTaskService);

  it("should create a controller instance with a mock service", () => {
    expect(controller).toBeDefined();
  });

  it("should create a project task successfully", async () => {
    const context = createMockContext();
    const result = await controller.create(context);

    expect(result.success).toBe(true);
    expect(result.data).toBeDefined();
    expect(result.message).toContain("created successfully");
  });

  it("should get a project task by ID successfully", async () => {
    const context = createMockContext();
    const result = await controller.getById(context);

    expect(result.success).toBe(true);
    expect(result.data).toBeDefined();
    expect(result.data.id).toBe("test-task-id");
  });

  it("should update a project task status successfully", async () => {
    const context = createMockContext({
      params: { id: "test-task-id" },
      body: { completion_status: TaskStatus.COMPLETED },
    });
    const result = await controller.updateStatus(context);

    expect(result.success).toBe(true);
    expect(result.data).toBeDefined();
    expect(result.data.completion_status).toBe(TaskStatus.COMPLETED);
  });

  it("should get all project tasks successfully", async () => {
    const context = createMockContext({
      query: { page: 1, limit: 10 },
    });
    const result = await controller.getAll(context);

    expect(result.success).toBe(true);
    expect(result.data).toBeDefined();
    expect(result.data.items).toBeDefined();
    expect(result.data.items.items).toBeDefined();
    expect(Array.isArray(result.data.items.items)).toBe(true);
    expect(result.data.items.items.length).toBeGreaterThan(0);
  });

  it("should get project tasks by project ID successfully", async () => {
    const context = createMockContext({
      params: { projectId: "test-project-id" },
      query: { page: 1, limit: 10 },
    });
    const result = await controller.getByProjectId(context);

    expect(result.success).toBe(true);
    expect(result.data).toBeDefined();
    expect(result.data.items).toBeDefined();
    expect(result.data.items.items).toBeDefined();
    expect(Array.isArray(result.data.items.items)).toBe(true);
    expect(result.data.items.items.length).toBeGreaterThan(0);
  });

  it("should update a project task successfully", async () => {
    const context = createMockContext({
      params: { id: "test-task-id" },
      body: { description: "Updated description" },
    });
    const result = await controller.update(context);

    expect(result.success).toBe(true);
    expect(result.data).toBeDefined();
    expect(result.data.description).toBe("Updated description");
  });

  it("should delete a project task successfully", async () => {
    const context = createMockContext({
      params: { id: "test-task-id" },
    });
    const result = await controller.delete(context);

    expect(result.success).toBe(true);
    expect(result.message).toContain("deleted successfully");
  });
});
