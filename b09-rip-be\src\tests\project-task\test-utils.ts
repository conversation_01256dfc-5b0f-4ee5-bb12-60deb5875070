import { TaskStatus } from "../../database/models/task.model";
import { ProjectTask } from "../../database/models/project-task.model";

/**
 * Creates a mock project task for testing
 * @param overrides Optional properties to override in the mock task
 * @returns A mock project task
 */
export function createMockProjectTask(overrides: Partial<ProjectTask> = {}): ProjectTask {
  return {
    id: "test-task-id",
    assigned_by: "test-user-id",
    description: "Test project task",
    completion_status: TaskStatus.NOT_COMPLETED,
    employee_id: "test-employee-id",
    initial_date: "2023-01-01",
    due_date: "2023-01-31",
    project_id: "test-project-id",
    weekly_log_id: "test-weekly-log-id",
    created_at: new Date().toISOString(),
    created_by: "test-user-id",
    updated_at: null,
    updated_by: null,
    deleted_at: null,
    deleted_by: null,
    ...overrides,
  };
}

/**
 * Creates a mock database utilities object for testing
 * @param overrides Optional method implementations to override
 * @returns A mock dbUtils object
 */
export function createMockDbUtils(overrides: Record<string, any> = {}) {
  // Create a mock task for testing
  const mockTask = createMockProjectTask();
  
  // Default mock implementations
  const defaultMocks = {
    create: () => Promise.resolve({ data: mockTask, error: null }),
    getById: (tableName: string, id: string) => {
      if (id === "test-task-id") {
        return Promise.resolve({ data: mockTask, error: null });
      }
      return Promise.resolve({ data: null, error: null });
    },
    getAll: () => Promise.resolve({
      data: {
        items: [mockTask],
        pagination: {
          page: 1,
          pageSize: 10,
          totalItems: 1,
          totalPages: 1,
        },
      },
      error: null,
    }),
    update: (tableName: string, id: string, data: any) => {
      if (id === "test-task-id") {
        return Promise.resolve({
          data: { ...mockTask, ...data },
          error: null,
        });
      }
      return Promise.resolve({
        data: null,
        error: new Error("Project task not found"),
      });
    },
    softDelete: (tableName: string, id: string) => {
      if (id === "test-task-id") {
        return Promise.resolve({
          data: { ...mockTask, deleted_at: new Date().toISOString() },
          error: null,
        });
      }
      return Promise.resolve({
        data: null,
        error: new Error("Project task not found"),
      });
    },
  };
  
  // Merge default mocks with overrides
  return { ...defaultMocks, ...overrides };
}

/**
 * Creates a mock function with call tracking
 * @param implementation Optional implementation function
 * @returns A mock function with call tracking
 */
export function createMockFn<T extends (...args: any[]) => any>(
  implementation?: T
): { (...args: Parameters<T>): Promise<any>; mock: { calls: any[][] } } {
  const calls: any[][] = [];
  const fn = (...args: Parameters<T>) => {
    calls.push(args);
    // Ensure we return a Promise because the service methods return Promises
    const result = implementation?.(...args);
    return result instanceof Promise ? result : Promise.resolve(result);
  };
  fn.mock = { calls };
  return fn;
}

/**
 * Sets up mocks for a test
 * @param mocks Optional mock overrides
 * @returns A function to reset the mocks
 */
export function setupMocks(mocks: Record<string, any> = {}) {
  // Import the mock module
  const mockModule = require("bun:test").mock;
  
  // Create mock database utilities
  const dbUtilsMock = createMockDbUtils(mocks);
  
  // Mock the database utilities module
  mockModule.module("../../utils/database", () => ({
    dbUtils: dbUtilsMock,
  }));
  
  // Return a function to reset the mocks
  return () => {
    // Reset the mocks by re-mocking the module
    mockModule.module("../../utils/database", () => ({
      dbUtils: createMockDbUtils(mocks),
    }));
  };
}
