import { describe, expect, it, mock, beforeEach } from "bun:test";

// Mock the supabase module
mock.module("../../libs/supabase", () => {
  return {
    supabase: {
      from: () => ({
        select: () => ({
          eq: () => ({
            single: () => Promise.resolve({ data: null, error: null }),
          }),
        }),
        insert: () => Promise.resolve({ data: null, error: null }),
        update: () => Promise.resolve({ data: null, error: null }),
        delete: () => Promise.resolve({ data: null, error: null }),
      }),
    },
  };
});

import { ProjectController } from "../../modules/project/controller";
import {
  ProjectStatus,
  ProjectCategory,
} from "../../database/models/project.model";
import { createMockProject, createMockContext } from "./test-utils";

// Mock the ProjectService
function mockProjectService() {
  const mockProject = createMockProject();

  mock.module("../../modules/project/service", () => {
    return {
      ProjectService: {
        create: async () => ({ data: mockProject, error: null }),
        getById: async (id) => {
          if (id === "test-project-id") {
            return { data: mockProject, error: null };
          }
          return { data: null, error: new Error("Project not found") };
        },
        getAll: async () => ({
          data: [mockProject],
          error: null,
          result: {
            total: 1,
            page: 1,
            pageSize: 10,
            pageCount: 1,
          },
        }),
        update: async () => ({
          data: { ...mockProject, project_name: "Updated Project" },
          error: null,
        }),
        delete: async () => ({
          data: { id: "test-project-id" },
          error: null,
        }),
      },
    };
  });

  return mockProject;
}

describe("ProjectController", () => {
  // Set up mocks before each test
  beforeEach(() => {
    // Mock the ProjectService for each test
    const mockProject = createMockProject();

    mock.module("../../modules/project/service", () => ({
      ProjectService: {
        create: async () => ({ data: mockProject, error: null }),
        getById: async (id) => {
          if (id === "test-project-id") {
            return { data: mockProject, error: null };
          }
          return { data: null, error: new Error("Project not found") };
        },
        getAll: async () => ({
          data: [mockProject],
          error: null,
          result: {
            total: 1,
            page: 1,
            pageSize: 10,
            pageCount: 1,
          },
        }),
        update: async () => ({
          data: { ...mockProject, project_name: "Updated Project" },
          error: null,
        }),
        delete: async () => ({
          data: { id: "test-project-id" },
          error: null,
        }),
      },
    }));
  });

  // Test create method
  describe("create", () => {
    it("should create a project successfully", async () => {
      // ARRANGE
      const context = createMockContext();

      // ACT
      const result = await ProjectController.create(context);

      // ASSERT
      expect(result.success).toBe(true);
      expect(result.data).toBeDefined();
      expect(result.message).toContain("created successfully");
    });

    it("should return bad request when required fields are missing", async () => {
      // ARRANGE
      const context = createMockContext({
        body: {
          // Missing required fields
          project_name: "Test Project",
          // No organization_id, pic_project, etc.
        },
      });

      // ACT
      const result = await ProjectController.create(context);

      // ASSERT
      expect(result.success).toBe(false);
      expect(result.error).toBeDefined();
      expect(result.error.code).toBe("MISSING_REQUIRED_FIELDS");
    });

    // Role validation is now handled at the route level
  });

  // Test getById method
  describe("getById", () => {
    it("should get a project by ID successfully", async () => {
      // ARRANGE
      const context = createMockContext();

      // ACT
      const result = await ProjectController.getById(context);

      // ASSERT
      expect(result.success).toBe(true);
      expect(result.data).toBeDefined();
      expect(result.message).toContain("retrieved successfully");
    });

    it("should return not found when project doesn't exist", async () => {
      // ARRANGE
      // Mock the ProjectService.getById to return null for non-existent IDs
      mock.module("../../modules/project/service", () => {
        return {
          ProjectService: {
            getById: async () => ({ data: null, error: null }),
          },
        };
      });

      const context = createMockContext({
        params: { id: "non-existent-id" },
      });

      // Mock the controller's notFound method
      context.notFound = (message) => ({
        success: false,
        error: { message, code: "NOT_FOUND" },
      });

      // ACT
      const result = await ProjectController.getById(context);

      // ASSERT
      expect(result.success).toBe(false);
      expect(result.error).toBeDefined();
      expect(result.error.code).toBe("NOT_FOUND");
    });

    // Role validation is now handled at the route level
  });

  // Test getAll method
  describe("getAll", () => {
    it("should get all projects successfully", async () => {
      // ARRANGE
      const context = createMockContext();

      // ACT
      const result = await ProjectController.getAll(context);

      // ASSERT
      expect(result.success).toBe(true);
      expect(result.data).toBeDefined();
      expect(result.data.items).toBeDefined();
      expect(Array.isArray(result.data.items)).toBe(true);
      expect(result.data.items.length).toBeGreaterThan(0);
      expect(result.data.pagination).toBeDefined();
    });

    // Role validation is now handled at the route level
  });

  // Test update method
  describe("update", () => {
    it("should update a project successfully", async () => {
      // ARRANGE
      const context = createMockContext({
        body: {
          project_name: "Updated Project",
        },
      });

      // ACT
      const result = await ProjectController.update(context);

      // ASSERT
      expect(result.success).toBe(true);
      expect(result.data).toBeDefined();
      expect(result.message).toContain("updated successfully");
    });

    // Role validation is now handled at the route level
  });

  // Test delete method
  describe("delete", () => {
    it("should delete a project successfully", async () => {
      // ARRANGE
      const context = createMockContext();

      // ACT
      const result = await ProjectController.delete(context);

      // ASSERT
      expect(result.success).toBe(true);
      expect(result.data).toBeDefined();
      expect(result.message).toContain("deleted successfully");
    });

    // Role validation is now handled at the route level
  });
});
