import { describe, expect, it } from "bun:test";
import { ProjectService } from "../../modules/project/service";
import {
  ProjectStatus,
  ProjectCategory,
} from "../../database/models/project.model";

describe("ProjectService Simple Tests", () => {
  // Test that the service exists and has the expected methods
  it("should have all required methods", () => {
    expect(ProjectService).toBeDefined();
    expect(typeof ProjectService.create).toBe("function");
    expect(typeof ProjectService.getById).toBe("function");
    expect(typeof ProjectService.getAll).toBe("function");
    expect(typeof ProjectService.update).toBe("function");
    expect(typeof ProjectService.delete).toBe("function");
  });

  // Test that the service exists and has the expected properties
  it("should be a valid service class", () => {
    expect(ProjectService).toBeDefined();
    expect(typeof ProjectService).toBe("object");
  });
});
