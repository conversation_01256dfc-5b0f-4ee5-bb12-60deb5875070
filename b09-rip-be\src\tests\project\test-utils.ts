import {
  Project,
  ProjectCategory,
  ProjectStatus,
} from "../../database/models/project.model";
import { mock } from "bun:test";

/**
 * Create a mock project for testing
 */
export function createMockProject(overrides = {}): Project {
  return {
    id: "test-project-id",
    organization_id: "test-org-id",
    project_category: ProjectCategory.BRAND_STRATEGY,
    project_name: "Test Project",
    pic_project: "test-employee-id",
    start_project: "2023-01-01",
    end_project: "2023-12-31",
    status_project: ProjectStatus.NOT_STARTED,
    budget_project: "100000",
    gantt_chart_id: "test-gantt-chart-id",
    project_charter_id: "test-project-charter-id",
    objectives: "Test objectives",
    created_at: "2023-01-01T00:00:00Z",
    created_by: "test-user-id",
    ...overrides,
  };
}

/**
 * Create a mock context for controller tests
 */
export function createMockContext(overrides = {}) {
  return {
    params: { id: "test-project-id" },
    query: {},
    body: {
      organization_id: "test-org-id",
      project_category: ProjectCategory.BRAND_STRATEGY,
      project_name: "Test Project",
      pic_project: "test-employee-id",
      start_project: "2023-01-01",
      end_project: "2023-12-31",
      status_project: ProjectStatus.NOT_STARTED,
      budget_project: "100000",
      objectives: "Test objectives",
    },
    user: { id: "test-user-id" },
    profile: { role: "Manager" },
    success: (data, message) => ({ success: true, data, message }),
    error: (message) => ({ success: false, error: { message } }),
    badRequest: (message, code) => ({
      success: false,
      error: { message, code },
    }),
    notFound: (message) => ({
      success: false,
      error: { message, code: "NOT_FOUND" },
    }),
    serverError: (message, error) => ({
      success: false,
      error: { message, details: error },
    }),
    ...overrides,
  };
}

/**
 * Set up database mocks for testing
 */
export function setupMocks(mocks = {}) {
  const mockProject = createMockProject();

  // Create a merged mocks object that properly overrides the defaults
  const mergedMocks = {
    create: async () => ({
      data: mockProject,
      error: null,
    }),
    getById: async (table, id) => {
      if (id === "test-project-id") {
        return { data: mockProject, error: null };
      }
      return { data: null, error: new Error("Project not found") };
    },
    getAll: async () => ({
      data: [mockProject],
      error: null,
      result: {
        total: 1,
        page: 1,
        pageSize: 10,
        pageCount: 1,
      },
    }),
    update: async () => ({
      data: createMockProject({
        project_name: "Updated Project",
      }),
      error: null,
    }),
    delete: async () => ({
      data: { id: "test-project-id" },
      error: null,
    }),
    ...mocks,
  };

  // Mock the database utils module
  mock.module("../../utils/database", () => {
    return {
      dbUtils: mergedMocks,
    };
  });

  // Return a function to reset the mocks
  return () => {
    mock.module("../../utils/database", () => ({
      dbUtils: mergedMocks,
    }));
  };
}
