import { describe, expect, it, mock, beforeEach, afterEach } from "bun:test";
import { SalaryUpdateHistoryService } from "../../modules/salary/salary-update-history.service";
import {
  createMockSalary,
  createMockSalaryHistory,
  setupMocks,
} from "./test-utils";
import { SalaryChangeItem } from "../../database/models/salary-update-history.model";
import { SalaryPaymentStatus } from "../../database/models/salary.model";

// Mock the Supabase client first to prevent real initialization
mock.module("../../libs/supabase", () => {
  return {
    supabase: {
      from: () => ({}),
    },
  };
});

describe("SalaryUpdateHistoryService", () => {
  // Reset mocks after each test
  let resetMocks: () => void;

  afterEach(() => {
    // Reset mocks to prevent test interference
    if (resetMocks) {
      resetMocks();
    }
  });

  describe("create", () => {
    it("should create a history record successfully", async () => {
      // ARRANGE
      const mockHistory = createMockSalaryHistory();
      resetMocks = setupMocks({
        create: () => Promise.resolve({ data: mockHistory, error: null }),
      });

      const historyData = {
        salary_id: "test-salary-id",
        change_description: JSON.stringify([
          {
            field: "base_salary",
            from_value: 5000000,
            to_value: 5500000,
          },
        ]),
      };

      // ACT
      const result = await SalaryUpdateHistoryService.create(
        historyData,
        "test-user-id"
      );

      // ASSERT
      expect(result.data).toBeDefined();
      expect(result.error).toBeNull();
    });

    it("should handle database errors when creating", async () => {
      // ARRANGE
      resetMocks = setupMocks({
        create: () =>
          Promise.resolve({
            data: null,
            error: new Error("Database error"),
          }),
      });

      const historyData = {
        salary_id: "test-salary-id",
        change_description: "[]",
      };

      // ACT
      const result = await SalaryUpdateHistoryService.create(
        historyData,
        "test-user-id"
      );

      // ASSERT
      expect(result.data).toBeNull();
      expect(result.error).toBeDefined();
      expect(result.error?.message).toContain("Database error");
    });
  });

  describe("getBySalaryId", () => {
    it("should get history records by salary ID successfully", async () => {
      // ARRANGE
      const mockHistory = createMockSalaryHistory();
      resetMocks = setupMocks({
        getByField: () => Promise.resolve({ data: [mockHistory], error: null }),
      });

      // ACT
      const result = await SalaryUpdateHistoryService.getBySalaryId(
        "test-salary-id"
      );

      // ASSERT
      expect(result.data).toBeDefined();
      expect(result.error).toBeNull();
      expect(result.data?.length).toBe(1);
      expect(result.data?.[0].salary_id).toBe("test-salary-id");
    });

    it("should handle database errors when getting by salary ID", async () => {
      // ARRANGE
      resetMocks = setupMocks({
        getByField: () =>
          Promise.resolve({
            data: null,
            error: new Error("Database error"),
          }),
      });

      // ACT
      const result = await SalaryUpdateHistoryService.getBySalaryId(
        "test-salary-id"
      );

      // ASSERT
      expect(result.data).toBeNull();
      expect(result.error).toBeDefined();
      expect(result.error?.message).toBeDefined();
    });
  });

  describe("compareSalaryChanges", () => {
    it("should detect changes between original and updated salary", () => {
      // ARRANGE
      const originalSalary = createMockSalary();
      const updateData = {
        base_salary: 5500000,
        payment_status: SalaryPaymentStatus.PAID,
      };

      // ACT
      const changes = SalaryUpdateHistoryService.compareSalaryChanges(
        originalSalary,
        updateData
      );

      // ASSERT
      expect(changes).toBeDefined();
      expect(changes?.length).toBe(2);

      // Check base_salary change (now with object structure)
      expect(changes?.[0].field).toBe("base_salary");
      expect(changes?.[0].changeType).toBe("field");
      expect(changes?.[0].subType).toBe("base_salary");
      expect(changes?.[0].from_value.amount).toBe(5000000);
      expect(changes?.[0].to_value.amount).toBe(5500000);
      expect(changes?.[0].from_value.total_salary).toBe(
        originalSalary.total_salary
      );
      expect(changes?.[0].to_value.total_salary).toBe(
        originalSalary.total_salary - 5000000 + 5500000
      );

      // Check payment_status change (simple field)
      expect(changes?.[1].field).toBe("payment_status");
      expect(changes?.[1].changeType).toBe("field");
      expect(changes?.[1].subType).toBe("simple");
      expect(changes?.[1].from_value).toBe("unpaid");
      expect(changes?.[1].to_value).toBe("paid");
    });

    it("should return null when no changes are detected", () => {
      // ARRANGE
      const originalSalary = createMockSalary();
      const updateData = {
        base_salary: 5000000,
        payment_status: SalaryPaymentStatus.UNPAID,
      };

      // ACT
      const changes = SalaryUpdateHistoryService.compareSalaryChanges(
        originalSalary,
        updateData
      );

      // ASSERT
      expect(changes).toBeNull();
    });

    it("should ignore undefined values in update data", () => {
      // ARRANGE
      const originalSalary = createMockSalary();
      const updateData = {
        base_salary: 5500000,
        payment_status: undefined,
      };

      // ACT
      const changes = SalaryUpdateHistoryService.compareSalaryChanges(
        originalSalary,
        updateData
      );

      // ASSERT
      expect(changes).toBeDefined();
      expect(changes?.length).toBe(1);
      expect(changes?.[0].field).toBe("base_salary");
    });
  });

  describe("trackSalaryUpdate", () => {
    it("should create a history record when changes are detected", async () => {
      // ARRANGE
      const mockHistory = createMockSalaryHistory();
      resetMocks = setupMocks({
        create: () => Promise.resolve({ data: mockHistory, error: null }),
      });

      const originalSalary = createMockSalary();
      const updateData = {
        base_salary: 5500000,
      };

      // ACT
      const result = await SalaryUpdateHistoryService.trackSalaryUpdate(
        "test-salary-id",
        originalSalary,
        updateData,
        "test-user-id"
      );

      // ASSERT
      expect(result.data).toBeDefined();
      expect(result.error).toBeNull();
    });

    it("should return null when no changes are detected", async () => {
      // ARRANGE
      const originalSalary = createMockSalary();
      const updateData = {
        base_salary: 5000000,
      };

      // ACT
      const result = await SalaryUpdateHistoryService.trackSalaryUpdate(
        "test-salary-id",
        originalSalary,
        updateData,
        "test-user-id"
      );

      // ASSERT
      expect(result.data).toBeNull();
      expect(result.error).toBeNull();
    });
  });

  describe("parseHistoryRecords", () => {
    it("should parse change descriptions in history records", async () => {
      // ARRANGE
      const changeDescription = JSON.stringify([
        {
          field: "base_salary",
          from_value: 5000000,
          to_value: 5500000,
        },
      ]);

      const historyRecords = [
        createMockSalaryHistory({ change_description: changeDescription }),
      ];

      // Mock the query function to return empty data for user profiles
      resetMocks = setupMocks({
        query: () => ({
          raw: {
            select: () => ({
              in: () => ({
                is: () => Promise.resolve({ data: [], error: null }),
              }),
            }),
          },
        }),
      });

      // ACT
      const parsedRecords =
        await SalaryUpdateHistoryService.parseHistoryRecords(historyRecords);

      // ASSERT
      expect(parsedRecords).toBeDefined();
      expect(parsedRecords.length).toBe(1);
      expect(parsedRecords[0].parsed_changes).toBeDefined();
      expect(parsedRecords[0].parsed_changes.length).toBe(1);
      expect(parsedRecords[0].parsed_changes[0].field).toBe("base_salary");
    });
  });

  describe("trackComponentUpdate", () => {
    it("should create a history record for component updates using new interface", async () => {
      // ARRANGE
      const mockHistory = createMockSalaryHistory();
      resetMocks = setupMocks({
        create: () => Promise.resolve({ data: mockHistory, error: null }),
      });

      // ACT
      const result = await SalaryUpdateHistoryService.trackComponentUpdate({
        salaryId: "test-salary-id",
        componentType: "bonus",
        action: "add",
        componentTypeName: "test-bonus-type",
        amount: 100000,
        oldTotalValue: 500000,
        newTotalValue: 600000,
        oldTotalSalary: 5000000,
        newTotalSalary: 5100000,
        userId: "test-user-id",
      });

      // ASSERT
      expect(result.data).toBeDefined();
      expect(result.error).toBeNull();
    });

    it("should create a history record for component updates using legacy interface", async () => {
      // ARRANGE
      const mockHistory = createMockSalaryHistory();
      resetMocks = setupMocks({
        create: () => Promise.resolve({ data: mockHistory, error: null }),
      });

      // ACT
      const result = await SalaryUpdateHistoryService.trackComponentUpdate(
        "test-salary-id",
        "bonus",
        "add",
        "test-bonus-type",
        100000,
        500000,
        600000,
        5000000, // oldTotalSalary
        5100000, // newTotalSalary
        "test-user-id"
      );

      // ASSERT
      expect(result.data).toBeDefined();
      expect(result.error).toBeNull();
    });
  });

  // We've removed trackTotalSalaryUpdate as we now include total salary changes
  // directly in component updates (bonus, deduction, allowance)
});
