import { describe, expect, it, mock, beforeEach, afterEach } from "bun:test";
import { Salary<PERSON>ontroller } from "../../modules/salary/controller";
import { SalaryService } from "../../modules/salary/service";
import { SalaryPaymentStatus } from "../../database/models/salary.model";
import { UserRole } from "../../database/models/user-profile.model";
import {
  createMockSalary,
  createMockContext,
  createMockFn,
} from "./test-utils";

// Mock the Supabase client first to prevent real initialization
mock.module("../../libs/supabase", () => {
  return {
    supabase: {
      from: () => ({}),
    },
  };
});

describe("SalaryController", () => {
  // Store original methods to restore after tests
  let originalGetById: typeof SalaryService.getById;
  let originalGetAll: typeof SalaryService.getAll;
  let originalGetByEmployeeId: typeof SalaryService.getByEmployeeId;
  let originalUpdateSalary: typeof SalaryService.updateSalary;

  beforeEach(() => {
    // Store original methods
    originalGetById = SalaryService.getById;
    originalGetAll = SalaryService.getAll;
    originalGetByEmployeeId = SalaryService.getByEmployeeId;
    originalUpdateSalary = SalaryService.updateSalary;
  });

  afterEach(() => {
    // Restore original methods
    SalaryService.getById = originalGetById;
    SalaryService.getAll = originalGetAll;
    SalaryService.getByEmployeeId = originalGetByEmployeeId;
    SalaryService.updateSalary = originalUpdateSalary;
  });

  describe("getById", () => {
    it("should get a salary by ID successfully", async () => {
      // ARRANGE
      const mockContext = createMockContext({
        params: { id: "test-salary-id" },
      });

      // Mock the service method
      SalaryService.getById = createMockFn(() =>
        Promise.resolve({
          data: createMockSalary(),
          error: null,
        })
      );

      // ACT
      const result = await SalaryController.getById(mockContext);

      // ASSERT
      expect(result.success).toBe(true);
      expect(result.data).toBeDefined();
      expect(result.data.id).toBe("test-salary-id");
    });

    it("should handle not found error", async () => {
      // ARRANGE
      const mockContext = createMockContext({
        params: { id: "test-salary-id" },
      });

      // Mock the service method
      SalaryService.getById = createMockFn(() =>
        Promise.resolve({
          data: null,
          error: null,
        })
      );

      // ACT
      const result = await SalaryController.getById(mockContext);

      // ASSERT
      expect(result.success).toBe(false);
      expect(result.message).toContain("not found");
    });

    it("should handle server error", async () => {
      // ARRANGE
      const mockContext = createMockContext({
        params: { id: "test-salary-id" },
      });

      // Mock the service method
      SalaryService.getById = createMockFn(() =>
        Promise.resolve({
          data: null,
          error: new Error("Test error"),
        })
      );

      // ACT
      const result = await SalaryController.getById(mockContext);

      // ASSERT
      expect(result.success).toBe(false);
      expect(result.error).toBeDefined();
      expect(result.error.message).toContain("Test error");
    });
  });

  describe("getAll", () => {
    it("should get all salaries successfully", async () => {
      // ARRANGE
      const mockContext = createMockContext({
        query: { page: "1", pageSize: "10" },
      });

      // Mock the service method
      SalaryService.getAll = createMockFn(() =>
        Promise.resolve({
          data: [createMockSalary()],
          result: {
            page: 1,
            pageSize: 10,
            total: 1,
            pageCount: 1,
          },
          error: null,
        })
      );

      // ACT
      const result = await SalaryController.getAll(mockContext);

      // ASSERT
      expect(result.success).toBe(true);
      expect(result.data).toBeDefined();
      expect(result.data.items).toBeDefined();
      expect(result.data.items.length).toBe(1);
      expect(result.data.pagination).toBeDefined();
    });

    it("should handle server error", async () => {
      // ARRANGE
      const mockContext = createMockContext({
        query: { page: "1", pageSize: "10" },
      });

      // Mock the service method
      SalaryService.getAll = createMockFn(() =>
        Promise.resolve({
          data: null,
          error: new Error("Test error"),
          result: null,
        })
      );

      // ACT
      const result = await SalaryController.getAll(mockContext);

      // ASSERT
      expect(result.success).toBe(false);
      expect(result.error).toBeDefined();
      expect(result.error.message).toContain("Test error");
    });
  });

  describe("getByEmployeeId", () => {
    it("should get salaries by employee ID successfully", async () => {
      // ARRANGE
      const mockContext = createMockContext({
        params: { employeeId: "test-employee-id" },
      });

      // Mock the service method
      SalaryService.getByEmployeeId = createMockFn(() =>
        Promise.resolve({
          data: [createMockSalary()],
          error: null,
        })
      );

      // ACT
      const result = await SalaryController.getByEmployeeId(mockContext);

      // ASSERT
      expect(result.success).toBe(true);
      expect(result.data).toBeDefined();
      expect(result.data.length).toBe(1);
      expect(result.data[0].employee_id).toBe("test-employee-id");
    });

    it("should handle server error", async () => {
      // ARRANGE
      const mockContext = createMockContext({
        params: { employeeId: "test-employee-id" },
      });

      // Mock the service method
      SalaryService.getByEmployeeId = createMockFn(() =>
        Promise.resolve({
          data: null,
          error: new Error("Test error"),
        })
      );

      // ACT
      const result = await SalaryController.getByEmployeeId(mockContext);

      // ASSERT
      expect(result.success).toBe(false);
      expect(result.error).toBeDefined();
      expect(result.error.message).toContain("Test error");
    });
  });

  describe("updateSalary", () => {
    it("should update a salary successfully", async () => {
      // ARRANGE
      const mockContext = createMockContext({
        params: { id: "test-salary-id" },
        body: { base_salary: 5500000 },
        user: { id: "test-user-id", profile: { role: UserRole.HR } },
      });

      // Mock the service method
      SalaryService.updateSalary = createMockFn(() =>
        Promise.resolve({
          data: createMockSalary({
            base_salary: 5500000,
            total_salary: 6100000,
          }),
          error: null,
        })
      );

      // ACT
      const result = await SalaryController.updateSalary(mockContext);

      // ASSERT
      expect(result.success).toBe(true);
      expect(result.data).toBeDefined();
      expect(result.data.base_salary).toBe(5500000);
    });

    it("should handle permission error", async () => {
      // ARRANGE
      const mockContext = createMockContext({
        params: { id: "test-salary-id" },
        body: { payment_status: SalaryPaymentStatus.PAID },
        user: { id: "test-user-id", profile: { role: UserRole.HR } },
      });

      // Mock the service method
      SalaryService.updateSalary = createMockFn(() =>
        Promise.resolve({
          data: null,
          error: new Error("HR cannot update payment status"),
        })
      );

      // ACT
      const result = await SalaryController.updateSalary(mockContext);

      // ASSERT
      expect(result.success).toBe(false);
      expect(result.error).toBeDefined();
      expect(result.message).toContain("HR cannot update payment status");
    });
  });
});
