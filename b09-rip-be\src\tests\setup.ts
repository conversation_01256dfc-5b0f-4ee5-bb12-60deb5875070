import { mock } from "bun:test";

// Mock Supabase client here to prevent it from being initialized with real credentials
// This needs to be done before any tests import the module
mock.module("@supabase/supabase-js", () => {
  return {
    createClient: () => ({
      from: () => ({
        select: () => ({
          eq: () => ({
            single: () => Promise.resolve({ data: null, error: null }),
            is: () => ({
              single: () => Promise.resolve({ data: null, error: null }),
              limit: () => Promise.resolve({ data: [], error: null }),
            }),
          }),
        }),
      }),
      auth: {
        getSession: () =>
          Promise.resolve({ data: { session: null }, error: null }),
        signInWithPassword: () => Promise.resolve({ data: null, error: null }),
        signOut: () => Promise.resolve({ error: null }),
      },
    }),
  };
});

// Mock our local supabase.ts file to use the mock client
mock.module("../../libs/supabase", () => {
  return {
    supabase: {
      from: () => ({
        select: () => ({
          eq: () => ({
            single: () => Promise.resolve({ data: null, error: null }),
          }),
        }),
      }),
      auth: {
        getSession: () =>
          Promise.resolve({ data: { session: null }, error: null }),
      },
    },
  };
});
