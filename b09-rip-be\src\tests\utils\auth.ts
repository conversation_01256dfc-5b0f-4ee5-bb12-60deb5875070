import { supabase } from "../../libs/supabase";
import { UserRole } from "../../database/models/user-profile.model";
import { v4 as uuidv4 } from "uuid";

/**
 * Create a test user with the specified roles
 * @param roles Array of user roles
 * @returns Object with user ID and auth token
 */
export async function createTestUser(roles: UserRole[] = [UserRole.Manager]) {
  // Generate a unique email to avoid conflicts
  const email = `test-${uuidv4()}@example.com`;
  const password = "Test123!";

  // Create the user in Supabase Auth
  const { data: authData, error: authError } = await supabase.auth.signUp({
    email,
    password,
  });

  if (authError || !authData.user) {
    throw new Error(`Failed to create test user: ${authError?.message}`);
  }

  const userId = authData.user.id;

  // Insert the user into the users table with the specified roles
  const { error: insertError } = await supabase.from("users").insert({
    id: userId,
    email,
    roles,
    is_active: true,
  });

  if (insertError) {
    // Clean up the auth user if we failed to insert into users table
    await supabase.auth.admin.deleteUser(userId);
    throw new Error(`Failed to insert test user: ${insertError.message}`);
  }

  // Sign in to get a token
  const { data: signInData, error: signInError } =
    await supabase.auth.signInWithPassword({
      email,
      password,
    });

  if (signInError || !signInData.session) {
    // Clean up
    await deleteTestUser(userId);
    throw new Error(`Failed to sign in test user: ${signInError?.message}`);
  }

  return {
    id: userId,
    email,
    token: signInData.session.access_token,
    roles,
  };
}

/**
 * Delete a test user
 * @param userId ID of the user to delete
 */
export async function deleteTestUser(userId: string) {
  // Delete from users table first (due to foreign key constraints)
  await supabase.from("users").delete().eq("id", userId);

  // Then delete from auth
  await supabase.auth.admin.deleteUser(userId);
}
