/**
 * Standard API response format for consistent error handling
 */
export interface ApiResponse<T = any> {
  success: boolean;
  message: string;
  data: T | null;
  error?: {
    code?: string;
    details?: any;
  };
}

/**
 * Create a success response
 * @param data The data to return
 * @param message Optional success message
 * @returns Standardized success response
 */
export function successResponse<T>(
  data: T,
  message = "Operation successful"
): ApiResponse<T> {
  return {
    success: true,
    message,
    data,
  };
}

/**
 * Create an error response
 * @param message Error message
 * @param data Optional data to include
 * @param errorCode Optional error code
 * @param details Optional error details
 * @returns Standardized error response
 */
export function errorResponse<T = null>(
  message: string,
  data: T | null = null,
  errorCode?: string,
  details?: any
): ApiResponse<T> {
  return {
    success: false,
    message,
    data,
    error: {
      code: errorCode,
      details,
    },
  };
}

/**
 * Middleware error handler for Elysia
 * Converts thrown errors to standardized API responses
 */
export function handleApiError(error: Error): ApiResponse {
  console.error("API Error:", error.message);

  // Check for known error types based on message
  const errorMessage = error.message || "An unexpected error occurred";

  if (
    errorMessage.includes("unauthorized") ||
    errorMessage.includes("unauthenticated")
  ) {
    return {
      success: false,
      message: errorMessage,
      data: null,
      error: {
        code: "UNAUTHORIZED",
        details: error.stack,
      },
    };
  }

  if (
    errorMessage.includes("forbidden") ||
    errorMessage.includes("permission")
  ) {
    return {
      success: false,
      message: errorMessage,
      data: null,
      error: {
        code: "FORBIDDEN",
        details: error.stack,
      },
    };
  }

  if (
    errorMessage.includes("not found") ||
    errorMessage.toLowerCase().includes("does not exist")
  ) {
    return {
      success: false,
      message: errorMessage,
      data: null,
      error: {
        code: "NOT_FOUND",
        details: error.stack,
      },
    };
  }

  if (
    errorMessage.toLowerCase().includes("bad request") ||
    errorMessage.toLowerCase().includes("invalid input") ||
    errorMessage.toLowerCase().includes("validation failed")
  ) {
    return {
      success: false,
      message: errorMessage,
      data: null,
      error: {
        code: "BAD_REQUEST",
        details: error.stack,
      },
    };
  }

  // Default error response (500)
  return {
    success: false,
    message: errorMessage,
    data: null,
    error: {
      code: "INTERNAL_SERVER_ERROR",
      details: error.stack,
    },
  };
}
