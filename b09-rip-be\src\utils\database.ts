import { supabase } from "../libs/supabase";
import {
  BaseRecord,
  DbOptions,
  FieldSearchConfig,
  FieldType,
  FilterOption,
  PaginatedResponse,
  PaginationOptions,
  PaginationResult,
  QueryOptions,
  SearchOptions,
  SortOption,
} from "./database.types";

/**
 * Check if a string looks like a UUID
 * This is a simple validation that checks for the basic UUID format
 * (8-4-4-4-12 characters with hyphens)
 */
function looksLikeUuid(str: string): boolean {
  const uuidRegex =
    /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i;
  return uuidRegex.test(str);
}

/**
 * Database utilities for common CRUD operations
 * Generic utilities without automatic user filtering but with automatic audit fields
 */
export const dbUtils = {
  /**
   * Create a new record with automatic audit fields
   * @param table The table name
   * @param data The data to insert
   * @param userId Optional user ID for audit fields
   * @returns The created record and any error
   */
  create: async <T extends BaseRecord>(
    table: string,
    data: Partial<T>,
    userId?: string
  ) => {
    const timestamp = new Date().toISOString();

    // Add audit fields
    const insertData = {
      ...data,
      created_at: timestamp,
      ...(userId ? { created_by: userId } : {}),
    };

    const { data: result, error } = await supabase
      .from(table)
      .insert(insertData)
      .select()
      .single();

    return { data: result as T, error };
  },

  /**
   * Get all records with optional search, filter, pagination, and sorting
   * @param table The table name
   * @param options Query options including search, filters, pagination, and sorting
   * @returns Records, pagination data, and any error
   */
  getAll: async <T extends BaseRecord>(
    table: string,
    options: QueryOptions = {}
  ): Promise<PaginatedResponse<T>> => {
    // Start with base query
    let query = supabase.from(table).select("*", { count: "exact" });

    // Apply soft delete filter unless includeSoftDeleted is true
    if (!options.includeSoftDeleted) {
      query = query.is("deleted_at", null);
    }

    // Apply search if provided
    if (
      options.search &&
      options.search.term &&
      options.search.fields.length > 0
    ) {
      const textFields: string[] = [];
      const uuidFields: string[] = [];

      // Categorize fields based on type
      options.search.fields.forEach((field) => {
        if (typeof field === "string") {
          // Check if it's likely a UUID field by name
          if (field.endsWith("_id") || field === "id") {
            uuidFields.push(field);
          } else {
            textFields.push(field);
          }
        } else {
          // Using typed field config
          if (field.type === FieldType.UUID) {
            uuidFields.push(field.field);
          } else {
            textFields.push(field.field);
          }
        }
      });

      // Build search clauses
      const searchClauses: string[] = [];

      // Add text field searches (always)
      if (textFields.length > 0) {
        const textSearches = textFields.map(
          (field) => `${field}.ilike.%${options.search!.term}%`
        );
        searchClauses.push(textSearches.join(","));
      }

      // Add UUID field searches ONLY if search term is a complete, valid UUID
      if (uuidFields.length > 0 && looksLikeUuid(options.search!.term)) {
        // If it looks like a UUID, do exact match only
        const uuidSearches = uuidFields.map(
          (field) => `${field}.eq.${options.search!.term}`
        );
        searchClauses.push(uuidSearches.join(","));
      }
      // No partial matching or LIKE operations on UUID fields

      // Apply OR search across all constructed clauses
      if (searchClauses.length > 0) {
        query = query.or(searchClauses.join(","));
      }
    }

    // Apply filters if provided
    if (options.filters && options.filters.length > 0) {
      options.filters.forEach((filter) => {
        const { field, value, operator = "eq" } = filter;

        switch (operator) {
          case "eq":
            query = query.eq(field, value);
            break;
          case "neq":
            query = query.neq(field, value);
            break;
          case "gt":
            query = query.gt(field, value);
            break;
          case "gte":
            query = query.gte(field, value);
            break;
          case "lt":
            query = query.lt(field, value);
            break;
          case "lte":
            query = query.lte(field, value);
            break;
          case "in":
            query = query.in(field, Array.isArray(value) ? value : [value]);
            break;
          case "is":
            query = query.is(field, value);
            break;
        }
      });
    }

    // Apply pagination if provided
    if (options.pagination) {
      const { page, pageSize } = options.pagination;
      const from = (page - 1) * pageSize;
      const to = from + pageSize - 1;
      query = query.range(from, to);
    }

    // Apply sorting if provided
    if (options.sort) {
      // Handle both single sort option and array of sort options
      const sortOptions = Array.isArray(options.sort)
        ? options.sort
        : [options.sort];

      // Apply each sort option in order
      sortOptions.forEach((sortOption, index) => {
        const { field, direction = "asc" } = sortOption;
        const ascending = direction === "asc";

        // For the first sort option, use order()
        if (index === 0) {
          query = query.order(field, { ascending });
        } else {
          // For subsequent sort options, use order() again (Supabase supports multiple order calls)
          query = query.order(field, { ascending });
        }
      });
    } else {
      // Apply default sorting by created_at in descending order (newest first) if no sort option provided
      query = query.order("created_at", { ascending: false });
    }

    // Execute the query
    const { data, error, count } = await query;

    // Prepare pagination result if pagination was requested
    const paginationResult =
      options.pagination && count !== null
        ? {
            total: Number(count),
            page: Number(options.pagination.page),
            pageSize: Number(options.pagination.pageSize),
            pageCount: Math.ceil(
              Number(count) / Number(options.pagination.pageSize)
            ),
          }
        : undefined;

    return {
      data: data as T[],
      error,
      result: paginationResult,
    };
  },

  /**
   * Get a record by ID
   * @param table The table name
   * @param id The record ID
   * @param options Additional options
   * @returns The record and any error
   */
  getById: async <T extends BaseRecord>(
    table: string,
    id: string,
    options: DbOptions = {}
  ) => {
    let query = supabase.from(table).select("*").eq("id", id);

    if (!options.includeSoftDeleted) {
      query = query.is("deleted_at", null);
    }

    const { data, error } = await query.single();
    return { data: data as T, error };
  },

  /**
   * Get records by a specific field value
   * @param table The table name
   * @param field The field to filter by
   * @param value The value to match
   * @param options Additional options
   * @returns The matching records and any error
   */
  getByField: async <T extends BaseRecord>(
    table: string,
    field: string,
    value: any,
    options: DbOptions = {}
  ) => {
    let query = supabase.from(table).select("*").eq(field, value);

    if (!options.includeSoftDeleted) {
      query = query.is("deleted_at", null);
    }

    const { data, error } = await query;
    return { data: data as T[], error };
  },

  /**
   * Update a record with automatic audit fields
   * @param table The table name
   * @param id The record ID
   * @param data The data to update
   * @param userId Optional user ID for audit fields
   * @returns The updated record and any error
   */
  update: async <T extends BaseRecord>(
    table: string,
    id: string,
    data: Partial<T>,
    userId?: string
  ) => {
    const timestamp = new Date().toISOString();

    // Add audit fields
    const updateData = {
      ...data,
      updated_at: timestamp,
      ...(userId ? { updated_by: userId } : {}),
    };

    const { data: result, error } = await supabase
      .from(table)
      .update(updateData)
      .eq("id", id)
      .is("deleted_at", null)
      .select()
      .single();

    return { data: result as T, error };
  },

  /**
   * Soft delete a record with automatic audit fields
   * @param table The table name
   * @param id The record ID
   * @param userId Optional user ID for audit fields
   * @returns The deleted record and any error
   */
  softDelete: async <T extends BaseRecord>(
    table: string,
    id: string,
    userId?: string
  ) => {
    const timestamp = new Date().toISOString();

    // Set audit fields for soft deletion
    const deleteData: Partial<BaseRecord> = {
      deleted_at: timestamp,
      ...(userId ? { deleted_by: userId } : {}),
    };

    const { data: result, error } = await supabase
      .from(table)
      .update(deleteData)
      .eq("id", id)
      .is("deleted_at", null)
      .select()
      .single();

    return { data: result as T, error };
  },

  /**
   * Hard delete a record (use with caution)
   * @param table The table name
   * @param id The record ID
   * @returns The result and any error
   */
  hardDelete: async (table: string, id: string) => {
    const { data, error } = await supabase.from(table).delete().eq("id", id);

    return { data, error };
  },

  /**
   * Custom query builder with additional utility methods
   * @param table The table name
   * @returns A query builder with extended methods
   */
  query: (table: string) => {
    return {
      /**
       * Get only soft-deleted records
       */
      getSoftDeleted: async <T extends BaseRecord>(
        filters: FilterOption[] = []
      ) => {
        let query = supabase
          .from(table)
          .select("*")
          .not("deleted_at", "is", null);

        // Apply additional filters if provided
        filters.forEach((filter) => {
          const { field, value, operator = "eq" } = filter;
          switch (operator) {
            case "eq":
              query = query.eq(field, value);
              break;
            // Add other operators if needed
          }
        });

        const { data, error } = await query;
        return { data: data as T[], error };
      },

      /**
       * Restore a soft-deleted record with automatic audit fields
       */
      restore: async <T extends BaseRecord>(id: string, userId?: string) => {
        const timestamp = new Date().toISOString();

        // Set audit fields for restoration
        const updateData: Partial<BaseRecord> = {
          deleted_at: null,
          deleted_by: null,
          updated_at: timestamp,
          ...(userId ? { updated_by: userId } : {}),
        };

        const { data, error } = await supabase
          .from(table)
          .update(updateData)
          .eq("id", id)
          .not("deleted_at", "is", null)
          .select()
          .single();

        return { data: data as T, error };
      },

      /**
       * Access to the raw builder for advanced queries
       */
      raw: supabase.from(table),
    };
  },
};
