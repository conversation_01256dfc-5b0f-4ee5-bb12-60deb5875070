/**
 * Base record interface with audit fields that all database models should extend
 */
export interface BaseRecord {
  id?: string;
  created_at?: string;
  created_by?: string;
  updated_at?: string | null;
  updated_by?: string | null;
  deleted_at?: string | null;
  deleted_by?: string | null;
}

/**
 * Options for database operations
 */
export interface DbOptions {
  /** Whether to include soft-deleted records */
  includeSoftDeleted?: boolean;
}

/**
 * Pagination options
 */
export interface PaginationOptions {
  /** Page number (1-based) */
  page: number;
  /** Number of items per page */
  pageSize: number;
}

/**
 * Pagination result
 */
export interface PaginationResult {
  /** Total number of items across all pages */
  total: number;
  /** Current page (1-based) */
  page: number;
  /** Number of items per page */
  pageSize: number;
  /** Total number of pages */
  pageCount: number;
}

/**
 * Field type for search operations
 */
export enum FieldType {
  Text = "text",
  UUID = "uuid",
  Number = "number",
  Date = "date",
  Boolean = "boolean",
}

/**
 * Field configuration for type-aware searching
 */
export interface FieldSearchConfig {
  field: string;
  type: FieldType;
}

/**
 * Search options
 */
export interface SearchOptions {
  /** Search term to look for */
  term: string;
  /** Fields to search in - can be string[] for all text search or FieldSearchConfig[] for type-aware search */
  fields: string[] | FieldSearchConfig[];
}

/**
 * Filter option
 */
export interface FilterOption {
  /** Field name to filter on */
  field: string;
  /** Value to filter by */
  value: any;
  /** Operator to use (default: eq) */
  operator?: "eq" | "neq" | "gt" | "gte" | "lt" | "lte" | "in" | "is";
}

/**
 * Sort option for query results
 */
export interface SortOption {
  /** Field name to sort by */
  field: string;
  /** Sort direction (default: asc) */
  direction?: "asc" | "desc";
}

/**
 * Extended query options
 */
export interface QueryOptions {
  /** Search parameters */
  search?: SearchOptions;
  /** Filter parameters */
  filters?: FilterOption[];
  /** Pagination parameters */
  pagination?: PaginationOptions;
  /** Sort parameters - can be a single sort option or an array for multi-field sorting */
  sort?: SortOption | SortOption[];
  /** Whether to include soft-deleted records */
  includeSoftDeleted?: boolean;
}

/**
 * Response with pagination
 */
export interface PaginatedResponse<T> {
  data: T[] | null;
  error: any;
  result?: PaginationResult;
}
