/**
 * Date utility functions for the application
 */
import {
  format,
  getISOWeek,
  getDay,
  addDays,
  differenceInDays,
} from "date-fns";
import { toZonedTime } from "date-fns-tz";

// Jakarta timezone constant
export const JAKARTA_TIMEZONE = "Asia/Jakarta";

// Simple cache for project week calculations
const projectWeekCache = new Map<string, number>();

/**
 * Get current date in Jakarta timezone (GMT+7)
 * @returns Current date in YYYY-MM-DD format
 */
export function getCurrentJakartaDate(): string {
  const jakartaTime = toZonedTime(new Date(), JAKARTA_TIMEZONE);
  return format(jakartaTime, "yyyy-MM-dd");
}

/**
 * Get current time in Jakarta timezone (GMT+7)
 * @returns Current time in HH:MM:SS format
 */
export function getCurrentJakartaTime(): string {
  const jakartaTime = toZonedTime(new Date(), JAKARTA_TIMEZONE);
  return format(jakartaTime, "HH:mm:ss");
}

/**
 * Get full Jakarta date and time
 * @returns Object with date and time properties
 */
export function getJakartaDateTime(): { date: string; time: string } {
  return {
    date: getCurrentJakartaDate(),
    time: getCurrentJakartaTime(),
  };
}

/**
 * Get current week number in Jakarta timezone
 * @returns Current ISO week number (1-53)
 */
export function getCurrentWeekNumber(): number {
  const jakartaTime = toZonedTime(new Date(), JAKARTA_TIMEZONE);
  return getISOWeek(jakartaTime);
}

/**
 * Get the start date (Monday) of the current week in Jakarta timezone
 * @returns Monday date in YYYY-MM-DD format
 */
export function getCurrentWeekStartDate(): string {
  const jakartaTime = toZonedTime(new Date(), JAKARTA_TIMEZONE);
  const dayOfWeek = getDay(jakartaTime); // 0 is Sunday, 1 is Monday, etc.
  const daysToMonday = dayOfWeek === 0 ? -6 : 1 - dayOfWeek; // If Sunday, go back 6 days, otherwise go back to Monday
  const monday = addDays(jakartaTime, daysToMonday);
  return format(monday, "yyyy-MM-dd");
}

/**
 * Get the end date (Sunday) of the current week in Jakarta timezone
 * @returns Sunday date in YYYY-MM-DD format
 */
export function getCurrentWeekEndDate(): string {
  const jakartaTime = toZonedTime(new Date(), JAKARTA_TIMEZONE);
  const dayOfWeek = getDay(jakartaTime); // 0 is Sunday, 1 is Monday, etc.
  const daysToMonday = dayOfWeek === 0 ? -6 : 1 - dayOfWeek; // If Sunday, go back 6 days, otherwise go back to Monday
  const monday = addDays(jakartaTime, daysToMonday);
  const sunday = addDays(monday, 6);
  return format(sunday, "yyyy-MM-dd");
}

/**
 * Get the current day of week in Jakarta timezone
 * @returns Day of week (0 = Sunday, 1 = Monday, ..., 6 = Saturday)
 */
export function getCurrentDayOfWeek(): number {
  const jakartaTime = toZonedTime(new Date(), JAKARTA_TIMEZONE);
  return getDay(jakartaTime);
}

/**
 * Check if today is Saturday in Jakarta timezone
 * @returns true if today is Saturday, false otherwise
 */
export function isSaturday(): boolean {
  return getCurrentDayOfWeek() === 6;
}

/**
 * Calculate week number relative to a project's start date
 * @param projectStartDate Project start date in YYYY-MM-DD format
 * @param currentDate Optional current date in YYYY-MM-DD format (defaults to today)
 * @returns Week number (1-based) relative to the project start
 */
export function getProjectWeekNumber(
  projectStartDate: string,
  currentDate?: string
): number {
  // Use cache for performance optimization
  const now = currentDate || getCurrentJakartaDate();
  const cacheKey = `${projectStartDate}_${now}`;

  // Check if result is in cache
  if (projectWeekCache.has(cacheKey)) {
    return projectWeekCache.get(cacheKey)!;
  }

  // Convert dates to Date objects
  const startDate = new Date(projectStartDate);
  const nowDate = new Date(now);

  // Find the Monday of the week that includes the start date
  const startDayOfWeek = getDay(startDate); // 0 is Sunday, 1 is Monday, etc.
  const daysToMonday = startDayOfWeek === 0 ? -6 : 1 - startDayOfWeek;
  const firstMonday = addDays(startDate, daysToMonday);

  // Find the Monday of the current week
  const currentDayOfWeek = getDay(nowDate);
  const daysToCurrentMonday = currentDayOfWeek === 0 ? -6 : 1 - currentDayOfWeek;
  const currentMonday = addDays(nowDate, daysToCurrentMonday);

  // Calculate weeks between first Monday and current Monday
  const diffTime = Math.abs(currentMonday.getTime() - firstMonday.getTime());
  const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
  const diffWeeks = Math.floor(diffDays / 7);

  // Add 1 to start from Week 1
  const weekNumber = diffWeeks + 1;

  // Cache the result
  projectWeekCache.set(cacheKey, weekNumber);

  return weekNumber;
}

/**
 * Get the start date of a specific project week
 * @param projectStartDate Project start date in YYYY-MM-DD format
 * @param weekNumber Project-specific week number (1-based)
 * @returns Start date in YYYY-MM-DD format
 */
export function getProjectWeekStartDate(
  projectStartDate: string,
  weekNumber: number
): string {
  // Find the Monday of the week that includes the start date
  const startDate = new Date(projectStartDate);
  const startDayOfWeek = getDay(startDate);
  const daysToMonday = startDayOfWeek === 0 ? -6 : 1 - startDayOfWeek;
  const firstMonday = addDays(startDate, daysToMonday);

  // For week 1, use the Monday of the start date's week
  if (weekNumber === 1) {
    return format(firstMonday, "yyyy-MM-dd");
  }

  // For subsequent weeks, calculate from the first Monday
  const daysToAdd = (weekNumber - 1) * 7;
  const weekStart = addDays(firstMonday, daysToAdd);
  return format(weekStart, "yyyy-MM-dd");
}

/**
 * Get the end date of a specific project week
 * @param projectStartDate Project start date in YYYY-MM-DD format
 * @param weekNumber Project-specific week number (1-based)
 * @returns End date in YYYY-MM-DD format
 */
export function getProjectWeekEndDate(
  projectStartDate: string,
  weekNumber: number
): string {
  // Find the Monday of the week that includes the start date
  const startDate = new Date(projectStartDate);
  const startDayOfWeek = getDay(startDate);
  const daysToMonday = startDayOfWeek === 0 ? -6 : 1 - startDayOfWeek;
  const firstMonday = addDays(startDate, daysToMonday);

  // For week 1, add 6 days to the Monday of the start date's week
  if (weekNumber === 1) {
    const weekEnd = addDays(firstMonday, 6);
    return format(weekEnd, "yyyy-MM-dd");
  }

  // For subsequent weeks, calculate from the first Monday
  const daysToAdd = (weekNumber - 1) * 7 + 6; // Add 6 days to get to the end of the week
  const weekEnd = addDays(firstMonday, daysToAdd);
  return format(weekEnd, "yyyy-MM-dd");
}

/**
 * Format a date range for display
 * @param startDate Start date in YYYY-MM-DD format
 * @param endDate End date in YYYY-MM-DD format
 * @returns Formatted date range (e.g., "Jan 1 - Jan 7, 2023")
 */
export function formatWeekDateRange(
  startDate: string,
  endDate: string
): string {
  const start = new Date(startDate);
  const end = new Date(endDate);

  // If same year, only show year once at the end
  if (start.getFullYear() === end.getFullYear()) {
    return `${format(start, "MMM d")} - ${format(end, "MMM d, yyyy")}`;
  }

  // If different years, show both years
  return `${format(start, "MMM d, yyyy")} - ${format(end, "MMM d, yyyy")}`;
}
