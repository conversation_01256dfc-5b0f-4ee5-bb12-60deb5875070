/**
 * Scheduler utilities for managing background tasks
 */
import { supabase } from "../libs/supabase";

// Lock timeout in milliseconds (10 minutes)
const LOCK_TIMEOUT = 10 * 60 * 1000;

/**
 * Acquire a scheduler lock
 * @param lockName Name of the lock to acquire
 * @param lockerId Unique identifier for the locker
 * @returns true if lock was acquired, false otherwise
 */
export async function acquireSchedulerLock(
  lockName: string,
  lockerId: string
): Promise<boolean> {
  try {
    // Try to insert a new lock record
    const { data, error } = await supabase
      .from('scheduler_locks')
      .insert({
        lock_name: lockName,
        locked_at: new Date().toISOString(),
        locked_by: lockerId
      })
      .select('id')
      .single();
    
    if (error) {
      // Check if it's a unique constraint violation (lock already exists)
      if (error.code === '23505') {
        // Check if the lock is stale (older than the timeout)
        const { data: existingLock } = await supabase
          .from('scheduler_locks')
          .select('locked_at')
          .eq('lock_name', lockName)
          .single();
        
        if (existingLock) {
          const lockedAt = new Date(existingLock.locked_at);
          const now = new Date();
          const lockAge = now.getTime() - lockedAt.getTime();
          
          if (lockAge > LOCK_TIMEOUT) {
            // Lock is stale, try to update it
            const { error: updateError } = await supabase
              .from('scheduler_locks')
              .update({
                locked_at: now.toISOString(),
                locked_by: lockerId
              })
              .eq('lock_name', lockName);
            
            return !updateError;
          }
        }
        
        // Lock exists and is not stale
        return false;
      }
      
      // Other error
      console.error(`Error acquiring lock ${lockName}:`, error);
      return false;
    }
    
    // Successfully acquired lock
    return true;
  } catch (error) {
    console.error(`Error in acquireSchedulerLock:`, error);
    return false;
  }
}

/**
 * Release a scheduler lock
 * @param lockName Name of the lock to release
 * @param lockerId Unique identifier for the locker
 * @returns true if lock was released, false otherwise
 */
export async function releaseSchedulerLock(
  lockName: string,
  lockerId: string
): Promise<boolean> {
  try {
    // Delete the lock if it belongs to this locker
    const { error } = await supabase
      .from('scheduler_locks')
      .delete()
      .eq('lock_name', lockName)
      .eq('locked_by', lockerId);
    
    if (error) {
      console.error(`Error releasing lock ${lockName}:`, error);
      return false;
    }
    
    return true;
  } catch (error) {
    console.error(`Error in releaseSchedulerLock:`, error);
    return false;
  }
}

/**
 * Record a scheduler run
 * @param schedulerName Name of the scheduler
 * @param status Status of the run (success/failure)
 * @param details Additional details about the run
 */
export async function recordSchedulerRun(
  schedulerName: string,
  status: 'success' | 'failure',
  details?: any
): Promise<void> {
  try {
    await supabase
      .from('scheduler_runs')
      .insert({
        scheduler_name: schedulerName,
        run_at: new Date().toISOString(),
        status,
        details: details || {}
      });
  } catch (error) {
    console.error(`Error recording scheduler run:`, error);
  }
}
