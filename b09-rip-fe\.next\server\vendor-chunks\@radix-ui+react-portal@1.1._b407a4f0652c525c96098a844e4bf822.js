"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@radix-ui+react-portal@1.1._b407a4f0652c525c96098a844e4bf822";
exports.ids = ["vendor-chunks/@radix-ui+react-portal@1.1._b407a4f0652c525c96098a844e4bf822"];
exports.modules = {

/***/ "(ssr)/./node_modules/.pnpm/@radix-ui+react-portal@1.1._b407a4f0652c525c96098a844e4bf822/node_modules/@radix-ui/react-portal/dist/index.mjs":
/*!********************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@radix-ui+react-portal@1.1._b407a4f0652c525c96098a844e4bf822/node_modules/@radix-ui/react-portal/dist/index.mjs ***!
  \********************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Portal: () => (/* binding */ Portal),\n/* harmony export */   Root: () => (/* binding */ Root)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/.pnpm/next@15.2.4_@playwright+tes_c2d897af77fd2199926485f21048dc2c/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react_dom__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react-dom */ \"(ssr)/./node_modules/.pnpm/next@15.2.4_@playwright+tes_c2d897af77fd2199926485f21048dc2c/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-dom.js\");\n/* harmony import */ var _radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @radix-ui/react-primitive */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-primitive@2_b07e9eb388a28a89b30b98878142c98a/node_modules/@radix-ui/react-primitive/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_use_layout_effect__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @radix-ui/react-use-layout-effect */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-use-layout-_11a853f13bd1148e6de52e0c2c678182/node_modules/@radix-ui/react-use-layout-effect/dist/index.mjs\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react/jsx-runtime */ \"(ssr)/./node_modules/.pnpm/next@15.2.4_@playwright+tes_c2d897af77fd2199926485f21048dc2c/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-runtime.js\");\n/* __next_internal_client_entry_do_not_use__ Portal,Root auto */ // packages/react/portal/src/portal.tsx\n\n\n\n\n\nvar PORTAL_NAME = \"Portal\";\nvar Portal = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { container: containerProp, ...portalProps } = props;\n    const [mounted, setMounted] = react__WEBPACK_IMPORTED_MODULE_0__.useState(false);\n    (0,_radix_ui_react_use_layout_effect__WEBPACK_IMPORTED_MODULE_3__.useLayoutEffect)({\n        \"Portal.useLayoutEffect\": ()=>setMounted(true)\n    }[\"Portal.useLayoutEffect\"], []);\n    const container = containerProp || mounted && globalThis?.document?.body;\n    return container ? /*#__PURE__*/ react_dom__WEBPACK_IMPORTED_MODULE_1__.createPortal(/* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_4__.Primitive.div, {\n        ...portalProps,\n        ref: forwardedRef\n    }), container) : null;\n});\nPortal.displayName = PORTAL_NAME;\nvar Root = Portal;\n //# sourceMappingURL=index.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@radix-ui+react-portal@1.1._b407a4f0652c525c96098a844e4bf822/node_modules/@radix-ui/react-portal/dist/index.mjs\n");

/***/ })

};
;