"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/layout",{

/***/ "(app-pages-browser)/./src/hooks/auth/useAuth.ts":
/*!***********************************!*\
  !*** ./src/hooks/auth/useAuth.ts ***!
  \***********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   useAuth: () => (/* binding */ useAuth)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.2.4_@playwright+tes_c2d897af77fd2199926485f21048dc2c/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.2.4_@playwright+tes_c2d897af77fd2199926485f21048dc2c/node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _lib_api_auth__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/api/auth */ \"(app-pages-browser)/./src/lib/api/auth.ts\");\n/* harmony import */ var _lib_auth_jwt__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/auth/jwt */ \"(app-pages-browser)/./src/lib/auth/jwt.ts\");\n/* harmony import */ var _lib_store_auth_store__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/lib/store/auth-store */ \"(app-pages-browser)/./src/lib/store/auth-store.tsx\");\n/* harmony import */ var _lib_auth_auth_service__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/lib/auth/auth-service */ \"(app-pages-browser)/./src/lib/auth/auth-service.ts\");\n/* harmony import */ var sonner__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! sonner */ \"(app-pages-browser)/./node_modules/.pnpm/sonner@2.0.1_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/sonner/dist/index.mjs\");\n\n\n\n\n\n\n\nfunction useAuth() {\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(false);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n    const [accountInactive, setAccountInactive] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)({\n        \"useAuth.useState\": ()=>{\n            // Initialize from localStorage if available (client-side only)\n            if (true) {\n                // Get the stored value\n                const storedValue = localStorage.getItem('accountInactive') === 'true';\n                // If the value is true, we'll verify it with an API call later via checkAuth/checkAccountActivation\n                // But for initial rendering, return the stored value\n                return storedValue;\n            }\n            return false;\n        }\n    }[\"useAuth.useState\"]);\n    const [profileIncomplete, setProfileIncomplete] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(false);\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_1__.useRouter)();\n    const { login } = (0,_lib_store_auth_store__WEBPACK_IMPORTED_MODULE_4__.useAuthStore)();\n    // Helper function to set account inactive state and persist it\n    const setAccountInactiveStatus = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useAuth.useCallback[setAccountInactiveStatus]\": (status)=>{\n            setAccountInactive(status);\n            if (true) {\n                if (status) {\n                    localStorage.setItem('accountInactive', 'true');\n                } else {\n                    localStorage.removeItem('accountInactive');\n                }\n            }\n        }\n    }[\"useAuth.useCallback[setAccountInactiveStatus]\"], []);\n    /**\n   * Helper function to extract and set the correct role from JWT\n   */ const getProfileWithCorrectRole = (profile)=>{\n        var _jwtPayload_user_metadata;\n        const jwtPayload = _lib_auth_jwt__WEBPACK_IMPORTED_MODULE_3__.JWTManager.getPayload();\n        const roleFromJWT = jwtPayload === null || jwtPayload === void 0 ? void 0 : (_jwtPayload_user_metadata = jwtPayload.user_metadata) === null || _jwtPayload_user_metadata === void 0 ? void 0 : _jwtPayload_user_metadata.role;\n        if (roleFromJWT && (!profile.role || profile.role !== roleFromJWT)) {\n            return {\n                ...profile,\n                role: roleFromJWT\n            };\n        }\n        return profile;\n    };\n    /**\n   * Helper function to handle authentication success and check profile status\n   */ const handleAuthSuccess = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useAuth.useCallback[handleAuthSuccess]\": function(profile, access_token, refresh_token, responseData) {\n            let autoRedirect = arguments.length > 4 && arguments[4] !== void 0 ? arguments[4] : true;\n            // Update profile with correct role from JWT\n            const updatedProfile = getProfileWithCorrectRole(profile);\n            // Add profileComplete property to the profile from the response data\n            // This ensures the property is stored with the user profile\n            const profileWithCompletionStatus = {\n                ...updatedProfile,\n                profileComplete: responseData.profileComplete\n            };\n            // Check profile completion status\n            const isProfileComplete = responseData.profileComplete !== false;\n            setProfileIncomplete(!isProfileComplete);\n            // Update tokens in centralized auth service\n            _lib_auth_auth_service__WEBPACK_IMPORTED_MODULE_5__.authService.updateTokens(access_token, refresh_token);\n            // Update auth store with profile that includes profileComplete\n            login(profileWithCompletionStatus, access_token, refresh_token);\n            // Only redirect if autoRedirect is true\n            if (autoRedirect) {\n                // Redirect based on profile completion\n                if (!isProfileComplete) {\n                    router.push('/update-account');\n                } else {\n                    router.push('/dashboard');\n                }\n            }\n            return true;\n        }\n    }[\"useAuth.useCallback[handleAuthSuccess]\"], [\n        login,\n        router,\n        setProfileIncomplete\n    ]);\n    /**\n   * Sign in with email and password\n   */ const signIn = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useAuth.useCallback[signIn]\": async function(credentials) {\n            let autoRedirect = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : true;\n            setLoading(true);\n            setError(null);\n            setAccountInactiveStatus(false);\n            setProfileIncomplete(false);\n            try {\n                const response = await _lib_api_auth__WEBPACK_IMPORTED_MODULE_2__.authApi.signIn(credentials);\n                if (response.success) {\n                    const { session, profile } = response.data;\n                    const { access_token, refresh_token } = session;\n                    // Update tokens in centralized auth service\n                    _lib_auth_auth_service__WEBPACK_IMPORTED_MODULE_5__.authService.updateTokens(access_token, refresh_token);\n                    // Check if account is active by calling the profile endpoint\n                    try {\n                        const profileResponse = await _lib_api_auth__WEBPACK_IMPORTED_MODULE_2__.authApi.getProfile();\n                        // If we get here, account is active\n                        return handleAuthSuccess(profile, access_token, refresh_token, profileResponse.data, autoRedirect);\n                    } catch (profileErr) {\n                        var _apiError_response, _apiError_response_data_error, _apiError_response_data, _apiError_response1;\n                        // Check if it's the inactive account error\n                        const apiError = profileErr;\n                        if (((_apiError_response = apiError.response) === null || _apiError_response === void 0 ? void 0 : _apiError_response.status) === 403 && ((_apiError_response1 = apiError.response) === null || _apiError_response1 === void 0 ? void 0 : (_apiError_response_data = _apiError_response1.data) === null || _apiError_response_data === void 0 ? void 0 : (_apiError_response_data_error = _apiError_response_data.error) === null || _apiError_response_data_error === void 0 ? void 0 : _apiError_response_data_error.code) === 'ACCOUNT_INACTIVE') {\n                            setAccountInactiveStatus(true);\n                            setError('Akun Anda belum diaktivasi. Mohon tunggu admin untuk mengaktivasi akun Anda.');\n                            // Don't remove the tokens so user doesn't have to log in again when activated\n                            return false;\n                        }\n                        // For other errors, log out and show error\n                        _lib_auth_auth_service__WEBPACK_IMPORTED_MODULE_5__.authService.logout();\n                        throw profileErr;\n                    }\n                } else {\n                    setError(response.message || 'Login gagal');\n                    return false;\n                }\n            } catch (err) {\n                // Improved error handling for different error types\n                const apiError = err;\n                if (apiError.response) {\n                    // The request was made and the server responded with a status code\n                    // that falls out of the range of 2xx\n                    if (apiError.response.status === 401) {\n                        setError('Email atau password salah. Silakan coba lagi.');\n                    } else if (apiError.response.status === 429) {\n                        setError('Terlalu banyak percobaan login. Silakan coba lagi nanti.');\n                    } else {\n                        var _apiError_response_data1;\n                        const message = ((_apiError_response_data1 = apiError.response.data) === null || _apiError_response_data1 === void 0 ? void 0 : _apiError_response_data1.message) || 'Terjadi kesalahan saat login';\n                        setError(message);\n                    }\n                } else if (apiError.request) {\n                    // The request was made but no response was received\n                    setError('Tidak dapat terhubung ke server. Periksa koneksi Anda.');\n                } else {\n                    // Something happened in setting up the request that triggered an Error\n                    setError('Terjadi kesalahan saat login');\n                }\n                return false;\n            } finally{\n                setLoading(false);\n            }\n        }\n    }[\"useAuth.useCallback[signIn]\"], [\n        handleAuthSuccess,\n        setAccountInactiveStatus\n    ]);\n    /**\n   * Register a new user\n   */ const signUp = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useAuth.useCallback[signUp]\": async function(userData) {\n            let autoRedirect = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : true;\n            setLoading(true);\n            setError(null);\n            // Clear any inactive account status when registering a new user\n            setAccountInactiveStatus(false);\n            try {\n                const response = await _lib_api_auth__WEBPACK_IMPORTED_MODULE_2__.authApi.signUp(userData);\n                if (response.success) {\n                    // Clear inactive account flag in localStorage to prevent it from persisting\n                    if (true) {\n                        localStorage.removeItem('accountInactive');\n                    }\n                    // Only redirect if autoRedirect is true\n                    if (autoRedirect) {\n                        router.push('/?registered=true');\n                    }\n                    return true;\n                } else {\n                    // Capture the error message from the unsuccessful response\n                    setError(response.message || 'Registrasi gagal');\n                    return false;\n                }\n            } catch (err) {\n                var _apiError_response_data, _apiError_response;\n                // Improved error handling to better capture API error messages\n                const apiError = err;\n                const errorMessage = ((_apiError_response = apiError.response) === null || _apiError_response === void 0 ? void 0 : (_apiError_response_data = _apiError_response.data) === null || _apiError_response_data === void 0 ? void 0 : _apiError_response_data.message) || // Direct message from API\n                apiError.message || // Error object message\n                'Terjadi kesalahan saat registrasi';\n                setError(errorMessage);\n                return false;\n            } finally{\n                setLoading(false);\n            }\n        }\n    }[\"useAuth.useCallback[signUp]\"], [\n        router,\n        setAccountInactiveStatus\n    ]);\n    /**\n   * Sign out the current user\n   */ const signOut = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useAuth.useCallback[signOut]\": async ()=>{\n            setLoading(true);\n            try {\n                // Try to call the sign-out API endpoint\n                await _lib_api_auth__WEBPACK_IMPORTED_MODULE_2__.authApi.signOut();\n            } catch (err) {\n                // Log error but continue with logout process regardless of API failure\n                console.error('Error during sign out:', err);\n            }\n            // Use centralized auth service to logout\n            _lib_auth_auth_service__WEBPACK_IMPORTED_MODULE_5__.authService.logout();\n            // Remove account inactive flag\n            setAccountInactiveStatus(false);\n            // Reset states\n            setProfileIncomplete(false);\n            // Set loading to false before showing toast\n            setLoading(false);\n            // Show success toast notification with explicit top-right position\n            sonner__WEBPACK_IMPORTED_MODULE_6__.toast.success('Berhasil keluar dari sistem', {\n                position: 'top-right',\n                duration: 4000,\n                id: 'logout-toast',\n                style: {\n                    fontWeight: 'bold'\n                }\n            });\n            // Add a delay before redirecting to ensure toast is visible\n            setTimeout({\n                \"useAuth.useCallback[signOut]\": ()=>{\n                    router.push('/');\n                }\n            }[\"useAuth.useCallback[signOut]\"], 1500);\n        }\n    }[\"useAuth.useCallback[signOut]\"], [\n        router,\n        setAccountInactiveStatus\n    ]);\n    /**\n   * Check if the user is authenticated\n   */ const checkAuth = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useAuth.useCallback[checkAuth]\": async ()=>{\n            // Check if token is valid\n            if (!_lib_auth_jwt__WEBPACK_IMPORTED_MODULE_3__.JWTManager.isAccessTokenValid()) {\n                try {\n                    // Use centralized auth service to refresh tokens\n                    const refreshSuccess = await _lib_auth_auth_service__WEBPACK_IMPORTED_MODULE_5__.authService.refreshTokens();\n                    if (!refreshSuccess) {\n                        console.log('Token refresh failed during checkAuth');\n                        return false;\n                    }\n                    console.log('Token refresh successful during checkAuth');\n                    // Token refresh was successful, now check if account is active\n                    try {\n                        const profileResponse = await _lib_api_auth__WEBPACK_IMPORTED_MODULE_2__.authApi.getProfile();\n                        const profileData = profileResponse.data;\n                        // Account is active, check profile completion\n                        const isProfileComplete = profileData.profileComplete !== false;\n                        setProfileIncomplete(!isProfileComplete);\n                        // Get tokens from auth service\n                        const accessToken = _lib_auth_auth_service__WEBPACK_IMPORTED_MODULE_5__.authService.getAccessToken() || '';\n                        const refreshToken = _lib_auth_auth_service__WEBPACK_IMPORTED_MODULE_5__.authService.getRefreshToken() || '';\n                        // Get profile from response\n                        const profile = profileData.profile || {};\n                        // Update profile with correct role from JWT and add profileComplete\n                        const updatedProfile = {\n                            ...getProfileWithCorrectRole(profile),\n                            profileComplete: profileData.profileComplete\n                        };\n                        // Update auth store\n                        login(updatedProfile, accessToken, refreshToken);\n                        return true;\n                    } catch (profileErr) {\n                        var _apiError_response, _apiError_response_data_error, _apiError_response_data, _apiError_response1;\n                        const apiError = profileErr;\n                        // Check if it's the inactive account error\n                        if (((_apiError_response = apiError.response) === null || _apiError_response === void 0 ? void 0 : _apiError_response.status) === 403 && ((_apiError_response1 = apiError.response) === null || _apiError_response1 === void 0 ? void 0 : (_apiError_response_data = _apiError_response1.data) === null || _apiError_response_data === void 0 ? void 0 : (_apiError_response_data_error = _apiError_response_data.error) === null || _apiError_response_data_error === void 0 ? void 0 : _apiError_response_data_error.code) === 'ACCOUNT_INACTIVE') {\n                            setAccountInactiveStatus(true);\n                            setError('Akun Anda belum diaktivasi. Mohon tunggu admin untuk mengaktivasi akun Anda.');\n                            return false;\n                        }\n                        // For other errors, logout\n                        _lib_auth_auth_service__WEBPACK_IMPORTED_MODULE_5__.authService.logout();\n                        return false;\n                    }\n                } catch (err) {\n                    console.error('Error in checkAuth:', err);\n                    // Logout if refresh fails\n                    _lib_auth_auth_service__WEBPACK_IMPORTED_MODULE_5__.authService.logout();\n                    return false;\n                }\n            }\n            // If token is valid but we don't have user info, fetch it\n            if (_lib_auth_jwt__WEBPACK_IMPORTED_MODULE_3__.JWTManager.isAccessTokenValid() && !_lib_store_auth_store__WEBPACK_IMPORTED_MODULE_4__.useAuthStore.getState().user) {\n                try {\n                    const profileResponse = await _lib_api_auth__WEBPACK_IMPORTED_MODULE_2__.authApi.getProfile();\n                    if (profileResponse.success) {\n                        const accessToken = _lib_auth_auth_service__WEBPACK_IMPORTED_MODULE_5__.authService.getAccessToken() || '';\n                        const refreshToken = _lib_auth_auth_service__WEBPACK_IMPORTED_MODULE_5__.authService.getRefreshToken() || '';\n                        const profileData = profileResponse.data;\n                        // Check profile completion status\n                        const isProfileComplete = profileData.profileComplete !== false;\n                        setProfileIncomplete(!isProfileComplete);\n                        // Update profile with correct role from JWT and add profileComplete\n                        const updatedProfile = {\n                            ...getProfileWithCorrectRole(profileData.profile),\n                            profileComplete: profileData.profileComplete\n                        };\n                        login(updatedProfile, accessToken, refreshToken);\n                        return true;\n                    }\n                    return false;\n                } catch (err) {\n                    var _apiError_response2, _apiError_response_data_error1, _apiError_response_data1, _apiError_response3;\n                    const apiError = err;\n                    // Check if it's the inactive account error\n                    if (((_apiError_response2 = apiError.response) === null || _apiError_response2 === void 0 ? void 0 : _apiError_response2.status) === 403 && ((_apiError_response3 = apiError.response) === null || _apiError_response3 === void 0 ? void 0 : (_apiError_response_data1 = _apiError_response3.data) === null || _apiError_response_data1 === void 0 ? void 0 : (_apiError_response_data_error1 = _apiError_response_data1.error) === null || _apiError_response_data_error1 === void 0 ? void 0 : _apiError_response_data_error1.code) === 'ACCOUNT_INACTIVE') {\n                        setAccountInactiveStatus(true);\n                        setError('Akun Anda belum diaktivasi. Mohon tunggu admin untuk mengaktivasi akun Anda.');\n                        return false;\n                    }\n                    return false;\n                }\n            }\n            return _lib_store_auth_store__WEBPACK_IMPORTED_MODULE_4__.useAuthStore.getState().isAuthenticated;\n        }\n    }[\"useAuth.useCallback[checkAuth]\"], [\n        login,\n        setAccountInactiveStatus,\n        setError\n    ]);\n    /**\n   * Check if account is active by calling the profile endpoint\n   */ const checkAccountActivation = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useAuth.useCallback[checkAccountActivation]\": async ()=>{\n            try {\n                const profileResponse = await _lib_api_auth__WEBPACK_IMPORTED_MODULE_2__.authApi.getProfile();\n                // If this succeeds, account is active\n                setAccountInactiveStatus(false);\n                setError(null); // Clear any existing error messages\n                // Since the account is now active, set up the session properly\n                const accessToken = _lib_auth_jwt__WEBPACK_IMPORTED_MODULE_3__.JWTManager.getAccessToken() || '';\n                const refreshToken = _lib_auth_jwt__WEBPACK_IMPORTED_MODULE_3__.JWTManager.getRefreshToken() || '';\n                if (accessToken && refreshToken && profileResponse.success) {\n                    const profileData = profileResponse.data;\n                    // Check profile completion status\n                    const isProfileComplete = profileData.profileComplete !== false;\n                    setProfileIncomplete(!isProfileComplete);\n                    // Update profile with correct role from JWT and add profileComplete\n                    const updatedProfile = {\n                        ...getProfileWithCorrectRole(profileData.profile),\n                        profileComplete: profileData.profileComplete\n                    };\n                    // Set up the full authenticated session\n                    login(updatedProfile, accessToken, refreshToken);\n                }\n                return true;\n            } catch (err) {\n                var _apiError_response, _apiError_response_data_error, _apiError_response_data, _apiError_response1;\n                const apiError = err;\n                // Check if it's still the inactive account error\n                if (((_apiError_response = apiError.response) === null || _apiError_response === void 0 ? void 0 : _apiError_response.status) === 403 && ((_apiError_response1 = apiError.response) === null || _apiError_response1 === void 0 ? void 0 : (_apiError_response_data = _apiError_response1.data) === null || _apiError_response_data === void 0 ? void 0 : (_apiError_response_data_error = _apiError_response_data.error) === null || _apiError_response_data_error === void 0 ? void 0 : _apiError_response_data_error.code) === 'ACCOUNT_INACTIVE') {\n                    setAccountInactiveStatus(true);\n                    return false;\n                }\n                // For other errors, probably not authenticated or server issue\n                return false;\n            }\n        }\n    }[\"useAuth.useCallback[checkAccountActivation]\"], [\n        login,\n        setAccountInactiveStatus,\n        setError,\n        setProfileIncomplete\n    ]);\n    return {\n        signIn,\n        signUp,\n        signOut,\n        checkAuth,\n        checkAccountActivation,\n        loading,\n        error,\n        accountInactive,\n        profileIncomplete,\n        isAuthenticated: _lib_store_auth_store__WEBPACK_IMPORTED_MODULE_4__.useAuthStore.getState().isAuthenticated,\n        user: _lib_store_auth_store__WEBPACK_IMPORTED_MODULE_4__.useAuthStore.getState().user\n    };\n}\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (useAuth);\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/hooks/auth/useAuth.ts\n"));

/***/ })

});