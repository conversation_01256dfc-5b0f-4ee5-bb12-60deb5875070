# Kasuat Frontend

Modern, high-performance ERP system frontend built with Next.js and React.

## Features

- 🚀 Next.js 15 with App Router for optimal performance
- ⚛️ React 19 for modern UI development
- 🎨 shadcn/ui for robust component system
- 🔐 JWT-based authentication with role management
- 📦 React Query for efficient data fetching
- 🗃️ Zustand for state management
- 💅 Tailwind CSS for styling
- 🧪 Comprehensive testing suite (Vitest + Playwright)
- 🐳 Docker support

## Prerequisites

- Node.js >= 22.14.0 (LTS)
- pnpm >= 10.4.0
- Docker (optional)

## Getting Started

### Installation

1. Clone the repository:

```bash
git clone https://gitlab.cs.ui.ac.id/propensi-2024-2025-genap/kelas-b/b09-rip-fe.git
cd b09-rip-fe
```

2. Install dependencies:

```bash
pnpm install
```

3. Set up environment variables:

```bash
cp .env.example .env.local
```

4. Start development server:

```bash
pnpm dev
```

The application will be available at `http://localhost:3001`.

## Implementation Progress

See detailed [Initiation Plan](initiation-plan.md) for full specifications.

Current Phase: Core Setup

### Phase 1: Core Setup

- [x] Initialize project structure
- [x] Configure development environment
- [x] Set up Docker configuration
- [ ] Configure authentication
- [ ] Set up component system
- [ ] Add API integration

### Phase 2: Feature Development

- [ ] User management
- [ ] Role management
- [ ] Basic CRUD modules
- [ ] API documentation

### Phase 3: Testing & Deployment

- [ ] Unit tests
- [ ] Integration tests
- [ ] CI/CD pipeline
- [ ] Production deployment

### Phase 4: Optimization

- [ ] Performance tuning
- [ ] Security hardening
- [ ] Monitoring enhancement
- [ ] Documentation updates

## Project Structure

```
src/
├── app/                    # Next.js app router
│   ├── layout.tsx         # Root layout
│   ├── page.tsx           # Home page
│   └── (auth)/           # Auth group
│       ├── login/        # Login route
│       └── register/     # Register route
├── components/            # UI components
│   ├── ui/               # shadcn components
│   └── shared/           # Common components
├── lib/                  # Core utilities
│   ├── auth/            # Auth utilities
│   ├── api/             # API utilities
│   └── utils/           # Helper functions
├── hooks/               # Custom hooks
│   ├── auth/           # Auth hooks
│   ├── queries/        # React Query hooks
│   └── store/          # Zustand hooks
└── types/              # TypeScript types
```

## Development

### Available Commands

```bash
# Development
pnpm dev              # Start development server
pnpm build           # Build for production
pnpm start           # Start production server
pnpm lint            # Run linter
pnpm test            # Run tests

# Docker
docker-compose up    # Start all services
docker-compose down  # Stop all services
```

## Documentation

- [Initiation Plan](initiation-plan.md)
- [Contributing Guide](contributing-guide.md)

## Proprietary Software

This software is proprietary and confidential. Unauthorized copying, transferring, or reproduction of the contents of this repository, via any medium, is strictly prohibited.

All rights reserved.

© 2025 Kasuat. All rights reserved.

This software is protected by copyright law and international treaties. Any unauthorized use, reproduction, or distribution of this software or any portion of it may result in severe civil and criminal penalties, and will be prosecuted to the maximum extent possible under law.
