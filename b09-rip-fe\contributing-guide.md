# Contributing to Kasuat Frontend

## Table of Contents

1. [Introduction](#1-introduction)
2. [Getting Started](#2-getting-started)
3. [Project Structure](#3-project-structure)
4. [Module Development Workflow](#4-module-development-workflow)
   - [Step 1: Plan Your Feature](#step-1-plan-your-feature)
   - [Step 2: Create the Page Component Scaffold](#step-2-create-the-page-component-scaffold)
   - [Step 3: Define Types and Interfaces](#step-3-define-types-and-interfaces)
   - [Step 4: Create API Services](#step-4-create-api-services)
   - [Step 5: Build UI Components](#step-5-build-ui-components)
   - [Step 6: Implement Role-Based Access Control](#step-6-implement-role-based-access-control)
   - [Step 7: Connect Data and UI](#step-7-connect-data-and-ui)
5. [Authentication and Authorization](#5-authentication-and-authorization)
6. [State Management Patterns](#6-state-management-patterns)
7. [UI Component Guidelines](#7-ui-component-guidelines)
8. [Detailed Example: Manaj<PERSON>en <PERSON>](#8-detailed-example-manajemen-akun-module)
9. [Best Practices](#9-best-practices)
10. [Troubleshooting](#10-troubleshooting)

## 1. Introduction

Welcome to the Kasuat Frontend contributing guide. This document provides comprehensive instructions for contributing to the Kasuat Frontend application. Our project is built using:

- **Next.js 15** - React framework with App Router
- **TypeScript** - For type safety and better developer experience
- **Tailwind CSS** - For utility-first styling
- **shadcn/ui** - For consistent and accessible UI components

This guide will help you understand our project structure, development workflow, and best practices.

## 2. Getting Started

### Prerequisites

- Node.js 18+ (LTS recommended)
- PNPM package manager

### Installation

```bash
# Clone the repository
git clone https://gitlab.cs.ui.ac.id/propensi-2024-2025-genap/kelas-b/b09-rip-fe.git
cd b09-rip-fe

# Install dependencies
pnpm install

# Start development server
pnpm dev
```

### Available Scripts

```bash
# Development
pnpm dev           # Start development server
pnpm build         # Build for production
pnpm lint         # Run ESLint
pnpm format       # Run Prettier

# Adding shadcn/ui components
pnpm dlx shadcn@latest add button  # Add button component
```

## 3. Project Structure

Our project follows a feature-based organization within the Next.js App Router architecture:

```
src/
├── app/                    # Pages and routes (App Router)
│   ├── (auth)/            # Auth route group (login, register)
│   ├── (main)/            # Main authenticated routes
│   │   ├── dashboard/     # Dashboard pages
│   │   ├── manajemen-akun/ # User management pages
│   ├── layout.tsx         # Root layout
│   └── page.tsx           # Root page
├── components/            # React components
│   ├── admin/            # Admin-specific components
│   ├── auth/             # Authentication components
│   ├── ui/               # shadcn/ui components
│   └── shared/           # Shared components
├── hooks/                 # Custom React hooks
│   ├── auth/             # Authentication hooks
│   ├── queries/          # Data fetching hooks
│   └── store/            # State management hooks
├── lib/                   # Utilities and libraries
│   ├── api/              # API clients
│   ├── auth/             # Auth utilities
│   ├── store/            # Global state stores
│   └── utils/            # Utility functions
└── types/                 # TypeScript type definitions
```

Key directories explained:

- `src/app/`: Contains all pages and layouts using Next.js App Router
- `src/components/`: Reusable React components organized by feature
- `src/hooks/`: Custom React hooks for logic reuse
- `src/lib/`: Shared utilities, API clients, and service layers
- `src/types/`: TypeScript interfaces and types

## 4. Module Development Workflow

In this project, a "module" typically represents a complete feature that includes types, API services, components, and pages. Follow these steps to implement a new module:

### Step 1: Plan Your Feature

Before writing any code, plan your feature:

- Define the feature requirements and user stories
- Identify data structures and API endpoints needed
- Sketch the UI and component hierarchy
- Determine necessary permissions and role requirements

For example, for the Manajemen Akun module:

- Requirements: Admin users need to view, activate, and delete user accounts
- Data: User profiles with activation status
- UI: Filterable table with action buttons
- Permissions: Admin role only

### Step 2: Create the Page Component Scaffold

Start by creating the main page component in the appropriate directory under `src/app/`:

```typescript
// src/app/(main)/manajemen-akun/page.tsx
'use client';

import React, { useState } from 'react';
import { toast } from 'sonner';

// Main component scaffold
const UserManagementPage = () => {
  const [loading, setLoading] = useState(true);

  return (
    <div className="container mx-auto py-6">
      <h1 className="text-2xl font-bold mb-6">Manajemen Akun</h1>

      <div className="bg-white rounded-lg shadow p-6">
        {/* Filter components will go here */}

        {/* Table component will go here */}

        {/* Pagination will go here */}
      </div>
    </div>
  );
};

export default UserManagementPage;
```

This gives you a visual starting point and helps identify what supporting components, types, and API services you'll need to build.

### Step 3: Define Types and Interfaces

Once you understand what data your page needs, define TypeScript interfaces in the appropriate files under `src/types/`:

```typescript
// src/types/admin.ts
import { UserProfile } from './auth';

export interface PaginationInfo {
  total: number;
  pageCount: number;
  currentPage: number;
  perPage: number;
}

export interface UserWithProfile {
  id: string;
  email: string;
  profile: UserProfile;
}

export interface UserFilterParams {
  page?: number;
  pageSize?: number;
  search?: string;
  role?: string;
}
```

### Step 4: Create API Services

Implement the API services needed by your page in the appropriate file under `src/lib/api/`:

```typescript
// src/lib/api/admin.ts
import api from './client';
import { ApiResponse } from '@/types/auth';
import { PaginatedUsersResponse, UserFilterParams } from '@/types/admin';

export const adminApi = {
  /**
   * Get all users with filtering and pagination
   */
  getUsers: async (
    params: UserFilterParams = {}
  ): Promise<ApiResponse<PaginatedUsersResponse>> => {
    const response = await api.get<ApiResponse<PaginatedUsersResponse>>(
      '/v1/admin/users/',
      { params }
    );
    return response.data;
  },

  // Other API methods...
};
```

### Step 5: Build UI Components

Create the reusable UI components needed by your page:

```typescript
// src/components/admin/UserSearchFilter.tsx
import React from 'react';
import { Input } from '@/components/ui/input';
import { Select, SelectContent /* ... */ } from '@/components/ui/select';
import { UserRole } from '@/types/auth';

interface UserSearchFilterProps {
  search: string;
  role: string | undefined;
  onSearchChange: (value: string) => void;
  onRoleChange: (value: string | undefined) => void;
}

export default function UserSearchFilter({
  search,
  role,
  onSearchChange,
  onRoleChange,
}: UserSearchFilterProps) {
  // Component implementation
}
```

### Step 6: Implement Role-Based Access Control

Finally, add role-based protection to your page:

```typescript
// src/app/(main)/manajemen-akun/page.tsx
'use client';

import React, { useEffect, useState } from 'react';
import { toast } from 'sonner';
import { UserWithProfile } from '@/types/admin';
import { adminApi } from '@/lib/api/admin';
import UserTable from '@/components/admin/UserTable';
// ... other imports
import { RequireAdmin } from '@/components/auth/RequireRole';

// Main component implementation
const UserManagementPage = () => {
  // ... component implementation
};

// Wrap with admin-only protection
export default function ProtectedUserManagementPage() {
  return (
    <RequireAdmin>
      <UserManagementPage />
    </RequireAdmin>
  );
}
```

To add role-based access control:

1. Use the `useRBAC` hook for conditional UI rendering:

```typescript
import { useRBAC } from '@/hooks/useRBAC';

const { isAdmin, hasRole } = useRBAC();

// Check if user is admin
if (isAdmin()) {
  // Admin-only UI or functionality
}
```

2. Make sure to add menu items with role conditions:

```typescript
// In sidebar.tsx
const menuItems: MenuItem[] = [
  {
    icon: UserCog,
    title: 'Manajemen Akun',
    href: '/manajemen-akun',
    adminOnly: true, // Only visible to admins
  },
  // Other menu items...
];
```

### Step 7: Connect Data and UI

Implement data fetching and state management in your page component:

```typescript
// In your page component
useEffect(() => {
  const fetchUsers = async () => {
    setLoading(true);
    try {
      const params: UserFilterParams = {
        page: currentPage,
        pageSize: 10,
        search: search || undefined,
        role: role as any,
      };

      const response = await adminApi.getUsers(params);

      if (response.success && response.data) {
        setUsers(response.data.items);
        setTotalPages(response.data.pagination.pageCount);
        setTotalItems(response.data.pagination.total);
      } else {
        toast.error('Failed to load users');
      }
    } catch (error: any) {
      // Error handling...
    } finally {
      setLoading(false);
    }
  };

  fetchUsers();
}, [currentPage, search, role]);
```

This workflow starts with what users will see (the page) and builds down to the supporting pieces like types, API services, and components. It's more intuitive and follows a natural development flow.

## 5. Authentication and Authorization

Our application uses JWT authentication with role-based access control:

### JWT Authentication

The JWT payload contains role information in the `user_metadata.role` field:

```json
{
  "sub": "user-id",
  "exp": 1742360764,
  "iat": 1742357164,
  "user_metadata": {
    "email_verified": true,
    "role": "Admin"
  },
  "role": "authenticated"
}
```

The `JWTManager` in `src/lib/auth/jwt.ts` handles token management and provides methods for extracting the user role:

```typescript
static getUserRole(): UserRole | null {
  const payload = this.getPayload();
  if (!payload) return null;
  return payload.user_metadata?.role || null;
}

static isAdmin(): boolean {
  return this.getUserRole() === 'Admin';
}
```

### Role-Based Access Control

The `useRBAC` hook provides methods for checking user roles:

```typescript
export function useRBAC() {
  const user = useAuthStore((state) => state.user);

  const hasRole = (allowedRoles: UserRole[]): boolean => {
    const userRole = user?.role || JWTManager.getUserRole();
    return userRole ? allowedRoles.includes(userRole) : false;
  };

  const isAdmin = (): boolean => {
    const userRole = user?.role || JWTManager.getUserRole();
    return userRole === 'Admin';
  };

  return { hasRole, isAdmin };
}
```

For route protection, use the `RequireRole` or `RequireAdmin` components.

## 6. State Management Patterns

Our application uses several patterns for state management:

### Local Component State

Use React's `useState` and `useReducer` for component-level state:

```typescript
const [users, setUsers] = useState<UserWithProfile[]>([]);
const [loading, setLoading] = useState(true);
```

### Global State

For global state, we use Zustand stores defined in `src/lib/store/`:

```typescript
// src/lib/store/auth-store.ts
import { create } from 'zustand';
import { persist } from 'zustand/middleware';
import { UserProfile } from '@/types/auth';

interface AuthState {
  user: UserProfile | null;
  isAuthenticated: boolean;
  login: (user: UserProfile) => void;
  logout: () => void;
}

export const useAuthStore = create<AuthState>()(
  persist(
    (set) => ({
      user: null,
      isAuthenticated: false,
      login: (user) => set({ user, isAuthenticated: true }),
      logout: () => set({ user: null, isAuthenticated: false }),
    }),
    { name: 'auth-storage' }
  )
);
```

### Server State

For server state (API data), we create custom hooks for each data type:

```typescript
// src/hooks/queries/useUsers.ts
export function useUsers(params: UserFilterParams) {
  const [users, setUsers] = useState<UserWithProfile[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchUsers = async () => {
      setLoading(true);
      try {
        const response = await adminApi.getUsers(params);
        if (response.success) {
          setUsers(response.data.items);
          setError(null);
        } else {
          setError(response.message);
        }
      } catch (err) {
        setError('Failed to fetch users');
      } finally {
        setLoading(false);
      }
    };

    fetchUsers();
  }, [params]);

  return { users, loading, error };
}
```

## 7. UI Component Guidelines

### Using shadcn/ui Components

We use shadcn/ui components as the foundation for our UI. To add a component:

```bash
pnpm dlx shadcn-ui@latest add button
```

Then import and use it in your components:

```typescript
import { Button } from '@/components/ui/button';

export function SaveButton() {
  return (
    <Button variant="default" size="lg">
      Save Changes
    </Button>
  );
}
```

### Custom Component Patterns

When creating custom components, follow these patterns:

1. Define clear interfaces for props
2. Use TypeScript for type safety
3. Organize components by feature
4. Use composition for complex components
5. Leverage Tailwind CSS for styling

Example of a well-structured component:

```typescript
interface CardProps {
  title: string;
  children: React.ReactNode;
  className?: string;
}

export function Card({ title, children, className }: CardProps) {
  return (
    <div className={cn("rounded-lg border bg-card p-4", className)}>
      <h3 className="font-semibold">{title}</h3>
      <div className="mt-4">{children}</div>
    </div>
  );
}
```

## 8. Detailed Example: Manajemen Akun Module

The Manajemen Akun (User Management) module is a complete example of our module development approach. Let's break down its implementation:

### Data Model

First, the types are defined in `src/types/admin.ts` and `src/types/auth.ts`:

```typescript
// src/types/admin.ts
export interface UserWithProfile {
  id: string;
  email: string;
  profile: UserProfile;
}

export interface UserFilterParams {
  page?: number;
  pageSize?: number;
  search?: string;
  role?: string;
}

export interface ActivateUserRequest {
  id: string;
  org_id?: string;
}

// src/types/auth.ts
export type UserRole =
  | 'Admin'
  | 'Manager'
  | 'HR'
  | 'Finance'
  | 'Operation'
  | 'Client';

export interface UserProfile {
  id: string;
  user_id: string;
  fullname: string;
  phonenum: string;
  role: UserRole;
  is_active: boolean;
  // Other fields...
}
```

### API Service Layer

The API services are defined in `src/lib/api/admin.ts`:

```typescript
export const adminApi = {
  getUsers: async (
    params: UserFilterParams = {}
  ): Promise<ApiResponse<PaginatedUsersResponse>> => {
    const response = await api.get<ApiResponse<PaginatedUsersResponse>>(
      '/v1/admin/users/',
      { params }
    );
    return response.data;
  },

  activateUser: async (
    data: ActivateUserRequest
  ): Promise<ApiResponse<any>> => {
    const response = await api.patch<ApiResponse<any>>(
      '/v1/admin/users/activate',
      data
    );
    return response.data;
  },

  deleteUser: async (id: string): Promise<ApiResponse<any>> => {
    const response = await api.delete<ApiResponse<any>>(
      `/v1/admin/users/${id}`
    );
    return response.data;
  },
};
```

### UI Components

The module has several UI components in `src/components/admin/`:

1. `UserTable.tsx` - Displays user data in a table
2. `UserSearchFilter.tsx` - Provides search and filtering
3. `Pagination.tsx` - Handles pagination
4. `UserActivationDialog.tsx` - Dialog for activating users
5. `DeleteConfirmationDialog.tsx` - Dialog for confirming deletions
6. `BulkActionBar.tsx` - Toolbar for bulk actions

### Page Implementation

The main page component is defined in `src/app/(main)/manajemen-akun/page.tsx`:

```typescript
'use client';

import React, { useEffect, useState } from 'react';
import { toast } from 'sonner';
import { UserWithProfile } from '@/types/admin';
import { UserFilterParams } from '@/types/admin';
import { adminApi } from '@/lib/api/admin';
import UserTable from '@/components/admin/UserTable';
import UserSearchFilter from '@/components/admin/UserSearchFilter';
import Pagination from '@/components/admin/Pagination';
import UserActivationDialog from '@/components/admin/UserActivationDialog';
import DeleteConfirmationDialog from '@/components/admin/DeleteConfirmationDialog';
import BulkActionBar from '@/components/admin/BulkActionBar';
import { RequireAdmin } from '@/components/auth/RequireRole';

// Main component implementation
const UserManagementPage = () => {
  const [users, setUsers] = useState<UserWithProfile[]>([]);
  const [loading, setLoading] = useState(true);
  const [totalPages, setTotalPages] = useState(1);
  const [totalItems, setTotalItems] = useState(0);
  const [currentPage, setCurrentPage] = useState(1);
  const [search, setSearch] = useState('');
  const [role, setRole] = useState<string | undefined>('All');

  useEffect(() => {
    const fetchUsers = async () => {
      setLoading(true);
      try {
        const params: UserFilterParams = {
          page: currentPage,
          pageSize: 10,
          search: search || undefined,
          role: role as any,
        };

        const response = await adminApi.getUsers(params);

        if (response.success && response.data) {
          setUsers(response.data.items);
          setTotalPages(response.data.pagination.pageCount);
          setTotalItems(response.data.pagination.total);
        } else {
          toast.error('Failed to load users');
        }
      } catch (error: any) {
        // Error handling...
      } finally {
        setLoading(false);
      }
    };

    fetchUsers();
  }, [currentPage, search, role]);

  // Event handlers for actions (select, activate, delete, etc.)

  return (
    <div className="container mx-auto py-6">
      <h1 className="text-2xl font-bold mb-6">Manajemen Akun</h1>

      <div className="bg-white rounded-lg shadow p-6">
        <UserSearchFilter
          search={search}
          role={role}
          onSearchChange={setSearch}
          onRoleChange={setRole}
        />

        <UserTable
          users={users}
          selectedUsers={[]}
          onSelectUser={() => {}}
          onSelectAll={() => {}}
          onActivate={() => {}}
          onDelete={() => {}}
        />

        <Pagination
          currentPage={currentPage}
          totalPages={totalPages}
          totalItems={totalItems}
          onPageChange={setCurrentPage}
        />
      </div>
    </div>
  );
};

// Wrap with admin-only protection
export default function ProtectedUserManagementPage() {
  return (
    <RequireAdmin>
      <UserManagementPage />
    </RequireAdmin>
  );
}
```

### Role-Based Access Control

The page is protected using the `RequireAdmin` component, ensuring only users with the Admin role can access it. Additionally, the sidebar navigation in `src/components/ui/sidebar.tsx` conditionally renders the Manajemen Akun menu item:

```typescript
const menuItems: MenuItem[] = [
  {
    icon: UserCog,
    title: 'Manajemen Akun',
    href: '/manajemen-akun',
    adminOnly: true, // Only visible to admins
  },
  // Other menu items...
];

// In the Sidebar component:
const filteredMenuItems = menuItems.filter((item) => {
  // Hide admin-only items from non-admin users
  if (item.adminOnly && !isAdmin()) {
    return false;
  }
  return true;
});
```

### Error Handling

The module handles errors appropriately:

```typescript
try {
  const response = await adminApi.getUsers(params);
  // Success handling...
} catch (error: any) {
  console.error('Error fetching users:', error);

  // More specific error message based on error code
  if (error.response?.status === 401) {
    toast.error('Session expired. Please login again.');
  } else if (error.response?.status === 403) {
    toast.error('You do not have permission to view this page.');
  } else {
    toast.error('Failed to load users. Please try again later.');
  }
}
```

## 9. Best Practices

### Code Organization

1. Group related files by feature/module
2. Keep components focused on a single responsibility
3. Extract reusable logic into custom hooks
4. Use TypeScript for all new code

### Naming Conventions

- Components: PascalCase (e.g., `UserTable.tsx`)
- Hooks: camelCase with 'use' prefix (e.g., `useAuth.ts`)
- Utility functions: camelCase (e.g., `formatDate.ts`)
- File names: kebab-case for pages (e.g., `user-profile.tsx`)

### Error Handling

1. Always wrap async operations in try/catch blocks
2. Provide meaningful error messages to users
3. Log errors for debugging
4. Handle specific error codes appropriately

### Performance

1. Use pagination for large data sets
2. Implement memoization for expensive calculations (`useMemo`, `useCallback`)
3. Use virtualization for long lists
4. Optimize image loading with Next.js Image component

### Accessibility

1. Use semantic HTML elements
2. Provide aria- attributes where necessary
3. Ensure sufficient color contrast
4. Make all functionality keyboard accessible

## 10. Troubleshooting

### Common Issues

1. **Authentication Issues**

   - Check if token exists and is valid
   - Verify role information is correctly extracted from JWT
   - Check API response status codes (401/403)

2. **API Request Issues**

   - Check network tab in DevTools for request/response details
   - Verify request parameters and headers
   - Ensure proper error handling

3. **Component Rendering Issues**
   - Check React DevTools for component state
   - Verify conditional rendering logic
   - Check for missing dependencies in useEffect

### Debugging Techniques

1. Use console.log for quick debugging
2. Use React DevTools to inspect component state and props
3. Use Network tab to debug API requests
4. Use breakpoints in browser DevTools

### Resources

- [Next.js Documentation](https://nextjs.org/docs)
- [TypeScript Handbook](https://www.typescriptlang.org/docs/handbook/intro.html)
- [Tailwind CSS Documentation](https://tailwindcss.com/docs)
- [shadcn/ui Documentation](https://ui.shadcn.com/docs)
