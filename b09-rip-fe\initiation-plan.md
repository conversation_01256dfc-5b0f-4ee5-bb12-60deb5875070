# Kasuat Frontend Initiation Plan

## Table of Contents

1. [System Overview](#1-system-overview)

   - [1.1 Architecture](#11-architecture)
   - [1.2 Technology Stack](#12-technology-stack)
   - [1.3 Key Design Decisions](#13-key-design-decisions)

2. [Core Infrastructure](#2-core-infrastructure)

   - [2.1 Project Setup](#21-project-setup)
   - [2.2 Authentication Layer](#22-authentication-layer)
   - [2.3 API Integration](#23-api-integration)
   - [2.4 State Management](#24-state-management)
   - [2.5 Component System](#25-component-system)
   - [2.6 Security](#26-security)

3. [Development Environment](#3-development-environment)

   - [3.1 Local Setup](#31-local-setup)
   - [3.2 Docker Integration](#32-docker-integration)

4. [Deployment Strategy](#4-deployment-strategy)

   - [4.1 CI/CD Pipeline](#41-cicd-pipeline)
   - [4.2 Monitoring](#42-monitoring)

5. [Performance Optimization](#5-performance-optimization)

   - [5.1 Code Splitting](#51-code-splitting)
   - [5.2 Caching Strategy](#52-caching-strategy)
   - [5.3 Build Optimization](#53-build-optimization)

6. [Testing Framework](#6-testing-framework)

   - [6.1 Unit Testing](#61-unit-testing)
   - [6.2 Component Testing](#62-component-testing)
   - [6.3 E2E Testing](#63-e2e-testing)

7. [Maintenance](#7-maintenance)
   - [7.1 Update Procedures](#71-update-procedures)
   - [7.2 Monitoring](#72-monitoring)
   - [7.3 Backup Strategy](#73-backup-strategy)

## Project Structure

```bash
src/
├── app/                    # Next.js app router
│   ├── layout.tsx         # Root layout
│   ├── page.tsx           # Home page
│   └── (auth)/           # Auth group
│       ├── login/        # Login route
│       └── register/     # Register route
├── components/            # UI components
│   ├── ui/               # shadcn components
│   └── shared/           # Common components
├── lib/                  # Core utilities
│   ├── auth/            # Auth utilities
│   │   ├── jwt.ts       # JWT handling
│   │   └── session.ts   # Session management
│   ├── api/             # API utilities
│   │   ├── client.ts    # Axios instance
│   │   └── endpoints.ts # API endpoints
│   └── utils/           # Helper functions
├── hooks/               # Custom hooks
│   ├── auth/           # Auth hooks
│   ├── queries/        # React Query hooks
│   └── store/          # Zustand hooks
└── types/              # TypeScript types
```

## 1. System Overview

### 1.1 Architecture

This project implements a modern frontend architecture using:

- Next.js 15 with App Router for server-side rendering
- React 19 for UI development
- shadcn/ui for component system
- TypeScript for type safety

Key architectural decisions:

- App Router for better performance
- Server Components for reduced client bundle
- Hybrid rendering strategy (SSR + CSR)
- Component-driven development

### 1.2 Technology Stack

Core Dependencies:

```bash
# Core dependencies
pnpm add next@latest react@latest react-dom@latest
pnpm add @tanstack/react-query axios zustand
pnpm add class-variance-authority clsx tailwind-merge
pnpm add @hookform/resolvers/zod react-hook-form zod

# Development dependencies
pnpm add -D typescript @types/node @types/react @types/react-dom
pnpm add -D tailwindcss postcss autoprefixer
pnpm add -D eslint prettier
pnpm add -D vitest @testing-library/react @testing-library/jest-dom
pnpm add -D @playwright/test
```

### 1.3 Key Design Decisions

1. Authentication

   - JWT-based authentication matching backend
   - Protected routes with middleware
   - Role-based access control
   - Persistent sessions with refresh tokens

2. State Management

   - React Query for server state
   - Zustand for UI state
   - React Hook Form for form state
   - Local storage for persistence

3. Component Design
   - shadcn/ui as component foundation
   - Atomic design principles
   - Composition over inheritance
   - Strict prop typing

## 2. Core Infrastructure

### 2.1 Project Setup

Initialize project:

```bash
pnpm create next-app@latest kasuat-frontend --typescript --tailwind --eslint
cd kasuat-frontend
```

Configure TypeScript:

```typescript
// tsconfig.json
{
  "compilerOptions": {
    "target": "es5",
    "lib": ["dom", "dom.iterable", "esnext"],
    "allowJs": true,
    "skipLibCheck": true,
    "strict": true,
    "forceConsistentCasingInFileNames": true,
    "noEmit": true,
    "esModuleInterop": true,
    "module": "esnext",
    "moduleResolution": "node",
    "resolveJsonModule": true,
    "isolatedModules": true,
    "jsx": "preserve",
    "incremental": true,
    "plugins": [
      {
        "name": "next"
      }
    ],
    "paths": {
      "@/*": ["./src/*"]
    }
  }
}
```

### 2.2 Authentication Layer

JWT implementation:

```typescript
// lib/auth/jwt.ts
export class JWTManager {
  static getToken(): string | null {
    return localStorage.getItem("token");
  }

  static setToken(token: string): void {
    localStorage.setItem("token", token);
  }

  static removeToken(): void {
    localStorage.removeItem("token");
  }

  static isTokenValid(token: string): boolean {
    try {
      const decoded = jwtDecode(token);
      return decoded.exp! * 1000 > Date.now();
    } catch {
      return false;
    }
  }
}
```

Protected route middleware:

```typescript
// middleware.ts
import { NextResponse } from "next/server";
import type { NextRequest } from "next/server";

export function middleware(request: NextRequest) {
  const token = request.cookies.get("token");

  if (!token && !request.nextUrl.pathname.startsWith("/auth")) {
    return NextResponse.redirect(new URL("/auth/login", request.url));
  }

  return NextResponse.next();
}

export const config = {
  matcher: [
    "/((?!api|_next/static|_next/image|favicon.ico|auth/login|auth/register).*)",
  ],
};
```

### 2.3 API Integration

Axios setup:

```typescript
// lib/api/client.ts
import axios from "axios";
import { JWTManager } from "@/lib/auth/jwt";

export const api = axios.create({
  baseURL: process.env.NEXT_PUBLIC_API_URL,
  headers: {
    "Content-Type": "application/json",
  },
});

api.interceptors.request.use((config) => {
  const token = JWTManager.getToken();
  if (token) {
    config.headers.Authorization = `Bearer ${token}`;
  }
  return config;
});

api.interceptors.response.use(
  (response) => response,
  async (error) => {
    if (error.response?.status === 401) {
      // Handle token refresh or logout
      JWTManager.removeToken();
      window.location.href = "/auth/login";
    }
    return Promise.reject(error);
  }
);
```

### 2.4 State Management

React Query setup:

```typescript
// lib/providers.tsx
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";

const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      staleTime: 5 * 60 * 1000, // 5 minutes
      cacheTime: 10 * 60 * 1000, // 10 minutes
      retry: 1,
      refetchOnWindowFocus: false,
    },
  },
});

export function Providers({ children }: { children: React.ReactNode }) {
  return (
    <QueryClientProvider client={queryClient}>{children}</QueryClientProvider>
  );
}
```

Zustand store:

```typescript
// hooks/store/useAuthStore.ts
import { create } from "zustand";
import { persist } from "zustand/middleware";

interface AuthState {
  user: User | null;
  setUser: (user: User | null) => void;
  logout: () => void;
}

export const useAuthStore = create<AuthState>()(
  persist(
    (set) => ({
      user: null,
      setUser: (user) => set({ user }),
      logout: () => set({ user: null }),
    }),
    {
      name: "auth-storage",
    }
  )
);
```

### 2.5 Component System

shadcn/ui setup:

```bash
pnpm dlx shadcn-ui@latest init
```

Theme configuration:

```typescript
// lib/utils.ts
import { type ClassValue, clsx } from "clsx";
import { twMerge } from "tailwind-merge";

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs));
}
```

Custom component example:

```typescript
// components/ui/data-table.tsx
import {
  ColumnDef,
  flexRender,
  getCoreRowModel,
  useReactTable,
} from "@tanstack/react-table";

import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";

interface DataTableProps<TData, TValue> {
  columns: ColumnDef<TData, TValue>[];
  data: TData[];
}

export function DataTable<TData, TValue>({
  columns,
  data,
}: DataTableProps<TData, TValue>) {
  const table = useReactTable({
    data,
    columns,
    getCoreRowModel: getCoreRowModel(),
  });

  return (
    <div className="rounded-md border">
      <Table>
        <TableHeader>
          {table.getHeaderGroups().map((headerGroup) => (
            <TableRow key={headerGroup.id}>
              {headerGroup.headers.map((header) => (
                <TableHead key={header.id}>
                  {flexRender(
                    header.column.columnDef.header,
                    header.getContext()
                  )}
                </TableHead>
              ))}
            </TableRow>
          ))}
        </TableHeader>
        <TableBody>
          {table.getRowModel().rows.map((row) => (
            <TableRow key={row.id}>
              {row.getVisibleCells().map((cell) => (
                <TableCell key={cell.id}>
                  {flexRender(cell.column.columnDef.cell, cell.getContext())}
                </TableCell>
              ))}
            </TableRow>
          ))}
        </TableBody>
      </Table>
    </div>
  );
}
```

### 2.6 Security

Security headers:

```typescript
// next.config.js
const securityHeaders = [
  {
    key: "X-DNS-Prefetch-Control",
    value: "on",
  },
  {
    key: "Strict-Transport-Security",
    value: "max-age=31536000; includeSubDomains",
  },
  {
    key: "X-Frame-Options",
    value: "SAMEORIGIN",
  },
  {
    key: "X-Content-Type-Options",
    value: "nosniff",
  },
  {
    key: "X-XSS-Protection",
    value: "1; mode=block",
  },
];

module.exports = {
  async headers() {
    return [
      {
        source: "/:path*",
        headers: securityHeaders,
      },
    ];
  },
};
```

## 3. Development Environment

### 3.1 Local Setup

Environment variables (.env.local):

```env
NEXT_PUBLIC_API_URL=http://localhost:3000
NEXT_PUBLIC_APP_URL=http://localhost:3001
```

### 3.2 Docker Integration

Dockerfile:

```dockerfile
FROM node:18-alpine AS base

# Install dependencies only when needed
FROM base AS deps
RUN apk add --no-cache libc6-compat
WORKDIR /app

# Install dependencies based on the preferred package manager
COPY package.json pnpm-lock.yaml* ./
RUN npm install -g pnpm && pnpm i

# Rebuild the source code only when needed
FROM base AS builder
WORKDIR /app
COPY --from=deps /app/node_modules ./node_modules
COPY . .

ENV NEXT_TELEMETRY_DISABLED 1

RUN npm run build

# Production image, copy all the files and run next
FROM base AS runner
WORKDIR /app

ENV NODE_ENV production
ENV NEXT_TELEMETRY_DISABLED 1

RUN addgroup --system --gid 1001 nodejs
RUN adduser --system --uid 1001 nextjs

COPY --from=builder /app/public ./public

# Set the correct permission for prerender cache
RUN mkdir .next
RUN chown nextjs:nodejs .next

# Automatically leverage output traces to reduce image size
COPY --from=builder --chown=nextjs:nodejs /app/.next/standalone ./
COPY --from=builder --chown=nextjs:nodejs /app/.next/static ./.next/static

USER nextjs

EXPOSE 3000

ENV PORT 3000
ENV HOSTNAME "0.0.0.0"

CMD ["node", "server.js"]
```

## 4. Deployment Strategy

### 4.1 CI/CD Pipeline

GitHub Actions workflow:

```yaml
name: CI/CD

on:
  push:
    branches: [main]
  pull_request:
    branches: [main]

jobs:
  test:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v2
      - uses: pnpm/action-setup@v2
      - uses: actions/setup-node@v2
        with:
          node-version: "18"
          cache: "pnpm"
      - run: pnpm install
      - run: pnpm test
      - run: pnpm lint

  build:
    needs: test
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v2
      - uses: pnpm/action-setup@v2
      - uses: actions/setup-node@v2
        with:
          node-version: "18"
          cache: "pnpm"
      - run: pnpm install
      - run: pnpm build
```

### 4.2 Monitoring

Error tracking:

```typescript
// lib/monitoring/error-tracking.ts
export function initErrorTracking() {
  if (typeof window !== "undefined") {
    window.onerror = (message, source, lineno, colno, error) => {
      console.error("Global error:", { message, source, lineno, colno, error });
      // Send to error tracking service
    };

    window.onunhandledrejection = (event) => {
      console.error("Unhandled promise rejection:", event.reason);
      // Send to error tracking service
    };
  }
}
```

## 5. Performance Optimization

### 5.1 Code Splitting

Dynamic imports:

```typescript
// pages/dashboard.tsx
import dynamic from "next/dynamic";

const DashboardChart = dynamic(() => import("@/components/DashboardChart"), {
  loading: () => <p>Loading chart...</p>,
  ssr: false,
});
```

### 5.2 Caching Strategy

API response caching:

```typescript
// hooks/queries/useUsers.ts
export function useUsers() {
  return useQuery({
    queryKey: ["users"],
    queryFn: () => api.get("/users"),
    staleTime: 5 * 60 * 1000, // 5 minutes
    cacheTime: 10 * 60 * 1000, // 10 minutes
  });
}
```

### 5.3 Build Optimization

Next.js config:

```typescript
// next.config.js
module.exports = {
  images: {
    domains: ["your-image-domain.com"],
  },
  experimental: {
    optimizeCss: true,
    scrollRestoration: true,
  },
  compiler: {
    removeConsole: process.env.NODE_ENV === "production",
  },
};
```

## 6. Testing Framework

### 6.1 Unit Testing

Vitest setup:

```typescript
// vitest.config.ts
import { defineConfig } from "vitest/config";
import react from "@vitejs/plugin-react";
import tsconfigPaths from "vite-tsconfig-paths";

export default defineConfig({
  plugins: [react(), tsconfigPaths()],
  test: {
    environment: "jsdom",
    globals: true,
    setupFiles: ["./src/test/setup.ts"],
  },
});
```

### 6.2 Component Testing

Component test example:

```typescript
// components/ui/button.test.tsx
import { render, screen } from "@testing-library/react";
import { Button } from "./button";

describe("Button", () => {
  it("renders correctly", () => {
    render(<Button>Click me</Button>);
    expect(screen.getByRole("button")).toHaveTextContent("Click me");
  });

  it("handles click events", () => {
    const onClick = vi.fn();
    render(<Button onClick={onClick}>Click me</Button>);
    screen.getByRole("button").click();
    expect(onClick).toHaveBeenCalled();
  });
});
```

### 6.3 E2E Testing

Playwright config:

```typescript
// playwright.config.ts
import { defineConfig } from "@playwright/test";

export default defineConfig({
  testDir: "./e2e",
  use: {
    baseURL: "http://localhost:3000",
    trace: "on-first-retry",
  },
  webServer: {
    command: "pnpm dev",
    url: "http://localhost:3000",
    reuseExistingServer: !process.env.CI,
  },
});
```

## 7. Maintenance

### 7.1 Update Procedures

1. Dependency updates
2. Breaking changes handling
3. Database schema updates
4. API version management

### 7.2 Monitoring

1. Error tracking
2. Performance monitoring
3. User analytics
4. Server health checks

### 7.3 Backup Strategy

1. State persistence
2. Configuration backups
3. Database backups
4. Recovery procedures

## Implementation Phases

1. Core Setup

   - [ ] Initialize project
   - [ ] Set up authentication
   - [ ] Configure API client
   - [ ] Add basic components

2. Feature Development

   - [ ] User management
   - [ ] Role management
   - [ ] Basic CRUD interfaces
   - [ ] Form implementations

3. Testing & Deployment

   - [ ] Unit tests
   - [ ] Integration tests
   - [ ] E2E tests
   - [ ] CI/CD pipeline

4. Optimization
   - [ ] Performance tuning
   - [ ] Security hardening
   - [ ] Monitoring setup
   - [ ] Documentation updates
