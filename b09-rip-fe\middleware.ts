import { NextResponse } from 'next/server';
import type { NextRequest } from 'next/server';

export function middleware(request: NextRequest) {
  // Get path
  const path = request.nextUrl.pathname;

  // Define public paths that don't need authentication
  const isPublicPath =
    path === '/' ||
    path.startsWith('/api/auth') ||
    path.startsWith('/_next') ||
    path === '/favicon.ico';

  // Get the token from cookies - we support both cookie and localStorage
  const token = request.cookies.get('access_token')?.value;

  // Also check authorization header for API routes
  const authHeader = request.headers.get('authorization');
  const headerToken = authHeader ? authHeader.replace('Bearer ', '') : null;

  // Check if we have a valid token from any source
  const hasToken = !!token || !!headerToken;

  // Root path handles its own redirects with full token validation
  if (path === '/') {
    return NextResponse.next();
  }

  // If trying to access protected route without token, redirect to landing page
  if (!isPublicPath && !hasToken) {
    // For API routes, return 401 instead of redirecting
    if (path.startsWith('/api/')) {
      return new NextResponse(
        JSON.stringify({ success: false, message: 'Unauthorized' }),
        {
          status: 401,
          headers: { 'content-type': 'application/json' },
        }
      );
    }

    return NextResponse.redirect(new URL('/', request.url));
  }

  // Redirect old dashboard paths to the dashboard within the (main) route group
  if (path.startsWith('/dashboard')) {
    // Avoid redirecting if we're already accessing the correct dashboard path
    if (!path.startsWith('/(main)/dashboard')) {
      return NextResponse.redirect(new URL('/dashboard', request.url));
    }
  }

  return NextResponse.next();
}

// Define which routes this middleware applies to
export const config = {
  matcher: [
    // Match all paths except for:
    // - Static files (_next/static, favicon.ico, etc.)
    // - Public files (/public/)
    '/((?!_next/static|_next/image|favicon.ico|public).*)',
  ],
};
