[phases.setup]
nixPkgs = ["nodejs", "yarn", "openssl"]

# Remove install and build phases - let Nixpacks auto-detect and handle everything

[start]
cmd = "node .next/standalone/server.js"

[variables]
NODE_ENV = "production"
NEXT_TELEMETRY_DISABLED = "1"

# Optimize for production
[nixpkgs]
node = "20"

# Use pnpm cache for faster builds
[phases.setup.cache]
directories = [".pnpm-store", ".next/cache"]

# Configure for Coolify
[deploy]
startCommand = "node .next/standalone/server.js"
restartPolicyType = "ON_FAILURE"
healthcheckPath = "/"
port = "3001"
