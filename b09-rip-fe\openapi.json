{"openapi": "3.0.3", "info": {"title": "Kasuat API Documentation", "description": "API documentation for the Kasuat application. The Organization module serves as a reference implementation that developers should follow when creating new modules.", "version": "1.0.0"}, "tags": [{"name": "auth", "description": "Authentication endpoints"}, {"name": "examples", "description": "Example endpoints demonstrating RBAC patterns"}, {"name": "admin", "description": "Admin endpoints for user management and system administration"}, {"name": "organizations", "description": "Organization management endpoints"}, {"name": "invoices", "description": "Invoice management endpoints"}, {"name": "employees", "description": "Employee management endpoints"}, {"name": "attendances", "description": "Attendance management endpoints"}, {"name": "salaries", "description": "Salary management endpoints"}, {"name": "kpi", "description": "KPI management endpoints"}, {"name": "bonuses", "description": "Bonus salary management endpoints"}, {"name": "deductions", "description": "Deduction salary management endpoints"}, {"name": "allowances", "description": "Allowance salary management endpoints"}, {"name": "Tasks", "description": "Task management endpoints"}, {"name": "Project Tasks", "description": "Project task management endpoints"}, {"name": "kpi-projects", "description": "KPI Project management endpoints"}, {"name": "projects", "description": "Project management endpoints"}], "components": {"securitySchemes": {"bearerAuth": {"type": "http", "scheme": "bearer", "bearerFormat": "JWT", "description": "JWT Authorization header using the Bear<PERSON> scheme"}}, "examples": {"signUpExample": {"summary": "Example sign-up request", "value": {"email": "<EMAIL>", "password": "securepassword123", "fullname": "<PERSON>", "phonenum": "628*********0", "role": "Client"}}, "signInExample": {"summary": "Example sign-in request", "value": {"email": "<EMAIL>", "password": "securepassword123"}}, "refreshTokenExample": {"summary": "Example refresh token request", "value": {"refresh_token": "yHVhQTC553L3oTBsEGsDHQ"}}, "getUsersExample": {"summary": "Example get users response", "value": {"success": true, "message": "Users retrieved successfully", "data": {"items": [{"id": "example-auth-user-id-12345", "email": "<EMAIL>", "profile": {"id": "example-profile-id-12345", "user_id": "example-auth-user-id-12345", "fullname": "Example Admin User", "phonenum": "*********0", "role": "Admin", "org_id": null, "employee_id": null, "created_at": "2023-01-01T00:00:00.000Z", "created_by": "example-creator-id-12345", "updated_at": null, "updated_by": null, "deleted_at": null, "deleted_by": null, "is_active": true}}, {"id": "example-auth-user-id-67890", "email": "<EMAIL>", "profile": {"id": "example-profile-id-67890", "user_id": "example-auth-user-id-67890", "fullname": "Example Manager User", "phonenum": "0987654321", "role": "Manager", "org_id": null, "employee_id": "example-employee-id-67890", "created_at": "2023-01-02T00:00:00.000Z", "created_by": "example-creator-id-12345", "updated_at": null, "updated_by": null, "deleted_at": null, "deleted_by": null, "is_active": false}}], "pagination": {"total": 6, "page": 1, "pageSize": 10, "pageCount": 1}}}}, "forbiddenErrorExample": {"summary": "Example forbidden error response", "value": {"success": false, "message": "Admin access required", "data": null, "error": {"code": "INSUFFICIENT_ROLE", "details": null}}}, "activateUserExample": {"summary": "Example activate user request", "value": {"id": "example-profile-id-67890", "org_id": "example-organization-id-12345"}}, "deleteUserExample": {"summary": "Example delete user response", "value": {"success": true, "message": "User profile deleted successfully. Auth record must be deleted separately.", "data": {"id": "example-profile-id-67890", "deleted": true}}}, "getInvoiceResponseExample": {"summary": "Example get invoice response", "value": {"success": true, "message": "Invoice retrieved successfully", "data": {"id": "123e4567-e89b-12d3-a456-************", "invoice_number": "001/ORDEV/Kasuat/III/2025", "invoice_type": "external", "service_type": "ORDEV", "recipient_name": "Acme Corporation", "project_id": "project_id_123", "project_name": "Website Redesign", "due_date": "2025-04-15", "payment_method": "bank_transfer", "payment_status": "pending", "notes": "Please pay within 30 days", "total_amount": 2000, "created_at": "2025-03-01T00:00:00.000Z", "created_by": "auth0|*********", "updated_at": null, "updated_by": null, "deleted_at": null, "deleted_by": null, "items": [{"id": "223e4567-e89b-12d3-a456-************", "invoice_id": "123e4567-e89b-12d3-a456-************", "item_name": "Web Design", "item_amount": 1, "item_price": 1500, "total_price": 1500, "created_at": "2025-03-01T00:00:00.000Z", "created_by": "auth0|*********", "updated_at": null, "updated_by": null, "deleted_at": null, "deleted_by": null}, {"id": "323e4567-e89b-12d3-a456-************", "invoice_id": "123e4567-e89b-12d3-a456-************", "item_name": "Content Creation", "item_amount": 10, "item_price": 50, "total_price": 500, "created_at": "2025-03-01T00:00:00.000Z", "created_by": "auth0|*********", "updated_at": null, "updated_by": null, "deleted_at": null, "deleted_by": null}]}}}, "getInvoicesResponseExample": {"summary": "Example get all invoices response", "value": {"success": true, "message": "Invoices retrieved successfully", "data": {"data": [{"id": "123e4567-e89b-12d3-a456-************", "invoice_number": "001/ORDEV/Kasuat/III/2025", "invoice_type": "external", "service_type": "ORDEV", "recipient_name": "Acme Corporation", "project_id": "project_id_123", "project_name": "Website Redesign", "due_date": "2025-04-15", "payment_method": "bank_transfer", "payment_status": "pending", "notes": "Please pay within 30 days", "total_amount": 2000, "created_at": "2025-03-01T00:00:00.000Z", "items": [{"id": "223e4567-e89b-12d3-a456-************", "invoice_id": "123e4567-e89b-12d3-a456-************", "item_name": "Web Design", "item_amount": 1, "item_price": 1500, "total_price": 1500}, {"id": "323e4567-e89b-12d3-a456-************", "invoice_id": "123e4567-e89b-12d3-a456-************", "item_name": "Content Creation", "item_amount": 10, "item_price": 50, "total_price": 500}]}, {"id": "223e4567-e89b-12d3-a456-************", "invoice_number": "002/FINANCE/Kasuat/III/2025", "invoice_type": "internal", "service_type": "FINANCE", "recipient_name": "Tech Partners Inc.", "due_date": "2025-05-01", "payment_method": "credit_card", "payment_status": "paid", "total_amount": 3500, "created_at": "2025-03-05T00:00:00.000Z", "items": [{"id": "423e4567-e89b-12d3-a456-************", "invoice_id": "223e4567-e89b-12d3-a456-************", "item_name": "Financial Consulting", "item_amount": 5, "item_price": 700, "total_price": 3500}]}], "pagination": {"total": 25, "page": 1, "pageSize": 10, "pageCount": 3}}}}, "createInvoiceExample": {"summary": "Example create invoice request", "value": {"invoice_type": "external", "service_type": "ORDEV", "recipient_name": "Acme Corporation", "project_id": "project_id_123", "project_name": "Website Redesign", "due_date": "2025-04-15", "payment_method": "bank_transfer", "notes": "Please pay within 30 days", "items": [{"item_name": "Web Design", "item_amount": 1, "item_price": 1500}, {"item_name": "Content Creation", "item_amount": 10, "item_price": 50}]}}, "createInvoiceResponseExample": {"summary": "Example create invoice response", "value": {"success": true, "message": "Invoice created successfully", "data": {"id": "123e4567-e89b-12d3-a456-************", "invoice_number": "001/ORDEV/Kasuat/III/2025", "invoice_type": "external", "service_type": "ORDEV", "recipient_name": "Acme Corporation", "project_id": "project_id_123", "project_name": "Website Redesign", "due_date": "2025-04-15", "payment_method": "bank_transfer", "payment_status": "pending", "notes": "Please pay within 30 days", "total_amount": 2000, "created_at": "2025-03-01T00:00:00.000Z", "created_by": "auth0|*********", "updated_at": null, "updated_by": null, "deleted_at": null, "deleted_by": null, "items": [{"id": "223e4567-e89b-12d3-a456-************", "invoice_id": "123e4567-e89b-12d3-a456-************", "item_name": "Web Design", "item_amount": 1, "item_price": 1500, "total_price": 1500, "created_at": "2025-03-01T00:00:00.000Z", "created_by": "auth0|*********", "updated_at": null, "updated_by": null, "deleted_at": null, "deleted_by": null}, {"id": "323e4567-e89b-12d3-a456-************", "invoice_id": "123e4567-e89b-12d3-a456-************", "item_name": "Content Creation", "item_amount": 10, "item_price": 50, "total_price": 500, "created_at": "2025-03-01T00:00:00.000Z", "created_by": "auth0|*********", "updated_at": null, "updated_by": null, "deleted_at": null, "deleted_by": null}]}}}, "updateInvoiceExample": {"summary": "Example update invoice request", "value": {"invoice_type": "external", "recipient_name": "Updated Corporation Ltd", "payment_status": "partial", "notes": "Updated payment received on March 15", "items": [{"id": "223e4567-e89b-12d3-a456-************", "item_name": "Updated Web Design", "item_amount": 1, "item_price": 2000}, {"item_name": "Additional Service", "item_amount": 5, "item_price": 100}]}}, "updateInvoiceResponseExample": {"summary": "Example update invoice response", "value": {"success": true, "message": "Invoice updated successfully", "data": {"id": "123e4567-e89b-12d3-a456-************", "invoice_number": "001/ORDEV/Kasuat/III/2025", "invoice_type": "external", "service_type": "ORDEV", "recipient_name": "Updated Corporation Ltd", "project_id": "project_id_123", "project_name": "Website Redesign", "due_date": "2025-04-15", "payment_method": "bank_transfer", "payment_status": "partial", "notes": "Updated payment received on March 15", "total_amount": 2500, "created_at": "2025-03-01T00:00:00.000Z", "created_by": "auth0|*********", "updated_at": "2025-03-15T00:00:00.000Z", "updated_by": "auth0|*********", "deleted_at": null, "deleted_by": null, "items": [{"id": "223e4567-e89b-12d3-a456-************", "invoice_id": "123e4567-e89b-12d3-a456-************", "item_name": "Updated Web Design", "item_amount": 1, "item_price": 2000, "total_price": 2000, "created_at": "2025-03-01T00:00:00.000Z", "created_by": "auth0|*********", "updated_at": "2025-03-15T00:00:00.000Z", "updated_by": "auth0|*********", "deleted_at": null, "deleted_by": null}, {"id": "423e4567-e89b-12d3-a456-************", "invoice_id": "123e4567-e89b-12d3-a456-************", "item_name": "Additional Service", "item_amount": 5, "item_price": 100, "total_price": 500, "created_at": "2025-03-15T00:00:00.000Z", "created_by": "auth0|*********", "updated_at": null, "updated_by": null, "deleted_at": null, "deleted_by": null}]}}}, "deleteInvoiceResponseExample": {"summary": "Example delete invoice response", "value": {"success": true, "message": "Invoice with ID 123e4567-e89b-12d3-a456-************ successfully deleted", "data": null}}, "createOrganizationExample": {"summary": "Example create organization request", "value": {"name": "Acme Corporation", "phone": "628*********0", "address": "123 Main Street, Metropolis", "client_type": "Enterprise", "notes": "Important client with multiple projects"}}, "updateOrganizationExample": {"summary": "Example update organization request", "value": {"name": "Acme Corporation Updated", "phone": "628*********0", "address": "456 New Avenue, Metropolis", "client_type": "Enterprise Premium", "notes": "VIP client with dedicated support team"}}, "getOrganizationsExample": {"summary": "Example get organizations response", "value": {"success": true, "message": "Organizations retrieved successfully", "data": {"items": [{"id": "123e4567-e89b-12d3-a456-************", "name": "Acme Corporation", "phone": "628*********0", "address": "123 Main Street, Metropolis", "client_type": "Enterprise", "notes": "Important client with multiple projects", "created_at": "2023-01-01T00:00:00.000Z", "created_by": "auth0|*********", "updated_at": null, "updated_by": null, "deleted_at": null, "deleted_by": null}, {"id": "223e4567-e89b-12d3-a456-************", "name": "TechStart Inc", "phone": "6287654321098", "address": "789 Innovation Drive, Silicon Valley", "client_type": "Startup", "notes": "Growing tech company", "created_at": "2023-01-15T00:00:00.000Z", "created_by": "auth0|*********", "updated_at": "2023-02-01T00:00:00.000Z", "updated_by": "auth0|*********", "deleted_at": null, "deleted_by": null}], "pagination": {"total": 2, "page": 1, "pageSize": 10, "pageCount": 1}}}}, "getOrganizationExample": {"summary": "Example get organization by ID response", "value": {"success": true, "message": "Organization retrieved successfully", "data": {"id": "123e4567-e89b-12d3-a456-************", "name": "Acme Corporation", "phone": "628*********0", "address": "123 Main Street, Metropolis", "client_type": "Enterprise", "notes": "Important client with multiple projects", "created_at": "2023-01-01T00:00:00.000Z", "created_by": "auth0|*********", "updated_at": null, "updated_by": null, "deleted_at": null, "deleted_by": null}}}, "notFoundErrorExample": {"summary": "Example not found error response", "value": {"success": false, "message": "Organization not found", "data": null, "error": {"code": "NOT_FOUND", "details": null}}}, "deleteOrganizationExample": {"summary": "Example delete organization response", "value": {"success": true, "message": "Organization deleted successfully", "data": null}}, "getKpisExample": {"summary": "Example get KPIs response", "value": {"success": true, "message": "KPIs retrieved successfully", "data": {"items": [{"id": "example-kpi-id-12345", "full_name": "<PERSON>", "employee_id": "EMP-12345", "description": "Increase sales by 25% in Q1 2024", "target": "25% increase in sales", "period": "2024-Q1", "status": "in_progress", "bonus_received": null, "additional_notes": "On track to meet target", "created_at": "2023-12-01T00:00:00.000Z", "created_by": "example-creator-id-12345", "updated_at": "2024-01-15T00:00:00.000Z", "updated_by": "example-updater-id-12345", "deleted_at": null, "deleted_by": null}, {"id": "example-kpi-id-67890", "full_name": "<PERSON>", "employee_id": "EMP-67890", "description": "Reduce customer support response time to under 2 hours", "target": "< 2 hour response time", "period": "2024-Q1", "status": "completed_above_target", "bonus_received": 5000, "additional_notes": "Achieved 1.5 hour average response time", "created_at": "2023-12-01T00:00:00.000Z", "created_by": "example-creator-id-12345", "updated_at": "2024-03-01T00:00:00.000Z", "updated_by": "example-updater-id-12345", "deleted_at": null, "deleted_by": null}], "pagination": {"total": 6, "page": 1, "pageSize": 10, "pageCount": 1}}}}, "createKpiExample": {"summary": "Example create KPI request", "value": {"full_name": "<PERSON>", "employee_id": "EMP-12345", "description": "Increase sales by 25% in Q1 2024", "target": "25% increase in sales", "period": "2024-Q1", "status": "not_started", "additional_notes": "Initial KPI for sales team lead"}}, "updateKpiExample": {"summary": "Example update KPI request", "value": {"status": "in_progress", "additional_notes": "Making good progress on target"}}, "updateKpiStatusExample": {"summary": "Example update KPI status request", "value": {"status": "completed_on_target"}}, "updateKpiBonusExample": {"summary": "Example update KPI bonus request", "value": {"bonus_received": 3500}}, "deleteKpiExample": {"summary": "Example delete KPI response", "value": {"success": true, "message": "KPI deleted successfully", "data": {"id": "example-kpi-id-12345", "deleted": true}}}, "getAllEmployeesExample": {"summary": "Example get all employees response with pagination", "value": {"success": true, "message": "Employees retrieved successfully", "data": {"items": [{"id": "123e4567-e89b-12d3-a456-************", "profile_id": "223e4567-e89b-12d3-a456-************", "dob": "1990-01-01", "address": "Jl. <PERSON> No. 123, Jakarta", "bank_account": "*********0", "bank_name": "Bank ABC", "employment_status": "Fulltime", "presence_status": "present", "department": "HR", "start_date": "2024-01-01", "salary_id": null, "created_at": "2024-01-01T00:00:00.000Z", "created_by": "auth0|*********", "updated_at": null, "updated_by": null, "deleted_at": null, "deleted_by": null, "user_profiles": {"fullname": "<PERSON>", "email": "<EMAIL>", "phonenum": "08*********0"}}], "pagination": {"total": 1, "page": 1, "pageSize": 10}}}}, "getEmployeeExample": {"summary": "Example get employee response", "value": {"success": true, "message": "Employee retrieved successfully", "data": {"id": "123e4567-e89b-12d3-a456-************", "profile_id": "223e4567-e89b-12d3-a456-************", "dob": "1990-01-01", "address": "Jl. <PERSON> No. 123, Jakarta", "bank_account": "*********0", "bank_name": "Bank ABC", "employment_status": "Fulltime", "presence_status": "present", "department": "HR", "start_date": "2024-01-01", "salary_id": null, "created_at": "2024-01-01T00:00:00.000Z", "created_by": "auth0|*********", "updated_at": null, "updated_by": null, "deleted_at": null, "deleted_by": null, "user_profiles": {"fullname": "<PERSON>", "email": "<EMAIL>", "phonenum": "08*********0"}}}}, "updateEmployeeExample": {"summary": "Example update employee request", "value": {"dob": "1990-01-01", "address": "Jl. <PERSON> No. 123, Jakarta", "bank_account": "*********0", "bank_name": "Bank ABC", "employment_status": "Fulltime", "presence_status": "present", "department": "HR", "start_date": "2024-01-01", "salary_id": null}}, "updateEmployeeResponseExample": {"summary": "Example update employee response", "value": {"success": true, "message": "Employee updated successfully", "data": {"id": "123e4567-e89b-12d3-a456-************", "profile_id": "223e4567-e89b-12d3-a456-************", "dob": "1990-01-01", "address": "Jl. <PERSON> No. 123, Jakarta", "bank_account": "*********0", "bank_name": "Bank ABC", "employment_status": "Fulltime", "presence_status": "present", "department": "HR", "start_date": "2024-01-01", "salary_id": null, "created_at": "2024-01-01T00:00:00.000Z", "created_by": "auth0|*********", "updated_at": "2024-03-15T00:00:00.000Z", "updated_by": "auth0|*********", "deleted_at": null, "deleted_by": null}}}, "errorResponseExample": {"summary": "Example error response", "value": {"success": false, "message": "Deduction not found", "error": "No deduction found with the provided ID"}}, "AttendanceExample": {"summary": "Example attendance record", "value": {"id": "123e4567-e89b-12d3-a456-************", "date": "2024-03-14", "employee_id": "EMP001", "status": "present", "clock_in": "08:00:00", "clock_out": "17:00:00", "notes": "Attended meeting at 10 AM"}}, "createAttendanceExample": {"summary": "Clock-in with tasks example", "value": {"status": "present", "notes": "Working on the new feature", "tasks": [{"description": "Implement authentication system", "due_date": "2023-12-31", "completion_status": "not_completed"}]}}, "clockOutExample": {"summary": "Clock-out with updated notes example", "value": {"status": "present", "notes": "Completed work on new feature", "tasks": [{"description": "Review code changes", "due_date": "2023-12-31", "completion_status": "completed"}]}}, "clockInResponseExample": {"summary": "Successful clock-in response", "value": {"success": true, "message": "Attendance created with clock-in time successfully", "data": {"id": "123e4567-e89b-12d3-a456-************", "date": "2023-12-15", "employee_id": "emp-123", "status": "present", "clock_in": "09:00:00", "clock_out": null, "notes": "Working on the new feature", "created_at": "2023-12-15T02:00:00.000Z", "updated_at": "2023-12-15T02:00:00.000Z", "deleted_at": null, "created_by": "user-123", "updated_by": "user-123"}}}, "clockOutResponseExample": {"summary": "Successful clock-out response", "value": {"success": true, "message": "Attendance updated with clock-out time successfully", "data": {"id": "123e4567-e89b-12d3-a456-************", "date": "2023-12-15", "employee_id": "emp-123", "status": "present", "clock_in": "09:00:00", "clock_out": "17:00:00", "notes": "Completed work on new feature", "created_at": "2023-12-15T02:00:00.000Z", "updated_at": "2023-12-15T10:00:00.000Z", "deleted_at": null, "created_by": "user-123", "updated_by": "user-123"}}}, "existingAttendanceExample": {"summary": "Existing attendance record", "value": {"success": true, "message": "Today's attendance record retrieved successfully", "data": {"id": "123e4567-e89b-12d3-a456-************", "date": "2023-12-15", "employee_id": "emp-123", "status": "present", "clock_in": "09:00:00", "clock_out": null, "notes": "Working on the new feature", "created_at": "2023-12-15T02:00:00.000Z", "updated_at": "2023-12-15T02:00:00.000Z", "deleted_at": null, "created_by": "user-123", "updated_by": "user-123", "tasks": [{"id": "task-123", "attendance_id": "123e4567-e89b-12d3-a456-************", "description": "Implement authentication system", "completion_status": "not_completed", "employee_id": "emp-123", "due_date": "2023-12-31", "created_at": "2023-12-15T02:00:00.000Z", "updated_at": "2023-12-15T02:00:00.000Z", "deleted_at": null, "created_by": "user-123", "updated_by": "user-123"}]}}}, "notFoundExample": {"summary": "No attendance found", "value": {"success": false, "message": "No attendance record found for today", "data": null, "error": {"code": "NOT_FOUND"}}}, "missingEmployeeIdExample": {"summary": "Missing employee ID error", "value": {"success": false, "message": "Employee ID not found in user profile", "data": null, "error": {"code": "BAD_REQUEST"}}}, "unauthorizedExample": {"summary": "Authentication required error", "value": {"success": false, "message": "Authentication required", "data": null, "error": {"code": "AUTH_REQUIRED"}}}, "forbiddenExample": {"summary": "Insufficient permissions error", "value": {"success": false, "message": "You do not have permission to access this resource", "data": null, "error": {"code": "INSUFFICIENT_ROLE"}}}, "serverErrorExample": {"summary": "Server error", "value": {"success": false, "message": "Unexpected error occurred", "data": null, "error": {"code": "INTERNAL_SERVER_ERROR"}}}, "attendanceListExample": {"summary": "Paginated attendance list with tasks", "value": {"success": true, "message": "Attendances fetched successfully", "data": {"data": [{"id": "123e4567-e89b-12d3-a456-************", "date": "2023-12-15", "employee_id": "emp-123", "status": "present", "clock_in": "09:00:00", "clock_out": "17:00:00", "notes": "Completed work on new feature", "created_at": "2023-12-15T02:00:00.000Z", "updated_at": "2023-12-15T10:00:00.000Z", "deleted_at": null, "created_by": "user-123", "updated_by": "user-123", "tasks": [{"id": "task-123", "attendance_id": "123e4567-e89b-12d3-a456-************", "description": "Implement authentication system", "completion_status": "completed", "employee_id": "emp-123", "due_date": "2023-12-31", "created_at": "2023-12-15T02:00:00.000Z", "updated_at": "2023-12-15T10:00:00.000Z", "deleted_at": null, "created_by": "user-123", "updated_by": "user-123"}]}, {"id": "223e4567-e89b-12d3-a456-************", "date": "2023-12-14", "employee_id": "emp-123", "status": "present", "clock_in": "09:30:00", "clock_out": "18:00:00", "notes": "Team meeting and planning", "created_at": "2023-12-14T02:30:00.000Z", "updated_at": "2023-12-14T11:00:00.000Z", "deleted_at": null, "created_by": "user-123", "updated_by": "user-123", "tasks": []}], "pagination": {"total": 2, "page": 1, "pageSize": 10, "totalPages": 1}}}}, "emptyAttendanceListExample": {"summary": "Empty attendance list", "value": {"success": true, "message": "Attendances fetched successfully", "data": {"data": [], "pagination": {"total": 0, "page": 1, "pageSize": 10, "totalPages": 0}}}}, "invalidDateFormatExample": {"summary": "Invalid date format", "value": {"success": false, "message": "Invalid date format. Date should be in YYYY-MM-DD format", "data": null, "error": {"code": "BAD_REQUEST"}}}, "invalidDateRangeExample": {"summary": "Invalid date range", "value": {"success": false, "message": "fromDate must be before or equal to toDate", "data": null, "error": {"code": "BAD_REQUEST"}}}, "employeeAttendanceExample": {"summary": "Employee attendance lookup example", "value": {"employee_id": "EMP001", "fromDate": "2023-01-01", "toDate": "2023-12-31", "page": 1, "pageSize": 10}}, "invalidEmployeeIdExample": {"summary": "Invalid employee ID", "value": {"success": false, "message": "Employee ID is required", "data": null, "error": {"code": "BAD_REQUEST"}}}, "generateSalaryExample": {"summary": "Example generate salary request", "value": {"employeeId": "8a172b90-b3d5-4f32-b971-3603fb62130a", "period": "2025-03"}}, "generateSalaryResponseExample": {"summary": "Example generate salary response", "value": {"success": true, "message": "Salary generated successfully", "data": {"id": "123e4567-e89b-12d3-a456-************", "employee_id": "8a172b90-b3d5-4f32-b971-3603fb62130a", "base_salary": 5000000, "total_bonus": 500000, "total_deduction": 100000, "total_allowance": 0, "total_salary": 5400000, "payment_status": "unpaid", "period": "2025-03"}}}, "updateSalaryExample": {"summary": "Example update salary request", "value": {"base_salary": 5500000, "payment_status": "paid"}}, "updateSalaryResponseExample": {"summary": "Example update salary response", "value": {"success": true, "message": "Salary updated successfully", "data": {"id": "123e4567-e89b-12d3-a456-************", "employee_id": "8a172b90-b3d5-4f32-b971-3603fb62130a", "base_salary": 5500000, "total_bonus": 500000, "total_deduction": 100000, "total_allowance": 0, "total_salary": 5900000, "payment_status": "paid", "period": "2025-03"}}}, "createBonusExample": {"summary": "Example create bonus request", "value": {"salary_id": "123e4567-e89b-12d3-a456-************", "amount": 500000, "bonus_type": "kpi", "notes": "KPI achievement bonus for Q2 2025", "kpi_id": "223e4567-e89b-12d3-a456-************", "project_id": null}}, "getBonusesExample": {"summary": "Example get bonuses response", "value": {"success": true, "message": "Bonuses retrieved successfully", "data": [{"id": "323e4567-e89b-12d3-a456-************", "salary_id": "123e4567-e89b-12d3-a456-************", "amount": 500000, "bonus_type": "kpi", "notes": "KPI achievement bonus for Q2 2025", "kpi_id": "223e4567-e89b-12d3-a456-************", "project_id": null, "created_at": "2025-05-01T00:00:00.000Z", "created_by": "auth0|*********", "updated_at": null, "updated_by": null, "deleted_at": null, "deleted_by": null}, {"id": "423e4567-e89b-12d3-a456-************", "salary_id": "123e4567-e89b-12d3-a456-************", "amount": 300000, "bonus_type": "project", "notes": "Project completion bonus", "kpi_id": null, "project_id": "523e4567-e89b-12d3-a456-426614174004", "created_at": "2025-05-01T00:00:00.000Z", "created_by": "auth0|*********", "updated_at": null, "updated_by": null, "deleted_at": null, "deleted_by": null}]}}, "createDeductionSalaryExample": {"summary": "Example create deduction salary request", "value": {"salary_id": "123e4567-e89b-12d3-a456-************", "amount": 500000, "deduction_type": "absence", "notes": "Deduction for 2 days of absence"}}, "createDeductionSalaryResponseExample": {"summary": "Example create deduction salary response", "value": {"success": true, "message": "Deduction created successfully", "data": {"id": "323e4567-e89b-12d3-a456-************", "salary_id": "123e4567-e89b-12d3-a456-************", "amount": 500000, "deduction_type": "absence", "notes": "Deduction for 2 days of absence", "created_at": "2024-12-01T00:00:00.000Z", "created_by": "auth0|*********", "updated_at": null, "updated_by": null, "deleted_at": null, "deleted_by": null}}}, "getDeductionSalaryByIdExample": {"summary": "Example get deduction salary by ID response", "value": {"success": true, "message": "Deduction retrieved successfully", "data": {"id": "323e4567-e89b-12d3-a456-************", "salary_id": "123e4567-e89b-12d3-a456-************", "amount": 500000, "deduction_type": "absence", "notes": "Deduction for 2 days of absence", "created_at": "2024-12-01T00:00:00.000Z", "created_by": "auth0|*********", "updated_at": null, "updated_by": null, "deleted_at": null, "deleted_by": null}}}, "getDeductionSalaryBySalaryIdExample": {"summary": "Example get deduction salary by salary ID response", "value": {"success": true, "message": "Deductions retrieved successfully", "data": [{"id": "323e4567-e89b-12d3-a456-************", "salary_id": "123e4567-e89b-12d3-a456-************", "amount": 500000, "deduction_type": "absence", "notes": "Deduction for 2 days of absence", "created_at": "2024-12-01T00:00:00.000Z", "created_by": "auth0|*********", "updated_at": null, "updated_by": null, "deleted_at": null, "deleted_by": null}, {"id": "423e4567-e89b-12d3-a456-************", "salary_id": "123e4567-e89b-12d3-a456-************", "amount": 100000, "deduction_type": "lateness", "notes": "Deduction for 3 times lateness", "created_at": "2024-12-01T00:00:00.000Z", "created_by": "auth0|*********", "updated_at": null, "updated_by": null, "deleted_at": null, "deleted_by": null}]}}, "updateDeductionSalaryExample": {"summary": "Example update deduction salary request", "value": {"amount": 600000, "deduction_type": "absence", "notes": "Updated: Deduction for 3 days of absence"}}, "updateDeductionSalaryResponseExample": {"summary": "Example update deduction salary response", "value": {"success": true, "message": "Deduction updated successfully", "data": {"id": "323e4567-e89b-12d3-a456-************", "salary_id": "123e4567-e89b-12d3-a456-************", "amount": 600000, "deduction_type": "absence", "notes": "Updated: Deduction for 3 days of absence", "created_at": "2024-12-01T00:00:00.000Z", "created_by": "auth0|*********", "updated_at": "2024-12-02T00:00:00.000Z", "updated_by": "auth0|987654321", "deleted_at": null, "deleted_by": null}}}, "deleteDeductionSalaryResponseExample": {"summary": "Example delete deduction salary response", "value": {"success": true, "message": "Deduction deleted successfully", "data": {"id": "323e4567-e89b-12d3-a456-************"}}}, "validationErrorExample": {"summary": "Example validation error response", "value": {"success": false, "message": "Validation error", "error": "Missing required fields: salary_id, amount, deduction_type"}}, "unauthorizedErrorExample": {"summary": "Example unauthorized error response", "value": {"success": false, "message": "Unauthorized", "error": "Only Managers and Staff Operations can perform this action"}}, "createAllowanceExample": {"summary": "Example create allowance request", "value": {"salary_id": "123e4567-e89b-12d3-a456-************", "amount": 200000, "allowance_type": "transport", "notes": "Transport allowance for May 2025"}}, "getAllowancesExample": {"summary": "Example get allowances response", "value": {"success": true, "message": "Allowances retrieved successfully", "data": [{"id": "223e4567-e89b-12d3-a456-************", "salary_id": "123e4567-e89b-12d3-a456-************", "amount": 200000, "allowance_type": "transport", "notes": "Transport allowance for May 2025", "created_at": "2025-05-01T00:00:00.000Z", "created_by": "auth0|*********", "updated_at": null, "updated_by": null, "deleted_at": null, "deleted_by": null}, {"id": "323e4567-e89b-12d3-a456-************", "salary_id": "123e4567-e89b-12d3-a456-************", "amount": 150000, "allowance_type": "meal", "notes": "Meal allowance for May 2025", "created_at": "2025-05-01T00:00:00.000Z", "created_by": "auth0|*********", "updated_at": null, "updated_by": null, "deleted_at": null, "deleted_by": null}]}}, "updateTaskStatusExample": {"summary": "Update task status example", "value": {"completion_status": "completed"}}, "createProjectTaskExample": {"summary": "Create project task example", "value": {"assigned_by": "123e4567-e89b-12d3-a456-************", "description": "Implement authentication system", "employee_id": "123e4567-e89b-12d3-a456-************", "initial_date": "2023-12-01", "due_date": "2023-12-31", "project_id": "123e4567-e89b-12d3-a456-************", "completion_status": "not_completed", "weekly_log_id": "123e4567-e89b-12d3-a456-************"}}, "updateProjectTaskStatusExample": {"summary": "Update project task status example", "value": {"completion_status": "completed"}}, "updateProjectTaskExample": {"summary": "Update project task example", "value": {"description": "Updated authentication system implementation", "due_date": "2024-01-15", "employee_id": "123e4567-e89b-12d3-a456-426614174004", "completion_status": "on_progress"}}, "createKpiProjectExample": {"summary": "Example create KPI Project request", "value": {"project_name": "ERP System Development", "project_id": "PROJ-12345", "description": "Develop a new ERP system for client XYZ", "target": "Complete all modules by Q2 2024", "period": "2024-Q1", "status": "not_started", "additional_notes": "Initial project setup phase"}}, "kpiProjectResponseExample": {"summary": "Example KPI Project response", "value": {"success": true, "message": "KPI Project created successfully", "data": {"id": "example-kpi-project-id-12345", "project_name": "ERP System Development", "project_id": "PROJ-12345", "description": "Develop a new ERP system for client XYZ", "target": "Complete all modules by Q2 2024", "period": "2024-Q1", "status": "not_started", "additional_notes": "Initial project setup phase", "created_at": "2023-12-01T00:00:00.000Z", "created_by": "example-creator-id-12345", "updated_at": null, "updated_by": null, "deleted_at": null, "deleted_by": null}}}}, "schemas": {"SignUpRequest": {"type": "object", "properties": {"email": {"type": "string", "format": "email", "example": "<EMAIL>"}, "password": {"type": "string", "minLength": 8, "example": "securepassword123"}, "fullname": {"type": "string", "example": "<PERSON>"}, "phonenum": {"type": "string", "example": "628*********0"}, "role": {"type": "string", "enum": ["Admin", "Manager", "HR", "Finance", "Operation", "Client"], "example": "Client"}}, "required": ["email", "password", "fullname", "phonenum", "role"]}, "SignInRequest": {"type": "object", "properties": {"email": {"type": "string", "format": "email", "example": "<EMAIL>"}, "password": {"type": "string", "minLength": 8, "example": "securepassword123"}}, "required": ["email", "password"]}, "RefreshTokenRequest": {"type": "object", "properties": {"refresh_token": {"type": "string", "example": "yHVhQTC553L3oTBsEGsDHQ", "description": "Refresh token used to get a new access token"}}, "required": ["refresh_token"]}, "UserWithProfile": {"type": "object", "description": "User with combined auth and profile data", "properties": {"id": {"type": "string", "format": "uuid", "description": "Auth user ID"}, "email": {"type": "string", "format": "email", "description": "User's email address from auth system"}, "profile": {"type": "object", "description": "User profile data", "$ref": "#/components/schemas/UserProfile"}}}, "UserProfile": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid", "description": "Profile ID (primary key in user_profiles table)"}, "user_id": {"type": "string", "format": "uuid", "description": "Auth user ID (foreign key to auth.users table)"}, "fullname": {"type": "string"}, "phonenum": {"type": "string"}, "role": {"type": "string", "enum": ["Admin", "Manager", "HR", "Finance", "Operation", "Client"]}, "org_id": {"type": "string", "format": "uuid", "nullable": true}, "employee_id": {"type": "string", "format": "uuid", "nullable": true}, "is_active": {"type": "boolean", "default": false}, "created_at": {"type": "string", "format": "date-time"}, "created_by": {"type": "string", "format": "uuid"}, "updated_at": {"type": "string", "format": "date-time", "nullable": true}, "updated_by": {"type": "string", "format": "uuid", "nullable": true}, "deleted_at": {"type": "string", "format": "date-time", "nullable": true}, "deleted_by": {"type": "string", "format": "uuid", "nullable": true}}}, "PaginationResult": {"type": "object", "properties": {"total": {"type": "integer"}, "page": {"type": "integer"}, "pageSize": {"type": "integer"}, "totalPages": {"type": "integer"}}}, "ActivateUserRequest": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid", "description": "Profile ID to activate (from user_profiles table, not auth user_id)"}, "org_id": {"type": "string", "format": "uuid", "description": "Organization ID (required for Client role)"}}, "required": ["id"]}, "GenericSuccessResponse": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "message": {"type": "string"}, "data": {"type": "object"}}}, "GenericErrorResponse": {"type": "object", "properties": {"success": {"type": "boolean", "example": false}, "message": {"type": "string"}, "data": {"type": "object", "nullable": true, "example": null}, "error": {"type": "object", "properties": {"code": {"type": "string"}, "details": {"type": "object", "nullable": true}}}}}, "UpdateInvoiceDto": {"type": "object", "properties": {"invoice_type": {"type": "string", "enum": ["external", "internal"], "description": "The type of the invoice (external or internal)"}, "service_type": {"type": "string", "enum": ["HCM", "ORDEV", "BE", "IT", "MARKETING", "FINANCE", "SALES", "OTHER"], "description": "The service type for the invoice"}, "recipient_name": {"type": "string", "minLength": 1, "maxLength": 100, "description": "The name of the recipient"}, "project_id": {"type": "string", "description": "The ID of the project"}, "project_name": {"type": "string", "description": "The name of the project"}, "due_date": {"type": "string", "pattern": "^\\d{4}-\\d{2}-\\d{2}$", "description": "The due date of the invoice (YYYY-MM-DD format)"}, "payment_method": {"type": "string", "enum": ["bank_transfer", "cash", "credit_card", "cheque", "other"], "description": "The method of payment"}, "payment_status": {"type": "string", "enum": ["pending", "partial", "paid", "overdue", "cancelled"], "description": "The status of the payment"}, "notes": {"type": "string", "description": "Additional notes for the invoice"}, "items": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid", "description": "The ID of an existing item (when updating)"}, "item_name": {"type": "string", "description": "The name of the item"}, "item_amount": {"type": "number", "description": "The amount/quantity of the item"}, "item_price": {"type": "number", "description": "The price per unit of the item"}}}}}}, "CreateInvoiceDto": {"type": "object", "required": ["invoice_type", "service_type", "recipient_name", "due_date", "payment_method", "items"], "properties": {"invoice_type": {"type": "string", "enum": ["external", "internal"], "description": "The type of the invoice (external or internal)"}, "service_type": {"type": "string", "enum": ["HCM", "ORDEV", "BE", "IT", "MARKETING", "FINANCE", "SALES", "OTHER"], "description": "The service type for the invoice"}, "recipient_name": {"type": "string", "minLength": 1, "maxLength": 100, "description": "The name of the recipient"}, "project_id": {"type": "string", "description": "The ID of the project (optional)"}, "project_name": {"type": "string", "description": "The name of the project (optional)"}, "due_date": {"type": "string", "pattern": "^\\d{4}-\\d{2}-\\d{2}$", "description": "The due date of the invoice (YYYY-MM-DD format)"}, "payment_method": {"type": "string", "enum": ["bank_transfer", "cash", "credit_card", "cheque", "other"], "description": "The method of payment"}, "payment_status": {"type": "string", "enum": ["pending", "partial", "paid", "overdue", "cancelled"], "description": "The status of the payment (optional, defaults to PENDING)"}, "notes": {"type": "string", "description": "Additional notes for the invoice (optional)"}, "items": {"type": "array", "items": {"type": "object", "required": ["item_name", "item_amount", "item_price"], "properties": {"item_name": {"type": "string", "description": "The name of the item"}, "item_amount": {"type": "number", "description": "The amount/quantity of the item"}, "item_price": {"type": "number", "description": "The price per unit of the item"}}}}}}, "Organization": {"type": "object", "required": ["id", "name", "phone", "address", "client_type", "created_at", "created_by"], "properties": {"id": {"type": "string", "format": "uuid", "description": "Unique identifier for the organization"}, "name": {"type": "string", "minLength": 2, "maxLength": 100, "description": "Organization name (2-100 characters)"}, "phone": {"type": "string", "pattern": "^[0-9]+$", "minLength": 10, "description": "Phone number (minimum 10 digits, only numbers allowed)"}, "address": {"type": "string", "minLength": 5, "maxLength": 500, "description": "Organization address (5-500 characters)"}, "client_type": {"type": "string", "minLength": 2, "maxLength": 50, "description": "Type of client organization (2-50 characters)"}, "notes": {"type": "string", "nullable": true, "maxLength": 1000, "description": "Optional notes about the organization (max 1000 characters)"}, "created_at": {"type": "string", "format": "date-time", "description": "Timestamp when the organization was created"}, "created_by": {"type": "string", "description": "ID of the user who created the organization"}, "updated_at": {"type": "string", "format": "date-time", "nullable": true, "description": "Timestamp when the organization was last updated"}, "updated_by": {"type": "string", "nullable": true, "description": "ID of the user who last updated the organization"}, "deleted_at": {"type": "string", "format": "date-time", "nullable": true, "description": "Timestamp when the organization was deleted (soft delete)"}, "deleted_by": {"type": "string", "nullable": true, "description": "ID of the user who deleted the organization"}}}, "CreateOrganizationDto": {"type": "object", "required": ["name", "phone", "address", "client_type"], "properties": {"name": {"type": "string", "minLength": 2, "maxLength": 100, "description": "Organization name (2-100 characters)"}, "phone": {"type": "string", "pattern": "^[0-9]+$", "minLength": 10, "description": "Phone number (minimum 10 digits, only numbers allowed)"}, "address": {"type": "string", "minLength": 5, "maxLength": 500, "description": "Organization address (5-500 characters)"}, "client_type": {"type": "string", "minLength": 2, "maxLength": 50, "description": "Type of client organization (2-50 characters)"}, "notes": {"type": "string", "nullable": true, "maxLength": 1000, "description": "Optional notes about the organization (max 1000 characters)"}}}, "UpdateOrganizationDto": {"type": "object", "properties": {"name": {"type": "string", "minLength": 2, "maxLength": 100, "description": "Organization name (2-100 characters)"}, "phone": {"type": "string", "pattern": "^[0-9]+$", "minLength": 10, "description": "Phone number (minimum 10 digits, only numbers allowed)"}, "address": {"type": "string", "minLength": 5, "maxLength": 500, "description": "Organization address (5-500 characters)"}, "client_type": {"type": "string", "minLength": 2, "maxLength": 50, "description": "Type of client organization (2-50 characters)"}, "notes": {"type": "string", "nullable": true, "maxLength": 1000, "description": "Optional notes about the organization (max 1000 characters)"}}}, "GenerateSalaryDto": {"type": "object", "required": ["employeeId", "period"], "properties": {"employeeId": {"type": "string", "format": "uuid", "description": "The employee ID for whom the salary is generated"}, "period": {"type": "string", "pattern": "^\\d{4}-\\d{2}$", "description": "Salary period in format YYYY-MM"}}}, "UpdateSalaryDto": {"type": "object", "properties": {"base_salary": {"type": "number", "minimum": 0, "description": "Base salary amount"}, "payment_status": {"type": "string", "enum": ["unpaid", "paid"], "description": "Payment status of the salary"}}}, "Kpi": {"type": "object", "description": "KPI record", "properties": {"id": {"type": "string", "format": "uuid", "description": "KPI ID (primary key)"}, "full_name": {"type": "string", "description": "The full name of the employee"}, "employee_id": {"type": "string", "description": "The ID of the employee"}, "description": {"type": "string", "description": "The description of the KPI"}, "target": {"type": "string", "description": "The target of the KPI"}, "period": {"type": "string", "description": "The period of the KPI (e.g., '2024-Q1')"}, "status": {"type": "string", "description": "The status of the KPI", "enum": ["not_started", "in_progress", "completed_below_target", "completed_on_target", "completed_above_target"]}, "bonus_received": {"type": "number", "description": "The bonus amount received", "nullable": true}, "additional_notes": {"type": "string", "description": "Additional notes for the KPI", "nullable": true}, "created_at": {"type": "string", "format": "date-time"}, "created_by": {"type": "string", "format": "uuid"}, "updated_at": {"type": "string", "format": "date-time", "nullable": true}, "updated_by": {"type": "string", "format": "uuid", "nullable": true}, "deleted_at": {"type": "string", "format": "date-time", "nullable": true}, "deleted_by": {"type": "string", "format": "uuid", "nullable": true}}}, "CreateKpiRequest": {"type": "object", "description": "Request to create a new KPI", "properties": {"full_name": {"type": "string", "description": "The full name of the employee"}, "employee_id": {"type": "string", "description": "The ID of the employee"}, "description": {"type": "string", "description": "The description of the KPI"}, "target": {"type": "string", "description": "The target of the KPI"}, "period": {"type": "string", "description": "The period of the KPI (e.g., '2024-Q1')"}, "status": {"type": "string", "description": "The status of the KPI", "enum": ["not_started", "in_progress", "completed_below_target", "completed_on_target", "completed_above_target"]}, "bonus_received": {"type": "number", "description": "The bonus amount received", "nullable": true}, "additional_notes": {"type": "string", "description": "Additional notes for the KPI", "nullable": true}}, "required": ["full_name", "employee_id", "description", "target", "period"]}, "UpdateKpiRequest": {"type": "object", "description": "Request to update an existing KPI", "properties": {"full_name": {"type": "string", "description": "The full name of the employee"}, "employee_id": {"type": "string", "description": "The ID of the employee"}, "description": {"type": "string", "description": "The description of the KPI"}, "target": {"type": "string", "description": "The target of the KPI"}, "period": {"type": "string", "description": "The period of the KPI (e.g., '2024-Q1')"}, "status": {"type": "string", "description": "The status of the KPI", "enum": ["not_started", "in_progress", "completed_below_target", "completed_on_target", "completed_above_target"]}, "bonus_received": {"type": "number", "description": "The bonus amount received", "nullable": true}, "additional_notes": {"type": "string", "description": "Additional notes for the KPI", "nullable": true}}}, "UpdateKpiStatusRequest": {"type": "object", "description": "Request to update a KPI's status", "properties": {"status": {"type": "string", "description": "The status of the KPI", "enum": ["not_started", "in_progress", "completed_below_target", "completed_on_target", "completed_above_target"]}}, "required": ["status"]}, "UpdateKpiBonusRequest": {"type": "object", "description": "Request to update a KPI's bonus amount", "properties": {"bonus_received": {"type": "number", "description": "The bonus amount received", "min": 0}}, "required": ["bonus_received"]}, "KpiListResponse": {"type": "object", "description": "Response containing a list of KPIs", "properties": {"items": {"type": "array", "items": {"$ref": "#/components/schemas/Kpi"}}, "pagination": {"$ref": "#/components/schemas/PaginationResult"}}}, "Employee": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid", "description": "Unique identifier for the employee"}, "profile_id": {"type": "string", "format": "uuid", "description": "The ID of the user profile associated with this employee"}, "dob": {"type": "string", "format": "date", "description": "Date of birth (YYYY-MM-DD)"}, "address": {"type": "string", "minLength": 1, "maxLength": 255, "description": "Complete address of the employee"}, "bank_account": {"type": "string", "minLength": 1, "maxLength": 50, "description": "Bank account number"}, "bank_name": {"type": "string", "minLength": 1, "maxLength": 50, "description": "Name of the bank"}, "employment_status": {"type": "string", "enum": ["Intern", "Fulltime", "Not provided"], "description": "Employment status (Intern/Fulltime/Not provided)"}, "presence_status": {"type": "string", "enum": ["present", "absent", "permit", "leave"], "description": "Current presence status"}, "department": {"type": "string", "enum": ["Admin", "Manager", "HR", "Finance", "Operation", "Client"], "description": "Department/role of the employee"}, "start_date": {"type": "string", "format": "date", "description": "Employment start date (YYYY-MM-DD)"}, "salary_id": {"type": "string", "format": "uuid", "nullable": true, "description": "The ID of the salary record for this employee"}, "created_at": {"type": "string", "format": "date-time", "description": "Creation timestamp"}, "created_by": {"type": "string", "description": "ID of the user who created the record"}, "updated_at": {"type": "string", "format": "date-time", "nullable": true, "description": "Last update timestamp"}, "updated_by": {"type": "string", "nullable": true, "description": "ID of the user who last updated the record"}, "deleted_at": {"type": "string", "format": "date-time", "nullable": true, "description": "Deletion timestamp"}, "deleted_by": {"type": "string", "nullable": true, "description": "ID of the user who deleted the record"}, "user_profiles": {"type": "object", "properties": {"fullname": {"type": "string", "description": "Full name of the employee"}, "email": {"type": "string", "format": "email", "description": "Email address of the employee"}, "phonenum": {"type": "string", "description": "Phone number of the employee"}}}}}, "GetAllEmployeesQuery": {"type": "object", "properties": {"search": {"type": "string", "description": "Search term to filter employees by name, email, or phone"}, "page": {"type": "number", "minimum": 1, "description": "Page number for pagination (starts at 1)"}, "pageSize": {"type": "number", "minimum": 1, "maximum": 100, "description": "Number of items per page"}, "department": {"type": "string", "enum": ["Admin", "Manager", "HR", "Finance", "Operation", "Client"], "description": "Filter by department/role"}, "employment_status": {"type": "string", "enum": ["Intern", "Fulltime", "Not provided"], "description": "Filter by employment status"}, "presence_status": {"type": "string", "enum": ["present", "absent", "permit", "leave"], "description": "Filter by presence status"}}}, "UpdateEmployeeDto": {"type": "object", "properties": {"dob": {"type": "string", "format": "date", "description": "Date of birth (YYYY-MM-DD)"}, "address": {"type": "string", "minLength": 1, "maxLength": 255, "description": "Complete address of the employee"}, "bank_account": {"type": "string", "minLength": 1, "maxLength": 50, "description": "Bank account number"}, "bank_name": {"type": "string", "minLength": 1, "maxLength": 50, "description": "Name of the bank"}, "employment_status": {"type": "string", "enum": ["Intern", "Fulltime", "Not provided"], "description": "Employment status (Intern/Fulltime/Not provided)"}, "presence_status": {"type": "string", "enum": ["present", "absent", "permit", "leave"], "description": "Current presence status"}, "department": {"type": "string", "enum": ["Admin", "Manager", "HR", "Finance", "Operation", "Client"], "description": "Department/role of the employee"}, "start_date": {"type": "string", "format": "date", "description": "Employment start date (YYYY-MM-DD)"}, "salary_id": {"type": "string", "format": "uuid", "nullable": true, "description": "The ID of the salary record for this employee"}}}, "Attendance": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid", "description": "Unique attendance ID"}, "date": {"type": "string", "format": "date", "description": "Attendance date"}, "employee_id": {"type": "string", "description": "Employee ID"}, "status": {"type": "string", "enum": ["present", "absent", "permit", "leave"], "description": "Presence status"}, "clock_in": {"type": "string", "format": "time", "description": "Clock-in time"}, "clock_out": {"type": "string", "format": "time", "description": "Clock-out time"}, "notes": {"type": "string", "maxLength": 1000, "description": "Additional notes"}}}, "CreateAttendanceDto": {"type": "object", "properties": {"status": {"type": "string", "enum": ["present", "absent", "permit", "leave"], "description": "Presence status (present, absent, permit, leave)"}, "notes": {"type": "string", "maxLength": 1000, "description": "Optional notes about attendance"}, "tasks": {"type": "array", "items": {"type": "object", "properties": {"description": {"type": "string", "minLength": 3, "maxLength": 255, "description": "Task description"}, "due_date": {"type": "string", "pattern": "^\\d{4}-\\d{2}-\\d{2}$", "description": "Due date in format YYYY-MM-DD"}, "completion_status": {"type": "string", "enum": ["not_completed", "on_progress", "completed"], "description": "Task completion status", "default": "not_completed"}}, "required": ["description", "due_date"]}, "description": "Array of tasks to be created with attendance"}}, "required": ["status"]}, "EmployeeAttendanceRequest": {"type": "object", "properties": {"employee_id": {"type": "string", "description": "Employee ID"}, "fromDate": {"type": "string", "pattern": "^\\d{4}-\\d{2}-\\d{2}$", "description": "Start date in YYYY-MM-DD format"}, "toDate": {"type": "string", "pattern": "^\\d{4}-\\d{2}-\\d{2}$", "description": "End date in YYYY-MM-DD format"}, "status": {"type": "string", "enum": ["present", "absent", "permit", "leave"], "description": "Filter by attendance status"}, "search": {"type": "string", "description": "Search term to find in attendance notes"}, "page": {"type": "integer", "minimum": 1, "default": 1, "description": "Page number for pagination"}, "pageSize": {"type": "integer", "minimum": 1, "maximum": 100, "default": 10, "description": "Number of records per page"}}, "required": ["employee_id"]}, "EmployeeAttendanceResponse": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "message": {"type": "string"}, "data": {"type": "object", "properties": {"data": {"type": "array", "items": {"$ref": "#/components/schemas/AttendanceWithTasks"}}, "pagination": {"$ref": "#/components/schemas/PaginationResult"}}}}}, "PersonalAttendanceResponse": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "message": {"type": "string"}, "data": {"type": "object", "properties": {"data": {"type": "array", "items": {"$ref": "#/components/schemas/AttendanceWithTasks"}}, "pagination": {"$ref": "#/components/schemas/PaginationResult"}}}}}, "AttendanceWithTasks": {"type": "object", "properties": {"id": {"type": "string"}, "employee_id": {"type": "string"}, "date": {"type": "string"}, "clock_in": {"type": "string"}, "clock_out": {"type": "string", "nullable": true}, "status": {"type": "string"}, "notes": {"type": "string", "nullable": true}, "tasks": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "string"}, "description": {"type": "string"}, "due_date": {"type": "string"}, "completion_status": {"type": "string", "enum": ["not_completed", "on_progress", "completed"]}}}}}}, "BonusSalary": {"type": "object", "required": ["id", "salary_id", "amount", "bonus_type", "created_at", "created_by"], "properties": {"id": {"type": "string", "format": "uuid", "description": "Unique identifier for the bonus"}, "salary_id": {"type": "string", "format": "uuid", "description": "ID of the salary this bonus belongs to"}, "amount": {"type": "number", "minimum": 0, "description": "Bonus amount"}, "bonus_type": {"type": "string", "enum": ["kpi", "project", "other"], "description": "Type of bonus"}, "notes": {"type": "string", "nullable": true, "description": "Additional notes about the bonus"}, "kpi_id": {"type": "string", "format": "uuid", "nullable": true, "description": "ID of the KPI record (required for KPI type bonuses)"}, "project_id": {"type": "string", "format": "uuid", "nullable": true, "description": "ID of the project (for project type bonuses)"}, "created_at": {"type": "string", "format": "date-time", "description": "Timestamp when the bonus was created"}, "created_by": {"type": "string", "description": "ID of the user who created the bonus"}, "updated_at": {"type": "string", "format": "date-time", "nullable": true, "description": "Timestamp when the bonus was last updated"}, "updated_by": {"type": "string", "nullable": true, "description": "ID of the user who last updated the bonus"}, "deleted_at": {"type": "string", "format": "date-time", "nullable": true, "description": "Timestamp when the bonus was deleted"}, "deleted_by": {"type": "string", "nullable": true, "description": "ID of the user who deleted the bonus"}}}, "CreateBonusSalaryDto": {"type": "object", "required": ["salary_id", "amount", "bonus_type"], "properties": {"salary_id": {"type": "string", "format": "uuid", "description": "ID of the salary this bonus belongs to"}, "amount": {"type": "number", "minimum": 0, "description": "Bonus amount"}, "bonus_type": {"type": "string", "enum": ["kpi", "project", "other"], "description": "Type of bonus"}, "notes": {"type": "string", "nullable": true, "description": "Additional notes about the bonus"}, "kpi_id": {"type": "string", "format": "uuid", "nullable": true, "description": "ID of the KPI record (required for KPI type bonuses)"}, "project_id": {"type": "string", "format": "uuid", "nullable": true, "description": "ID of the project (for project type bonuses)"}}}, "DeductionSalary": {"type": "object", "required": ["id", "salary_id", "amount", "deduction_type", "created_at", "created_by"], "properties": {"id": {"type": "string", "format": "uuid", "description": "Unique identifier"}, "salary_id": {"type": "string", "format": "uuid", "description": "ID of the associated salary record"}, "amount": {"type": "number", "description": "Amount of the deduction"}, "deduction_type": {"type": "string", "enum": ["absence", "lateness", "loan", "other"], "description": "Type of deduction"}, "notes": {"type": "string", "description": "Additional notes about the deduction"}, "created_at": {"type": "string", "format": "date-time", "description": "Timestamp when the deduction was created"}, "created_by": {"type": "string", "format": "uuid", "description": "ID of the user who created the deduction"}, "updated_at": {"type": "string", "format": "date-time", "nullable": true, "description": "Timestamp when the deduction was last updated"}, "updated_by": {"type": "string", "format": "uuid", "nullable": true, "description": "ID of the user who last updated the deduction"}, "deleted_at": {"type": "string", "format": "date-time", "nullable": true, "description": "Timestamp when the deduction was deleted"}, "deleted_by": {"type": "string", "format": "uuid", "nullable": true, "description": "ID of the user who deleted the deduction"}}}, "CreateDeductionSalaryDto": {"type": "object", "required": ["salary_id", "amount", "deduction_type"], "properties": {"salary_id": {"type": "string", "format": "uuid", "description": "ID of the associated salary record"}, "amount": {"type": "number", "description": "Amount of the deduction"}, "deduction_type": {"type": "string", "enum": ["absence", "lateness", "loan", "other"], "description": "Type of deduction"}, "notes": {"type": "string", "description": "Additional notes about the deduction"}}}, "UpdateDeductionSalaryDto": {"type": "object", "properties": {"amount": {"type": "number", "description": "Amount of the deduction"}, "deduction_type": {"type": "string", "enum": ["absence", "lateness", "loan", "other"], "description": "Type of deduction"}, "notes": {"type": "string", "description": "Additional notes about the deduction"}}}, "DeleteDeductionSalaryResponse": {"type": "object", "required": ["success", "message", "data"], "properties": {"success": {"type": "boolean", "description": "Whether the operation was successful"}, "message": {"type": "string", "description": "Success or error message"}, "data": {"type": "object", "required": ["id"], "properties": {"id": {"type": "string", "format": "uuid", "description": "ID of the deleted deduction"}}}}}, "AllowanceSalary": {"type": "object", "required": ["id", "salary_id", "amount", "allowance_type", "created_at", "created_by"], "properties": {"id": {"type": "string", "format": "uuid", "description": "Unique identifier for the allowance"}, "salary_id": {"type": "string", "format": "uuid", "description": "ID of the salary this allowance belongs to"}, "amount": {"type": "number", "minimum": 0, "description": "Allowance amount"}, "allowance_type": {"type": "string", "enum": ["transport", "meal", "health", "position", "tenure", "thr", "other"], "description": "Type of allowance"}, "notes": {"type": "string", "nullable": true, "description": "Additional notes about the allowance"}, "created_at": {"type": "string", "format": "date-time", "description": "Timestamp when the allowance was created"}, "created_by": {"type": "string", "description": "ID of the user who created the allowance"}, "updated_at": {"type": "string", "format": "date-time", "nullable": true, "description": "Timestamp when the allowance was last updated"}, "updated_by": {"type": "string", "nullable": true, "description": "ID of the user who last updated the allowance"}, "deleted_at": {"type": "string", "format": "date-time", "nullable": true, "description": "Timestamp when the allowance was deleted"}, "deleted_by": {"type": "string", "nullable": true, "description": "ID of the user who deleted the allowance"}}}, "CreateAllowanceSalaryDto": {"type": "object", "required": ["salary_id", "amount", "allowance_type"], "properties": {"salary_id": {"type": "string", "format": "uuid", "description": "ID of the salary this allowance belongs to"}, "amount": {"type": "number", "minimum": 0, "description": "Allowance amount"}, "allowance_type": {"type": "string", "enum": ["transport", "meal", "health", "position", "tenure", "thr", "other"], "description": "Type of allowance"}, "notes": {"type": "string", "nullable": true, "description": "Additional notes about the allowance"}}}, "Task": {"type": "object", "required": ["id", "description", "completion_status", "employee_id", "due_date", "created_at", "created_by"], "properties": {"id": {"type": "string", "format": "uuid", "description": "Unique identifier"}, "description": {"type": "string", "description": "Task description"}, "completion_status": {"type": "string", "enum": ["not_completed", "on_progress", "completed"], "description": "Task completion status"}, "employee_id": {"type": "string", "format": "uuid", "description": "Employee ID assigned to the task"}, "due_date": {"type": "string", "format": "date", "description": "Due date in format YYYY-MM-DD"}, "attendance_id": {"type": "string", "format": "uuid", "description": "Attendance ID (optional)", "nullable": true}, "created_at": {"type": "string", "format": "date-time", "description": "Creation timestamp"}, "created_by": {"type": "string", "format": "uuid", "description": "User ID who created the record"}, "updated_at": {"type": "string", "format": "date-time", "description": "Last update timestamp", "nullable": true}, "updated_by": {"type": "string", "format": "uuid", "description": "User ID who last updated the record", "nullable": true}, "deleted_at": {"type": "string", "format": "date-time", "description": "Deletion timestamp", "nullable": true}, "deleted_by": {"type": "string", "format": "uuid", "description": "User ID who deleted the record", "nullable": true}}}, "UpdateTaskStatusDto": {"type": "object", "required": ["completion_status"], "properties": {"completion_status": {"type": "string", "enum": ["not_completed", "on_progress", "completed"], "description": "Task completion status"}}}, "ProjectTask": {"type": "object", "required": ["id", "assigned_by", "description", "completion_status", "employee_id", "initial_date", "due_date", "project_id", "created_at", "created_by"], "properties": {"id": {"type": "string", "format": "uuid", "description": "Unique identifier"}, "assigned_by": {"type": "string", "format": "uuid", "description": "UUID of the user who assigned the task"}, "description": {"type": "string", "description": "Task description"}, "completion_status": {"type": "string", "enum": ["not_completed", "on_progress", "completed"], "description": "Task completion status (not_completed, on_progress, completed)"}, "employee_id": {"type": "string", "format": "uuid", "description": "Employee ID assigned to the task"}, "initial_date": {"type": "string", "format": "date", "description": "Start date in YYYY-MM-DD format"}, "due_date": {"type": "string", "format": "date", "description": "Due date in YYYY-MM-DD format"}, "project_id": {"type": "string", "format": "uuid", "description": "Project ID"}, "weekly_log_id": {"type": "string", "format": "uuid", "description": "Optional UUID of the associated weekly log", "nullable": true}, "created_at": {"type": "string", "format": "date-time", "description": "Creation timestamp"}, "created_by": {"type": "string", "format": "uuid", "description": "User ID who created the record"}, "updated_at": {"type": "string", "format": "date-time", "description": "Last update timestamp", "nullable": true}, "updated_by": {"type": "string", "format": "uuid", "description": "User ID who last updated the record", "nullable": true}, "deleted_at": {"type": "string", "format": "date-time", "description": "Deletion timestamp", "nullable": true}, "deleted_by": {"type": "string", "format": "uuid", "description": "User ID who deleted the record", "nullable": true}}}, "CreateProjectTaskDto": {"type": "object", "required": ["assigned_by", "description", "employee_id", "initial_date", "due_date", "project_id"], "properties": {"assigned_by": {"type": "string", "format": "uuid", "description": "UUID of the user who assigned the task"}, "description": {"type": "string", "minLength": 3, "maxLength": 255, "description": "Task description"}, "completion_status": {"type": "string", "enum": ["not_completed", "on_progress", "completed"], "description": "Task completion status", "default": "not_completed"}, "employee_id": {"type": "string", "format": "uuid", "description": "Employee ID assigned to the task"}, "initial_date": {"type": "string", "format": "date", "description": "Start date in YYYY-MM-DD format"}, "due_date": {"type": "string", "format": "date", "description": "Due date in YYYY-MM-DD format"}, "project_id": {"type": "string", "format": "uuid", "description": "Project ID"}, "weekly_log_id": {"type": "string", "format": "uuid", "description": "Optional UUID of the associated weekly log", "nullable": true}}}, "UpdateProjectTaskStatusDto": {"type": "object", "required": ["completion_status"], "properties": {"completion_status": {"type": "string", "enum": ["not_completed", "on_progress", "completed"], "description": "Task completion status (not_completed, on_progress, completed)"}}}, "KpiProject": {"type": "object", "description": "KPI Project record", "properties": {"id": {"type": "string", "format": "uuid", "description": "KPI Project ID (primary key)"}, "project_name": {"type": "string", "description": "The name of the project"}, "project_id": {"type": "string", "description": "The ID of the project"}, "description": {"type": "string", "description": "The description of the KPI project"}, "target": {"type": "string", "description": "The target of the KPI project"}, "period": {"type": "string", "description": "The period of the KPI project (e.g., '2024-Q1')"}, "status": {"type": "string", "description": "The status of the KPI project", "enum": ["not_started", "in_progress", "completed_below_target", "completed_on_target", "completed_above_target"]}, "additional_notes": {"type": "string", "nullable": true, "description": "Additional notes for the KPI project"}, "created_at": {"type": "string", "format": "date-time", "description": "Creation timestamp"}, "created_by": {"type": "string", "format": "uuid", "description": "User ID who created the record"}, "updated_at": {"type": "string", "format": "date-time", "nullable": true, "description": "Last update timestamp"}, "updated_by": {"type": "string", "format": "uuid", "nullable": true, "description": "User ID who last updated the record"}, "deleted_at": {"type": "string", "format": "date-time", "nullable": true, "description": "Soft deletion timestamp"}, "deleted_by": {"type": "string", "format": "uuid", "nullable": true, "description": "User ID who deleted the record"}}}}}, "security": [{"bearerAuth": []}], "paths": {"/v1/auth/sign-up": {"post": {"parameters": [], "operationId": "postV1AuthSign-up", "tags": ["auth"], "summary": "Register a new user", "description": "Create a new user account with email, password, and profile information.\n\nExample request body:\n```json\n{\n  \"email\": \"<EMAIL>\",\n  \"password\": \"securepassword123\",\n  \"fullname\": \"<PERSON>\",\n  \"phonenum\": \"628*********0\",\n  \"role\": \"Client\"\n}\n```", "security": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"description": "User registration information", "type": "object", "properties": {"email": {"format": "email", "description": "User email address in valid email format", "type": "string"}, "password": {"minLength": 8, "description": "Password with minimum 8 characters", "type": "string"}, "fullname": {"minLength": 2, "maxLength": 100, "description": "User's full name (2-100 characters)", "type": "string"}, "phonenum": {"pattern": "^[0-9]+$", "minLength": 10, "description": "Phone number (minimum 10 digits, only numbers allowed)", "type": "string"}, "role": {"description": "User role (Manager, HR, Finance, Operation, Client)", "anyOf": [{"const": "Admin", "type": "string"}, {"const": "Manager", "type": "string"}, {"const": "HR", "type": "string"}, {"const": "Finance", "type": "string"}, {"const": "Operation", "type": "string"}, {"const": "Client", "type": "string"}]}}, "required": ["email", "password", "fullname", "phonenum", "role"]}}, "multipart/form-data": {"schema": {"description": "User registration information", "type": "object", "properties": {"email": {"format": "email", "description": "User email address in valid email format", "type": "string"}, "password": {"minLength": 8, "description": "Password with minimum 8 characters", "type": "string"}, "fullname": {"minLength": 2, "maxLength": 100, "description": "User's full name (2-100 characters)", "type": "string"}, "phonenum": {"pattern": "^[0-9]+$", "minLength": 10, "description": "Phone number (minimum 10 digits, only numbers allowed)", "type": "string"}, "role": {"description": "User role (Manager, HR, Finance, Operation, Client)", "anyOf": [{"const": "Admin", "type": "string"}, {"const": "Manager", "type": "string"}, {"const": "HR", "type": "string"}, {"const": "Finance", "type": "string"}, {"const": "Operation", "type": "string"}, {"const": "Client", "type": "string"}]}}, "required": ["email", "password", "fullname", "phonenum", "role"]}}, "text/plain": {"schema": {"description": "User registration information", "type": "object", "properties": {"email": {"format": "email", "description": "User email address in valid email format", "type": "string"}, "password": {"minLength": 8, "description": "Password with minimum 8 characters", "type": "string"}, "fullname": {"minLength": 2, "maxLength": 100, "description": "User's full name (2-100 characters)", "type": "string"}, "phonenum": {"pattern": "^[0-9]+$", "minLength": 10, "description": "Phone number (minimum 10 digits, only numbers allowed)", "type": "string"}, "role": {"description": "User role (Manager, HR, Finance, Operation, Client)", "anyOf": [{"const": "Admin", "type": "string"}, {"const": "Manager", "type": "string"}, {"const": "HR", "type": "string"}, {"const": "Finance", "type": "string"}, {"const": "Operation", "type": "string"}, {"const": "Client", "type": "string"}]}}, "required": ["email", "password", "fullname", "phonenum", "role"]}}}}, "responses": {"200": {}}}}, "/v1/auth/sign-in": {"post": {"parameters": [], "operationId": "postV1AuthSign-in", "tags": ["auth"], "summary": "Authenticate a user", "description": "Sign in with email and password to get authentication token.\n\nExample request body:\n```json\n{\n  \"email\": \"<EMAIL>\",\n  \"password\": \"securepassword123\"\n}\n```", "security": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"description": "User authentication credentials", "type": "object", "properties": {"email": {"format": "email", "description": "User email address in valid email format", "type": "string"}, "password": {"minLength": 8, "description": "Password with minimum 8 characters", "type": "string"}}, "required": ["email", "password"]}}, "multipart/form-data": {"schema": {"description": "User authentication credentials", "type": "object", "properties": {"email": {"format": "email", "description": "User email address in valid email format", "type": "string"}, "password": {"minLength": 8, "description": "Password with minimum 8 characters", "type": "string"}}, "required": ["email", "password"]}}, "text/plain": {"schema": {"description": "User authentication credentials", "type": "object", "properties": {"email": {"format": "email", "description": "User email address in valid email format", "type": "string"}, "password": {"minLength": 8, "description": "Password with minimum 8 characters", "type": "string"}}, "required": ["email", "password"]}}}}, "responses": {"200": {}}}}, "/v1/auth/sign-out": {"post": {"operationId": "postV1AuthSign-out", "tags": ["auth"], "summary": "Sign out the current user", "description": "Invalidate the current session using JWT token in Authorization header", "security": [{"bearerAuth": []}], "responses": {"200": {}}}}, "/v1/auth/refresh": {"post": {"parameters": [], "operationId": "postV1AuthRefresh", "tags": ["auth"], "summary": "Refresh authentication token", "description": "Use a refresh token to get a new authentication token", "security": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"description": "Token for refreshing user session", "type": "object", "properties": {"refresh_token": {"description": "Refresh token used to get a new access token", "type": "string"}}, "required": ["refresh_token"]}}, "multipart/form-data": {"schema": {"description": "Token for refreshing user session", "type": "object", "properties": {"refresh_token": {"description": "Refresh token used to get a new access token", "type": "string"}}, "required": ["refresh_token"]}}, "text/plain": {"schema": {"description": "Token for refreshing user session", "type": "object", "properties": {"refresh_token": {"description": "Refresh token used to get a new access token", "type": "string"}}, "required": ["refresh_token"]}}}}, "responses": {"200": {}}}}, "/v1/auth/me/": {"get": {"operationId": "getV1AuthMe", "tags": ["auth"], "summary": "Get current user profile", "description": "Retrieve the profile information for the authenticated user", "security": [{"bearerAuth": []}], "responses": {"200": {}}}}, "/v1/examples/public/": {"get": {"operationId": "getV1ExamplesPublic", "tags": ["examples"], "summary": "Public endpoint for authenticated users", "description": "Example endpoint accessible to any authenticated user", "security": [{"bearerAuth": []}], "responses": {"200": {}}}}, "/v1/examples/role-based/managers": {"get": {"operationId": "getV1ExamplesRole-basedManagers", "tags": ["examples"], "summary": "Manager-only endpoint", "description": "Example endpoint accessible only to users with the Manager role", "security": [{"bearerAuth": []}], "responses": {"200": {}}}}, "/v1/examples/role-based/hr": {"get": {"operationId": "getV1ExamplesRole-basedHr", "tags": ["examples"], "summary": "HR and Manager endpoint", "description": "Example endpoint accessible to HR staff and Managers", "security": [{"bearerAuth": []}], "responses": {"200": {}}}}, "/v1/examples/role-based/finance": {"get": {"operationId": "getV1ExamplesRole-basedFinance", "tags": ["examples"], "summary": "Finance and Manager endpoint", "description": "Example endpoint accessible to Finance staff and Managers", "security": [{"bearerAuth": []}], "responses": {"200": {}}}}, "/v1/examples/role-based/clients": {"get": {"operationId": "getV1ExamplesRole-basedClients", "tags": ["examples"], "summary": "Client-only endpoint", "description": "Example endpoint accessible only to users with the Client role", "security": [{"bearerAuth": []}], "responses": {"200": {}}}}, "/v1/examples/role-based/employees": {"get": {"operationId": "getV1ExamplesRole-basedEmployees", "tags": ["examples"], "summary": "Employee-only endpoint", "description": "Example endpoint accessible to all employee roles (Manager, HR, Finance, Operation)", "security": [{"bearerAuth": []}], "responses": {"200": {}}}}, "/v1/examples/role-based/operation-finance": {"get": {"operationId": "getV1ExamplesRole-basedOperation-finance", "tags": ["examples"], "summary": "Operation and Finance endpoint", "description": "Example endpoint accessible to users with either Operation or Finance roles", "security": [{"bearerAuth": []}], "responses": {"200": {}}}}, "/v1/examples/projects/role-based": {"get": {"parameters": [{"schema": {"type": "string"}, "in": "query", "name": "search", "required": false}, {"schema": {"anyOf": [{"format": "numeric", "default": 0, "type": "string"}, {"type": "number"}]}, "in": "query", "name": "page", "required": false}, {"schema": {"anyOf": [{"format": "numeric", "default": 0, "type": "string"}, {"type": "number"}]}, "in": "query", "name": "pageSize", "required": false}, {"schema": {"type": "string"}, "in": "query", "name": "org_id", "required": false}, {"schema": {"type": "string"}, "in": "query", "name": "employee_id", "required": false}], "operationId": "getV1ExamplesProjectsRole-based", "tags": ["examples"], "summary": "Role-based data filtering", "description": "Example endpoint that returns different data based on the user's role", "security": [{"bearerAuth": []}], "responses": {"200": {}}}}, "/v1/examples/projects/manager": {"get": {"parameters": [{"schema": {"type": "string"}, "in": "query", "name": "search", "required": false}, {"schema": {"anyOf": [{"format": "numeric", "default": 0, "type": "string"}, {"type": "number"}]}, "in": "query", "name": "page", "required": false}, {"schema": {"anyOf": [{"format": "numeric", "default": 0, "type": "string"}, {"type": "number"}]}, "in": "query", "name": "pageSize", "required": false}, {"schema": {"type": "string"}, "in": "query", "name": "org_id", "required": false}, {"schema": {"type": "string"}, "in": "query", "name": "employee_id", "required": false}], "operationId": "getV1ExamplesProjectsManager", "tags": ["examples"], "summary": "All projects (Manager view)", "description": "Managers can see all projects in the system", "security": [{"bearerAuth": []}], "responses": {"200": {}}}}, "/v1/examples/projects/client": {"get": {"parameters": [{"schema": {"type": "string"}, "in": "query", "name": "search", "required": false}, {"schema": {"anyOf": [{"format": "numeric", "default": 0, "type": "string"}, {"type": "number"}]}, "in": "query", "name": "page", "required": false}, {"schema": {"anyOf": [{"format": "numeric", "default": 0, "type": "string"}, {"type": "number"}]}, "in": "query", "name": "pageSize", "required": false}, {"schema": {"type": "string"}, "in": "query", "name": "org_id", "required": false}, {"schema": {"type": "string"}, "in": "query", "name": "employee_id", "required": false}], "operationId": "getV1ExamplesProjectsClient", "tags": ["examples"], "summary": "Organization projects (Client view)", "description": "Clients can only see projects for their organization", "security": [{"bearerAuth": []}], "responses": {"200": {}}}}, "/v1/examples/projects/operation": {"get": {"parameters": [{"schema": {"type": "string"}, "in": "query", "name": "search", "required": false}, {"schema": {"anyOf": [{"format": "numeric", "default": 0, "type": "string"}, {"type": "number"}]}, "in": "query", "name": "page", "required": false}, {"schema": {"anyOf": [{"format": "numeric", "default": 0, "type": "string"}, {"type": "number"}]}, "in": "query", "name": "pageSize", "required": false}, {"schema": {"type": "string"}, "in": "query", "name": "org_id", "required": false}, {"schema": {"type": "string"}, "in": "query", "name": "employee_id", "required": false}], "operationId": "getV1ExamplesProjectsOperation", "tags": ["examples"], "summary": "Operation Projects", "description": "Example endpoint showing operation-specific projects", "security": [{"bearerAuth": []}], "responses": {"200": {}}}}, "/v1/examples/api-responses/not-found": {"get": {"operationId": "getV1ExamplesApi-responsesNot-found", "tags": ["examples"], "summary": "Not Found (404) Response Example", "description": "Example of a Not Found response using the API response middleware", "security": [{"bearerAuth": []}], "responses": {"200": {}}}}, "/v1/examples/api-responses/server-error": {"get": {"operationId": "getV1ExamplesApi-responsesServer-error", "tags": ["examples"], "summary": "Server Error (500) Response Example", "description": "Example of a Server Error response using the API response middleware", "security": [{"bearerAuth": []}], "responses": {"200": {}}}}, "/v1/examples/api-responses/unauthorized": {"get": {"operationId": "getV1ExamplesApi-responsesUnauthorized", "tags": ["examples"], "summary": "Unauthorized (401) Response Example", "description": "Example of an Unauthorized response using the API response middleware", "security": [{"bearerAuth": []}], "responses": {"200": {}}}}, "/v1/examples/api-responses/forbidden": {"get": {"operationId": "getV1ExamplesApi-responsesForbidden", "tags": ["examples"], "summary": "Forbidden (403) Response Example", "description": "Example of a Forbidden response using the API response middleware", "security": [{"bearerAuth": []}], "responses": {"200": {}}}}, "/v1/admin/users/": {"get": {"parameters": [{"description": "Search term to filter users by name, email, or phone", "schema": {"type": "string"}, "in": "query", "name": "search", "required": false}, {"description": "Page number for pagination (starts at 1)", "schema": {"anyOf": [{"format": "numeric", "default": 0, "type": "string"}, {"description": "Page number for pagination (starts at 1)", "type": "number"}]}, "in": "query", "name": "page", "required": false}, {"description": "Number of items per page", "schema": {"anyOf": [{"format": "numeric", "default": 0, "type": "string"}, {"description": "Number of items per page", "type": "number"}]}, "in": "query", "name": "pageSize", "required": false}, {"description": "Filter users by role (<PERSON><PERSON>, Manager, HR, Finance, Operation, Client)", "schema": {"type": "string"}, "in": "query", "name": "role", "required": false}, {"description": "Filter users by active status", "schema": {"type": "boolean"}, "in": "query", "name": "is_active", "required": false}], "operationId": "getV1AdminUsers", "tags": ["admin"], "summary": "Get all users", "description": "Retrieve all user profiles with search, filter, and pagination. Returns a paginated list of user profiles.", "security": [{"bearerAuth": []}], "responses": {"200": {"description": "Successfully retrieved users", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "message": {"type": "string"}, "data": {"type": "object", "properties": {"items": {"type": "array", "items": {"$ref": "#/components/schemas/UserWithProfile"}}, "pagination": {"$ref": "#/components/schemas/PaginationResult"}}}}}, "examples": {"getUsersExample": {"$ref": "#/components/examples/getUsersExample"}}}}}, "403": {"description": "Forbidden - User does not have admin access", "content": {"application/json": {"examples": {"forbiddenErrorExample": {"$ref": "#/components/examples/forbiddenErrorExample"}}}}}}}}, "/v1/admin/users/activate": {"patch": {"parameters": [], "operationId": "patchV1AdminUsersActivate", "tags": ["admin"], "summary": "Activate a user", "description": "Activate a user and create associated entity. NOTE: Requires profile ID (not auth user_id).", "security": [{"bearerAuth": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"id": {"format": "uuid", "description": "Profile ID to activate (the 'id' field from user_profiles table, not the auth user_id)", "type": "string"}, "org_id": {"format": "uuid", "description": "Organization ID to assign client users (required for client role)", "type": "string"}}, "required": ["id"]}}, "multipart/form-data": {"schema": {"type": "object", "properties": {"id": {"format": "uuid", "description": "Profile ID to activate (the 'id' field from user_profiles table, not the auth user_id)", "type": "string"}, "org_id": {"format": "uuid", "description": "Organization ID to assign client users (required for client role)", "type": "string"}}, "required": ["id"]}}, "text/plain": {"schema": {"type": "object", "properties": {"id": {"format": "uuid", "description": "Profile ID to activate (the 'id' field from user_profiles table, not the auth user_id)", "type": "string"}, "org_id": {"format": "uuid", "description": "Organization ID to assign client users (required for client role)", "type": "string"}}, "required": ["id"]}}}}, "responses": {"200": {"description": "Successfully activated user", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/GenericSuccessResponse"}}}}}}}, "/v1/admin/users/{id}": {"delete": {"parameters": [{"description": "Profile ID to delete (the 'id' field from user_profiles table, not the auth user_id)", "schema": {"type": "string", "format": "uuid"}, "in": "path", "name": "id", "required": true}], "operationId": "deleteV1AdminUsersById", "tags": ["admin"], "summary": "Delete a user profile", "description": "Delete a user's profile record only. Auth record must be deleted separately via SQL functions. NOTE: Requires profile ID (not auth user_id).", "security": [{"bearerAuth": []}], "responses": {"200": {"description": "Successfully deleted user profile", "content": {"application/json": {"examples": {"deleteUserExample": {"$ref": "#/components/examples/deleteUserExample"}}}}}}}}, "/v1/employees/": {"get": {"parameters": [{"description": "Search term to filter employees by name, email, or phone", "schema": {"type": "string"}, "in": "query", "name": "search", "required": false}, {"description": "Page number for pagination (starts at 1)", "schema": {"type": "number", "minimum": 1}, "in": "query", "name": "page", "required": false}, {"description": "Number of items per page", "schema": {"type": "number", "minimum": 1, "maximum": 1000}, "in": "query", "name": "pageSize", "required": false}, {"description": "Filter by department/role", "schema": {"anyOf": [{"const": "Admin", "type": "string"}, {"const": "Manager", "type": "string"}, {"const": "HR", "type": "string"}, {"const": "Finance", "type": "string"}, {"const": "Operation", "type": "string"}, {"const": "Client", "type": "string"}]}, "in": "query", "name": "department", "required": false}, {"description": "Filter by employment status", "schema": {"anyOf": [{"const": "Intern", "type": "string"}, {"const": "Fulltime", "type": "string"}, {"const": "Not provided", "type": "string"}]}, "in": "query", "name": "employment_status", "required": false}, {"description": "Filter by presence status", "schema": {"anyOf": [{"const": "present", "type": "string"}, {"const": "absent", "type": "string"}, {"const": "permit", "type": "string"}, {"const": "leave", "type": "string"}]}, "in": "query", "name": "presence_status", "required": false}], "operationId": "getV1Employees", "tags": ["employees"], "summary": "Get all employees", "description": "Retrieve all employee profiles with search, filter, and pagination. Returns a paginated list of employee profiles.", "security": [{"bearerAuth": []}], "responses": {"200": {"description": "Successfully retrieved employees", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "message": {"type": "string"}, "data": {"type": "object", "properties": {"items": {"type": "array", "items": {"$ref": "#/components/schemas/Employee"}}, "pagination": {"$ref": "#/components/schemas/PaginationResult"}}}}}}}}, "403": {"description": "Forbidden - User does not have required role"}}}}, "/v1/employees/{id}": {"get": {"parameters": [{"description": "The ID of the employee to retrieve", "schema": {"type": "string", "format": "uuid"}, "in": "path", "name": "id", "required": true}], "operationId": "getV1EmployeesById", "tags": ["employees"], "summary": "Get employee by ID", "description": "Retrieve detailed information about a specific employee.", "security": [{"bearerAuth": []}], "responses": {"200": {"description": "Successfully retrieved employee", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "message": {"type": "string"}, "data": {"$ref": "#/components/schemas/Employee"}}}}}}, "403": {"description": "Forbidden - User does not have required role"}, "404": {"description": "Employee not found"}}}, "patch": {"parameters": [{"description": "The ID of the employee to update", "schema": {"type": "string", "format": "uuid"}, "in": "path", "name": "id", "required": true}], "operationId": "patchV1EmployeesById", "tags": ["employees"], "summary": "Update employee", "description": "Update information for a specific employee. HR can update any employee, while other users can only update their own profile.", "security": [{"bearerAuth": []}], "responses": {"200": {"description": "Successfully updated employee", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "message": {"type": "string"}, "data": {"$ref": "#/components/schemas/Employee"}}}}}}, "403": {"description": "Forbidden - User does not have required role"}, "404": {"description": "Employee not found"}}, "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"dob": {"format": "date", "description": "Date of birth (YYYY-MM-DD)", "type": "string"}, "employment_status": {"description": "Employment status (intern/fulltime)", "anyOf": [{"const": "Intern", "type": "string"}, {"const": "Fulltime", "type": "string"}, {"const": "Not provided", "type": "string"}]}, "presence_status": {"description": "Current presence status", "anyOf": [{"const": "present", "type": "string"}, {"const": "absent", "type": "string"}, {"const": "permit", "type": "string"}, {"const": "leave", "type": "string"}]}, "start_date": {"format": "date", "description": "Employment start date (YYYY-MM-DD)", "type": "string"}, "department": {"description": "Department/role of the employee", "anyOf": [{"const": "Admin", "type": "string"}, {"const": "Manager", "type": "string"}, {"const": "HR", "type": "string"}, {"const": "Finance", "type": "string"}, {"const": "Operation", "type": "string"}, {"const": "Client", "type": "string"}]}, "address": {"minLength": 1, "maxLength": 255, "description": "Complete address of the employee", "type": "string"}, "bank_account": {"minLength": 1, "maxLength": 50, "description": "Bank account number", "type": "string"}, "bank_name": {"minLength": 1, "maxLength": 50, "description": "Name of the bank", "type": "string"}}}}, "multipart/form-data": {"schema": {"type": "object", "properties": {"dob": {"format": "date", "description": "Date of birth (YYYY-MM-DD)", "type": "string"}, "employment_status": {"description": "Employment status (intern/fulltime)", "anyOf": [{"const": "Intern", "type": "string"}, {"const": "Fulltime", "type": "string"}, {"const": "Not provided", "type": "string"}]}, "presence_status": {"description": "Current presence status", "anyOf": [{"const": "present", "type": "string"}, {"const": "absent", "type": "string"}, {"const": "permit", "type": "string"}, {"const": "leave", "type": "string"}]}, "start_date": {"format": "date", "description": "Employment start date (YYYY-MM-DD)", "type": "string"}, "department": {"description": "Department/role of the employee", "anyOf": [{"const": "Admin", "type": "string"}, {"const": "Manager", "type": "string"}, {"const": "HR", "type": "string"}, {"const": "Finance", "type": "string"}, {"const": "Operation", "type": "string"}, {"const": "Client", "type": "string"}]}, "address": {"minLength": 1, "maxLength": 255, "description": "Complete address of the employee", "type": "string"}, "bank_account": {"minLength": 1, "maxLength": 50, "description": "Bank account number", "type": "string"}, "bank_name": {"minLength": 1, "maxLength": 50, "description": "Name of the bank", "type": "string"}}}}, "text/plain": {"schema": {"type": "object", "properties": {"dob": {"format": "date", "description": "Date of birth (YYYY-MM-DD)", "type": "string"}, "employment_status": {"description": "Employment status (intern/fulltime)", "anyOf": [{"const": "Intern", "type": "string"}, {"const": "Fulltime", "type": "string"}, {"const": "Not provided", "type": "string"}]}, "presence_status": {"description": "Current presence status", "anyOf": [{"const": "present", "type": "string"}, {"const": "absent", "type": "string"}, {"const": "permit", "type": "string"}, {"const": "leave", "type": "string"}]}, "start_date": {"format": "date", "description": "Employment start date (YYYY-MM-DD)", "type": "string"}, "department": {"description": "Department/role of the employee", "anyOf": [{"const": "Admin", "type": "string"}, {"const": "Manager", "type": "string"}, {"const": "HR", "type": "string"}, {"const": "Finance", "type": "string"}, {"const": "Operation", "type": "string"}, {"const": "Client", "type": "string"}]}, "address": {"minLength": 1, "maxLength": 255, "description": "Complete address of the employee", "type": "string"}, "bank_account": {"minLength": 1, "maxLength": 50, "description": "Bank account number", "type": "string"}, "bank_name": {"minLength": 1, "maxLength": 50, "description": "Name of the bank", "type": "string"}}}}}}}}, "/v1/organizations/": {"get": {"parameters": [{"description": "Search term to filter organizations by name, phone, address, or client type", "schema": {"type": "string"}, "in": "query", "name": "search", "required": false}, {"description": "Page number for pagination (starts at 1)", "schema": {"anyOf": [{"format": "numeric", "default": 0, "type": "string"}, {"description": "Page number for pagination (starts at 1)", "type": "number"}]}, "in": "query", "name": "page", "required": false}, {"description": "Number of items per page", "schema": {"anyOf": [{"format": "numeric", "default": 0, "type": "string"}, {"description": "Number of items per page", "type": "number"}]}, "in": "query", "name": "pageSize", "required": false}, {"description": "Filter organizations by client type", "schema": {"type": "string"}, "in": "query", "name": "client_type", "required": false}], "operationId": "getV1Organizations", "tags": ["organizations"], "summary": "Get all organizations", "description": "Retrieve all organizations with search, filter, and pagination", "security": [{"bearerAuth": []}], "responses": {"200": {"description": "Successfully retrieved organizations", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Organization"}, "examples": {"getOrganizationsExample": {"$ref": "#/components/examples/getOrganizationsExample"}}}}}, "403": {"description": "Forbidden - User does not have required role", "content": {"application/json": {"examples": {"notFoundErrorExample": {"$ref": "#/components/examples/notFoundErrorExample"}}}}}}}, "post": {"parameters": [], "operationId": "postV1Organizations", "tags": ["organizations"], "summary": "Create organization", "description": "Create a new organization", "security": [{"bearerAuth": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"name": {"minLength": 2, "maxLength": 100, "description": "Organization name (2-100 characters)", "type": "string"}, "phone": {"pattern": "^[0-9]+$", "minLength": 10, "description": "Phone number (minimum 10 digits, only numbers allowed)", "type": "string"}, "address": {"minLength": 5, "maxLength": 500, "description": "Organization address (5-500 characters)", "type": "string"}, "client_type": {"minLength": 2, "maxLength": 50, "description": "Type of client organization (2-50 characters)", "type": "string"}, "notes": {"maxLength": 1000, "description": "Optional notes about the organization (max 1000 characters)", "type": "string"}}, "required": ["name", "phone", "address", "client_type"]}}, "multipart/form-data": {"schema": {"type": "object", "properties": {"name": {"minLength": 2, "maxLength": 100, "description": "Organization name (2-100 characters)", "type": "string"}, "phone": {"pattern": "^[0-9]+$", "minLength": 10, "description": "Phone number (minimum 10 digits, only numbers allowed)", "type": "string"}, "address": {"minLength": 5, "maxLength": 500, "description": "Organization address (5-500 characters)", "type": "string"}, "client_type": {"minLength": 2, "maxLength": 50, "description": "Type of client organization (2-50 characters)", "type": "string"}, "notes": {"maxLength": 1000, "description": "Optional notes about the organization (max 1000 characters)", "type": "string"}}, "required": ["name", "phone", "address", "client_type"]}}, "text/plain": {"schema": {"type": "object", "properties": {"name": {"minLength": 2, "maxLength": 100, "description": "Organization name (2-100 characters)", "type": "string"}, "phone": {"pattern": "^[0-9]+$", "minLength": 10, "description": "Phone number (minimum 10 digits, only numbers allowed)", "type": "string"}, "address": {"minLength": 5, "maxLength": 500, "description": "Organization address (5-500 characters)", "type": "string"}, "client_type": {"minLength": 2, "maxLength": 50, "description": "Type of client organization (2-50 characters)", "type": "string"}, "notes": {"maxLength": 1000, "description": "Optional notes about the organization (max 1000 characters)", "type": "string"}}, "required": ["name", "phone", "address", "client_type"]}}}}, "responses": {"200": {"description": "Successfully created organization", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Organization"}}}}}}}, "/v1/organizations/{id}": {"get": {"parameters": [{"description": "Organization ID", "schema": {"type": "string", "format": "uuid"}, "in": "path", "name": "id", "required": true}], "operationId": "getV1OrganizationsById", "tags": ["organizations"], "summary": "Get organization by ID", "description": "Retrieve a specific organization by ID", "security": [{"bearerAuth": []}], "responses": {"200": {"description": "Successfully retrieved organization", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Organization"}, "examples": {"getOrganizationExample": {"$ref": "#/components/examples/getOrganizationExample"}}}}}, "404": {"description": "Organization not found", "content": {"application/json": {"examples": {"notFoundErrorExample": {"$ref": "#/components/examples/notFoundErrorExample"}}}}}}}, "put": {"parameters": [{"description": "Organization ID", "schema": {"type": "string", "format": "uuid"}, "in": "path", "name": "id", "required": true}], "operationId": "putV1OrganizationsById", "tags": ["organizations"], "summary": "Update organization", "description": "Update an existing organization", "security": [{"bearerAuth": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"name": {"minLength": 2, "maxLength": 100, "description": "Organization name (2-100 characters)", "type": "string"}, "phone": {"pattern": "^[0-9]+$", "minLength": 10, "description": "Phone number (minimum 10 digits, only numbers allowed)", "type": "string"}, "address": {"minLength": 5, "maxLength": 500, "description": "Organization address (5-500 characters)", "type": "string"}, "client_type": {"minLength": 2, "maxLength": 50, "description": "Type of client organization (2-50 characters)", "type": "string"}, "notes": {"maxLength": 1000, "description": "Optional notes about the organization (max 1000 characters)", "type": "string"}}}}, "multipart/form-data": {"schema": {"type": "object", "properties": {"name": {"minLength": 2, "maxLength": 100, "description": "Organization name (2-100 characters)", "type": "string"}, "phone": {"pattern": "^[0-9]+$", "minLength": 10, "description": "Phone number (minimum 10 digits, only numbers allowed)", "type": "string"}, "address": {"minLength": 5, "maxLength": 500, "description": "Organization address (5-500 characters)", "type": "string"}, "client_type": {"minLength": 2, "maxLength": 50, "description": "Type of client organization (2-50 characters)", "type": "string"}, "notes": {"maxLength": 1000, "description": "Optional notes about the organization (max 1000 characters)", "type": "string"}}}}, "text/plain": {"schema": {"type": "object", "properties": {"name": {"minLength": 2, "maxLength": 100, "description": "Organization name (2-100 characters)", "type": "string"}, "phone": {"pattern": "^[0-9]+$", "minLength": 10, "description": "Phone number (minimum 10 digits, only numbers allowed)", "type": "string"}, "address": {"minLength": 5, "maxLength": 500, "description": "Organization address (5-500 characters)", "type": "string"}, "client_type": {"minLength": 2, "maxLength": 50, "description": "Type of client organization (2-50 characters)", "type": "string"}, "notes": {"maxLength": 1000, "description": "Optional notes about the organization (max 1000 characters)", "type": "string"}}}}}}, "responses": {"200": {"description": "Successfully updated organization", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Organization"}}}}, "404": {"description": "Organization not found", "content": {"application/json": {"examples": {"notFoundErrorExample": {"$ref": "#/components/examples/notFoundErrorExample"}}}}}}}, "delete": {"parameters": [{"description": "Organization ID", "schema": {"type": "string", "format": "uuid"}, "in": "path", "name": "id", "required": true}], "operationId": "deleteV1OrganizationsById", "tags": ["organizations"], "summary": "Delete organization", "description": "Delete an organization (soft delete)", "security": [{"bearerAuth": []}], "responses": {"200": {"description": "Successfully deleted organization", "content": {"application/json": {"examples": {"deleteOrganizationExample": {"$ref": "#/components/examples/deleteOrganizationExample"}}}}}, "404": {"description": "Organization not found", "content": {"application/json": {"examples": {"notFoundErrorExample": {"$ref": "#/components/examples/notFoundErrorExample"}}}}}}}}, "/v1/invoices/": {"get": {"parameters": [{"description": "The search term", "schema": {"type": "string"}, "in": "query", "name": "search", "required": false}, {"description": "The page number for pagination (starts at 1)", "schema": {"type": "number"}, "in": "query", "name": "page", "required": false}, {"description": "Number of items per page (default: 10)", "schema": {"type": "number"}, "in": "query", "name": "pageSize", "required": false}, {"description": "The payment status", "schema": {"anyOf": [{"const": "pending", "type": "string"}, {"const": "partial", "type": "string"}, {"const": "paid", "type": "string"}, {"const": "overdue", "type": "string"}, {"const": "cancelled", "type": "string"}]}, "in": "query", "name": "payment_status", "required": false}], "operationId": "getV1Invoices", "tags": ["invoices"], "summary": "Get all invoices", "description": "Retrieve all invoices with search, filter, and pagination. Returns a paginated list of invoices.", "security": [{"bearerAuth": []}], "responses": {"200": {"description": "Successfully retrieved invoices", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "message": {"type": "string"}, "data": {"type": "object", "properties": {"items": {"type": "array", "items": {"$ref": "#/components/schemas/Invoice"}}, "pagination": {"$ref": "#/components/schemas/PaginationResult"}}}}}, "examples": {"getInvoicesResponseExample": {"$ref": "#/components/examples/getInvoicesResponseExample"}}}}}, "403": {"description": "Forbidden - User does not have required role"}}}, "post": {"parameters": [], "operationId": "postV1Invoices", "tags": ["invoices"], "summary": "Create invoice", "description": "Create a new invoice with items", "security": [{"bearerAuth": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"invoice_type": {"description": "The type of the invoice", "anyOf": [{"const": "external", "type": "string"}, {"const": "internal", "type": "string"}]}, "service_type": {"description": "The service type for the invoice", "anyOf": [{"const": "HCM", "type": "string"}, {"const": "ORDEV", "type": "string"}, {"const": "BE", "type": "string"}, {"const": "IT", "type": "string"}, {"const": "MARKETING", "type": "string"}, {"const": "FINANCE", "type": "string"}, {"const": "SALES", "type": "string"}, {"const": "OTHER", "type": "string"}]}, "recipient_name": {"minLength": 1, "maxLength": 100, "description": "The name of the recipient", "type": "string"}, "project_id": {"minLength": 1, "maxLength": 100, "description": "The id of the project", "type": "string"}, "project_name": {"minLength": 1, "maxLength": 100, "description": "The name of the project", "type": "string"}, "due_date": {"pattern": "^\\d{4}-\\d{2}-\\d{2}$", "description": "The due date of the invoice (YYYY-MM-DD format)", "type": "string"}, "payment_method": {"description": "The method of the payment", "anyOf": [{"const": "bank_transfer", "type": "string"}, {"const": "cash", "type": "string"}, {"const": "credit_card", "type": "string"}, {"const": "cheque", "type": "string"}, {"const": "other", "type": "string"}]}, "payment_status": {"description": "The status of the payment", "anyOf": [{"const": "pending", "type": "string"}, {"const": "partial", "type": "string"}, {"const": "paid", "type": "string"}, {"const": "overdue", "type": "string"}, {"const": "cancelled", "type": "string"}]}, "notes": {"description": "The notes of the invoice", "type": "string"}, "items": {"type": "array", "items": {"type": "object", "properties": {"item_name": {"minLength": 1, "maxLength": 100, "description": "The name of the item", "type": "string"}, "item_amount": {"min": 1, "max": 1000000, "description": "The amount of the item", "type": "number"}, "item_price": {"min": 0, "max": 1000000, "description": "The price of the item", "type": "number"}}, "required": ["item_name", "item_amount", "item_price"]}}}, "required": ["invoice_type", "service_type", "recipient_name", "due_date", "payment_method", "items"]}}, "multipart/form-data": {"schema": {"type": "object", "properties": {"invoice_type": {"description": "The type of the invoice", "anyOf": [{"const": "external", "type": "string"}, {"const": "internal", "type": "string"}]}, "service_type": {"description": "The service type for the invoice", "anyOf": [{"const": "HCM", "type": "string"}, {"const": "ORDEV", "type": "string"}, {"const": "BE", "type": "string"}, {"const": "IT", "type": "string"}, {"const": "MARKETING", "type": "string"}, {"const": "FINANCE", "type": "string"}, {"const": "SALES", "type": "string"}, {"const": "OTHER", "type": "string"}]}, "recipient_name": {"minLength": 1, "maxLength": 100, "description": "The name of the recipient", "type": "string"}, "project_id": {"minLength": 1, "maxLength": 100, "description": "The id of the project", "type": "string"}, "project_name": {"minLength": 1, "maxLength": 100, "description": "The name of the project", "type": "string"}, "due_date": {"pattern": "^\\d{4}-\\d{2}-\\d{2}$", "description": "The due date of the invoice (YYYY-MM-DD format)", "type": "string"}, "payment_method": {"description": "The method of the payment", "anyOf": [{"const": "bank_transfer", "type": "string"}, {"const": "cash", "type": "string"}, {"const": "credit_card", "type": "string"}, {"const": "cheque", "type": "string"}, {"const": "other", "type": "string"}]}, "payment_status": {"description": "The status of the payment", "anyOf": [{"const": "pending", "type": "string"}, {"const": "partial", "type": "string"}, {"const": "paid", "type": "string"}, {"const": "overdue", "type": "string"}, {"const": "cancelled", "type": "string"}]}, "notes": {"description": "The notes of the invoice", "type": "string"}, "items": {"type": "array", "items": {"type": "object", "properties": {"item_name": {"minLength": 1, "maxLength": 100, "description": "The name of the item", "type": "string"}, "item_amount": {"min": 1, "max": 1000000, "description": "The amount of the item", "type": "number"}, "item_price": {"min": 0, "max": 1000000, "description": "The price of the item", "type": "number"}}, "required": ["item_name", "item_amount", "item_price"]}}}, "required": ["invoice_type", "service_type", "recipient_name", "due_date", "payment_method", "items"]}}, "text/plain": {"schema": {"type": "object", "properties": {"invoice_type": {"description": "The type of the invoice", "anyOf": [{"const": "external", "type": "string"}, {"const": "internal", "type": "string"}]}, "service_type": {"description": "The service type for the invoice", "anyOf": [{"const": "HCM", "type": "string"}, {"const": "ORDEV", "type": "string"}, {"const": "BE", "type": "string"}, {"const": "IT", "type": "string"}, {"const": "MARKETING", "type": "string"}, {"const": "FINANCE", "type": "string"}, {"const": "SALES", "type": "string"}, {"const": "OTHER", "type": "string"}]}, "recipient_name": {"minLength": 1, "maxLength": 100, "description": "The name of the recipient", "type": "string"}, "project_id": {"minLength": 1, "maxLength": 100, "description": "The id of the project", "type": "string"}, "project_name": {"minLength": 1, "maxLength": 100, "description": "The name of the project", "type": "string"}, "due_date": {"pattern": "^\\d{4}-\\d{2}-\\d{2}$", "description": "The due date of the invoice (YYYY-MM-DD format)", "type": "string"}, "payment_method": {"description": "The method of the payment", "anyOf": [{"const": "bank_transfer", "type": "string"}, {"const": "cash", "type": "string"}, {"const": "credit_card", "type": "string"}, {"const": "cheque", "type": "string"}, {"const": "other", "type": "string"}]}, "payment_status": {"description": "The status of the payment", "anyOf": [{"const": "pending", "type": "string"}, {"const": "partial", "type": "string"}, {"const": "paid", "type": "string"}, {"const": "overdue", "type": "string"}, {"const": "cancelled", "type": "string"}]}, "notes": {"description": "The notes of the invoice", "type": "string"}, "items": {"type": "array", "items": {"type": "object", "properties": {"item_name": {"minLength": 1, "maxLength": 100, "description": "The name of the item", "type": "string"}, "item_amount": {"min": 1, "max": 1000000, "description": "The amount of the item", "type": "number"}, "item_price": {"min": 0, "max": 1000000, "description": "The price of the item", "type": "number"}}, "required": ["item_name", "item_amount", "item_price"]}}}, "required": ["invoice_type", "service_type", "recipient_name", "due_date", "payment_method", "items"]}}}}, "responses": {"200": {"description": "Successfully created invoice", "content": {"application/json": {"examples": {"createInvoiceResponseExample": {"$ref": "#/components/examples/createInvoiceResponseExample"}}}}}}}}, "/v1/invoices/{id}": {"get": {"parameters": [{"description": "The id of the invoice", "schema": {"type": "string", "format": "uuid"}, "in": "path", "name": "id", "required": true}], "operationId": "getV1InvoicesById", "tags": ["invoices"], "summary": "Get invoice by ID", "description": "Retrieve a specific invoice by ID with its items", "security": [{"bearerAuth": []}], "responses": {"200": {"description": "Successfully retrieved invoice", "content": {"application/json": {"examples": {"getInvoiceResponseExample": {"$ref": "#/components/examples/getInvoiceResponseExample"}}}}}, "404": {"description": "Invoice not found"}}}, "delete": {"parameters": [{"description": "The ID of the invoice to delete", "schema": {"type": "string", "format": "uuid"}, "in": "path", "name": "id", "required": true}], "operationId": "deleteV1InvoicesById", "tags": ["invoices"], "summary": "Delete invoice", "description": "Delete an invoice and its items", "security": [{"bearerAuth": []}], "responses": {"200": {"description": "Successfully deleted invoice", "content": {"application/json": {"examples": {"deleteInvoiceResponseExample": {"$ref": "#/components/examples/deleteInvoiceResponseExample"}}}}}, "404": {"description": "Invoice not found"}}}, "put": {"parameters": [{"description": "The ID of the invoice to update", "schema": {"type": "string", "format": "uuid"}, "in": "path", "name": "id", "required": true}], "operationId": "putV1InvoicesById", "tags": ["invoices"], "summary": "Update invoice", "description": "Update an existing invoice and its items", "security": [{"bearerAuth": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"invoice_type": {"description": "The type of the invoice", "anyOf": [{"const": "external", "type": "string"}, {"const": "internal", "type": "string"}]}, "service_type": {"description": "The service type for the invoice", "anyOf": [{"const": "HCM", "type": "string"}, {"const": "ORDEV", "type": "string"}, {"const": "BE", "type": "string"}, {"const": "IT", "type": "string"}, {"const": "MARKETING", "type": "string"}, {"const": "FINANCE", "type": "string"}, {"const": "SALES", "type": "string"}, {"const": "OTHER", "type": "string"}]}, "recipient_name": {"minLength": 1, "maxLength": 100, "description": "The name of the recipient", "type": "string"}, "project_id": {"minLength": 1, "maxLength": 100, "description": "The id of the project", "type": "string"}, "project_name": {"minLength": 1, "maxLength": 100, "description": "The name of the project", "type": "string"}, "due_date": {"pattern": "^\\d{4}-\\d{2}-\\d{2}$", "description": "The due date of the invoice (YYYY-MM-DD format)", "type": "string"}, "payment_method": {"description": "The method of the payment", "anyOf": [{"const": "bank_transfer", "type": "string"}, {"const": "cash", "type": "string"}, {"const": "credit_card", "type": "string"}, {"const": "cheque", "type": "string"}, {"const": "other", "type": "string"}]}, "payment_status": {"description": "The status of the payment", "anyOf": [{"const": "pending", "type": "string"}, {"const": "partial", "type": "string"}, {"const": "paid", "type": "string"}, {"const": "overdue", "type": "string"}, {"const": "cancelled", "type": "string"}]}, "notes": {"description": "The notes of the invoice", "type": "string"}, "items": {"type": "array", "items": {"type": "object", "properties": {"id": {"format": "uuid", "description": "The ID of the existing item (if updating)", "type": "string"}, "item_name": {"minLength": 1, "maxLength": 100, "description": "The name of the item", "type": "string"}, "item_amount": {"min": 1, "max": 1000000, "description": "The amount of the item", "type": "number"}, "item_price": {"min": 0, "max": 1000000, "description": "The price of the item", "type": "number"}}}}}}}, "multipart/form-data": {"schema": {"type": "object", "properties": {"invoice_type": {"description": "The type of the invoice", "anyOf": [{"const": "external", "type": "string"}, {"const": "internal", "type": "string"}]}, "service_type": {"description": "The service type for the invoice", "anyOf": [{"const": "HCM", "type": "string"}, {"const": "ORDEV", "type": "string"}, {"const": "BE", "type": "string"}, {"const": "IT", "type": "string"}, {"const": "MARKETING", "type": "string"}, {"const": "FINANCE", "type": "string"}, {"const": "SALES", "type": "string"}, {"const": "OTHER", "type": "string"}]}, "recipient_name": {"minLength": 1, "maxLength": 100, "description": "The name of the recipient", "type": "string"}, "project_id": {"minLength": 1, "maxLength": 100, "description": "The id of the project", "type": "string"}, "project_name": {"minLength": 1, "maxLength": 100, "description": "The name of the project", "type": "string"}, "due_date": {"pattern": "^\\d{4}-\\d{2}-\\d{2}$", "description": "The due date of the invoice (YYYY-MM-DD format)", "type": "string"}, "payment_method": {"description": "The method of the payment", "anyOf": [{"const": "bank_transfer", "type": "string"}, {"const": "cash", "type": "string"}, {"const": "credit_card", "type": "string"}, {"const": "cheque", "type": "string"}, {"const": "other", "type": "string"}]}, "payment_status": {"description": "The status of the payment", "anyOf": [{"const": "pending", "type": "string"}, {"const": "partial", "type": "string"}, {"const": "paid", "type": "string"}, {"const": "overdue", "type": "string"}, {"const": "cancelled", "type": "string"}]}, "notes": {"description": "The notes of the invoice", "type": "string"}, "items": {"type": "array", "items": {"type": "object", "properties": {"id": {"format": "uuid", "description": "The ID of the existing item (if updating)", "type": "string"}, "item_name": {"minLength": 1, "maxLength": 100, "description": "The name of the item", "type": "string"}, "item_amount": {"min": 1, "max": 1000000, "description": "The amount of the item", "type": "number"}, "item_price": {"min": 0, "max": 1000000, "description": "The price of the item", "type": "number"}}}}}}}, "text/plain": {"schema": {"type": "object", "properties": {"invoice_type": {"description": "The type of the invoice", "anyOf": [{"const": "external", "type": "string"}, {"const": "internal", "type": "string"}]}, "service_type": {"description": "The service type for the invoice", "anyOf": [{"const": "HCM", "type": "string"}, {"const": "ORDEV", "type": "string"}, {"const": "BE", "type": "string"}, {"const": "IT", "type": "string"}, {"const": "MARKETING", "type": "string"}, {"const": "FINANCE", "type": "string"}, {"const": "SALES", "type": "string"}, {"const": "OTHER", "type": "string"}]}, "recipient_name": {"minLength": 1, "maxLength": 100, "description": "The name of the recipient", "type": "string"}, "project_id": {"minLength": 1, "maxLength": 100, "description": "The id of the project", "type": "string"}, "project_name": {"minLength": 1, "maxLength": 100, "description": "The name of the project", "type": "string"}, "due_date": {"pattern": "^\\d{4}-\\d{2}-\\d{2}$", "description": "The due date of the invoice (YYYY-MM-DD format)", "type": "string"}, "payment_method": {"description": "The method of the payment", "anyOf": [{"const": "bank_transfer", "type": "string"}, {"const": "cash", "type": "string"}, {"const": "credit_card", "type": "string"}, {"const": "cheque", "type": "string"}, {"const": "other", "type": "string"}]}, "payment_status": {"description": "The status of the payment", "anyOf": [{"const": "pending", "type": "string"}, {"const": "partial", "type": "string"}, {"const": "paid", "type": "string"}, {"const": "overdue", "type": "string"}, {"const": "cancelled", "type": "string"}]}, "notes": {"description": "The notes of the invoice", "type": "string"}, "items": {"type": "array", "items": {"type": "object", "properties": {"id": {"format": "uuid", "description": "The ID of the existing item (if updating)", "type": "string"}, "item_name": {"minLength": 1, "maxLength": 100, "description": "The name of the item", "type": "string"}, "item_amount": {"min": 1, "max": 1000000, "description": "The amount of the item", "type": "number"}, "item_price": {"min": 0, "max": 1000000, "description": "The price of the item", "type": "number"}}}}}}}}}, "responses": {"200": {"description": "Successfully updated invoice", "content": {"application/json": {"examples": {"updateInvoiceResponseExample": {"$ref": "#/components/examples/updateInvoiceResponseExample"}}}}}, "404": {"description": "Invoice not found"}}}}, "/v1/invoices/{id}/history": {"get": {"parameters": [{"description": "The id of the invoice to get history for", "schema": {"type": "string", "format": "uuid"}, "in": "path", "name": "id", "required": true}, {"description": "The page number for pagination (starts at 1)", "schema": {"type": "number"}, "in": "query", "name": "page", "required": false}, {"description": "Number of items per page (default: 10)", "schema": {"type": "number"}, "in": "query", "name": "pageSize", "required": false}], "operationId": "getV1InvoicesByIdHistory", "tags": ["invoices"], "summary": "Get invoice update history", "description": "Retrieve the update history for a specific invoice", "security": [{"bearerAuth": []}], "responses": {"200": {"description": "Successfully retrieved invoice history", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "message": {"type": "string"}, "data": {"type": "object", "properties": {"items": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "string"}, "invoice_id": {"type": "string"}, "change_description": {"type": "string"}, "parsed_changes": {"type": "array", "items": {"type": "object", "properties": {"field": {"type": "string"}, "from_value": {"type": "string"}, "to_value": {"type": "string"}}}}, "created_at": {"type": "string"}, "created_by": {"type": "string"}}}}, "pagination": {"$ref": "#/components/schemas/PaginationResult"}}}}}}}}, "404": {"description": "Invoice not found"}}}}, "/v1/invoices/{id}/payment-proofs": {"post": {"parameters": [{"schema": {"type": "string"}, "in": "path", "name": "id", "required": true}], "operationId": "postV1InvoicesByIdPayment-proofs", "tags": ["invoices"], "summary": "Upload a payment proof for an invoice", "description": "Upload a payment proof document for an invoice", "security": [{"bearerAuth": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"file": {"default": "File", "type": "string", "format": "binary"}, "notes": {"type": "string"}}, "required": ["file"]}}, "multipart/form-data": {"schema": {"type": "object", "properties": {"file": {"default": "File", "type": "string", "format": "binary"}, "notes": {"type": "string"}}, "required": ["file"]}}, "text/plain": {"schema": {"type": "object", "properties": {"file": {"default": "File", "type": "string", "format": "binary"}, "notes": {"type": "string"}}, "required": ["file"]}}}}, "responses": {"200": {}}}, "get": {"operationId": "getV1InvoicesByIdPayment-proofs", "tags": ["invoices"], "summary": "Get all payment proofs for an invoice", "description": "Get all payment proof documents for an invoice", "security": [{"bearerAuth": []}], "parameters": [{"schema": {"type": "string"}, "in": "path", "name": "id", "required": true}], "responses": {"200": {}}}}, "/v1/invoices/{id}/payment-proofs/{proofId}": {"delete": {"operationId": "deleteV1InvoicesByIdPayment-proofsByProofId", "tags": ["invoices"], "summary": "Delete a payment proof", "description": "Delete a payment proof document", "security": [{"bearerAuth": []}], "parameters": [{"schema": {"type": "string"}, "in": "path", "name": "id", "required": true}, {"schema": {"type": "string"}, "in": "path", "name": "proofId", "required": true}], "responses": {"200": {}}}}, "/v1/salaries/": {"get": {"parameters": [{"description": "Filter by employee ID", "schema": {"type": "string", "format": "uuid"}, "in": "query", "name": "employeeId", "required": false}, {"description": "Filter by period (YYYY-MM)", "schema": {"type": "string", "pattern": "^\\d{4}-\\d{2}$"}, "in": "query", "name": "period", "required": false}, {"description": "Filter by payment status", "schema": {"anyOf": [{"const": "unpaid", "type": "string"}, {"const": "paid", "type": "string"}]}, "in": "query", "name": "paymentStatus", "required": false}, {"description": "Search term for searching across different fields", "schema": {"type": "string"}, "in": "query", "name": "search", "required": false}, {"description": "The page number", "schema": {"type": "number", "minimum": 1}, "in": "query", "name": "page", "required": false}, {"description": "The page size", "schema": {"type": "number", "minimum": 1, "maximum": 100}, "in": "query", "name": "pageSize", "required": false}], "operationId": "getV1Salaries", "tags": ["salaries"], "summary": "Get all salary records", "description": "Retrieve all salary records with optional filters. Automatically generates salaries for the current month if needed.", "security": [{"bearerAuth": []}], "responses": {"200": {"description": "Successfully retrieved salary records", "content": {"application/json": {"example": {"success": true, "message": "Salary records retrieved successfully", "data": {"items": [{"id": "123e4567-e89b-12d3-a456-************", "employee_id": "8a172b90-b3d5-4f32-b971-3603fb62130a", "base_salary": 9000000, "bonus": 1800000, "pay_reduction": 0, "total_salary": 10800000, "payment_status": "unpaid", "period": "2025-03"}], "pagination": {"total": 5, "page": 1, "pageSize": 10}}}}}}}}}, "/v1/salaries/employee/{employeeId}": {"get": {"parameters": [{"description": "The ID of the employee", "schema": {"type": "string", "format": "uuid"}, "in": "path", "name": "employeeId", "required": true}], "operationId": "getV1SalariesEmployeeByEmployeeId", "tags": ["salaries"], "summary": "Get salaries by employee ID", "description": "Retrieve all salary records for a specific employee", "security": [{"bearerAuth": []}], "responses": {"200": {}}}}, "/v1/salaries/{id}": {"get": {"parameters": [{"description": "The ID of the salary record", "schema": {"type": "string", "format": "uuid"}, "in": "path", "name": "id", "required": true}], "operationId": "getV1SalariesById", "tags": ["salaries"], "summary": "Get salary by ID", "description": "Retrieve a specific salary record by its ID", "security": [{"bearerAuth": []}], "responses": {"200": {"description": "Successfully retrieved salary record", "content": {"application/json": {"example": {"success": true, "message": "Salary record retrieved successfully", "data": {"id": "123e4567-e89b-12d3-a456-************", "employee_id": "8a172b90-b3d5-4f32-b971-3603fb62130a", "base_salary": 9000000, "bonus": 1800000, "pay_reduction": 0, "total_salary": 10800000, "payment_status": "unpaid", "period": "2025-03"}}}}}}}, "patch": {"parameters": [{"description": "The ID of the salary record", "schema": {"type": "string", "format": "uuid"}, "in": "path", "name": "id", "required": true}], "operationId": "patchV1SalariesById", "tags": ["salaries"], "summary": "Update salary", "description": "Update a salary record with role-based permissions. HR can update base_salary for unpaid salaries. Finance can update base_salary and payment_status. Managers can do both.", "security": [{"bearerAuth": []}], "responses": {"200": {"description": "Successfully updated salary record", "content": {"application/json": {"example": {"success": true, "message": "Salary record updated successfully", "data": {"id": "123e4567-e89b-12d3-a456-************", "employee_id": "8a172b90-b3d5-4f32-b971-3603fb62130a", "base_salary": 9500000, "total_bonus": 2000000, "total_deduction": 100000, "total_allowance": 300000, "total_salary": 11700000, "payment_status": "paid", "period": "2025-03"}}}}}, "403": {"description": "Forbidden - User doesn't have permission for the requested update"}, "404": {"description": "Salary record not found"}}, "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"base_salary": {"minimum": 0, "maximum": **********, "description": "The base salary amount", "type": "number"}, "payment_status": {"description": "The status of the salary payment", "anyOf": [{"const": "unpaid", "type": "string"}, {"const": "paid", "type": "string"}]}}}}, "multipart/form-data": {"schema": {"type": "object", "properties": {"base_salary": {"minimum": 0, "maximum": **********, "description": "The base salary amount", "type": "number"}, "payment_status": {"description": "The status of the salary payment", "anyOf": [{"const": "unpaid", "type": "string"}, {"const": "paid", "type": "string"}]}}}}, "text/plain": {"schema": {"type": "object", "properties": {"base_salary": {"minimum": 0, "maximum": **********, "description": "The base salary amount", "type": "number"}, "payment_status": {"description": "The status of the salary payment", "anyOf": [{"const": "unpaid", "type": "string"}, {"const": "paid", "type": "string"}]}}}}}}}}, "/v1/salaries/{id}/history": {"get": {"parameters": [{"description": "The ID of the salary record", "schema": {"type": "string", "format": "uuid"}, "in": "path", "name": "id", "required": true}], "operationId": "getV1SalariesByIdHistory", "tags": ["salaries"], "summary": "Get salary update history", "description": "Retrieve the update history for a specific salary", "security": [{"bearerAuth": []}], "responses": {"200": {"description": "Successfully retrieved salary history", "content": {"application/json": {"example": {"success": true, "message": "Salary history retrieved successfully", "data": {"items": [{"id": "123e4567-e89b-12d3-a456-************", "salary_id": "8a172b90-b3d5-4f32-b971-3603fb62130a", "change_description": "[{\"field\":\"base_salary\",\"from_value\":9000000,\"to_value\":9500000}]", "parsed_changes": [{"field": "base_salary", "from_value": 9000000, "to_value": 9500000}], "created_at": "2023-04-01T12:00:00Z", "created_by": "user-123"}]}}}}}, "404": {"description": "Salary not found"}}}}, "/v1/attendances/": {"post": {"parameters": [], "operationId": "postV1Attendances", "tags": ["attendances"], "summary": "Create or update attendance with clock-in/out", "description": "Creates a new attendance record with the current date and clock-in time, or updates an existing record with clock-out time. Tasks can be included in both scenarios.", "security": [{"bearerAuth": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"status": {"description": "Presence status (present, absent, permit, leave)", "anyOf": [{"const": "present", "type": "string"}, {"const": "absent", "type": "string"}, {"const": "permit", "type": "string"}, {"const": "leave", "type": "string"}]}, "notes": {"maxLength": 1000, "description": "Optional notes about attendance (max 1000 characters)", "type": "string"}, "tasks": {"description": "Array of tasks to be created with attendance", "type": "array", "items": {"type": "object", "properties": {"description": {"minLength": 3, "maxLength": 255, "description": "Task description (min 3 characters, max 255 characters)", "type": "string"}, "due_date": {"pattern": "^\\d{4}-\\d{2}-\\d{2}$", "description": "Due date in format YYYY-MM-DD", "type": "string"}, "completion_status": {"description": "Task completion status (not_completed, on_progress, completed)", "default": "not_completed", "anyOf": [{"const": "not_completed", "type": "string"}, {"const": "on_progress", "type": "string"}, {"const": "completed", "type": "string"}]}}, "required": ["description", "due_date"]}}}, "required": ["status"]}}, "multipart/form-data": {"schema": {"type": "object", "properties": {"status": {"description": "Presence status (present, absent, permit, leave)", "anyOf": [{"const": "present", "type": "string"}, {"const": "absent", "type": "string"}, {"const": "permit", "type": "string"}, {"const": "leave", "type": "string"}]}, "notes": {"maxLength": 1000, "description": "Optional notes about attendance (max 1000 characters)", "type": "string"}, "tasks": {"description": "Array of tasks to be created with attendance", "type": "array", "items": {"type": "object", "properties": {"description": {"minLength": 3, "maxLength": 255, "description": "Task description (min 3 characters, max 255 characters)", "type": "string"}, "due_date": {"pattern": "^\\d{4}-\\d{2}-\\d{2}$", "description": "Due date in format YYYY-MM-DD", "type": "string"}, "completion_status": {"description": "Task completion status (not_completed, on_progress, completed)", "default": "not_completed", "anyOf": [{"const": "not_completed", "type": "string"}, {"const": "on_progress", "type": "string"}, {"const": "completed", "type": "string"}]}}, "required": ["description", "due_date"]}}}, "required": ["status"]}}, "text/plain": {"schema": {"type": "object", "properties": {"status": {"description": "Presence status (present, absent, permit, leave)", "anyOf": [{"const": "present", "type": "string"}, {"const": "absent", "type": "string"}, {"const": "permit", "type": "string"}, {"const": "leave", "type": "string"}]}, "notes": {"maxLength": 1000, "description": "Optional notes about attendance (max 1000 characters)", "type": "string"}, "tasks": {"description": "Array of tasks to be created with attendance", "type": "array", "items": {"type": "object", "properties": {"description": {"minLength": 3, "maxLength": 255, "description": "Task description (min 3 characters, max 255 characters)", "type": "string"}, "due_date": {"pattern": "^\\d{4}-\\d{2}-\\d{2}$", "description": "Due date in format YYYY-MM-DD", "type": "string"}, "completion_status": {"description": "Task completion status (not_completed, on_progress, completed)", "default": "not_completed", "anyOf": [{"const": "not_completed", "type": "string"}, {"const": "on_progress", "type": "string"}, {"const": "completed", "type": "string"}]}}, "required": ["description", "due_date"]}}}, "required": ["status"]}}}}, "responses": {"200": {"description": "Successfully created or updated attendance", "content": {"application/json": {"examples": {"clockInResponseExample": {"$ref": "#/components/examples/clockInResponseExample"}, "clockOutResponseExample": {"$ref": "#/components/examples/clockOutResponseExample"}}}}}, "400": {"description": "Bad request - Invalid input data", "content": {"application/json": {"examples": {"invalidPresenceStatusExample": {"$ref": "#/components/examples/invalidPresenceStatusExample"}, "missingPresenceStatusExample": {"$ref": "#/components/examples/missingPresenceStatusExample"}, "missingEmployeeIdExample": {"$ref": "#/components/examples/missingEmployeeIdExample"}}}}}, "401": {"description": "Unauthorized - Authentication required", "content": {"application/json": {"example": {"$ref": "#/components/examples/unauthorizedExample"}}}}, "403": {"description": "Forbidden - Insufficient permissions", "content": {"application/json": {"example": {"$ref": "#/components/examples/forbiddenExample"}}}}, "500": {"description": "Server error", "content": {"application/json": {"example": {"$ref": "#/components/examples/serverErrorExample"}}}}}}, "get": {"parameters": [{"name": "fromDate", "in": "query", "description": "Filter for records from this date (YYYY-MM-DD format)", "required": false, "schema": {"type": "string", "pattern": "^\\d{4}-\\d{2}-\\d{2}$", "example": "2023-01-01"}}, {"name": "toDate", "in": "query", "description": "Filter for records until this date (YYYY-MM-DD format)", "required": false, "schema": {"type": "string", "pattern": "^\\d{4}-\\d{2}-\\d{2}$", "example": "2023-12-31"}}, {"name": "page", "in": "query", "description": "Page number for pagination (default: 1)", "required": false, "schema": {"type": "integer", "minimum": 1, "default": 1, "example": 1}}, {"name": "pageSize", "in": "query", "description": "Number of records per page (default: 10, max: 100)", "required": false, "schema": {"type": "integer", "minimum": 1, "maximum": 100, "default": 10, "example": 10}}], "operationId": "getV1Attendances", "tags": ["attendances"], "summary": "Get all attendances", "description": "Retrieve a list of all attendances, filtered by date range and paginated.", "security": [{"bearerAuth": []}], "responses": {"200": {"description": "Successfully fetched attendances", "content": {"application/json": {"examples": {"attendanceListExample": {"$ref": "#/components/examples/attendanceListExample"}, "emptyAttendanceListExample": {"$ref": "#/components/examples/emptyAttendanceListExample"}}}}}, "400": {"description": "Bad request - Invalid query parameters", "content": {"application/json": {"examples": {"invalidDateFormatExample": {"$ref": "#/components/examples/invalidDateFormatExample"}, "invalidDateRangeExample": {"$ref": "#/components/examples/invalidDateRangeExample"}}}}}, "401": {"description": "Unauthorized - Authentication required", "content": {"application/json": {"example": {"$ref": "#/components/examples/unauthorizedExample"}}}}, "403": {"description": "Forbidden - Insufficient permissions", "content": {"application/json": {"example": {"$ref": "#/components/examples/forbiddenExample"}}}}}}}, "/v1/attendances/today": {"get": {"operationId": "getV1AttendancesToday", "tags": ["attendances"], "summary": "Get today's attendance for the current user", "description": "Retrieve the attendance record for the current user for today's date (based on Jakarta time).", "security": [{"bearerAuth": []}], "responses": {"200": {"description": "Successfully fetched today's attendance", "content": {"application/json": {"examples": {"existingAttendanceExample": {"$ref": "#/components/examples/existingAttendanceExample"}}}}}, "400": {"description": "Bad request - Missing employee ID", "content": {"application/json": {"example": {"$ref": "#/components/examples/missingEmployeeIdExample"}}}}, "401": {"description": "Unauthorized - Authentication required", "content": {"application/json": {"example": {"$ref": "#/components/examples/unauthorizedExample"}}}}, "403": {"description": "Forbidden - Insufficient permissions", "content": {"application/json": {"example": {"$ref": "#/components/examples/forbiddenExample"}}}}, "404": {"description": "No attendance record found for today", "content": {"application/json": {"example": {"$ref": "#/components/examples/notFoundExample"}}}}}}}, "/v1/attendances/employee": {"post": {"parameters": [], "operationId": "postV1AttendancesEmployee", "tags": ["attendances"], "summary": "Get attendance records for a specific employee", "description": "Retrieves attendance records for a specific employee with optional date filtering and pagination. Admin/HR/Manager only.", "security": [{"bearerAuth": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"error": "fromDate must be before or equal to toDate", "type": "object", "properties": {"employee_id": {"description": "Employee ID", "type": "string"}, "fromDate": {"pattern": "^\\d{4}-\\d{2}-\\d{2}$", "description": "Start date in YYYY-MM-DD format", "type": "string"}, "toDate": {"pattern": "^\\d{4}-\\d{2}-\\d{2}$", "description": "End date in YYYY-MM-DD format", "type": "string"}, "status": {"description": "Filter by attendance status (present, absent, permit, leave)", "anyOf": [{"const": "present", "type": "string"}, {"const": "absent", "type": "string"}, {"const": "permit", "type": "string"}, {"const": "leave", "type": "string"}]}, "search": {"description": "Search term to find in attendance notes", "type": "string"}, "page": {"minimum": 1, "default": 1, "description": "Page number for pagination", "type": "number"}, "pageSize": {"minimum": 1, "maximum": 100, "default": 10, "description": "Number of records per page", "type": "number"}}, "required": ["employee_id"]}}, "multipart/form-data": {"schema": {"error": "fromDate must be before or equal to toDate", "type": "object", "properties": {"employee_id": {"description": "Employee ID", "type": "string"}, "fromDate": {"pattern": "^\\d{4}-\\d{2}-\\d{2}$", "description": "Start date in YYYY-MM-DD format", "type": "string"}, "toDate": {"pattern": "^\\d{4}-\\d{2}-\\d{2}$", "description": "End date in YYYY-MM-DD format", "type": "string"}, "status": {"description": "Filter by attendance status (present, absent, permit, leave)", "anyOf": [{"const": "present", "type": "string"}, {"const": "absent", "type": "string"}, {"const": "permit", "type": "string"}, {"const": "leave", "type": "string"}]}, "search": {"description": "Search term to find in attendance notes", "type": "string"}, "page": {"minimum": 1, "default": 1, "description": "Page number for pagination", "type": "number"}, "pageSize": {"minimum": 1, "maximum": 100, "default": 10, "description": "Number of records per page", "type": "number"}}, "required": ["employee_id"]}}, "text/plain": {"schema": {"error": "fromDate must be before or equal to toDate", "type": "object", "properties": {"employee_id": {"description": "Employee ID", "type": "string"}, "fromDate": {"pattern": "^\\d{4}-\\d{2}-\\d{2}$", "description": "Start date in YYYY-MM-DD format", "type": "string"}, "toDate": {"pattern": "^\\d{4}-\\d{2}-\\d{2}$", "description": "End date in YYYY-MM-DD format", "type": "string"}, "status": {"description": "Filter by attendance status (present, absent, permit, leave)", "anyOf": [{"const": "present", "type": "string"}, {"const": "absent", "type": "string"}, {"const": "permit", "type": "string"}, {"const": "leave", "type": "string"}]}, "search": {"description": "Search term to find in attendance notes", "type": "string"}, "page": {"minimum": 1, "default": 1, "description": "Page number for pagination", "type": "number"}, "pageSize": {"minimum": 1, "maximum": 100, "default": 10, "description": "Number of records per page", "type": "number"}}, "required": ["employee_id"]}}}}, "responses": {"200": {"description": "Employee attendance records retrieved successfully", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/EmployeeAttendanceResponse"}}}}, "400": {"description": "Bad request - missing or invalid data", "content": {"application/json": {"examples": {"invalidEmployeeIdExample": {"$ref": "#/components/examples/invalidEmployeeIdExample"}, "invalidDateRangeExample": {"$ref": "#/components/examples/invalidDateRangeExample"}}}}}, "401": {"description": "Unauthorized - invalid or missing token", "content": {"application/json": {"example": {"$ref": "#/components/examples/unauthorizedExample"}}}}, "403": {"description": "Forbidden - insufficient permissions", "content": {"application/json": {"example": {"$ref": "#/components/examples/forbiddenExample"}}}}, "500": {"description": "Internal server error", "content": {"application/json": {"example": {"$ref": "#/components/examples/serverErrorExample"}}}}}}}, "/v1/attendances/me": {"get": {"operationId": "getV1AttendancesMe", "tags": ["attendances"], "summary": "Get current user's attendance records", "description": "Retrieves attendance records for the current user with optional date filtering and pagination", "security": [{"bearerAuth": []}], "parameters": [{"name": "fromDate", "in": "query", "description": "Filter for records from this date (YYYY-MM-DD format)", "required": false, "schema": {"type": "string", "pattern": "^\\d{4}-\\d{2}-\\d{2}$", "example": "2023-01-01"}}, {"name": "toDate", "in": "query", "description": "Filter for records until this date (YYYY-MM-DD format)", "required": false, "schema": {"type": "string", "pattern": "^\\d{4}-\\d{2}-\\d{2}$", "example": "2023-12-31"}}, {"name": "page", "in": "query", "description": "Page number for pagination (default: 1)", "required": false, "schema": {"type": "integer", "minimum": 1, "default": 1, "example": 1}}, {"name": "pageSize", "in": "query", "description": "Number of records per page (default: 10, max: 100)", "required": false, "schema": {"type": "integer", "minimum": 1, "maximum": 100, "default": 10, "example": 10}}], "responses": {"200": {"description": "Personal attendance records retrieved successfully", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/PersonalAttendanceResponse"}}}}, "400": {"description": "Bad request - missing or invalid data", "content": {"application/json": {"examples": {"invalidDateFormatExample": {"$ref": "#/components/examples/invalidDateFormatExample"}, "invalidDateRangeExample": {"$ref": "#/components/examples/invalidDateRangeExample"}}}}}, "401": {"description": "Unauthorized - invalid or missing token", "content": {"application/json": {"example": {"$ref": "#/components/examples/unauthorizedExample"}}}}, "500": {"description": "Internal server error", "content": {"application/json": {"example": {"$ref": "#/components/examples/serverErrorExample"}}}}}}}, "/v1/kpi/": {"get": {"parameters": [{"description": "Search term for full_name, description, or period", "schema": {"type": "string"}, "in": "query", "name": "search", "required": false}, {"description": "The ID of the employee", "schema": {"type": "string", "minLength": 1, "maxLength": 50}, "in": "query", "name": "employee_id", "required": false}, {"description": "The period of the KPI (e.g., '2024-Q1')", "schema": {"type": "string", "minLength": 1, "maxLength": 50}, "in": "query", "name": "period", "required": false}, {"description": "The status of the KPI", "schema": {"anyOf": [{"const": "not_started", "type": "string"}, {"const": "in_progress", "type": "string"}, {"const": "completed_below_target", "type": "string"}, {"const": "completed_on_target", "type": "string"}, {"const": "completed_above_target", "type": "string"}]}, "in": "query", "name": "status", "required": false}, {"description": "The page number", "schema": {"type": "number"}, "in": "query", "name": "page", "required": false}, {"description": "The page size", "schema": {"type": "number"}, "in": "query", "name": "pageSize", "required": false}], "operationId": "getV1Kpi", "tags": ["kpi"], "summary": "Get all KPIs", "description": "Retrieve all KPIs with search, filter, and pagination", "security": [{"bearerAuth": []}], "responses": {"200": {}}}, "post": {"parameters": [], "operationId": "postV1Kpi", "tags": ["kpi"], "summary": "Create a new KPI", "security": [{"bearerAuth": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"full_name": {"minLength": 1, "maxLength": 100, "description": "The full name of the employee", "type": "string"}, "employee_id": {"minLength": 1, "maxLength": 50, "description": "The ID of the employee", "type": "string"}, "description": {"minLength": 1, "maxLength": 500, "description": "The description of the KPI", "type": "string"}, "target": {"minLength": 1, "maxLength": 200, "description": "The target of the KPI", "type": "string"}, "period": {"minLength": 1, "maxLength": 50, "description": "The period of the KPI (e.g., '2024-Q1')", "type": "string"}, "status": {"description": "The status of the KPI", "anyOf": [{"const": "not_started", "type": "string"}, {"const": "in_progress", "type": "string"}, {"const": "completed_below_target", "type": "string"}, {"const": "completed_on_target", "type": "string"}, {"const": "completed_above_target", "type": "string"}]}, "bonus_received": {"min": 0, "description": "The bonus amount received", "type": "number"}, "additional_notes": {"description": "Additional notes for the KPI", "type": "string"}}, "required": ["full_name", "employee_id", "description", "target", "period"]}}, "multipart/form-data": {"schema": {"type": "object", "properties": {"full_name": {"minLength": 1, "maxLength": 100, "description": "The full name of the employee", "type": "string"}, "employee_id": {"minLength": 1, "maxLength": 50, "description": "The ID of the employee", "type": "string"}, "description": {"minLength": 1, "maxLength": 500, "description": "The description of the KPI", "type": "string"}, "target": {"minLength": 1, "maxLength": 200, "description": "The target of the KPI", "type": "string"}, "period": {"minLength": 1, "maxLength": 50, "description": "The period of the KPI (e.g., '2024-Q1')", "type": "string"}, "status": {"description": "The status of the KPI", "anyOf": [{"const": "not_started", "type": "string"}, {"const": "in_progress", "type": "string"}, {"const": "completed_below_target", "type": "string"}, {"const": "completed_on_target", "type": "string"}, {"const": "completed_above_target", "type": "string"}]}, "bonus_received": {"min": 0, "description": "The bonus amount received", "type": "number"}, "additional_notes": {"description": "Additional notes for the KPI", "type": "string"}}, "required": ["full_name", "employee_id", "description", "target", "period"]}}, "text/plain": {"schema": {"type": "object", "properties": {"full_name": {"minLength": 1, "maxLength": 100, "description": "The full name of the employee", "type": "string"}, "employee_id": {"minLength": 1, "maxLength": 50, "description": "The ID of the employee", "type": "string"}, "description": {"minLength": 1, "maxLength": 500, "description": "The description of the KPI", "type": "string"}, "target": {"minLength": 1, "maxLength": 200, "description": "The target of the KPI", "type": "string"}, "period": {"minLength": 1, "maxLength": 50, "description": "The period of the KPI (e.g., '2024-Q1')", "type": "string"}, "status": {"description": "The status of the KPI", "anyOf": [{"const": "not_started", "type": "string"}, {"const": "in_progress", "type": "string"}, {"const": "completed_below_target", "type": "string"}, {"const": "completed_on_target", "type": "string"}, {"const": "completed_above_target", "type": "string"}]}, "bonus_received": {"min": 0, "description": "The bonus amount received", "type": "number"}, "additional_notes": {"description": "Additional notes for the KPI", "type": "string"}}, "required": ["full_name", "employee_id", "description", "target", "period"]}}}}, "responses": {"200": {}}}}, "/v1/kpi/{id}": {"get": {"parameters": [{"description": "The ID of the KPI", "schema": {"type": "string", "format": "uuid"}, "in": "path", "name": "id", "required": true}], "operationId": "getV1KpiById", "tags": ["kpi"], "summary": "Get KPI by ID", "description": "Retrieve a specific KPI by its ID", "security": [{"bearerAuth": []}], "responses": {"200": {}}}, "put": {"parameters": [{"description": "The ID of the KPI to update", "schema": {"type": "string", "format": "uuid"}, "in": "path", "name": "id", "required": true}], "operationId": "putV1KpiById", "tags": ["kpi"], "summary": "Update a KPI", "security": [{"bearerAuth": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"full_name": {"minLength": 1, "maxLength": 100, "description": "The full name of the employee", "type": "string"}, "employee_id": {"minLength": 1, "maxLength": 50, "description": "The ID of the employee", "type": "string"}, "description": {"minLength": 1, "maxLength": 500, "description": "The description of the KPI", "type": "string"}, "target": {"minLength": 1, "maxLength": 200, "description": "The target of the KPI", "type": "string"}, "period": {"minLength": 1, "maxLength": 50, "description": "The period of the KPI (e.g., '2024-Q1')", "type": "string"}, "status": {"description": "The status of the KPI", "anyOf": [{"const": "not_started", "type": "string"}, {"const": "in_progress", "type": "string"}, {"const": "completed_below_target", "type": "string"}, {"const": "completed_on_target", "type": "string"}, {"const": "completed_above_target", "type": "string"}]}, "bonus_received": {"min": 0, "description": "The bonus amount received", "type": "number"}, "additional_notes": {"description": "Additional notes for the KPI", "type": "string"}}}}, "multipart/form-data": {"schema": {"type": "object", "properties": {"full_name": {"minLength": 1, "maxLength": 100, "description": "The full name of the employee", "type": "string"}, "employee_id": {"minLength": 1, "maxLength": 50, "description": "The ID of the employee", "type": "string"}, "description": {"minLength": 1, "maxLength": 500, "description": "The description of the KPI", "type": "string"}, "target": {"minLength": 1, "maxLength": 200, "description": "The target of the KPI", "type": "string"}, "period": {"minLength": 1, "maxLength": 50, "description": "The period of the KPI (e.g., '2024-Q1')", "type": "string"}, "status": {"description": "The status of the KPI", "anyOf": [{"const": "not_started", "type": "string"}, {"const": "in_progress", "type": "string"}, {"const": "completed_below_target", "type": "string"}, {"const": "completed_on_target", "type": "string"}, {"const": "completed_above_target", "type": "string"}]}, "bonus_received": {"min": 0, "description": "The bonus amount received", "type": "number"}, "additional_notes": {"description": "Additional notes for the KPI", "type": "string"}}}}, "text/plain": {"schema": {"type": "object", "properties": {"full_name": {"minLength": 1, "maxLength": 100, "description": "The full name of the employee", "type": "string"}, "employee_id": {"minLength": 1, "maxLength": 50, "description": "The ID of the employee", "type": "string"}, "description": {"minLength": 1, "maxLength": 500, "description": "The description of the KPI", "type": "string"}, "target": {"minLength": 1, "maxLength": 200, "description": "The target of the KPI", "type": "string"}, "period": {"minLength": 1, "maxLength": 50, "description": "The period of the KPI (e.g., '2024-Q1')", "type": "string"}, "status": {"description": "The status of the KPI", "anyOf": [{"const": "not_started", "type": "string"}, {"const": "in_progress", "type": "string"}, {"const": "completed_below_target", "type": "string"}, {"const": "completed_on_target", "type": "string"}, {"const": "completed_above_target", "type": "string"}]}, "bonus_received": {"min": 0, "description": "The bonus amount received", "type": "number"}, "additional_notes": {"description": "Additional notes for the KPI", "type": "string"}}}}}}, "responses": {"200": {}}}, "delete": {"parameters": [{"description": "The ID of the KPI to delete", "schema": {"type": "string", "format": "uuid"}, "in": "path", "name": "id", "required": true}], "operationId": "deleteV1KpiById", "tags": ["kpi"], "summary": "Delete a KPI", "security": [{"bearerAuth": []}], "responses": {"200": {}}}}, "/v1/kpi/{id}/status": {"patch": {"parameters": [{"description": "The ID of the KPI", "schema": {"type": "string", "format": "uuid"}, "in": "path", "name": "id", "required": true}], "operationId": "patchV1KpiByIdStatus", "tags": ["kpi"], "summary": "Update KPI status", "security": [{"bearerAuth": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"status": {"description": "The status of the KPI", "anyOf": [{"const": "not_started", "type": "string"}, {"const": "in_progress", "type": "string"}, {"const": "completed_below_target", "type": "string"}, {"const": "completed_on_target", "type": "string"}, {"const": "completed_above_target", "type": "string"}]}}, "required": ["status"]}}, "multipart/form-data": {"schema": {"type": "object", "properties": {"status": {"description": "The status of the KPI", "anyOf": [{"const": "not_started", "type": "string"}, {"const": "in_progress", "type": "string"}, {"const": "completed_below_target", "type": "string"}, {"const": "completed_on_target", "type": "string"}, {"const": "completed_above_target", "type": "string"}]}}, "required": ["status"]}}, "text/plain": {"schema": {"type": "object", "properties": {"status": {"description": "The status of the KPI", "anyOf": [{"const": "not_started", "type": "string"}, {"const": "in_progress", "type": "string"}, {"const": "completed_below_target", "type": "string"}, {"const": "completed_on_target", "type": "string"}, {"const": "completed_above_target", "type": "string"}]}}, "required": ["status"]}}}}, "responses": {"200": {}}}}, "/v1/kpi/{id}/bonus": {"patch": {"parameters": [{"description": "The ID of the KPI", "schema": {"type": "string", "format": "uuid"}, "in": "path", "name": "id", "required": true}], "operationId": "patchV1KpiByIdBonus", "tags": ["kpi"], "summary": "Update KPI bonus", "security": [{"bearerAuth": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"bonus_received": {"min": 0, "description": "The bonus amount received", "type": "number"}}, "required": ["bonus_received"]}}, "multipart/form-data": {"schema": {"type": "object", "properties": {"bonus_received": {"min": 0, "description": "The bonus amount received", "type": "number"}}, "required": ["bonus_received"]}}, "text/plain": {"schema": {"type": "object", "properties": {"bonus_received": {"min": 0, "description": "The bonus amount received", "type": "number"}}, "required": ["bonus_received"]}}}}, "responses": {"200": {}}}}, "/v1/projects/": {"post": {"parameters": [], "operationId": "postV1Projects", "tags": ["projects"], "summary": "Create a new project", "description": "Create a new project with the provided data", "security": [{"bearerAuth": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"organization_id": {"format": "uuid", "description": "Organization ID", "type": "string"}, "project_category": {"description": "Project category", "anyOf": [{"const": "Preliminary Research", "type": "string"}, {"const": "Administrasi", "type": "string"}, {"const": "Monitoring", "type": "string"}, {"const": "Digital Marketing", "type": "string"}, {"const": "Brand Audit", "type": "string"}, {"const": "Brand Strategy", "type": "string"}, {"const": "Draft Monthly Report", "type": "string"}]}, "project_name": {"minLength": 2, "maxLength": 100, "description": "Project name (2-100 characters)", "type": "string"}, "pic_project": {"format": "uuid", "description": "Employee ID of the person in charge of the project", "type": "string"}, "start_project": {"pattern": "^\\d{4}-\\d{2}-\\d{2}$", "description": "Date in YYYY-MM-DD format", "type": "string"}, "end_project": {"pattern": "^\\d{4}-\\d{2}-\\d{2}$", "description": "Date in YYYY-MM-DD format", "type": "string"}, "status_project": {"description": "Project status", "anyOf": [{"const": "Not Started", "type": "string"}, {"const": "In Progress", "type": "string"}, {"const": "Completed", "type": "string"}]}, "budget_project": {"description": "Project budget", "type": "string"}, "objectives": {"minLength": 2, "maxLength": 1000, "description": "Project objectives (2-1000 characters)", "type": "string"}}, "required": ["organization_id", "project_category", "project_name", "pic_project", "start_project", "end_project", "budget_project", "objectives"]}}, "multipart/form-data": {"schema": {"type": "object", "properties": {"organization_id": {"format": "uuid", "description": "Organization ID", "type": "string"}, "project_category": {"description": "Project category", "anyOf": [{"const": "Preliminary Research", "type": "string"}, {"const": "Administrasi", "type": "string"}, {"const": "Monitoring", "type": "string"}, {"const": "Digital Marketing", "type": "string"}, {"const": "Brand Audit", "type": "string"}, {"const": "Brand Strategy", "type": "string"}, {"const": "Draft Monthly Report", "type": "string"}]}, "project_name": {"minLength": 2, "maxLength": 100, "description": "Project name (2-100 characters)", "type": "string"}, "pic_project": {"format": "uuid", "description": "Employee ID of the person in charge of the project", "type": "string"}, "start_project": {"pattern": "^\\d{4}-\\d{2}-\\d{2}$", "description": "Date in YYYY-MM-DD format", "type": "string"}, "end_project": {"pattern": "^\\d{4}-\\d{2}-\\d{2}$", "description": "Date in YYYY-MM-DD format", "type": "string"}, "status_project": {"description": "Project status", "anyOf": [{"const": "Not Started", "type": "string"}, {"const": "In Progress", "type": "string"}, {"const": "Completed", "type": "string"}]}, "budget_project": {"description": "Project budget", "type": "string"}, "objectives": {"minLength": 2, "maxLength": 1000, "description": "Project objectives (2-1000 characters)", "type": "string"}}, "required": ["organization_id", "project_category", "project_name", "pic_project", "start_project", "end_project", "budget_project", "objectives"]}}, "text/plain": {"schema": {"type": "object", "properties": {"organization_id": {"format": "uuid", "description": "Organization ID", "type": "string"}, "project_category": {"description": "Project category", "anyOf": [{"const": "Preliminary Research", "type": "string"}, {"const": "Administrasi", "type": "string"}, {"const": "Monitoring", "type": "string"}, {"const": "Digital Marketing", "type": "string"}, {"const": "Brand Audit", "type": "string"}, {"const": "Brand Strategy", "type": "string"}, {"const": "Draft Monthly Report", "type": "string"}]}, "project_name": {"minLength": 2, "maxLength": 100, "description": "Project name (2-100 characters)", "type": "string"}, "pic_project": {"format": "uuid", "description": "Employee ID of the person in charge of the project", "type": "string"}, "start_project": {"pattern": "^\\d{4}-\\d{2}-\\d{2}$", "description": "Date in YYYY-MM-DD format", "type": "string"}, "end_project": {"pattern": "^\\d{4}-\\d{2}-\\d{2}$", "description": "Date in YYYY-MM-DD format", "type": "string"}, "status_project": {"description": "Project status", "anyOf": [{"const": "Not Started", "type": "string"}, {"const": "In Progress", "type": "string"}, {"const": "Completed", "type": "string"}]}, "budget_project": {"description": "Project budget", "type": "string"}, "objectives": {"minLength": 2, "maxLength": 1000, "description": "Project objectives (2-1000 characters)", "type": "string"}}, "required": ["organization_id", "project_category", "project_name", "pic_project", "start_project", "end_project", "budget_project", "objectives"]}}}}, "responses": {"200": {}}}, "get": {"parameters": [{"schema": {"type": "string"}, "in": "query", "name": "search", "required": false}, {"schema": {"anyOf": [{"format": "numeric", "default": 0, "type": "string"}, {"type": "number"}]}, "in": "query", "name": "page", "required": false}, {"schema": {"anyOf": [{"format": "numeric", "default": 0, "type": "string"}, {"type": "number"}]}, "in": "query", "name": "pageSize", "required": false}, {"schema": {"type": "string"}, "in": "query", "name": "project_category", "required": false}, {"schema": {"type": "string"}, "in": "query", "name": "status_project", "required": false}], "operationId": "getV1Projects", "tags": ["projects"], "summary": "Get all projects", "description": "Retrieve all projects with search, filter, and pagination", "security": [{"bearerAuth": []}], "responses": {"200": {}}}}, "/v1/projects/{id}": {"get": {"parameters": [{"description": "Project ID", "schema": {"type": "string", "format": "uuid"}, "in": "path", "name": "id", "required": true}], "operationId": "getV1ProjectsById", "tags": ["projects"], "summary": "Get project by ID", "description": "Retrieve a specific project by its ID", "security": [{"bearerAuth": []}], "responses": {"200": {}}}, "put": {"parameters": [{"description": "Project ID", "schema": {"type": "string", "format": "uuid"}, "in": "path", "name": "id", "required": true}], "operationId": "putV1ProjectsById", "tags": ["projects"], "summary": "Update a project", "description": "Update an existing project with the provided data", "security": [{"bearerAuth": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"organization_id": {"format": "uuid", "description": "Organization ID", "type": "string"}, "project_category": {"description": "Project category", "anyOf": [{"const": "Preliminary Research", "type": "string"}, {"const": "Administrasi", "type": "string"}, {"const": "Monitoring", "type": "string"}, {"const": "Digital Marketing", "type": "string"}, {"const": "Brand Audit", "type": "string"}, {"const": "Brand Strategy", "type": "string"}, {"const": "Draft Monthly Report", "type": "string"}]}, "project_name": {"minLength": 2, "maxLength": 100, "description": "Project name (2-100 characters)", "type": "string"}, "pic_project": {"format": "uuid", "description": "Employee ID of the person in charge of the project", "type": "string"}, "start_project": {"pattern": "^\\d{4}-\\d{2}-\\d{2}$", "description": "Date in YYYY-MM-DD format", "type": "string"}, "end_project": {"pattern": "^\\d{4}-\\d{2}-\\d{2}$", "description": "Date in YYYY-MM-DD format", "type": "string"}, "status_project": {"description": "Project status", "anyOf": [{"const": "Not Started", "type": "string"}, {"const": "In Progress", "type": "string"}, {"const": "Completed", "type": "string"}]}, "budget_project": {"description": "Project budget", "type": "string"}, "objectives": {"minLength": 2, "maxLength": 1000, "description": "Project objectives (2-1000 characters)", "type": "string"}}}}, "multipart/form-data": {"schema": {"type": "object", "properties": {"organization_id": {"format": "uuid", "description": "Organization ID", "type": "string"}, "project_category": {"description": "Project category", "anyOf": [{"const": "Preliminary Research", "type": "string"}, {"const": "Administrasi", "type": "string"}, {"const": "Monitoring", "type": "string"}, {"const": "Digital Marketing", "type": "string"}, {"const": "Brand Audit", "type": "string"}, {"const": "Brand Strategy", "type": "string"}, {"const": "Draft Monthly Report", "type": "string"}]}, "project_name": {"minLength": 2, "maxLength": 100, "description": "Project name (2-100 characters)", "type": "string"}, "pic_project": {"format": "uuid", "description": "Employee ID of the person in charge of the project", "type": "string"}, "start_project": {"pattern": "^\\d{4}-\\d{2}-\\d{2}$", "description": "Date in YYYY-MM-DD format", "type": "string"}, "end_project": {"pattern": "^\\d{4}-\\d{2}-\\d{2}$", "description": "Date in YYYY-MM-DD format", "type": "string"}, "status_project": {"description": "Project status", "anyOf": [{"const": "Not Started", "type": "string"}, {"const": "In Progress", "type": "string"}, {"const": "Completed", "type": "string"}]}, "budget_project": {"description": "Project budget", "type": "string"}, "objectives": {"minLength": 2, "maxLength": 1000, "description": "Project objectives (2-1000 characters)", "type": "string"}}}}, "text/plain": {"schema": {"type": "object", "properties": {"organization_id": {"format": "uuid", "description": "Organization ID", "type": "string"}, "project_category": {"description": "Project category", "anyOf": [{"const": "Preliminary Research", "type": "string"}, {"const": "Administrasi", "type": "string"}, {"const": "Monitoring", "type": "string"}, {"const": "Digital Marketing", "type": "string"}, {"const": "Brand Audit", "type": "string"}, {"const": "Brand Strategy", "type": "string"}, {"const": "Draft Monthly Report", "type": "string"}]}, "project_name": {"minLength": 2, "maxLength": 100, "description": "Project name (2-100 characters)", "type": "string"}, "pic_project": {"format": "uuid", "description": "Employee ID of the person in charge of the project", "type": "string"}, "start_project": {"pattern": "^\\d{4}-\\d{2}-\\d{2}$", "description": "Date in YYYY-MM-DD format", "type": "string"}, "end_project": {"pattern": "^\\d{4}-\\d{2}-\\d{2}$", "description": "Date in YYYY-MM-DD format", "type": "string"}, "status_project": {"description": "Project status", "anyOf": [{"const": "Not Started", "type": "string"}, {"const": "In Progress", "type": "string"}, {"const": "Completed", "type": "string"}]}, "budget_project": {"description": "Project budget", "type": "string"}, "objectives": {"minLength": 2, "maxLength": 1000, "description": "Project objectives (2-1000 characters)", "type": "string"}}}}}}, "responses": {"200": {}}}, "delete": {"parameters": [{"description": "Project ID", "schema": {"type": "string", "format": "uuid"}, "in": "path", "name": "id", "required": true}], "operationId": "deleteV1ProjectsById", "tags": ["projects"], "summary": "Delete a project", "description": "Soft delete a project", "security": [{"bearerAuth": []}], "responses": {"200": {}}}}, "/v1/deductions/": {"post": {"parameters": [], "operationId": "postV1Deductions", "tags": ["deductions"], "summary": "Create a new salary deduction", "description": "Create a new deduction entry for a salary record", "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"salary_id": {"format": "uuid", "description": "The ID of the salary record", "type": "string"}, "amount": {"minimum": 0, "maximum": **********, "description": "The deduction amount", "type": "number"}, "deduction_type": {"description": "The type of deduction", "anyOf": [{"const": "absence", "type": "string"}, {"const": "lateness", "type": "string"}, {"const": "loan", "type": "string"}, {"const": "other", "type": "string"}]}, "notes": {"maxLength": 1000, "description": "Additional notes about the deduction", "type": "string"}}, "required": ["salary_id", "amount", "deduction_type"]}}, "multipart/form-data": {"schema": {"type": "object", "properties": {"salary_id": {"format": "uuid", "description": "The ID of the salary record", "type": "string"}, "amount": {"minimum": 0, "maximum": **********, "description": "The deduction amount", "type": "number"}, "deduction_type": {"description": "The type of deduction", "anyOf": [{"const": "absence", "type": "string"}, {"const": "lateness", "type": "string"}, {"const": "loan", "type": "string"}, {"const": "other", "type": "string"}]}, "notes": {"maxLength": 1000, "description": "Additional notes about the deduction", "type": "string"}}, "required": ["salary_id", "amount", "deduction_type"]}}, "text/plain": {"schema": {"type": "object", "properties": {"salary_id": {"format": "uuid", "description": "The ID of the salary record", "type": "string"}, "amount": {"minimum": 0, "maximum": **********, "description": "The deduction amount", "type": "number"}, "deduction_type": {"description": "The type of deduction", "anyOf": [{"const": "absence", "type": "string"}, {"const": "lateness", "type": "string"}, {"const": "loan", "type": "string"}, {"const": "other", "type": "string"}]}, "notes": {"maxLength": 1000, "description": "Additional notes about the deduction", "type": "string"}}, "required": ["salary_id", "amount", "deduction_type"]}}}}, "responses": {"200": {}}}}, "/v1/deductions/salary/{salaryId}": {"get": {"parameters": [{"description": "The ID of the salary record", "schema": {"type": "string", "format": "uuid"}, "in": "path", "name": "salaryId", "required": true}], "operationId": "getV1DeductionsSalaryBySalaryId", "tags": ["deductions"], "summary": "Get deductions by salary ID", "description": "Retrieve all deductions for a specific salary record", "responses": {"200": {}}}}, "/v1/deductions/{id}": {"get": {"parameters": [{"description": "The ID of the deduction record", "schema": {"type": "string", "format": "uuid"}, "in": "path", "name": "id", "required": true}], "operationId": "getV1DeductionsById", "tags": ["deductions"], "summary": "Get deduction by ID", "description": "Retrieve a specific deduction by its ID", "responses": {"200": {}}}, "put": {"parameters": [{"description": "The ID of the deduction record", "schema": {"type": "string", "format": "uuid"}, "in": "path", "name": "id", "required": true}], "operationId": "putV1DeductionsById", "tags": ["deductions"], "summary": "Update a deduction", "description": "Update an existing deduction record", "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"amount": {"minimum": 0, "maximum": **********, "description": "The deduction amount", "type": "number"}, "deduction_type": {"description": "The type of deduction", "anyOf": [{"const": "absence", "type": "string"}, {"const": "lateness", "type": "string"}, {"const": "loan", "type": "string"}, {"const": "other", "type": "string"}]}, "notes": {"maxLength": 1000, "description": "Additional notes about the deduction", "type": "string"}}}}, "multipart/form-data": {"schema": {"type": "object", "properties": {"amount": {"minimum": 0, "maximum": **********, "description": "The deduction amount", "type": "number"}, "deduction_type": {"description": "The type of deduction", "anyOf": [{"const": "absence", "type": "string"}, {"const": "lateness", "type": "string"}, {"const": "loan", "type": "string"}, {"const": "other", "type": "string"}]}, "notes": {"maxLength": 1000, "description": "Additional notes about the deduction", "type": "string"}}}}, "text/plain": {"schema": {"type": "object", "properties": {"amount": {"minimum": 0, "maximum": **********, "description": "The deduction amount", "type": "number"}, "deduction_type": {"description": "The type of deduction", "anyOf": [{"const": "absence", "type": "string"}, {"const": "lateness", "type": "string"}, {"const": "loan", "type": "string"}, {"const": "other", "type": "string"}]}, "notes": {"maxLength": 1000, "description": "Additional notes about the deduction", "type": "string"}}}}}}, "responses": {"200": {}}}, "delete": {"parameters": [{"description": "The ID of the deduction record", "schema": {"type": "string", "format": "uuid"}, "in": "path", "name": "id", "required": true}], "operationId": "deleteV1DeductionsById", "tags": ["deductions"], "summary": "Delete a deduction", "description": "Delete a deduction record", "responses": {"200": {}}}}, "/v1/allowances/": {"post": {"parameters": [], "operationId": "postV1Allowances", "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"salary_id": {"format": "uuid", "description": "The ID of the salary record", "type": "string"}, "amount": {"minimum": 0, "maximum": **********, "description": "The allowance amount", "type": "number"}, "allowance_type": {"description": "The type of allowance", "anyOf": [{"const": "transport", "type": "string"}, {"const": "meal", "type": "string"}, {"const": "health", "type": "string"}, {"const": "position", "type": "string"}, {"const": "tenure", "type": "string"}, {"const": "thr", "type": "string"}, {"const": "other", "type": "string"}]}, "notes": {"maxLength": 1000, "description": "Additional notes about the allowance", "type": "string"}}, "required": ["salary_id", "amount", "allowance_type"]}}, "multipart/form-data": {"schema": {"type": "object", "properties": {"salary_id": {"format": "uuid", "description": "The ID of the salary record", "type": "string"}, "amount": {"minimum": 0, "maximum": **********, "description": "The allowance amount", "type": "number"}, "allowance_type": {"description": "The type of allowance", "anyOf": [{"const": "transport", "type": "string"}, {"const": "meal", "type": "string"}, {"const": "health", "type": "string"}, {"const": "position", "type": "string"}, {"const": "tenure", "type": "string"}, {"const": "thr", "type": "string"}, {"const": "other", "type": "string"}]}, "notes": {"maxLength": 1000, "description": "Additional notes about the allowance", "type": "string"}}, "required": ["salary_id", "amount", "allowance_type"]}}, "text/plain": {"schema": {"type": "object", "properties": {"salary_id": {"format": "uuid", "description": "The ID of the salary record", "type": "string"}, "amount": {"minimum": 0, "maximum": **********, "description": "The allowance amount", "type": "number"}, "allowance_type": {"description": "The type of allowance", "anyOf": [{"const": "transport", "type": "string"}, {"const": "meal", "type": "string"}, {"const": "health", "type": "string"}, {"const": "position", "type": "string"}, {"const": "tenure", "type": "string"}, {"const": "thr", "type": "string"}, {"const": "other", "type": "string"}]}, "notes": {"maxLength": 1000, "description": "Additional notes about the allowance", "type": "string"}}, "required": ["salary_id", "amount", "allowance_type"]}}}}, "responses": {"200": {}}}}, "/v1/allowances/salary/{salaryId}": {"get": {"parameters": [{"description": "The ID of the salary record", "schema": {"type": "string", "format": "uuid"}, "in": "path", "name": "salaryId", "required": true}], "operationId": "getV1AllowancesSalaryBySalaryId", "responses": {"200": {}}}}, "/v1/allowances/{id}": {"get": {"parameters": [{"description": "The ID of the allowance entry to retrieve", "schema": {"type": "string", "format": "uuid"}, "in": "path", "name": "id", "required": true}], "operationId": "getV1AllowancesById", "tags": ["allowances"], "summary": "Get allowance by ID", "description": "Retrieve a specific allowance by its ID", "security": [{"bearerAuth": []}], "responses": {"200": {}}}, "put": {"parameters": [{"description": "The ID of the allowance entry to update", "schema": {"type": "string", "format": "uuid"}, "in": "path", "name": "id", "required": true}], "operationId": "putV1AllowancesById", "tags": ["allowances"], "summary": "Update allowance", "description": "Update an existing allowance entry", "security": [{"bearerAuth": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"amount": {"minimum": 0, "maximum": **********, "description": "The allowance amount", "type": "number"}, "allowance_type": {"description": "The type of allowance", "anyOf": [{"const": "transport", "type": "string"}, {"const": "meal", "type": "string"}, {"const": "health", "type": "string"}, {"const": "position", "type": "string"}, {"const": "tenure", "type": "string"}, {"const": "thr", "type": "string"}, {"const": "other", "type": "string"}]}, "notes": {"maxLength": 1000, "description": "Additional notes about the allowance", "type": "string"}}}}, "multipart/form-data": {"schema": {"type": "object", "properties": {"amount": {"minimum": 0, "maximum": **********, "description": "The allowance amount", "type": "number"}, "allowance_type": {"description": "The type of allowance", "anyOf": [{"const": "transport", "type": "string"}, {"const": "meal", "type": "string"}, {"const": "health", "type": "string"}, {"const": "position", "type": "string"}, {"const": "tenure", "type": "string"}, {"const": "thr", "type": "string"}, {"const": "other", "type": "string"}]}, "notes": {"maxLength": 1000, "description": "Additional notes about the allowance", "type": "string"}}}}, "text/plain": {"schema": {"type": "object", "properties": {"amount": {"minimum": 0, "maximum": **********, "description": "The allowance amount", "type": "number"}, "allowance_type": {"description": "The type of allowance", "anyOf": [{"const": "transport", "type": "string"}, {"const": "meal", "type": "string"}, {"const": "health", "type": "string"}, {"const": "position", "type": "string"}, {"const": "tenure", "type": "string"}, {"const": "thr", "type": "string"}, {"const": "other", "type": "string"}]}, "notes": {"maxLength": 1000, "description": "Additional notes about the allowance", "type": "string"}}}}}}, "responses": {"200": {}}}, "delete": {"parameters": [{"description": "The ID of the allowance entry to delete", "schema": {"type": "string", "format": "uuid"}, "in": "path", "name": "id", "required": true}], "operationId": "deleteV1AllowancesById", "tags": ["allowances"], "summary": "Delete allowance", "description": "Delete an existing allowance entry", "security": [{"bearerAuth": []}], "responses": {"200": {}}}}, "/v1/bonuses/": {"post": {"parameters": [], "operationId": "postV1Bonuses", "tags": ["bonuses"], "summary": "Create a new bonus", "description": "Create a new bonus entry for a salary", "security": [{"bearerAuth": []}], "responses": {"200": {"description": "Bonus created successfully", "content": {"application/json": {"example": {"success": true, "message": "Bonus created successfully", "data": {"id": "323e4567-e89b-12d3-a456-************", "salary_id": "123e4567-e89b-12d3-a456-************", "amount": 500000, "bonus_type": "kpi", "notes": "KPI achievement bonus for Q2 2025", "kpi_id": "223e4567-e89b-12d3-a456-************", "project_id": null}}}}}}, "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"salary_id": {"format": "uuid", "description": "The ID of the salary record", "type": "string"}, "amount": {"minimum": 0, "maximum": **********, "description": "The bonus amount", "type": "number"}, "bonus_type": {"description": "The type of bonus", "anyOf": [{"const": "kpi", "type": "string"}, {"const": "project", "type": "string"}, {"const": "other", "type": "string"}]}, "notes": {"maxLength": 1000, "description": "Additional notes about the bonus", "type": "string"}, "kpi_id": {"format": "uuid", "description": "The ID of the KPI record (required for KPI type bonuses)", "type": "string"}, "project_id": {"format": "uuid", "description": "The ID of the project (for project type bonuses)", "type": "string"}}, "required": ["salary_id", "amount", "bonus_type"]}}, "multipart/form-data": {"schema": {"type": "object", "properties": {"salary_id": {"format": "uuid", "description": "The ID of the salary record", "type": "string"}, "amount": {"minimum": 0, "maximum": **********, "description": "The bonus amount", "type": "number"}, "bonus_type": {"description": "The type of bonus", "anyOf": [{"const": "kpi", "type": "string"}, {"const": "project", "type": "string"}, {"const": "other", "type": "string"}]}, "notes": {"maxLength": 1000, "description": "Additional notes about the bonus", "type": "string"}, "kpi_id": {"format": "uuid", "description": "The ID of the KPI record (required for KPI type bonuses)", "type": "string"}, "project_id": {"format": "uuid", "description": "The ID of the project (for project type bonuses)", "type": "string"}}, "required": ["salary_id", "amount", "bonus_type"]}}, "text/plain": {"schema": {"type": "object", "properties": {"salary_id": {"format": "uuid", "description": "The ID of the salary record", "type": "string"}, "amount": {"minimum": 0, "maximum": **********, "description": "The bonus amount", "type": "number"}, "bonus_type": {"description": "The type of bonus", "anyOf": [{"const": "kpi", "type": "string"}, {"const": "project", "type": "string"}, {"const": "other", "type": "string"}]}, "notes": {"maxLength": 1000, "description": "Additional notes about the bonus", "type": "string"}, "kpi_id": {"format": "uuid", "description": "The ID of the KPI record (required for KPI type bonuses)", "type": "string"}, "project_id": {"format": "uuid", "description": "The ID of the project (for project type bonuses)", "type": "string"}}, "required": ["salary_id", "amount", "bonus_type"]}}}}}}, "/v1/bonuses/salary/{salaryId}": {"get": {"parameters": [{"description": "The ID of the salary record", "schema": {"type": "string", "format": "uuid"}, "in": "path", "name": "salaryId", "required": true}], "operationId": "getV1BonusesSalaryBySalaryId", "tags": ["bonuses"], "summary": "Get bonuses by salary ID", "description": "Retrieve all bonus entries for a specific salary", "security": [{"bearerAuth": []}], "responses": {"200": {"description": "Bonuses retrieved successfully", "content": {"application/json": {"example": {"success": true, "message": "Bonuses retrieved successfully", "data": [{"id": "323e4567-e89b-12d3-a456-************", "salary_id": "123e4567-e89b-12d3-a456-************", "amount": 500000, "bonus_type": "kpi", "notes": "KPI achievement bonus for Q2 2025", "kpi_id": "223e4567-e89b-12d3-a456-************", "project_id": null}]}}}}}}}, "/v1/bonuses/{id}": {"get": {"parameters": [{"description": "The ID of the bonus record", "schema": {"type": "string", "format": "uuid"}, "in": "path", "name": "id", "required": true}], "operationId": "getV1BonusesById", "tags": ["bonuses"], "summary": "Get bonus by ID", "description": "Retrieve a specific bonus entry by its ID", "security": [{"bearerAuth": []}], "responses": {"200": {"description": "Bonus retrieved successfully", "content": {"application/json": {"example": {"success": true, "message": "Bonus retrieved successfully", "data": {"id": "323e4567-e89b-12d3-a456-************", "salary_id": "123e4567-e89b-12d3-a456-************", "amount": 500000, "bonus_type": "kpi", "notes": "KPI achievement bonus for Q2 2025", "kpi_id": "223e4567-e89b-12d3-a456-************", "project_id": null}}}}}}}, "put": {"parameters": [{"description": "The ID of the bonus record", "schema": {"type": "string", "format": "uuid"}, "in": "path", "name": "id", "required": true}], "operationId": "putV1BonusesById", "tags": ["bonuses"], "summary": "Update a bonus", "description": "Update an existing bonus entry", "security": [{"bearerAuth": []}], "responses": {"200": {"description": "Bonus updated successfully", "content": {"application/json": {"example": {"success": true, "message": "Bonus updated successfully", "data": {"id": "323e4567-e89b-12d3-a456-************", "salary_id": "123e4567-e89b-12d3-a456-************", "amount": 600000, "bonus_type": "kpi", "notes": "Updated KPI achievement bonus for Q2 2025", "kpi_id": "223e4567-e89b-12d3-a456-************", "project_id": null}}}}}}, "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"amount": {"minimum": 0, "maximum": **********, "description": "The bonus amount", "type": "number"}, "bonus_type": {"description": "The type of bonus", "anyOf": [{"const": "kpi", "type": "string"}, {"const": "project", "type": "string"}, {"const": "other", "type": "string"}]}, "notes": {"maxLength": 1000, "description": "Additional notes about the bonus", "type": "string"}, "kpi_id": {"format": "uuid", "description": "The ID of the KPI record (required for KPI type bonuses)", "type": "string"}, "project_id": {"format": "uuid", "description": "The ID of the project (for project type bonuses)", "type": "string"}}}}, "multipart/form-data": {"schema": {"type": "object", "properties": {"amount": {"minimum": 0, "maximum": **********, "description": "The bonus amount", "type": "number"}, "bonus_type": {"description": "The type of bonus", "anyOf": [{"const": "kpi", "type": "string"}, {"const": "project", "type": "string"}, {"const": "other", "type": "string"}]}, "notes": {"maxLength": 1000, "description": "Additional notes about the bonus", "type": "string"}, "kpi_id": {"format": "uuid", "description": "The ID of the KPI record (required for KPI type bonuses)", "type": "string"}, "project_id": {"format": "uuid", "description": "The ID of the project (for project type bonuses)", "type": "string"}}}}, "text/plain": {"schema": {"type": "object", "properties": {"amount": {"minimum": 0, "maximum": **********, "description": "The bonus amount", "type": "number"}, "bonus_type": {"description": "The type of bonus", "anyOf": [{"const": "kpi", "type": "string"}, {"const": "project", "type": "string"}, {"const": "other", "type": "string"}]}, "notes": {"maxLength": 1000, "description": "Additional notes about the bonus", "type": "string"}, "kpi_id": {"format": "uuid", "description": "The ID of the KPI record (required for KPI type bonuses)", "type": "string"}, "project_id": {"format": "uuid", "description": "The ID of the project (for project type bonuses)", "type": "string"}}}}}}}, "delete": {"parameters": [{"description": "The ID of the bonus record", "schema": {"type": "string", "format": "uuid"}, "in": "path", "name": "id", "required": true}], "operationId": "deleteV1BonusesById", "tags": ["bonuses"], "summary": "Delete a bonus", "description": "Delete an existing bonus entry", "security": [{"bearerAuth": []}], "responses": {"200": {"description": "Bonus deleted successfully", "content": {"application/json": {"example": {"success": true, "message": "Bonus deleted successfully", "data": {"id": "323e4567-e89b-12d3-a456-************", "deleted_at": "2025-05-10T00:00:00.000Z", "deleted_by": "auth0|*********"}}}}}}}}, "/v1/kpi-projects/": {"post": {"parameters": [], "operationId": "postV1Kpi-projects", "tags": ["kpi-projects"], "summary": "Create a new KPI Project", "description": "Create a new KPI Project with the provided details", "security": [{"bearerAuth": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"project_name": {"minLength": 1, "maxLength": 100, "description": "The name of the project", "type": "string"}, "project_id": {"minLength": 1, "maxLength": 50, "description": "The ID of the project", "type": "string"}, "description": {"minLength": 1, "maxLength": 500, "description": "The description of the KPI project", "type": "string"}, "target": {"minLength": 1, "maxLength": 200, "description": "The target of the KPI project", "type": "string"}, "period": {"minLength": 1, "maxLength": 50, "description": "The period of the KPI project (e.g., '2024-Q1')", "type": "string"}, "status": {"description": "The status of the KPI project", "anyOf": [{"const": "not_started", "type": "string"}, {"const": "in_progress", "type": "string"}, {"const": "completed_below_target", "type": "string"}, {"const": "completed_on_target", "type": "string"}, {"const": "completed_above_target", "type": "string"}]}, "additional_notes": {"maxLength": 1000, "description": "Additional notes about the KPI project", "type": "string"}}, "required": ["project_name", "project_id", "description", "target", "period"]}}, "multipart/form-data": {"schema": {"type": "object", "properties": {"project_name": {"minLength": 1, "maxLength": 100, "description": "The name of the project", "type": "string"}, "project_id": {"minLength": 1, "maxLength": 50, "description": "The ID of the project", "type": "string"}, "description": {"minLength": 1, "maxLength": 500, "description": "The description of the KPI project", "type": "string"}, "target": {"minLength": 1, "maxLength": 200, "description": "The target of the KPI project", "type": "string"}, "period": {"minLength": 1, "maxLength": 50, "description": "The period of the KPI project (e.g., '2024-Q1')", "type": "string"}, "status": {"description": "The status of the KPI project", "anyOf": [{"const": "not_started", "type": "string"}, {"const": "in_progress", "type": "string"}, {"const": "completed_below_target", "type": "string"}, {"const": "completed_on_target", "type": "string"}, {"const": "completed_above_target", "type": "string"}]}, "additional_notes": {"maxLength": 1000, "description": "Additional notes about the KPI project", "type": "string"}}, "required": ["project_name", "project_id", "description", "target", "period"]}}, "text/plain": {"schema": {"type": "object", "properties": {"project_name": {"minLength": 1, "maxLength": 100, "description": "The name of the project", "type": "string"}, "project_id": {"minLength": 1, "maxLength": 50, "description": "The ID of the project", "type": "string"}, "description": {"minLength": 1, "maxLength": 500, "description": "The description of the KPI project", "type": "string"}, "target": {"minLength": 1, "maxLength": 200, "description": "The target of the KPI project", "type": "string"}, "period": {"minLength": 1, "maxLength": 50, "description": "The period of the KPI project (e.g., '2024-Q1')", "type": "string"}, "status": {"description": "The status of the KPI project", "anyOf": [{"const": "not_started", "type": "string"}, {"const": "in_progress", "type": "string"}, {"const": "completed_below_target", "type": "string"}, {"const": "completed_on_target", "type": "string"}, {"const": "completed_above_target", "type": "string"}]}, "additional_notes": {"maxLength": 1000, "description": "Additional notes about the KPI project", "type": "string"}}, "required": ["project_name", "project_id", "description", "target", "period"]}}}}, "responses": {"200": {}}}, "get": {"parameters": [{"schema": {"type": "number", "min": 1, "max": 100, "default": 10}, "in": "query", "name": "limit", "required": false}, {"schema": {"type": "number", "min": 0, "default": 0}, "in": "query", "name": "offset", "required": false}, {"description": "The project name to search for KPI Projects", "schema": {"type": "string", "minLength": 1}, "in": "query", "name": "project_name", "required": false}], "operationId": "getV1Kpi-projects", "tags": ["kpi-projects"], "summary": "Get all KPI Projects", "description": "Retrieve all KPI Projects with search (by project name), filter, and pagination", "security": [{"bearerAuth": []}], "responses": {"200": {}}}}, "/v1/kpi-projects/from-project": {"post": {"parameters": [], "operationId": "postV1Kpi-projectsFrom-project", "tags": ["kpi-projects"], "summary": "Create a KPI Project from a regular project", "description": "Automatically create a KPI Project when a regular project is created", "security": [{"bearerAuth": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"project_id": {"minLength": 1, "description": "The ID of the project", "type": "string"}, "project_name": {"minLength": 1, "description": "The name of the project", "type": "string"}, "objectives": {"minLength": 1, "description": "The objectives of the project", "type": "string"}, "start_date": {"pattern": "^\\d{4}-\\d{2}-\\d{2}$", "description": "The start date of the project (YYYY-MM-DD)", "type": "string"}, "end_date": {"pattern": "^\\d{4}-\\d{2}-\\d{2}$", "description": "The end date of the project (YYYY-MM-DD)", "type": "string"}}, "required": ["project_id", "project_name", "objectives", "start_date", "end_date"]}}, "multipart/form-data": {"schema": {"type": "object", "properties": {"project_id": {"minLength": 1, "description": "The ID of the project", "type": "string"}, "project_name": {"minLength": 1, "description": "The name of the project", "type": "string"}, "objectives": {"minLength": 1, "description": "The objectives of the project", "type": "string"}, "start_date": {"pattern": "^\\d{4}-\\d{2}-\\d{2}$", "description": "The start date of the project (YYYY-MM-DD)", "type": "string"}, "end_date": {"pattern": "^\\d{4}-\\d{2}-\\d{2}$", "description": "The end date of the project (YYYY-MM-DD)", "type": "string"}}, "required": ["project_id", "project_name", "objectives", "start_date", "end_date"]}}, "text/plain": {"schema": {"type": "object", "properties": {"project_id": {"minLength": 1, "description": "The ID of the project", "type": "string"}, "project_name": {"minLength": 1, "description": "The name of the project", "type": "string"}, "objectives": {"minLength": 1, "description": "The objectives of the project", "type": "string"}, "start_date": {"pattern": "^\\d{4}-\\d{2}-\\d{2}$", "description": "The start date of the project (YYYY-MM-DD)", "type": "string"}, "end_date": {"pattern": "^\\d{4}-\\d{2}-\\d{2}$", "description": "The end date of the project (YYYY-MM-DD)", "type": "string"}}, "required": ["project_id", "project_name", "objectives", "start_date", "end_date"]}}}}, "responses": {"200": {}}}}, "/v1/kpi-projects/{id}": {"get": {"parameters": [{"description": "The ID of the project", "schema": {"type": "string", "minLength": 1, "maxLength": 50}, "in": "path", "name": "id", "required": true}], "operationId": "getV1Kpi-projectsById", "tags": ["kpi-projects"], "summary": "Get KPI Project by ID", "description": "Retrieve a specific KPI Project by its ID", "security": [{"bearerAuth": []}], "responses": {"200": {}}}, "put": {"parameters": [{"description": "The ID of the project", "schema": {"type": "string", "minLength": 1, "maxLength": 50}, "in": "path", "name": "id", "required": true}], "operationId": "putV1Kpi-projectsById", "tags": ["kpi-projects"], "summary": "Update a KPI Project", "description": "Update an existing KPI Project", "security": [{"bearerAuth": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"project_name": {"minLength": 1, "maxLength": 100, "description": "The name of the project", "type": "string"}, "description": {"minLength": 1, "maxLength": 500, "description": "The description of the KPI project", "type": "string"}, "target": {"minLength": 1, "maxLength": 200, "description": "The target of the KPI project", "type": "string"}, "period": {"minLength": 1, "maxLength": 50, "description": "The period of the KPI project (e.g., '2024-Q1')", "type": "string"}, "status": {"description": "The status of the KPI project", "anyOf": [{"const": "not_started", "type": "string"}, {"const": "in_progress", "type": "string"}, {"const": "completed_below_target", "type": "string"}, {"const": "completed_on_target", "type": "string"}, {"const": "completed_above_target", "type": "string"}]}, "additional_notes": {"maxLength": 1000, "description": "Additional notes about the KPI project", "type": "string"}}}}, "multipart/form-data": {"schema": {"type": "object", "properties": {"project_name": {"minLength": 1, "maxLength": 100, "description": "The name of the project", "type": "string"}, "description": {"minLength": 1, "maxLength": 500, "description": "The description of the KPI project", "type": "string"}, "target": {"minLength": 1, "maxLength": 200, "description": "The target of the KPI project", "type": "string"}, "period": {"minLength": 1, "maxLength": 50, "description": "The period of the KPI project (e.g., '2024-Q1')", "type": "string"}, "status": {"description": "The status of the KPI project", "anyOf": [{"const": "not_started", "type": "string"}, {"const": "in_progress", "type": "string"}, {"const": "completed_below_target", "type": "string"}, {"const": "completed_on_target", "type": "string"}, {"const": "completed_above_target", "type": "string"}]}, "additional_notes": {"maxLength": 1000, "description": "Additional notes about the KPI project", "type": "string"}}}}, "text/plain": {"schema": {"type": "object", "properties": {"project_name": {"minLength": 1, "maxLength": 100, "description": "The name of the project", "type": "string"}, "description": {"minLength": 1, "maxLength": 500, "description": "The description of the KPI project", "type": "string"}, "target": {"minLength": 1, "maxLength": 200, "description": "The target of the KPI project", "type": "string"}, "period": {"minLength": 1, "maxLength": 50, "description": "The period of the KPI project (e.g., '2024-Q1')", "type": "string"}, "status": {"description": "The status of the KPI project", "anyOf": [{"const": "not_started", "type": "string"}, {"const": "in_progress", "type": "string"}, {"const": "completed_below_target", "type": "string"}, {"const": "completed_on_target", "type": "string"}, {"const": "completed_above_target", "type": "string"}]}, "additional_notes": {"maxLength": 1000, "description": "Additional notes about the KPI project", "type": "string"}}}}}}, "responses": {"200": {}}}, "delete": {"parameters": [{"description": "The ID of the project", "schema": {"type": "string", "minLength": 1, "maxLength": 50}, "in": "path", "name": "id", "required": true}], "operationId": "deleteV1Kpi-projectsById", "tags": ["kpi-projects"], "summary": "Delete a KPI Project", "description": "Delete an existing KPI Project", "security": [{"bearerAuth": []}], "responses": {"200": {}}}}, "/v1/kpi-projects/project/{project_id}": {"get": {"parameters": [{"description": "The ID of the project", "schema": {"type": "string", "minLength": 1, "maxLength": 50}, "in": "path", "name": "project_id", "required": true}], "operationId": "getV1Kpi-projectsProjectByProject_id", "tags": ["kpi-projects"], "summary": "Get KPI Projects by Project ID", "description": "Retrieve all KPI Projects related to a specific project", "security": [{"bearerAuth": []}], "responses": {"200": {}}}}, "/v1/project-tasks/": {"post": {"parameters": [], "operationId": "postV1Project-tasks", "summary": "Create a new project task", "tags": ["Project Tasks"], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"description": {"minLength": 3, "maxLength": 255, "description": "Task description (min 3 characters, max 255 characters)", "type": "string"}, "initial_date": {"pattern": "^\\d{4}-\\d{2}-\\d{2}$", "description": "Initial date in format YYYY-MM-DD", "type": "string"}, "due_date": {"pattern": "^\\d{4}-\\d{2}-\\d{2}$", "description": "Due date in format YYYY-MM-DD", "type": "string"}, "project_id": {"format": "uuid", "description": "Project ID (UUID format)", "type": "string"}, "employee_id": {"format": "uuid", "description": "Employee ID assigned to the task (UUID format)", "type": "string"}, "assigned_by": {"format": "uuid", "description": "User ID who assigned the task (UUID format)", "type": "string"}, "completion_status": {"description": "Task completion status (not_completed, on_progress, completed)", "default": "not_completed", "anyOf": [{"const": "not_completed", "type": "string"}, {"const": "on_progress", "type": "string"}, {"const": "completed", "type": "string"}]}, "weekly_log_id": {"format": "uuid", "description": "Weekly log ID (optional, UUID format)", "type": "string"}}, "required": ["description", "initial_date", "due_date", "project_id", "employee_id", "assigned_by"]}}, "multipart/form-data": {"schema": {"type": "object", "properties": {"description": {"minLength": 3, "maxLength": 255, "description": "Task description (min 3 characters, max 255 characters)", "type": "string"}, "initial_date": {"pattern": "^\\d{4}-\\d{2}-\\d{2}$", "description": "Initial date in format YYYY-MM-DD", "type": "string"}, "due_date": {"pattern": "^\\d{4}-\\d{2}-\\d{2}$", "description": "Due date in format YYYY-MM-DD", "type": "string"}, "project_id": {"format": "uuid", "description": "Project ID (UUID format)", "type": "string"}, "employee_id": {"format": "uuid", "description": "Employee ID assigned to the task (UUID format)", "type": "string"}, "assigned_by": {"format": "uuid", "description": "User ID who assigned the task (UUID format)", "type": "string"}, "completion_status": {"description": "Task completion status (not_completed, on_progress, completed)", "default": "not_completed", "anyOf": [{"const": "not_completed", "type": "string"}, {"const": "on_progress", "type": "string"}, {"const": "completed", "type": "string"}]}, "weekly_log_id": {"format": "uuid", "description": "Weekly log ID (optional, UUID format)", "type": "string"}}, "required": ["description", "initial_date", "due_date", "project_id", "employee_id", "assigned_by"]}}, "text/plain": {"schema": {"type": "object", "properties": {"description": {"minLength": 3, "maxLength": 255, "description": "Task description (min 3 characters, max 255 characters)", "type": "string"}, "initial_date": {"pattern": "^\\d{4}-\\d{2}-\\d{2}$", "description": "Initial date in format YYYY-MM-DD", "type": "string"}, "due_date": {"pattern": "^\\d{4}-\\d{2}-\\d{2}$", "description": "Due date in format YYYY-MM-DD", "type": "string"}, "project_id": {"format": "uuid", "description": "Project ID (UUID format)", "type": "string"}, "employee_id": {"format": "uuid", "description": "Employee ID assigned to the task (UUID format)", "type": "string"}, "assigned_by": {"format": "uuid", "description": "User ID who assigned the task (UUID format)", "type": "string"}, "completion_status": {"description": "Task completion status (not_completed, on_progress, completed)", "default": "not_completed", "anyOf": [{"const": "not_completed", "type": "string"}, {"const": "on_progress", "type": "string"}, {"const": "completed", "type": "string"}]}, "weekly_log_id": {"format": "uuid", "description": "Weekly log ID (optional, UUID format)", "type": "string"}}, "required": ["description", "initial_date", "due_date", "project_id", "employee_id", "assigned_by"]}}}}, "responses": {"200": {}}}, "get": {"parameters": [{"description": "Page number for pagination", "schema": {"default": 1, "anyOf": [{"format": "numeric", "default": 0, "type": "string"}, {"description": "Page number for pagination", "default": 1, "type": "number"}]}, "in": "query", "name": "page", "required": false}, {"description": "Number of items per page", "schema": {"default": 10, "anyOf": [{"format": "numeric", "default": 0, "type": "string"}, {"description": "Number of items per page", "default": 10, "type": "number"}]}, "in": "query", "name": "limit", "required": false}, {"description": "Field to sort by", "schema": {"type": "string"}, "in": "query", "name": "sort_by", "required": false}, {"description": "Sort direction (asc or desc)", "schema": {"type": "string", "enum": ["asc", "desc"], "default": "asc"}, "in": "query", "name": "sort_order", "required": false}, {"description": "Filter by project ID (UUID format)", "schema": {"type": "string", "format": "uuid"}, "in": "query", "name": "project_id", "required": false}, {"description": "Filter by employee ID (UUID format)", "schema": {"type": "string", "format": "uuid"}, "in": "query", "name": "employee_id", "required": false}, {"description": "Filter by assigned by ID (UUID format)", "schema": {"type": "string", "format": "uuid"}, "in": "query", "name": "assigned_by", "required": false}, {"description": "Filter by completion status (not_completed, on_progress, completed)", "schema": {"anyOf": [{"const": "not_completed", "type": "string"}, {"const": "on_progress", "type": "string"}, {"const": "completed", "type": "string"}]}, "in": "query", "name": "completion_status", "required": false}], "operationId": "getV1Project-tasks", "summary": "Get all project tasks", "tags": ["Project Tasks"], "responses": {"200": {}}}}, "/v1/project-tasks/{id}": {"get": {"parameters": [{"description": "The ID of the project task to retrieve", "schema": {"type": "string", "format": "uuid"}, "in": "path", "name": "id", "required": true}], "operationId": "getV1Project-tasksById", "summary": "Get project task by ID", "tags": ["Project Tasks"], "responses": {"200": {}}}, "put": {"parameters": [{"description": "The ID of the project task", "schema": {"type": "string", "format": "uuid"}, "in": "path", "name": "id", "required": true}], "operationId": "putV1Project-tasksById", "summary": "Update a project task", "tags": ["Project Tasks"], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"description": {"minLength": 3, "maxLength": 255, "description": "Task description (min 3 characters, max 255 characters)", "type": "string"}, "initial_date": {"pattern": "^\\d{4}-\\d{2}-\\d{2}$", "description": "Initial date in format YYYY-MM-DD", "type": "string"}, "due_date": {"pattern": "^\\d{4}-\\d{2}-\\d{2}$", "description": "Due date in format YYYY-MM-DD", "type": "string"}, "employee_id": {"format": "uuid", "description": "Employee ID assigned to the task (UUID format)", "type": "string"}, "assigned_by": {"format": "uuid", "description": "User ID who assigned the task (UUID format)", "type": "string"}, "completion_status": {"description": "Task completion status (not_completed, on_progress, completed)", "anyOf": [{"const": "not_completed", "type": "string"}, {"const": "on_progress", "type": "string"}, {"const": "completed", "type": "string"}]}, "weekly_log_id": {"format": "uuid", "description": "Weekly log ID (optional, UUID format)", "type": "string"}, "project_id": {"format": "uuid", "description": "Project ID (UUID format)", "type": "string"}}}}, "multipart/form-data": {"schema": {"type": "object", "properties": {"description": {"minLength": 3, "maxLength": 255, "description": "Task description (min 3 characters, max 255 characters)", "type": "string"}, "initial_date": {"pattern": "^\\d{4}-\\d{2}-\\d{2}$", "description": "Initial date in format YYYY-MM-DD", "type": "string"}, "due_date": {"pattern": "^\\d{4}-\\d{2}-\\d{2}$", "description": "Due date in format YYYY-MM-DD", "type": "string"}, "employee_id": {"format": "uuid", "description": "Employee ID assigned to the task (UUID format)", "type": "string"}, "assigned_by": {"format": "uuid", "description": "User ID who assigned the task (UUID format)", "type": "string"}, "completion_status": {"description": "Task completion status (not_completed, on_progress, completed)", "anyOf": [{"const": "not_completed", "type": "string"}, {"const": "on_progress", "type": "string"}, {"const": "completed", "type": "string"}]}, "weekly_log_id": {"format": "uuid", "description": "Weekly log ID (optional, UUID format)", "type": "string"}, "project_id": {"format": "uuid", "description": "Project ID (UUID format)", "type": "string"}}}}, "text/plain": {"schema": {"type": "object", "properties": {"description": {"minLength": 3, "maxLength": 255, "description": "Task description (min 3 characters, max 255 characters)", "type": "string"}, "initial_date": {"pattern": "^\\d{4}-\\d{2}-\\d{2}$", "description": "Initial date in format YYYY-MM-DD", "type": "string"}, "due_date": {"pattern": "^\\d{4}-\\d{2}-\\d{2}$", "description": "Due date in format YYYY-MM-DD", "type": "string"}, "employee_id": {"format": "uuid", "description": "Employee ID assigned to the task (UUID format)", "type": "string"}, "assigned_by": {"format": "uuid", "description": "User ID who assigned the task (UUID format)", "type": "string"}, "completion_status": {"description": "Task completion status (not_completed, on_progress, completed)", "anyOf": [{"const": "not_completed", "type": "string"}, {"const": "on_progress", "type": "string"}, {"const": "completed", "type": "string"}]}, "weekly_log_id": {"format": "uuid", "description": "Weekly log ID (optional, UUID format)", "type": "string"}, "project_id": {"format": "uuid", "description": "Project ID (UUID format)", "type": "string"}}}}}}, "responses": {"200": {}}}, "delete": {"parameters": [{"description": "The ID of the project task to delete", "schema": {"type": "string", "format": "uuid"}, "in": "path", "name": "id", "required": true}], "operationId": "deleteV1Project-tasksById", "summary": "Delete a project task (soft delete)", "tags": ["Project Tasks"], "responses": {"200": {}}}}, "/v1/project-tasks/project/{projectId}": {"get": {"parameters": [{"description": "The ID of the project", "schema": {"type": "string", "format": "uuid"}, "in": "path", "name": "projectId", "required": true}, {"description": "Page number for pagination", "schema": {"default": 1, "anyOf": [{"format": "numeric", "default": 0, "type": "string"}, {"description": "Page number for pagination", "default": 1, "type": "number"}]}, "in": "query", "name": "page", "required": false}, {"description": "Number of items per page", "schema": {"default": 10, "anyOf": [{"format": "numeric", "default": 0, "type": "string"}, {"description": "Number of items per page", "default": 10, "type": "number"}]}, "in": "query", "name": "limit", "required": false}, {"description": "Field to sort by", "schema": {"type": "string"}, "in": "query", "name": "sort_by", "required": false}, {"description": "Sort direction (asc or desc)", "schema": {"type": "string", "default": "asc"}, "in": "query", "name": "sort_order", "required": false}], "operationId": "getV1Project-tasksProjectByProjectId", "summary": "Get project tasks by project ID", "tags": ["Project Tasks"], "responses": {"200": {}}}}, "/v1/project-tasks/{id}/status": {"patch": {"parameters": [{"description": "The ID of the project task", "schema": {"type": "string", "format": "uuid"}, "in": "path", "name": "id", "required": true}], "operationId": "patchV1Project-tasksByIdStatus", "summary": "Update project task status", "tags": ["Project Tasks"], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"completion_status": {"description": "Task completion status (not_completed, on_progress, completed)", "anyOf": [{"const": "not_completed", "type": "string"}, {"const": "on_progress", "type": "string"}, {"const": "completed", "type": "string"}]}}, "required": ["completion_status"]}}, "multipart/form-data": {"schema": {"type": "object", "properties": {"completion_status": {"description": "Task completion status (not_completed, on_progress, completed)", "anyOf": [{"const": "not_completed", "type": "string"}, {"const": "on_progress", "type": "string"}, {"const": "completed", "type": "string"}]}}, "required": ["completion_status"]}}, "text/plain": {"schema": {"type": "object", "properties": {"completion_status": {"description": "Task completion status (not_completed, on_progress, completed)", "anyOf": [{"const": "not_completed", "type": "string"}, {"const": "on_progress", "type": "string"}, {"const": "completed", "type": "string"}]}}, "required": ["completion_status"]}}}}, "responses": {"200": {}}}}, "/v1/tasks/{id}/status": {"patch": {"parameters": [{"description": "The ID of the task", "schema": {"type": "string", "format": "uuid"}, "in": "path", "name": "id", "required": true}], "operationId": "patchV1TasksByIdStatus", "summary": "Update task status", "tags": ["Tasks"], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"completion_status": {"description": "Task completion status (not_completed, on_progress, completed)", "anyOf": [{"const": "not_completed", "type": "string"}, {"const": "on_progress", "type": "string"}, {"const": "completed", "type": "string"}]}}, "required": ["completion_status"]}}, "multipart/form-data": {"schema": {"type": "object", "properties": {"completion_status": {"description": "Task completion status (not_completed, on_progress, completed)", "anyOf": [{"const": "not_completed", "type": "string"}, {"const": "on_progress", "type": "string"}, {"const": "completed", "type": "string"}]}}, "required": ["completion_status"]}}, "text/plain": {"schema": {"type": "object", "properties": {"completion_status": {"description": "Task completion status (not_completed, on_progress, completed)", "anyOf": [{"const": "not_completed", "type": "string"}, {"const": "on_progress", "type": "string"}, {"const": "completed", "type": "string"}]}}, "required": ["completion_status"]}}}}, "responses": {"200": {}}}}, "/": {"get": {"operationId": "getIndex", "responses": {"200": {}}}}}}