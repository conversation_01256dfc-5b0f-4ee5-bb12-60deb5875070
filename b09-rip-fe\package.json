{"name": "kasuat-frontend", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev -p 3001", "build": "next build", "build:prod": "next build && mkdir -p .next/standalone/.next && cp -r .next/static .next/standalone/.next/ && cp -r public .next/standalone/ && mkdir -p '.next/standalone/.next/server/app/(main)' || true", "start": "next start -p 3001", "start:prod": "PORT=3001 node .next/standalone/server.js", "lint": "next lint"}, "dependencies": {"@dnd-kit/core": "^6.3.1", "@dnd-kit/modifiers": "^9.0.0", "@hookform/resolvers": "^4.1.3", "@radix-ui/react-alert-dialog": "^1.1.11", "@radix-ui/react-avatar": "^1.1.9", "@radix-ui/react-checkbox": "^1.1.4", "@radix-ui/react-collapsible": "^1.1.3", "@radix-ui/react-context-menu": "^2.2.14", "@radix-ui/react-dialog": "^1.1.6", "@radix-ui/react-dropdown-menu": "^2.1.6", "@radix-ui/react-icons": "^1.3.2", "@radix-ui/react-label": "^2.1.2", "@radix-ui/react-popover": "^1.1.6", "@radix-ui/react-radio-group": "^1.2.3", "@radix-ui/react-select": "^2.1.6", "@radix-ui/react-separator": "^1.1.2", "@radix-ui/react-slot": "^1.1.2", "@radix-ui/react-toast": "^1.2.6", "@radix-ui/react-tooltip": "^1.1.8", "@tanstack/react-query": "^5.69.0", "@types/react-datepicker": "^7.0.0", "@uidotdev/usehooks": "^2.4.1", "axios": "^1.8.4", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^1.1.1", "critters": "^0.0.20", "date-fns": "^4.1.0", "html2canvas": "^1.4.1", "jotai": "^2.12.4", "jspdf": "^3.0.1", "jwt-decode": "^4.0.0", "lodash.throttle": "^4.1.1", "lucide-react": "^0.479.0", "next": "15.2.4", "react": "^19.0.0", "react-datepicker": "^8.3.0", "react-day-picker": "8.10.1", "react-dom": "^19.0.0", "react-hook-form": "^7.54.2", "recharts": "^2.15.3", "soner": "^1.1.0", "sonner": "^2.0.1", "tailwind-merge": "^3.0.2", "tailwindcss-animate": "^1.0.7", "zod": "^3.24.2", "zustand": "^5.0.3"}, "devDependencies": {"@eslint/eslintrc": "^3.3.1", "@playwright/test": "^1.51.1", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.2.0", "@types/lodash.throttle": "^4.1.9", "@types/node": "^20.17.27", "@types/react": "^19.0.12", "@types/react-dom": "^19.0.4", "autoprefixer": "^10.4.21", "eslint": "^9.23.0", "eslint-config-next": "15.2.4", "postcss": "^8.5.3", "prettier": "^3.5.3", "tailwindcss": "^3.4.17", "typescript": "^5.8.2", "vitest": "^3.0.9"}, "pnpm": {"onlyBuiltDependencies": ["core-js", "esbuild", "sharp"]}, "packageManager": "pnpm@10.6.5"}