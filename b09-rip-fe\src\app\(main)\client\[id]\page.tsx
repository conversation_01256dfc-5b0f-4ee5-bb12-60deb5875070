'use client';

import { RequireRole } from '@/components/auth/RequireRole';
import OrganizationDetailContent from '@/components/organization/OrganizationDetailContent';
import { useParams } from 'next/navigation';

export default function ClientDetailPage() {
  const params = useParams();
  const id = params.id as string;

  return (
    <RequireRole allowedRoles={['Operation', 'Manager']}>
      <div className="container mx-auto py-6 px-6">
        <OrganizationDetailContent id={id} />
      </div>
    </RequireRole>
  );
}
