'use client';

import { useState, useEffect } from 'react';
import { PageTitle } from '@/components/ui/PageTitle';
import { attendanceApi } from '@/lib/api/attendance';
import { PresenceStatus } from '@/types/attendance';
import Link from 'next/link';
import { Button } from '@/components/ui/button';
import { Clock } from 'lucide-react';

// Define interface for the attendance data from API
interface AttendanceData {
  id: string;
  employee_id: string;
  date: string;
  clock_in: string | null;
  clock_out?: string | null;
  presence_status: string;
  notes?: string | null;
  tasks: {
    id: string;
    description: string;
    due_date: string;
    assigned_by: string;
    completion_status: boolean;
  }[];
}

export default function DashboardPage() {
  const [attendance, setAttendance] = useState<AttendanceData | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const fetchTodayAttendance = async () => {
      try {
        const response = await attendanceApi.getTodayAttendance();
        if (response.success && response.data) {
          setAttendance(response.data as unknown as AttendanceData);
        }
      } catch {
        // If 404, it means no attendance for today - that's expected
        console.log('No attendance record found for today');
      } finally {
        setLoading(false);
      }
    };

    fetchTodayAttendance();
  }, []);

  // Helper to get status in Indonesian
  const getStatusInIndonesian = (status: string | undefined) => {
    if (!status) return 'Belum Tercatat';

    switch (status) {
      case PresenceStatus.PRESENT:
        return 'Hadir';
      case PresenceStatus.ABSENT:
        return 'Tidak Hadir';
      case PresenceStatus.PERMIT:
        return 'Izin';
      case PresenceStatus.LEAVE:
        return 'Cuti';
      default:
        return 'Belum Tercatat';
    }
  };

  return (
    <div className="container mx-auto py-6 px-6">
      <PageTitle title="Kasuat Dashboard" />
      <p className="mt-4 text-gray-600">Selamat Datang di Dashboard Kasuat.</p>

      <div className="mt-8 grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        <div className="bg-white p-6 rounded-lg shadow">
          <h2 className="text-lg font-semibold mb-2">Status Kehadiran</h2>
          <p className="text-gray-500">Informasi kehadiran hari ini</p>

          <div className="mt-4">
            {loading ? (
              <div className="flex items-center justify-center h-20">
                <p className="text-sm text-gray-500">
                  Memuat data kehadiran...
                </p>
              </div>
            ) : attendance ? (
              <>
                <div
                  className={`p-3 rounded-md ${
                    attendance.presence_status === PresenceStatus.PRESENT
                      ? 'bg-green-50 border-l-2 border-green-500'
                      : attendance.presence_status === PresenceStatus.ABSENT
                        ? 'bg-red-50 border-l-2 border-red-500'
                        : 'bg-yellow-50 border-l-2 border-yellow-500'
                  }`}
                >
                  <p
                    className={`font-medium ${
                      attendance.presence_status === PresenceStatus.PRESENT
                        ? 'text-green-700'
                        : attendance.presence_status === PresenceStatus.ABSENT
                          ? 'text-red-700'
                          : 'text-yellow-700'
                    }`}
                  >
                    {getStatusInIndonesian(attendance.presence_status)}
                  </p>
                  {attendance.clock_in &&
                    attendance.presence_status === PresenceStatus.PRESENT && (
                      <p className="text-sm mt-1">
                        Check-in: {attendance.clock_in}
                      </p>
                    )}
                </div>

                {attendance.notes && (
                  <div className="mt-3 text-sm">
                    <p className="font-medium">Catatan:</p>
                    <p className="text-gray-600">{attendance.notes}</p>
                  </div>
                )}
              </>
            ) : (
              <div className="space-y-3">
                <p className="text-sm text-gray-600">
                  Anda belum mencatat kehadiran hari ini
                </p>
                <Link href="/attendance/record">
                  <Button size="sm" className="w-full mt-2 gap-2">
                    <Clock className="h-4 w-4" />
                    Catat Kehadiran
                  </Button>
                </Link>
              </div>
            )}
          </div>
        </div>

        <div className="bg-white p-6 rounded-lg shadow">
          <h2 className="text-lg font-semibold mb-2">Aktivitas Terkini</h2>
          <p className="text-gray-500">Lihat aktivitas terbaru Anda</p>
          <div className="mt-4">
            <ul className="space-y-3">
              <li className="text-sm flex items-start">
                <span className="w-2 h-2 mt-1.5 mr-2 rounded-full bg-green-500"></span>
                <div>
                  <p className="font-medium">
                    Proyek &quot;Website Redesign&quot; diperbarui
                  </p>
                  <p className="text-xs text-gray-500">2 jam yang lalu</p>
                </div>
              </li>
              <li className="text-sm flex items-start">
                <span className="w-2 h-2 mt-1.5 mr-2 rounded-full bg-blue-500"></span>
                <div>
                  <p className="font-medium">
                    Klien baru ditambahkan: PT Sukses Makmur
                  </p>
                  <p className="text-xs text-gray-500">Kemarin</p>
                </div>
              </li>
            </ul>
          </div>
        </div>

        <div className="bg-white p-6 rounded-lg shadow">
          <h2 className="text-lg font-semibold mb-2">Pembaruan Status</h2>
          <p className="text-gray-500">Notifikasi dan peringatan sistem</p>
          <div className="mt-4">
            <div className="space-y-3">
              <div className="p-3 bg-yellow-50 border-l-2 border-yellow-500 text-sm">
                <p className="font-medium text-yellow-700">
                  Peringatan Pemeliharaan
                </p>
                <p className="text-yellow-600">
                  Pemeliharaan sistem dijadwalkan besok pukul 22:00
                </p>
              </div>
              <div className="p-3 bg-green-50 border-l-2 border-green-500 text-sm">
                <p className="font-medium text-green-700">Pembaruan Selesai</p>
                <p className="text-green-600">
                  Sistem faktur berhasil diperbarui ke v2.1
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
