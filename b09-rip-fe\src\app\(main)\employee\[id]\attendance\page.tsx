'use client';

import { useParams } from 'next/navigation';
import { RequireRole } from '@/components/auth/RequireRole';
import EmployeeAttendanceContent from '@/components/attendance/EmployeeAttendanceContent';
import { useEffect, useState } from 'react';
import { getEmployeeById } from '@/lib/api/employee';
import { toast } from 'sonner';

export default function EmployeeAttendancePage() {
  const params = useParams();
  const employeeId = params.id as string;
  const [employeeName, setEmployeeName] = useState('');
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const fetchEmployeeData = async () => {
      setLoading(true);
      try {
        const response = await getEmployeeById(employeeId);
        if (response.success && response.data) {
          setEmployeeName(response.data.profile.fullname || '');
        } else {
          toast.error('Failed to load employee details');
        }
      } catch (error) {
        console.error('Error fetching employee details:', error);
        toast.error('Failed to load employee details');
      } finally {
        setLoading(false);
      }
    };

    if (employeeId) {
      fetchEmployeeData();
    }
  }, [employeeId]);

  return (
    <RequireRole allowedRoles={['Admin', 'HR']}>
      <div className="container mx-auto py-6 px-6">
        {loading ? (
          <div className="flex justify-center items-center h-40">
            <div className="h-8 w-8 animate-spin rounded-full border-2 border-solid border-[#AB8B3B] border-t-transparent"></div>
          </div>
        ) : (
          <EmployeeAttendanceContent
            employeeId={employeeId}
            employeeName={employeeName || `Employee ${employeeId}`}
          />
        )}
      </div>
    </RequireRole>
  );
}
