'use client';

import { RequireRole } from '@/components/auth/RequireRole';
import { EmployeeEditContent } from '@/components/employee/EmployeeEditContent';
import { useParams } from 'next/navigation';

export default function EmployeeEditPage() {
  const params = useParams();
  const id = params.id as string;

  return (
    <RequireRole allowedRoles={['Admin', 'HR']}>
      <div className="container mx-auto py-6 px-6">
        <EmployeeEditContent id={id} />
      </div>
    </RequireRole>
  );
}
