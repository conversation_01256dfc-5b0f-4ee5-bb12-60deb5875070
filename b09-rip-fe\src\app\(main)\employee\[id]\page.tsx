'use client';

import { RequireRole } from '@/components/auth/RequireRole';
import { EmployeeViewContent } from '@/components/employee/EmployeeViewContent';
import { useParams } from 'next/navigation';

export default function EmployeeDetailPage() {
  const params = useParams();
  const id = params.id as string;

  return (
    <RequireRole allowedRoles={['Admin', 'HR', 'Manager']}>
      <div className="container mx-auto py-6 px-6">
        <EmployeeViewContent id={id} />
      </div>
    </RequireRole>
  );
}
