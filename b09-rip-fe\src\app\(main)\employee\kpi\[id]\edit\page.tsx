'use client';

import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { toast } from 'sonner';
import { RequireRole } from '@/components/auth/RequireRole';
import KPIForm from '@/components/kpi/KPIForm';
import { kpiApi } from '@/lib/api/kpi';
import { KPI } from '@/types/kpi';

export default function EditKPIPage({
  params,
}: {
  params: Promise<{ id: string }>;
}) {
  const router = useRouter();
  const [kpi, setKpi] = useState<KPI | null>(null);
  const [loading, setLoading] = useState(true);
  const [id, setId] = useState<string | null>(null);

  useEffect(() => {
    // Handle the params Promise
    Promise.resolve(params).then((resolvedParams) => {
      setId(resolvedParams.id);
    });
  }, [params]);

  useEffect(() => {
    if (!id) return;

    const fetchKPI = async () => {
      try {
        const response = await kpiApi.getKPIById(id);
        if (response.success && response.data) {
          setKpi(response.data);
        } else {
          toast.error('Failed to load KPI');
          router.push('/employee/kpi');
        }
      } catch (error) {
        console.error('Error fetching KPI:', error);
        toast.error('Failed to load KPI');
        router.push('/employee/kpi');
      } finally {
        setLoading(false);
      }
    };

    fetchKPI();
  }, [id, router]);

  return (
    <RequireRole allowedRoles={['HR', 'Manager']}>
      <div className="container mx-auto py-6 px-6">
        {loading ? (
          <div className="text-center">Loading...</div>
        ) : kpi ? (
          <KPIForm initialData={kpi} isEdit />
        ) : (
          <div className="text-center">KPI not found. Redirecting...</div>
        )}
      </div>
    </RequireRole>
  );
}
