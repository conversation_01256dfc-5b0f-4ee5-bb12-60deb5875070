import React from 'react';
import { use } from 'react';
import { RequireRole } from '@/components/auth/RequireRole';
import KPIDetailContent from '@/components/kpi/KPIDetailContent';

interface KPIDetailPageProps {
  params: Promise<{ id: string }>;
}

export default function KPIDetailPage({ params }: KPIDetailPageProps) {
  // Use React.use to unwrap the params Promise
  const { id } = use(params);

  return (
    <RequireRole allowedRoles={['HR', 'Manager']}>
      <div className="container mx-auto py-6 px-6">
        <KPIDetailContent id={id} />
      </div>
    </RequireRole>
  );
}
