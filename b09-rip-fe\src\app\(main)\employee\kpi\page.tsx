// path: b09-rip-fe/src/app/(main)/employee/kpi/page.tsx
'use client';

import React from 'react';
import { RequireRole } from '@/components/auth/RequireRole';
import KPIManagementContent from '@/components/kpi/KPIManagementContent';
import KPIByEmployee from '@/components/kpi/KPIByEmployee';
import { useSearchParams } from 'next/navigation';

export default function HalamanManajemenKPI() {
  const searchParams = useSearchParams();
  const employeeId = searchParams.get('employeeId');

  return (
    <RequireRole allowedRoles={['HR', 'Manager']}>
      {employeeId ? (
        // Show filtered view for specific employee
        <KPIByEmployee employeeId={employeeId} />
      ) : (
        // Show general KPI management if no employee ID specified
        <KPIManagementContent />
      )}
    </RequireRole>
  );
}
