'use client';

import React from 'react';
import { use } from 'react';
import MyKPIDetailContent from '@/components/kpi/MyKPIDetailContent';

interface MyKPIDetailPageProps {
  params: Promise<{ id: string }>;
}

export default function MyKPIDetailPage({ params }: MyKPIDetailPageProps) {
  // Use React.use to unwrap the params Promise
  const { id } = use(params);

  return (
    <div className="container mx-auto py-6 px-6">
      <MyKPIDetailContent id={id} />
    </div>
  );
}
