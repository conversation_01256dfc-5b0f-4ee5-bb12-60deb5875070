'use client';

import { useParams } from 'next/navigation';
import { RequireRole } from '@/components/auth/RequireRole';
import SalaryDetailContent from '@/components/salary/SalaryDetailContent';
import ErrorPage from '@/components/error/error-page';
import { UserRole } from '@/types/auth';

// Allowed roles for accessing salary information
const allowedRoles: UserRole[] = ['Admin', 'Manager', 'Finance', 'HR'];

export default function SalaryDetailPage() {
  const params = useParams();
  const id = params?.id as string;

  if (!id) {
    return (
      <ErrorPage
        statusCode="400"
        title="Parameter ID Tidak Valid"
        message="Maaf, ID penggajian tidak ditemukan pada URL."
        showHomeButton={true}
        showBackButton={true}
        homeHref="/"
      />
    );
  }

  return (
    <RequireRole allowedRoles={allowedRoles}>
      <div className="container mx-auto py-6 px-6">
        <SalaryDetailContent id={id} />
      </div>
    </RequireRole>
  );
}
