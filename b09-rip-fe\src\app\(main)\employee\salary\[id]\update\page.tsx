'use client';

import { useParams } from 'next/navigation';
import { RequireRole } from '@/components/auth/RequireRole';
import UpdateSalaryForm from '@/components/salary/UpdateSalaryForm';
import { UserRole } from '@/types/auth';
import ErrorPage from '@/components/error/error-page';
import useAuth from '@/hooks/auth/useAuth';

// Allowed roles for salary update
const allowedRoles: UserRole[] = ['Admin', 'Finance', 'HR'];

export default function UpdateSalaryPage() {
  const params = useParams();
  const { user } = useAuth();

  // Check if ID is missing
  if (!params.id) {
    return (
      <ErrorPage
        statusCode="400"
        title="Parameter ID Tidak Valid"
        message="Maaf, ID penggajian tidak ditemukan pada URL."
        showHomeButton={true}
        showBackButton={true}
        homeHref="/dashboard"
        homeLabel="Kembali ke Dashboard"
        backLabel="Kembali"
      />
    );
  }

  return (
    <RequireRole allowedRoles={allowedRoles}>
      <div className="container mx-auto py-6 px-6">
        <UpdateSalaryForm
          id={params.id as string}
          userRole={user?.role || ''}
        />
      </div>
    </RequireRole>
  );
}
