'use client';

import { useParams } from 'next/navigation';
import { RequireRole } from '@/components/auth/RequireRole';
import EmployeeSalaryDetailContent from '@/components/salary/EmployeeSalaryDetailContent';
import ErrorPage from '@/components/error/error-page';
import { UserRole } from '@/types/auth';

// Allowed roles for accessing salary information
const allowedRoles: UserRole[] = ['Admin', 'Manager', 'Finance', 'HR'];

export default function EmployeeSalaryDetailPage() {
  const params = useParams();
  const employeeId = params?.employeeId as string;

  if (!employeeId) {
    return (
      <ErrorPage
        statusCode="400"
        title="Parameter ID Tidak Valid"
        message="Maaf, ID karyawan tidak ditemukan pada URL."
        showHomeButton={true}
        showBackButton={true}
        homeHref="/"
      />
    );
  }

  return (
    <RequireRole allowedRoles={allowedRoles}>
      <div className="container mx-auto py-6 px-6">
        <EmployeeSalaryDetailContent employeeId={employeeId} />
      </div>
    </RequireRole>
  );
}
