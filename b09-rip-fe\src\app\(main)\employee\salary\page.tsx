'use client';

import React from 'react';
import { RequireRole } from '@/components/auth/RequireRole';
import SalaryManagementContent from '@/components/salary/SalaryManagementContent';
import { UserRole } from '@/types/auth';

// Allowed roles for accessing salary information
const allowedRoles: UserRole[] = ['Admin', 'Manager', 'Finance', 'HR'];

export default function SalaryViewPage() {
  return (
    <RequireRole allowedRoles={allowedRoles}>
      <SalaryManagementContent />
    </RequireRole>
  );
}
