import Link from 'next/link';
import { But<PERSON> } from '@/components/ui/button';
import { AlertCircle } from 'lucide-react';

export default function FakturNotFound() {
  return (
    <div className="container flex h-[70vh] flex-col items-center justify-center space-y-4 text-center">
      <div className="rounded-full bg-muted p-4">
        <AlertCircle className="h-10 w-10 text-muted-foreground" />
      </div>
      <h1 className="text-3xl font-bold tracking-tighter sm:text-4xl">
        Faktur Tidak Ditemukan
      </h1>
      <p className="max-w-[600px] text-muted-foreground">
        Faktur yang Anda cari tidak ada atau telah dihapus.
      </p>
      <div className="flex gap-2">
        <Link href="/invoice">
          <Button>Kembali ke Daftar Faktur</Button>
        </Link>
        <Link href="/invoice/create">
          <Button variant="outline">Buat Faktur Baru</Button>
        </Link>
      </div>
    </div>
  );
}
