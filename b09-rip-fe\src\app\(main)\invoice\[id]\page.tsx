'use client';

import React from 'react';
import { InvoiceDetailClientContent } from '@/components/invoice/InvoiceDetailClientContent';
import { PrintStyles } from '@/components/invoice/PrintStyles';
import { RequireRole } from '@/components/auth/RequireRole';
import { useParams } from 'next/navigation';

export default function FakturDetailPage() {
  // Use the useParams hook to get the id parameter
  const params = useParams();
  const id = params.id as string;

  return (
    <RequireRole allowedRoles={['Finance']}>
      <main className="container mx-auto py-6 px-6 print:p-0 print:m-0">
        {/* This outer div is important for scoping the content when generating PDFs */}
        <div className="print:hidden">
          {/* This content will be hidden during printing and PDF generation */}
          <div className="max-w-[1200px] mx-auto">
            <InvoiceDetailClientContent id={id} />
          </div>
        </div>

        {/* This container will only be visible when printing/generating PDF */}
        <div
          id="invoice-container"
          className="hidden print:block max-w-full mx-auto print:p-0 print:m-0 print:max-w-full print:bg-white"
          style={{ pageBreakInside: 'avoid' }}
        >
          <InvoiceDetailClientContent id={id} />
        </div>

        {/* Include print styles via client component */}
        <PrintStyles />
      </main>
    </RequireRole>
  );
}
