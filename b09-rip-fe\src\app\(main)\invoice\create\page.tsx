'use client';

import { useEffect, useState } from 'react';
import { useRouter } from 'next/navigation';
import { InvoiceForm } from '@/components/invoice/InvoiceForm';
import { PageTitle } from '@/components/ui/PageTitle';
import { BackButton } from '@/components/ui/BackButton';
import { RequireRole } from '@/components/auth/RequireRole';

export default function CreateInvoicePage() {
  const router = useRouter();
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    // Simulate loading state
    const timer = setTimeout(() => {
      setIsLoading(false);
    }, 500);

    return () => clearTimeout(timer);
  }, []);

  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900"></div>
      </div>
    );
  }

  return (
    <RequireRole allowedRoles={['Finance']}>
      <div className="container mx-auto px-4 py-8">
        <div className="flex items-center gap-4 mb-6">
          <BackButton onClick={() => router.push('/invoice')} />
          <PageTitle
            title="Buat Faktur"
            subtitle="Buat faktur baru untuk klien atau internal"
          />
        </div>
        <InvoiceForm />
      </div>
    </RequireRole>
  );
}
