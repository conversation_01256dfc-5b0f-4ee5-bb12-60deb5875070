'use client';

import React, { useState } from 'react';
import { toast } from 'sonner';
import { useRouter } from 'next/navigation';
import { Button } from '@/components/ui/button';
import { Plus } from 'lucide-react';
import { InvoiceTable } from '@/components/invoice/InvoiceTable';
import { InvoiceFilters } from '@/components/invoice/InvoiceFilters';
import { DeleteInvoiceDialog } from '@/components/invoice/DeleteInvoiceDialog';
import { useInvoiceManagement } from '@/hooks/useInvoiceManagement';
import { Invoice } from '@/types/invoice';
import { invoiceApi } from '@/lib/api/invoice';
import { RequireRole } from '@/components/auth/RequireRole';
import { PageTitle } from '@/components/ui/PageTitle';

const FakturListPage = () => {
  const router = useRouter();
  const [deleteInvoice, setDeleteInvoice] = useState<Invoice | null>(null);
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [isDeleting, setIsDeleting] = useState(false);

  const {
    invoices,
    loading,
    search,
    paymentStatus,
    sortField,
    sortDirection,
    handleViewInvoice,
    handleEditInvoice,
    handleSearchChange,
    handleStatusChange,
    handleSort,
  } = useInvoiceManagement();

  // Handle delete actions
  const handleDeleteClick = (invoice: Invoice) => {
    setDeleteInvoice(invoice);
    setDeleteDialogOpen(true);
  };

  const handleDeleteConfirm = async () => {
    if (!deleteInvoice) return;

    setIsDeleting(true);
    try {
      const response = await invoiceApi.deleteInvoice(deleteInvoice.id);

      if (response.success) {
        toast.success(`Faktur ${deleteInvoice.invoice_number} telah dihapus.`);
        // Force reload to refresh the data
        window.location.reload();
      } else {
        toast.error(response.message || 'Gagal menghapus faktur');
      }
    } catch (error: unknown) {
      toast.error(
        error instanceof Error
          ? error.message
          : 'Terjadi kesalahan saat menghapus faktur'
      );
    } finally {
      setIsDeleting(false);
      setDeleteDialogOpen(false);
      setDeleteInvoice(null);
    }
  };

  const handleCreateInvoice = () => {
    router.push('/invoice/create');
  };

  return (
    <div className="container mx-auto py-6 px-6">
      <div className="flex items-center justify-between mb-6">
        <PageTitle
          title="Manajemen Faktur"
          subtitle="Kelola dan lihat semua faktur"
        />
        <Button
          onClick={handleCreateInvoice}
          leftIcon={<Plus className="h-4 w-4" />}
        >
          Buat Faktur
        </Button>
      </div>

      <div className="bg-white rounded-lg shadow p-6">
        <div className="mb-6">
          <InvoiceFilters
            search={search}
            payment_status={paymentStatus}
            onSearchChange={handleSearchChange}
            onStatusChange={handleStatusChange}
          />
        </div>

        <div className="overflow-x-auto mb-4">
          <InvoiceTable
            invoices={invoices}
            isLoading={loading}
            onView={handleViewInvoice}
            onEdit={handleEditInvoice}
            onDelete={handleDeleteClick}
            onSort={handleSort}
            sortField={sortField}
            sortDirection={sortDirection}
            // itemsPerPage prop removed as it's not used in DataTable anymore
            // itemsPerPage={10}
          />
        </div>
      </div>

      <DeleteInvoiceDialog
        invoice={deleteInvoice}
        open={deleteDialogOpen}
        onOpenChange={setDeleteDialogOpen}
        onConfirm={handleDeleteConfirm}
        isDeleting={isDeleting}
      />
    </div>
  );
};

export default function FakturPage() {
  return (
    <RequireRole allowedRoles={['Finance']}>
      <FakturListPage />
    </RequireRole>
  );
}
