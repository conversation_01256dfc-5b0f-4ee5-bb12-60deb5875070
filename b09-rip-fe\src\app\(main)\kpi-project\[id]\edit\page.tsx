'use client';

import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { toast } from 'sonner';
import { RequireRole } from '@/components/auth/RequireRole';
//import KpiProjectForm from '@/components/kpi-project/KpiProjectForm'; // Pastikan form ini sudah disiapkan untuk edit
import { kpiProjectApi } from '@/lib/api/kpi-project';
import { KpiProject } from '@/types/kpi-project';
import KpiProjectForm from '@/components/kpi-project/KpiProjectForm';

export default function EditKpiProjectPage({
  params,
}: {
  params: Promise<{ id: string }>;
}) {
  const router = useRouter();
  const [kpiProject, setKpiProject] = useState<KpiProject | null>(null);
  const [loading, setLoading] = useState(true);
  const [id, setId] = useState<string | null>(null);

  useEffect(() => {
    // Handle the params Promise
    Promise.resolve(params).then((resolvedParams) => {
      console.log('Resolved params:', resolvedParams);
      setId(resolvedParams.id);
    }).catch(error => {
      console.error('Error resolving params:', error);
      toast.error('Failed to get KPI Project ID');
      router.push('/kpi-project');
    });
  }, [params, router]);

  useEffect(() => {
    if (!id) return;

    const fetchKpiProject = async () => {
      try {
        console.log('Fetching KPI Project with ID:', id);
        const response = await kpiProjectApi.getKpiProjectById(id);
        console.log('KPI Project response:', response);

        if (response && response.success && response.data) {
          console.log('Setting KPI Project data:', response.data);
          setKpiProject(response.data);
        } else {
          console.error('Failed to load KPI Project:', response);
          toast.error('Failed to load KPI Project');
          router.push('/kpi-project');
        }
      } catch (error) {
        console.error('Error fetching KPI Project:', error);
        toast.error('Failed to load KPI Project');
        router.push('/kpi-project');
      } finally {
        setLoading(false);
      }
    };

    fetchKpiProject();
  }, [id, router]);

  return (
    <RequireRole allowedRoles={['Operation', 'Manager']}>
      <div className="container mx-auto py-6 px-6">
        {loading ? (
          <div className="text-center">Loading...</div>
        ) : kpiProject ? (
          <KpiProjectForm initialData={kpiProject} isEdit /> // Pass the existing data for edit mode
        ) : (
          <div className="text-center">KPI Project not found. Redirecting...</div>
        )}
      </div>
    </RequireRole>
  );
}
