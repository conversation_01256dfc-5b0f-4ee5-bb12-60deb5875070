'use client';

import React from 'react';
import { use } from 'react';
import { RequireRole } from '@/components/auth/RequireRole';
import KpiProjectDetailContent from '@/components/kpi-project/KpiProjectDetailContent';
import KpiPerProjectDetail from '@/components/kpi-project/KpiPerProjectDetail';
import { useSearchParams } from 'next/navigation';

interface KpiProjectDetailPageProps {
  params: Promise<{ id: string }>;
}

export default function KpiProjectDetailPage({
  params,
}: KpiProjectDetailPageProps) {
  // Use React.use to unwrap the params Promise
  const { id } = use(params);
  const searchParams = useSearchParams();
  const fromProjectView = searchParams.has('fromProject');
  const projectId = searchParams.get('projectId');

  return (
    <RequireRole
      allowedRoles={[
        'Operation',
        'Manager',
        'Client',
        'Finance',
        'HR',
        'Admin',
      ]}
    >
      <div className="container mx-auto py-6 px-6">
        {fromProjectView ? (
          <KpiPerProjectDetail
            id={id}
            fromProjectView={fromProjectView}
            projectId={projectId}
          />
        ) : (
          <KpiProjectDetailContent id={id} />
        )}
      </div>
    </RequireRole>
  );
}
