'use client';

import React from 'react';
import { RequireRole } from '@/components/auth/RequireRole';
import KpiProjectContent from '@/components/kpi-project/KpiProjectContent';
import KPIByProject from '@/components/kpi-project/KPIByProject';
import { useSearchParams } from 'next/navigation';

export default function HalamanManajemenKpiProject() {
  const searchParams = useSearchParams();
  const projectId = searchParams.get('projectId');

  return (
    <RequireRole
      allowedRoles={[
        'Operation',
        'Manager',
        // 'Client',
        // 'Finance',
        // 'HR',
        'Admin',
      ]}
    >
      {projectId ? (
        // Show filtered view for specific project
        <KPIByProject projectId={projectId} />
      ) : (
        // Show general KPI project management if no project ID specified
        <KpiProjectContent />
      )}
    </RequireRole>
  );
}
