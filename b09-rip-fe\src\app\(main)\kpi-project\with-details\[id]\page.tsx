'use client';

import React from 'react';
import { use } from 'react';
import { RequireRole } from '@/components/auth/RequireRole';
import KpiPerProjectDetail from '@/components/kpi-project/KpiPerProjectDetail';

interface KpiPerProjectDetailPageProps {
  params: Promise<{ id: string }>;
}

export default function KpiPerProjectDetailPage({
  params,
}: KpiPerProjectDetailPageProps) {
  // Use React.use to unwrap the params Promise
  const { id } = use(params);

  return (
    <RequireRole
      allowedRoles={[
        'Operation',
        'Manager',
        'Client',
        'Finance',
        'HR',
        'Admin',
      ]}
    >
      <div className="container mx-auto py-6 px-6">
        <KpiPerProjectDetail id={id} />
      </div>
    </RequireRole>
  );
}
