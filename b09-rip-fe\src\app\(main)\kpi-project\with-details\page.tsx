'use client';

import React from 'react';
import { RequireRole } from '@/components/auth/RequireRole';
import KpiPerProject from '@/components/kpi-project/KpiPerProject';
import { useSearchParams } from 'next/navigation';

export default function KpiProjectWithDetailsPage() {
  const searchParams = useSearchParams();
  const projectId = searchParams.get('projectId');

  return (
    <RequireRole allowedRoles={['Operation', 'Manager', 'Client','Finance', 'HR', 'Admin' ]}>
      <KpiPerProject projectId={projectId || undefined} />
    </RequireRole>
  );
}
