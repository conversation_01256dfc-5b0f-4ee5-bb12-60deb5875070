'use client';

import { use } from 'react';
import { RequireRole } from '@/components/auth/RequireRole';
import { ProjectCharterCreate } from '@/components/project-charter/ProjectCharterCreate';

interface ProjectCharterCreatePageProps {
  params: Promise<{
    id: string;
  }>;
}

export default function ProjectCharterCreatePage({ params }: ProjectCharterCreatePageProps) {
  const { id } = use(params);
  return (
    <RequireRole allowedRoles={['Operation', 'Manager']}>
      <ProjectCharterCreate projectId={id} />
    </RequireRole>
  );
}
