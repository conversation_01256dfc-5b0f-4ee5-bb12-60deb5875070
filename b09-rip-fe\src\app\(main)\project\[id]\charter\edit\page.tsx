'use client';

import { use } from 'react';
import { RequireRole } from '@/components/auth/RequireRole';
import { ProjectCharterEdit } from '@/components/project-charter/ProjectCharterEdit';

interface ProjectCharterEditPageProps {
  params: Promise<{
    id: string;
  }>;
}

export default function ProjectCharterEditPage({ params }: ProjectCharterEditPageProps) {
  const { id } = use(params);
  return (
    <RequireRole allowedRoles={['Operation', 'Manager']}>
      <ProjectCharterEdit projectId={id} />
    </RequireRole>
  );
}
