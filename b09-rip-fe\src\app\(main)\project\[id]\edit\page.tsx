'use client';

import { use } from 'react';
import { RequireRole } from '@/components/auth/RequireRole';
import { EditProjectForm } from '@/components/project/EditProjectForm';

interface EditProjectPageProps {
  params: Promise<{
    id: string;
  }>;
}

export default function EditProjectPage({ params }: EditProjectPageProps) {
  const { id } = use(params);
  return (
    <RequireRole allowedRoles={['Operation', 'Manager']}>
      <EditProjectForm id={id} />
    </RequireRole>
  );
}
