'use client';

import { useEffect, useState, use } from 'react';
import { WeeklyLogList } from '@/components/weekly-log/WeeklyLogList';
import { useToast } from '@/components/ui/use-toast';
import { projectApi } from '@/lib/api/project';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';

interface ProjectWeeklyLogPageProps {
  params: Promise<{
    id: string;
  }>;
}

export default function ProjectWeeklyLogPage({
  params,
}: ProjectWeeklyLogPageProps) {
  const { id } = use(params);
  const [projectStatus, setProjectStatus] = useState<string>('');
  const [loading, setLoading] = useState(true);
  const { toast } = useToast();

  useEffect(() => {
    const fetchProjectStatus = async () => {
      try {
        setLoading(true);
        // Validate that id is a UUID
        if (
          !/^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i.test(
            id
          )
        ) {
          throw new Error('Invalid project ID format');
        }
        const response = await projectApi.getProjectById(id);
        if (response.success && response.data) {
          setProjectStatus(response.data.status_project);
        }
      } catch (error) {
        console.error('Error fetching project status:', error);
        toast({
          title: 'Error',
          description:
            error instanceof Error
              ? error.message
              : 'Failed to fetch project status',
          variant: 'destructive',
        });
      } finally {
        setLoading(false);
      }
    };

    fetchProjectStatus();
  }, [id, toast]);

  if (loading) {
    return (
      <div className="container mx-auto py-6">
        <Card>
          <CardHeader>
            <CardTitle>Weekly Logs</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-center py-8">
              Loading project information...
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="container mx-auto py-6">
      <WeeklyLogList projectId={id} projectStatus={projectStatus} />
    </div>
  );
}
