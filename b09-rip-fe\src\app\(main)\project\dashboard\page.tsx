'use client';

import { RequireRole } from '@/components/auth/RequireRole';
import { ProjectsDashboardContent } from '@/components/project/ProjectsDashboardContent';

export default function ProjectsDashboardPage() {
  return (
    <RequireRole
      allowedRoles={[
        'Operation',
        'Manager',
        'Client',
        'Finance',
        'HR',
        'Admin',
      ]}
    >
      <ProjectsDashboardContent />
    </RequireRole>
  );
}
