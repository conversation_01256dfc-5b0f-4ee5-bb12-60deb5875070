'use client';

import React from 'react';
import { RequireRole } from '@/components/auth/RequireRole';
import ProjectTaskDetail from '@/components/project-task/ProjectTaskDetail';

interface ProjectTaskDetailPageProps {
  params: Promise<{
    id: string;
  }>;
}

export default function ProjectTaskDetailPage({
  params,
}: ProjectTaskDetailPageProps) {
  // Unwrap params with React.use() to fix the Next.js warning
  const { id } = React.use(params);

  return (
    <RequireRole allowedRoles={['Admin', 'Operation', 'Manager']}>
      <ProjectTaskDetail taskId={id} />
    </RequireRole>
  );
}
