'use client';

import ProjectTaskManagementContent from '@/components/project-task/ProjectTaskManagementContent';
import ProjectTasksByProject from '@/components/project-task/ProjectTaskByProject';
import { useSearchParams } from 'next/navigation';

export default function ProjectTaskPage() {
  const searchParams = useSearchParams();
  const projectId = searchParams.get('projectId');

  return (
    <div className="container mx-auto py-6 px-6">
      {projectId ? (
        // Show filtered view for specific project
        <ProjectTasksByProject projectId={projectId} />
      ) : (
        // Show general task management if no project ID specified
        <ProjectTaskManagementContent />
      )}
    </div>
  );
}
