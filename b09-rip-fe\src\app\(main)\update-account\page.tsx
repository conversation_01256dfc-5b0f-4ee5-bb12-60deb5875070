'use client';

import { useEffect, useState } from 'react';
import { ProtectedRoute } from '@/components/auth/ProtectedRoute';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { useAuthStore } from '@/lib/store/auth-store';
import { useRouter } from 'next/navigation';
import { AccountUpdateContent } from '@/components/account/AccountUpdateContent';

export default function UpdateAccountPage() {
  const user = useAuthStore((state) => state.user);
  const router = useRouter();
  const [employeeId, setEmployeeId] = useState<string | null>(null);

  useEffect(() => {
    // Check if user has an employee_id
    if (user?.employee_id) {
      setEmployeeId(user.employee_id);
    }
  }, [user]);

  return (
    <ProtectedRoute>
      <div className="container mx-auto py-6 px-6">
        {employeeId ? (
          <AccountUpdateContent employeeId={employeeId} />
        ) : (
          <Card>
            <CardContent className="pt-6">
              <div className="flex flex-col items-center justify-center p-8 text-center">
                <h2 className="text-xl font-semibold mb-2">
                  Akun Belum Terhubung
                </h2>
                <p className="text-gray-500 mb-4">
                  Akun Anda belum terhubung dengan profil karyawan. Silakan
                  hubungi administrator Anda.
                </p>
                <Button onClick={() => router.push('/dashboard')}>
                  Kembali ke Dashboard
                </Button>
              </div>
            </CardContent>
          </Card>
        )}
      </div>
    </ProtectedRoute>
  );
}
