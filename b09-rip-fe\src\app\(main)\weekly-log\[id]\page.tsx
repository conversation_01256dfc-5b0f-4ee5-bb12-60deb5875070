'use client';

import { use } from 'react';
import { WeeklyLogDetail } from '@/components/weekly-log/WeeklyLogDetail';

interface WeeklyLogDetailPageProps {
  params: Promise<{
    id: string;
  }>;
}

export default function WeeklyLogDetailPage({
  params,
}: WeeklyLogDetailPageProps) {
  const { id } = use(params);
  return (
    <div className="container mx-auto py-6">
      <WeeklyLogDetail weeklyLogId={id} />
    </div>
  );
}
