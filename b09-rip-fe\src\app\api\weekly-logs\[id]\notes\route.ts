import { NextRequest, NextResponse } from 'next/server';
import { getAuthToken } from '@/lib/auth/server-auth';

export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  const resolvedParams = await params;
  try {
    // Get the weekly log ID from the URL params
    const weeklyLogId = resolvedParams.id;

    // Get the auth token
    const token = await getAuthToken();

    if (!token) {
      return NextResponse.json(
        { success: false, message: 'Authentication required' },
        { status: 401 }
      );
    }

    // Parse the request body
    const body = await request.json();

    // Validate the request body
    if (!body.notes || typeof body.notes !== 'object') {
      return NextResponse.json(
        { success: false, message: 'Notes data is required' },
        { status: 400 }
      );
    }

    // Make the API request to the backend
    const response = await fetch(
      `${process.env.NEXT_PUBLIC_API_URL}/v1/weekly-logs/${weeklyLogId}/notes`,
      {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
          Authorization: `Bearer ${token}`,
        },
        body: JSON.stringify({ notes: body.notes }),
      }
    );

    // Parse the response
    const data = await response.json();

    // Return the response
    if (response.ok) {
      return NextResponse.json({
        success: true,
        data: data.data,
        message: data.message || 'Notes updated successfully',
      });
    } else {
      return NextResponse.json(
        {
          success: false,
          message: data.message || 'Failed to update notes',
        },
        { status: response.status }
      );
    }
  } catch (error: unknown) {
    console.error('Error updating weekly log notes:', error);
    const errorMessage =
      error instanceof Error ? error.message : 'An unexpected error occurred';
    return NextResponse.json(
      {
        success: false,
        message: errorMessage,
      },
      { status: 500 }
    );
  }
}
