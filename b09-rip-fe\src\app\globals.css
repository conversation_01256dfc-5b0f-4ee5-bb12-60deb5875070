@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 0 0% 100%;

    --foreground: 0 0% 3.9%;

    --card: 0 0% 100%;

    --card-foreground: 0 0% 3.9%;

    --popover: 0 0% 100%;

    --popover-foreground: 0 0% 3.9%;

    --primary: 43 50% 53%;

    --primary-foreground: 0 0% 100%;

    --secondary: 48 71% 91%;

    --secondary-foreground: 43 50% 30%;

    --muted: 0 0% 96.1%;

    --muted-foreground: 0 0% 45.1%;

    --accent: 0 0% 96.1%;

    --accent-foreground: 0 0% 9%;

    --destructive: 0 70% 50%;

    --destructive-foreground: 0 0% 100%;

    --border: 0 0% 89.8%;

    --input: 0 0% 89.8%;

    --ring: 43 50% 53%;

    --chart-1: 12 76% 61%;

    --chart-2: 173 58% 39%;

    --chart-3: 197 37% 24%;

    --chart-4: 43 74% 66%;

    --chart-5: 27 87% 67%;

    --radius: 0.5rem;
  }
  .dark {
    --background: 0 0% 3.9%;

    --foreground: 0 0% 98%;

    --card: 0 0% 3.9%;

    --card-foreground: 0 0% 98%;

    --popover: 0 0% 3.9%;

    --popover-foreground: 0 0% 98%;

    --primary: 0 0% 98%;

    --primary-foreground: 0 0% 9%;

    --secondary: 0 0% 14.9%;

    --secondary-foreground: 0 0% 98%;

    --muted: 0 0% 14.9%;

    --muted-foreground: 0 0% 63.9%;

    --accent: 0 0% 14.9%;

    --accent-foreground: 0 0% 98%;

    --destructive: 0 62.8% 30.6%;

    --destructive-foreground: 0 0% 98%;

    --border: 0 0% 14.9%;

    --input: 0 0% 14.9%;

    --ring: 0 0% 83.1%;

    --chart-1: 220 70% 50%;

    --chart-2: 160 60% 45%;

    --chart-3: 30 80% 55%;

    --chart-4: 280 65% 60%;

    --chart-5: 340 75% 55%;
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
  }
}

/* Print styles for invoices */
@media print {
  @page {
    size: A4 portrait;
    margin: 1.5cm;
  }

  body {
    background-color: white !important;
    color: black !important;
  }

  /* Hide navigation, buttons, and UI elements */
  nav,
  header,
  footer,
  button,
  .no-print,
  [role='dialog'] {
    display: none !important;
  }

  /* Remove shadows and borders for a cleaner look */
  * {
    box-shadow: none !important;
    text-shadow: none !important;
    border-radius: 0 !important;
  }

  /* Improve text contrast */
  p,
  h1,
  h2,
  h3,
  h4,
  h5,
  h6,
  span,
  td,
  th {
    color: black !important;
  }

  /* Ensure tables display properly */
  table {
    page-break-inside: avoid;
    width: 100% !important;
  }

  /* Format the invoice container */
  #invoice-container {
    margin: 0 !important;
    padding: 0 !important;
    width: 100% !important;
  }
}

/* Additional styles for PDF generation */
body.printing-pdf {
  background-color: white !important;
}

body.printing-pdf .print\:hidden {
  display: none !important;
}

body.printing-pdf .print\:block {
  display: block !important;
}

body.printing-pdf .print\:shadow-none {
  box-shadow: none !important;
}

body.printing-pdf .print\:border-none {
  border: none !important;
}

body.printing-pdf .print\:bg-transparent {
  background-color: transparent !important;
}

body.printing-pdf #invoice-container {
  margin: 0 !important;
  padding: 0 !important;
  width: 100% !important;
}

body.printing-pdf p,
body.printing-pdf h1,
body.printing-pdf h2,
body.printing-pdf h3,
body.printing-pdf h4,
body.printing-pdf h5,
body.printing-pdf h6,
body.printing-pdf span,
body.printing-pdf td,
body.printing-pdf th {
  color: black !important;
}

/* Custom animations for landing page */
@keyframes fade-in {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slide-up {
  from {
    opacity: 0;
    transform: translateY(40px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes spin-slow {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

.animate-fade-in {
  animation: fade-in 0.8s ease-out;
}

.animate-slide-up {
  animation: slide-up 0.8s ease-out;
}

.animate-spin-slow {
  animation: spin-slow 20s linear infinite;
}

.delay-200 {
  animation-delay: 0.2s;
}

.delay-400 {
  animation-delay: 0.4s;
}

.delay-500 {
  animation-delay: 0.5s;
}

.delay-600 {
  animation-delay: 0.6s;
}

.delay-1000 {
  animation-delay: 1s;
}
