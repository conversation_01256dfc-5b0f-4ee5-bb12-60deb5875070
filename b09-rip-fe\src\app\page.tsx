'use client';

import { useEffect, useState } from 'react';
import { useRouter } from 'next/navigation';
import useAuth from '@/hooks/auth/useAuth';
import LandingPage from '@/components/landing/LandingPage';

/**
 * Root page that serves as the entry point
 * Shows landing page for unauthenticated users, redirects authenticated users to dashboard
 */
export default function RootPage() {
  const router = useRouter();
  const { checkAuth } = useAuth();
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    const handleAuthCheck = async () => {
      try {
        // This will validate token, refresh if needed, and return auth status
        const authenticated = await checkAuth();

        if (authenticated) {
          // Valid authentication, redirect to dashboard
          router.push('/dashboard');
        } else {
          // Not authenticated, show landing page
          // No action needed, will show landing page
        }
      } catch (error) {
        console.error('Auth check error:', error);
        // On error, show landing page
        // No action needed, will show landing page
      } finally {
        setIsLoading(false);
      }
    };

    handleAuthCheck();
  }, [router, checkAuth]);

  // Loading indicator while checking auth
  if (isLoading) {
    return (
      <div className="min-h-screen flex flex-col items-center justify-center bg-gray-900">
        <div className="text-center">
          <div className="h-8 w-8 animate-spin rounded-full border-2 border-solid border-[#B78F38] border-t-transparent"></div>
          <p className="mt-4 text-gray-300">Loading...</p>
        </div>
      </div>
    );
  }

  // Show landing page for unauthenticated users
  return <LandingPage />;
}
