'use client';

import { useState, useEffect, ReactNode } from 'react';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { Toaster } from 'sonner';
import { useAuth } from '@/hooks/auth/useAuth';
import TokenSynchronizer from '@/components/auth/TokenSynchronizer';

// Create a client
const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      staleTime: 5 * 60 * 1000, // 5 minutes
      gcTime: 10 * 60 * 1000, // 10 minutes (previously cacheTime)
      retry: 1,
      refetchOnWindowFocus: false,
    },
  },
});

interface AuthProviderProps {
  children: ReactNode;
}

const AuthProvider = ({ children }: AuthProviderProps) => {
  const { checkAuth } = useAuth();
  const [isChecking, setIsChecking] = useState(true);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    const initAuth = async () => {
      try {
        await checkAuth();
      } finally {
        setIsChecking(false);
        // Add a small delay to ensure styles are loaded
        setTimeout(() => setIsLoading(false), 100);
      }
    };

    initAuth();
  }, [checkAuth]);

  if (isChecking || isLoading) {
    return (
      <div className="fixed inset-0 flex items-center justify-center bg-white/80 z-50">
        <div className="text-center">
          <div className="h-8 w-8 animate-spin rounded-full border-2 border-solid border-[#B78F38] border-t-transparent"></div>
          <p className="mt-4 text-gray-600">Memuat...</p>
        </div>
      </div>
    );
  }

  return (
    <>
      {/* Include TokenSynchronizer to keep localStorage and cookies in sync */}
      <TokenSynchronizer />
      {children}
    </>
  );
};

export function Providers({ children }: { children: ReactNode }) {
  return (
    <QueryClientProvider client={queryClient}>
      <AuthProvider>
        {children}
        <Toaster
          richColors
          position="top-right"
          closeButton
          offset={{ top: '80px' }}
          toastOptions={{
            duration: 5000,
          }}
        />
      </AuthProvider>
    </QueryClientProvider>
  );
}

export default Providers;
