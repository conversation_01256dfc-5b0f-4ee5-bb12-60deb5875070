'use client';

import { useRouter } from 'next/navigation';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { UpdateEmployeeDto } from '@/types/employee';
import { EmployeeInformationCard } from '../employee/EmployeeInformationCard';
import { Save } from 'lucide-react';
import { useEmployeeDetail } from '@/hooks/useEmployeeDetail';
import { Skeleton } from '@/components/ui/skeleton';
import { toast } from 'sonner';
import { useAuthStore } from '@/lib/store/auth-store';
import { authApi } from '@/lib/api/auth';
import { PageTitle } from '@/components/ui/PageTitle';
import { BackButton } from '@/components/ui/BackButton';

interface AccountUpdateContentProps {
  employeeId: string;
}

export function AccountUpdateContent({
  employeeId,
}: AccountUpdateContentProps) {
  const router = useRouter();
  const { employee, loading, updating, setEmployee, handleSaveChanges } =
    useEmployeeDetail(employeeId);
  const { updateUserProfile } = useAuthStore();

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!employee) return;

    // Validation
    if (!employee.dob) {
      toast.error('Tanggal lahir wajib diisi');
      return;
    }
    if (!employee.address) {
      toast.error('Alamat wajib diisi');
      return;
    }
    if (!employee.bank_account) {
      toast.error('Nomor rekening wajib diisi');
      return;
    }
    if (!employee.bank_name) {
      toast.error('Nama bank wajib diisi');
      return;
    }

    const updateData: UpdateEmployeeDto = {
      dob: employee.dob,
      address: employee.address,
      bank_account: employee.bank_account,
      bank_name: employee.bank_name,
      // Don't allow users to change their department, status, etc.
      // Only include personal information fields
    };

    const success = await handleSaveChanges(updateData);
    if (success) {
      // Update the profile completion status in auth store
      const profileStatusUpdated = await authApi.getProfile();

      if (profileStatusUpdated?.data.profileComplete === true) {
        // Update the user profile in auth store with the new profile data
        if (profileStatusUpdated.success) {
          // Extract just the needed profile data
          const updatedProfileData = {
            profileComplete: profileStatusUpdated.data.profileComplete,
            // Add any other fields that need to be updated
            ...profileStatusUpdated.data.profile,
          };

          // Update the auth store with just the profile data
          updateUserProfile(updatedProfileData);

          // Clear the profile incomplete flag from session storage
          if (typeof window !== 'undefined') {
            sessionStorage.removeItem('profile-toast-shown');
          }
        }

        toast.success(
          'Informasi Anda berhasil diperbarui dan profil telah dilengkapi'
        );
      } else {
        toast.success('Informasi Anda berhasil diperbarui');
      }

      // Redirect to dashboard after successful update
      setTimeout(() => {
        router.push('/dashboard');
      }, 1500);
    }
  };

  if (loading) {
    return (
      <div className="space-y-6">
        <div className="flex justify-between items-center">
          <Skeleton className="h-8 w-48" />
          <Skeleton className="h-10 w-24" />
        </div>
        <Card>
          <CardHeader>
            <Skeleton className="h-6 w-32" />
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="grid gap-4 md:grid-cols-2">
                {[1, 2, 3, 4].map((i) => (
                  <div key={i} className="space-y-2">
                    <Skeleton className="h-4 w-24" />
                    <Skeleton className="h-10 w-full" />
                  </div>
                ))}
              </div>
              <div className="grid gap-4 md:grid-cols-2">
                {[1, 2, 3, 4].map((i) => (
                  <div key={i} className="space-y-2">
                    <Skeleton className="h-4 w-24" />
                    <Skeleton className="h-10 w-full" />
                  </div>
                ))}
              </div>
              <div className="flex justify-end space-x-2">
                <Skeleton className="h-10 w-24" />
                <Skeleton className="h-10 w-32" />
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  if (!employee) {
    return (
      <div className="flex flex-col items-center justify-center p-8">
        <h2 className="text-xl font-semibold mb-2">
          Informasi karyawan tidak ditemukan
        </h2>
        <p className="text-gray-500 mb-4">
          Kami tidak dapat menemukan informasi karyawan Anda. Silakan hubungi HR
          atau administrator Anda.
        </p>
        <Button onClick={() => router.push('/dashboard')}>
          Kembali ke Dashboard
        </Button>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center gap-4">
        <BackButton onClick={() => router.push('/dashboard')} />
        <PageTitle title="Perbarui Informasi Saya" />
      </div>

      <Card>
        <CardHeader>
          <CardTitle>Informasi Profil Saya</CardTitle>
        </CardHeader>
        <CardContent>
          <form onSubmit={handleSubmit} className="space-y-4">
            <EmployeeInformationCard
              employee={employee}
              onEmployeeChange={setEmployee}
              readOnlyFields={[
                'fullname',
                'email',
                'phonenum',
                'role',
                'department',
                'employment_status',
                'presence_status',
                'start_date',
              ]}
            />
            <div className="flex justify-end space-x-2">
              <Button
                type="button"
                variant="outline"
                onClick={() => router.push('/dashboard')}
              >
                Batal
              </Button>
              <Button type="submit" disabled={updating}>
                <Save className="h-4 w-4 mr-2" />
                {updating ? 'Memperbarui...' : 'Simpan Perubahan'}
              </Button>
            </div>
          </form>
        </CardContent>
      </Card>
    </div>
  );
}
