import React from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { CheckIcon, TrashIcon, XIcon } from 'lucide-react';

interface BulkActionBarProps {
  selectedCount: number;
  onActivate: () => void;
  onDelete: () => void;
  onCancel: () => void;
  showActivate: boolean;
}

const BulkActionBar: React.FC<BulkActionBarProps> = ({
  selectedCount,
  onActivate,
  onDelete,
  onCancel,
  showActivate,
}) => {
  if (selectedCount === 0) return null;

  return (
    <div className="bg-gray-50 border-t border-gray-200 p-3 rounded-b-lg mt-2">
      <div className="flex items-center justify-between">
        <div className="text-gray-700 font-medium">
          {selectedCount} user{selectedCount !== 1 ? 's' : ''} dipilih
        </div>
        <div className="flex space-x-3">
          {showActivate && (
            <Button onClick={onActivate} variant="success" size="sm">
              <CheckIcon className="h-4 w-4 mr-2" />
              Aktivasi ({selectedCount})
            </Button>
          )}
          <Button onClick={onDelete} variant="destructive" size="sm">
            <TrashIcon className="h-4 w-4 mr-2" />
            Hapus ({selectedCount})
          </Button>
          <Button onClick={onCancel} variant="outline" size="sm">
            <XIcon className="h-4 w-4 mr-2" />
            Batal
          </Button>
        </div>
      </div>
    </div>
  );
};

export default BulkActionBar;
