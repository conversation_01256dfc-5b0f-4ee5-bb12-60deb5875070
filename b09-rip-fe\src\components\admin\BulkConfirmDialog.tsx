import React, { useState, useEffect } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { AlertCircle } from 'lucide-react';
import { Label } from '@/components/ui/label';
import { OrganizationCombobox } from '@/components/organization/OrganizationCombobox';

interface BulkConfirmDialogProps {
  isOpen: boolean;
  onClose: () => void;
  onConfirm: (organizationId?: string) => void;
  count: number;
  isLoading: boolean;
  type: 'activate' | 'delete';
  hasClientUsers?: boolean;
}

const BulkConfirmDialog: React.FC<BulkConfirmDialogProps> = ({
  isOpen,
  onClose,
  onConfirm,
  count,
  isLoading,
  type,
  hasClientUsers = false,
}) => {
  const [selectedOrgId, setSelectedOrgId] = useState<string>('');
  const title = type === 'activate' ? 'Aktivasi User' : 'Hapus User';
  const action = type === 'activate' ? 'aktivasi' : 'hapus';
  const buttonText = type === 'activate' ? 'Aktivasi' : 'Hapus';

  // Reset selected organization when dialog opens
  useEffect(() => {
    if (isOpen) {
      setSelectedOrgId('');
    }
  }, [isOpen]);

  const handleConfirm = () => {
    // Only pass organization ID if activating client users
    if (type === 'activate' && hasClientUsers) {
      onConfirm(selectedOrgId);
    } else {
      onConfirm();
    }
  };

  const showOrgSelector = type === 'activate' && hasClientUsers;

  return (
    <Dialog
      open={isOpen}
      onOpenChange={(open) => !isLoading && !open && onClose()}
    >
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <AlertCircle className="h-5 w-5" />
            {title}
          </DialogTitle>
          <DialogDescription>
            Apakah Anda yakin ingin {action} {count} user yang dipilih?
          </DialogDescription>
        </DialogHeader>

        {showOrgSelector && (
          <div className="grid gap-4 py-4">
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="org" className="text-right">
                Organisasi
              </Label>
              <div className="col-span-3">
                <OrganizationCombobox
                  value={selectedOrgId}
                  onSelect={(id) => setSelectedOrgId(id)}
                  placeholder="Pilih organisasi untuk client"
                  disabled={isLoading}
                />
              </div>
            </div>
          </div>
        )}

        <DialogFooter className="mt-4">
          <Button variant="cancel" onClick={onClose} disabled={isLoading}>
            Batal
          </Button>
          <Button
            variant={type === 'activate' ? 'success' : 'destructive'}
            onClick={handleConfirm}
            disabled={isLoading || (showOrgSelector && !selectedOrgId)}
          >
            {isLoading ? 'Memproses...' : buttonText}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

export default BulkConfirmDialog;
