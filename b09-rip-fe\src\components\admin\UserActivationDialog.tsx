import React, { useState } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { UserWithProfile } from '@/types/admin';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Label } from '@/components/ui/label';
import { OrganizationCombobox } from '@/components/organization/OrganizationCombobox';
import { Organization } from '@/types/organization';

interface UserActivationDialogProps {
  user: UserWithProfile | null;
  isOpen: boolean;
  onClose: () => void;
  onActivate: (organizationId?: string) => Promise<void>;
  isLoading: boolean;
  organizations: Organization[];
}

const UserActivationDialog: React.FC<UserActivationDialogProps> = ({
  user,
  isOpen,
  onClose,
  onActivate,
  isLoading,
  organizations,
}) => {
  const [selectedOrgId, setSelectedOrgId] = useState<string>('');
  const isClient = user?.profile.role === 'Client';

  // Reset state when dialog opens with new user
  React.useEffect(() => {
    if (isOpen && user) {
      setSelectedOrgId('');
    }
  }, [isOpen, user]);

  const handleActivate = () => {
    if (!user) return;

    // For clients, organization is required
    if (isClient && !selectedOrgId) {
      return;
    }

    onActivate(isClient ? selectedOrgId : undefined);
  };

  return (
    <Dialog
      open={isOpen}
      onOpenChange={(open) => !isLoading && !open && onClose()}
    >
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle>Verifikasi Akun</DialogTitle>
          <DialogDescription>
            Verifikasi akun user {user?.profile.fullname}.
            {isClient && ' Pilih organisasi untuk user client.'}
          </DialogDescription>
        </DialogHeader>

        {isClient && (
          <div className="grid gap-4 py-4">
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="org" className="text-right">
                Organisasi
              </Label>
              <div className="col-span-3">
                <OrganizationCombobox
                  value={selectedOrgId}
                  onSelect={(id) => setSelectedOrgId(id)}
                  placeholder="Pilih organisasi"
                  disabled={isLoading}
                  organizations={organizations}
                />
              </div>
            </div>
          </div>
        )}

        <DialogFooter>
          <Button variant="cancel" onClick={onClose} disabled={isLoading}>
            Batal
          </Button>
          <Button
            onClick={handleActivate}
            disabled={isLoading || (isClient && !selectedOrgId)}
          >
            {isLoading ? 'Memproses...' : 'Verifikasi'}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

export default UserActivationDialog;
