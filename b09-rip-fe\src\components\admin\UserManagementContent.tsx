import React from 'react';
import UserTable from '@/components/admin/UserTable';
import { UserSearchFilter } from '@/components/admin/UserSearchFilter';
import UserActivationDialog from '@/components/admin/UserActivationDialog';
import DeleteConfirmationDialog from '@/components/admin/DeleteConfirmationDialog';
import BulkActionBar from '@/components/admin/BulkActionBar';
import BulkConfirmDialog from '@/components/admin/BulkConfirmDialog';
import { useUserManagement } from '@/hooks/admin/useUserManagement';
import { PageTitle } from '@/components/ui/PageTitle';

const UserManagementContent: React.FC = () => {
  const {
    users,
    selectedUsers,
    actionLoading,
    search,
    role,
    activateDialogOpen,
    deleteDialogOpen,
    selectedUser,
    bulkActivateDialogOpen,
    bulkDeleteDialogOpen,
    organizations,
    allSelectedUsersUnactivated,
    hasClientUsersInSelection,
    handleSelectUser,
    handleSelectAll,
    handleActivateClick,
    handleDeleteClick,
    handleBulkActivateClick,
    handleBulkDeleteClick,
    handleCancelSelection,
    handleBulkActivate,
    handleBulkDelete,
    handleActivate,
    handleDelete,
    handleSearchChange,
    handleRoleChange,
    setActivateDialogOpen,
    setDeleteDialogOpen,
    setBulkActivateDialogOpen,
    setBulkDeleteDialogOpen,
  } = useUserManagement();

  return (
    <div className="container mx-auto py-6 px-6">
      <PageTitle title="Manajemen Akun" />

      <div className="bg-white rounded-lg shadow p-6">
        <div className="mb-6">
          <UserSearchFilter
            search={search}
            role={role}
            onSearchChange={handleSearchChange}
            onRoleChange={handleRoleChange}
          />
        </div>

        <div className="overflow-x-auto mb-4">
          <UserTable
            users={users}
            selectedUsers={selectedUsers}
            onSelectUser={handleSelectUser}
            onSelectAll={handleSelectAll}
            onActivate={handleActivateClick}
            onDelete={handleDeleteClick}
          />
        </div>

        {selectedUsers.length > 0 && (
          <BulkActionBar
            selectedCount={selectedUsers.length}
            onActivate={handleBulkActivateClick}
            onDelete={handleBulkDeleteClick}
            onCancel={handleCancelSelection}
            showActivate={allSelectedUsersUnactivated()}
          />
        )}
      </div>

      {/* User Activation Dialog */}
      <UserActivationDialog
        user={selectedUser}
        isOpen={activateDialogOpen}
        onClose={() => setActivateDialogOpen(false)}
        onActivate={handleActivate}
        organizations={organizations}
        isLoading={actionLoading}
      />

      {/* Delete Confirmation Dialog */}
      <DeleteConfirmationDialog
        user={selectedUser}
        isOpen={deleteDialogOpen}
        onClose={() => setDeleteDialogOpen(false)}
        onConfirm={handleDelete}
        isLoading={actionLoading}
      />

      {/* Bulk Activate Confirmation Dialog */}
      <BulkConfirmDialog
        isOpen={bulkActivateDialogOpen}
        onClose={() => setBulkActivateDialogOpen(false)}
        onConfirm={handleBulkActivate}
        count={selectedUsers.length}
        isLoading={actionLoading}
        type="activate"
        hasClientUsers={hasClientUsersInSelection()}
      />

      {/* Bulk Delete Confirmation Dialog */}
      <BulkConfirmDialog
        isOpen={bulkDeleteDialogOpen}
        onClose={() => setBulkDeleteDialogOpen(false)}
        onConfirm={handleBulkDelete}
        count={selectedUsers.length}
        isLoading={actionLoading}
        type="delete"
      />
    </div>
  );
};

export default UserManagementContent;
