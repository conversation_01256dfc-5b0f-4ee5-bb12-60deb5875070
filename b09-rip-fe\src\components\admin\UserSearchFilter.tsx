'use client';

import { SearchFilter, Filter } from '@/components/ui/search-filter';

interface UserSearchFilterProps {
  search: string;
  role: string | undefined;
  onSearchChange: (value: string) => void;
  onRoleChange: (value: string | undefined) => void;
}

export function UserSearchFilter({
  search,
  role,
  onSearchChange,
  onRoleChange,
}: UserSearchFilterProps) {
  const roleFilter: Filter = {
    label: 'Role',
    value: role,
    onChange: onRoleChange,
    options: ['Admin', 'Manager', 'HR', 'Finance', 'Operation', 'Client'].map(
      (role) => ({
        value: role,
        label: role,
      })
    ),
  };

  return (
    <SearchFilter
      search={search}
      onSearchChange={onSearchChange}
      filters={[roleFilter]}
      searchPlaceholder="Cari nama, email, atau nomor telepon"
    />
  );
}
