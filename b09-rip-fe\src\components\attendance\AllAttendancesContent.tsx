'use client';

import AttendanceFilters from './AttendanceFilters';
import { AttendanceTable } from './AttendanceTable';
import { useAllAttendances } from '@/hooks/attendance/useAllAttendances';
import { PageTitle } from '@/components/ui/PageTitle';

// Simple loader component
const Loader = () => (
  <div className="flex justify-center items-center p-8">
    <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
  </div>
);

export default function AllAttendancesContent() {
  const {
    // Data
    attendances,
    loading,

    // Filter state
    searchTerm,
    statusFilter,
    dateRangeFilter,
    sortField,
    sortDirection,

    // Handlers
    handleSearchChange,
    handleStatusChange,
    handleDateRangeChange,
    handleSorting,
  } = useAllAttendances();

  return (
    <div className="container mx-auto py-6 px-6">
      <div className="flex justify-between items-center mb-6">
        <PageTitle title="Catatan Kehadiran Karyawan" />
      </div>

      <div className="bg-white rounded-lg shadow p-6">
        <div className="mb-6">
          <AttendanceFilters
            search={searchTerm}
            status={statusFilter}
            dateRange={dateRangeFilter}
            onSearchChange={handleSearchChange}
            onStatusChange={handleStatusChange}
            onDateRangeChange={handleDateRangeChange}
          />
        </div>

        {loading ? (
          <Loader />
        ) : (
          <div className="overflow-x-auto mb-4">
            <AttendanceTable
              attendances={Array.isArray(attendances) ? attendances : []}
              isLoading={false}
              sortField={sortField}
              sortDirection={sortDirection}
              onSort={handleSorting}
              showEmployeeColumn={true}
            />
          </div>
        )}
      </div>
    </div>
  );
}
