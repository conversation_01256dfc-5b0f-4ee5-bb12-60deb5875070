'use client';

import { PresenceStatus } from '@/types/attendance';
import {
  SearchFilter,
  Filter,
  DateRangeFilter,
} from '@/components/ui/search-filter';
import { DateRange } from 'react-day-picker';

interface AttendanceFiltersProps {
  search: string;
  status?: PresenceStatus;
  dateRange?: DateRange;
  onSearchChange: (value: string) => void;
  onStatusChange: (value: PresenceStatus | undefined) => void;
  onDateRangeChange: (range: DateRange | undefined) => void;
}

export function AttendanceFilters({
  search,
  status,
  dateRange,
  onSearchChange,
  onStatusChange,
  onDateRangeChange,
}: AttendanceFiltersProps) {
  const statusFilter: Filter = {
    label: 'Status kehadiran',
    value: status,
    onChange: (value) => onStatusChange(value as PresenceStatus | undefined),
    options: [
      { value: PresenceStatus.PRESENT, label: 'Hadir' },
      { value: PresenceStatus.ABSENT, label: 'Tidak <PERSON>' },
      { value: PresenceStatus.PERMIT, label: 'Izin' },
      { value: PresenceStatus.LEAVE, label: 'Cuti' },
    ],
  };

  const dateRangeFilter: DateRangeFilter = {
    value: dateRange,
    onChange: onDateRangeChange,
  };

  return (
    <SearchFilter
      search={search}
      onSearchChange={onSearchChange}
      filters={[statusFilter]}
      dateRange={dateRangeFilter}
      searchPlaceholder="Cari berdasarkan catatan"
    />
  );
}

export default AttendanceFilters;
