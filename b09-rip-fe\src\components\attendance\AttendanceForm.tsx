'use client';

import { useEffect, useState } from 'react';
import { toast } from 'sonner';
import { ArrowLeft, Plus, Trash, AlertCircle } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Textarea } from '@/components/ui/textarea';
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Label } from '@/components/ui/label';
import { PresenceStatus } from '@/types/attendance';
import { attendanceApi } from '@/lib/api/attendance';
import Link from 'next/link';
import { useRouter } from 'next/navigation';
import { format } from 'date-fns';
import { Calendar } from '@/components/ui/calendar';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover';
import { cn } from '@/lib/utils';
import { CalendarIcon } from 'lucide-react';
import { Checkbox } from '@/components/ui/checkbox';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { PageTitle } from '@/components/ui/PageTitle';
import { BackButton } from '@/components/ui/BackButton';

interface TaskData {
  id?: string;
  description: string;
  due_date: Date;
  completion_status: boolean;
}

interface AttendanceData {
  id: string;
  employee_id: string;
  date: string;
  clock_in: string | null;
  clock_out: string | null;
  presence_status: string;
  status?: PresenceStatus;
  notes: string | null;
  tasks: {
    id: string;
    description: string;
    due_date: string;
    assigned_by: string;
    completion_status: boolean;
  }[];
  alreadyComplete: boolean;
  message?: string;
}

interface AttendancePayload {
  status: PresenceStatus;
  presence_status: PresenceStatus;
  notes?: string;
  tasks?: Array<{
    id?: string;
    description: string;
    due_date: string;
    assigned_by: string;
    completion_status: boolean;
  }>;
}

export default function AttendanceForm() {
  const router = useRouter();

  // State for form fields
  const [attendanceId, setAttendanceId] = useState<string | null>(null);
  const [notes, setNotes] = useState<string>('');
  const [status, setStatus] = useState<PresenceStatus | ''>('');
  const [tasks, setTasks] = useState<TaskData[]>([]);
  const [isComplete, setIsComplete] = useState<boolean>(false);
  const [clockIn, setClockIn] = useState<string | null>(null);
  const [clockOut, setClockOut] = useState<string | null>(null);

  // Loading and error states
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [isSubmitting, setIsSubmitting] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);

  // Fetch today's attendance record if exists
  useEffect(() => {
    const fetchTodayAttendance = async () => {
      setIsLoading(true);
      try {
        const response = await attendanceApi.getTodayAttendance();

        if (response.success && response.data) {
          // Type assertion to unknown first then to AttendanceData
          const data = response.data as unknown as AttendanceData;

          // Set attendance ID for update operations
          setAttendanceId(data.id);

          // Set form values
          setStatus((data.status || data.presence_status) as PresenceStatus);
          setNotes(data.notes || '');
          setIsComplete(data.alreadyComplete);
          setClockIn(data.clock_in);
          setClockOut(data.clock_out);

          // Map tasks from API to component state
          const mappedTasks = data.tasks.map((task) => ({
            id: task.id,
            description: task.description,
            due_date: new Date(task.due_date),
            completion_status: task.completion_status,
          }));

          setTasks(mappedTasks);
        }
      } catch (error) {
        // If 404, it means no attendance for today - that's expected
        if (error && (error as { status?: number }).status !== 404) {
          console.error("Error fetching today's attendance:", error);
          setError('Terjadi kesalahan saat mengambil data presensi hari ini');
        }
      } finally {
        setIsLoading(false);
      }
    };

    fetchTodayAttendance();
  }, []);

  // Load saved data from localStorage only if no existing attendance
  useEffect(() => {
    // Only load from localStorage if no attendance record exists
    if (!isLoading && !attendanceId) {
      const savedNotes = localStorage.getItem('notes');
      const savedStatus = localStorage.getItem('status');
      const savedTasks = localStorage.getItem('tasks');

      if (savedNotes) setNotes(savedNotes);
      if (savedStatus) setStatus(savedStatus as PresenceStatus);
      if (savedTasks) {
        try {
          const parsedTasks = JSON.parse(savedTasks);
          // Convert string dates to Date objects
          const formattedTasks = parsedTasks.map(
            (task: {
              description: string;
              due_date: string;
              completion_status: boolean;
            }) => ({
              ...task,
              due_date: task.due_date ? new Date(task.due_date) : new Date(),
            })
          );
          setTasks(formattedTasks);
        } catch (e) {
          console.error('Error parsing saved tasks', e);
        }
      }
    }
  }, [isLoading, attendanceId]);

  // Save form data to localStorage when fields change (only if creating new record)
  useEffect(() => {
    if (!attendanceId && !isComplete) {
      localStorage.setItem('notes', notes);
      localStorage.setItem('status', status);
      localStorage.setItem('tasks', JSON.stringify(tasks));
    }
  }, [notes, status, tasks, attendanceId, isComplete]);

  // Handle add task button click
  const handleAddTask = () => {
    setTasks([
      ...tasks,
      {
        description: '',
        due_date: new Date(),
        completion_status: false,
      },
    ]);
  };

  // Handle task description change
  const handleTaskDescriptionChange = (index: number, value: string) => {
    const updatedTasks = [...tasks];
    updatedTasks[index].description = value;
    setTasks(updatedTasks);
  };

  // Handle task due date change
  const handleTaskDueDateChange = (index: number, date: Date | undefined) => {
    if (!date) return;

    const updatedTasks = [...tasks];
    updatedTasks[index].due_date = date;
    setTasks(updatedTasks);
  };

  // Handle task completion status change
  const handleTaskCompletionChange = (index: number, checked: boolean) => {
    const updatedTasks = [...tasks];
    updatedTasks[index].completion_status = checked;
    setTasks(updatedTasks);
  };

  // Handle delete task button click
  const handleDeleteTask = (index: number) => {
    const updatedTasks = tasks.filter((_, i) => i !== index);
    setTasks(updatedTasks);
  };

  // Form submission handler
  const handleSubmit = async () => {
    setIsSubmitting(true);
    setError(null);

    // Basic validation
    if (!status) {
      setError('Status kehadiran harus dipilih');
      setIsSubmitting(false);
      return;
    }

    // Filter out tasks with empty descriptions
    const validTasks = tasks.filter((task) => task.description.trim() !== '');

    try {
      // Prepare attendance data
      const attendanceData: AttendancePayload = {
        status: status as PresenceStatus,
        presence_status: status as PresenceStatus,
        // Only include notes if it has value
        ...(notes && notes.trim() !== '' ? { notes } : {}),
        // Only include tasks if status is "present"
        ...(status === PresenceStatus.PRESENT
          ? {
              tasks: validTasks.map((task) => ({
                ...(task.id ? { id: task.id } : {}), // Include ID if it exists (for updates)
                description: task.description,
                due_date: format(task.due_date, 'yyyy-MM-dd'),
                assigned_by: 'myself',
                completion_status: task.completion_status,
              })),
            }
          : {}),
      };

      let response;

      // If attendanceId exists, we're updating an existing record
      if (attendanceId) {
        // Don't include the attendance ID in the payload - backend will handle it
        response = await attendanceApi.createAttendance(attendanceData);
      } else {
        // Otherwise create a new record
        response = await attendanceApi.createAttendance(attendanceData);
      }

      if (response.success) {
        toast.success(
          attendanceId
            ? 'Presensi berhasil diperbarui!'
            : 'Presensi berhasil dicatat!'
        );

        // Selectively clear only form-related localStorage items instead of everything
        localStorage.removeItem('notes');
        localStorage.removeItem('status');
        localStorage.removeItem('tasks');
        localStorage.removeItem('lastSavedDate'); // Just in case this exists from previous code

        // Navigate back to attendance history
        router.push('/attendance');
      } else {
        setError(response.message || 'Gagal mencatat presensi');
      }
    } catch (error: unknown) {
      console.error('Error submitting attendance:', error);
      setError(
        error instanceof Error
          ? error.message
          : 'Terjadi kesalahan saat mencatat presensi'
      );
    } finally {
      setIsSubmitting(false);
    }
  };

  // Show loading state
  if (isLoading) {
    return (
      <div className="container mx-auto py-6 px-6">
        <div className="mb-6">
          <Link href="/attendance">
            <Button variant="ghost" size="sm" className="gap-1">
              <ArrowLeft className="h-4 w-4" />
              Kembali ke Riwayat Presensi
            </Button>
          </Link>
        </div>

        <div className="flex items-center justify-center h-64">
          <p className="text-muted-foreground">Memuat data presensi...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto py-6 px-6">
      <div className="flex items-center gap-4 mb-6">
        <BackButton onClick={() => router.push('/attendance')} />
        <PageTitle
          title="Rekam Presensi"
          subtitle="Isi form di bawah ini untuk mencatat presensi Anda"
        />
      </div>

      {isComplete && (
        <Alert className="mb-6">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>
            Presensi untuk hari ini telah lengkap. Anda tidak dapat mengubahnya
            lagi.
          </AlertDescription>
        </Alert>
      )}

      <Card className="w-full">
        <CardHeader>
          <CardTitle>
            {attendanceId
              ? isComplete
                ? 'Detail Presensi'
                : 'Perbarui Presensi'
              : 'Form Presensi'}
          </CardTitle>
          <CardDescription>
            {attendanceId
              ? isComplete
                ? 'Detail presensi dan tugas Anda hari ini'
                : 'Perbarui presensi dan tugas Anda hari ini'
              : 'Catat kehadiran Anda dan tugas-tugas yang akan dikerjakan'}
          </CardDescription>
        </CardHeader>

        <CardContent className="space-y-6">
          {/* Error message */}
          {error && (
            <div className="bg-destructive/10 text-destructive p-3 rounded-md text-sm">
              {error}
            </div>
          )}

          {/* Clock times - only shown for existing attendance */}
          {attendanceId && status === PresenceStatus.PRESENT && (
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 p-3 border rounded-md bg-muted/20">
              {clockIn && (
                <div>
                  <Label className="text-sm">Waktu Mulai Bekerja</Label>
                  <p className="font-medium mt-1">{clockIn}</p>
                </div>
              )}

              {isComplete && clockOut && (
                <div>
                  <Label className="text-sm">Waktu Selesai Bekerja</Label>
                  <p className="font-medium mt-1">{clockOut}</p>
                </div>
              )}
            </div>
          )}

          {/* Status selection */}
          <div className="space-y-2">
            <Label htmlFor="status">Status Kehadiran</Label>
            <Select
              value={status}
              onValueChange={(value) => setStatus(value as PresenceStatus)}
              disabled={isComplete || !!attendanceId}
            >
              <SelectTrigger id="status">
                <SelectValue placeholder="Pilih status kehadiran" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value={PresenceStatus.PRESENT}>Hadir</SelectItem>
                <SelectItem value={PresenceStatus.ABSENT}>
                  Tidak Hadir
                </SelectItem>
                <SelectItem value={PresenceStatus.PERMIT}>Izin</SelectItem>
                <SelectItem value={PresenceStatus.LEAVE}>Cuti</SelectItem>
              </SelectContent>
            </Select>
          </div>

          {/* Tasks - only show when status is "present" */}
          {status === PresenceStatus.PRESENT && (
            <div className="space-y-4">
              <div className="flex justify-between items-center">
                <Label>Tugas</Label>
                {!isComplete && (
                  <Button
                    onClick={handleAddTask}
                    type="button"
                    variant="outline"
                    size="sm"
                    className="gap-1"
                  >
                    <Plus className="h-4 w-4" />
                    Tambah Tugas
                  </Button>
                )}
              </div>

              {tasks.length === 0 ? (
                <p className="text-muted-foreground text-sm">
                  Belum ada tugas yang ditambahkan.
                </p>
              ) : (
                <div className="space-y-4">
                  {tasks.map((task, index) => (
                    <div
                      key={index}
                      className="space-y-2 p-3 border rounded-md"
                    >
                      <div className="flex items-center gap-2">
                        <Checkbox
                          id={`task-${index}-completion`}
                          checked={task.completion_status}
                          onCheckedChange={(checked) =>
                            handleTaskCompletionChange(
                              index,
                              checked as boolean
                            )
                          }
                          disabled={isComplete}
                        />
                        <Input
                          value={task.description}
                          onChange={(e) =>
                            handleTaskDescriptionChange(index, e.target.value)
                          }
                          placeholder={`Deskripsi tugas ${index + 1}`}
                          className={cn(
                            task.completion_status &&
                              'line-through text-muted-foreground'
                          )}
                          disabled={isComplete}
                        />
                        {!isComplete && (
                          <Button
                            onClick={() => handleDeleteTask(index)}
                            variant="ghost"
                            size="icon"
                            type="button"
                          >
                            <Trash className="h-4 w-4" />
                          </Button>
                        )}
                      </div>

                      <div className="ml-6">
                        <Label
                          htmlFor={`task-${index}-due-date`}
                          className="text-sm"
                        >
                          Tenggat Waktu
                        </Label>
                        <Popover>
                          <PopoverTrigger asChild>
                            <Button
                              id={`task-${index}-due-date`}
                              variant="outline"
                              className={cn(
                                'mt-1 w-full justify-start text-left font-normal',
                                !task.due_date && 'text-muted-foreground'
                              )}
                              disabled={isComplete}
                            >
                              <CalendarIcon className="mr-2 h-4 w-4" />
                              {task.due_date
                                ? format(task.due_date, 'dd MMMM yyyy')
                                : 'Pilih tanggal'}
                            </Button>
                          </PopoverTrigger>
                          {!isComplete && (
                            <PopoverContent className="w-auto p-0">
                              <Calendar
                                mode="single"
                                selected={task.due_date}
                                onSelect={(date) =>
                                  handleTaskDueDateChange(index, date)
                                }
                              />
                            </PopoverContent>
                          )}
                        </Popover>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </div>
          )}

          {/* Notes */}
          <div className="space-y-2">
            <Label htmlFor="notes">Catatan</Label>
            <Textarea
              id="notes"
              value={notes}
              onChange={(e) => setNotes(e.target.value)}
              placeholder="Tambahkan catatan (opsional)"
              rows={3}
              disabled={isComplete}
            />
          </div>
        </CardContent>

        <CardFooter className="flex justify-end gap-2">
          <Button
            variant="cancel"
            onClick={() => router.push('/attendance')}
            disabled={isSubmitting}
          >
            {isComplete ? 'Kembali' : 'Batal'}
          </Button>

          {!isComplete && (
            <Button onClick={handleSubmit} disabled={isSubmitting}>
              {isSubmitting
                ? 'Menyimpan...'
                : attendanceId
                  ? 'Perbarui Presensi'
                  : 'Simpan Presensi'}
            </Button>
          )}
        </CardFooter>
      </Card>
    </div>
  );
}
