'use client';

import { useRouter } from 'next/navigation';
import { Button } from '@/components/ui/button';
import { Plus } from 'lucide-react';
import { AttendanceTable } from './AttendanceTable';
import { AttendanceFilters } from './AttendanceFilters';
import { useAttendanceHistory } from '@/hooks/attendance/useAttendanceHistory';
import { PageTitle } from '@/components/ui/PageTitle';

export default function AttendanceHistory() {
  const router = useRouter();

  const {
    attendances,
    loading,
    searchTerm,
    statusFilter,
    dateRangeFilter,
    sortField,
    sortDirection,
    handleSearchChange,
    handleStatusChange,
    handleDateRangeChange,
    handleSorting,
  } = useAttendanceHistory();

  // Navigate to record attendance page
  const handleRecordAttendance = () => {
    router.push('/attendance/record');
  };

  return (
    <div className="container mx-auto py-6 px-6">
      <div className="flex items-center justify-between mb-6">
        <PageTitle title="Riwayat Presensi" />
        <Button
          onClick={handleRecordAttendance}
          className="flex items-center gap-1"
        >
          <Plus className="h-4 w-4" />
          Rekam Presensi
        </Button>
      </div>

      <div className="bg-white rounded-lg shadow p-6">
        <div className="mb-6">
          <AttendanceFilters
            search={searchTerm}
            status={statusFilter}
            dateRange={dateRangeFilter}
            onSearchChange={handleSearchChange}
            onStatusChange={handleStatusChange}
            onDateRangeChange={handleDateRangeChange}
          />
        </div>

        <div className="overflow-x-auto mb-4">
          <AttendanceTable
            attendances={attendances}
            isLoading={loading}
            sortField={sortField}
            sortDirection={sortDirection}
            onSort={handleSorting}
          />
        </div>
      </div>
    </div>
  );
}
