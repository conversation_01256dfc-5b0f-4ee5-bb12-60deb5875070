'use client';

import { Badge } from '@/components/ui/badge';
import { PresenceStatus } from '@/types/attendance';

interface AttendanceStatusBadgeProps {
  status: PresenceStatus | string | undefined;
}

export function AttendanceStatusBadge({ status }: AttendanceStatusBadgeProps) {
  let badgeVariant: 'success' | 'warning' | 'danger' | 'info' | 'secondary' =
    'secondary';
  let label = '';

  // Handle undefined status
  if (!status) {
    badgeVariant = 'secondary';
    label = 'Unknown';
    return <Badge variant={badgeVariant}>{label}</Badge>;
  }

  switch (status) {
    case PresenceStatus.PRESENT:
      badgeVariant = 'success';
      label = 'Hadir';
      break;
    case PresenceStatus.ABSENT:
      badgeVariant = 'danger';
      label = 'Tidak Hadir';
      break;
    case PresenceStatus.PERMIT:
      badgeVariant = 'warning';
      label = 'Izin';
      break;
    case PresenceStatus.LEAVE:
      badgeVariant = 'info';
      label = 'Cuti';
      break;
    default:
      badgeVariant = 'secondary';
      label = status;
  }

  return <Badge variant={badgeVariant}>{label}</Badge>;
}

export default AttendanceStatusBadge;
