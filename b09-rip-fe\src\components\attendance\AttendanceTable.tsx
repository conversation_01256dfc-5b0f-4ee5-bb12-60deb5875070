'use client';

import { Attendance } from '@/types/attendance';
import { format } from 'date-fns';
import { id } from 'date-fns/locale';
import { Check, Edit, Trash } from 'lucide-react';
import { Button } from '@/components/ui/button';
import AttendanceStatusBadge from './AttendanceStatusBadge';
import Link from 'next/link';
import { DataTable, SortDirection } from '@/components/ui/data-table';

interface AttendanceTableProps {
  attendances: Attendance[];
  isLoading: boolean;
  onView?: (attendance: Attendance) => void;
  onEdit?: (attendance: Attendance) => void;
  onDelete?: (attendance: Attendance) => void;
  showEmployeeColumn?: boolean;
  sortField?: string;
  sortDirection?: SortDirection;
  onSort?: (field: string, direction: SortDirection) => void;
  // Pagination props removed as they're not used in DataTable anymore
  // currentPage?: number;
  // itemsPerPage?: number;
  // onPageChange?: (page: number) => void;
}

export function AttendanceTable({
  attendances,
  isLoading,
  onView,
  onEdit,
  onDelete,
  showEmployeeColumn = false,
  sortField,
  sortDirection,
  onSort,
  // Pagination props removed as they're not used in DataTable anymore
  // currentPage = 1,
  // itemsPerPage = 10,
  // onPageChange,
}: AttendanceTableProps) {
  // Define the columns for the DataTable
  const baseColumns = [
    {
      key: 'date',
      header: 'Tanggal',
      width: '120px',
      sortable: false,
      render: (attendance: Attendance) =>
        format(new Date(attendance.date), 'dd MMM yyyy', {
          locale: id,
        }),
    },
  ];

  // Employee column (conditional)
  const employeeColumn = showEmployeeColumn
    ? [
        {
          key: 'employee_name',
          header: 'Karyawan',
          sortable: false,
          render: (attendance: Attendance) =>
            attendance.employee_id ? (
              <Link
                href={`/employee/${attendance.employee_id}`}
                className="text-[#9B7533] hover:text-[#AB8B3B] hover:underline cursor-pointer font-medium"
              >
                {attendance.employee_name || attendance.employee_id}
              </Link>
            ) : (
              <span>{attendance.employee_name || 'Unknown'}</span>
            ),
        },
      ]
    : [];

  // Status and other columns
  const commonColumns = [
    {
      key: 'presence_status',
      header: 'Status',
      sortable: false,
      render: (attendance: Attendance) => (
        <AttendanceStatusBadge
          status={attendance.presence_status || attendance.status}
        />
      ),
    },
    {
      key: 'clock_in',
      header: 'Mulai Bekerja',
      render: (attendance: Attendance) =>
        attendance.clock_in || attendance.check_in || '-',
    },
    {
      key: 'clock_out',
      header: 'Selesai Bekerja',
      render: (attendance: Attendance) =>
        attendance.clock_out || attendance.check_out || '-',
    },
    {
      key: 'notes',
      header: 'Catatan',
      width: '300px',
      render: (attendance: Attendance) => (
        <div className="max-w-[300px] truncate">{attendance.notes || '-'}</div>
      ),
    },
  ];

  // Action buttons column (if handlers are provided)
  const actionColumn =
    onView || onEdit || onDelete
      ? [
          {
            key: 'actions',
            header: 'Aksi',
            width: '150px',
            render: (attendance: Attendance) => (
              <div className="flex space-x-2">
                {onView && (
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => onView(attendance)}
                  >
                    <Check className="h-4 w-4" />
                  </Button>
                )}
                {onEdit && (
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => onEdit(attendance)}
                  >
                    <Edit className="h-4 w-4" />
                  </Button>
                )}
                {onDelete && (
                  <Button
                    variant="destructive"
                    size="sm"
                    onClick={() => onDelete(attendance)}
                  >
                    <Trash className="h-4 w-4" />
                  </Button>
                )}
              </div>
            ),
          },
        ]
      : [];

  // Combine all columns
  const columns = [
    ...baseColumns,
    ...employeeColumn,
    ...commonColumns,
    ...actionColumn,
  ];

  // Define the expandable content for tasks
  const expandableContent = (attendance: Attendance) => {
    if (!attendance.tasks || attendance.tasks.length === 0) {
      return null;
    }

    return (
      <div className="space-y-2">
        <div className="text-sm font-medium mb-2">Tugas:</div>
        {attendance.tasks.map((task) => (
          <div
            key={task.id}
            className="flex items-start gap-2 p-2 rounded-md bg-background"
          >
            <div className="text-sm">
              <div className="font-medium">{task.description}</div>
              <div className="text-gray-600 mt-1">
                Ditugaskan oleh: {task.assigned_by}
              </div>
              <div className="flex items-center gap-4 mt-2 text-xs">
                <div className="text-gray-500">
                  Status:{' '}
                  {task.completion_status ? 'Selesai' : 'Belum diselesaikan'}
                </div>
                {task.due_date && (
                  <div className="text-gray-500">
                    Tenggat waktu:{' '}
                    {format(new Date(task.due_date), 'dd MMM yyyy', {
                      locale: id,
                    })}
                  </div>
                )}
              </div>
            </div>
          </div>
        ))}
      </div>
    );
  };

  return (
    <DataTable
      columns={columns}
      data={attendances}
      keyExtractor={(attendance) => attendance.id}
      loading={isLoading}
      sortField={sortField}
      sortDirection={sortDirection}
      onSort={onSort}
      expandableContent={(attendance) =>
        attendance.tasks && attendance.tasks.length > 0
          ? expandableContent(attendance)
          : null
      }
      // Pagination props removed as they're not used in DataTable anymore
      // currentPage={currentPage}
      // itemsPerPage={itemsPerPage}
      // onPageChange={onPageChange}
      emptyStateMessage="Tidak ada data presensi yang ditemukan."
      // showPagination prop removed as it's not used in DataTable anymore
      // showPagination={false}
    />
  );
}

export default AttendanceTable;
