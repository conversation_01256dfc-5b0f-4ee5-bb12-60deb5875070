'use client';

import { useRouter } from 'next/navigation';
import { BackButton } from '@/components/ui/BackButton';
import { AttendanceTable } from './AttendanceTable';
import { AttendanceFilters } from './AttendanceFilters';
import { useEmployeeAttendance } from '@/hooks/attendance/useEmployeeAttendance';
import { PageTitle } from '@/components/ui/PageTitle';

interface EmployeeAttendanceContentProps {
  employeeId: string;
  employeeName: string;
}

const Loader = () => (
  <div className="flex justify-center items-center p-8">
    <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
  </div>
);

export default function EmployeeAttendanceContent({
  employeeId,
  employeeName,
}: EmployeeAttendanceContentProps) {
  const router = useRouter();
  const {
    // Data
    attendances,
    loading,

    // Filter state
    searchTerm,
    statusFilter,
    dateRangeFilter,
    sortField,
    sortDirection,

    // Handlers
    handleSearchChange,
    handleStatusChange,
    handleDateRangeChange,
    handleSorting,
  } = useEmployeeAttendance(employeeId);

  return (
    <div className="space-y-4">
      <div className="flex items-center gap-4 mb-2">
        <BackButton onClick={() => router.push(`/employee/${employeeId}`)} />
        <PageTitle title={`Catatan Kehadiran ${employeeName}`} />
      </div>
      <div className="bg-white rounded-lg shadow p-6">
        <div className="mb-6">
          <AttendanceFilters
            search={searchTerm}
            status={statusFilter}
            dateRange={dateRangeFilter}
            onSearchChange={handleSearchChange}
            onStatusChange={handleStatusChange}
            onDateRangeChange={handleDateRangeChange}
          />
        </div>

        {loading ? (
          <Loader />
        ) : (
          <div className="overflow-x-auto mb-4">
            <AttendanceTable
              attendances={Array.isArray(attendances) ? attendances : []}
              isLoading={false}
              sortField={sortField}
              sortDirection={sortDirection}
              onSort={handleSorting}
              showEmployeeColumn={false}
            />
          </div>
        )}
      </div>
    </div>
  );
}
