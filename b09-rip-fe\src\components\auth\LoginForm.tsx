'use client';

import { useState, useEffect } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import Link from 'next/link';
import Image from 'next/image';
import { useAuth } from '@/hooks/auth/useAuth';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { InfoCircledIcon, ReloadIcon } from '@radix-ui/react-icons';
import { useRouter } from 'next/navigation';
import { JWTManager } from '@/lib/auth/jwt';
import { toast } from 'sonner';
import { useAuthStore } from '@/lib/store/auth-store';

// Validation schema
const loginSchema = z.object({
  email: z.string().email({ message: 'Email tidak valid' }),
  password: z.string().min(8, { message: 'Password minimal 8 karakter' }),
});

type LoginFormValues = z.infer<typeof loginSchema>;

export default function LoginForm() {
  const {
    signIn,
    signOut,
    loading,
    error,
    accountInactive,
    profileIncomplete,
    checkAccountActivation,
    checkAuth,
  } = useAuth();
  const [submitError, setSubmitError] = useState<string | null>(error);
  const [checkingActivation, setCheckingActivation] = useState(false);
  const router = useRouter();

  const {
    register,
    handleSubmit,
    formState: { errors },
  } = useForm<LoginFormValues>({
    resolver: zodResolver(loginSchema),
  });

  // Update error message when error from useAuth changes
  useEffect(() => {
    setSubmitError(error);
  }, [error]);

  // Clear any persisted error state when component mounts
  useEffect(() => {
    // Clear localStorage flag that might be incorrectly set
    if (typeof window !== 'undefined') {
      localStorage.removeItem('accountInactive');
    }

    // Clear error states
    setSubmitError(null);

    // Refresh the auth state to ensure we have the latest status
    const refreshAuthState = async () => {
      // Check auth state directly to ensure we're up to date
      try {
        await checkAuth();
      } catch (err) {
        console.error('Error refreshing auth state:', err);
      }

      // Then check tokens status
      const hasTokens =
        !!JWTManager.getAccessToken() && !!JWTManager.getRefreshToken();

      if (hasTokens) {
        // We have tokens - check if account is active directly
        await checkAccountActivation();
      }
    };

    refreshAuthState();
  }, [checkAccountActivation, checkAuth]);

  // Check for existing tokens and inactive account status on mount
  useEffect(() => {
    const checkTokensAndStatus = async () => {
      // If we already have tokens and account is inactive, keep that state
      const hasTokens =
        !!JWTManager.getAccessToken() && !!JWTManager.getRefreshToken();

      if (hasTokens && accountInactive) {
        // Already in the correct state from the useAuth initialization
        setSubmitError(
          'Akun Anda belum diaktivasi. Mohon tunggu admin untuk mengaktivasi akun Anda.'
        );
      } else if (hasTokens) {
        // We have tokens but don't know account status yet - check it
        const isActive = await checkAccountActivation();

        // Clear error message if account is active
        if (isActive) {
          setSubmitError(null);
        }
      }
    };

    checkTokensAndStatus();
  }, [accountInactive, checkAccountActivation]);

  const onSubmit = async (data: LoginFormValues) => {
    setSubmitError(null);

    try {
      const success = await signIn(data, false);
      if (success) {
        // Show login success toast with welcome message
        toast.success('Selamat datang di Kasuat!', {
          id: 'login-success', // Add ID to prevent duplicates
        });

        // User is authenticated, redirect based on profile completion
        if (profileIncomplete) {
          // Don't show profile incomplete toast here - ProtectedRoute will handle that
          router.push('/update-account');
        } else {
          router.push('/dashboard');
        }
      } else if (accountInactive) {
        // Show warning toast for inactive account
        toast.warning('Akun belum aktif', {
          description: 'Akun Anda belum diaktivasi oleh admin',
        });
        setSubmitError(
          'Akun Anda belum diaktivasi. Mohon tunggu admin untuk mengaktivasi akun Anda.'
        );
      } else {
        // Show error toast
        toast.error('Login gagal', {
          description: error || 'Periksa email dan password Anda',
        });
        setSubmitError(
          error || 'Login gagal. Periksa email dan password Anda.'
        );
      }
    } catch (err) {
      // Show error toast for unexpected errors
      toast.error('Terjadi kesalahan', {
        description: 'Sistem tidak dapat memproses permintaan Anda',
      });
      setSubmitError('Terjadi kesalahan saat login. Silakan coba lagi.');
      console.error('Login error:', err);
    }
  };

  const handleCheckActivation = async () => {
    setCheckingActivation(true);
    setSubmitError(null); // Clear any existing error messages before check

    try {
      const isActive = await checkAccountActivation();

      if (isActive) {
        toast.success('Akun sudah aktif', {
          description: 'Akun Anda telah diaktivasi oleh admin',
        });

        // Since checkAccountActivation now handles authentication,
        // we can check if auth state has been established
        if (useAuthStore.getState().isAuthenticated) {
          const currentUser = useAuthStore.getState().user;

          if (currentUser?.profileComplete === false) {
            toast.info('Profil belum lengkap', {
              description: 'Anda perlu melengkapi profil Anda terlebih dahulu',
            });
            router.push('/update-account');
          } else {
            toast.success('Dialihkan ke dashboard', {
              description: 'Selamat datang di Kasuat!',
            });
            router.push('/dashboard');
          }
        }
      } else {
        toast.warning('Akun belum aktif', {
          description: 'Akun Anda belum diaktivasi oleh admin',
        });
      }
    } catch (err) {
      console.error('Error checking activation status:', err);
      toast.error('Terjadi kesalahan', {
        description: 'Gagal memeriksa status aktivasi',
      });
    } finally {
      setCheckingActivation(false);
    }
  };

  return (
    <div className="flex min-h-screen">
      {/* Left side with illustration */}
      <div className="hidden md:flex md:w-1/2 bg-background items-center justify-center p-8">
        <div className="max-w-md">
          <h1 className="text-4xl font-bold text-[#B78F38] mb-4">
            Selamat Datang di Kasuat,
          </h1>
          <p className="text-lg text-muted-foreground">
            Masuk dengan akun Anda dengan login atau registrasi
          </p>
          <Image
            src="/assets/illustrations/login-illustration.svg"
            alt="Login illustration"
            className="mt-8 max-w-sm"
            width={500}
            height={300}
            onError={(e) => {
              // Fallback if image doesn't exist
              (e.target as HTMLImageElement).style.display = 'none';
            }}
          />
        </div>
      </div>

      {/* Right side with form */}
      <div className="w-full md:w-1/2 flex items-center justify-center p-8">
        <div className="w-full max-w-md">
          <div className="bg-[#3C3C3C] rounded-lg p-8 shadow-lg">
            <h2 className="text-2xl font-semibold text-white text-center mb-6">
              Login
            </h2>

            {accountInactive &&
            !useAuthStore.getState().isAuthenticated &&
            JWTManager.getAccessToken() &&
            JWTManager.getRefreshToken() ? (
              <div className="mb-6">
                <Alert className="bg-amber-500/10 border border-amber-500/50 text-amber-300">
                  <InfoCircledIcon className="h-4 w-4" />
                  <AlertTitle className="text-amber-300">
                    Akun Belum Aktif
                  </AlertTitle>
                  <AlertDescription className="text-amber-200">
                    Akun Anda belum diaktivasi. Mohon tunggu admin untuk
                    mengaktivasi akun Anda.
                    <div className="mt-3 flex space-x-2">
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={handleCheckActivation}
                        disabled={checkingActivation}
                      >
                        {checkingActivation ? (
                          <>
                            <ReloadIcon className="mr-2 h-4 w-4 animate-spin" />
                            Memeriksa...
                          </>
                        ) : (
                          'Periksa Status Aktivasi'
                        )}
                      </Button>
                      <Button
                        variant="destructive"
                        size="sm"
                        onClick={signOut}
                        disabled={loading}
                      >
                        Logout
                      </Button>
                    </div>
                  </AlertDescription>
                </Alert>
              </div>
            ) : (
              <div className="space-y-6">
                <form onSubmit={handleSubmit(onSubmit)}>
                  <div className="space-y-6">
                    <div>
                      <label className="block text-sm font-medium text-[#B78F38] mb-1">
                        Email
                      </label>
                      <Input
                        type="email"
                        placeholder="Masukkan alamat email"
                        className="w-full bg-white text-gray-900"
                        {...register('email')}
                      />
                      {errors.email && (
                        <p className="mt-1 text-sm text-red-400">
                          {errors.email.message}
                        </p>
                      )}
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-[#B78F38] mb-1">
                        Password
                      </label>
                      <Input
                        type="password"
                        placeholder="Masukkan password Anda"
                        className="w-full bg-white text-gray-900"
                        {...register('password')}
                      />
                      {errors.password && (
                        <p className="mt-1 text-sm text-red-400">
                          {errors.password.message}
                        </p>
                      )}
                    </div>

                    {submitError && (
                      <Alert className="bg-red-500/10 border border-red-500/50">
                        <AlertTitle className="text-red-400">
                          Login Gagal
                        </AlertTitle>
                        <AlertDescription className="text-red-400">
                          {submitError}
                        </AlertDescription>
                      </Alert>
                    )}

                    <Button type="submit" className="w-full" disabled={loading}>
                      {loading ? 'Memproses...' : 'Login'}
                    </Button>
                  </div>
                </form>

                <div className="text-center text-sm text-white">
                  <span>Belum memiliki akun? </span>
                  <Link
                    href="/register"
                    className="text-[#B78F38] hover:underline"
                  >
                    Registrasi dulu yuk!
                  </Link>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}
