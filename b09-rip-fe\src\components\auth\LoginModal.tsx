'use client';

import { useState, useEffect } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { useAuth } from '@/hooks/auth/useAuth';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { InfoCircledIcon, ReloadIcon } from '@radix-ui/react-icons';
import { useRouter } from 'next/navigation';
import { JWTManager } from '@/lib/auth/jwt';
import { toast } from 'sonner';
import { useAuthStore } from '@/lib/store/auth-store';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';

// Validation schema
const loginSchema = z.object({
  email: z.string().email({ message: 'Email tidak valid' }),
  password: z.string().min(8, { message: 'Password minimal 8 karakter' }),
});

type LoginFormValues = z.infer<typeof loginSchema>;

interface LoginModalProps {
  isOpen: boolean;
  onClose: () => void;
  onOpenRegister?: () => void;
}

export default function LoginModal({ isOpen, onClose, onOpenRegister }: LoginModalProps) {
  const {
    signIn,
    signOut,
    loading,
    error,
    accountInactive,
    profileIncomplete,
    checkAccountActivation,
    checkAuth,
  } = useAuth();
  const [submitError, setSubmitError] = useState<string | null>(error);
  const [checkingActivation, setCheckingActivation] = useState(false);
  const router = useRouter();

  const {
    register,
    handleSubmit,
    formState: { errors },
    reset,
  } = useForm<LoginFormValues>({
    resolver: zodResolver(loginSchema),
  });

  // Update error message when error from useAuth changes
  useEffect(() => {
    setSubmitError(error);
  }, [error]);

  // Clear form when modal closes
  useEffect(() => {
    if (!isOpen) {
      reset();
      setSubmitError(null);
    }
  }, [isOpen, reset]);

  // Clear any persisted error state when component mounts
  useEffect(() => {
    if (isOpen) {
      // Clear localStorage flag that might be incorrectly set
      if (typeof window !== 'undefined') {
        localStorage.removeItem('accountInactive');
      }

      // Clear error states
      setSubmitError(null);

      // Refresh the auth state to ensure we have the latest status
      const refreshAuthState = async () => {
        try {
          await checkAuth();
        } catch (err) {
          console.error('Error refreshing auth state:', err);
        }

        const hasTokens =
          !!JWTManager.getAccessToken() && !!JWTManager.getRefreshToken();

        if (hasTokens) {
          await checkAccountActivation();
        }
      };

      refreshAuthState();
    }
  }, [isOpen, checkAccountActivation, checkAuth]);

  // Check for existing tokens and inactive account status on mount
  useEffect(() => {
    if (isOpen) {
      const checkTokensAndStatus = async () => {
        const hasTokens =
          !!JWTManager.getAccessToken() && !!JWTManager.getRefreshToken();

        if (hasTokens && accountInactive) {
          setSubmitError(
            'Akun Anda belum diaktivasi. Mohon tunggu admin untuk mengaktivasi akun Anda.'
          );
        } else if (hasTokens) {
          const isActive = await checkAccountActivation();
          if (isActive) {
            setSubmitError(null);
          }
        }
      };

      checkTokensAndStatus();
    }
  }, [isOpen, accountInactive, checkAccountActivation]);

  const onSubmit = async (data: LoginFormValues) => {
    setSubmitError(null);

    try {
      const success = await signIn(data, false);
      if (success) {
        toast.success('Selamat datang di Kasuat!', {
          id: 'login-success',
        });

        onClose(); // Close modal on successful login

        if (profileIncomplete) {
          router.push('/update-account');
        } else {
          router.push('/dashboard');
        }
      } else if (accountInactive) {
        toast.warning('Akun belum aktif', {
          description: 'Akun Anda belum diaktivasi oleh admin',
        });
        setSubmitError(
          'Akun Anda belum diaktivasi. Mohon tunggu admin untuk mengaktivasi akun Anda.'
        );
      } else {
        toast.error('Login gagal', {
          description: error || 'Periksa email dan password Anda',
        });
        setSubmitError(
          error || 'Login gagal. Periksa email dan password Anda.'
        );
      }
    } catch (err) {
      toast.error('Terjadi kesalahan', {
        description: 'Sistem tidak dapat memproses permintaan Anda',
      });
      setSubmitError('Terjadi kesalahan saat login. Silakan coba lagi.');
      console.error('Login error:', err);
    }
  };

  const handleCheckActivation = async () => {
    setCheckingActivation(true);
    setSubmitError(null);

    try {
      const isActive = await checkAccountActivation();

      if (isActive) {
        toast.success('Akun sudah aktif', {
          description: 'Akun Anda telah diaktivasi oleh admin',
        });

        if (useAuthStore.getState().isAuthenticated) {
          const currentUser = useAuthStore.getState().user;

          onClose(); // Close modal

          if (currentUser?.profileComplete === false) {
            toast.info('Profil belum lengkap', {
              description: 'Anda perlu melengkapi profil Anda terlebih dahulu',
            });
            router.push('/update-account');
          } else {
            toast.success('Dialihkan ke dashboard', {
              description: 'Selamat datang di Kasuat!',
            });
            router.push('/dashboard');
          }
        }
      } else {
        toast.warning('Akun belum aktif', {
          description: 'Akun Anda belum diaktivasi oleh admin',
        });
      }
    } catch (err) {
      console.error('Error checking activation status:', err);
      toast.error('Terjadi kesalahan', {
        description: 'Gagal memeriksa status aktivasi',
      });
    } finally {
      setCheckingActivation(false);
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-md bg-white border-gray-200 shadow-2xl">
        <DialogHeader>
          <DialogTitle className="text-2xl font-semibold text-gray-900 text-center">
            Login
          </DialogTitle>
        </DialogHeader>

        <div className="space-y-6">
          {accountInactive &&
          !useAuthStore.getState().isAuthenticated &&
          JWTManager.getAccessToken() &&
          JWTManager.getRefreshToken() ? (
            <div>
              <Alert className="bg-amber-500/10 border border-amber-500/50 text-amber-300">
                <InfoCircledIcon className="h-4 w-4" />
                <AlertTitle className="text-amber-300">
                  Akun Belum Aktif
                </AlertTitle>
                <AlertDescription className="text-amber-200">
                  Akun Anda belum diaktivasi. Mohon tunggu admin untuk
                  mengaktivasi akun Anda.
                  <div className="mt-3 flex space-x-2">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={handleCheckActivation}
                      disabled={checkingActivation}
                    >
                      {checkingActivation ? (
                        <>
                          <ReloadIcon className="mr-2 h-4 w-4 animate-spin" />
                          Memeriksa...
                        </>
                      ) : (
                        'Periksa Status Aktivasi'
                      )}
                    </Button>
                    <Button
                      variant="destructive"
                      size="sm"
                      onClick={signOut}
                      disabled={loading}
                    >
                      Logout
                    </Button>
                  </div>
                </AlertDescription>
              </Alert>
            </div>
          ) : (
            <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
              <div>
                <label className="block text-sm font-medium text-[#B78F38] mb-1">
                  Email
                </label>
                <Input
                  type="email"
                  placeholder="Masukkan alamat email"
                  className="w-full bg-gray-50 border-gray-200 text-gray-900 focus:border-[#B78F38] focus:ring-[#B78F38]"
                  {...register('email')}
                />
                {errors.email && (
                  <p className="mt-1 text-sm text-red-500">
                    {errors.email.message}
                  </p>
                )}
              </div>

              <div>
                <label className="block text-sm font-medium text-[#B78F38] mb-1">
                  Password
                </label>
                <Input
                  type="password"
                  placeholder="Masukkan password Anda"
                  className="w-full bg-gray-50 border-gray-200 text-gray-900 focus:border-[#B78F38] focus:ring-[#B78F38]"
                  {...register('password')}
                />
                {errors.password && (
                  <p className="mt-1 text-sm text-red-500">
                    {errors.password.message}
                  </p>
                )}
              </div>

              {submitError && (
                <Alert className="bg-red-500/10 border border-red-500/50">
                  <AlertTitle className="text-red-400">Login Gagal</AlertTitle>
                  <AlertDescription className="text-red-400">
                    {submitError}
                  </AlertDescription>
                </Alert>
              )}

              <Button type="submit" className="w-full" disabled={loading}>
                {loading ? 'Memproses...' : 'Login'}
              </Button>
            </form>
          )}

          <div className="text-center text-sm text-gray-600">
            <span>Belum memiliki akun? </span>
            <button
              type="button"
              className="text-[#B78F38] hover:underline font-medium"
              onClick={() => {
                onClose();
                onOpenRegister?.();
              }}
            >
              Registrasi dulu yuk!
            </button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}
