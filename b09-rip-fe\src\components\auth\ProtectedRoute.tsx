'use client';

import { useEffect, useState } from 'react';
import { useRouter, usePathname } from 'next/navigation';
import { useAuth } from '@/hooks/auth/useAuth';
import { useAuthStore } from '@/lib/store/auth-store';
import { JWTManager } from '@/lib/auth/jwt';
import { toast } from 'sonner';

export interface ProtectedRouteProps {
  children: React.ReactNode;
}

export const ProtectedRoute: React.FC<ProtectedRouteProps> = ({ children }) => {
  const router = useRouter();
  const pathname = usePathname();
  const { checkAuth } = useAuth();
  const [isChecking, setIsChecking] = useState(true);
  const [shouldRedirect, setShouldRedirect] = useState(false);
  const isUpdateAccountPage = pathname === '/update-account';

  useEffect(() => {
    const verifyAuth = async () => {
      // Check if token is valid
      const isValid = await checkAuth();

      // If not authenticated, redirect to login
      if (!isValid) {
        // Clear any existing tokens
        JWTManager.removeTokens();

        // Redirect to login page
        router.push('/login');
        return;
      }

      // Get current state after checkAuth has run
      const currentUser = useAuthStore.getState().user;
      const isProfileIncomplete = currentUser?.profileComplete === false;

      // Check if profile is complete, and redirect if necessary
      if (isProfileIncomplete && !isUpdateAccountPage) {
        // If profile is incomplete and not already on update account page, redirect
        setShouldRedirect(true);

        // Show toast notification about profile completion required, but only once
        // Check if we came directly from login to avoid duplicate with login toast
        const cameFromLogin = document.referrer.includes('/login');

        // Only show toast if not coming from login page or if it's a different session
        if (!cameFromLogin || !sessionStorage.getItem('profile-toast-shown')) {
          toast.warning(
            'Silakan lengkapi profil Anda terlebih dahulu untuk melanjutkan',
            {
              duration: 5000,
              id: 'profile-incomplete', // Use an ID to prevent duplicate toasts
            }
          );

          // Mark that we've shown this toast in this session
          sessionStorage.setItem('profile-toast-shown', 'true');
        }

        router.push('/update-account');
      } else {
        // Remove the else-if condition that redirects from update-account to dashboard
        // Users should be able to access the update-account page regardless of profile status
        setIsChecking(false);
      }
    };

    verifyAuth();
  }, [checkAuth, router, pathname, isUpdateAccountPage]);

  // Handle profile incomplete redirect with useEffect instead of during render
  useEffect(() => {
    const currentUser = useAuthStore.getState().user;
    const isProfileIncomplete = currentUser?.profileComplete === false;

    if (shouldRedirect && isProfileIncomplete && !isUpdateAccountPage) {
      router.push('/update-account');
    }
  }, [shouldRedirect, isUpdateAccountPage, router]);

  // Show loading while checking authentication
  if (isChecking) {
    return (
      <div className="flex h-screen w-full items-center justify-center">
        <div className="text-center">
          <div className="h-8 w-8 animate-spin rounded-full border-2 border-solid border-[#B78F38] border-t-transparent"></div>
          <p className="mt-4 text-gray-600">Memuat...</p>
        </div>
      </div>
    );
  }

  // For update-account page, allow access regardless of profile completion status
  if (isUpdateAccountPage) {
    return <>{children}</>;
  }

  // For other pages, only allow access if profile is complete
  const currentUser = useAuthStore.getState().user;
  const isProfileIncomplete = currentUser?.profileComplete === false;

  if (isProfileIncomplete) {
    // Do not call router.push here directly - it's handled in useEffect now
    return (
      <div className="flex h-screen w-full items-center justify-center">
        <div className="text-center">
          <div className="h-8 w-8 animate-spin rounded-full border-2 border-solid border-[#B78F38] border-t-transparent"></div>
          <p className="mt-4 text-gray-600">
            Mengalihkan ke halaman update profil...
          </p>
        </div>
      </div>
    );
  }

  // Render children if authenticated and profile is complete
  return <>{children}</>;
};

export default ProtectedRoute;
