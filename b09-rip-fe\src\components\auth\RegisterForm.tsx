'use client';

import { useState, useEffect } from 'react';
import { useF<PERSON>, Controller } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import Link from 'next/link';
import Image from 'next/image';
import { useAuth } from '@/hooks/auth/useAuth';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { InfoCircledIcon } from '@radix-ui/react-icons';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';
import { Label } from '@/components/ui/label';

// Validation schema
const registerSchema = z.object({
  fullname: z.string().min(2, { message: 'Nama lengkap minimal 2 karakter' }),
  email: z.string().email({ message: 'Email tidak valid' }),
  phonenum: z
    .string()
    .min(10, { message: 'Nomor telepon minimal 10 digit' })
    .regex(/^[0-9]+$/, { message: 'Nomor telepon hanya boleh berisi angka' }),
  password: z.string().min(8, { message: 'Password minimal 8 karakter' }),
  role: z.enum(['Client', 'Manager', 'HR', 'Finance', 'Operation'], {
    errorMap: () => ({ message: 'Silakan pilih role yang valid' }),
  }),
});

type RegisterFormValues = z.infer<typeof registerSchema>;

export default function RegisterForm() {
  const { signUp, loading, error } = useAuth();
  const [submitError, setSubmitError] = useState<string | null>(error);
  const [isRegistrationSuccessful, setIsRegistrationSuccessful] =
    useState(false);
  const [userType, setUserType] = useState<'Klien' | 'Karyawan'>('Klien');

  const {
    register,
    control,
    handleSubmit,
    setValue,
    formState: { errors },
  } = useForm<RegisterFormValues>({
    resolver: zodResolver(registerSchema),
    defaultValues: {
      fullname: '',
      email: '',
      phonenum: '',
      password: '',
      role: 'Client',
    },
  });

  // Set role automatically when userType changes
  useEffect(() => {
    if (userType === 'Klien') {
      setValue('role', 'Client');
    } else if (userType === 'Karyawan') {
      // Set default role for Karyawan
      setValue('role', 'Manager');
    }
  }, [userType, setValue]);

  const onSubmit = async (data: RegisterFormValues) => {
    setSubmitError(null);
    const success = await signUp(data, false);
    if (success) {
      setIsRegistrationSuccessful(true);
    } else {
      setSubmitError(error);
    }
  };

  // Success message when registration is complete
  if (isRegistrationSuccessful)
    return (
      <div className="flex min-h-screen">
        {/* Left side with illustration */}
        <div className="hidden md:flex md:w-1/2 bg-background items-center justify-center p-8">
          <div className="max-w-md">
            <h1 className="text-4xl font-bold text-[#B78F38] mb-4">
              Selamat Datang di Kasuat,
            </h1>
            <p className="text-lg text-muted-foreground">
              Buat akun baru untuk mengakses sistem
            </p>
            <Image
              src="/assets/illustrations/register-illustration.svg"
              alt="Registration illustration"
              className="mt-8 max-w-sm"
              width={300}
              height={300}
              onError={(e) => {
                // Fallback if image doesn't exist
                (e.target as HTMLImageElement).style.display = 'none';
              }}
            />
          </div>
        </div>

        {/* Right side with success message */}
        <div className="w-full md:w-1/2 flex items-center justify-center p-8">
          <div className="w-full max-w-md">
            <div className="bg-[#3C3C3C] rounded-lg p-8 shadow-lg">
              <div className="text-center mb-6">
                <div className="inline-flex items-center justify-center w-16 h-16 rounded-full bg-green-500/20 mb-4">
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    width="32"
                    height="32"
                    viewBox="0 0 24 24"
                    fill="none"
                    stroke="currentColor"
                    strokeWidth="2"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    className="text-green-400"
                  >
                    <path d="M22 11.08V12a10 10 0 1 1-5.93-9.14"></path>
                    <polyline points="22 4 12 14.01 9 11.01"></polyline>
                  </svg>
                </div>
                <h2 className="text-2xl font-semibold text-white mb-2">
                  Registrasi Berhasil!
                </h2>
                <p className="text-muted-foreground mb-6">
                  Akun Anda telah berhasil dibuat. Mohon tunggu admin untuk
                  mengaktivasi akun Anda sebelum dapat login ke sistem.
                </p>
                <Alert className="bg-amber-500/10 border border-amber-500/50 text-amber-300 mb-6">
                  <InfoCircledIcon className="h-4 w-4" />
                  <AlertTitle className="text-amber-300">
                    Aktivasi Akun
                  </AlertTitle>
                  <AlertDescription className="text-amber-200">
                    Admin akan memeriksa informasi yang Anda berikan dan
                    mengaktivasi akun Anda. Proses ini mungkin memerlukan waktu
                    beberapa saat.
                  </AlertDescription>
                </Alert>
                <Link href="/login">
                  <Button className="w-full">Kembali ke Halaman Login</Button>
                </Link>
              </div>
            </div>
          </div>
        </div>
      </div>
    );

  return (
    <div className="flex min-h-screen">
      {/* Left side with illustration */}
      <div className="hidden md:flex md:w-1/2 bg-background items-center justify-center p-8">
        <div className="max-w-md">
          <h1 className="text-4xl font-bold text-[#B78F38] mb-4">
            Selamat Datang di Kasuat,
          </h1>
          <p className="text-lg text-muted-foreground">
            Buat akun baru untuk mengakses sistem
          </p>
          <Image
            src="/assets/illustrations/register-illustration.svg"
            alt="Registration illustration"
            className="mt-8 max-w-sm"
            width={300}
            height={300}
            onError={(e) => {
              // Fallback if image doesn't exist
              (e.target as HTMLImageElement).style.display = 'none';
            }}
          />
        </div>
      </div>

      {/* Right side with form */}
      <div className="w-full md:w-1/2 flex items-center justify-center p-8">
        <div className="w-full max-w-md">
          <div className="bg-[#3C3C3C] rounded-lg p-8 shadow-lg">
            <h2 className="text-2xl font-semibold text-white text-center mb-6">
              Registrasi
            </h2>

            <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
              {/* User Type Selection */}
              <div>
                <label className="block text-sm font-medium text-[#B78F38] mb-2">
                  Tipe Pengguna
                </label>
                <RadioGroup
                  value={userType}
                  onValueChange={(value) =>
                    setUserType(value as 'Klien' | 'Karyawan')
                  }
                  className="flex space-x-4"
                >
                  <div className="flex items-center space-x-2">
                    <RadioGroupItem value="Klien" id="klien" />
                    <Label htmlFor="klien" className="text-white">
                      Klien
                    </Label>
                  </div>
                  <div className="flex items-center space-x-2">
                    <RadioGroupItem value="Karyawan" id="karyawan" />
                    <Label htmlFor="karyawan" className="text-white">
                      Karyawan
                    </Label>
                  </div>
                </RadioGroup>
              </div>

              <div>
                <label className="block text-sm font-medium text-[#B78F38] mb-1">
                  Nama Lengkap
                </label>
                <Input
                  type="text"
                  placeholder="Masukkan nama lengkap Anda"
                  className="w-full bg-white text-gray-900"
                  {...register('fullname')}
                />
                {errors.fullname && (
                  <p className="mt-1 text-sm text-red-400">
                    {errors.fullname.message}
                  </p>
                )}
              </div>

              <div>
                <label className="block text-sm font-medium text-[#B78F38] mb-1">
                  Email
                </label>
                <Input
                  type="email"
                  placeholder="Masukkan alamat email Anda"
                  className="w-full bg-white text-gray-900"
                  {...register('email')}
                />
                {errors.email && (
                  <p className="mt-1 text-sm text-red-400">
                    {errors.email.message}
                  </p>
                )}
              </div>

              <div>
                <label className="block text-sm font-medium text-[#B78F38] mb-1">
                  Nomor Telepon
                </label>
                <Input
                  type="tel"
                  placeholder="Masukkan nomor telepon Anda (contoh: 6281234567890)"
                  className="w-full bg-white text-gray-900"
                  {...register('phonenum')}
                />
                {errors.phonenum && (
                  <p className="mt-1 text-sm text-red-400">
                    {errors.phonenum.message}
                  </p>
                )}
              </div>

              <div>
                <label className="block text-sm font-medium text-[#B78F38] mb-1">
                  Password
                </label>
                <Input
                  type="password"
                  placeholder="Masukkan password Anda"
                  className="w-full bg-white text-gray-900"
                  {...register('password')}
                />
                {errors.password && (
                  <p className="mt-1 text-sm text-red-400">
                    {errors.password.message}
                  </p>
                )}
              </div>

              {/* Only show role dropdown for Karyawan */}
              {userType === 'Karyawan' && (
                <div>
                  <label className="block text-sm font-medium text-[#B78F38] mb-1">
                    Role <span className="text-red-400">*</span>
                  </label>
                  <Controller
                    name="role"
                    control={control}
                    render={({ field }) => (
                      <select
                        {...field}
                        className="w-full rounded-md border-0 py-2 px-3 bg-white text-gray-900"
                        required
                      >
                        <option value="" disabled>
                          Pilih Role Anda
                        </option>
                        <option value="Manager">Manager</option>
                        <option value="HR">HR</option>
                        <option value="Finance">Finance</option>
                        <option value="Operation">Operation</option>
                      </select>
                    )}
                  />
                  {errors.role && (
                    <p className="mt-1 text-sm text-red-400">
                      {errors.role.message}
                    </p>
                  )}
                </div>
              )}

              {submitError && (
                <Alert className="bg-red-500/10 border border-red-500/50">
                  <AlertTitle className="text-red-400">
                    Registrasi Gagal
                  </AlertTitle>
                  <AlertDescription className="text-red-400">
                    {submitError ===
                    'A user with this email address has already been registered'
                      ? 'Email ini sudah terdaftar. Silakan gunakan email lain atau login dengan email ini.'
                      : submitError || 'Registrasi gagal. Silakan coba lagi.'}
                  </AlertDescription>
                </Alert>
              )}

              <Button type="submit" className="w-full" disabled={loading}>
                {loading ? 'Memproses...' : 'Registrasi'}
              </Button>

              <div className="text-center text-sm text-white">
                <span>Sudah memiliki akun? </span>
                <Link href="/login" className="text-[#B78F38] hover:underline">
                  Login disini
                </Link>
              </div>
            </form>
          </div>
        </div>
      </div>
    </div>
  );
}
