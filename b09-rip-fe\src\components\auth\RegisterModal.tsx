'use client';

import { useState, useEffect } from 'react';
import { useF<PERSON>, Controller } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { useAuth } from '@/hooks/auth/useAuth';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { InfoCircledIcon } from '@radix-ui/react-icons';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';
import { Label } from '@/components/ui/label';
import Image from 'next/image';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';

// Validation schema
const registerSchema = z.object({
  fullname: z.string().min(2, { message: '<PERSON>a lengkap minimal 2 karakter' }),
  email: z.string().email({ message: 'Email tidak valid' }),
  phonenum: z
    .string()
    .min(10, { message: 'Nomor telepon minimal 10 digit' })
    .regex(/^[0-9]+$/, { message: 'Nomor telepon hanya boleh berisi angka' }),
  password: z.string().min(8, { message: 'Password minimal 8 karakter' }),
  role: z.enum(['Client', 'Manager', 'HR', 'Finance', 'Operation'], {
    errorMap: () => ({ message: 'Silakan pilih role yang valid' }),
  }),
});

type RegisterFormValues = z.infer<typeof registerSchema>;

interface RegisterModalProps {
  isOpen: boolean;
  onClose: () => void;
  onOpenLogin?: () => void;
}

export default function RegisterModal({ isOpen, onClose, onOpenLogin }: RegisterModalProps) {
  const { signUp, loading, error } = useAuth();
  const [submitError, setSubmitError] = useState<string | null>(error);
  const [isRegistrationSuccessful, setIsRegistrationSuccessful] = useState(false);
  const [userType, setUserType] = useState<'Klien' | 'Karyawan'>('Klien');

  const {
    register,
    control,
    handleSubmit,
    setValue,
    formState: { errors },
    reset,
  } = useForm<RegisterFormValues>({
    resolver: zodResolver(registerSchema),
    defaultValues: {
      fullname: '',
      email: '',
      phonenum: '',
      password: '',
      role: 'Client',
    },
  });

  // Set role automatically when userType changes
  useEffect(() => {
    if (userType === 'Klien') {
      setValue('role', 'Client');
    } else if (userType === 'Karyawan') {
      setValue('role', 'Manager');
    }
  }, [userType, setValue]);

  // Clear form when modal closes
  useEffect(() => {
    if (!isOpen) {
      reset();
      setSubmitError(null);
      setIsRegistrationSuccessful(false);
      setUserType('Klien');
    }
  }, [isOpen, reset]);

  const onSubmit = async (data: RegisterFormValues) => {
    setSubmitError(null);
    const success = await signUp(data, false);
    if (success) {
      setIsRegistrationSuccessful(true);
    } else {
      setSubmitError(error);
    }
  };

  // Success message when registration is complete
  if (isRegistrationSuccessful) {
    return (
      <Dialog open={isOpen} onOpenChange={onClose}>
        <DialogContent className="sm:max-w-4xl bg-white border-gray-200 shadow-2xl p-0 overflow-hidden">
          <div className="grid md:grid-cols-2 min-h-[500px]">
            {/* Left Side - Success Image */}
            <div className="bg-gradient-to-br from-green-500/10 to-green-600/5 p-8 flex items-center justify-center relative overflow-hidden">
              {/* Background Pattern */}
              <div className="absolute inset-0 opacity-5">
                <div className="absolute top-10 left-10 w-32 h-32 border border-green-500/20 rounded-full"></div>
                <div className="absolute bottom-10 right-10 w-24 h-24 border border-green-500/20 rounded-full"></div>
              </div>

              {/* Success Illustration */}
              <div className="relative z-10 flex items-center justify-center w-full h-full">
                <Image
                  src="/assets/illustrations/login-illustration.svg"
                  alt="Success Illustration"
                  width={300}
                  height={300}
                  className="w-full h-auto max-w-sm"
                  priority
                />
              </div>
            </div>

            {/* Right Side - Success Message */}
            <div className="p-8 flex flex-col justify-center">
              <DialogHeader className="mb-6">
                <DialogTitle className="text-3xl font-semibold text-gray-900 text-center">
                  Permintaan Akses Terkirim!
                </DialogTitle>
              </DialogHeader>

              <div className="text-center space-y-6">
                <p className="text-gray-600 text-lg">
                  Akun Anda telah berhasil dibuat. Mohon tunggu admin untuk
                  mengaktivasi akun Anda sebelum dapat login ke portal.
                </p>

                <Alert className="bg-amber-500/10 border border-amber-500/50">
                  <InfoCircledIcon className="h-4 w-4 text-amber-600" />
                  <AlertTitle className="text-amber-700">
                    Menunggu Aktivasi
                  </AlertTitle>
                  <AlertDescription className="text-amber-600">
                    Admin akan memeriksa informasi yang Anda berikan dan
                    mengaktivasi akun Anda. Proses ini mungkin memerlukan waktu
                    beberapa saat.
                  </AlertDescription>
                </Alert>

                <Button
                  className="w-full bg-[#B78F38] hover:bg-[#A67D32]"
                  onClick={() => {
                    setIsRegistrationSuccessful(false);
                    onClose();
                  }}
                >
                  Tutup
                </Button>
              </div>
            </div>
          </div>
        </DialogContent>
      </Dialog>
    );
  }

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-5xl bg-white border-gray-200 shadow-2xl p-0 overflow-hidden max-h-[90vh]">
        <div className="grid md:grid-cols-2 min-h-[600px]">
          {/* Left Side - Image */}
          <div className="bg-gradient-to-br from-[#B78F38]/10 to-[#B78F38]/5 p-8 flex items-center justify-center relative overflow-hidden">
            {/* Background Pattern */}
            <div className="absolute inset-0 opacity-5">
              <div className="absolute top-10 left-10 w-32 h-32 border border-[#B78F38]/20 rounded-full"></div>
              <div className="absolute bottom-10 right-10 w-24 h-24 border border-[#B78F38]/20 rounded-full"></div>
              <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-40 h-40 border border-[#B78F38]/20 rounded-full"></div>
            </div>

            {/* Login Illustration SVG */}
            <div className="relative z-10 flex items-center justify-center w-full h-full">
              <Image
                src="/assets/illustrations/login-illustration.svg"
                alt="Login Illustration"
                width={300}
                height={300}
                className="w-full h-auto max-w-sm"
                priority
              />
            </div>
          </div>

          {/* Right Side - Form */}
          <div className="p-8 flex flex-col justify-center overflow-y-auto">
            <DialogHeader className="mb-6">
              <DialogTitle className="text-3xl font-semibold text-gray-900 text-center">
                Minta Akses Portal
              </DialogTitle>
            </DialogHeader>

        <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
          {/* User Type Selection */}
          <div>
            <label className="block text-sm font-medium text-[#B78F38] mb-2">
              Tipe Pengguna
            </label>
            <RadioGroup
              value={userType}
              onValueChange={(value) =>
                setUserType(value as 'Klien' | 'Karyawan')
              }
              className="flex space-x-4"
            >
              <div className="flex items-center space-x-2">
                <RadioGroupItem value="Klien" id="klien" />
                <Label htmlFor="klien" className="text-gray-700">
                  Klien
                </Label>
              </div>
              <div className="flex items-center space-x-2">
                <RadioGroupItem value="Karyawan" id="karyawan" />
                <Label htmlFor="karyawan" className="text-gray-700">
                  Karyawan
                </Label>
              </div>
            </RadioGroup>
          </div>

          <div>
            <label className="block text-sm font-medium text-[#B78F38] mb-1">
              Nama Lengkap
            </label>
            <Input
              type="text"
              placeholder="Masukkan nama lengkap Anda"
              className="w-full bg-gray-50 border-gray-200 text-gray-900 focus:border-[#B78F38] focus:ring-[#B78F38]"
              {...register('fullname')}
            />
            {errors.fullname && (
              <p className="mt-1 text-sm text-red-500">
                {errors.fullname.message}
              </p>
            )}
          </div>

          <div>
            <label className="block text-sm font-medium text-[#B78F38] mb-1">
              Email
            </label>
            <Input
              type="email"
              placeholder="Masukkan alamat email Anda"
              className="w-full bg-gray-50 border-gray-200 text-gray-900 focus:border-[#B78F38] focus:ring-[#B78F38]"
              {...register('email')}
            />
            {errors.email && (
              <p className="mt-1 text-sm text-red-500">
                {errors.email.message}
              </p>
            )}
          </div>

          <div>
            <label className="block text-sm font-medium text-[#B78F38] mb-1">
              Nomor Telepon
            </label>
            <Input
              type="tel"
              placeholder="Masukkan nomor telepon Anda (contoh: 6281234567890)"
              className="w-full bg-gray-50 border-gray-200 text-gray-900 focus:border-[#B78F38] focus:ring-[#B78F38]"
              {...register('phonenum')}
            />
            {errors.phonenum && (
              <p className="mt-1 text-sm text-red-500">
                {errors.phonenum.message}
              </p>
            )}
          </div>

          <div>
            <label className="block text-sm font-medium text-[#B78F38] mb-1">
              Password
            </label>
            <Input
              type="password"
              placeholder="Masukkan password Anda"
              className="w-full bg-gray-50 border-gray-200 text-gray-900 focus:border-[#B78F38] focus:ring-[#B78F38]"
              {...register('password')}
            />
            {errors.password && (
              <p className="mt-1 text-sm text-red-500">
                {errors.password.message}
              </p>
            )}
          </div>

          {/* Only show role dropdown for Karyawan */}
          {userType === 'Karyawan' && (
            <div>
              <label className="block text-sm font-medium text-[#B78F38] mb-1">
                Role <span className="text-red-400">*</span>
              </label>
              <Controller
                name="role"
                control={control}
                render={({ field }) => (
                  <select
                    {...field}
                    className="w-full rounded-md border border-gray-200 py-2 px-3 bg-gray-50 text-gray-900 focus:border-[#B78F38] focus:ring-[#B78F38]"
                    required
                  >
                    <option value="" disabled>
                      Pilih Role Anda
                    </option>
                    <option value="Manager">Manager</option>
                    <option value="HR">HR</option>
                    <option value="Finance">Finance</option>
                    <option value="Operation">Operation</option>
                  </select>
                )}
              />
              {errors.role && (
                <p className="mt-1 text-sm text-red-500">
                  {errors.role.message}
                </p>
              )}
            </div>
          )}

          {submitError && (
            <Alert className="bg-red-500/10 border border-red-500/50">
              <AlertTitle className="text-red-400">
                Registrasi Gagal
              </AlertTitle>
              <AlertDescription className="text-red-400">
                {submitError ===
                'A user with this email address has already been registered'
                  ? 'Email ini sudah terdaftar. Silakan gunakan email lain atau login dengan email ini.'
                  : submitError || 'Registrasi gagal. Silakan coba lagi.'}
              </AlertDescription>
            </Alert>
          )}

          <Button type="submit" className="w-full" disabled={loading}>
            {loading ? 'Memproses...' : 'Registrasi'}
          </Button>

            <div className="text-center text-sm text-gray-600">
              <span>Sudah memiliki akun? </span>
              <button
                type="button"
                className="text-[#B78F38] hover:underline font-medium"
                onClick={() => {
                  onClose();
                  onOpenLogin?.();
                }}
              >
                Login disini
              </button>
            </div>
          </form>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}
