//src\components\auth\RequireRole.tsx

'use client';

import { useRBAC } from '@/hooks/useRBAC';
import { UserRole } from '@/types/auth';
import ErrorPage from '@/components/error/error-page';

// Default error messages
const DEFAULT_ERROR_TITLE = 'A<PERSON><PERSON>';
const DEFAULT_ERROR_MESSAGE =
  'Anda tidak memiliki izin untuk mengakses halaman ini.';

interface RequireRoleProps {
  allowedRoles: UserRole[];
  children: React.ReactNode;
  errorTitle?: string;
  errorMessage?: string;
  showHomeButton?: boolean;
  showBackButton?: boolean;
  homeHref?: string;
  homeLabel?: string;
  backLabel?: string;
}

export const RequireRole: React.FC<RequireRoleProps> = ({
  allowedRoles,
  children,
  errorTitle = DEFAULT_ERROR_TITLE,
  errorMessage = DEFAULT_ERROR_MESSAGE,
  showHomeButton = true,
  showBackButton = true,
  homeHref = '/dashboard',
  homeLabel = 'Kemba<PERSON> ke Dashboard',
  backLabel = 'Kembali',
}) => {
  const { hasRole, loading } = useRBAC();

  // Show loading state while checking role
  if (loading) {
    return (
      <div className="flex h-screen w-full items-center justify-center">
        <div className="text-center">
          <div className="h-8 w-8 animate-spin rounded-full border-2 border-solid border-[#B78F38] border-t-transparent"></div>
          <p className="mt-4 text-gray-600">Memuat...</p>
        </div>
      </div>
    );
  }

  // Show error page if user doesn't have required role
  if (!hasRole(allowedRoles) && !hasRole(['Admin'])) {
    return (
      <ErrorPage
        statusCode="403"
        title={errorTitle}
        message={errorMessage}
        showHomeButton={showHomeButton}
        showBackButton={showBackButton}
        homeHref={homeHref}
        homeLabel={homeLabel}
        backLabel={backLabel}
      />
    );
  }

  return <>{children}</>;
};

// Convenience component for admin-only routes
export const RequireAdmin: React.FC<Omit<RequireRoleProps, 'allowedRoles'>> = (
  props
) => {
  return <RequireRole allowedRoles={['Admin']} {...props} />;
};
