'use client';

import { useEffect } from 'react';
import { authService } from '@/lib/auth/auth-service';

/**
 * This component synchronizes tokens between localStorage and cookies
 * It should be included once in the application layout
 */
export default function TokenSynchronizer() {
  useEffect(() => {
    // Sync tokens on mount
    authService.syncTokenStorages();

    // Re-sync when focus returns to the window (user switches back to the tab)
    const handleFocus = () => {
      authService.syncTokenStorages();
    };

    window.addEventListener('focus', handleFocus);

    return () => {
      window.removeEventListener('focus', handleFocus);
    };
  }, []);

  // This component doesn't render anything
  return null;
}
