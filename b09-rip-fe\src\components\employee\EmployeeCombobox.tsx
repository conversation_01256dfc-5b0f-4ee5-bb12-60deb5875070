'use client';

import * as React from 'react';
import { Check, ChevronsUpDown } from 'lucide-react';
import { cn } from '@/lib/utils';
import { Button } from '@/components/ui/button';
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
} from '@/components/ui/command';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover';
import { Employee } from '@/types/employee';
import { employeeApi } from '@/lib/api/employee';
import { toast } from 'sonner';

interface EmployeeComboboxProps {
  value: string; // employee_id
  onSelect: (employeeId: string, fullName: string) => void;
  placeholder?: string;
  className?: string;
  disabled?: boolean;
}

export function EmployeeCombobox({
  value,
  onSelect,
  placeholder = 'Pilih karyawan...',
  className,
  disabled = false,
}: EmployeeComboboxProps) {
  const [open, setOpen] = React.useState(false);
  const [employees, setEmployees] = React.useState<Employee[]>([]);
  const [loading, setLoading] = React.useState(false);
  const [searchQuery, setSearchQuery] = React.useState('');
  const [error, setError] = React.useState<string | null>(null);

  // Load employees on component mount
  React.useEffect(() => {
    // Don't fetch if disabled (optimization)
    if (disabled && value) return;

    const fetchEmployees = async () => {
      setLoading(true);
      setError(null);
      try {
        // Request with a large pageSize to get all employees at once
        const response = await employeeApi.getEmployees({
          page: 1,
          pageSize: 1000, // Request a large number to get all employees
          search: searchQuery,
        });

        if (response.success && response.data) {
          setEmployees(response.data.items || []);
        }
      } catch (error: unknown) {
        console.error('Error fetching employees:', error);
        const errorObj = error as { response?: { status?: number } };
        if (errorObj.response?.status === 403) {
          setError('Anda tidak memiliki akses untuk melihat daftar karyawan');
          toast.error(
            'Anda tidak memiliki akses untuk melihat daftar karyawan'
          );
        } else {
          setError('Terjadi kesalahan saat mengambil data karyawan');
          toast.error('Terjadi kesalahan saat mengambil data karyawan');
        }
      } finally {
        setLoading(false);
      }
    };

    fetchEmployees();
  }, [searchQuery, disabled, value]);

  // Handle search input change with debounce
  const handleSearchChange = React.useCallback((value: string) => {
    // Use a timeout to debounce the search
    const timeoutId = setTimeout(() => {
      setSearchQuery(value);
    }, 300);

    return () => clearTimeout(timeoutId);
  }, []);

  // Find the selected employee label
  const selectedEmployee = employees.find((emp) => emp.id === value);
  const displayValue = selectedEmployee
    ? selectedEmployee.profile.fullname
    : placeholder;

  // Don't allow opening if disabled or there's an error
  const handleOpenChange = (open: boolean) => {
    if (!disabled && !error) {
      setOpen(open);
    }
  };

  return (
    <Popover open={open} onOpenChange={handleOpenChange}>
      <PopoverTrigger asChild>
        <Button
          variant="outline"
          role="combobox"
          aria-expanded={open}
          className={cn(
            'w-full justify-between border-input text-foreground',
            (disabled || error) && 'opacity-70 cursor-not-allowed',
            className
          )}
          disabled={disabled || !!error}
        >
          {error ? 'Akses ditolak' : displayValue}
          <ChevronsUpDown
            className={cn(
              'opacity-50 h-4 w-4 ml-2 shrink-0',
              (disabled || error) && 'opacity-30'
            )}
          />
        </Button>
      </PopoverTrigger>
      {!disabled && !error && (
        <PopoverContent className="w-full p-0">
          <Command>
            <CommandInput
              placeholder="Cari karyawan..."
              className="h-9"
              onValueChange={handleSearchChange}
            />
            <CommandList>
              {loading ? (
                <CommandEmpty>Loading...</CommandEmpty>
              ) : employees.length === 0 ? (
                <CommandEmpty>Karyawan tidak ditemukan.</CommandEmpty>
              ) : (
                <CommandGroup>
                  {employees.map((employee) => (
                    <CommandItem
                      key={employee.id}
                      value={employee.profile.fullname}
                      onSelect={() => {
                        onSelect(employee.id, employee.profile.fullname);
                        setOpen(false);
                      }}
                    >
                      <span>{employee.profile.fullname}</span>
                      <Check
                        className={cn(
                          'ml-auto h-4 w-4',
                          value === employee.id ? 'opacity-100' : 'opacity-0'
                        )}
                      />
                    </CommandItem>
                  ))}
                </CommandGroup>
              )}
            </CommandList>
          </Command>
        </PopoverContent>
      )}
    </Popover>
  );
}
