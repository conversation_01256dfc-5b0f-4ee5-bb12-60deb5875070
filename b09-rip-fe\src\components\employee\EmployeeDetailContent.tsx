import { useEffect, useState, useCallback } from 'react';
import { useRouter } from 'next/navigation';
import { toast } from 'sonner';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Employee } from '@/types/employee';
import { getEmployeeById, updateEmployee } from '@/lib/api/employee';
import { EmployeeInformationCard } from './EmployeeInformationCard';
import { Skeleton } from '@/components/ui/skeleton';
import { ArrowLeft, Save } from 'lucide-react';

interface EmployeeDetailContentProps {
  id: string;
}

export function EmployeeDetailContent({ id }: EmployeeDetailContentProps) {
  const router = useRouter();
  const [employee, setEmployee] = useState<Employee | null>(null);
  const [loading, setLoading] = useState(true);
  const [updating, setUpdating] = useState(false);

  const fetchEmployee = useCallback(async () => {
    try {
      setLoading(true);
      const response = await getEmployeeById(id);
      if (response.success) {
        setEmployee(response.data);
      } else {
        toast.error(response.message || 'Failed to fetch employee details');
      }
    } catch (error) {
      toast.error('Failed to fetch employee details');
      console.error('Error fetching employee:', error);
    } finally {
      setLoading(false);
    }
  }, [id]);

  useEffect(() => {
    fetchEmployee();
  }, [fetchEmployee]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!employee) return;

    try {
      setUpdating(true);
      const updateData = {
        dob: employee.dob,
        address: employee.address,
        bank_account: employee.bank_account,
        bank_name: employee.bank_name,
        employment_status: employee.employment_status,
        presence_status: employee.presence_status,
        department: employee.department,
        start_date: employee.start_date,
      };

      const response = await updateEmployee(id, updateData);
      if (response.success) {
        setEmployee(response.data);
        toast.success('Employee updated successfully');
      } else {
        toast.error(response.message || 'Failed to update employee');
      }
    } catch (error) {
      toast.error('Failed to update employee');
      console.error('Error updating employee:', error);
    } finally {
      setUpdating(false);
    }
  };

  if (loading) {
    return (
      <div className="space-y-6">
        <div className="flex justify-between items-center pb-6 border-b border-gray-200">
          <Skeleton className="h-8 w-48" />
          <div className="flex gap-3">
            <Skeleton className="h-10 w-24" />
            <Skeleton className="h-10 w-32" />
          </div>
        </div>
        <Card className="border border-gray-200 shadow-sm">
          <CardHeader className="bg-gray-50 border-b border-gray-200">
            <Skeleton className="h-6 w-32" />
          </CardHeader>
          <CardContent className="p-6">
            <div className="space-y-6">
              <div className="grid gap-6 md:grid-cols-2">
                {[1, 2, 3, 4].map((i) => (
                  <div key={i} className="space-y-2">
                    <Skeleton className="h-4 w-24" />
                    <Skeleton className="h-10 w-full rounded-md" />
                  </div>
                ))}
              </div>
              <div className="flex justify-end gap-3">
                <Skeleton className="h-10 w-24" />
                <Skeleton className="h-10 w-32" />
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  if (!employee) {
    return (
      <div className="flex flex-col items-center justify-center p-12 bg-gray-50 rounded-lg border border-gray-200">
        <h2 className="text-xl font-semibold text-[#AB8B3B] mb-2">
          Employee not found
        </h2>
        <p className="text-gray-600 mb-6 text-center max-w-md">
          The employee you are looking for does not exist or has been removed.
        </p>
        <Button
          onClick={() => router.push('/employee')}
          className="bg-[#AB8B3B] text-white hover:bg-[#8B6B2B]"
        >
          <ArrowLeft className="h-4 w-4 mr-2" />
          Back to Employee List
        </Button>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center pb-6 border-b border-gray-200">
        <h1 className="text-2xl font-bold text-[#AB8B3B]">
          Update My Information
        </h1>
        <Button
          variant="outline"
          size="sm"
          onClick={() => router.push('/employee')}
          className="border-[#AB8B3B] text-[#AB8B3B] hover:bg-[#AB8B3B]/10"
        >
          <ArrowLeft className="h-4 w-4 mr-2" />
          Cancel
        </Button>
      </div>

      <Card className="border border-gray-200 shadow-sm">
        <CardHeader className="bg-gray-50 border-b border-gray-200">
          <CardTitle className="text-[#AB8B3B]">Personal Information</CardTitle>
        </CardHeader>
        <CardContent className="p-6">
          <form onSubmit={handleSubmit} className="space-y-6">
            <EmployeeInformationCard
              employee={employee}
              onEmployeeChange={setEmployee}
              readOnlyFields={[
                'fullname',
                'email',
                'phonenum',
                'role',
                'employment_status',
                'presence_status',
                'department',
                'start_date',
              ]}
            />
            <div className="flex justify-end gap-3">
              <Button
                type="button"
                variant="outline"
                className="border-[#AB8B3B] text-[#AB8B3B] hover:bg-[#AB8B3B]/10"
                onClick={() => router.push('/employee')}
              >
                Cancel
              </Button>
              <Button
                type="submit"
                disabled={updating}
                className="bg-[#AB8B3B] text-white hover:bg-[#8B6B2B] disabled:bg-[#AB8B3B]/50"
              >
                <Save className="h-4 w-4 mr-2" />
                {updating ? 'Updating...' : 'Save Changes'}
              </Button>
            </div>
          </form>
        </CardContent>
      </Card>
    </div>
  );
}
