import { useRouter } from 'next/navigation';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { UpdateEmployeeDto } from '@/types/employee';
import { EmployeeInformationCard } from './EmployeeInformationCard';
import { ArrowLeft, Save } from 'lucide-react';
import { useEmployeeDetail } from '@/hooks/useEmployeeDetail';
import { Skeleton } from '@/components/ui/skeleton';
import { toast } from 'sonner';
import { PageTitle } from '@/components/ui/PageTitle';
import { BackButton } from '@/components/ui/BackButton';

interface EmployeeEditContentProps {
  id: string;
}

export function EmployeeEditContent({ id }: EmployeeEditContentProps) {
  const router = useRouter();
  const { employee, loading, updating, setEmployee, handleSaveChanges } =
    useEmployeeDetail(id);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!employee) return;

    // Validation
    if (!employee.dob) {
      toast.error('Date of birth is required');
      return;
    }
    if (!employee.address) {
      toast.error('Address is required');
      return;
    }
    if (!employee.bank_account) {
      toast.error('Bank account is required');
      return;
    }
    if (!employee.bank_name) {
      toast.error('Bank name is required');
      return;
    }
    if (!employee.start_date) {
      toast.error('Start date is required');
      return;
    }

    const updateData: UpdateEmployeeDto = {
      dob: employee.dob,
      address: employee.address,
      bank_account: employee.bank_account,
      bank_name: employee.bank_name,
      employment_status: employee.employment_status,
      presence_status: employee.presence_status,
      department: employee.department,
      start_date: employee.start_date,
      salary_id: employee.salary_id,
    };

    const success = await handleSaveChanges(updateData);
    if (success) {
      toast.success('Employee updated successfully');
      router.push(`/employee/${id}`);
    }
  };

  if (loading) {
    return (
      <div className="space-y-6">
        <div className="flex justify-between items-center pb-6 border-b border-gray-200">
          <Skeleton className="h-8 w-48" />
          <div className="flex gap-3">
            <Skeleton className="h-10 w-24" />
            <Skeleton className="h-10 w-24" />
          </div>
        </div>
        <Card className="border border-gray-200 shadow-sm">
          <CardHeader className="bg-gray-50 border-b border-gray-200">
            <Skeleton className="h-6 w-32" />
          </CardHeader>
          <CardContent className="p-6">
            <div className="space-y-6">
              <div className="grid gap-6 md:grid-cols-2">
                {[1, 2, 3, 4].map((i) => (
                  <div key={i} className="space-y-2">
                    <Skeleton className="h-4 w-24" />
                    <Skeleton className="h-10 w-full rounded-md" />
                  </div>
                ))}
              </div>
              <div className="grid gap-6 md:grid-cols-2">
                {[1, 2, 3, 4].map((i) => (
                  <div key={i} className="space-y-2">
                    <Skeleton className="h-4 w-24" />
                    <Skeleton className="h-10 w-full rounded-md" />
                  </div>
                ))}
              </div>
              <div className="flex justify-end gap-3">
                <Skeleton className="h-10 w-24" />
                <Skeleton className="h-10 w-32" />
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  if (!employee) {
    return (
      <div className="flex flex-col items-center justify-center p-12 bg-gray-50 rounded-lg border border-gray-200">
        <h2 className="text-xl font-semibold text-[#AB8B3B] mb-2">
          Employee not found
        </h2>
        <p className="text-gray-600 mb-6 text-center max-w-md">
          The employee you are trying to edit does not exist or has been
          removed.
        </p>
        <Button
          onClick={() => router.push('/employee')}
          variant="default"
          leftIcon={<ArrowLeft />}
        >
          Back to Employee List
        </Button>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center pb-6 border-b border-gray-200">
        <div className="flex items-center gap-4">
          <BackButton onClick={() => router.push(`/employee/${id}`)} />
          <PageTitle title="Edit Informasi Karyawan" />
        </div>
      </div>

      <Card className="border border-gray-200 shadow-sm">
        <CardHeader className="bg-gray-50 border-b border-gray-200">
          <CardTitle className="text-[#AB8B3B]">
            Edit Informasi Karyawan
          </CardTitle>
        </CardHeader>
        <CardContent className="p-6">
          <form onSubmit={handleSubmit} className="space-y-6">
            <EmployeeInformationCard
              employee={employee}
              onEmployeeChange={setEmployee}
              readOnlyFields={[
                'fullname',
                'email',
                'phonenum',
                'role',
                'presence_status',
              ]}
            />
            <div className="flex justify-end gap-3">
              <Button
                type="button"
                variant="cancel"
                onClick={() => router.push(`/employee/${id}`)}
              >
                Cancel
              </Button>
              <Button
                type="submit"
                disabled={updating}
                variant="default"
                leftIcon={<Save />}
              >
                {updating ? 'Updating...' : 'Save Changes'}
              </Button>
            </div>
          </form>
        </CardContent>
      </Card>
    </div>
  );
}
