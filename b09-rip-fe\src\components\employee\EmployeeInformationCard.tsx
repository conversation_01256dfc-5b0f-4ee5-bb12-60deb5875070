import { Input } from '@/components/ui/input';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import {
  Employee,
  Department,
  EmploymentStatus,
  PresenceStatus,
} from '@/types/employee';
import { InfoCircledIcon } from '@radix-ui/react-icons';
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from '@/components/ui/tooltip';

interface EmployeeInformationCardProps {
  employee: Employee;
  onEmployeeChange: (employee: Employee) => void;
  readOnly?: boolean;
  readOnlyFields?: string[];
}

export function EmployeeInformationCard({
  employee,
  onEmployeeChange,
  readOnly = false,
  readOnlyFields = [],
}: EmployeeInformationCardProps) {
  // Ensure profile exists, even if API doesn't return it
  if (!employee.profile) {
    employee.profile = {
      fullname: '',
      phonenum: '',
      role: '',
    };
  }

  const handleChange = (
    field:
      | keyof Employee
      | 'profile.fullname'
      | 'profile.phonenum'
      | 'profile.role',
    value: string | Department | EmploymentStatus | PresenceStatus
  ) => {
    if (readOnly) return;

    // Check if the field is in the readOnlyFields array
    const fieldName = field.includes('.') ? field.split('.')[1] : field;
    if (readOnlyFields.includes(fieldName as string)) return;

    if (field.includes('.')) {
      const [parent, child] = field.split('.');
      if (parent === 'profile') {
        onEmployeeChange({
          ...employee,
          profile: {
            ...employee.profile,
            [child]: value,
          },
        });
      }
    } else {
      onEmployeeChange({
        ...employee,
        [field]: value,
      });
    }
  };

  // Check if a field should be read-only
  const isFieldReadOnly = (field: string): boolean => {
    return readOnly || readOnlyFields.includes(field);
  };

  // Add helper function to render enum labels in a more user-friendly way
  const formatEnumValue = (value: string): string => {
    return value
      .split('_')
      .map((word) => word.charAt(0).toUpperCase() + word.slice(1).toLowerCase())
      .join(' ');
  };

  // Check if a value is a placeholder
  const isPlaceholder = (
    field: string,
    value: string | null | undefined
  ): boolean => {
    if (!value) return false;

    switch (field) {
      case 'dob':
        return value === '1990-01-01';
      case 'address':
      case 'bank_account':
      case 'bank_name':
        return value === 'Not provided';
      default:
        return false;
    }
  };

  // Placeholder tooltip component
  const PlaceholderIndicator = () => (
    <TooltipProvider>
      <Tooltip>
        <TooltipTrigger asChild>
          <InfoCircledIcon className="h-4 w-4 text-amber-500 ml-2 inline-block" />
        </TooltipTrigger>
        <TooltipContent>
          <p className="text-xs">
            Ini adalah nilai sementara. Silakan perbarui dengan informasi Anda
            yang sebenarnya.
          </p>
        </TooltipContent>
      </Tooltip>
    </TooltipProvider>
  );

  return (
    <div className="space-y-6">
      <div className="grid gap-6 md:grid-cols-2">
        <div className="space-y-2">
          <label className="text-sm font-medium text-gray-700">
            Nama Lengkap
          </label>
          <Input
            value={employee.profile.fullname}
            onChange={(e) => handleChange('profile.fullname', e.target.value)}
            disabled={isFieldReadOnly('fullname')}
            className={`border-gray-200 focus:border-[#AB8B3B] focus:ring-[#AB8B3B] ${
              isFieldReadOnly('fullname') ? 'bg-gray-50' : ''
            }`}
          />
        </div>
        <div className="space-y-2">
          <label className="text-sm font-medium text-gray-700">Email</label>
          <Input
            value={employee.email}
            onChange={(e) => handleChange('email', e.target.value)}
            disabled={isFieldReadOnly('email')}
            className={`border-gray-200 focus:border-[#AB8B3B] focus:ring-[#AB8B3B] ${
              isFieldReadOnly('email') ? 'bg-gray-50' : ''
            }`}
          />
        </div>
        <div className="space-y-2">
          <label className="text-sm font-medium text-gray-700">
            Nomor Telepon
          </label>
          <Input
            value={employee.profile.phonenum}
            onChange={(e) => handleChange('profile.phonenum', e.target.value)}
            disabled={isFieldReadOnly('phonenum')}
            className={`border-gray-200 focus:border-[#AB8B3B] focus:ring-[#AB8B3B] ${
              isFieldReadOnly('phonenum') ? 'bg-gray-50' : ''
            }`}
          />
        </div>
        <div className="space-y-2">
          <label className="text-sm font-medium text-gray-700">Jabatan</label>
          <Input
            value={employee.profile.role}
            onChange={(e) => handleChange('profile.role', e.target.value)}
            disabled={isFieldReadOnly('role')}
            className={`border-gray-200 focus:border-[#AB8B3B] focus:ring-[#AB8B3B] ${
              isFieldReadOnly('role') ? 'bg-gray-50' : ''
            }`}
          />
        </div>
      </div>

      <div className="grid gap-6 md:grid-cols-2">
        <div className="space-y-2">
          <label className="text-sm font-medium text-gray-700">
            Tanggal Lahir
            {isPlaceholder('dob', employee.dob) && <PlaceholderIndicator />}
          </label>
          <Input
            type="date"
            value={employee.dob}
            onChange={(e) => handleChange('dob', e.target.value)}
            disabled={isFieldReadOnly('dob')}
            className={`border-gray-200 focus:border-[#AB8B3B] focus:ring-[#AB8B3B] ${
              isFieldReadOnly('dob') ? 'bg-gray-50' : ''
            } ${isPlaceholder('dob', employee.dob) ? 'border-amber-300' : ''}`}
          />
          {isPlaceholder('dob', employee.dob) && (
            <p className="text-xs text-amber-500">
              Ini terlihat seperti tanggal sementara. Silakan masukkan tanggal
              lahir Anda yang sebenarnya.
            </p>
          )}
        </div>
        <div className="space-y-2">
          <label className="text-sm font-medium text-gray-700">
            Alamat
            {isPlaceholder('address', employee.address) && (
              <PlaceholderIndicator />
            )}
          </label>
          <Input
            value={employee.address}
            onChange={(e) => handleChange('address', e.target.value)}
            disabled={isFieldReadOnly('address')}
            className={`border-gray-200 focus:border-[#AB8B3B] focus:ring-[#AB8B3B] ${
              isFieldReadOnly('address') ? 'bg-gray-50' : ''
            } ${isPlaceholder('address', employee.address) ? 'border-amber-300' : ''}`}
          />
          {isPlaceholder('address', employee.address) && (
            <p className="text-xs text-amber-500">
              Silakan masukkan alamat Anda yang sebenarnya.
            </p>
          )}
        </div>
        <div className="space-y-2">
          <label className="text-sm font-medium text-gray-700">
            Nomor Rekening
            {isPlaceholder('bank_account', employee.bank_account) && (
              <PlaceholderIndicator />
            )}
          </label>
          <Input
            value={employee.bank_account}
            onChange={(e) => handleChange('bank_account', e.target.value)}
            disabled={isFieldReadOnly('bank_account')}
            className={`border-gray-200 focus:border-[#AB8B3B] focus:ring-[#AB8B3B] ${
              isFieldReadOnly('bank_account') ? 'bg-gray-50' : ''
            } ${isPlaceholder('bank_account', employee.bank_account) ? 'border-amber-300' : ''}`}
          />
          {isPlaceholder('bank_account', employee.bank_account) && (
            <p className="text-xs text-amber-500">
              Silakan masukkan nomor rekening bank Anda yang sebenarnya.
            </p>
          )}
        </div>
        <div className="space-y-2">
          <label className="text-sm font-medium text-gray-700">
            Nama Bank
            {isPlaceholder('bank_name', employee.bank_name) && (
              <PlaceholderIndicator />
            )}
          </label>
          <Input
            value={employee.bank_name}
            onChange={(e) => handleChange('bank_name', e.target.value)}
            disabled={isFieldReadOnly('bank_name')}
            className={`border-gray-200 focus:border-[#AB8B3B] focus:ring-[#AB8B3B] ${
              isFieldReadOnly('bank_name') ? 'bg-gray-50' : ''
            } ${isPlaceholder('bank_name', employee.bank_name) ? 'border-amber-300' : ''}`}
          />
          {isPlaceholder('bank_name', employee.bank_name) && (
            <p className="text-xs text-amber-500">
              Silakan masukkan nama bank Anda yang sebenarnya.
            </p>
          )}
        </div>
      </div>

      <div className="grid gap-6 md:grid-cols-2">
        <div className="space-y-2">
          <label className="text-sm font-medium text-gray-700">
            Status Kepegawaian
          </label>
          {isFieldReadOnly('employment_status') ? (
            <Input
              value={employee.employment_status}
              disabled={true}
              className="bg-gray-50 border-gray-200"
            />
          ) : (
            <Select
              value={employee.employment_status}
              onValueChange={(value: EmploymentStatus) =>
                handleChange('employment_status', value)
              }
              disabled={isFieldReadOnly('employment_status')}
            >
              <SelectTrigger className="border-gray-200 focus:border-[#AB8B3B] focus:ring-[#AB8B3B]">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                {Object.values(EmploymentStatus).map((status) => (
                  <SelectItem
                    key={status}
                    value={status}
                    className="hover:bg-[#AB8B3B]/10 focus:bg-[#AB8B3B]/10"
                  >
                    {status}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          )}
        </div>
        <div className="space-y-2">
          <label className="text-sm font-medium text-gray-700">
            Status Kehadiran
          </label>
          {isFieldReadOnly('presence_status') ? (
            <Input
              value={employee.presence_status}
              disabled={true}
              className="bg-gray-50 border-gray-200"
            />
          ) : (
            <Select
              value={employee.presence_status}
              onValueChange={(value: PresenceStatus) =>
                handleChange('presence_status', value)
              }
              disabled={isFieldReadOnly('presence_status')}
            >
              <SelectTrigger className="border-gray-200 focus:border-[#AB8B3B] focus:ring-[#AB8B3B]">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                {Object.values(PresenceStatus).map((status) => (
                  <SelectItem
                    key={status}
                    value={status}
                    className="hover:bg-[#AB8B3B]/10 focus:bg-[#AB8B3B]/10"
                  >
                    {formatEnumValue(status)}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          )}
        </div>
        <div className="space-y-2">
          <label className="text-sm font-medium text-gray-700">
            Departemen
          </label>
          {isFieldReadOnly('department') ? (
            <Input
              value={employee.department}
              disabled={true}
              className="bg-gray-50 border-gray-200"
            />
          ) : (
            <Select
              value={employee.department}
              onValueChange={(value: Department) =>
                handleChange('department', value)
              }
              disabled={isFieldReadOnly('department')}
            >
              <SelectTrigger className="border-gray-200 focus:border-[#AB8B3B] focus:ring-[#AB8B3B]">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                {Object.values(Department).map((dept) => (
                  <SelectItem
                    key={dept}
                    value={dept}
                    className="hover:bg-[#AB8B3B]/10 focus:bg-[#AB8B3B]/10"
                  >
                    {dept}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          )}
        </div>
        <div className="space-y-2">
          <label className="text-sm font-medium text-gray-700">
            Start Date
          </label>
          <Input
            type="date"
            value={employee.start_date}
            onChange={(e) => handleChange('start_date', e.target.value)}
            disabled={isFieldReadOnly('start_date')}
            className={`border-gray-200 focus:border-[#AB8B3B] focus:ring-[#AB8B3B] ${
              isFieldReadOnly('start_date') ? 'bg-gray-50' : ''
            }`}
          />
        </div>
      </div>
    </div>
  );
}
