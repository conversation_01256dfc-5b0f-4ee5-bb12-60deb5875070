import React from 'react';
import { useEmployeeManagement } from '@/hooks/useEmployeeManagement';
import { EmployeeTable } from './EmployeeTable';
import { EmployeeSearchFilter } from './EmployeeSearchFilter';
import { PageTitle } from '@/components/ui/PageTitle';

export function EmployeeManagementContent() {
  const {
    employees,
    loading,
    search,
    department,
    employmentStatus,
    presenceStatus,
    handleSearchChange,
    handleDepartmentChange,
    handleEmploymentStatusChange,
    handlePresenceStatusChange,
    handleViewDetail,
  } = useEmployeeManagement();

  return (
    <div className="container mx-auto py-6 px-6">
      <div className="flex justify-between items-center mb-6">
        <PageTitle title="Manajemen Karyawan" />
      </div>

      <div className="bg-white rounded-lg shadow p-6">
        <div className="mb-6">
          <EmployeeSearchFilter
            search={search}
            onSearch={handleSearchChange}
            department={department}
            onDepartmentChange={handleDepartmentChange}
            employmentStatus={employmentStatus}
            onEmploymentStatusChange={handleEmploymentStatusChange}
            presenceStatus={presenceStatus}
            onPresenceStatusChange={handlePresenceStatusChange}
          />
        </div>

        <div className="overflow-x-auto mb-4">
          <EmployeeTable
            employees={employees}
            loading={loading}
            onRowClick={handleViewDetail}
          />
        </div>
      </div>
    </div>
  );
}
