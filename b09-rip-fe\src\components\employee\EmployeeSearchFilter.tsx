'use client';

import { Department, EmploymentStatus, PresenceStatus } from '@/types/employee';
import { SearchFilter, Filter } from '@/components/ui/search-filter';

interface EmployeeSearchFilterProps {
  search: string;
  onSearch: (value: string) => void;
  department: Department | undefined;
  onDepartmentChange: (value: Department | undefined) => void;
  employmentStatus: EmploymentStatus | undefined;
  onEmploymentStatusChange: (value: EmploymentStatus | undefined) => void;
  presenceStatus: PresenceStatus | undefined;
  onPresenceStatusChange: (value: PresenceStatus | undefined) => void;
}

export function EmployeeSearchFilter({
  search,
  onSearch,
  department,
  onDepartmentChange,
  employmentStatus,
  onEmploymentStatusChange,
  presenceStatus,
  onPresenceStatusChange,
}: EmployeeSearchFilterProps) {
  // Helper function to display human-readable labels
  const formatEnumValue = (value: string): string => {
    return value
      .split('_')
      .map((word) => word.charAt(0).toUpperCase() + word.slice(1).toLowerCase())
      .join(' ');
  };

  const filters: Filter[] = [
    {
      label: 'Departemen',
      value: department,
      onChange: (value) => onDepartmentChange(value as Department | undefined),
      options: Object.values(Department).map((dept) => ({
        value: dept,
        label: dept,
      })),
    },
    {
      label: 'Status Kepegawaian',
      value: employmentStatus,
      onChange: (value) =>
        onEmploymentStatusChange(value as EmploymentStatus | undefined),
      options: Object.values(EmploymentStatus).map((status) => ({
        value: status,
        label: status,
      })),
    },
    {
      label: 'Status Kehadiran',
      value: presenceStatus,
      onChange: (value) =>
        onPresenceStatusChange(value as PresenceStatus | undefined),
      options: Object.values(PresenceStatus).map((status) => ({
        value: status,
        label: formatEnumValue(status),
      })),
    },
  ];

  return (
    <SearchFilter
      search={search}
      onSearchChange={onSearch}
      filters={filters}
      searchPlaceholder="Cari karyawan..."
    />
  );
}
