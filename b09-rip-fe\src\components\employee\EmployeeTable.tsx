import { Employee } from '@/types/employee';
import { Eye } from 'lucide-react';
import { But<PERSON> } from '@/components/ui/button';
import { EmploymentStatusBadge } from './EmploymentStatusBadge';
import { PresenceStatusBadge } from './PresenceStatusBadge';
import { DataTable } from '@/components/ui/data-table';

interface EmployeeTableProps {
  employees: Employee[];
  loading: boolean;
  onRowClick: (id: string) => void;
  // Pagination props removed as they're not used in DataTable anymore
  // currentPage?: number;
  // itemsPerPage?: number;
  // onPageChange?: (page: number) => void;
  totalEmployees?: number;
  sortField?: string;
  sortDirection?: 'asc' | 'desc' | null;
  onSort?: (field: string, direction: 'asc' | 'desc' | null) => void;
}

export function EmployeeTable({
  employees,
  loading,
  onRowClick,
  // Pagination props removed as they're not used in DataTable anymore
  // currentPage = 1,
  // itemsPerPage = 10,
  // onPageChange,
  totalEmployees,
  sortField,
  sortDirection,
  onSort,
}: EmployeeTableProps) {
  // Define columns configuration for DataTable
  const columns = [
    {
      key: 'name',
      header: 'Nama',
      sortable: false,
      render: (employee: Employee) => (
        <span className="font-medium">{employee.profile.fullname}</span>
      ),
    },
    {
      key: 'email',
      header: 'Email',
      sortable: false,
      hideOnMobile: true,
      render: (employee: Employee) => (
        <span className="text-gray-600">{employee.email}</span>
      ),
    },
    {
      key: 'role',
      header: 'Jabatan',
      sortable: false,
      render: (employee: Employee) => (
        <span className="text-gray-600">{employee.profile.role}</span>
      ),
    },
    {
      key: 'department',
      header: 'Departemen',
      sortable: false,
      hideOnMobile: true,
      render: (employee: Employee) => (
        <span className="text-gray-600">{employee.department}</span>
      ),
    },
    {
      key: 'employment_status',
      header: 'Status',
      sortable: false,
      render: (employee: Employee) => (
        <EmploymentStatusBadge status={employee.employment_status} />
      ),
    },
    {
      key: 'presence_status',
      header: 'Kehadiran',
      sortable: false,
      render: (employee: Employee) => (
        <PresenceStatusBadge status={employee.presence_status} />
      ),
    },
    {
      key: 'actions',
      header: 'Aksi',
      width: '100px',
      render: (employee: Employee) => (
        <Button
          variant="outline"
          size="sm"
          leftIcon={<Eye />}
          onClick={(e) => {
            e.stopPropagation();
            onRowClick(employee.id);
          }}
        >
          Lihat
        </Button>
      ),
    },
  ];

  // Empty state message
  const emptyStateMessage =
    employees.length === 0 && !loading
      ? 'Tidak ada karyawan ditemukan. Coba sesuaikan filter pencarian Anda.'
      : 'Tidak ada data untuk ditampilkan';

  // Use data pagination from props if available, otherwise DataTable will handle it
  const data = totalEmployees ? employees : employees;

  return (
    <DataTable
      columns={columns}
      data={data}
      keyExtractor={(employee) => employee.id}
      loading={loading}
      // Pagination props removed as they're not used in DataTable anymore
      // currentPage={currentPage}
      // itemsPerPage={itemsPerPage}
      // onPageChange={onPageChange}
      sortField={sortField}
      sortDirection={sortDirection}
      onSort={onSort}
      emptyStateMessage={emptyStateMessage}
      // showPagination prop removed as it's not used in DataTable anymore
      // showPagination={false}
    />
  );
}
