import { useRouter } from 'next/navigation';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Pencil, Calendar, ArrowLeft } from 'lucide-react';
import { useRBAC } from '@/hooks/useRBAC';
import { useEmployeeDetail } from '@/hooks/useEmployeeDetail';
import { Skeleton } from '@/components/ui/skeleton';
import { PageTitle } from '@/components/ui/PageTitle';
import { BackButton } from '@/components/ui/BackButton';
import { EmploymentStatusBadge } from './EmploymentStatusBadge';
import { PresenceStatusBadge } from './PresenceStatusBadge';

interface EmployeeViewContentProps {
  id: string;
}

export function EmployeeViewContent({ id }: EmployeeViewContentProps) {
  const router = useRouter();
  const { hasRole } = useRBAC();
  const { employee, loading } = useEmployeeDetail(id);

  if (loading) {
    return (
      <div className="space-y-6">
        <div className="flex justify-between items-center">
          <Skeleton className="h-8 w-48" />
          <div className="flex gap-2">
            <Skeleton className="h-10 w-24" />
            <Skeleton className="h-10 w-24" />
          </div>
        </div>
        {[1, 2, 3].map((i) => (
          <Card key={i} className="border border-gray-200 shadow-sm">
            <CardHeader className="bg-gray-50 border-b border-gray-200">
              <Skeleton className="h-6 w-32" />
            </CardHeader>
            <CardContent className="p-6">
              <div className="grid grid-cols-2 gap-6">
                {[1, 2, 3, 4].map((j) => (
                  <div key={j} className="space-y-2">
                    <Skeleton className="h-4 w-24" />
                    <Skeleton className="h-6 w-full" />
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
    );
  }

  if (!employee) {
    return (
      <div className="flex flex-col items-center justify-center p-12 bg-gray-50 rounded-lg border border-gray-200">
        <h2 className="text-xl font-semibold text-[#AB8B3B] mb-2">
          Karyawan tidak ditemukan
        </h2>
        <p className="text-gray-600 mb-6 text-center max-w-md">
          Karyawan yang Anda cari tidak ada atau telah dihapus.
        </p>
        <Button
          onClick={() => router.push('/employee')}
          variant="default"
          leftIcon={<ArrowLeft />}
        >
          Kembali ke Daftar Karyawan
        </Button>
      </div>
    );
  }

  const canEdit = hasRole(['HR', 'Admin']);
  const canViewAttendance = hasRole(['HR', 'Admin']);

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center pb-6 border-b border-gray-200">
        <div className="flex items-center gap-4">
          <BackButton onClick={() => router.push('/employee')} />
          <PageTitle title="Detail Karyawan" />
        </div>
        <div className="flex gap-3">
          {canViewAttendance && (
            <Button
              variant="outline"
              size="sm"
              leftIcon={<Calendar />}
              onClick={() => router.push(`/employee/${id}/attendance`)}
            >
              Presensi
            </Button>
          )}
          {canEdit && (
            <Button
              size="sm"
              variant="default"
              leftIcon={<Pencil />}
              onClick={() => router.push(`/employee/${id}/edit`)}
            >
              Edit
            </Button>
          )}
        </div>
      </div>

      <Card className="border border-gray-200 shadow-sm">
        <CardHeader className="bg-gray-50 border-b border-gray-200">
          <CardTitle className="text-[#AB8B3B]">Informasi Pribadi</CardTitle>
        </CardHeader>
        <CardContent className="p-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="space-y-1.5">
              <p className="text-sm font-medium text-gray-500">Nama Lengkap</p>
              <p className="text-base font-medium">
                {employee.profile.fullname}
              </p>
            </div>
            <div className="space-y-1.5">
              <p className="text-sm font-medium text-gray-500">Email</p>
              <p className="text-base">{employee.email}</p>
            </div>
            <div className="space-y-1.5">
              <p className="text-sm font-medium text-gray-500">Nomor Telepon</p>
              <p className="text-base">{employee.profile.phonenum}</p>
            </div>
            <div className="space-y-1.5">
              <p className="text-sm font-medium text-gray-500">Jabatan</p>
              <p className="text-base">{employee.profile.role}</p>
            </div>
            <div className="space-y-1.5">
              <p className="text-sm font-medium text-gray-500">Tanggal Lahir</p>
              <p className="text-base">{employee.dob}</p>
            </div>
            <div className="space-y-1.5">
              <p className="text-sm font-medium text-gray-500">Alamat</p>
              <p className="text-base">{employee.address}</p>
            </div>
          </div>
        </CardContent>
      </Card>

      <Card className="border border-gray-200 shadow-sm">
        <CardHeader className="bg-gray-50 border-b border-gray-200">
          <CardTitle className="text-[#AB8B3B]">
            Informasi Kepegawaian
          </CardTitle>
        </CardHeader>
        <CardContent className="p-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="space-y-1.5">
              <p className="text-sm font-medium text-gray-500">Departemen</p>
              <p className="text-base">{employee.department}</p>
            </div>
            <div className="space-y-1.5">
              <p className="text-sm font-medium text-gray-500">
                Status Kepegawaian
              </p>
              <EmploymentStatusBadge status={employee.employment_status} />
            </div>
            <div className="space-y-1.5">
              <p className="text-sm font-medium text-gray-500">
                Status Kehadiran
              </p>
              <PresenceStatusBadge status={employee.presence_status} />
            </div>
            <div className="space-y-1.5">
              <p className="text-sm font-medium text-gray-500">Tanggal Mulai</p>
              <p className="text-base">{employee.start_date}</p>
            </div>
          </div>
        </CardContent>
      </Card>

      <Card className="border border-gray-200 shadow-sm">
        <CardHeader className="bg-gray-50 border-b border-gray-200">
          <CardTitle className="text-[#AB8B3B]">Informasi Bank</CardTitle>
        </CardHeader>
        <CardContent className="p-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="space-y-1.5">
              <p className="text-sm font-medium text-gray-500">Nama Bank</p>
              <p className="text-base">{employee.bank_name || '-'}</p>
            </div>
            <div className="space-y-1.5">
              <p className="text-sm font-medium text-gray-500">
                Nomor Rekening
              </p>
              <p className="text-base">{employee.bank_account || '-'}</p>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
