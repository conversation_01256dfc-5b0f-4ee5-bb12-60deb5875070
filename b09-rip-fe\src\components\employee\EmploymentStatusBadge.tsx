import { Badge } from '@/components/ui/badge';
import { EmploymentStatus } from '@/types/employee';

interface EmploymentStatusBadgeProps {
  status: EmploymentStatus;
  className?: string;
}

export function EmploymentStatusBadge({
  status,
  className,
}: EmploymentStatusBadgeProps) {
  const getVariant = (status: EmploymentStatus) => {
    switch (status) {
      case EmploymentStatus.Fulltime:
        return 'success';
      case EmploymentStatus.Intern:
        return 'warning';
      case EmploymentStatus.NotProvided:
        return 'secondary';
      default:
        return 'secondary';
    }
  };

  const formatStatus = (status: string): string => {
    return status.split(/(?=[A-Z])/).join(' ');
  };

  return (
    <Badge variant={getVariant(status)} className={className}>
      {formatStatus(status)}
    </Badge>
  );
}
