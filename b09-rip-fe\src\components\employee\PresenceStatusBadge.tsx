import { Badge } from '@/components/ui/badge';
import { PresenceStatus } from '@/types/employee';

interface PresenceStatusBadgeProps {
  status: PresenceStatus;
  className?: string;
}

export function PresenceStatusBadge({
  status,
  className,
}: PresenceStatusBadgeProps) {
  const getVariant = (status: PresenceStatus) => {
    switch (status) {
      case PresenceStatus.Present:
        return 'success';
      case PresenceStatus.Absent:
        return 'danger';
      case PresenceStatus.Permit:
        return 'warning';
      case PresenceStatus.Leave:
        return 'info';
      default:
        return 'secondary';
    }
  };

  const formatStatus = (status: string): string => {
    return status.charAt(0).toUpperCase() + status.slice(1).toLowerCase();
  };

  return (
    <Badge variant={getVariant(status)} className={className}>
      {formatStatus(status)}
    </Badge>
  );
}
