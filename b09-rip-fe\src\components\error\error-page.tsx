"use client"

import Link from "next/link"
import { useRouter } from "next/navigation"
import { But<PERSON> } from "@/components/ui/button"
import { AlertCircle, Ban, FileQuestion, Lock, Server, XCircle } from "lucide-react"

export type ErrorType = "404" | "500" | "403" | "401" | "400" | "generic"

interface ErrorPageProps {
  statusCode?: ErrorType
  title?: string
  message?: string
  showHomeButton?: boolean
  showBackButton?: boolean
  showRetryButton?: boolean
  onRetry?: () => void
  className?: string
  homeHref?: string
  homeLabel?: string
  backLabel?: string
  retryLabel?: string
}

// Define error configurations with icons and colors
const errorConfigs: Record<ErrorType, {
  title: string
  message: string
  icon: React.ElementType
  iconColor: string
  iconBgColor: string
}> = {
  "404": {
    title: "Halaman Tidak Ditemukan",
    message: "<PERSON><PERSON>, halaman yang Anda cari tidak dapat ditemukan.",
    icon: FileQuestion,
    iconColor: "text-blue-500",
    iconBgColor: "bg-blue-100",
  },
  "500": {
    title: "Kesalahan Server",
    message: "<PERSON><PERSON>, terja<PERSON> kesalahan pada server. Silakan coba lagi nanti.",
    icon: Server,
    iconColor: "text-red-500",
    iconBgColor: "bg-red-100",
  },
  "403": {
    title: "Akses Ditolak",
    message: "Anda tidak memiliki izin untuk mengakses halaman ini.",
    icon: Ban,
    iconColor: "text-amber-500",
    iconBgColor: "bg-amber-100",
  },
  "401": {
    title: "Tidak Terautentikasi",
    message: "Anda perlu login untuk mengakses halaman ini.",
    icon: Lock,
    iconColor: "text-amber-500",
    iconBgColor: "bg-amber-100",
  },
  "400": {
    title: "Permintaan Tidak Valid",
    message: "Permintaan yang Anda kirim tidak valid.",
    icon: XCircle,
    iconColor: "text-orange-500",
    iconBgColor: "bg-orange-100",
  },
  "generic": {
    title: "Terjadi Kesalahan",
    message: "Maaf, terjadi kesalahan. Silakan coba lagi nanti.",
    icon: AlertCircle,
    iconColor: "text-gray-500",
    iconBgColor: "bg-gray-100",
  },
}

export default function ErrorPage({
  statusCode = "generic",
  title,
  message,
  showHomeButton = true,
  showBackButton = true,
  showRetryButton = false,
  onRetry,
  className = "",
  homeHref = "/",
  homeLabel = "Kembali ke Beranda",
  backLabel = "Kembali",
  retryLabel = "Coba Lagi",
}: ErrorPageProps) {
  const router = useRouter()
  const errorConfig = errorConfigs[statusCode]

  const errorTitle = title || errorConfig.title
  const errorMessage = message || errorConfig.message
  const Icon = errorConfig.icon
  const { iconColor, iconBgColor } = errorConfig

  const handleBack = () => {
    router.back()
  }

  const handleRetry = () => {
    if (onRetry) {
      onRetry()
    } else {
      window.location.reload()
    }
  }

  return (
    <div className={`flex min-h-screen flex-col items-center justify-center bg-gray-50 px-4 text-center ${className}`}>
      <div className="mx-auto max-w-md">
        <div className={`rounded-full ${iconBgColor} p-6 mx-auto w-fit mb-6`}>
          <Icon className={`h-16 w-16 ${iconColor}`} />
        </div>

        <h1 className="text-5xl font-bold text-gray-800 mb-2">
          {statusCode !== "generic" ? statusCode : "Error"}
        </h1>

        <h2 className="text-2xl font-semibold text-gray-800 mb-4">{errorTitle}</h2>

        <p className="text-gray-600 mb-8">{errorMessage}</p>

        <div className="flex flex-col sm:flex-row gap-4 justify-center">
          {showBackButton && (
            <Button
              onClick={handleBack}
              variant="outline"
              className="border-[#AB8B3B] text-[#AB8B3B] hover:bg-[#AB8B3B]/10 transition-colors duration-200"
            >
              {backLabel}
            </Button>
          )}

          {showRetryButton && (
            <Button
              onClick={handleRetry}
              variant="outline"
              className="border-[#AB8B3B] text-[#AB8B3B] hover:bg-[#AB8B3B]/10 transition-colors duration-200"
            >
              {retryLabel}
            </Button>
          )}

          {showHomeButton && (
            <Link href={homeHref}>
              <Button className="bg-[#AB8B3B] hover:bg-[#9B7533] text-white transition-colors duration-200">
                {homeLabel}
              </Button>
            </Link>
          )}
        </div>
      </div>
    </div>
  )
}
