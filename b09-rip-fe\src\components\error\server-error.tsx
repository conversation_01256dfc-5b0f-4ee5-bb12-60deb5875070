"use client"

import { useEffect } from "react"
import ErrorPage from "@/components/error/error-page"

interface ServerErrorProps {
  error: Error & { digest?: string }
  reset?: () => void
}

export default function ServerError({ error, reset }: ServerErrorProps) {
  useEffect(() => {
    // Log the error to an error reporting service
    console.error("Server error:", error)
  }, [error])

  return (
    <div className="container mx-auto max-w-5xl py-12">
      <ErrorPage statusCode="500" message={error.message || undefined} showRetryButton={!!reset} onRetry={reset} />
    </div>
  )
}

