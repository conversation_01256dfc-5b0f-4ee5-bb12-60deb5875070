import ErrorPage from "@/components/error/error-page"

interface UnauthorizedErrorProps {
  message?: string
  onRetry?: () => void
}

export default function UnauthorizedError({ message, onRetry }: UnauthorizedErrorProps) {
  return (
    <div className="container mx-auto max-w-5xl py-12">
      <ErrorPage statusCode="401" message={message} showRetryButton={!!onRetry} onRetry={onRetry} />
    </div>
  )
}

