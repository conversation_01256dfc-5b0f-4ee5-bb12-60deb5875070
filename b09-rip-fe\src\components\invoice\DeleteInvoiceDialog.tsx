'use client';

import React from 'react';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogFooter,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Invoice } from '@/types/invoice';
import { AlertTriangle } from 'lucide-react';

interface DeleteInvoiceDialogProps {
  invoice: Invoice | null;
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onConfirm: () => void;
  isDeleting: boolean;
  error?: string | null;
}

export function DeleteInvoiceDialog({
  invoice,
  open,
  onOpenChange,
  onConfirm,
  isDeleting,
  error,
}: DeleteInvoiceDialogProps) {
  if (!invoice) return null;

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2 text-destructive">
            <AlertTriangle className="h-5 w-5" />
            Hapus Faktur
          </DialogTitle>
          <DialogDescription>
            Apakah Anda yakin ingin menghapus faktur{' '}
            <strong>#{invoice.invoice_number}</strong>? Tindakan ini tidak dapat
            dibatalkan.
          </DialogDescription>
        </DialogHeader>

        <div className="py-4">
          <p className="text-sm text-muted-foreground mb-2">Detail faktur:</p>
          <div className="text-sm border rounded-md p-3 bg-muted/50">
            <p>
              <span className="font-medium">Penerima:</span>{' '}
              {invoice.recipient_name}
            </p>
            <p>
              <span className="font-medium">Jumlah:</span>{' '}
              {new Intl.NumberFormat('id-ID', {
                style: 'currency',
                currency: 'IDR',
              }).format(invoice.total_amount)}
            </p>
            <p>
              <span className="font-medium">Status:</span>{' '}
              {invoice.payment_status}
            </p>
          </div>

          {error && <p className="mt-3 text-sm text-destructive">{error}</p>}
        </div>

        <DialogFooter>
          <Button
            variant="outline"
            onClick={() => onOpenChange(false)}
            disabled={isDeleting}
          >
            Batal
          </Button>
          <Button
            variant="destructive"
            onClick={onConfirm}
            disabled={isDeleting}
          >
            {isDeleting ? 'Menghapus...' : 'Hapus Faktur'}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
