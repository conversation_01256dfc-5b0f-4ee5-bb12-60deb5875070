import Image from 'next/image';
import { cn } from '@/lib/utils';
import { Invoice } from '@/types/invoice';

interface InvoiceCompanyHeaderProps {
  className?: string;
  invoice?: Invoice;
}

export function InvoiceCompanyHeader({
  className,
  invoice,
}: InvoiceCompanyHeaderProps) {
  return (
    <div className={cn('print:block hidden mb-8 print:mb-10', className)}>
      <div className="flex justify-between items-start mb-8">
        {/* Logo */}
        <div className="relative h-16 w-[180px] print:h-20 print:w-[220px]">
          <Image
            src="/logo-navbar.svg"
            alt="Kasuat"
            fill
            className="object-contain object-left print:!object-scale-down"
            priority
            unoptimized
            sizes="(max-width: 768px) 180px, 220px"
          />
        </div>

        {/* Company details */}
        <div className="text-right space-y-0.5 text-sm">
          <h3 className="text-lg font-medium text-[#C3A64C]">
            PT Halaman Tumbuh Bersama
          </h3>
          <p className="print:text-black">Jl. Terusan Sukadamai II No.5</p>
          <p className="print:text-black">
            Sukabungah, Kec. Sukajadi, Kota Bandung
          </p>
          <p className="print:text-black">+62 87802514374</p>
          <p className="print:text-black"><EMAIL></p>
        </div>
      </div>

      {/* Invoice title without status */}
      {invoice && (
        <div className="flex justify-between items-center mb-8 border-b pb-4 print:pb-5 print:mb-6">
          <h1 className="text-2xl font-bold print:text-black">
            Faktur {invoice.invoice_number}
          </h1>
        </div>
      )}
    </div>
  );
}
