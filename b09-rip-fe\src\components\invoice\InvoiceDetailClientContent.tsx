'use client';

import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { toast } from 'sonner';
import { InvoiceDetailView } from './InvoiceDetailView';
import { InvoiceDetailSkeleton } from './InvoiceDetailSkeleton';
import { useInvoiceDetailStore } from '@/lib/store/invoice-detail-store';
import { PageTitle } from '@/components/ui/PageTitle';
import { BackButton } from '@/components/ui/BackButton';
import { Button } from '@/components/ui/button';
import { Download, Pencil, Trash2, FileText } from 'lucide-react';
import { generateInvoicePDF } from '@/lib/utils/pdf-generator';

interface InvoiceDetailClientContentProps {
  id: string;
}

export function InvoiceDetailClientContent({
  id,
}: InvoiceDetailClientContentProps) {
  const router = useRouter();
  const [loading, setLoading] = useState(true);
  const { invoice, error, fetchInvoice } = useInvoiceDetailStore();

  useEffect(() => {
    const getInvoiceDetail = async () => {
      setLoading(true);
      try {
        await fetchInvoice(id);
      } catch (error: unknown) {
        console.error('Error fetching invoice:', error);

        const err = error as { response?: { status?: number } };
        if (err.response?.status === 404) {
          toast.error('Faktur tidak ditemukan');
          router.push('/invoice');
        } else if (err.response?.status === 401) {
          toast.error('Sesi berakhir. Silakan login kembali.');
        } else if (err.response?.status === 403) {
          toast.error('Anda tidak memiliki izin untuk melihat faktur ini.');
        } else {
          toast.error('Gagal memuat detail faktur. Silakan coba lagi nanti.');
        }
      } finally {
        setLoading(false);
      }
    };

    if (id) {
      getInvoiceDetail();
    }

    // Clean up store on unmount
    return () => {
      useInvoiceDetailStore.getState().reset();
    };
  }, [id, router, fetchInvoice]);

  if (loading) {
    return <InvoiceDetailSkeleton />;
  }

  if (error) {
    // If there's a specific error, we could show it differently
    if (error === 'Invoice not found.') {
      toast.error('Faktur tidak ditemukan');
      router.push('/invoice');
      return null;
    }

    return (
      <div className="flex flex-col items-center justify-center py-12">
        <div className="bg-red-100 border border-red-200 text-red-700 px-4 py-3 rounded relative mb-4">
          <strong className="font-bold">Kesalahan: </strong>
          <span className="block sm:inline">{error}</span>
        </div>
        <button
          onClick={() => fetchInvoice(id)}
          className="bg-primary text-white px-4 py-2 rounded mt-4 hover:bg-primary/90"
        >
          Coba Lagi
        </button>
      </div>
    );
  }

  if (!invoice) {
    return (
      <div className="text-center py-10">
        <p className="text-muted-foreground">
          Faktur tidak ditemukan atau terjadi kesalahan.
        </p>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-start mb-6 print:hidden">
        <div className="flex items-center gap-4">
          <BackButton onClick={() => router.push('/invoice')} />
          <PageTitle
            title="Detail Faktur"
            subtitle="Lihat informasi detail faktur"
          />
        </div>
        {invoice && (
          <div className="flex gap-2">
            <Button
              variant="outline"
              size="sm"
              leftIcon={<Download />}
              onClick={() => {
                generateInvoicePDF(invoice);
                toast.success('Faktur berhasil diunduh');
              }}
              className="text-blue-600 border-blue-500 hover:bg-blue-50"
            >
              Unduh Faktur
            </Button>
            <Button
              variant="outline"
              size="sm"
              leftIcon={<FileText />}
              onClick={() =>
                useInvoiceDetailStore.getState().setShowPaymentProofModal(true)
              }
              className="text-purple-600 border-purple-500 hover:bg-purple-50"
            >
              Bukti Pembayaran
            </Button>
            <Button
              variant="outline"
              size="sm"
              leftIcon={<Pencil />}
              onClick={() => router.push(`/invoice/${invoice.id}/edit`)}
            >
              Ubah Faktur
            </Button>
            <Button
              variant="destructive"
              size="sm"
              leftIcon={<Trash2 />}
              onClick={() => {
                useInvoiceDetailStore.getState().setShowDeleteDialog(true);
              }}
            >
              Hapus
            </Button>
          </div>
        )}
      </div>
      <InvoiceDetailView invoice={invoice} />
    </div>
  );
}
