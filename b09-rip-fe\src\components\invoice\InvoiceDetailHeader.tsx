'use client';

import React from 'react';
import { Download, Edit, Trash2 } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { InvoiceStatusBadge } from './InvoiceStatusBadge';
import { InvoiceTypeBadge, ServiceTypeBadge } from './InvoiceTypeBadge';
import { Invoice } from '@/types/invoice';

interface InvoiceDetailHeaderProps {
  invoice: Invoice;
  onDownload: () => void;
  onEdit?: () => void;
  onDelete?: () => void;
  className?: string;
  isDownloading?: boolean;
}

export function InvoiceDetailHeader({
  invoice,
  onDownload,
  onEdit,
  onDelete,
  className = '',
  isDownloading = false,
}: InvoiceDetailHeaderProps) {
  return (
    <div className={`space-y-5 ${className}`}>
      {/* Title and actions */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-5 mb-2">
        <div>
          <h1 className="text-2xl font-bold text-gray-900 flex items-center mb-1">
            <span className="mr-3">Faktur {invoice.invoice_number}</span>
            <InvoiceStatusBadge status={invoice.payment_status} />
          </h1>
          <div className="flex items-center gap-2 mt-2">
            <ServiceTypeBadge type={invoice.service_type} />
            <InvoiceTypeBadge type={invoice.invoice_type} />
          </div>
        </div>

        <div className="flex flex-wrap items-center gap-2 mt-2 sm:mt-0">
          <Button
            variant="outline"
            size="sm"
            className="gap-1 text-blue-600 border-blue-700 hover:bg-blue-50"
            onClick={onDownload}
            disabled={isDownloading}
          >
            <Download className="h-4 w-4" />
            {isDownloading ? 'Generating...' : 'Unduh Faktur'}
          </Button>

          {onEdit && (
            <Button
              variant="outline"
              size="sm"
              className="gap-1 text-amber-600 border-amber-700 hover:bg-amber-50"
              onClick={onEdit}
            >
              <Edit className="h-4 w-4" />
              Ubah Faktur
            </Button>
          )}

          {onDelete && (
            <Button
              variant="outline"
              size="sm"
              className="gap-1 text-red-600 border-red-700 hover:bg-red-50"
              onClick={onDelete}
            >
              <Trash2 className="h-4 w-4" />
              <span className="sr-only md:not-sr-only md:ml-1">Hapus</span>
            </Button>
          )}
        </div>
      </div>
    </div>
  );
}
