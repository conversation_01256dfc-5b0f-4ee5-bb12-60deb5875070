'use client';

import React from 'react';
import { Invoice } from '@/types/invoice';
import { InvoiceItemsList } from './InvoiceItemsList';
import { InvoiceStatusBadge } from './InvoiceStatusBadge';
import { DeleteInvoiceDialog } from './DeleteInvoiceDialog';
import { PaymentProofModal } from './PaymentProofModal';
import { InvoiceTimeline } from './InvoiceTimeline';
import { useRouter } from 'next/navigation';
import { toast } from 'sonner';
import { useInvoiceDetailStore } from '@/lib/store/invoice-detail-store';
import { useRBAC } from '@/hooks/useRBAC';
import { UserRole } from '@/types/auth';
import { InvoiceCompanyHeader } from './InvoiceCompanyHeader';
import { InvoiceTypeBadge, ServiceTypeBadge } from './InvoiceTypeBadge';

// Roles that are allowed to delete invoices
const DELETE_ROLES: UserRole[] = ['Admin'];

interface InvoiceDetailViewProps {
  invoice: Invoice;
}

export function InvoiceDetailView({ invoice }: InvoiceDetailViewProps) {
  const router = useRouter();
  const { hasRole } = useRBAC();
  const {
    isDeleting,
    deleteError,
    showDeleteDialog,
    setShowDeleteDialog,
    deleteInvoice,
  } = useInvoiceDetailStore();

  // Permissions
  const canDelete = hasRole(DELETE_ROLES);

  const handleConfirmDelete = async () => {
    if (!canDelete) {
      toast.error('Anda tidak memiliki izin untuk menghapus faktur');
      return;
    }

    const success = await deleteInvoice(invoice.id);

    if (success) {
      toast.success('Faktur berhasil dihapus');
      router.push('/invoice');
    } else if (deleteError) {
      toast.error(deleteError);
    }
  };

  // Format date in Indonesian format
  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('id-ID', {
      day: 'numeric',
      month: 'numeric',
      year: 'numeric',
    });
  };

  return (
    <div className="max-w-full space-y-8 print:space-y-6 print:bg-white">
      {/* Company header - only shown when printing */}
      <InvoiceCompanyHeader invoice={invoice} />

      {/* Main invoice content */}
      <div className="rounded-lg bg-white shadow-md border border-gray-100 overflow-hidden print:border-none print:shadow-none print:mt-0 print:bg-white">
        {/* Invoice header with status and type badges */}
        <div className="px-6 pt-6 pb-2 print:px-0 print:pt-2 print:hidden">
          <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4 mb-4 print:mb-6">
            <div>
              <h2 className="text-xl font-bold text-gray-900 flex items-center print:text-black">
                <span className="mr-3">Faktur {invoice.invoice_number}</span>
                <InvoiceStatusBadge status={invoice.payment_status} />
              </h2>
              <div className="flex items-center gap-2 mt-2">
                <ServiceTypeBadge type={invoice.service_type} />
                <InvoiceTypeBadge type={invoice.invoice_type} />
              </div>
            </div>
          </div>
        </div>

        {/* Invoice content */}
        <div className="p-6 print:p-0 print:px-0">
          <div className="grid grid-cols-2 gap-6 mb-8 print:grid-cols-2 print:gap-8 print:mb-6">
            {/* Left column */}
            <div>
              {/* Recipient */}
              <div className="mb-4 print:mb-5">
                <h3 className="text-sm font-medium text-gray-500 mb-1 print:text-gray-700">
                  Penerima
                </h3>
                <p className="text-base font-medium print:text-black">
                  {invoice.recipient_name}
                </p>
              </div>

              {/* Project */}
              <div className="mb-4 print:mb-5">
                <h3 className="text-sm font-medium text-gray-500 mb-1 print:text-gray-700">
                  Proyek
                </h3>
                <p className="text-base font-medium print:text-black">
                  {invoice.project_name || 'Team Structure Optimization'}
                </p>
              </div>

              {/* Payment Method */}
              <div className="mb-4 print:mb-5">
                <h3 className="text-sm font-medium text-gray-500 mb-1 print:text-gray-700">
                  Metode Pembayaran
                </h3>
                <p className="text-base font-medium capitalize print:text-black">
                  {invoice.payment_method === 'bank_transfer'
                    ? 'Transfer Bank'
                    : invoice.payment_method === 'cash'
                      ? 'Tunai'
                      : invoice.payment_method === 'credit_card'
                        ? 'Kartu Kredit'
                        : invoice.payment_method === 'cheque'
                          ? 'Cek'
                          : invoice.payment_method.replace('_', ' ')}
                </p>
              </div>
            </div>

            {/* Right column */}
            <div>
              {/* Invoice Number */}
              <div className="mb-4 print:mb-5">
                <h3 className="text-sm font-medium text-gray-500 mb-1 print:text-gray-700">
                  Nomor Faktur
                </h3>
                <p className="text-base font-medium print:text-black">
                  {invoice.invoice_number}
                </p>
              </div>

              {/* Creation Date */}
              <div className="mb-4 print:mb-5">
                <h3 className="text-sm font-medium text-gray-500 mb-1 print:text-gray-700">
                  Tanggal Pembuatan Faktur
                </h3>
                <p className="text-base font-medium print:text-black">
                  {formatDate(invoice.created_at)}
                </p>
              </div>

              {/* Due Date */}
              <div className="mb-4 print:mb-5">
                <h3 className="text-sm font-medium text-gray-500 mb-1 print:text-gray-700">
                  Tenggat Bayar
                </h3>
                <p className="text-base font-medium print:text-black">
                  {formatDate(invoice.due_date)}
                </p>
              </div>

              {/* Status */}
              <div className="mb-4 print:mb-5">
                <h3 className="text-sm font-medium text-gray-500 mb-1 print:text-gray-700">
                  Status
                </h3>
                <div className="print:hidden">
                  <InvoiceStatusBadge status={invoice.payment_status} />
                </div>
                <div className="hidden print:block">
                  {invoice.payment_status === 'paid' && (
                    <span className="text-green-600 font-medium">Paid</span>
                  )}
                  {invoice.payment_status === 'partial' && (
                    <span className="text-amber-600 font-medium">
                      Partially Paid
                    </span>
                  )}
                  {invoice.payment_status === 'pending' && (
                    <span className="text-blue-600 font-medium">Pending</span>
                  )}
                  {invoice.payment_status === 'overdue' && (
                    <span className="text-red-600 font-medium">Overdue</span>
                  )}
                  {invoice.payment_status === 'cancelled' && (
                    <span className="text-gray-600 font-medium">Cancelled</span>
                  )}
                </div>
              </div>
            </div>
          </div>

          {/* Invoice Items section */}
          <div className="mt-12 mb-4 print:mt-8">
            <h3 className="font-medium mb-4 print:mb-4 print:text-black">
              Item Faktur
            </h3>
            <InvoiceItemsList
              items={invoice.items || []}
              className="min-w-full print:border print:border-gray-200"
            />
          </div>

          {/* Notes section - always shown */}
          <div className="mt-6 print:mt-8 print:border-t print:border-gray-200 print:pt-4">
            <h3 className="text-sm font-medium mb-2 print:text-gray-700">
              Catatan
            </h3>
            <p className="text-sm text-gray-700 print:text-black">
              {invoice.notes || 'Please pay before due date'}
            </p>
          </div>
        </div>
      </div>

      {/* Invoice History Timeline - hidden during print */}
      <div className="print:hidden">
        <InvoiceTimeline invoice={invoice} />
      </div>

      {/* Dialog components - hidden during print */}
      <div className="print:hidden">
        {canDelete && (
          <DeleteInvoiceDialog
            invoice={invoice}
            open={showDeleteDialog}
            onOpenChange={setShowDeleteDialog}
            onConfirm={handleConfirmDelete}
            isDeleting={isDeleting}
            error={deleteError}
          />
        )}
        <PaymentProofModal />
      </div>
    </div>
  );
}
