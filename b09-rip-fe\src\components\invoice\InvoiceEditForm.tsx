'use client';

import React, { useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { toast } from 'sonner';
import { format } from 'date-fns';
import { useInvoiceEditStore } from '@/lib/store/invoice-edit-store';
import { InvoiceItemsTable } from './InvoiceItemsTable';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import {
  InvoiceType,
  PaymentMethod,
  PaymentStatus,
  ServiceType,
} from '@/types/invoice';
import { Skeleton } from '@/components/ui/skeleton';
import { AlertCircle, Loader2 } from 'lucide-react';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { ProjectCombobox } from '@/components/project/ProjectCombobox';

interface InvoiceEditFormProps {
  id: string;
}

export function InvoiceEditForm({ id }: InvoiceEditFormProps) {
  const router = useRouter();
  const {
    originalInvoice,
    formData,
    fieldErrors,
    itemErrors,
    generalError,
    isLoading,
    isSubmitting,
    loadInvoice,
    updateField,
    addItem,
    updateItem,
    removeItem,
    resetForm,
    submitForm,
  } = useInvoiceEditStore();

  // Handle project selection from combobox
  const handleProjectSelect = (projectId: string, projectName: string) => {
    updateField('project_id', projectId);
    updateField('project_name', projectName);
  };

  // Load invoice data on component mount
  useEffect(() => {
    loadInvoice(id);

    // Cleanup on unmount
    return () => {
      resetForm();
    };
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [id]);

  // Handle form submission
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    const success = await submitForm(id);
    if (success) {
      toast.success('Faktur berhasil diperbarui');

      // Navigate back to invoice details
      setTimeout(() => {
        router.push(`/invoice/${id}`);
      }, 1500);
    } else if (generalError) {
      toast.error(generalError);
    }
  };

  // Handle reset form to original data
  const handleReset = () => {
    resetForm();
    toast.info('Form dikembalikan ke data semula');
  };

  // Handle cancel and return to invoice details
  const handleCancel = () => {
    router.push(`/invoice/${id}`);
  };

  // Invoice type options
  const invoiceTypes: { value: InvoiceType; label: string }[] = [
    { value: 'internal', label: 'Internal' },
    { value: 'external', label: 'External' },
  ];

  // Service type options
  const serviceTypes: { value: ServiceType; label: string }[] = [
    { value: 'HCM', label: 'HCM' },
    { value: 'ORDEV', label: 'ORDEV' },
    { value: 'BE', label: 'BE' },
    { value: 'IT', label: 'IT' },
    { value: 'MARKETING', label: 'Marketing' },
    { value: 'FINANCE', label: 'Finance' },
    { value: 'SALES', label: 'Sales' },
    { value: 'OTHER', label: 'Other' },
  ];

  // Payment method options
  const paymentMethods: { value: PaymentMethod; label: string }[] = [
    { value: 'bank_transfer', label: 'Bank Transfer' },
    { value: 'cash', label: 'Cash' },
    { value: 'credit_card', label: 'Credit Card' },
    { value: 'cheque', label: 'Check' },
    { value: 'other', label: 'Other' },
  ];

  // Payment status options
  const paymentStatuses: { value: PaymentStatus; label: string }[] = [
    { value: 'pending', label: 'Pending' },
    { value: 'partial', label: 'Partially Paid' },
    { value: 'paid', label: 'Paid' },
    { value: 'overdue', label: 'Overdue' },
    { value: 'cancelled', label: 'Cancelled' },
  ];

  // Show loading state
  if (isLoading) {
    return (
      <div className="space-y-6">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div className="space-y-2">
            <Skeleton className="h-5 w-32" />
            <Skeleton className="h-10 w-full" />
          </div>
          <div className="space-y-2">
            <Skeleton className="h-5 w-32" />
            <Skeleton className="h-10 w-full" />
          </div>
          <div className="space-y-2">
            <Skeleton className="h-5 w-32" />
            <Skeleton className="h-10 w-full" />
          </div>
          <div className="space-y-2">
            <Skeleton className="h-5 w-32" />
            <Skeleton className="h-10 w-full" />
          </div>
        </div>
        <Skeleton className="h-64 w-full" />
      </div>
    );
  }

  // Show error if loading fails
  if (!isLoading && !originalInvoice) {
    return (
      <Alert variant="destructive">
        <AlertCircle className="h-4 w-4" />
        <AlertDescription>
          {generalError || 'Gagal memuat data faktur. Silakan coba lagi.'}
        </AlertDescription>
      </Alert>
    );
  }

  return (
    <form onSubmit={handleSubmit} className="space-y-8">
      {generalError && (
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>{generalError}</AlertDescription>
        </Alert>
      )}

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {/* Invoice Number (Read-only) */}
        <div className="space-y-2">
          <Label htmlFor="invoice_number">Nomor Faktur</Label>
          <Input
            id="invoice_number"
            value={originalInvoice?.invoice_number || ''}
            readOnly
            disabled
            className="bg-gray-50"
          />
        </div>

        {/* Created Date (Read-only) */}
        <div className="space-y-2">
          <Label htmlFor="created_at">Tanggal Dibuat</Label>
          <Input
            id="created_at"
            value={
              originalInvoice
                ? format(new Date(originalInvoice.created_at), 'PPP')
                : ''
            }
            readOnly
            disabled
            className="bg-gray-50"
          />
        </div>

        {/* Invoice Type */}
        <div className="space-y-2">
          <Label htmlFor="invoice_type">
            Tipe Faktur <span className="text-red-500">*</span>
          </Label>
          <Select
            value={formData.invoice_type}
            onValueChange={(value) =>
              updateField('invoice_type', value as InvoiceType)
            }
            disabled={true}
          >
            <SelectTrigger id="invoice_type" className="bg-gray-50">
              <SelectValue placeholder="Pilih tipe faktur" />
            </SelectTrigger>
            <SelectContent>
              {invoiceTypes.map((type) => (
                <SelectItem key={type.value} value={type.value}>
                  {type.label}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>

        {/* Service Type */}
        <div className="space-y-2">
          <Label htmlFor="service_type">
            Tipe Layanan <span className="text-red-500">*</span>
          </Label>
          <Select
            value={formData.service_type}
            onValueChange={(value) =>
              updateField('service_type', value as ServiceType)
            }
            disabled={true}
          >
            <SelectTrigger id="service_type" className="bg-gray-50">
              <SelectValue placeholder="Pilih tipe layanan" />
            </SelectTrigger>
            <SelectContent>
              {serviceTypes.map((type) => (
                <SelectItem key={type.value} value={type.value}>
                  {type.label}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>

        {/* Recipient Name */}
        <div className="space-y-2">
          <Label htmlFor="recipient_name">
            Penerima <span className="text-red-500">*</span>
          </Label>
          <Input
            id="recipient_name"
            value={formData.recipient_name}
            onChange={(e) => updateField('recipient_name', e.target.value)}
            placeholder="Masukkan nama penerima"
            className={fieldErrors.recipient_name ? 'border-red-500' : ''}
            disabled={isSubmitting}
          />
          {fieldErrors.recipient_name && (
            <p className="text-sm text-red-500">{fieldErrors.recipient_name}</p>
          )}
        </div>

        {/* Due Date */}
        <div className="space-y-2">
          <Label htmlFor="due_date">
            Tanggal Jatuh Tempo <span className="text-red-500">*</span>
          </Label>
          <Input
            id="due_date"
            type="date"
            value={formData.due_date}
            onChange={(e) => {
              // Ensure we're getting a valid date
              const dateValue = e.target.value;
              if (dateValue) {
                updateField('due_date', dateValue);
              }
            }}
            className={fieldErrors.due_date ? 'border-red-500' : ''}
            disabled={isSubmitting}
          />
          {fieldErrors.due_date && (
            <p className="text-sm text-red-500">{fieldErrors.due_date}</p>
          )}
        </div>

        {/* Project (Optional) */}
        <div className="space-y-2 md:col-span-2">
          <Label htmlFor="project">Proyek (Opsional)</Label>
          <ProjectCombobox
            value={formData.project_id || ''}
            onSelect={handleProjectSelect}
            placeholder="Pilih proyek..."
            disabled={isSubmitting}
          />
        </div>

        {/* Payment Method */}
        <div className="space-y-2">
          <Label htmlFor="payment_method">
            Metode Pembayaran <span className="text-red-500">*</span>
          </Label>
          <Select
            value={formData.payment_method}
            onValueChange={(value) =>
              updateField('payment_method', value as PaymentMethod)
            }
            disabled={isSubmitting}
          >
            <SelectTrigger id="payment_method">
              <SelectValue placeholder="Pilih metode pembayaran" />
            </SelectTrigger>
            <SelectContent>
              {paymentMethods.map((method) => (
                <SelectItem key={method.value} value={method.value}>
                  {method.label}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>

        {/* Payment Status */}
        <div className="space-y-2">
          <Label htmlFor="payment_status">
            Status Pembayaran <span className="text-red-500">*</span>
          </Label>
          <Select
            value={formData.payment_status}
            onValueChange={(value) =>
              updateField('payment_status', value as PaymentStatus)
            }
            disabled={isSubmitting}
          >
            <SelectTrigger id="payment_status">
              <SelectValue placeholder="Select payment status" />
            </SelectTrigger>
            <SelectContent>
              {paymentStatuses.map((status) => (
                <SelectItem key={status.value} value={status.value}>
                  {status.label}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>

        {/* Notes (Optional) */}
        <div className="space-y-2 md:col-span-2">
          <Label htmlFor="notes">Catatan (Opsional)</Label>
          <Textarea
            id="notes"
            value={formData.notes || ''}
            onChange={(e) => updateField('notes', e.target.value)}
            placeholder="Masukkan catatan tambahan"
            rows={3}
            disabled={isSubmitting}
          />
        </div>
      </div>

      {/* Invoice Items */}
      <div className="space-y-4">
        <div className="flex items-center justify-between">
          <h3 className="text-lg font-medium">Item Faktur</h3>
        </div>

        <InvoiceItemsTable
          items={formData.items}
          onAddItem={addItem}
          onUpdateItem={updateItem}
          onRemoveItem={removeItem}
          disabled={isSubmitting}
        />

        {fieldErrors.items && (
          <p className="text-sm text-red-500">{fieldErrors.items}</p>
        )}

        {/* Item-specific errors */}
        {Object.keys(itemErrors).length > 0 && (
          <div className="text-sm text-red-500">
            Silakan perbaiki kesalahan pada item faktur.
          </div>
        )}
      </div>

      {/* Form Actions */}
      <div className="flex flex-col sm:flex-row gap-3 justify-end">
        <Button
          type="button"
          variant="outline"
          onClick={handleCancel}
          disabled={isSubmitting}
        >
          Batal
        </Button>
        <Button
          type="button"
          variant="secondary"
          onClick={handleReset}
          disabled={isSubmitting}
        >
          Reset
        </Button>
        <Button type="submit" disabled={isSubmitting}>
          {isSubmitting && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
          Perbarui Faktur
        </Button>
      </div>
    </form>
  );
}
