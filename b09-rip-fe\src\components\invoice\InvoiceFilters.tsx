'use client';

import { SearchFilter, Filter } from '@/components/ui/search-filter';

interface InvoiceFiltersProps {
  search: string;
  payment_status: string | undefined;
  onSearchChange: (value: string | undefined) => void;
  onStatusChange: (value: string | undefined) => void;
}

export function InvoiceFilters({
  search,
  payment_status,
  onSearchChange,
  onStatusChange,
}: InvoiceFiltersProps) {
  const statusFilter: Filter = {
    label: 'Status Pembayaran',
    value: payment_status,
    onChange: onStatusChange,
    options: [
      { value: 'pending', label: 'Pending' },
      { value: 'partial', label: 'Partial' },
      { value: 'paid', label: 'Paid' },
      { value: 'overdue', label: 'Overdue' },
      { value: 'cancelled', label: 'Cancelled' },
    ],
  };

  return (
    <SearchFilter
      search={search}
      onSearchChange={(value) => onSearchChange(value)}
      filters={[statusFilter]}
      searchPlaceholder="Cari invoice..."
    />
  );
}
