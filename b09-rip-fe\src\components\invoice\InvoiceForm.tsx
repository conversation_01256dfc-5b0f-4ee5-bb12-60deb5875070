'use client';

import React, { useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { toast } from 'sonner';
import { useInvoiceFormStore } from '@/lib/store/invoice-form-store';
import { InvoiceItemsTable } from './InvoiceItemsTable';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { InvoiceType, PaymentMethod, ServiceType } from '@/types/invoice';
import { ProjectCombobox } from '@/components/project/ProjectCombobox';

export function InvoiceForm() {
  const router = useRouter();
  const {
    formData,
    fieldErrors,
    itemErrors,
    generalError,
    isSubmitting,
    updateField,
    addItem,
    updateItem,
    removeItem,
    resetForm,
    submitForm,
  } = useInvoiceFormStore();

  // Handle project selection from combobox
  const handleProjectSelect = (projectId: string, projectName: string) => {
    updateField('project_id', projectId);
    updateField('project_name', projectName);
  };

  // Handle form submission
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    const success = await submitForm();
    if (success) {
      toast.success('Faktur berhasil dibuat');

      // Navigate back to invoices list
      setTimeout(() => {
        router.push('/invoice');
      }, 1500);
    } else if (generalError) {
      toast.error(generalError);
    }
  };

  // Reset form when leaving the page
  useEffect(() => {
    return () => {
      resetForm();
    };
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  // Invoice type options
  const invoiceTypes: { value: InvoiceType; label: string }[] = [
    { value: 'internal', label: 'Internal' },
    { value: 'external', label: 'External' },
  ];

  // Service type options
  const serviceTypes: { value: ServiceType; label: string }[] = [
    { value: 'HCM', label: 'HCM' },
    { value: 'ORDEV', label: 'ORDEV' },
    { value: 'BE', label: 'BE' },
    { value: 'IT', label: 'IT' },
    { value: 'MARKETING', label: 'Marketing' },
    { value: 'FINANCE', label: 'Finance' },
    { value: 'SALES', label: 'Sales' },
    { value: 'OTHER', label: 'Other' },
  ];

  // Payment method options
  const paymentMethods: { value: PaymentMethod; label: string }[] = [
    { value: 'bank_transfer', label: 'Bank Transfer' },
    { value: 'cash', label: 'Cash' },
    { value: 'credit_card', label: 'Credit Card' },
    { value: 'cheque', label: 'Check' },
    { value: 'other', label: 'Other' },
  ];

  return (
    <form onSubmit={handleSubmit} className="space-y-8">
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {/* Invoice Type */}
        <div className="space-y-2">
          <Label htmlFor="invoice_type">
            Tipe Faktur <span className="text-red-500">*</span>
          </Label>
          <Select
            value={formData.invoice_type}
            onValueChange={(value) =>
              updateField('invoice_type', value as InvoiceType)
            }
          >
            <SelectTrigger id="invoice_type">
              <SelectValue placeholder="Pilih tipe faktur" />
            </SelectTrigger>
            <SelectContent>
              {invoiceTypes.map((type) => (
                <SelectItem key={type.value} value={type.value}>
                  {type.label}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>

        {/* Service Type */}
        <div className="space-y-2">
          <Label htmlFor="service_type">
            Tipe Layanan <span className="text-red-500">*</span>
          </Label>
          <Select
            value={formData.service_type}
            onValueChange={(value) =>
              updateField('service_type', value as ServiceType)
            }
          >
            <SelectTrigger id="service_type">
              <SelectValue placeholder="Pilih tipe layanan" />
            </SelectTrigger>
            <SelectContent>
              {serviceTypes.map((type) => (
                <SelectItem key={type.value} value={type.value}>
                  {type.label}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>

        {/* Recipient Name */}
        <div className="space-y-2">
          <Label htmlFor="recipient_name">
            Penerima <span className="text-red-500">*</span>
          </Label>
          <Input
            id="recipient_name"
            value={formData.recipient_name}
            onChange={(e) => updateField('recipient_name', e.target.value)}
            placeholder="Masukkan nama penerima"
            className={fieldErrors.recipient_name ? 'border-red-500' : ''}
          />
          {fieldErrors.recipient_name && (
            <p className="text-sm text-red-500">{fieldErrors.recipient_name}</p>
          )}
        </div>

        {/* Due Date */}
        <div className="space-y-2">
          <Label htmlFor="due_date">
            Tanggal Jatuh Tempo <span className="text-red-500">*</span>
          </Label>
          <Input
            id="due_date"
            type="date"
            value={formData.due_date}
            onChange={(e) => updateField('due_date', e.target.value)}
            className={fieldErrors.due_date ? 'border-red-500' : ''}
          />
          {fieldErrors.due_date && (
            <p className="text-sm text-red-500">{fieldErrors.due_date}</p>
          )}
        </div>

        {/* Project (Optional) */}
        <div className="space-y-2 md:col-span-2">
          <Label htmlFor="project">Proyek (Opsional)</Label>
          <ProjectCombobox
            value={formData.project_id || ''}
            onSelect={handleProjectSelect}
            placeholder="Pilih proyek..."
            disabled={isSubmitting}
          />
        </div>

        {/* Payment Method */}
        <div className="space-y-2">
          <Label htmlFor="payment_method">
            Metode Pembayaran <span className="text-red-500">*</span>
          </Label>
          <Select
            value={formData.payment_method}
            onValueChange={(value) =>
              updateField('payment_method', value as PaymentMethod)
            }
          >
            <SelectTrigger id="payment_method">
              <SelectValue placeholder="Pilih metode pembayaran" />
            </SelectTrigger>
            <SelectContent>
              {paymentMethods.map((method) => (
                <SelectItem key={method.value} value={method.value}>
                  {method.label}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>

        {/* Notes (Optional) */}
        <div className="space-y-2 md:col-span-2">
          <Label htmlFor="notes">Catatan (Opsional)</Label>
          <Textarea
            id="notes"
            value={formData.notes || ''}
            onChange={(e) => updateField('notes', e.target.value)}
            placeholder="Masukkan catatan tambahan"
            rows={3}
          />
        </div>
      </div>

      {/* Invoice Items */}
      <div className="space-y-4">
        <div className="flex items-center justify-between">
          <h3 className="text-lg font-medium">Item Faktur</h3>
        </div>

        <InvoiceItemsTable
          items={formData.items}
          onAddItem={addItem}
          onUpdateItem={updateItem}
          onRemoveItem={removeItem}
          disabled={isSubmitting}
        />

        {fieldErrors.items && (
          <p className="text-sm text-red-500">{fieldErrors.items}</p>
        )}

        {/* Item-specific errors */}
        {Object.keys(itemErrors).length > 0 && (
          <div className="text-sm text-red-500">
            Silakan perbaiki kesalahan pada item faktur.
          </div>
        )}
      </div>

      {/* Form Actions */}
      <div className="flex justify-end space-x-4">
        <Button
          type="button"
          variant="outline"
          onClick={() => router.push('/invoice')}
          disabled={isSubmitting}
        >
          Batal
        </Button>
        <Button type="submit" disabled={isSubmitting}>
          {isSubmitting ? 'Membuat Faktur...' : 'Buat Faktur'}
        </Button>
      </div>
    </form>
  );
}
