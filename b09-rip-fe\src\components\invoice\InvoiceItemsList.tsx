'use client';

import React from 'react';
import { InvoiceItem } from '@/types/invoice';
import { formatCurrency } from '@/lib/utils';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { cn } from '@/lib/utils';

interface InvoiceItemsListProps {
  items: InvoiceItem[];
  className?: string;
}

export function InvoiceItemsList({ items, className }: InvoiceItemsListProps) {
  // Helper to split item name into main title and subtitle
  const parseItemName = (name: string) => {
    const parts = name.split(' - ');
    return {
      title: parts[0],
      subtitle: parts.length > 1 ? parts.slice(1).join(' - ') : '',
    };
  };

  // Calculate total amount from items
  const totalAmount = items.reduce((sum, item) => sum + item.total_price, 0);

  return (
    <div
      className={cn(
        'rounded-lg border border-gray-200 print:border-gray-300 overflow-hidden',
        className
      )}
    >
      <div className="overflow-x-auto">
        <Table>
          <TableHeader>
            <TableRow className="bg-gray-50 print:bg-gray-100">
              <TableHead className="w-12 text-center print:text-black">
                No.
              </TableHead>
              <TableHead className="print:text-black">Deskripsi</TableHead>
              <TableHead className="w-24 text-center print:text-black">
                Qty
              </TableHead>
              <TableHead className="w-44 text-right print:text-black">
                Harga Satuan
              </TableHead>
              <TableHead className="w-44 text-right print:text-black">
                Biaya
              </TableHead>
            </TableRow>
          </TableHeader>

          <TableBody>
            {items.length === 0 ? (
              <TableRow>
                <TableCell
                  colSpan={5}
                  className="text-center py-8 text-gray-500 print:text-black"
                >
                  No items found
                </TableCell>
              </TableRow>
            ) : (
              items.map((item, index) => {
                const { title, subtitle } = parseItemName(item.item_name);
                return (
                  <TableRow
                    key={item.id || index}
                    className={cn(
                      'border-b hover:bg-blue-50/30 transition-colors print:hover:bg-transparent',
                      index % 2 === 0 ? 'bg-white' : 'bg-gray-50/50',
                      'print:bg-transparent'
                    )}
                  >
                    <TableCell className="align-top py-4 text-gray-600 print:text-black font-medium pl-4 print:pr-0 text-center">
                      {index + 1}
                    </TableCell>
                    <TableCell className="py-4 print:py-3">
                      <div className="font-medium text-gray-900 print:text-black">
                        {title}
                      </div>
                      {subtitle && (
                        <div className="text-sm text-gray-500 print:text-gray-700 mt-1">
                          {subtitle}
                        </div>
                      )}
                    </TableCell>
                    <TableCell className="text-center align-top py-4 print:py-3 text-gray-700 print:text-black font-medium">
                      {item.item_amount}
                    </TableCell>
                    <TableCell className="text-right align-top py-4 print:py-3 text-gray-700 print:text-black font-medium">
                      {formatCurrency(item.item_price)}
                    </TableCell>
                    <TableCell className="text-right align-top py-4 print:py-3 text-gray-900 print:text-black font-bold">
                      {formatCurrency(item.total_price)}
                    </TableCell>
                  </TableRow>
                );
              })
            )}
          </TableBody>
        </Table>
      </div>

      {/* Total amount row */}
      <div className="flex justify-end items-center py-5 px-6 mt-0 print:bg-transparent print:border-t print:border-gray-300 print:mt-0 print:py-4">
        <div className="text-base font-semibold text-gray-700 print:text-black pr-20">
          Total
        </div>
        <div className="text-xl font-bold text-gray-900 print:text-black w-44 text-right">
          {formatCurrency(totalAmount)}
        </div>
      </div>
    </div>
  );
}
