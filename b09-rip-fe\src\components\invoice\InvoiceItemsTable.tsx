'use client';

import React from 'react';
import { PlusCircle, Trash2 } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { CreateInvoiceItemDto } from '@/types/invoice';
import { formatCurrency } from '@/lib/utils';

interface InvoiceItemsTableProps {
  items: CreateInvoiceItemDto[];
  onAddItem: () => void;
  onUpdateItem: (
    index: number,
    field: keyof CreateInvoiceItemDto,
    value: string | number
  ) => void;
  onRemoveItem: (index: number) => void;
  disabled?: boolean;
}

export function InvoiceItemsTable({
  items,
  onAddItem,
  onUpdateItem,
  onRemoveItem,
  disabled = false,
}: InvoiceItemsTableProps) {
  const calculateSubtotal = (item: CreateInvoiceItemDto) => {
    const quantity = parseFloat(item.item_amount.toString()) || 0;
    const unitPrice = parseFloat(item.item_price.toString()) || 0;
    return quantity * unitPrice;
  };

  return (
    <div className="border rounded-md">
      <Table>
        <TableHeader>
          <TableRow>
            <TableHead className="w-[40%]">Deskripsi</TableHead>
            <TableHead className="w-[15%]">Jumlah</TableHead>
            <TableHead className="w-[20%]">Harga Unit</TableHead>
            <TableHead className="w-[20%]">Subtotal</TableHead>
            <TableHead className="w-[5%]"></TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {items.length === 0 ? (
            <TableRow>
              <TableCell
                colSpan={5}
                className="text-center py-6 text-muted-foreground"
              >
                Belum ada item. Tambahkan item untuk memulai.
              </TableCell>
            </TableRow>
          ) : (
            items.map((item, index) => (
              <TableRow key={index}>
                <TableCell>
                  <Input
                    value={item.item_name}
                    onChange={(e) =>
                      onUpdateItem(index, 'item_name', e.target.value)
                    }
                    placeholder="Masukkan deskripsi item"
                    disabled={disabled}
                  />
                </TableCell>
                <TableCell>
                  <Input
                    type="number"
                    min="1"
                    value={item.item_amount}
                    onChange={(e) =>
                      onUpdateItem(
                        index,
                        'item_amount',
                        parseFloat(e.target.value)
                      )
                    }
                    placeholder="Jml"
                    disabled={disabled}
                  />
                </TableCell>
                <TableCell>
                  <Input
                    type="number"
                    min="0"
                    step="0.01"
                    value={item.item_price}
                    onChange={(e) =>
                      onUpdateItem(
                        index,
                        'item_price',
                        parseFloat(e.target.value)
                      )
                    }
                    placeholder="0.00"
                    disabled={disabled}
                  />
                </TableCell>
                <TableCell className="font-medium">
                  {formatCurrency(calculateSubtotal(item))}
                </TableCell>
                <TableCell>
                  <Button
                    variant="ghost"
                    size="icon"
                    onClick={() => onRemoveItem(index)}
                    disabled={disabled}
                  >
                    <Trash2 className="h-4 w-4 text-destructive" />
                  </Button>
                </TableCell>
              </TableRow>
            ))
          )}
        </TableBody>
      </Table>

      <div className="p-4 flex items-center justify-between border-t">
        <Button
          type="button"
          variant="outline"
          size="sm"
          onClick={onAddItem}
          disabled={disabled}
          className="flex items-center gap-1"
        >
          <PlusCircle className="h-4 w-4" />
          Tambah Item
        </Button>

        <div className="text-right">
          <div className="flex justify-end gap-4 items-center">
            <span className="text-muted-foreground">Total:</span>
            <span className="text-lg font-bold">
              {formatCurrency(
                items.reduce((sum, item) => sum + calculateSubtotal(item), 0)
              )}
            </span>
          </div>
        </div>
      </div>
    </div>
  );
}
