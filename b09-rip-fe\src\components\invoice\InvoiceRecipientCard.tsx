'use client';

import React from 'react';
import { format } from 'date-fns';
import { Invoice } from '@/types/invoice';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Calendar, Building, CreditCard, FileText } from 'lucide-react';
import { cn } from '@/lib/utils';

interface InvoiceRecipientCardProps {
  invoice: Invoice;
  className?: string;
}

export function InvoiceRecipientCard({
  invoice,
  className = '',
}: InvoiceRecipientCardProps) {
  // Format the dates in DD/MM/YYYY format
  const formatDate = (dateString: string) => {
    try {
      return format(new Date(dateString), 'dd/MM/yyyy');
    } catch {
      console.error('Invalid date:', dateString);
      return 'Invalid date';
    }
  };

  // Format for created_at and updated_at
  const createdDate = formatDate(invoice.created_at);
  const dueDate = formatDate(invoice.due_date);
  const lastUpdated = invoice.updated_at ? formatDate(invoice.updated_at) : '-';

  return (
    <Card
      className={cn(
        'overflow-hidden border border-gray-200 print:border-0 print:shadow-none',
        className
      )}
    >
      <CardHeader className="bg-gray-50 pb-3 border-b print:bg-transparent print:px-0">
        <CardTitle className="text-lg flex items-center">
          <Building className="h-5 w-5 mr-2 text-gray-500 print:text-black" />
          <span className="text-gray-900 print:text-black font-semibold">
            {invoice.recipient_name}
          </span>
        </CardTitle>
      </CardHeader>
      <CardContent className="p-0 print:px-0">
        <div className="divide-y divide-gray-100">
          {/* Project details section */}
          {(invoice.project_id || invoice.project_name) && (
            <div className="p-5 print:px-0 print:py-3">
              <div className="flex items-start">
                <FileText className="h-4 w-4 text-gray-500 print:text-black mt-1 mr-3 flex-shrink-0" />
                <div>
                  <p className="text-sm font-medium text-gray-700 print:text-black mb-2">
                    Project
                  </p>
                  <p className="text-sm text-gray-900 print:text-black">
                    {invoice.project_name || 'Unnamed Project'}
                    {invoice.project_id && (
                      <span className="text-xs text-gray-500 print:text-gray-600 ml-1">
                        (ID: {invoice.project_id})
                      </span>
                    )}
                  </p>
                </div>
              </div>
            </div>
          )}

          {/* Payment method section */}
          <div className="p-5 print:px-0 print:py-3 print:border-t-0">
            <div className="flex items-start">
              <CreditCard className="h-4 w-4 text-gray-500 print:text-black mt-1 mr-3 flex-shrink-0" />
              <div>
                <p className="text-sm font-medium text-gray-700 print:text-black mb-2">
                  Payment Method
                </p>
                <p className="text-sm text-gray-900 print:text-black capitalize">
                  {invoice.payment_method.replace('_', ' ')}
                </p>
              </div>
            </div>
          </div>

          {/* Dates section */}
          <div className="p-5 print:px-0 print:py-3 print:border-t-0">
            <div className="flex items-start">
              <Calendar className="h-4 w-4 text-gray-500 print:text-black mt-1 mr-3 flex-shrink-0" />
              <div className="w-full">
                <p className="text-sm font-medium text-gray-700 print:text-black mb-3">
                  Important Dates
                </p>

                <div className="grid grid-cols-1 gap-4 print:gap-2">
                  <div>
                    <p className="text-xs font-medium text-gray-500 print:text-gray-600 mb-1">
                      Tanggal Pembuatan Faktur
                    </p>
                    <p className="text-sm text-gray-900 print:text-black">
                      {createdDate}
                    </p>
                  </div>

                  <div>
                    <p className="text-xs font-medium text-gray-500 print:text-gray-600 mb-1">
                      Tenggat Bayar
                    </p>
                    <p className="text-sm font-medium text-amber-700 print:text-black">
                      {dueDate}
                    </p>
                  </div>

                  <div className="print:hidden">
                    <p className="text-xs font-medium text-gray-500 mb-1">
                      Waktu Perubahan
                    </p>
                    <p className="text-sm text-gray-900">{lastUpdated}</p>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Notes if available */}
          {invoice.notes && (
            <div className="p-5 print:px-0 print:py-3 bg-gray-50 print:bg-transparent print:border-t-0">
              <p className="text-sm font-medium text-gray-700 print:text-black mb-2">
                Notes
              </p>
              <p className="text-sm text-gray-700 print:text-black">
                {invoice.notes}
              </p>
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  );
}
