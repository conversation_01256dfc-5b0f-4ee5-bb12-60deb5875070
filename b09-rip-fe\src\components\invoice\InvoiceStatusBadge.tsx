import React from 'react';
import { Badge } from '@/components/ui/badge';
import { PaymentStatus } from '@/types/invoice';

interface InvoiceStatusBadgeProps {
  status: PaymentStatus;
  className?: string;
}

export function InvoiceStatusBadge({
  status,
  className,
}: InvoiceStatusBadgeProps) {
  // Get appropriate variant for the status
  const getVariant = (status: PaymentStatus) => {
    switch (status) {
      case 'pending':
        return 'warning';
      case 'partial':
        return 'info';
      case 'paid':
        return 'success';
      case 'overdue':
        return 'danger';
      case 'cancelled':
        return 'secondary';
      default:
        return 'secondary';
    }
  };

  // Format status text for display
  const formatStatus = (status: PaymentStatus) => {
    return status.charAt(0).toUpperCase() + status.slice(1);
  };

  return (
    <Badge variant={getVariant(status)} className={className}>
      {formatStatus(status)}
    </Badge>
  );
}
