'use client';

import React from 'react';
import { Check, ChevronDown, Loader2 } from 'lucide-react';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { Button } from '@/components/ui/button';
import { PaymentStatus } from '@/types/invoice';
import { useInvoiceDetailStore } from '@/lib/store/invoice-detail-store';
import { toast } from 'sonner';
import { cn } from '@/lib/utils';

const statusOptions: { value: PaymentStatus; label: string }[] = [
  { value: 'pending', label: 'Pending' },
  { value: 'partial', label: 'Partial' },
  { value: 'paid', label: 'Paid' },
  { value: 'overdue', label: 'Overdue' },
  { value: 'cancelled', label: 'Cancelled' },
];

const getStatusColor = (status: PaymentStatus): string => {
  switch (status) {
    case 'pending':
      return 'text-yellow-500';
    case 'partial':
      return 'text-blue-500';
    case 'paid':
      return 'text-green-500';
    case 'overdue':
      return 'text-red-500';
    case 'cancelled':
      return 'text-gray-500';
    default:
      return 'text-muted-foreground';
  }
};

interface InvoiceStatusUpdateProps {
  className?: string;
}

export function InvoiceStatusUpdate({ className }: InvoiceStatusUpdateProps) {
  const { invoice, isUpdatingStatus, updateInvoiceStatus } =
    useInvoiceDetailStore();

  if (!invoice) return null;

  const currentStatus = invoice.payment_status;

  const handleStatusChange = async (status: PaymentStatus) => {
    if (status === currentStatus) return;

    const success = await updateInvoiceStatus(invoice.id, status);

    if (success) {
      toast.success(`Status updated to ${status}`);
    } else {
      toast.error('Failed to update status');
    }
  };

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button
          variant="outline"
          className={cn('flex items-center gap-1 min-w-[120px]', className)}
          disabled={isUpdatingStatus}
        >
          {isUpdatingStatus ? (
            <>
              <Loader2 className="h-4 w-4 animate-spin mr-1" />
              Updating...
            </>
          ) : (
            <>
              <span className={cn('capitalize', getStatusColor(currentStatus))}>
                {currentStatus}
              </span>
              <ChevronDown className="h-4 w-4 ml-auto" />
            </>
          )}
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end">
        {statusOptions.map((option) => (
          <DropdownMenuItem
            key={option.value}
            className={cn(
              'flex items-center gap-2 cursor-pointer',
              option.value === currentStatus && 'font-medium'
            )}
            onClick={() => handleStatusChange(option.value)}
          >
            {option.value === currentStatus && <Check className="h-4 w-4" />}
            <span
              className={cn(option.value === currentStatus ? 'ml-0' : 'ml-6')}
            >
              {option.label}
            </span>
          </DropdownMenuItem>
        ))}
      </DropdownMenuContent>
    </DropdownMenu>
  );
}
