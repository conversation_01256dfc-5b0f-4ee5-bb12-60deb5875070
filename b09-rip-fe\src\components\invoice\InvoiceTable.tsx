import React from 'react';
import { InvoiceStatusBadge } from './InvoiceStatusBadge';
import { Button } from '@/components/ui/button';
import { Eye, Pencil, Trash2 } from 'lucide-react';
import { Invoice } from '@/types/invoice';
import { format } from 'date-fns';
import { DataTable, SortDirection } from '@/components/ui/data-table';

interface InvoiceTableProps {
  invoices: Invoice[];
  isLoading: boolean;
  onView: (invoice: Invoice) => void;
  onEdit: (invoice: Invoice) => void;
  onDelete: (invoice: Invoice) => void;
  onSort?: (field: string, direction: SortDirection) => void;
  sortField?: string;
  sortDirection?: SortDirection;
  // Pagination props removed as they're not used in DataTable anymore
  // currentPage?: number;
  // itemsPerPage?: number;
  // onPageChange?: (page: number) => void;
}

export function InvoiceTable({
  invoices,
  isLoading,
  onView,
  onEdit,
  onDelete,
  onSort,
  sortField,
  sortDirection,
  // Pagination props removed as they're not used in DataTable anymore
  // currentPage = 1,
  // itemsPerPage = 10,
  // onPageChange,
}: InvoiceTableProps) {
  // Format date for display
  const formatDate = (dateStr: string) => {
    try {
      return format(new Date(dateStr), 'MMM dd, yyyy');
    } catch {
      return dateStr;
    }
  };

  // Format currency
  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('id-ID', {
      style: 'currency',
      currency: 'IDR',
      minimumFractionDigits: 0,
    }).format(amount);
  };

  // Define columns
  const columns = [
    {
      key: 'invoice_number',
      header: 'No. Faktur',
      sortable: false,
      width: '200px',
      render: (invoice: Invoice) => (
        <span className="font-medium">{invoice.invoice_number}</span>
      ),
    },
    {
      key: 'recipient_name',
      header: 'Penerima',
      render: (invoice: Invoice) => invoice.recipient_name,
    },
    {
      key: 'invoice_type',
      header: 'Tipe',
      render: (invoice: Invoice) => (
        <span className="capitalize">{invoice.invoice_type}</span>
      ),
    },
    {
      key: 'due_date',
      header: 'Tanggal Jatuh Tempo',
      sortable: false,
      render: (invoice: Invoice) => formatDate(invoice.due_date),
    },
    {
      key: 'payment_status',
      header: 'Status',
      render: (invoice: Invoice) => (
        <InvoiceStatusBadge status={invoice.payment_status} />
      ),
    },
    {
      key: 'total_amount',
      header: 'Jumlah',
      sortable: false,
      align: 'right',
      render: (invoice: Invoice) => formatCurrency(invoice.total_amount),
    },
    {
      key: 'actions',
      header: 'Aksi',
      align: 'right',
      render: (invoice: Invoice) => (
        <div className="flex justify-end space-x-1">
          <Button
            variant="ghost"
            size="sm"
            onClick={() => onView(invoice)}
            className="h-8 w-8 p-0 text-blue-500 hover:text-blue-600 hover:bg-blue-50"
          >
            <span className="sr-only">View</span>
            <Eye className="h-4 w-4" />
          </Button>
          <Button
            variant="ghost"
            size="sm"
            onClick={() => onEdit(invoice)}
            className="h-8 w-8 p-0 text-amber-500 hover:text-amber-600 hover:bg-amber-50"
          >
            <span className="sr-only">Edit</span>
            <Pencil className="h-4 w-4" />
          </Button>
          <Button
            variant="ghost"
            size="sm"
            onClick={() => onDelete(invoice)}
            className="h-8 w-8 p-0 text-red-500 hover:text-red-600 hover:bg-red-50"
          >
            <span className="sr-only">Delete</span>
            <Trash2 className="h-4 w-4" />
          </Button>
        </div>
      ),
    },
  ];

  return (
    <DataTable
      columns={columns}
      data={invoices}
      keyExtractor={(invoice) => invoice.id}
      loading={isLoading}
      sortField={sortField}
      sortDirection={sortDirection}
      onSort={onSort}
      // Pagination props removed as they're not used in DataTable anymore
      // currentPage={currentPage}
      // itemsPerPage={itemsPerPage}
      // onPageChange={onPageChange}
      emptyStateMessage="No invoices found. Try adjusting your search or filter to find what you're looking for."
      // showPagination prop removed as it's not used in DataTable anymore
      // showPagination={false}
    />
  );
}
