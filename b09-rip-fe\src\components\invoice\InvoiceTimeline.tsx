'use client';

import { Invoice, Invoice<PERSON> } from '@/types/invoice';
import { useInvoiceHistory } from '@/hooks/useInvoiceHistory';
import { formatDate } from '@/lib/formatters';
import { Button } from '@/components/ui/button';
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from '@/components/ui/tooltip';

interface InvoiceTimelineProps {
  invoice: Invoice;
}

interface TimelineItem {
  id: string;
  title: string;
  timestamp: string;
  createdBy: string;
  changes: InvoiceChange[];
}

export function InvoiceTimeline({ invoice }: InvoiceTimelineProps) {
  const { history, loading, error } = useInvoiceHistory(invoice.id);

  // Function to get user-friendly field name
  function getFieldDisplayName(field: string): string {
    const fieldMap: Record<string, string> = {
      project_id: 'ID Proyek',
      project_name: '<PERSON><PERSON>',
      payment_proof_upload: '<PERSON><PERSON><PERSON>',
      payment_proof_delete: '<PERSON><PERSON><PERSON>',
      payment_status: 'Status Pembayaran',
      recipient_name: '<PERSON><PERSON>',
      invoice_type: 'Tipe Faktur',
      service_type: 'Tipe Layanan',
      due_date: 'Tanggal Jatuh Tempo',
      payment_method: 'Metode Pembayaran',
      notes: 'Catatan',
      total_amount: 'Total Jumlah',
      amount_paid: 'Jumlah Dibayar',
    };

    return fieldMap[field] || field;
  }

  // Function to format change description
  function formatChangeDescription(change: InvoiceChange): string {
    const fieldName = getFieldDisplayName(change.field);

    if (change.field === 'payment_proof_upload') {
      return `${fieldName} diunggah: ${change.to_value || 'Tidak ada'}`;
    }

    if (change.field === 'payment_proof_delete') {
      return `${fieldName} dihapus: ${change.from_value || 'Tidak ada'}`;
    }

    const fromValue =
      change.from_value === null ? 'Tidak ada' : change.from_value;
    const toValue = change.to_value === null ? 'Tidak ada' : change.to_value;

    return `${fieldName} diubah dari "${fromValue}" menjadi "${toValue}"`;
  }

  // Function to get color based on change type
  function getChangeColor(change: InvoiceChange): string {
    if (change.field === 'payment_proof_upload') {
      return 'text-green-600';
    }

    if (change.field === 'payment_proof_delete') {
      return 'text-red-600';
    }

    if (change.field === 'payment_status') {
      if (change.to_value === 'paid') {
        return 'text-green-600';
      } else if (change.to_value === 'cancelled') {
        return 'text-red-600';
      } else if (change.to_value === 'overdue') {
        return 'text-orange-600';
      }
    }

    return 'text-blue-600';
  }

  // Create the initial timeline item for invoice creation
  const creationItem: TimelineItem = {
    id: 'creation',
    title: 'Faktur Dibuat',
    timestamp: invoice.created_at,
    createdBy: invoice.created_by,
    changes: [],
  };

  // Combine with history items if available
  const timelineItems: TimelineItem[] = [
    creationItem,
    ...(history || []).map((item) => ({
      id: item.id,
      title: 'Faktur Diperbarui',
      timestamp: item.created_at,
      createdBy: item.created_by,
      changes: item.parsed_changes,
    })),
  ].sort(
    (a, b) => new Date(a.timestamp).getTime() - new Date(b.timestamp).getTime()
  );

  return (
    <div className="mt-8 pt-6 border-t">
      <h3 className="text-lg font-semibold text-gray-500 mb-4">
        Riwayat Perubahan
      </h3>

      {error && (
        <div className="mb-4 p-3 bg-red-50 text-red-700 rounded-md">
          <p>Gagal memuat riwayat perubahan: {error}</p>
          <Button
            variant="outline"
            size="sm"
            className="mt-2"
            onClick={() => window.location.reload()}
          >
            Coba Lagi
          </Button>
        </div>
      )}

      <div className="relative">
        {/* Timeline line */}
        <div className="absolute left-4 top-0 bottom-0 w-0.5 bg-gray-200"></div>

        {/* Timeline items */}
        <div className="space-y-6">
          {timelineItems.map((item, index) => (
            <div key={item.id} className="relative pl-10">
              {/* Timeline dot */}
              <div className="absolute left-0 top-1.5 w-8 h-8 rounded-full bg-blue-600 flex items-center justify-center text-white">
                {index + 1}
              </div>

              {/* Content */}
              <div className="bg-white p-4 rounded-lg border shadow-sm">
                <div className="flex justify-between items-start mb-2">
                  <h4 className="font-semibold text-blue-600">{item.title}</h4>
                  <div className="text-right">
                    <span className="text-sm text-gray-500 block">
                      {formatDate(item.timestamp)}
                    </span>
                  </div>
                </div>

                {item.id === 'creation' ? (
                  <p className="text-gray-600">
                    Faktur nomor {invoice.invoice_number} dibuat
                  </p>
                ) : (
                  <div className="space-y-2">
                    {item.changes.map((change, i) => (
                      <TooltipProvider key={i}>
                        <Tooltip>
                          <TooltipTrigger asChild>
                            <div
                              className={`text-sm ${getChangeColor(change)} cursor-help`}
                            >
                              {formatChangeDescription(change)}
                            </div>
                          </TooltipTrigger>
                          <TooltipContent>
                            <p>Detail perubahan:</p>
                            <pre className="text-xs mt-1 max-w-xs overflow-auto">
                              {JSON.stringify(change, null, 2)}
                            </pre>
                          </TooltipContent>
                        </Tooltip>
                      </TooltipProvider>
                    ))}
                  </div>
                )}
              </div>
            </div>
          ))}

          {/* Loading state */}
          {loading && (
            <div className="relative pl-10">
              <div className="absolute left-0 top-1.5 w-8 h-8 rounded-full bg-gray-200 animate-pulse"></div>
              <div className="bg-white p-4 rounded-lg border shadow-sm">
                <div className="h-5 bg-gray-200 rounded animate-pulse mb-2 w-1/3"></div>
                <div className="h-4 bg-gray-200 rounded animate-pulse w-1/2"></div>
              </div>
            </div>
          )}
        </div>
      </div>

      {/* Empty state (only creation, no history) */}
      {!loading && history && history.length === 0 && (
        <div className="text-center text-gray-500 mt-4">
          Belum ada perubahan pada data faktur ini.
        </div>
      )}
    </div>
  );
}
