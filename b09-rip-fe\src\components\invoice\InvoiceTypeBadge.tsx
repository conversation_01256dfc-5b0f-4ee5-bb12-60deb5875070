import React from 'react';
import { Badge } from '@/components/ui/badge';
import { InvoiceType, ServiceType } from '@/types/invoice';

interface InvoiceTypeBadgeProps {
  type: InvoiceType;
  className?: string;
}

interface ServiceTypeBadgeProps {
  type: ServiceType;
  className?: string;
}

export function InvoiceTypeBadge({ type, className }: InvoiceTypeBadgeProps) {
  // Get appropriate variant for the type
  const getVariant = (type: InvoiceType) => {
    switch (type) {
      case 'internal':
        return 'secondary'; // Using secondary for purple/internal
      case 'external':
        return 'info'; // Using info for indigo/external
      default:
        return 'secondary';
    }
  };

  // Format type text for display
  const formatType = (type: InvoiceType) => {
    return type.charAt(0).toUpperCase() + type.slice(1);
  };

  return (
    <Badge variant={getVariant(type)} className={className}>
      {formatType(type)}
    </Badge>
  );
}

export function ServiceTypeBadge({ type, className }: ServiceTypeBadgeProps) {
  // Get appropriate variant for service type
  const getVariant = (type: ServiceType) => {
    switch (type) {
      case 'HCM':
        return 'success'; // Using success for emerald
      case 'ORDEV':
        return 'info'; // Using info for blue
      case 'BE':
        return 'warning'; // Using warning for amber
      case 'IT':
        return 'info'; // Using info for cyan
      case 'MARKETING':
        return 'danger'; // Using danger for rose
      case 'FINANCE':
        return 'success'; // Using success for green
      case 'SALES':
        return 'warning'; // Using warning for orange
      case 'OTHER':
      default:
        return 'secondary';
    }
  };

  return (
    <Badge variant={getVariant(type)} className={className}>
      {type}
    </Badge>
  );
}
