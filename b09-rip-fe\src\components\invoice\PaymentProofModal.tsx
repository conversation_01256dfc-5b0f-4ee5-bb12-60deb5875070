'use client';

import React, { useState, useRef } from 'react';
import { toast } from 'sonner';
import { useInvoiceDetailStore } from '@/lib/store/invoice-detail-store';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogFooter,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Loader2, Upload, Trash2, FileText, Eye } from 'lucide-react';
import { PaymentProof } from '@/types/invoice';
import { format } from 'date-fns';

export function PaymentProofModal() {
  const {
    invoice,
    paymentProofs,
    isLoadingProofs,
    isUploadingProof,
    isDeletingProof,
    proofError,
    showPaymentProofModal,
    fetchPaymentProofs,
    uploadPaymentProof,
    deletePaymentProof,
    setShowPaymentProofModal,
  } = useInvoiceDetailStore();

  const [file, setFile] = useState<File | null>(null);
  const [notes, setNotes] = useState('');
  const fileInputRef = useRef<HTMLInputElement>(null);

  // Load payment proofs when modal opens
  React.useEffect(() => {
    if (showPaymentProofModal && invoice) {
      fetchPaymentProofs(invoice.id);
    }
  }, [showPaymentProofModal, invoice, fetchPaymentProofs]);

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files.length > 0) {
      setFile(e.target.files[0]);
    }
  };

  const handleUpload = async () => {
    if (!file || !invoice) return;

    const success = await uploadPaymentProof(invoice.id, file, notes);
    if (success) {
      toast.success('Bukti pembayaran berhasil diunggah');
      setFile(null);
      setNotes('');
      if (fileInputRef.current) {
        fileInputRef.current.value = '';
      }
    } else {
      toast.error('Gagal mengunggah bukti pembayaran');
    }
  };

  const handleDelete = async (proofId: string) => {
    if (!invoice) return;

    const success = await deletePaymentProof(invoice.id, proofId);
    if (success) {
      toast.success('Bukti pembayaran berhasil dihapus');
    } else {
      toast.error('Gagal menghapus bukti pembayaran');
    }
  };

  // Handle opening/viewing files
  const handleOpenFile = (proof: PaymentProof) => {
    if (!proof.download_url) {
      toast.error('URL unduhan tidak tersedia');
      return;
    }

    // Open the file in a new tab (works for images, PDFs, and most viewable files)
    window.open(proof.download_url, '_blank');

    // Alternative approach for different file types:
    // if (fileType === 'image' || fileType === 'pdf') {
    //   // Open viewable files in a new tab
    //   window.open(proof.download_url, '_blank');
    // } else {
    //   // For other files, trigger a download
    //   const link = document.createElement('a');
    //   link.href = proof.download_url;
    //   link.download = proof.file_name;
    //   document.body.appendChild(link);
    //   link.click();
    //   document.body.removeChild(link);
    // }
  };

  // Format date for display
  const formatDate = (dateStr: string) => {
    try {
      return format(new Date(dateStr), 'dd MMM yyyy, HH:mm');
    } catch {
      return dateStr;
    }
  };

  return (
    <Dialog
      open={showPaymentProofModal}
      onOpenChange={setShowPaymentProofModal}
    >
      <DialogContent className="sm:max-w-[500px]">
        <DialogHeader>
          <DialogTitle>Bukti Pembayaran</DialogTitle>
          <DialogDescription>
            Unggah dan kelola bukti pembayaran untuk faktur ini.
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-6">
          {/* Upload Form */}
          <div className="space-y-4">
            <h3 className="text-sm font-medium">
              Unggah Bukti Pembayaran Baru
            </h3>

            <div className="space-y-2">
              <Label htmlFor="proof-file">File</Label>
              <Input
                id="proof-file"
                type="file"
                ref={fileInputRef}
                onChange={handleFileChange}
                disabled={
                  isUploadingProof || invoice?.payment_status === 'paid'
                }
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="proof-notes">Catatan</Label>
              <Textarea
                id="proof-notes"
                value={notes}
                onChange={(e) => setNotes(e.target.value)}
                placeholder="Tambahkan catatan tentang bukti pembayaran ini"
                disabled={
                  isUploadingProof || invoice?.payment_status === 'paid'
                }
              />
            </div>

            <Button
              onClick={handleUpload}
              disabled={
                !file || isUploadingProof || invoice?.payment_status === 'paid'
              }
              className="w-full"
            >
              {isUploadingProof ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Mengunggah...
                </>
              ) : (
                <>
                  <Upload className="mr-2 h-4 w-4" />
                  Unggah Bukti Pembayaran
                </>
              )}
            </Button>
          </div>

          {/* Existing Proofs */}
          <div className="space-y-4">
            <h3 className="text-sm font-medium">Bukti Pembayaran yang Ada</h3>

            {isLoadingProofs ? (
              <div className="flex justify-center py-4">
                <Loader2 className="h-6 w-6 animate-spin text-muted-foreground" />
              </div>
            ) : paymentProofs.length === 0 ? (
              <p className="text-sm text-muted-foreground py-2">
                Belum ada bukti pembayaran yang diunggah.
              </p>
            ) : (
              <div className="space-y-2">
                {paymentProofs.map((proof) => (
                  <div
                    key={proof.id}
                    className="flex items-center justify-between p-3 border rounded-md"
                  >
                    <div className="flex items-center space-x-3">
                      <FileText className="h-5 w-5 text-blue-500" />
                      <div>
                        <p
                          className="text-sm font-medium text-blue-600 hover:underline cursor-pointer"
                          onClick={() => handleOpenFile(proof)}
                        >
                          {proof.file_name}
                        </p>
                        <p className="text-xs text-muted-foreground">
                          {formatDate(proof.created_at)}
                        </p>
                        {proof.notes && (
                          <p className="text-xs mt-1">{proof.notes}</p>
                        )}
                      </div>
                    </div>
                    <div className="flex items-center gap-2">
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => handleOpenFile(proof)}
                        className="flex items-center gap-1"
                      >
                        <Eye className="h-4 w-4" />
                        <span className="hidden sm:inline">Lihat</span>
                      </Button>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => handleDelete(proof.id)}
                        disabled={
                          isDeletingProof || invoice?.payment_status === 'paid'
                        }
                      >
                        <Trash2 className="h-4 w-4 text-red-500" />
                      </Button>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </div>

          {proofError && <p className="text-sm text-red-500">{proofError}</p>}
        </div>

        <DialogFooter>
          <Button
            variant="outline"
            onClick={() => setShowPaymentProofModal(false)}
          >
            Tutup
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
