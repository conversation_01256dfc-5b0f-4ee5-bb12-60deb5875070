'use client';

import React from 'react';

export function PrintStyles() {
  return (
    <style jsx global>{`
      @media print {
        /* Base styling */
        body {
          background: white !important;
          font-size: 12pt !important;
          color: black !important;
          margin: 0 !important;
          padding: 0 !important;
        }

        /* Hide all navigation elements */
        nav,
        header,
        .sidebar,
        .navbar,
        footer,
        .print\\:hidden,
        button,
        [role='dialog'],
        .Toaster,
        .BackButton {
          display: none !important;
        }

        /* Hide specific app layout elements */
        div[class*='sidebar'],
        div[class*='navbar'],
        div[class*='footer'],
        div[class*='BackButton'],
        div[class*='tabs'],
        [role='tablist'],
        [role='tab'],
        div[class*='badge'],
        div[class*='pill'],
        div[data-state='active'],
        div[data-state='inactive'] {
          display: none !important;
        }

        /* Hide any tabs and badges */
        .tabs,
        .tab,
        .badge,
        .status-badge,
        .type-badge,
        .pill {
          display: none !important;
        }

        /* Show print-only elements */
        .print\\:block {
          display: block !important;
        }

        /* Container formatting */
        #invoice-container {
          width: 100% !important;
          max-width: 100% !important;
          margin: 0 !important;
          padding: 20px !important;
          box-shadow: none !important;
          border: none !important;
        }

        /* Remove borders and shadows */
        .print\\:border-none,
        .border,
        [class*='shadow'] {
          border: none !important;
          box-shadow: none !important;
        }

        /* Layout optimizations */
        .container {
          width: 100% !important;
          max-width: 100% !important;
          padding: 0 !important;
          margin: 0 !important;
        }

        main {
          padding: 0 !important;
          margin: 0 !important;
        }

        /* Image handling for print */
        img {
          max-width: 100% !important;
          height: auto !important;
          object-fit: contain !important;
        }

        img[src$='.svg'] {
          width: auto !important;
          height: auto !important;
          max-height: 80px !important;
        }

        /* Logo container specific styling */
        div[class*='relative'] img {
          position: relative !important;
          object-position: left center !important;
        }

        /* Better table printing */
        table {
          border-collapse: collapse;
          width: 100%;
        }

        th,
        td {
          padding: 8px;
          border-bottom: 1px solid #eee;
        }

        /* Ensure proper color printing */
        .bg-gray-50 {
          background-color: #f9fafb !important;
          -webkit-print-color-adjust: exact !important;
          print-color-adjust: exact !important;
        }

        /* Font and text improvements */
        h1,
        h2,
        h3,
        h4,
        h5,
        h6 {
          color: black !important;
          margin-bottom: 0.5rem !important;
        }

        p {
          margin-bottom: 0.5rem !important;
          color: black !important;
        }

        /* Spacing and alignment */
        .grid {
          display: grid !important;
          grid-template-columns: 1fr 1fr !important;
          gap: 2rem !important;
        }

        .flex {
          display: flex !important;
        }

        /* Override Tailwind display properties */
        .block {
          display: block !important;
        }
        .inline-block {
          display: inline-block !important;
        }
        .inline {
          display: inline !important;
        }

        /* Status colors */
        .text-green-600 {
          color: #059669 !important;
        }
        .text-amber-600 {
          color: #d97706 !important;
        }
        .text-blue-600 {
          color: #2563eb !important;
        }
        .text-red-600 {
          color: #dc2626 !important;
        }
        .text-gray-600 {
          color: #4b5563 !important;
        }
      }
    `}</style>
  );
}
