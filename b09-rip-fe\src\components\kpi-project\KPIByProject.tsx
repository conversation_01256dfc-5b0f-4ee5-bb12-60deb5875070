'use client';

import React, { useState, useMemo } from 'react';
import useProjectK<PERSON> from '@/hooks/useProjectKPI';
import KpiProjectTable from '@/components/kpi-project/KpiProjectTable';
import { Button } from '@/components/ui/button';
import { Plus, Target } from 'lucide-react';
import { useRouter } from 'next/navigation';
import { KpiProjectSearchFilter } from '@/components/kpi-project/KpiProjectSearchFilter';
import { PageTitle } from '@/components/ui/PageTitle';
import { BackButton } from '@/components/ui/BackButton';
import { SortDirection } from '@/components/ui/data-table';
import { Card, CardContent } from '@/components/ui/card';

interface KPIByProjectProps {
  projectId: string;
}

const KPIByProject: React.FC<KPIByProjectProps> = ({ projectId }) => {
  const router = useRouter();
  const [sortField, setSortField] = useState<string | undefined>(undefined);
  const [sortDirection, setSortDirection] = useState<SortDirection>(null);

  const {
    kpiProjects,
    loading,
    search,
    status,
    projectInfo,
    // currentPage, // Not used after DataTable changes
    handleViewDetail,
    handleSearchChange,
    handleStatusChange,
    // handlePageChange, // Not used after DataTable changes
    handleBackToAll,
  } = useProjectKPI(projectId);

  const handleSort = (field: string, direction: SortDirection) => {
    setSortField(field);
    setSortDirection(direction);
  };

  const handleAddKpiProject = () => {
    router.push('/kpi-project/add');
  };

  // Calculate the number of projects that achieved targets
  const achievedTargetsStats = useMemo(() => {
    if (!kpiProjects || kpiProjects.length === 0) {
      return {
        onTarget: 0,
        aboveTarget: 0,
        total: 0,
        percentage: 0,
      };
    }

    const onTarget = kpiProjects.filter(
      (project) => project.status === 'completed_on_target'
    ).length;
    const aboveTarget = kpiProjects.filter(
      (project) => project.status === 'completed_above_target'
    ).length;
    const total = onTarget + aboveTarget;
    const percentage = Math.round((total / kpiProjects.length) * 100);

    return {
      onTarget,
      aboveTarget,
      total,
      percentage,
    };
  }, [kpiProjects]);

  const statusOptions = [
    { value: 'not_started', label: 'Belum Dimulai' },
    { value: 'in_progress', label: 'Dalam Proses' },
    { value: 'completed_below_target', label: 'Selesai Di Bawah Target' },
    { value: 'completed_on_target', label: 'Selesai Sesuai Target' },
    { value: 'completed_above_target', label: 'Selesai Di Atas Target' },
  ];

  return (
    <div className="container mx-auto py-6 px-6">
      <div className="flex justify-between items-center mb-6">
        <div className="flex items-center gap-6">
          <BackButton onClick={handleBackToAll} />
          <PageTitle
            title={
              loading
                ? 'Memuat...'
                : `KPI Proyek: ${projectInfo?.project_name || ''}`
            }
            subtitle="Kelola dan pantau KPI proyek"
          />
        </div>

        <Button
          onClick={handleAddKpiProject}
          className="flex items-center gap-2"
        >
          <Plus className="h-4 w-4" />
          Tambah KPI Proyek
        </Button>
      </div>

      {/* Stats Card */}
      <div className="mb-6">
        <Card className="bg-white">
          <CardContent className="p-6">
            <div className="flex flex-col md:flex-row gap-4 items-center">
              <div className="flex items-center gap-3 bg-green-50 p-4 rounded-lg">
                <Target className="h-8 w-8 text-green-600" />
                <div>
                  <h3 className="font-semibold text-lg text-green-700">
                    Pencapaian Target
                  </h3>
                  <p className="text-sm text-green-600">
                    {loading ? (
                      <span className="h-4 w-20 bg-gray-200 animate-pulse rounded"></span>
                    ) : (
                      `${achievedTargetsStats.total} dari ${kpiProjects.length} proyek (${achievedTargetsStats.percentage}%)`
                    )}
                  </p>
                </div>
              </div>

              <div className="flex flex-col md:flex-row gap-4">
                <div className="flex items-center gap-2">
                  <div className="h-3 w-3 rounded-full bg-yellow-400"></div>
                  <span className="text-sm">
                    Sesuai Target: {achievedTargetsStats.onTarget} proyek
                  </span>
                </div>
                <div className="flex items-center gap-2">
                  <div className="h-3 w-3 rounded-full bg-green-500"></div>
                  <span className="text-sm">
                    Di Atas Target: {achievedTargetsStats.aboveTarget} proyek
                  </span>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      <div className="bg-white rounded-lg shadow p-6">
        <KpiProjectSearchFilter
          search={search}
          status={status}
          statusOptions={statusOptions}
          onSearchChange={handleSearchChange}
          onStatusChange={handleStatusChange}
        />

        <div className="overflow-x-auto mt-4">
          <KpiProjectTable
            kpiProjects={kpiProjects}
            // Pagination props removed as they're not used in KpiProjectTable anymore
            // currentPage={currentPage}
            // itemsPerPage={10}
            // onPageChange={handlePageChange}
            onViewDetail={handleViewDetail}
            loading={loading}
            sortField={sortField}
            sortDirection={sortDirection}
            onSort={handleSort}
          />
        </div>
      </div>
    </div>
  );
};

export default KPIByProject;
