import React, { useState, useMemo } from 'react';
import { useRouter } from 'next/navigation';
import { ChevronLeft, ChevronRight, Target } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { SortDirection } from '@/components/ui/data-table';
import { useViewingKpiProjects } from '@/hooks/useViewingKpiProjects';
import { KpiProjectSearchFilter } from './KpiProjectSearchFilter';
import { PageTitle } from '@/components/ui/PageTitle';
import { BackButton } from '@/components/ui/BackButton';
import KpiProjectTable from './KpiProjectTable';
import { Card, CardContent } from '@/components/ui/card';

interface KpiPerProjectProps {
  projectId?: string;
}

const KpiPerProject: React.FC<KpiPerProjectProps> = ({ projectId }) => {
  const router = useRouter();
  const {
    kpiProjects,
    loading,
    totalPages,
    currentPage,
    pageSize,
    search,
    status,
    handleViewDetail,
    handleSearchChange,
    handleStatusChange,
    setCurrentPage,
    totalItems,
  } = useViewingKpiProjects(projectId);

  const [sortField, setSortField] = useState<string | undefined>(undefined);
  const [sortDirection, setSortDirection] = useState<SortDirection>(null);

  const handleSort = (field: string, direction: SortDirection) => {
    setSortField(field);
    setSortDirection(direction);
  };

  // Calculate the number of projects that achieved targets
  const achievedTargetsStats = useMemo(() => {
    if (!kpiProjects || kpiProjects.length === 0) {
      return {
        onTarget: 0,
        aboveTarget: 0,
        total: 0,
        percentage: 0,
      };
    }

    const onTarget = kpiProjects.filter(
      (project) => project.status === 'completed_on_target'
    ).length;
    const aboveTarget = kpiProjects.filter(
      (project) => project.status === 'completed_above_target'
    ).length;
    const total = onTarget + aboveTarget;
    const percentage = Math.round((total / kpiProjects.length) * 100);

    return {
      onTarget,
      aboveTarget,
      total,
      percentage,
    };
  }, [kpiProjects]);

  const statusOptions = [
    { value: 'not_started', label: 'Belum Dimulai' },
    { value: 'in_progress', label: 'Dalam Proses' },
    { value: 'completed_below_target', label: 'Selesai Di Bawah Target' },
    { value: 'completed_on_target', label: 'Selesai Sesuai Target' },
    { value: 'completed_above_target', label: 'Selesai Di Atas Target' },
  ];

  return (
    <div className="container mx-auto py-6 px-6">
      <div className="mb-6 flex items-center gap-4">
        {projectId && (
          <BackButton onClick={() => router.push(`/project/${projectId}`)} />
        )}
        <PageTitle
          title="KPI Per Proyek"
          subtitle="Lihat KPI untuk setiap proyek"
        />
      </div>

      {/* Stats Card */}
      <div className="mb-6">
        <Card className="bg-white">
          <CardContent className="p-6">
            <div className="flex flex-col md:flex-row gap-4 items-center">
              <div className="flex items-center gap-3 bg-green-50 p-4 rounded-lg">
                <Target className="h-8 w-8 text-green-600" />
                <div>
                  <h3 className="font-semibold text-lg text-green-700">
                    Pencapaian Target
                  </h3>
                  <p className="text-sm text-green-600">
                    {loading ? (
                      <span className="h-4 w-20 bg-gray-200 animate-pulse rounded"></span>
                    ) : (
                      `${achievedTargetsStats.total} dari ${kpiProjects.length} proyek (${achievedTargetsStats.percentage}%)`
                    )}
                  </p>
                </div>
              </div>

              <div className="flex flex-col md:flex-row gap-4">
                <div className="flex items-center gap-2">
                  <div className="h-3 w-3 rounded-full bg-yellow-400"></div>
                  <span className="text-sm">
                    Sesuai Target: {achievedTargetsStats.onTarget} proyek
                  </span>
                </div>
                <div className="flex items-center gap-2">
                  <div className="h-3 w-3 rounded-full bg-green-500"></div>
                  <span className="text-sm">
                    Di Atas Target: {achievedTargetsStats.aboveTarget} proyek
                  </span>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      <div className="bg-white rounded-lg shadow p-6">
        <KpiProjectSearchFilter
          search={search}
          status={status}
          statusOptions={statusOptions}
          onSearchChange={handleSearchChange}
          onStatusChange={handleStatusChange}
        />
        <div className="overflow-x-auto mt-4">
          <KpiProjectTable
            kpiProjects={kpiProjects}
            onViewDetail={handleViewDetail}
            loading={loading}
            sortField={sortField}
            sortDirection={sortDirection}
            onSort={handleSort}
          />
        </div>

        {totalPages > 1 && (
          <div className="mt-4 flex items-center justify-between">
            <div className="text-sm text-gray-500">
              {loading ? (
                <div className="h-5 w-40 bg-gray-200 animate-pulse rounded"></div>
              ) : (
                `Menampilkan ${(currentPage - 1) * pageSize + 1}-${Math.min(currentPage * pageSize, totalItems)} dari ${totalItems} data`
              )}
            </div>
            <div className="flex items-center space-x-2">
              <Button
                variant="outline"
                size="icon"
                className="w-8 h-8 p-0"
                onClick={() => setCurrentPage(currentPage - 1)}
                disabled={currentPage <= 1 || loading}
              >
                <ChevronLeft className="h-4 w-4" />
              </Button>

              {Array.from({ length: Math.min(5, totalPages) }).map(
                (_, index) => {
                  let pageNum;

                  if (totalPages <= 5) {
                    pageNum = index + 1;
                  } else if (currentPage <= 3) {
                    pageNum = index + 1;
                    if (index === 4) pageNum = totalPages;
                  } else if (currentPage >= totalPages - 2) {
                    pageNum = totalPages - 4 + index;
                    if (index === 0) pageNum = 1;
                  } else {
                    pageNum = currentPage - 2 + index;
                    if (index === 0) pageNum = 1;
                    if (index === 4) pageNum = totalPages;
                  }

                  return (
                    <React.Fragment key={pageNum}>
                      {index === 1 && pageNum > 2 && <span>...</span>}
                      <Button
                        variant="outline"
                        size="icon"
                        className={`w-8 h-8 p-0 ${currentPage === pageNum ? 'bg-[#C3A64C]/60' : ''}`}
                        onClick={() => setCurrentPage(pageNum)}
                        disabled={loading}
                      >
                        {pageNum}
                      </Button>
                      {index === 3 && pageNum < totalPages - 1 && (
                        <span>...</span>
                      )}
                    </React.Fragment>
                  );
                }
              )}

              <Button
                variant="outline"
                size="icon"
                className="w-8 h-8 p-0"
                onClick={() => setCurrentPage(currentPage + 1)}
                disabled={currentPage >= totalPages || loading}
              >
                <ChevronRight className="h-4 w-4" />
              </Button>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default KpiPerProject;
