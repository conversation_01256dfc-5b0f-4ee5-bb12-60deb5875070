// src\components\kpi-project\KpiPerProjectDetail.tsx
'use client';

import React from 'react';
import { useRouter } from 'next/navigation';
import { AlertCircle } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Separator } from '@/components/ui/separator';
import { formatDate } from '@/lib/utils/date';
import { KpiProjectStatusBadge } from './KpiProjectStatusBadge';
import { PageTitle } from '@/components/ui/PageTitle';
import { BackButton } from '@/components/ui/BackButton';
import { useKpiPerProjectDetail } from '@/hooks/useKpiPerProjectDetail';

interface KpiPerProjectDetailProps {
  id: string;
  fromProjectView?: boolean;
  projectId?: string | null;
}

const KpiPerProjectDetail: React.FC<KpiPerProjectDetailProps> = ({
  id,
  fromProjectView,
  projectId,
}) => {
  const router = useRouter();
  const { kpiProject, loading } = useKpiPerProjectDetail(id);

  return (
    <div className="max-w-5xl mx-auto">
      <div className="mb-6 flex items-start">
        <div className="flex items-center gap-4">
          <BackButton
            onClick={() => {
              if (fromProjectView && projectId) {
                router.push(`/project/${projectId}`);
              } else {
                router.push('/project');
              }
            }}
          />
          <PageTitle
            title="Detail KPI Project"
            subtitle="Lihat detail KPI Project."
          />
        </div>
      </div>
      {loading ? (
        <div className="bg-white rounded-lg shadow p-6 text-center py-8">
          Loading...
        </div>
      ) : !kpiProject ? (
        <div className="bg-white rounded-lg shadow p-6 text-center py-8">
          <AlertCircle className="h-8 w-8 text-red-500 mx-auto mb-4" />
          <h2 className="text-xl font-medium">KPI Project tidak ditemukan</h2>
          <p className="text-gray-500 mt-2">
            KPI Project dengan ID ini tidak ditemukan atau telah dihapus.
          </p>
          <Button className="mt-4" onClick={() => router.push('/project')}>
            Kembali ke Daftar Proyek
          </Button>
        </div>
      ) : (
        <>
          <Card className="mb-6">
            <CardHeader>
              <div className="flex justify-between items-center">
                <div>
                  <CardTitle className="text-xl">
                    {kpiProject.project_name}
                  </CardTitle>
                </div>
                <div>
                  <KpiProjectStatusBadge status={kpiProject.status} />
                </div>
              </div>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <h3 className="text-sm font-medium text-gray-500">Periode</h3>
                  <p className="mt-1">{kpiProject.period}</p>
                </div>
              </div>

              <Separator />

              <div>
                <h3 className="text-sm font-medium text-gray-500 mb-2">
                  Deskripsi KPI Project
                </h3>
                <p className="whitespace-pre-wrap">{kpiProject.description}</p>
              </div>

              <div>
                <h3 className="text-sm font-medium text-gray-500 mb-2">
                  Target
                </h3>
                <p className="whitespace-pre-wrap">{kpiProject.target}</p>
              </div>

              {kpiProject.additional_notes && (
                <div>
                  <h3 className="text-sm font-medium text-gray-500 mb-2">
                    Catatan Tambahan
                  </h3>
                  <p className="whitespace-pre-wrap">
                    {kpiProject.additional_notes}
                  </p>
                </div>
              )}
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle className="text-lg">Informasi Tambahan</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <h3 className="text-sm font-medium text-gray-500">
                    Dibuat Pada
                  </h3>
                  <p className="mt-1">{formatDate(kpiProject.created_at)}</p>
                </div>
                {kpiProject.updated_at && (
                  <div>
                    <h3 className="text-sm font-medium text-gray-500">
                      Diperbarui Pada
                    </h3>
                    <p className="mt-1">{formatDate(kpiProject.updated_at)}</p>
                  </div>
                )}
              </div>
            </CardContent>
          </Card>
        </>
      )}
    </div>
  );
};

export default KpiPerProjectDetail;
