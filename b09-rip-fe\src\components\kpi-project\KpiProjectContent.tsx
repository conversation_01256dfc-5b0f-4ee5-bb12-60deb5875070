import React, { useState } from 'react';
import { Plus } from 'lucide-react';
import { useRouter } from 'next/navigation';
import { Button } from '@/components/ui/button';
import { useKpiProjectManagement } from '@/hooks/useKpiProjectManagement';
import KpiProjectTable from '@/components/kpi-project/KpiProjectTable';
import { KpiProjectSearchFilter } from '@/components/kpi-project/KpiProjectSearchFilter';
import { PageTitle } from '@/components/ui/PageTitle';
import { SortDirection } from '@/components/ui/data-table';
import { useRBAC } from '@/hooks/useRBAC';

const KpiProjectManagementContent: React.FC = () => {
  const router = useRouter();
  const { hasRole } = useRBAC();
  const {
    kpiProjects,
    loading,
    // totalPages and totalItems not needed after pagination removal
    // currentPage, // Not used after DataTable changes
    // pageSize, // Not used after DataTable changes
    search,
    status,
    handleViewDetail,
    handleSearchChange,
    handleStatusChange,
    // setCurrentPage, // Not used after DataTable changes
  } = useKpiProjectManagement();

  const [sortField, setSortField] = useState<string | undefined>(undefined);
  const [sortDirection, setSortDirection] = useState<SortDirection>(null);

  const handleSort = (field: string, direction: SortDirection) => {
    setSortField(field);
    setSortDirection(direction);
  };

  const handleAddKpiProject = () => {
    router.push('/kpi-project/add');
  };

  const statusOptions = [
    { value: 'not_started', label: 'Belum Dimulai' },
    { value: 'in_progress', label: 'Dalam Proses' },
    { value: 'completed_below_target', label: 'Selesai Di Bawah Target' },
    { value: 'completed_on_target', label: 'Selesai Sesuai Target' },
    { value: 'completed_above_target', label: 'Selesai Di Atas Target' },
  ];

  return (
    <div className="container mx-auto py-6 px-6">
      <div className="flex justify-between items-center mb-6">
        <PageTitle title="Manajemen KPI Proyek" />
        {hasRole(['Operation', 'Manager']) && (
          <Button onClick={handleAddKpiProject} leftIcon={<Plus />}>
            Tambah KPI Proyek
          </Button>
        )}
      </div>

      <div className="bg-white rounded-lg shadow p-6">
        <KpiProjectSearchFilter
          search={search}
          status={status}
          statusOptions={statusOptions}
          onSearchChange={handleSearchChange}
          onStatusChange={handleStatusChange}
        />
        <div className="overflow-x-auto">
          <KpiProjectTable
            kpiProjects={kpiProjects}
            // Pagination props removed as they're not used in KpiProjectTable anymore
            // currentPage={currentPage}
            // itemsPerPage={pageSize}
            // onPageChange={setCurrentPage}
            onViewDetail={handleViewDetail}
            loading={loading}
            sortField={sortField}
            sortDirection={sortDirection}
            onSort={handleSort}
          />
        </div>

        {/* Pagination has been removed */}
      </div>
    </div>
  );
};

export default KpiProjectManagementContent;
