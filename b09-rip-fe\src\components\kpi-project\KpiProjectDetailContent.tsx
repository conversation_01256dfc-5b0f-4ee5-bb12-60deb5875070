'use client';

import React, { useState } from 'react';
import { useRouter } from 'next/navigation';
import { Edit, Trash2, AlertCircle } from 'lucide-react';
import { Button } from '@/components/ui/button';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogDescription,
  DialogFooter,
} from '@/components/ui/dialog';
import {
  Card,
  CardContent,
  CardFooter,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Separator } from '@/components/ui/separator';
//import { useKpiProjectDetail } from '@/hooks/useKpiProjectDetail'; // Hook untuk detail kpi project
import { formatDate } from '@/lib/utils/date';
import { KpiProjectStatusBadge } from './KpiProjectStatusBadge'; // Badge untuk status KpiProject
import KpiProjectStatusUpdateForm from './KpiProjectStatusUpdateForm'; // Form untuk update status
//import KpiProjectBonusUpdateForm from './KpiProjectBonusUpdateForm'; // Form untuk update bonus
import { PageTitle } from '@/components/ui/PageTitle';
import { BackButton } from '@/components/ui/BackButton';
import { useKpiProjectDetail } from '@/hooks/useKpiProjectDetail';
import { useRBAC } from '@/hooks/useRBAC';

interface KpiProjectDetailContentProps {
  id: string;
}

const KpiProjectDetailContent: React.FC<KpiProjectDetailContentProps> = ({
  id,
}) => {
  const router = useRouter();
  const { hasRole } = useRBAC();
  const {
    kpiProject,
    loading,
    deleteLoading,
    deleteKpiProject,
    updateStatus,
    statusUpdateLoading,
    //bonusUpdateLoading,
  } = useKpiProjectDetail(id);

  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [statusUpdateOpen, setStatusUpdateOpen] = useState(false);
  // const [bonusUpdateOpen, setBonusUpdateOpen] = useState(false);

  const handleEditClick = () => {
    router.push(`/kpi-project/${id}/edit`);
  };

  const handleDeleteClick = () => {
    setDeleteDialogOpen(true);
  };

  const handleDeleteConfirm = async () => {
    const success = await deleteKpiProject();
    if (success) {
      setDeleteDialogOpen(false);
    }
  };

  return (
    <div className="max-w-5xl mx-auto">
      <div className="mb-6 flex justify-between items-start">
        <div className="flex items-center gap-4">
          <BackButton onClick={() => router.push('/kpi-project')} />
          <PageTitle
            title="Detail KPI Project"
            subtitle="Lihat dan kelola detail KPI Project."
          />
        </div>
        {hasRole(['Operation', 'Manager']) && (
          <div className="flex space-x-2">
            <Button
              variant="outline"
              size="sm"
              onClick={handleEditClick}
              disabled={loading}
              leftIcon={<Edit />}
            >
              Edit
            </Button>
            <Button
              variant="destructive"
              size="sm"
              onClick={handleDeleteClick}
              disabled={loading || deleteLoading}
              leftIcon={<Trash2 />}
            >
              Hapus
            </Button>
          </div>
        )}
      </div>

      {loading ? (
        <div className="bg-white rounded-lg shadow p-6 text-center py-8">
          Loading...
        </div>
      ) : !kpiProject ? (
        <div className="bg-white rounded-lg shadow p-6 text-center py-8">
          <AlertCircle className="h-8 w-8 text-red-500 mx-auto mb-4" />
          <h2 className="text-xl font-medium">KPI Project tidak ditemukan</h2>
          <p className="text-gray-500 mt-2">
            KPI Project dengan ID ini tidak ditemukan atau telah dihapus.
          </p>
          <Button className="mt-4" onClick={() => router.push('/kpi-project')}>
            Kembali ke Daftar KPI Project
          </Button>
        </div>
      ) : (
        <>
          <Card className="mb-6">
            <CardHeader>
              <div className="flex justify-between items-center">
                <div>
                  <CardTitle className="text-xl">
                    {kpiProject.project_name}
                  </CardTitle>
                </div>
                <div>
                  <KpiProjectStatusBadge status={kpiProject.status} />
                </div>
              </div>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <h3 className="text-sm font-medium text-gray-500">Periode</h3>
                  <p className="mt-1">{kpiProject.period}</p>
                </div>
                {/* <div>
                  <h3 className="text-sm font-medium text-gray-500">Bonus</h3>
                  <p className="mt-1">
                    {kpiProject.bonus_received
                      ? formatCurrency(kpiProject.bonus_received)
                      : '-'}
                  </p>
                </div> */}
              </div>

              <Separator />

              <div>
                <h3 className="text-sm font-medium text-gray-500 mb-2">
                  Deskripsi KPI Project
                </h3>
                <p className="whitespace-pre-wrap">{kpiProject.description}</p>
              </div>

              <div>
                <h3 className="text-sm font-medium text-gray-500 mb-2">
                  Target
                </h3>
                <p className="whitespace-pre-wrap">{kpiProject.target}</p>
              </div>

              {kpiProject.additional_notes && (
                <div>
                  <h3 className="text-sm font-medium text-gray-500 mb-2">
                    Catatan Tambahan
                  </h3>
                  <p className="whitespace-pre-wrap">
                    {kpiProject.additional_notes}
                  </p>
                </div>
              )}
            </CardContent>
            <CardFooter>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4 w-full">
                {hasRole(['Operation', 'Manager']) && (
                  <Button
                    onClick={() => setStatusUpdateOpen(true)}
                    className="w-full"
                    leftIcon={<Edit />}
                  >
                    Update Status
                  </Button>
                )}
              </div>
            </CardFooter>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle className="text-lg">Informasi Tambahan</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <h3 className="text-sm font-medium text-gray-500">
                    Dibuat Pada
                  </h3>
                  <p className="mt-1">{formatDate(kpiProject.created_at)}</p>
                </div>
                {kpiProject.updated_at && (
                  <div>
                    <h3 className="text-sm font-medium text-gray-500">
                      Diperbarui Pada
                    </h3>
                    <p className="mt-1">{formatDate(kpiProject.updated_at)}</p>
                  </div>
                )}
              </div>
            </CardContent>
          </Card>

          {/* Delete Confirmation Dialog */}
          <Dialog open={deleteDialogOpen} onOpenChange={setDeleteDialogOpen}>
            <DialogContent>
              <DialogHeader>
                <DialogTitle>Hapus KPI Project</DialogTitle>
                <DialogDescription>
                  Apakah Anda yakin ingin menghapus KPI Project ini? Tindakan
                  ini tidak dapat dibatalkan.
                </DialogDescription>
              </DialogHeader>
              <DialogFooter>
                <Button
                  variant="cancel"
                  onClick={() => setDeleteDialogOpen(false)}
                  disabled={deleteLoading}
                >
                  Batal
                </Button>
                <Button
                  variant="destructive"
                  onClick={handleDeleteConfirm}
                  disabled={deleteLoading}
                >
                  {deleteLoading ? 'Menghapus...' : 'Hapus'}
                </Button>
              </DialogFooter>
            </DialogContent>
          </Dialog>

          {/* Status Update Dialog */}
          {kpiProject && (
            <KpiProjectStatusUpdateForm
              kpiProject={kpiProject}
              open={statusUpdateOpen}
              onOpenChange={setStatusUpdateOpen}
              updateStatus={updateStatus}
              statusUpdateLoading={statusUpdateLoading}
            />
          )}
        </>
      )}
    </div>
  );
};

export default KpiProjectDetailContent;
