// KpiProjectForm Component - Corrected

import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { useForm } from 'react-hook-form';
import { toast } from 'sonner';
import { Loader2, Check } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Label } from '@/components/ui/label';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogDescription,
  DialogFooter,
} from '@/components/ui/dialog';
import {
  KpiProject,
  CreateKpiProjectRequest,
  UpdateKpiProjectRequest,
  KpiProjectStatus,
} from '@/types/kpi-project';
import { kpiProjectApi } from '@/lib/api/kpi-project';
import { cn } from '@/lib/utils';
import { PageTitle } from '@/components/ui/PageTitle';
import { BackButton } from '@/components/ui/BackButton';
import { ProjectCombobox } from '../project/ProjectCombobox';

interface KpiProjectFormProps {
  initialData?: KpiProject;
  isEdit?: boolean;
}

const KpiProjectForm: React.FC<KpiProjectFormProps> = ({
  initialData,
  isEdit = false,
}) => {
  const router = useRouter();
  const [loading, setLoading] = useState(false);
  const [confirmDialogOpen, setConfirmDialogOpen] = useState(false);
  const [selectedProject, setSelectedProject] = useState<string>(''); // Store selected project name
  const [selectedPeriod, setSelectedPeriod] = useState<string>(''); // Store selected period
  const [selectedStatus, setSelectedStatus] =
    useState<KpiProjectStatus>('not_started'); // Store selected status

  // Fetch all projects (names) to populate the project dropdown
  useEffect(() => {
    const fetchProjects = async () => {
      try {
        const response = await kpiProjectApi.getKpiProjects({
          page: 1,
          pageSize: 100,
        });
        if (
          response.success &&
          response.data &&
          Array.isArray(response.data.data)
        ) {
          setSelectedProject(response.data.data[0]?.project_name || ''); // Default to the first project
        } else {
          toast.error('Gagal memuat daftar proyek');
        }
      } catch (error: unknown) {
        console.error('Error loading projects:', error);
        toast.error('Terjadi kesalahan saat memuat proyek');
      }
    };
    fetchProjects();
  }, []);

  // Status options with display names
  const statusOptions = [
    { value: 'not_started', label: 'Belum Dimulai' },
    { value: 'in_progress', label: 'Dalam Proses' },
    { value: 'completed_below_target', label: 'Selesai Di Bawah Target' },
    { value: 'completed_on_target', label: 'Selesai Sesuai Target' },
    { value: 'completed_above_target', label: 'Selesai Di Atas Target' },
  ];

  const {
    register,
    handleSubmit,
    setValue,
    getValues,
    watch,
    formState: { errors },
    reset,
  } = useForm<CreateKpiProjectRequest | UpdateKpiProjectRequest>({
    defaultValues: {
      project_name: '',
      description: '',
      target: '',
      period: '',
      status: 'not_started',
      additional_notes: '',
    },
  });

  // Register period and status fields
  useEffect(() => {
    register('period', { required: 'Periode diperlukan' });
    register('status', { required: 'Status diperlukan' });
    register('project_name', { required: 'Nama proyek diperlukan' });
  }, [register]);

  // Set initial form values when initialData changes
  useEffect(() => {
    if (initialData) {
      console.log('Setting initial form data:', initialData);
      console.log('Initial status from API:', initialData.status);
      console.log('Current selectedStatus before update:', selectedStatus);

      reset({
        ...initialData,
        period: initialData.period || '',
        status: initialData.status || 'not_started',
      });

      if (initialData.period) {
        setSelectedPeriod(initialData.period);
        setValue('period', initialData.period);
      }

      if (initialData.project_name) {
        setSelectedProject(initialData.project_name);
        setValue('project_name', initialData.project_name);
      }

      if (initialData.project_id) {
        setValue('project_id', initialData.project_id); // Set the project_id in the form
      }
    }
  }, [initialData, reset, setValue, selectedStatus]);

  // Dedicated useEffect for status initialization
  useEffect(() => {
    if (initialData && initialData.status) {
      console.log('Setting status from initialData:', initialData.status);
      setSelectedStatus(initialData.status);
      setValue('status', initialData.status);
      console.log('Updated selectedStatus:', initialData.status);
    }
  }, [initialData, setValue, selectedStatus]);

  // Log when selectedStatus changes
  useEffect(() => {
    console.log('selectedStatus changed to:', selectedStatus);
  }, [selectedStatus]);

  // Handle project selection
  const handleProjectSelect = (projectId: string, projectName: string) => {
    setSelectedProject(projectName); // Store project_name
    setValue('project_name', projectName, { shouldValidate: true });
    setValue('project_id', projectId, { shouldValidate: true }); // Store project_id in form
  };

  // Watch form values for the confirmation dialog
  const watchedDescription = watch('description');
  const watchedTarget = watch('target');
  const watchedAdditionalNotes = watch('additional_notes');

  // Handle form submission to show confirmation dialog
  const onSubmit = (
    data: CreateKpiProjectRequest | UpdateKpiProjectRequest
  ) => {
    console.log('Form submitted with data:', data);

    if (!data.project_id || !data.project_name) {
      console.error('Missing required fields: project_id or project_name');
      toast.error('Silakan pilih proyek terlebih dahulu');
      return;
    }

    // Show confirmation dialog instead of immediately submitting
    setConfirmDialogOpen(true);
  };

  // Handle confirmed submission
  const confirmSubmit = async () => {
    setLoading(true);
    setConfirmDialogOpen(false);

    // Get the current form data using React Hook Form
    const formValues = getValues();
    const data = {
      project_name: selectedProject,
      project_id: formValues.project_id,
      period: selectedPeriod,
      status: selectedStatus,
      description: formValues.description,
      target: formValues.target,
      additional_notes: formValues.additional_notes,
    };

    try {
      let response;

      if (isEdit && initialData) {
        console.log('Updating KPI Project with ID:', initialData.id, data);

        const updateData = {
          ...data,
          project_name: data.project_name,
          project_id: data.project_id, // Ensure project_id is included
          period: data.period,
          status: data.status || 'not_started',
          description: data.description,
          target: data.target,
          additional_notes: data.additional_notes,
        };

        response = await kpiProjectApi.updateKpiProject(
          initialData.id,
          updateData
        );

        if (response && response.success) {
          toast.success('KPI Project berhasil diperbarui');
          router.push(`/kpi-project/${initialData.id}`);
        } else {
          toast.error('Gagal memperbarui KPI Project');
        }
      } else {
        console.log('Creating new KPI Project');

        const createData = {
          ...data,
          project_name: data.project_name,
          project_id: data.project_id, // Ensure project_id is included
          period: data.period,
          status: data.status || 'not_started',
          description: data.description,
          target: data.target,
          additional_notes: data.additional_notes,
        } as CreateKpiProjectRequest;

        response = await kpiProjectApi.createKpiProject(createData);

        if (response && response.success) {
          toast.success('KPI Project berhasil dibuat');
          router.push('/kpi-project');
        } else {
          toast.error('Gagal membuat KPI Project');
        }
      }
    } catch (error: unknown) {
      console.error('Error submitting form:', error);
      toast.error('Terjadi kesalahan. Silakan coba lagi nanti.');
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="max-w-5xl mx-auto">
      <div className="flex items-center gap-4 mb-6">
        <BackButton
          onClick={() => {
            if (isEdit && initialData) {
              router.push(`/kpi-project/${initialData.id}`);
            } else {
              router.push('/kpi-project');
            }
          }}
        />
        <PageTitle
          title={isEdit ? 'Edit KPI Project' : 'Tambah KPI Project Baru'}
          subtitle={
            isEdit
              ? 'Edit informasi KPI Project.'
              : 'Lengkapi formulir untuk menambahkan KPI Project baru.'
          }
        />
      </div>

      <div className="bg-white rounded-lg shadow p-6">
        <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="md:col-span-2">
              <Label htmlFor="project_name">Nama Proyek</Label>
              <ProjectCombobox
                value={selectedProject}
                onSelect={handleProjectSelect}
                placeholder="Pilih proyek..."
                className={cn(
                  'mt-1',
                  isEdit && 'opacity-70 pointer-events-none'
                )}
                disabled={isEdit}
              />
              {errors.project_name && (
                <p className="text-red-500 text-sm mt-1">
                  Proyek harus dipilih
                </p>
              )}
            </div>

            {/* Project ID field hidden as per requirements */}

            <div>
              <Label htmlFor="period">Periode</Label>
              <Input
                id="period"
                {...register('period', { required: 'Periode diperlukan' })}
                value={selectedPeriod}
                onChange={(e) => {
                  setSelectedPeriod(e.target.value);
                  setValue('period', e.target.value, { shouldValidate: true });
                }}
                className="mt-1"
              />
              {errors.period && (
                <p className="text-red-500 text-sm mt-1">
                  {errors.period.message}
                </p>
              )}
            </div>

            <div>
              <Label htmlFor="status">Status</Label>
              {/* Debug log removed to fix build error */}
              <Select
                key={`status-select-${selectedStatus}`}
                value={selectedStatus}
                defaultValue={initialData?.status || 'not_started'}
                onValueChange={(value) => {
                  // Debug log removed
                  setSelectedStatus(value as KpiProjectStatus);
                  setValue('status', value as KpiProjectStatus, {
                    shouldValidate: true,
                  });
                }}
                name="status"
              >
                <SelectTrigger id="status" className="mt-1">
                  <SelectValue placeholder="Pilih status">
                    {statusOptions.find((s) => s.value === selectedStatus)
                      ?.label || 'Pilih status'}
                  </SelectValue>
                </SelectTrigger>
                <SelectContent>
                  {statusOptions.map((status) => (
                    <SelectItem key={status.value} value={status.value}>
                      {status.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              {errors.status && (
                <p className="text-red-500 text-sm mt-1">
                  {errors.status.message}
                </p>
              )}
            </div>

            <div className="md:col-span-2">
              <Label htmlFor="description">Deskripsi KPI Project</Label>
              <Textarea
                id="description"
                {...register('description', {
                  required: 'Deskripsi diperlukan',
                })}
                className="mt-1"
                rows={3}
              />
              {errors.description && (
                <p className="text-red-500 text-sm mt-1">
                  {errors.description.message}
                </p>
              )}
            </div>

            <div className="md:col-span-2">
              <Label htmlFor="target">Target</Label>
              <Textarea
                id="target"
                {...register('target', { required: 'Target diperlukan' })}
                className="mt-1"
                rows={3}
              />
              {errors.target && (
                <p className="text-red-500 text-sm mt-1">
                  {errors.target.message}
                </p>
              )}
            </div>

            <div className="md:col-span-2">
              <Label htmlFor="additional_notes">Catatan Tambahan</Label>
              <Textarea
                id="additional_notes"
                {...register('additional_notes')}
                className="mt-1"
                rows={3}
              />
            </div>
          </div>

          <div className="flex justify-end space-x-4 pt-4">
            <Button
              type="button"
              variant="cancel"
              onClick={() => router.push('/kpi-project')}
              disabled={loading}
            >
              Batal
            </Button>
            <Button
              type="submit"
              disabled={loading}
              leftIcon={
                loading ? <Loader2 className="animate-spin" /> : undefined
              }
            >
              {loading
                ? 'Menyimpan...'
                : isEdit
                  ? 'Perbarui KPI Project'
                  : 'Simpan KPI Project'}
            </Button>
          </div>
        </form>
      </div>

      {/* Confirmation Dialog */}
      <Dialog open={confirmDialogOpen} onOpenChange={setConfirmDialogOpen}>
        <DialogContent className="max-w-md max-h-[80vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>
              {isEdit
                ? 'Konfirmasi Perubahan KPI Project'
                : 'Konfirmasi Pembuatan KPI Project'}
            </DialogTitle>
            <DialogDescription>
              Berikut adalah detail {isEdit ? 'perubahan' : ''} KPI Project.
              Silakan periksa kembali sebelum melanjutkan.
            </DialogDescription>
          </DialogHeader>

          <div className="py-4 space-y-4">
            <div className="grid grid-cols-1 gap-2">
              <div>
                <h4 className="font-medium text-sm">Nama Proyek:</h4>
                <p className="text-sm">{selectedProject}</p>
              </div>

              <div>
                <h4 className="font-medium text-sm">Periode:</h4>
                <p className="text-sm">{selectedPeriod}</p>
              </div>

              <div>
                <h4 className="font-medium text-sm">Status:</h4>
                <p className="text-sm">
                  {statusOptions.find((s) => s.value === selectedStatus)
                    ?.label || selectedStatus}
                </p>
              </div>

              <div>
                <h4 className="font-medium text-sm">Deskripsi:</h4>
                <p className="text-sm whitespace-pre-wrap">
                  {watchedDescription || '-'}
                </p>
              </div>

              <div>
                <h4 className="font-medium text-sm">Target:</h4>
                <p className="text-sm whitespace-pre-wrap">
                  {watchedTarget || '-'}
                </p>
              </div>

              <div>
                <h4 className="font-medium text-sm">Catatan Tambahan:</h4>
                <p className="text-sm whitespace-pre-wrap">
                  {watchedAdditionalNotes || '-'}
                </p>
              </div>
            </div>
          </div>

          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => setConfirmDialogOpen(false)}
              disabled={loading}
            >
              Batal
            </Button>
            <Button onClick={confirmSubmit} disabled={loading}>
              {loading ? (
                <>
                  <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                  {isEdit ? 'Menyimpan...' : 'Membuat...'}
                </>
              ) : (
                <>
                  <Check className="h-4 w-4 mr-2" />
                  {isEdit ? 'Ya, Simpan Perubahan' : 'Ya, Buat KPI Project'}
                </>
              )}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default KpiProjectForm;
