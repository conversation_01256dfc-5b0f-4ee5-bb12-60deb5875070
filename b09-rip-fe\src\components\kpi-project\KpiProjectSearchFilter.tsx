import { SearchFilter, Filter } from '@/components/ui/search-filter';
import { KpiProjectStatus } from '@/types/kpi-project';

interface KpiProjectSearchFilterProps {
  search: string;
  status: KpiProjectStatus | undefined;
  statusOptions?: Array<{ value: string; label: string }>;
  onSearchChange: (value: string) => void;
  onStatusChange: (value: KpiProjectStatus | undefined) => void;
}

export function KpiProjectSearchFilter({
  search,
  status,
  statusOptions = [
    { value: 'not_started', label: 'Belum Dimulai' },
    { value: 'in_progress', label: 'Dalam Proses' },
    { value: 'completed_below_target', label: 'Selesai Di Bawah Target' },
    { value: 'completed_on_target', label: 'Selesai Sesuai Target' },
    { value: 'completed_above_target', label: 'Selesai Di Atas Target' },
  ],
  onSearchChange,
  onStatusChange,
}: KpiProjectSearchFilterProps) {

  // Filter options array
  const filters: Filter[] = [];

  // Always include status filter
  filters.push({
    label: 'Status',
    value: status,
    onChange: (value) => onStatusChange(value as KpiProjectStatus | undefined),
    options: statusOptions,
  });

  return (
    <SearchFilter
      search={search}
      onSearchChange={onSearchChange}
      filters={filters}
      searchPlaceholder="Cari berdasarkan nama proyek..."
    />
  );
}

export default KpiProjectSearchFilter;
