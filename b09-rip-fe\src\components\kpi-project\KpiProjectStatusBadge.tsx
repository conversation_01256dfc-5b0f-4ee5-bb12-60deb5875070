import { Badge } from '@/components/ui/badge';
import { KpiProjectStatus } from '@/types/kpi-project';
import { Trophy } from 'lucide-react';

interface KpiProjectStatusBadgeProps {
  status: KpiProjectStatus;
  className?: string;
}

export function KpiProjectStatusBadge({ status, className }: KpiProjectStatusBadgeProps) {
  const getVariant = (status: KpiProjectStatus) => {
    switch (status) {
      case 'not_started':
        return 'secondary';
      case 'in_progress':
        return 'info';
      case 'completed_below_target':
        return 'danger';
      case 'completed_on_target':
        return 'success';
      case 'completed_above_target':
        return 'premium';
      default:
        return 'secondary';
    }
  };

  const formatStatus = (status: string): string => {
    const statusDisplayMap: Record<string, string> = {
      not_started: 'Belum Dimulai',
      in_progress: 'Dalam Proses',
      completed_below_target: 'Selesai Di Bawah Target',
      completed_on_target: 'Selesai Sesuai Target',
      completed_above_target: 'Selesai Di Atas Target',
    };
    return statusDisplayMap[status] || status;
  };

  if (status === 'completed_above_target') {
    return (
      <Badge variant={getVariant(status)} className={className}>
        <Trophy className="h-3.5 w-3.5 mr-1 text-amber-500" />
        {formatStatus(status)}
      </Badge>
    );
  }

  return (
    <Badge variant={getVariant(status)} className={className}>
      {formatStatus(status)}
    </Badge>
  );
}

export default KpiProjectStatusBadge;
