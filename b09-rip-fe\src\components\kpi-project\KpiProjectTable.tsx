import React from 'react';
import { Eye } from 'lucide-react';
import { But<PERSON> } from '@/components/ui/button';
import { KpiProject } from '@/types/kpi-project';
import { KpiProjectStatusBadge } from './KpiProjectStatusBadge';
import { DataTable, SortDirection } from '@/components/ui/data-table';
import { useRouter } from 'next/navigation';

interface KpiProjectTableProps {
  kpiProjects: KpiProject[];
  // Pagination props removed as they're not used in DataTable anymore
  // currentPage: number;
  // itemsPerPage?: number;
  // onPageChange?: (page: number) => void;
  onViewDetail: (kpiProject: KpiProject) => void;
  loading?: boolean;
  sortField?: string;
  sortDirection?: SortDirection;
  onSort?: (field: string, direction: SortDirection) => void;
}

const KpiProjectTable: React.FC<KpiProjectTableProps> = ({
  kpiProjects,
  // Pagination props removed as they're not used in DataTable anymore
  // currentPage = 1,
  // itemsPerPage = 10,
  // onPageChange,
  onViewDetail,
  loading = false,
  sortField,
  sortDirection,
  onSort,
}) => {
  const router = useRouter();

  const handleProjectClick = (projectId: string) => {
    router.push(`/kpi-project/with-details?projectId=${projectId}`);
  };

  const columns = [
    {
      key: 'project_name',
      header: 'Nama Proyek',
      sortable: false,
      render: (kpiProject: KpiProject) => (
        <button
          onClick={() => handleProjectClick(kpiProject.project_id)}
          className="font-semibold text-gray-800 underline text-left hover:text-gray-900"
        >
          {kpiProject.project_name}
        </button>
      ),
    },
    {
      key: 'description',
      header: 'Deskripsi',
      render: (kpiProject: KpiProject) => (
        <div className="max-w-[200px] truncate">{kpiProject.description}</div>
      ),
    },
    {
      key: 'period',
      header: 'Periode',
      sortable: false,
      render: (kpiProject: KpiProject) => kpiProject.period,
    },
    {
      key: 'status',
      header: 'Status',
      sortable: false,
      render: (kpiProject: KpiProject) => (
        <KpiProjectStatusBadge status={kpiProject.status} />
      ),
    },
    {
      key: 'actions',
      header: 'Aksi',
      width: '120px',
      render: (kpiProject: KpiProject) => (
        <Button
          variant="outline"
          size="sm"
          onClick={() => onViewDetail(kpiProject)}
          leftIcon={<Eye className="h-4 w-4" />}
        >
          Lihat Detail
        </Button>
      ),
    },
  ];

  return (
    <DataTable
      columns={columns}
      data={kpiProjects}
      keyExtractor={(kpiProject) => kpiProject.id}
      // Pagination props removed as they're not used in DataTable anymore
      // currentPage={currentPage}
      // itemsPerPage={itemsPerPage}
      // onPageChange={onPageChange}
      loading={loading}
      sortField={sortField}
      sortDirection={sortDirection}
      onSort={onSort}
      emptyStateMessage="Tidak ada data KPI Proyek."
      // showPagination prop removed as it's not used in DataTable anymore
      // showPagination={false}
    />
  );
};

export default KpiProjectTable;
