'use client';

import React, { useState } from 'react';
import { Loader2 } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Label } from '@/components/ui/label';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogDescription,
  DialogFooter,
} from '@/components/ui/dialog';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { KpiProject, KpiProjectStatus } from '@/types/kpi-project';

interface KpiProjectStatusUpdateFormProps {
  kpiProject: KpiProject;
  open: boolean;
  onOpenChange: (open: boolean) => void;
  updateStatus: (status: KpiProjectStatus) => Promise<boolean | undefined>;
  statusUpdateLoading: boolean;
}

const KpiProjectStatusUpdateForm: React.FC<KpiProjectStatusUpdateFormProps> = ({
  kpiProject,
  open,
  onOpenChange,
  updateStatus,
  statusUpdateLoading,
}) => {
  const [status, setStatus] = useState<KpiProjectStatus>(kpiProject.status);

  // Status options with display names for KpiProject
  const statusOptions = [
    { value: 'not_started', label: 'Belum Dimulai' },
    { value: 'in_progress', label: 'Dalam Proses' },
    { value: 'completed_below_target', label: 'Selesai Di Bawah Target' },
    { value: 'completed_on_target', label: 'Selesai Sesuai Target' },
    { value: 'completed_above_target', label: 'Selesai Di Atas Target' },
  ];

  const handleSubmit = async () => {
    // Only proceed if status has changed
    if (status !== kpiProject.status) {
      const success = await updateStatus(status);
      if (success) {
        onOpenChange(false);
      }
    } else {
      onOpenChange(false);
    }
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent>
        <DialogHeader>
          <DialogTitle>Update Status KPI Project</DialogTitle>
          <DialogDescription>
            Perbarui status KPI Project untuk proyek {kpiProject.project_name}
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-4 py-4">
          <div className="space-y-2">
            <Label htmlFor="status">Status</Label>
            <Select
              value={status}
              onValueChange={(value) => setStatus(value as KpiProjectStatus)}
            >
              <SelectTrigger>
                <SelectValue placeholder="Pilih status" />
              </SelectTrigger>
              <SelectContent>
                {statusOptions.map((option) => (
                  <SelectItem key={option.value} value={option.value}>
                    {option.label}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
        </div>

        <DialogFooter>
          <Button
            variant="cancel"
            onClick={() => onOpenChange(false)}
            disabled={statusUpdateLoading}
          >
            Batal
          </Button>
          <Button
            onClick={handleSubmit}
            disabled={statusUpdateLoading || status === kpiProject.status}
            leftIcon={statusUpdateLoading ? <Loader2 className="animate-spin" /> : undefined}
          >
            {statusUpdateLoading ? 'Memperbarui...' : 'Perbarui Status'}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

export default KpiProjectStatusUpdateForm;
