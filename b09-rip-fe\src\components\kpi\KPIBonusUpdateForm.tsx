'use client';

import React, { useState } from 'react';
import { Loader2 } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Label } from '@/components/ui/label';
import { Input } from '@/components/ui/input';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogDescription,
  DialogFooter,
} from '@/components/ui/dialog';
import { KPI } from '@/types/kpi';
import { formatCurrency } from '@/lib/utils/format';

interface KPIBonusUpdateFormProps {
  kpi: KPI;
  open: boolean;
  onOpenChange: (open: boolean) => void;
  updateBonus: (bonusReceived: number) => Promise<boolean | undefined>;
  bonusUpdateLoading: boolean;
}

const KPIBonusUpdateForm: React.FC<KPIBonusUpdateFormProps> = ({
  kpi,
  open,
  onOpenChange,
  updateBonus,
  bonusUpdateLoading,
}) => {
  const [bonus, setBonus] = useState<number>(kpi.bonus_received || 0);
  const [error, setError] = useState<string | null>(null);

  const handleBonusChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    const numericValue = parseInt(value, 10);

    if (value === '') {
      setBonus(0);
      setError(null);
    } else if (isNaN(numericValue)) {
      setError('Bonus harus berupa angka');
    } else if (numericValue < 0) {
      setError('Bonus tidak boleh negatif');
    } else {
      setBonus(numericValue);
      setError(null);
    }
  };

  const handleSubmit = async () => {
    if (error) return;

    // Only proceed if bonus has changed
    if (bonus !== (kpi.bonus_received || 0)) {
      const success = await updateBonus(bonus);
      if (success) {
        onOpenChange(false);
      }
    } else {
      onOpenChange(false);
    }
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent>
        <DialogHeader>
          <DialogTitle>Update Bonus KPI</DialogTitle>
          <DialogDescription>
            Perbarui jumlah bonus untuk KPI karyawan {kpi.full_name}
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-4 py-4">
          <div className="space-y-2">
            <Label htmlFor="bonus">Jumlah Bonus (Rp)</Label>
            <Input
              id="bonus"
              type="number"
              min="0"
              value={bonus}
              onChange={handleBonusChange}
            />
            {error && <p className="text-red-500 text-sm">{error}</p>}
            {bonus > 0 && (
              <p className="text-sm text-gray-500 mt-1">
                Format: {formatCurrency(bonus)}
              </p>
            )}
          </div>
        </div>

        <DialogFooter>
          <Button
            variant="cancel"
            onClick={() => onOpenChange(false)}
            disabled={bonusUpdateLoading}
          >
            Batal
          </Button>
          <Button
            onClick={handleSubmit}
            disabled={
              bonusUpdateLoading ||
              error !== null ||
              bonus === (kpi.bonus_received || 0)
            }
            leftIcon={
              bonusUpdateLoading ? (
                <Loader2 className="animate-spin" />
              ) : undefined
            }
          >
            {bonusUpdateLoading ? 'Memperbarui...' : 'Perbarui Bonus'}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

export default KPIBonusUpdateForm;
