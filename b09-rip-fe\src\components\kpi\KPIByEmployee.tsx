// path: b09-rip-fe/src/components/kpi/KPIByEmployee.tsx
'use client';

import React from 'react';
import { useEmployeeKPI } from '@/hooks/useEmployeeKPI';
import KPITable from '@/components/kpi/KPITable';
import { Button } from '@/components/ui/button';
import { Plus } from 'lucide-react';
import { useRouter } from 'next/navigation';
import { KPISearchFilter } from '@/components/kpi/KPISearchFilter';
import { PageTitle } from '@/components/ui/PageTitle';
import { BackButton } from '@/components/ui/BackButton';
import { SortDirection } from '@/components/ui/data-table';

interface KPIByEmployeeProps {
  employeeId: string;
}

const KPIByEmployee: React.FC<KPIByEmployeeProps> = ({ employeeId }) => {
  const router = useRouter();
  const [sortField, setSortField] = React.useState<string | undefined>(
    undefined
  );
  const [sortDirection, setSortDirection] = React.useState<SortDirection>(null);

  const {
    kpis,
    loading,
    search,
    period,
    status,
    employeeProfile,
    // currentPage, // Not used after DataTable changes
    handleViewDetail,
    handleSearchChange,
    handlePeriodChange,
    handleStatusChange,
    // handlePageChange, // Not used after DataTable changes
    handleBackToAll,
  } = useEmployeeKPI(employeeId);

  const handleSort = (field: string, direction: SortDirection) => {
    setSortField(field);
    setSortDirection(direction);
  };

  const generatePeriodOptions = () => {
    const currentYear = new Date().getFullYear();
    const options = [];

    for (let i = 1; i <= 4; i++) {
      options.push({
        value: `${currentYear - 1}-Q${i}`,
        label: `${currentYear - 1}-Q${i}`,
      });
    }

    for (let i = 1; i <= 4; i++) {
      options.push({
        value: `${currentYear}-Q${i}`,
        label: `${currentYear}-Q${i}`,
      });
    }

    return options;
  };

  const statusOptions = [
    { value: 'not_started', label: 'Belum Dimulai' },
    { value: 'in_progress', label: 'Dalam Proses' },
    { value: 'completed_below_target', label: 'Selesai Di Bawah Target' },
    { value: 'completed_on_target', label: 'Selesai Sesuai Target' },
    { value: 'completed_above_target', label: 'Selesai Di Atas Target' },
  ];

  const handleAddKPI = () => {
    router.push('/employee/kpi/add');
  };

  // Debug: Log the employeeProfile object
  console.log('employeeProfile:', employeeProfile);
  console.log('kpis:', kpis);

  // Function to get the title based on available data
  const getTitle = () => {
    if (loading) {
      return 'Memuat...';
    }

    if (employeeProfile) {
      return `KPI Karyawan: ${employeeProfile.fullname} (${employeeProfile.role})`;
    }

    if (kpis.length > 0) {
      return `KPI Karyawan: ${kpis[0].full_name}`;
    }

    return 'KPI Karyawan';
  };

  return (
    <div className="container mx-auto py-6 px-6">
      <div className="flex justify-between items-center mb-6">
        <div className="flex items-center gap-6">
          <BackButton onClick={handleBackToAll} />
          <PageTitle
            title={getTitle()}
            subtitle="Kelola dan pantau KPI karyawan"
          />
        </div>

        <Button onClick={handleAddKPI} className="flex items-center gap-2">
          <Plus className="h-4 w-4" />
          Tambah KPI
        </Button>
      </div>



      <div className="bg-white rounded-lg shadow p-6">
        <KPISearchFilter
          search={search}
          period={period}
          status={status}
          periodOptions={generatePeriodOptions()}
          statusOptions={statusOptions}
          onSearchChange={handleSearchChange}
          onPeriodChange={handlePeriodChange}
          onStatusChange={handleStatusChange}
        />

        <div className="overflow-x-auto">
          <KPITable
            kpis={kpis}
            // Pagination props removed as they're not used in KPITable anymore
            // currentPage={currentPage}
            // itemsPerPage={10}
            // onPageChange={handlePageChange}
            onViewDetail={handleViewDetail}
            loading={loading}
            sortField={sortField}
            sortDirection={sortDirection}
            onSort={handleSort}
            hideEmployeeNameColumn={true}
          />
        </div>
      </div>
    </div>
  );
};

export default KPIByEmployee;
