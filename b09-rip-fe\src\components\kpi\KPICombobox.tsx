'use client';

import * as React from 'react';
import { Check, ChevronsUpDown } from 'lucide-react';
import { cn } from '@/lib/utils';
import { Button } from '@/components/ui/button';
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
} from '@/components/ui/command';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover';
import { KPI } from '@/types/kpi';
import { kpiApi } from '@/lib/api/kpi';

interface KPIComboboxProps {
  value: string; // KPI description or name
  onSelect: (kpiId: string, kpiDescription: string) => void;
  placeholder?: string;
  className?: string;
  disabled?: boolean;
}

export function KPICombobox({
  value,
  onSelect,
  placeholder = 'Pilih KPI...',
  className,
  disabled = false,
}: KPIComboboxProps) {
  const [open, setOpen] = React.useState(false);
  const [kpis, setKpis] = React.useState<KPI[]>([]);
  const [loading, setLoading] = React.useState(false);
  const [searchQuery, setSearchQuery] = React.useState('');
  const [error, setError] = React.useState<string | null>(null);

  // Load KPIs on component mount
  React.useEffect(() => {
    if (disabled && value) return;

    const fetchKPIs = async () => {
      setLoading(true);
      setError(null);
      try {
        const response = await kpiApi.getKPIs({
          page: 1,
          pageSize: 1000,
          search: searchQuery,
        });

        if (response.success && response.data) {
          // Check if items exists in the expected structure
          if (response.data.items && Array.isArray(response.data.items)) {
            setKpis(response.data.items);
          } else {
            console.error(
              'Unexpected data structure in KPI response:',
              response.data
            );
            setKpis([]);
          }
        } else {
          console.error('API response not successful:', response);
          setKpis([]);
          setError('Gagal memuat data KPI');
        }
      } catch (error) {
        console.error('Error fetching KPIs:', error);
        setKpis([]);
        setError('Terjadi kesalahan saat mengambil data KPI');
      } finally {
        setLoading(false);
      }
    };

    fetchKPIs();
  }, [searchQuery, disabled, value]);

  const handleSearchChange = React.useCallback((value: string) => {
    const timeoutId = setTimeout(() => {
      setSearchQuery(value);
    }, 300);

    return () => clearTimeout(timeoutId);
  }, []);

  const selectedKPI = kpis.find((kpi) => kpi.description === value);
  const displayValue = selectedKPI
    ? selectedKPI.description
    : value || placeholder;

  const handleOpenChange = (open: boolean) => {
    if (!disabled && !error) {
      setOpen(open);
    }
  };

  return (
    <Popover open={open} onOpenChange={handleOpenChange}>
      <PopoverTrigger asChild>
        <Button
          variant="outline"
          role="combobox"
          aria-expanded={open}
          className={cn(
            'w-full justify-between border-input text-foreground',
            (disabled || error) && 'opacity-70 cursor-not-allowed',
            className
          )}
          disabled={disabled || !!error}
        >
          {error ? 'Gagal memuat data' : displayValue}
          <ChevronsUpDown
            className={cn(
              'opacity-50 h-4 w-4 ml-2 shrink-0',
              (disabled || error) && 'opacity-30'
            )}
          />
        </Button>
      </PopoverTrigger>
      {!disabled && !error && (
        <PopoverContent className="w-full p-0">
          <Command>
            <CommandInput
              placeholder="Cari KPI..."
              className="h-9"
              onValueChange={handleSearchChange}
            />
            <CommandList>
              {loading ? (
                <CommandEmpty>Loading...</CommandEmpty>
              ) : kpis.length === 0 ? (
                <CommandEmpty>KPI tidak ditemukan.</CommandEmpty>
              ) : (
                <CommandGroup>
                  {kpis.map((kpi) => (
                    <CommandItem
                      key={kpi.id}
                      value={kpi.description}
                      onSelect={() => {
                        onSelect(kpi.id, kpi.description);
                        setOpen(false);
                      }}
                    >
                      <span>{kpi.description}</span>
                      <Check
                        className={cn(
                          'ml-auto h-4 w-4',
                          value === kpi.description ? 'opacity-100' : 'opacity-0'
                        )}
                      />
                    </CommandItem>
                  ))}
                </CommandGroup>
              )}
            </CommandList>
          </Command>
        </PopoverContent>
      )}
    </Popover>
  );
}
