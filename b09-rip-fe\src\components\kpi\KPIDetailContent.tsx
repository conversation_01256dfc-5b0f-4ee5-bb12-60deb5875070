//path: b09-rip-fe/src/components/kpi/KPIDetailContent.tsx
'use client';

import React, { useState } from 'react';
import { useRouter } from 'next/navigation';
import { Edit, Trash2, AlertCircle, Award } from 'lucide-react';
import { But<PERSON> } from '@/components/ui/button';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogDescription,
  DialogFooter,
} from '@/components/ui/dialog';
import {
  Card,
  CardContent,
  CardFooter,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Separator } from '@/components/ui/separator';
import { useKPIDetail } from '@/hooks/useKPIDetail';
import { formatDate } from '@/lib/utils/date';
import { formatCurrency } from '@/lib/utils/format';
import KPIStatusUpdateForm from './KPIStatusUpdateForm';
import KPIBonusUpdateForm from './KPIBonusUpdateForm';
import { PageTitle } from '@/components/ui/PageTitle';
import { BackButton } from '@/components/ui/BackButton';
import { KPIStatusBadge } from './KPIStatusBadge';

interface KPIDetailContentProps {
  id: string;
}

const KPIDetailContent: React.FC<KPIDetailContentProps> = ({ id }) => {
  const router = useRouter();
  const {
    kpi,
    loading,
    deleteLoading,
    deleteKPI,
    updateStatus,
    statusUpdateLoading,
    updateBonus,
    bonusUpdateLoading,
  } = useKPIDetail(id);

  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [statusUpdateOpen, setStatusUpdateOpen] = useState(false);
  const [bonusUpdateOpen, setBonusUpdateOpen] = useState(false);

  const handleEditClick = () => {
    router.push(`/employee/kpi/${id}/edit`);
  };

  const handleDeleteClick = () => {
    setDeleteDialogOpen(true);
  };

  const handleDeleteConfirm = async () => {
    const success = await deleteKPI();
    if (success) {
      setDeleteDialogOpen(false);
    }
  };

  return (
    <div className="max-w-5xl mx-auto">
      <div className="mb-6 flex justify-between items-start">
        <div className="flex items-center gap-4">
          <BackButton onClick={() => router.push('/employee/kpi')} />
          <PageTitle
            title="Detail KPI"
            subtitle="Lihat dan kelola detail KPI karyawan."
          />
        </div>
        <div className="flex space-x-2">
          <Button
            variant="outline"
            size="sm"
            onClick={handleEditClick}
            disabled={loading}
            leftIcon={<Edit />}
          >
            Edit
          </Button>
          <Button
            variant="destructive"
            size="sm"
            onClick={handleDeleteClick}
            disabled={loading || deleteLoading}
            leftIcon={<Trash2 />}
          >
            Hapus
          </Button>
        </div>
      </div>

      {loading ? (
        <div className="bg-white rounded-lg shadow p-6 text-center py-8">
          Loading...
        </div>
      ) : !kpi ? (
        <div className="bg-white rounded-lg shadow p-6 text-center py-8">
          <AlertCircle className="h-8 w-8 text-red-500 mx-auto mb-4" />
          <h2 className="text-xl font-medium">KPI tidak ditemukan</h2>
          <p className="text-gray-500 mt-2">
            KPI dengan ID ini tidak ditemukan atau telah dihapus.
          </p>
          <Button className="mt-4" onClick={() => router.push('/employee/kpi')}>
            Kembali ke Daftar KPI
          </Button>
        </div>
      ) : (
        <>
          <Card className="mb-6">
            <CardHeader>
              <div className="flex justify-between items-center">
                <div>
                  <CardTitle className="text-xl">{kpi.full_name}</CardTitle>
                </div>
                <div>
                  <KPIStatusBadge status={kpi.status} />
                </div>
              </div>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <h3 className="text-sm font-medium text-gray-500">Periode</h3>
                  <p className="mt-1">{kpi.period}</p>
                </div>
                <div>
                  <h3 className="text-sm font-medium text-gray-500">Bonus</h3>
                  <p className="mt-1">
                    {kpi.bonus_received
                      ? formatCurrency(kpi.bonus_received)
                      : '-'}
                  </p>
                </div>
              </div>

              <Separator />

              <div>
                <h3 className="text-sm font-medium text-gray-500 mb-2">
                  Deskripsi KPI
                </h3>
                <p className="whitespace-pre-wrap">{kpi.description}</p>
              </div>

              <div>
                <h3 className="text-sm font-medium text-gray-500 mb-2">
                  Target
                </h3>
                <p className="whitespace-pre-wrap">{kpi.target}</p>
              </div>

              {kpi.additional_notes && (
                <div>
                  <h3 className="text-sm font-medium text-gray-500 mb-2">
                    Catatan Tambahan
                  </h3>
                  <p className="whitespace-pre-wrap">{kpi.additional_notes}</p>
                </div>
              )}
            </CardContent>
            <CardFooter>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4 w-full">
                <Button
                  onClick={() => setStatusUpdateOpen(true)}
                  className="w-full"
                  leftIcon={<Edit />}
                >
                  Update Status
                </Button>
                <Button
                  onClick={() => setBonusUpdateOpen(true)}
                  className="w-full"
                  variant="outline"
                  leftIcon={<Award />}
                >
                  Update Bonus
                </Button>
              </div>
            </CardFooter>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle className="text-lg">Informasi Tambahan</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <h3 className="text-sm font-medium text-gray-500">
                    Dibuat Pada
                  </h3>
                  <p className="mt-1">{formatDate(kpi.created_at)}</p>
                </div>
                {kpi.updated_at && (
                  <div>
                    <h3 className="text-sm font-medium text-gray-500">
                      Diperbarui Pada
                    </h3>
                    <p className="mt-1">{formatDate(kpi.updated_at)}</p>
                  </div>
                )}
              </div>
            </CardContent>
          </Card>

          {/* Status Update Dialog */}
          <KPIStatusUpdateForm
            kpi={kpi}
            open={statusUpdateOpen}
            onOpenChange={setStatusUpdateOpen}
            updateStatus={updateStatus}
            statusUpdateLoading={statusUpdateLoading}
          />

          {/* Bonus Update Dialog */}
          <KPIBonusUpdateForm
            kpi={kpi}
            open={bonusUpdateOpen}
            onOpenChange={setBonusUpdateOpen}
            updateBonus={updateBonus}
            bonusUpdateLoading={bonusUpdateLoading}
          />

          {/* Delete Confirmation Dialog */}
          <Dialog open={deleteDialogOpen} onOpenChange={setDeleteDialogOpen}>
            <DialogContent>
              <DialogHeader>
                <DialogTitle>Hapus KPI</DialogTitle>
                <DialogDescription>
                  Apakah Anda yakin ingin menghapus KPI ini? Tindakan ini tidak
                  dapat dibatalkan.
                </DialogDescription>
              </DialogHeader>
              <DialogFooter>
                <Button
                  variant="cancel"
                  onClick={() => setDeleteDialogOpen(false)}
                  disabled={deleteLoading}
                >
                  Batal
                </Button>
                <Button
                  variant="destructive"
                  onClick={handleDeleteConfirm}
                  disabled={deleteLoading}
                >
                  {deleteLoading ? 'Menghapus...' : 'Hapus'}
                </Button>
              </DialogFooter>
            </DialogContent>
          </Dialog>
        </>
      )}
    </div>
  );
};

export default KPIDetailContent;
