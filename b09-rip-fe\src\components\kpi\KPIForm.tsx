import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { useForm } from 'react-hook-form';
import { toast } from 'sonner';
import { Loader2 } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Label } from '@/components/ui/label';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import {
  KPI,
  CreateKPIRequest,
  UpdateKPIRequest,
  KPIStatus,
} from '@/types/kpi';
import { kpiApi } from '@/lib/api/kpi';
import { EmployeeCombobox } from '@/components/employee/EmployeeCombobox';
import { cn } from '@/lib/utils';
import { PageTitle } from '@/components/ui/PageTitle';
import { BackButton } from '@/components/ui/BackButton';

interface KPIFormProps {
  initialData?: KPI;
  isEdit?: boolean;
}

const KPIForm: React.FC<KPIFormProps> = ({ initialData, isEdit = false }) => {
  const router = useRouter();
  const [loading, setLoading] = useState(false);
  // Initialize with empty values first, will be set in useEffect
  const [selectedPeriod, setSelectedPeriod] = useState<string>('');
  const [selectedStatus, setSelectedStatus] =
    useState<KPIStatus>('not_started');
  const [isInitialized, setIsInitialized] = useState(false);

  // Generate period options (quarters for current year and previous year)
  const generatePeriodOptions = () => {
    const currentYear = new Date().getFullYear();
    const options = [];

    // Previous year
    for (let i = 1; i <= 4; i++) {
      options.push(`${currentYear - 1}-Q${i}`);
    }

    // Current year and next year (to account for future periods)
    for (let i = 1; i <= 4; i++) {
      options.push(`${currentYear}-Q${i}`);
    }

    for (let i = 1; i <= 4; i++) {
      options.push(`${currentYear + 1}-Q${i}`);
    }

    // Additional years if needed (in case the initialData.period is from an older year)
    if (initialData?.period && !options.includes(initialData.period)) {
      // Extract year from period (format: YYYY-Q#)
      const match = initialData.period.match(/^(\d{4})-Q\d$/);
      if (match) {
        const year = parseInt(match[1], 10);
        // Add quarters for the year from initialData.period
        for (let i = 1; i <= 4; i++) {
          // Add at beginning or end based on whether year is past or future
          if (year < currentYear - 1) {
            options.unshift(`${year}-Q${i}`);
          } else if (year > currentYear + 1) {
            options.push(`${year}-Q${i}`);
          }
        }
      }
    }

    return options;
  };

  const periodOptions = generatePeriodOptions();

  // Status options with display names
  const statusOptions = [
    { value: 'not_started', label: 'Belum Dimulai' },
    { value: 'in_progress', label: 'Dalam Proses' },
    { value: 'completed_below_target', label: 'Selesai Di Bawah Target' },
    { value: 'completed_on_target', label: 'Selesai Sesuai Target' },
    { value: 'completed_above_target', label: 'Selesai Di Atas Target' },
  ];

  const {
    register,
    handleSubmit,
    setValue,
    getValues,
    watch,
    formState: { errors },
    reset,
  } = useForm<CreateKPIRequest | UpdateKPIRequest>({
    defaultValues: {
      full_name: '',
      employee_id: '',
      description: '',
      target: '',
      period: '',
      status: 'not_started',
      bonus_received: undefined,
      additional_notes: '',
    },
  });

  // Register period and status fields
  useEffect(() => {
    register('period', { required: 'Periode diperlukan' });
    register('status', { required: 'Status diperlukan' });
    register('employee_id', { required: 'ID karyawan diperlukan' });
    register('full_name', { required: 'Nama karyawan diperlukan' });
  }, [register]);

  // Set initial form values when initialData changes
  useEffect(() => {
    if (initialData) {
      console.log('Setting initial data:', initialData);

      // Reset the form with initial data, transforming null to undefined
      reset({
        ...initialData,
        period: initialData.period || '',
        status: initialData.status || 'not_started',
        // Transform null to undefined for bonus_received
        bonus_received:
          initialData.bonus_received === null
            ? undefined
            : initialData.bonus_received,
      });

      // Explicitly set the period value
      if (initialData.period) {
        console.log('Setting period to:', initialData.period);
        // Use a timeout to ensure state is updated after the component has mounted
        setTimeout(() => {
          setSelectedPeriod(initialData.period);
          setValue('period', initialData.period);
        }, 0);
      }

      // Explicitly set the status value
      if (initialData.status) {
        console.log('Setting status to:', initialData.status);
        // Use a timeout to ensure state is updated after the component has mounted
        setTimeout(() => {
          setSelectedStatus(initialData.status);
          setValue('status', initialData.status);
        }, 0);
      }

      // Set employee data
      if (initialData.employee_id) {
        setValue('employee_id', initialData.employee_id);
      }
      if (initialData.full_name) {
        setValue('full_name', initialData.full_name);
      }

      setIsInitialized(true);
    }
  }, [initialData, reset, setValue]);

  // Debug effect to verify state updates
  useEffect(() => {
    if (isEdit && initialData) {
      console.log('Current state values:');
      console.log('- selectedPeriod:', selectedPeriod);
      console.log('- initialData.period:', initialData.period);
      console.log('- selectedStatus:', selectedStatus);
      console.log('- initialData.status:', initialData.status);
      console.log('- Form values:', getValues());
    }
  }, [selectedPeriod, selectedStatus, initialData, isEdit, getValues]);

  // Handle employee selection
  const handleEmployeeSelect = (employeeId: string, fullName: string) => {
    setValue('employee_id', employeeId, { shouldValidate: true });
    setValue('full_name', fullName, { shouldValidate: true });
  };

  const onSubmit = async (data: CreateKPIRequest | UpdateKPIRequest) => {
    setLoading(true);
    try {
      // Ensure bonus is sent as a number, not a string
      if (data.bonus_received) {
        data.bonus_received = Number(data.bonus_received);
      }

      let response;

      if (isEdit && initialData) {
        // Update existing KPI
        response = await kpiApi.updateKPI(initialData.id, data);
        if (response.success) {
          toast.success('KPI berhasil diperbarui');
          router.push(`/employee/kpi/${initialData.id}`);
        } else {
          toast.error('Gagal memperbarui KPI');
        }
      } else {
        // Create new KPI
        response = await kpiApi.createKPI(data as CreateKPIRequest);
        if (response.success) {
          toast.success('KPI berhasil dibuat');
          router.push('/employee/kpi');
        } else {
          toast.error('Gagal membuat KPI');
        }
      }
    } catch (error: unknown) {
      console.error('Error submitting KPI form:', error);

      if (
        error &&
        typeof error === 'object' &&
        'response' in error &&
        error.response
      ) {
        const errorResponse = error.response as { status?: number };
        if (errorResponse.status === 401) {
          toast.error('Sesi habis. Silakan login kembali.');
        } else if (errorResponse.status === 403) {
          toast.error('Anda tidak memiliki izin untuk melakukan tindakan ini.');
        } else {
          toast.error('Terjadi kesalahan. Silakan coba lagi nanti.');
        }
      } else {
        toast.error('Terjadi kesalahan. Silakan coba lagi nanti.');
      }
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="max-w-5xl mx-auto">
      <div className="flex items-center gap-4 mb-6">
        <BackButton
          onClick={() => {
            if (isEdit && initialData) {
              router.push(`/employee/kpi/${initialData.id}`);
            } else {
              router.push('/employee/kpi');
            }
          }}
        />
        <PageTitle
          title={isEdit ? 'Edit KPI' : 'Tambah KPI Baru'}
          subtitle={
            isEdit
              ? 'Edit informasi KPI karyawan.'
              : 'Lengkapi formulir untuk menambahkan KPI baru.'
          }
        />
      </div>

      <div className="bg-white rounded-lg shadow p-6">
        <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="md:col-span-2">
              <Label htmlFor="employee">Karyawan</Label>
              <EmployeeCombobox
                value={watch('employee_id') || ''}
                onSelect={handleEmployeeSelect}
                placeholder="Pilih karyawan..."
                className={cn(
                  'mt-1',
                  isEdit && 'opacity-70 pointer-events-none'
                )}
                disabled={isEdit}
              />
              {(errors.employee_id || errors.full_name) && (
                <p className="text-red-500 text-sm mt-1">
                  Karyawan harus dipilih
                </p>
              )}
            </div>

            <div>
              <Label htmlFor="period">Periode</Label>
              <div className="relative">
                {initialData?.period && !isInitialized && (
                  <div className="absolute inset-0 flex items-center px-3 text-muted-foreground bg-gray-100/50 rounded pointer-events-none">
                    Loading...
                  </div>
                )}
                <Select
                  value={selectedPeriod || undefined}
                  onValueChange={(value) => {
                    console.log('Period changed to:', value);
                    setSelectedPeriod(value);
                    setValue('period', value, { shouldValidate: true });
                  }}
                  name="period"
                  defaultValue={initialData?.period}
                >
                  <SelectTrigger id="period" className="mt-1">
                    <SelectValue placeholder="Pilih periode">
                      {selectedPeriod || initialData?.period || 'Pilih periode'}
                    </SelectValue>
                  </SelectTrigger>
                  <SelectContent>
                    {periodOptions.map((period) => (
                      <SelectItem key={period} value={period}>
                        {period}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
              {errors.period && (
                <p className="text-red-500 text-sm mt-1">
                  {errors.period.message}
                </p>
              )}
            </div>

            <div>
              <Label htmlFor="status">Status</Label>
              <div className="relative">
                {initialData?.status && !isInitialized && (
                  <div className="absolute inset-0 flex items-center px-3 text-muted-foreground bg-gray-100/50 rounded pointer-events-none">
                    Loading...
                  </div>
                )}
                <Select
                  value={selectedStatus || undefined}
                  onValueChange={(value) => {
                    console.log('Status changed to:', value);
                    setSelectedStatus(value as KPIStatus);
                    setValue('status', value as KPIStatus, {
                      shouldValidate: true,
                    });
                  }}
                  name="status"
                  defaultValue={initialData?.status}
                >
                  <SelectTrigger id="status" className="mt-1">
                    <SelectValue placeholder="Pilih status">
                      {statusOptions.find(
                        (s) =>
                          s.value === (selectedStatus || initialData?.status)
                      )?.label || 'Pilih status'}
                    </SelectValue>
                  </SelectTrigger>
                  <SelectContent>
                    {statusOptions.map((status) => (
                      <SelectItem key={status.value} value={status.value}>
                        {status.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
              {errors.status && (
                <p className="text-red-500 text-sm mt-1">
                  {errors.status.message}
                </p>
              )}
            </div>

            <div className="md:col-span-2">
              <Label htmlFor="description">Deskripsi KPI</Label>
              <Textarea
                id="description"
                {...register('description', {
                  required: 'Deskripsi KPI diperlukan',
                })}
                className="mt-1"
                rows={3}
              />
              {errors.description && (
                <p className="text-red-500 text-sm mt-1">
                  {errors.description.message}
                </p>
              )}
            </div>

            <div className="md:col-span-2">
              <Label htmlFor="target">Target</Label>
              <Textarea
                id="target"
                {...register('target', { required: 'Target diperlukan' })}
                className="mt-1"
                rows={3}
              />
              {errors.target && (
                <p className="text-red-500 text-sm mt-1">
                  {errors.target.message}
                </p>
              )}
            </div>

            <div>
              <Label htmlFor="bonus_received">Bonus (Rp)</Label>
              <Input
                id="bonus_received"
                type="number"
                {...register('bonus_received', {
                  min: { value: 0, message: 'Bonus tidak boleh negatif' },
                  valueAsNumber: true, // Ensure value is cast to a number
                })}
                className="mt-1"
                placeholder="0"
              />
              {errors.bonus_received && (
                <p className="text-red-500 text-sm mt-1">
                  {errors.bonus_received.message}
                </p>
              )}
            </div>

            <div className="md:col-span-2">
              <Label htmlFor="additional_notes">Catatan Tambahan</Label>
              <Textarea
                id="additional_notes"
                {...register('additional_notes')}
                className="mt-1"
                rows={3}
              />
            </div>
          </div>

          <div className="flex justify-end space-x-4 pt-4">
            <Button
              type="button"
              variant="cancel"
              onClick={() => router.push('/employee/kpi')}
              disabled={loading}
            >
              Batal
            </Button>
            <Button
              type="submit"
              disabled={loading}
              leftIcon={
                loading ? <Loader2 className="animate-spin" /> : undefined
              }
            >
              {loading
                ? 'Menyimpan...'
                : isEdit
                  ? 'Perbarui KPI'
                  : 'Simpan KPI'}
            </Button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default KPIForm;
