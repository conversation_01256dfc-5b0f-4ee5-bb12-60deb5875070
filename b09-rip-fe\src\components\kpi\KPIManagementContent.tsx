// path: b09-rip-fe/src/components/kpi/KPIManagementContent.tsx
import React, { useState } from 'react';
import { Plus } from 'lucide-react';
import { useRouter } from 'next/navigation';
import { Button } from '@/components/ui/button';
import { useKPIManagement } from '@/hooks/useKPIManagement';
import KPITable from '@/components/kpi/KPITable';
import { KPISearchFilter } from '@/components/kpi/KPISearchFilter';
import { PageTitle } from '@/components/ui/PageTitle';
import { SortDirection } from '@/components/ui/data-table';

const KPIManagementContent: React.FC = () => {
  const router = useRouter();
  const {
    kpis,
    loading,
    search,
    period,
    status,
    handleViewDetail,
    handleSearchChange,
    handlePeriodChange,
    handleStatusChange,
  } = useKPIManagement();

  // Add sorting state
  const [sortField, setSortField] = useState<string | undefined>(undefined);
  const [sortDirection, setSortDirection] = useState<SortDirection>(null);

  // Handle sorting
  const handleSort = (field: string, direction: SortDirection) => {
    setSortField(field);
    setSortDirection(direction);
    // Here you would typically call an API with the new sort parameters
    // or update your local sorting logic
  };

  const handleAddKPI = () => {
    router.push('/employee/kpi/add');
  };

  // Generate period options (quarters for current year and previous year)
  const generatePeriodOptions = () => {
    const currentYear = new Date().getFullYear();
    const options = [];

    // Previous year
    for (let i = 1; i <= 4; i++) {
      options.push({
        value: `${currentYear - 1}-Q${i}`,
        label: `${currentYear - 1}-Q${i}`,
      });
    }

    // Current year
    for (let i = 1; i <= 4; i++) {
      options.push({
        value: `${currentYear}-Q${i}`,
        label: `${currentYear}-Q${i}`,
      });
    }

    return options;
  };

  return (
    <div className="container mx-auto py-6 px-6">
      <div className="flex justify-between items-center mb-6">
        <PageTitle
          title="Manajemen KPI"
          subtitle="Kelola dan pantau KPI seluruh karyawan"
        />
        <Button onClick={handleAddKPI} className="flex items-center gap-2">
          <Plus className="h-4 w-4" />
          Tambah KPI
        </Button>
      </div>

      <div className="bg-white rounded-lg shadow p-6">
        <KPISearchFilter
          search={search}
          period={period}
          status={status}
          periodOptions={generatePeriodOptions()}
          onSearchChange={handleSearchChange}
          onPeriodChange={handlePeriodChange}
          onStatusChange={handleStatusChange}
        />

        <div className="overflow-x-auto">
          <KPITable
            kpis={kpis}
            // itemsPerPage prop removed as it's not used in KPITable anymore
            // itemsPerPage={10}
            onViewDetail={handleViewDetail}
            loading={loading}
            sortField={sortField}
            sortDirection={sortDirection}
            onSort={handleSort}
          />
        </div>
      </div>
    </div>
  );
};

export default KPIManagementContent;
