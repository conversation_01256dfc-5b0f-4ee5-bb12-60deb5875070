'use client';

import { SearchFilter, Filter } from '@/components/ui/search-filter';
import { KPIStatus } from '@/types/kpi';

interface KPISearchFilterProps {
  search: string;
  period: string | undefined;
  status: KPIStatus | undefined;
  periodOptions?: Array<{ value: string; label: string }>;
  statusOptions?: Array<{ value: string; label: string }>;
  onSearchChange: (value: string) => void;
  onPeriodChange: (value: string | undefined) => void;
  onStatusChange: (value: KPIStatus | undefined) => void;
}

export function KPISearchFilter({
  search,
  period,
  status,
  periodOptions = [],
  statusOptions = [
    { value: 'not_started', label: 'Belum Dimulai' },
    { value: 'in_progress', label: 'Dalam Proses' },
    { value: 'completed_below_target', label: 'Selesai Di Bawah Target' },
    { value: 'completed_on_target', label: 'Selesai Sesuai Target' },
    { value: 'completed_above_target', label: 'Selesai Di Atas Target' },
  ],
  onSearchChange,
  onPeriodChange,
  onStatusChange,
}: KPISearchFilterProps) {
  const filters: Filter[] = [
    {
      label: 'Periode',
      value: period,
      onChange: onPeriodChange,
      options: periodOptions,
    },
    {
      label: 'Status',
      value: status,
      onChange: (value) => onStatusChange(value as KPIStatus | undefined),
      options: statusOptions,
    },
  ];

  return (
    <SearchFilter
      search={search}
      onSearchChange={onSearchChange}
      filters={filters}
      searchPlaceholder="Cari berdasarkan deskripsi..."
    />
  );
}
