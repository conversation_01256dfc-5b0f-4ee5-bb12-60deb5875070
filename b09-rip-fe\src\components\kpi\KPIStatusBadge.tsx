//path: b09-rip-fe/src/components/kpi/KPIStatusBadge.tsx
import { Badge } from '@/components/ui/badge';
import { KPIStatus } from '@/types/kpi';
import { Trophy } from 'lucide-react';

interface KPIStatusBadgeProps {
  status: KPIStatus;
  className?: string;
}

export function KPIStatusBadge({ status, className }: KPIStatusBadgeProps) {
  const getVariant = (status: KPIStatus) => {
    switch (status) {
      case 'not_started':
        return 'secondary';
      case 'in_progress':
        return 'info';
      case 'completed_below_target':
        return 'danger';
      case 'completed_on_target':
        return 'success';
      case 'completed_above_target':
        return 'premium';
      default:
        return 'secondary';
    }
  };

  const formatStatus = (status: string): string => {
    const statusDisplayMap: Record<string, string> = {
      not_started: 'Belum Dimulai',
      in_progress: 'Dalam Proses',
      completed_below_target: 'Selesai Di Bawah Target',
      completed_on_target: 'Selesai Sesuai Target',
      completed_above_target: 'Selesai Di Atas Target',
    };
    return statusDisplayMap[status] || status;
  };

  if (status === 'completed_above_target') {
    return (
      <Badge variant={getVariant(status)} className={className}>
        <Trophy className="h-3.5 w-3.5 mr-1 text-amber-500" />
        {formatStatus(status)}
      </Badge>
    );
  }

  return (
    <Badge variant={getVariant(status)} className={className}>
      {formatStatus(status)}
    </Badge>
  );
}

export default KPIStatusBadge;
