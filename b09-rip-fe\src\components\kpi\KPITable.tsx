// path: b09-rip-fe/src/components/kpi/KPITable.tsx
import React from 'react';
import { Eye } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { KPI } from '@/types/kpi';
import { formatCurrency } from '@/lib/utils/format';
import { KPIStatusBadge } from './KPIStatusBadge';
import { DataTable, SortDirection } from '@/components/ui/data-table';
import { useRouter } from 'next/navigation';

interface KPITableProps {
  kpis: KPI[];
  // Pagination props removed as they're not used in DataTable anymore
  // currentPage?: number;
  // itemsPerPage?: number;
  // onPageChange?: (page: number) => void;
  onViewDetail: (kpi: KPI) => void;
  loading?: boolean;
  sortField?: string;
  sortDirection?: SortDirection;
  onSort?: (field: string, direction: SortDirection) => void;
  hideEmployeeNameColumn?: boolean;
}

const KPITable: React.FC<KPITableProps> = ({
  kpis,
  // Pagination props removed as they're not used in DataTable anymore
  // currentPage = 1,
  // itemsPerPage = 10,
  // onPageChange,
  onViewDetail,
  loading = false,
  sortField,
  sortDirection,
  onSort,
  hideEmployeeNameColumn = false,
}) => {
  const router = useRouter();

  const handleEmployeeClick = (employeeId: string) => {
    router.push(`/employee/kpi?employeeId=${employeeId}`);
  };

  const columns = [];

  if (!hideEmployeeNameColumn) {
    columns.push({
      key: 'full_name',
      header: 'Nama Karyawan',
      sortable: false,
      render: (kpi: KPI) => (
        <button
          onClick={() => handleEmployeeClick(kpi.employee_id)}
          className="font-semibold text-gray-800 underline text-left hover:text-gray-900"
        >
          {kpi.full_name}
        </button>
      ),
    });
  }

  columns.push(
    {
      key: 'description',
      header: 'Deskripsi',
      render: (kpi: KPI) => (
        <div className="max-w-[200px] truncate">{kpi.description}</div>
      ),
    },
    {
      key: 'period',
      header: 'Periode',
      sortable: false,
      render: (kpi: KPI) => kpi.period,
    },
    {
      key: 'status',
      header: 'Status',
      sortable: false,
      render: (kpi: KPI) => <KPIStatusBadge status={kpi.status} />,
    },
    {
      key: 'bonus_received',
      header: 'Bonus',
      sortable: false,
      render: (kpi: KPI) =>
        kpi.bonus_received !== null ? formatCurrency(kpi.bonus_received) : '-',
    },
    {
      key: 'actions',
      header: 'Aksi',
      width: '120px',
      render: (kpi: KPI) => (
        <Button
          variant="outline"
          size="sm"
          onClick={(e) => {
            e.preventDefault();
            e.stopPropagation();
            onViewDetail(kpi);
          }}
          leftIcon={<Eye className="h-4 w-4" />}
        >
          Lihat Detail
        </Button>
      ),
    }
  );

  return (
    <DataTable
      columns={columns}
      data={kpis}
      keyExtractor={(kpi) => kpi.id}
      // Pagination props removed as they're not used in DataTable anymore
      // currentPage={currentPage}
      // itemsPerPage={itemsPerPage}
      // onPageChange={onPageChange}
      loading={loading}
      sortField={sortField}
      sortDirection={sortDirection}
      onSort={onSort}
      emptyStateMessage="Tidak ada data KPI."
      // showPagination prop removed as it's not used in DataTable anymore
      // showPagination={!!onPageChange}
    />
  );
};

export default KPITable;
