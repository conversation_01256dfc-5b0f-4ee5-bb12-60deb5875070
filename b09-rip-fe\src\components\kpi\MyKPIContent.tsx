// path: b09-rip-fe/src/components/kpi/MyKPIContent.tsx
'use client';

import React, { useMemo } from 'react';
import { useMyKPI } from '@/hooks/useMyKPI';
import KPITable from '@/components/kpi/KPITable';
import { PageTitle } from '@/components/ui/PageTitle';
import { SortDirection } from '@/components/ui/data-table';
import { Card, CardContent } from '@/components/ui/card';
import { Target } from 'lucide-react';

const MyKPIContent: React.FC = () => {
  const [sortField, setSortField] = React.useState<string | undefined>(
    undefined
  );
  const [sortDirection, setSortDirection] = React.useState<SortDirection>(null);

  const { kpis, loading, handleViewDetail } = useMyKPI();

  const handleSort = (field: string, direction: SortDirection) => {
    setSortField(field);
    setSortDirection(direction);
  };

  // Calculate the number of KPIs that achieved targets
  const achievedKpiStats = useMemo(() => {
    if (!kpis || kpis.length === 0) {
      return {
        onTarget: 0,
        aboveTarget: 0,
        total: 0,
        percentage: 0,
      };
    }

    const onTarget = kpis.filter(
      (kpi) => kpi.status === 'completed_on_target'
    ).length;
    const aboveTarget = kpis.filter(
      (kpi) => kpi.status === 'completed_above_target'
    ).length;
    const total = onTarget + aboveTarget;
    const percentage = Math.round((total / kpis.length) * 100);

    return {
      onTarget,
      aboveTarget,
      total,
      percentage,
    };
  }, [kpis]);

  return (
    <div className="container mx-auto py-6 px-6">
      <div className="flex justify-between items-center mb-6">
        <PageTitle title="KPI Saya" subtitle="Lihat dan pantau KPI disini" />
      </div>

      {/* Stats Card */}
      <div className="mb-6">
        <Card className="bg-white">
          <CardContent className="p-6">
            <div className="flex flex-col md:flex-row gap-4 items-center">
              <div className="flex items-center gap-3 bg-green-50 p-4 rounded-lg">
                <Target className="h-8 w-8 text-green-600" />
                <div>
                  <h3 className="font-semibold text-lg text-green-700">
                    Pencapaian Target
                  </h3>
                  <p className="text-sm text-green-600">
                    {loading ? (
                      <span className="h-4 w-20 bg-gray-200 animate-pulse rounded"></span>
                    ) : (
                      `${achievedKpiStats.total} dari ${kpis.length} KPI (${achievedKpiStats.percentage}%)`
                    )}
                  </p>
                </div>
              </div>

              <div className="flex flex-col md:flex-row gap-4">
                <div className="flex items-center gap-2">
                  <div className="h-3 w-3 rounded-full bg-yellow-400"></div>
                  <span className="text-sm">
                    Sesuai Target: {achievedKpiStats.onTarget} KPI
                  </span>
                </div>
                <div className="flex items-center gap-2">
                  <div className="h-3 w-3 rounded-full bg-green-500"></div>
                  <span className="text-sm">
                    Di Atas Target: {achievedKpiStats.aboveTarget} KPI
                  </span>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      <div className="bg-white rounded-lg shadow p-6">
        <div className="overflow-x-auto">
          <KPITable
            kpis={kpis}
            onViewDetail={handleViewDetail}
            loading={loading}
            sortField={sortField}
            sortDirection={sortDirection}
            onSort={handleSort}
            hideEmployeeNameColumn={true}
          />
        </div>
      </div>
    </div>
  );
};

export default MyKPIContent;
