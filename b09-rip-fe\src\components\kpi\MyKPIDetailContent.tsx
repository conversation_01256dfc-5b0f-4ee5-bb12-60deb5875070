//path: b09-rip-fe/src/components/kpi/MyKPIDetailContent.tsx
'use client';

import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { AlertCircle } from 'lucide-react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Separator } from '@/components/ui/separator';
import { useMyKPI } from '@/hooks/useMyKPI';
import { formatDate } from '@/lib/utils/date';
import { formatCurrency } from '@/lib/utils/format';
import { PageTitle } from '@/components/ui/PageTitle';
import { BackButton } from '@/components/ui/BackButton';
import { KPIStatusBadge } from './KPIStatusBadge';
import { KPI } from '@/types/kpi';

interface MyKPIDetailContentProps {
  id: string;
}

const MyKPIDetailContent: React.FC<MyKPIDetailContentProps> = ({ id }) => {
  const router = useRouter();
  const { kpis, loading } = useMyKPI();
  const [kpi, setKpi] = useState<KPI | null>(null);
  const [notFound, setNotFound] = useState(false);

  // Find the specific KPI by ID from the list of KPIs
  useEffect(() => {
    if (!loading && kpis.length > 0) {
      const foundKpi = kpis.find((k) => k.id === id);
      if (foundKpi) {
        setKpi(foundKpi);
      } else {
        setNotFound(true);
      }
    }
  }, [id, kpis, loading]);

  return (
    <div className="max-w-5xl mx-auto">
      <div className="mb-6 flex items-start">
        <div className="flex items-center gap-4">
          <BackButton onClick={() => router.push('/employee/mykpi')} />
          <PageTitle title="Detail KPI" subtitle="Lihat detail KPI Anda." />
        </div>
      </div>

      {loading ? (
        <div className="bg-white rounded-lg shadow p-6 text-center py-8">
          Loading...
        </div>
      ) : notFound || !kpi ? (
        <div className="bg-white rounded-lg shadow p-6 text-center py-8">
          <AlertCircle className="h-8 w-8 text-red-500 mx-auto mb-4" />
          <h2 className="text-xl font-medium">KPI tidak ditemukan</h2>
          <p className="text-gray-500 mt-2">
            KPI dengan ID ini tidak ditemukan atau telah dihapus.
          </p>
          <Button
            className="mt-4"
            onClick={() => router.push('/employee/mykpi')}
          >
            Kembali ke Daftar KPI
          </Button>
        </div>
      ) : (
        <>
          <Card className="mb-6">
            <CardHeader>
              <div className="flex justify-between items-center">
                <div>
                  <CardTitle className="text-xl">{kpi.full_name}</CardTitle>
                </div>
                <div>
                  <KPIStatusBadge status={kpi.status} />
                </div>
              </div>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <h3 className="text-sm font-medium text-gray-500">Periode</h3>
                  <p className="mt-1">{kpi.period}</p>
                </div>
                <div>
                  <h3 className="text-sm font-medium text-gray-500">Bonus</h3>
                  <p className="mt-1">
                    {kpi.bonus_received
                      ? formatCurrency(kpi.bonus_received)
                      : '-'}
                  </p>
                </div>
              </div>

              <Separator />

              <div>
                <h3 className="text-sm font-medium text-gray-500 mb-2">
                  Deskripsi KPI
                </h3>
                <p className="whitespace-pre-wrap">{kpi.description}</p>
              </div>

              <div>
                <h3 className="text-sm font-medium text-gray-500 mb-2">
                  Target
                </h3>
                <p className="whitespace-pre-wrap">{kpi.target}</p>
              </div>

              {kpi.additional_notes && (
                <div>
                  <h3 className="text-sm font-medium text-gray-500 mb-2">
                    Catatan Tambahan
                  </h3>
                  <p className="whitespace-pre-wrap">{kpi.additional_notes}</p>
                </div>
              )}
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle className="text-lg">Informasi Tambahan</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <h3 className="text-sm font-medium text-gray-500">
                    Dibuat Pada
                  </h3>
                  <p className="mt-1">{formatDate(kpi.created_at)}</p>
                </div>
                {kpi.updated_at && (
                  <div>
                    <h3 className="text-sm font-medium text-gray-500">
                      Diperbarui Pada
                    </h3>
                    <p className="mt-1">{formatDate(kpi.updated_at)}</p>
                  </div>
                )}
              </div>
            </CardContent>
          </Card>
        </>
      )}
    </div>
  );
};

export default MyKPIDetailContent;
