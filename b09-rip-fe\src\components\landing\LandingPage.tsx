'use client';

import { useState, useEffect, useRef } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { Footer } from '@/components/ui/footer';
import LoginModal from '@/components/auth/LoginModal';
import RegisterModal from '@/components/auth/RegisterModal';
import Image from 'next/image';

// Counter component for animated numbers
function AnimatedCounter({ end, duration = 2000, suffix = '' }: { end: number; duration?: number; suffix?: string }) {
  const [count, setCount] = useState(0);
  const [isVisible, setIsVisible] = useState(false);
  const counterRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    const observer = new IntersectionObserver(
      ([entry]) => {
        if (entry.isIntersecting && !isVisible) {
          setIsVisible(true);
        }
      },
      { threshold: 0.1 }
    );

    if (counterRef.current) {
      observer.observe(counterRef.current);
    }

    return () => observer.disconnect();
  }, [isVisible]);

  useEffect(() => {
    if (!isVisible) return;

    let startTime: number;
    let animationFrame: number;

    const animate = (timestamp: number) => {
      if (!startTime) startTime = timestamp;
      const progress = Math.min((timestamp - startTime) / duration, 1);

      setCount(Math.floor(progress * end));

      if (progress < 1) {
        animationFrame = requestAnimationFrame(animate);
      }
    };

    animationFrame = requestAnimationFrame(animate);

    return () => {
      if (animationFrame) {
        cancelAnimationFrame(animationFrame);
      }
    };
  }, [isVisible, end, duration]);

  return (
    <div ref={counterRef} className="text-4xl font-bold text-[#B78F38] mb-3 group-hover:scale-110 transition-transform">
      {count}{suffix}
    </div>
  );
}



export default function LandingPage() {
  const [isLoginOpen, setIsLoginOpen] = useState(false);
  const [isRegisterOpen, setIsRegisterOpen] = useState(false);

  return (
    <div className="min-h-screen bg-white">
      {/* Hero Section */}
      <section className="relative overflow-hidden bg-gradient-to-br from-gray-50 to-white min-h-screen flex items-center">
        <div className="absolute inset-0 bg-gradient-to-r from-[#B78F38]/5 to-transparent"></div>

        {/* Floating Animation Elements */}
        <div className="absolute inset-0 overflow-hidden">
          <div className="absolute top-20 left-10 w-20 h-20 bg-[#B78F38]/10 rounded-full animate-pulse"></div>
          <div className="absolute top-40 right-20 w-16 h-16 bg-[#B78F38]/20 rounded-full animate-bounce delay-1000"></div>
          <div className="absolute bottom-40 left-20 w-12 h-12 bg-[#B78F38]/15 rounded-full animate-pulse delay-500"></div>
        </div>

        <div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-24">
          <div className="grid lg:grid-cols-2 gap-20 items-center">
            {/* Left Content */}
            <div className="text-left space-y-8">

              <div className="space-y-6">
                <h1 className="text-5xl md:text-6xl lg:text-7xl font-bold text-gray-900 leading-tight animate-slide-up">
                  <span className="text-[#B78F38] font-bold">Kasuat</span>
                  <br />
                  <span className="text-gray-700 text-3xl md:text-4xl lg:text-5xl font-bold">Information Systems</span>
                </h1>

                <p className="text-m md:text-2xl text-gray-600 leading-relaxed animate-slide-up delay-200">
                  Portal terpadu untuk tim, manajemen, dan klien untuk meningkatkan efisiensi dalam satu platform terintegrasi.
                </p>
              </div>

              <div className="flex flex-col sm:flex-row gap-4 pt-4 animate-slide-up delay-400">
                <Button
                  size="lg"
                  className="bg-[#B78F38] hover:bg-[#A67D32] text-white px-10 py-4 text-lg font-semibold shadow-lg hover:shadow-xl transition-all transform hover:scale-105"
                  onClick={() => setIsLoginOpen(true)}
                >
                  Login
                </Button>
                <Button
                  size="lg"
                  variant="outline"
                  className="border-2 border-[#B78F38] text-[#B78F38] hover:bg-[#B78F38] hover:text-white px-10 py-4 text-lg font-semibold shadow-lg hover:shadow-xl transition-all transform hover:scale-105"
                  onClick={() => setIsRegisterOpen(true)}
                >
                  Registrasi
                </Button>
              </div>
            </div>

            {/* Right Image - Office Illustration */}
            <div className="relative animate-fade-in delay-600">
              {/* Office SVG Illustration */}
              <div className="aspect-square flex items-center justify-center">
                <Image
                  src="/assets/illustrations/landing-1.svg"
                  alt="Office Illustration"
                  width={500}
                  height={500}
                  className="w-full h-auto max-w-lg"
                  priority
                />
              </div>

              {/* Decorative Elements */}
              <div className="absolute -top-4 -right-4 w-16 h-16 bg-[#B78F38]/20 rounded-full animate-pulse"></div>
              <div className="absolute -bottom-4 -left-4 w-12 h-12 bg-[#B78F38]/30 rounded-full animate-bounce"></div>
            </div>
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section className="py-24 bg-gradient-to-br from-[#B78F38] to-[#A67D32] relative overflow-hidden">
        {/* Background Pattern */}
        <div className="absolute inset-0 opacity-10">
          <div className="absolute top-10 left-10 w-32 h-32 border border-white/20 rounded-full"></div>
          <div className="absolute top-40 right-20 w-24 h-24 border border-white/20 rounded-full"></div>
          <div className="absolute bottom-20 left-1/4 w-40 h-40 border border-white/20 rounded-full"></div>
        </div>

        <div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-20">
            <h2 className="text-4xl md:text-5xl font-bold text-white mb-6">
              Fitur Utama
            </h2>
            <p className="text-xl text-white/90 max-w-3xl mx-auto leading-relaxed">
              Akses lengkap untuk tim, manajemen, dan klien dalam mengelola proyek konsultasi
            </p>
          </div>

          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-10">
            <Card className="bg-white/95 backdrop-blur border-white/20 hover:bg-white hover:shadow-2xl transition-all duration-300 group transform hover:scale-105">
              <CardContent className="p-8">
                <div className="w-16 h-16 bg-[#B78F38]/10 rounded-xl flex items-center justify-center mb-6 group-hover:bg-[#B78F38]/20 transition-colors">
                  <svg className="w-8 h-8 text-[#B78F38]" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" />
                  </svg>
                </div>
                <h3 className="text-2xl font-semibold text-gray-900 mb-4">Manajemen Proyek Konsultasi</h3>
                <p className="text-gray-600 leading-relaxed">
                  Kelola proyek konsultasi klien dari proposal hingga deliverable dengan tracking progress real-time.
                </p>
              </CardContent>
            </Card>

            <Card className="bg-white/95 backdrop-blur border-white/20 hover:bg-white hover:shadow-2xl transition-all duration-300 group transform hover:scale-105">
              <CardContent className="p-8">
                <div className="w-16 h-16 bg-[#B78F38]/10 rounded-xl flex items-center justify-center mb-6 group-hover:bg-[#B78F38]/20 transition-colors">
                  <svg className="w-8 h-8 text-[#B78F38]" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
                  </svg>
                </div>
                <h3 className="text-2xl font-semibold text-gray-900 mb-4">Manajemen SDM Internal</h3>
                <p className="text-gray-600 leading-relaxed">
                  Sistem absensi, evaluasi KPI, dan laporan mingguan untuk tim internal Kasuat.
                </p>
              </CardContent>
            </Card>

            <Card className="bg-white/95 backdrop-blur border-white/20 hover:bg-white hover:shadow-2xl transition-all duration-300 group transform hover:scale-105">
              <CardContent className="p-8">
                <div className="w-16 h-16 bg-[#B78F38]/10 rounded-xl flex items-center justify-center mb-6 group-hover:bg-[#B78F38]/20 transition-colors">
                  <svg className="w-8 h-8 text-[#B78F38]" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
                  </svg>
                </div>
                <h3 className="text-2xl font-semibold text-gray-900 mb-4">Dashboard & Analytics</h3>
                <p className="text-gray-600 leading-relaxed">
                  Monitoring performa proyek, analisis produktivitas tim, dan laporan eksekutif.
                </p>
              </CardContent>
            </Card>

            <Card className="bg-white/95 backdrop-blur border-white/20 hover:bg-white hover:shadow-2xl transition-all duration-300 group transform hover:scale-105">
              <CardContent className="p-8">
                <div className="w-16 h-16 bg-[#B78F38]/10 rounded-xl flex items-center justify-center mb-6 group-hover:bg-[#B78F38]/20 transition-colors">
                  <svg className="w-8 h-8 text-[#B78F38]" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                  </svg>
                </div>
                <h3 className="text-2xl font-semibold text-gray-900 mb-4">Manajemen Finansial</h3>
                <p className="text-gray-600 leading-relaxed">
                  Tracking invoice klien, budget proyek, dan laporan keuangan untuk operasional konsultasi.
                </p>
              </CardContent>
            </Card>

            <Card className="bg-white/95 backdrop-blur border-white/20 hover:bg-white hover:shadow-2xl transition-all duration-300 group transform hover:scale-105">
              <CardContent className="p-8">
                <div className="w-16 h-16 bg-[#B78F38]/10 rounded-xl flex items-center justify-center mb-6 group-hover:bg-[#B78F38]/20 transition-colors">
                  <svg className="w-8 h-8 text-[#B78F38]" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4" />
                  </svg>
                </div>
                <h3 className="text-2xl font-semibold text-gray-900 mb-4">Database Klien</h3>
                <p className="text-gray-600 leading-relaxed">
                  Manajemen data klien, organisasi, dan riwayat proyek untuk layanan konsultasi yang optimal.
                </p>
              </CardContent>
            </Card>

            <Card className="bg-white/95 backdrop-blur border-white/20 hover:bg-white hover:shadow-2xl transition-all duration-300 group transform hover:scale-105">
              <CardContent className="p-8">
                <div className="w-16 h-16 bg-[#B78F38]/10 rounded-xl flex items-center justify-center mb-6 group-hover:bg-[#B78F38]/20 transition-colors">
                  <svg className="w-8 h-8 text-[#B78F38]" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z" />
                  </svg>
                </div>
                <h3 className="text-2xl font-semibold text-gray-900 mb-4">Keamanan & Akses</h3>
                <p className="text-gray-600 leading-relaxed">
                  Kontrol akses untuk data sensitif perusahaan dan informasi klien.
                </p>
              </CardContent>
            </Card>
          </div>
        </div>
      </section>

      {/* About Section */}
      <section className="py-24 bg-white relative overflow-hidden">
        {/* Animated Background Elements */}
        <div className="absolute inset-0 opacity-5">
          <div className="absolute top-20 left-10 w-64 h-64 bg-[#B78F38] rounded-full animate-pulse"></div>
          <div className="absolute bottom-20 right-10 w-48 h-48 bg-[#B78F38] rounded-full animate-pulse delay-1000"></div>
        </div>

        <div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid lg:grid-cols-2 gap-16 items-center">
            <div className="animate-slide-up">
              <div className="inline-block bg-[#B78F38]/10 text-[#B78F38] px-4 py-2 rounded-full text-sm font-medium mb-6">
                PT Halaman Tumbuh Bersama
              </div>
              <h2 className="text-4xl md:text-5xl font-bold text-gray-900 mb-8">
                Tentang <span className="text-[#B78F38]">Kasuat</span>
              </h2>
              <p className="text-xl text-gray-600 mb-8 leading-relaxed">
                Kasuat adalah perusahaan konsultasi terpercaya yang telah melayani berbagai klien
                dengan solusi bisnis yang inovatif dan berkelanjutan. Kami memiliki fokus dalam
                memberikan layanan konsultasi strategis untuk berbagai industri.
              </p>
              <p className="text-xl text-gray-600 mb-12 leading-relaxed">
                Dengan tim profesional berpengalaman dan pendekatan yang terstruktur,
                kami berkomitmen memberikan hasil terbaik untuk setiap proyek yang dipercayakan kepada kami.
              </p>
              <div className="grid grid-cols-3 gap-8">
                <div className="text-center group">
                  <AnimatedCounter end={25} suffix="+" />
                  <div className="text-gray-600 font-medium">Proyek Selesai</div>
                </div>
                <div className="text-center group">
                  <AnimatedCounter end={30} suffix="+" />
                  <div className="text-gray-600 font-medium">Klien Aktif</div>
                </div>
                <div className="text-center group">
                  <AnimatedCounter end={95} suffix="%" />
                  <div className="text-gray-600 font-medium">Tingkat Kepuasan</div>
                </div>
              </div>
            </div>
            <div className="relative">
                <Image
                  src="/assets/illustrations/landing-2.svg"
                  alt="Office Illustration"
                  width={500}
                  height={500}
                  className="w-full h-auto max-w-lg"
                  priority
                />
            </div>
          </div>
        </div>
      </section>

      {/* Benefits Section - Gold Background */}
      <section className="py-24 bg-gradient-to-br from-[#B78F38] to-[#A67D32] relative overflow-hidden">
        {/* Background Pattern */}
        <div className="absolute inset-0 opacity-10">
          <div className="absolute top-20 right-20 w-96 h-96 border-2 border-white/20 rounded-full animate-spin-slow"></div>
          <div className="absolute bottom-20 left-20 w-64 h-64 border border-white/20 rounded-full animate-pulse"></div>
        </div>

        <div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-4xl md:text-5xl font-bold text-white mb-6">
              Keunggulan Sistem
            </h2>
            <p className="text-xl text-white/90 max-w-3xl mx-auto leading-relaxed">
              Menyediakan solusi lengkap untuk kebutuhan operasional perusahaan
            </p>
          </div>

          <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8">
            <div className="text-center group">
              <div className="w-20 h-20 bg-white/20 rounded-full flex items-center justify-center mx-auto mb-6 group-hover:scale-110 transition-transform">
                <svg className="w-10 h-10 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
                </svg>
              </div>
              <h3 className="text-xl font-semibold text-white mb-3">Efisiensi Tinggi</h3>
              <p className="text-white/80">Otomatisasi proses bisnis untuk produktivitas maksimal</p>
            </div>

            <div className="text-center group">
              <div className="w-20 h-20 bg-white/20 rounded-full flex items-center justify-center mx-auto mb-6 group-hover:scale-110 transition-transform">
                <svg className="w-10 h-10 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
              </div>
              <h3 className="text-xl font-semibold text-white mb-3">Terintegrasi</h3>
              <p className="text-white/80">Semua modul terhubung dalam satu platform</p>
            </div>

            <div className="text-center group">
              <div className="w-20 h-20 bg-white/20 rounded-full flex items-center justify-center mx-auto mb-6 group-hover:scale-110 transition-transform">
                <svg className="w-10 h-10 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z" />
                </svg>
              </div>
              <h3 className="text-xl font-semibold text-white mb-3">Aman</h3>
              <p className="text-white/80">Keamanan data tingkat enterprise</p>
            </div>

            <div className="text-center group">
              <div className="w-20 h-20 bg-white/20 rounded-full flex items-center justify-center mx-auto mb-6 group-hover:scale-110 transition-transform">
                <svg className="w-10 h-10 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 12l3-3 3 3 4-4M8 21l4-4 4 4M3 4h18M4 4h16v12a1 1 0 01-1 1H5a1 1 0 01-1-1V4z" />
                </svg>
              </div>
              <h3 className="text-xl font-semibold text-white mb-3">Real-time</h3>
              <p className="text-white/80">Monitoring dan laporan secara langsung</p>
            </div>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-24 bg-gray-100 relative overflow-hidden">
        {/* Animated Elements */}
        <div className="absolute inset-0 opacity-10">
          <div className="absolute top-10 left-1/4 w-32 h-32 bg-[#B78F38] rounded-full animate-bounce"></div>
          <div className="absolute bottom-10 right-1/4 w-24 h-24 bg-[#B78F38] rounded-full animate-pulse"></div>
        </div>

        <div className="relative max-w-5xl mx-auto text-center px-4 sm:px-6 lg:px-8">
          <h2 className="text-4xl md:text-5xl font-bold text-gray-900 mb-8">
            Bergabunglah menjadi bagian kami!
          </h2>
          <p className="text-xl text-gray-600 mb-12 max-w-3xl mx-auto leading-relaxed">
            Kelola proyek konsultasi dan informasi penting dalam satu platform.
          </p>
          <div className="flex flex-col sm:flex-row gap-6 justify-center">
            <Button
              size="lg"
              className="bg-[#B78F38] hover:bg-[#A67D32] text-white px-12 py-6 text-xl font-semibold shadow-lg hover:shadow-xl transition-all transform hover:scale-105"
              onClick={() => setIsLoginOpen(true)}
            >
              Login
            </Button>
            <Button
              size="lg"
              variant="outline"
              className="border-[#B78F38] text-[#B78F38] hover:bg-[#B78F38] hover:text-white px-12 py-6 text-xl font-semibold shadow-lg hover:shadow-xl transition-all transform hover:scale-105"
              onClick={() => setIsRegisterOpen(true)}
            >
              Registrasi
            </Button>
          </div>
        </div>
      </section>

      {/* Footer */}
      <Footer />

      {/* Modals */}
      <LoginModal
        isOpen={isLoginOpen}
        onClose={() => setIsLoginOpen(false)}
        onOpenRegister={() => {
          setIsLoginOpen(false);
          setIsRegisterOpen(true);
        }}
      />
      <RegisterModal
        isOpen={isRegisterOpen}
        onClose={() => setIsRegisterOpen(false)}
        onOpenLogin={() => {
          setIsRegisterOpen(false);
          setIsLoginOpen(true);
        }}
      />
    </div>
  );
}
