// ClientInformationCard.tsx

import React from 'react';
import { Organization, UpdateOrganizationRequest } from '@/types/organization';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Pencil, Save, X, Loader2 } from 'lucide-react';
import { ClientTypeBadge } from './ClientTypeBadge';

interface ClientInformationCardProps {
  organization: Organization;
  isEditing: boolean;
  isUpdating: boolean;
  onEdit: () => void;
  onCancel: () => void;
  onSave: (data: UpdateOrganizationRequest) => void;
}

// List of common client types
const clientTypes = [
  'Individual',
  'Corporate',
  'Enterprise',
  'Small Business',
  'Non-Profit',
  'Healthcare',
  'Educational',
  'Government',
  'Retail',
  'Other',
];

const ClientInformationCard: React.FC<ClientInformationCardProps> = ({
  organization,
  isEditing,
  isUpdating,
  onEdit,
  onCancel,
  onSave,
}) => {
  const [formData, setFormData] = React.useState<UpdateOrganizationRequest>({
    name: organization.name,
    phone: organization.phone,
    address: organization.address,
    client_type: organization.client_type,
    notes: organization.notes,
  });

  React.useEffect(() => {
    // Reset form data when organization changes or when exiting edit mode
    if (!isEditing) {
      setFormData({
        name: organization.name,
        phone: organization.phone,
        address: organization.address,
        client_type: organization.client_type,
        notes: organization.notes,
      });
    }
  }, [organization, isEditing]);

  const handleChange = (
    e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>
  ) => {
    const { name, value } = e.target;
    setFormData((prev) => ({ ...prev, [name]: value }));
  };

  const handleClientTypeChange = (value: string) => {
    setFormData((prev) => ({ ...prev, client_type: value }));
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    onSave(formData);
  };

  return (
    <Card className="w-full mb-6">
      <CardHeader className="flex flex-row items-center justify-between pb-2">
        <CardTitle className="text-xl">Informasi Klien</CardTitle>
        <div>
          {isEditing ? (
            <div className="flex gap-2">
              <Button
                variant="outline"
                size="sm"
                onClick={onCancel}
                disabled={isUpdating}
                leftIcon={<X className="h-4 w-4" />}
              >
                Batal
              </Button>
              <Button
                type="submit"
                size="sm"
                onClick={handleSubmit}
                disabled={isUpdating}
                leftIcon={
                  isUpdating ? (
                    <Loader2 className="h-4 w-4 animate-spin" />
                  ) : (
                    <Save className="h-4 w-4" />
                  )
                }
              >
                {isUpdating ? 'Menyimpan...' : 'Simpan'}
              </Button>
            </div>
          ) : (
            <Button
              variant="outline"
              size="sm"
              onClick={onEdit}
              leftIcon={<Pencil className="h-4 w-4" />}
            >
              Ubah Informasi
            </Button>
          )}
        </div>
      </CardHeader>
      <CardContent>
        <form className="space-y-4" onSubmit={handleSubmit}>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="name">Nama Klien</Label>
              {isEditing ? (
                <Input
                  id="name"
                  name="name"
                  value={formData.name}
                  onChange={handleChange}
                  disabled={isUpdating}
                  required
                />
              ) : (
                <p className="py-2">{organization.name}</p>
              )}
            </div>

            <div className="space-y-2">
              <Label htmlFor="phone">No. Telepon</Label>
              {isEditing ? (
                <Input
                  id="phone"
                  name="phone"
                  value={formData.phone}
                  onChange={handleChange}
                  disabled={isUpdating}
                  required
                />
              ) : (
                <p className="py-2">{organization.phone}</p>
              )}
            </div>

            <div className="space-y-2">
              <Label htmlFor="client_type">Jenis Klien</Label>
              {isEditing ? (
                <Select
                  value={formData.client_type}
                  onValueChange={handleClientTypeChange}
                  disabled={isUpdating}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Pilih Jenis Klien" />
                  </SelectTrigger>
                  <SelectContent>
                    {clientTypes.map((type) => (
                      <SelectItem key={type} value={type}>
                        {type}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              ) : (
                <div className="py-2">
                  <ClientTypeBadge type={organization.client_type} />
                </div>
              )}
            </div>
          </div>

          <div className="space-y-2">
            <Label htmlFor="address">Alamat</Label>
            {isEditing ? (
              <Input
                id="address"
                name="address"
                value={formData.address}
                onChange={handleChange}
                disabled={isUpdating}
                required
              />
            ) : (
              <p className="py-2">{organization.address}</p>
            )}
          </div>

          <div className="space-y-2">
            <Label htmlFor="notes">Catatan</Label>
            {isEditing ? (
              <Textarea
                id="notes"
                name="notes"
                value={formData.notes || ''}
                onChange={handleChange}
                disabled={isUpdating}
                rows={4}
              />
            ) : (
              <p className="py-2">{organization.notes || '-'}</p>
            )}
          </div>
        </form>
      </CardContent>
    </Card>
  );
};

export default ClientInformationCard;
