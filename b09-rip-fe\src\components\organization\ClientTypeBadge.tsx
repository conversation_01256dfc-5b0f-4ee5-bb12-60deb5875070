// ClientTypeBadge.tsx
import React from 'react';
import { Badge } from '@/components/ui/badge';
import { cn } from '@/lib/utils';

interface ClientTypeBadgeProps {
  type: string;
  className?: string;
}

export function ClientTypeBadge({ type, className }: ClientTypeBadgeProps) {
  const getVariant = (
    type: string
  ):
    | 'default'
    | 'secondary'
    | 'outline'
    | 'destructive'
    | 'success'
    | 'warning'
    | 'info' => {
    const lowerType = type.toLowerCase();

    switch (lowerType) {
      case 'individual':
        return 'secondary';
      case 'corporate':
        return 'default';
      case 'enterprise':
        return 'info';
      case 'small business':
      case 'sme':
        return 'success';
      case 'government':
        return 'warning';
      case 'non-profit':
        return 'outline';
      case 'healthcare':
        return 'info';
      case 'educational':
        return 'success';
      case 'retail':
        return 'secondary';
      default:
        return 'outline';
    }
  };

  return (
    <Badge variant={getVariant(type)} className={cn('font-normal', className)}>
      {type}
    </Badge>
  );
}

export default ClientTypeBadge;
