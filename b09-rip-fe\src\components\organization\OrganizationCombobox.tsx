//OrganizationCombobox.tsx
'use client';

import * as React from 'react';
import { Check, ChevronsUpDown } from 'lucide-react';
import { cn } from '@/lib/utils';
import { Button } from '@/components/ui/button';
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
} from '@/components/ui/command';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover';
import { Organization } from '@/types/organization';
import { organizationApi } from '@/lib/api/organization';

interface OrganizationComboboxProps {
  value: string; // organization_id
  onSelect: (organizationId: string, name?: string) => void;
  placeholder?: string;
  className?: string;
  disabled?: boolean;
  organizations?: Organization[];
}

export function OrganizationCombobox({
  value,
  onSelect,
  placeholder = 'Pilih organisasi...',
  className,
  disabled = false,
  organizations: passedOrganizations,
}: OrganizationComboboxProps) {
  const [open, setOpen] = React.useState(false);
  const [organizations, setOrganizations] = React.useState<Organization[]>([]);
  const [loading, setLoading] = React.useState(false);
  const [searchQuery, setSearchQuery] = React.useState('');

  // Load organizations on component mount
  React.useEffect(() => {
    // If organizations are passed, use them
    if (passedOrganizations) {
      setOrganizations(passedOrganizations);
      return;
    }

    // Don't fetch if disabled (optimization)
    if (disabled && value) return;

    const fetchOrganizations = async () => {
      setLoading(true);
      try {
        // Request with a large pageSize to get all organizations at once
        const response = await organizationApi.getOrganizations({
          page: 1,
          pageSize: 1000, // Request a large number to get all organizations
          search: searchQuery,
        });

        if (response.success && response.data) {
          setOrganizations(response.data.items || []);
        }
      } catch (error) {
        console.error('Error fetching organizations:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchOrganizations();
  }, [searchQuery, disabled, value, passedOrganizations]);

  // Handle search input change with debounce
  const handleSearchChange = React.useCallback((value: string) => {
    // Use a timeout to debounce the search
    const timeoutId = setTimeout(() => {
      setSearchQuery(value);
    }, 300);

    return () => clearTimeout(timeoutId);
  }, []);

  // Find the selected organization label
  const selectedOrganization = organizations.find((org) => org.id === value);
  const displayValue = selectedOrganization
    ? selectedOrganization.name
    : placeholder;

  // Don't allow opening if disabled
  const handleOpenChange = (open: boolean) => {
    if (!disabled) {
      setOpen(open);
    }
  };

  return (
    <Popover open={open} onOpenChange={handleOpenChange}>
      <PopoverTrigger asChild>
        <Button
          variant="outline"
          role="combobox"
          aria-expanded={open}
          className={cn(
            'w-full justify-between border-input text-foreground',
            className
          )}
        >
          <span className="truncate">{displayValue}</span>
          <ChevronsUpDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
        </Button>
      </PopoverTrigger>
      {!disabled && (
        <PopoverContent className="w-full p-0">
          <Command>
            <CommandInput
              placeholder="Cari organisasi..."
              className="h-9"
              onValueChange={handleSearchChange}
            />
            <CommandList>
              {loading ? (
                <CommandEmpty>Loading...</CommandEmpty>
              ) : organizations.length === 0 ? (
                <CommandEmpty>Organisasi tidak ditemukan.</CommandEmpty>
              ) : (
                <CommandGroup>
                  {organizations.map((organization) => (
                    <CommandItem
                      key={organization.id}
                      value={organization.name}
                      onSelect={() => {
                        onSelect(organization.id, organization.name);
                        setOpen(false);
                      }}
                    >
                      <span>{organization.name}</span>
                      <Check
                        className={cn(
                          'ml-auto h-4 w-4',
                          value === organization.id
                            ? 'opacity-100'
                            : 'opacity-0'
                        )}
                      />
                    </CommandItem>
                  ))}
                </CommandGroup>
              )}
            </CommandList>
          </Command>
        </PopoverContent>
      )}
    </Popover>
  );
}
