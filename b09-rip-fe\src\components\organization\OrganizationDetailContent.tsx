// OrganizationDetailContent.tsx
'use client';

import React, { useState } from 'react';
import { Trash2 } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { useOrganizationDetail } from '@/hooks/useOrganizationDetail';
import ClientInformationCard from '@/components/organization/ClientInformationCard';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { PageTitle } from '@/components/ui/PageTitle';
import { BackButton } from '@/components/ui/BackButton';
import { useRouter } from 'next/navigation';

interface OrganizationDetailContentProps {
  id: string;
}

const OrganizationDetailContent: React.FC<OrganizationDetailContentProps> = ({
  id,
}) => {
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);

  const {
    organization,
    loading,
    updating,
    isEditing,
    deleting,
    handleEditToggle,
    handleSaveChanges,
    handleCancelEdit,
    handleDelete,
  } = useOrganizationDetail(id);

  const router = useRouter();

  const onDeleteConfirm = () => {
    handleDelete();
    setDeleteDialogOpen(false);
  };

  return (
    <div>
      <div className="flex justify-between items-center mb-6">
        <div className="flex items-center gap-4">
          <BackButton onClick={() => router.push('/client')} />
          <PageTitle
            title="Informasi Klien"
            subtitle="Lihat dan kelola informasi klien"
          />
        </div>

        {organization && (
          <Button
            variant="destructive"
            size="sm"
            onClick={() => setDeleteDialogOpen(true)}
            disabled={isEditing || deleting || updating}
            leftIcon={<Trash2 className="h-4 w-4" />}
          >
            Hapus Klien
          </Button>
        )}
      </div>

      {loading ? (
        <div className="flex justify-center items-center py-20">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary mx-auto mb-4"></div>
            <p className="text-muted-foreground">Loading...</p>
          </div>
        </div>
      ) : organization ? (
        <div>
          <ClientInformationCard
            organization={organization}
            isEditing={isEditing}
            isUpdating={updating}
            onEdit={handleEditToggle}
            onCancel={handleCancelEdit}
            onSave={handleSaveChanges}
          />
        </div>
      ) : (
        <div className="text-center py-10">
          <p className="text-muted-foreground">
            Organization not found or an error occurred.
          </p>
        </div>
      )}

      {/* Delete Confirmation Dialog */}
      <Dialog open={deleteDialogOpen} onOpenChange={setDeleteDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Konfirmasi Hapus Klien</DialogTitle>
            <DialogDescription>
              Apakah Anda yakin ingin menghapus klien ini? Tindakan ini tidak
              dapat dibatalkan.
            </DialogDescription>
          </DialogHeader>
          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => setDeleteDialogOpen(false)}
              disabled={deleting}
            >
              Batal
            </Button>
            <Button
              variant="destructive"
              onClick={onDeleteConfirm}
              disabled={deleting}
            >
              {deleting ? 'Menghapus...' : 'Ya, Hapus'}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default OrganizationDetailContent;
