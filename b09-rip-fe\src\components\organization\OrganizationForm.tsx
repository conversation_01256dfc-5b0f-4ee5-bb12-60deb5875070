//OrganizationForm.tsx
'use client';

import React, { useState } from 'react';
import { useRouter } from 'next/navigation';
import { toast } from 'sonner';
import { <PERSON><PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
  CardFooter,
} from '@/components/ui/card';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Save, Loader2 } from 'lucide-react';
import { BackButton } from '@/components/ui/BackButton';
import { CreateOrganizationRequest } from '@/types/organization';
import { organizationApi } from '@/lib/api/organization';
import { PageTitle } from '@/components/ui/PageTitle';

// List of common client types
const clientTypes = [
  'Individual',
  'Corporate',
  'Enterprise',
  'Small Business',
  'Non-Profit',
  'Healthcare',
  'Educational',
  'Government',
  'Retail',
  'Other',
];

const OrganizationForm: React.FC = () => {
  const router = useRouter();
  const [submitting, setSubmitting] = useState(false);
  const [formData, setFormData] = useState<CreateOrganizationRequest>({
    name: '',
    phone: '',
    address: '',
    client_type: 'Corporate',
    notes: '',
  });

  const handleChange = (
    e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>
  ) => {
    const { name, value } = e.target;
    setFormData((prev) => ({ ...prev, [name]: value }));
  };

  const handleClientTypeChange = (value: string) => {
    setFormData((prev) => ({ ...prev, client_type: value }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    // Simple validation
    if (
      !formData.name ||
      !formData.phone ||
      !formData.address ||
      !formData.client_type
    ) {
      toast.error('Mohon lengkapi semua field yang wajib diisi');
      return;
    }

    setSubmitting(true);
    try {
      const response = await organizationApi.createOrganization(formData);

      if (response.success && response.data) {
        toast.success('Klien berhasil ditambahkan');
        router.push('/client');
      } else {
        toast.error('Gagal menambahkan klien');
      }
    } catch (error: unknown) {
      console.error('Error creating organization:', error);

      if (error && typeof error === 'object' && 'response' in error) {
        const errorResponse = error.response as { status?: number };
        if (errorResponse.status === 400) {
          toast.error('Data tidak valid. Silakan periksa kembali input Anda.');
        } else if (errorResponse.status === 401) {
          toast.error('Sesi expired. Silakan login kembali.');
        } else if (errorResponse.status === 403) {
          toast.error('Anda tidak memiliki izin untuk menambahkan klien.');
        } else {
          toast.error('Gagal menambahkan klien. Silakan coba lagi nanti.');
        }
      } else {
        toast.error('Gagal menambahkan klien. Silakan coba lagi nanti.');
      }
    } finally {
      setSubmitting(false);
    }
  };

  return (
    <div className="container mx-auto py-6 px-6">
      <div className="flex items-center gap-4 mb-6">
        <BackButton onClick={() => router.push('/client')} />
        <PageTitle
          title="Tambah Klien Baru"
          subtitle="Tambahkan klien baru ke sistem"
        />
      </div>

      <p className="text-muted-foreground mb-8">
        Isi form di bawah ini untuk menambahkan klien baru
      </p>

      <Card className="w-full">
        <CardHeader>
          <CardTitle>Form Informasi Klien</CardTitle>
        </CardHeader>
        <CardContent>
          <form
            id="organization-form"
            className="space-y-4"
            onSubmit={handleSubmit}
          >
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="name">
                  Nama Klien <span className="text-red-500">*</span>
                </Label>
                <Input
                  id="name"
                  name="name"
                  value={formData.name}
                  onChange={handleChange}
                  disabled={submitting}
                  required
                  placeholder="Masukkan nama klien"
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="phone">
                  No. Telepon <span className="text-red-500">*</span>
                </Label>
                <Input
                  id="phone"
                  name="phone"
                  value={formData.phone}
                  onChange={handleChange}
                  disabled={submitting}
                  required
                  placeholder="Masukkan nomor telepon"
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="client_type">
                  Jenis Klien <span className="text-red-500">*</span>
                </Label>
                <Select
                  value={formData.client_type}
                  onValueChange={handleClientTypeChange}
                  disabled={submitting}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Pilih jenis klien" />
                  </SelectTrigger>
                  <SelectContent>
                    {clientTypes.map((type) => (
                      <SelectItem key={type} value={type}>
                        {type}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            </div>

            <div className="space-y-2">
              <Label htmlFor="address">
                Alamat <span className="text-red-500">*</span>
              </Label>
              <Input
                id="address"
                name="address"
                value={formData.address}
                onChange={handleChange}
                disabled={submitting}
                required
                placeholder="Masukkan alamat lengkap"
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="notes">Catatan</Label>
              <Textarea
                id="notes"
                name="notes"
                value={formData.notes || ''}
                onChange={handleChange}
                disabled={submitting}
                rows={4}
                placeholder="Tambahkan catatan jika ada"
              />
            </div>
          </form>
        </CardContent>
        <CardFooter className="flex justify-end gap-2">
          <Button
            variant="cancel"
            onClick={() => router.push('/client')}
            disabled={submitting}
          >
            Batal
          </Button>
          <Button
            type="submit"
            form="organization-form"
            disabled={submitting}
            leftIcon={
              submitting ? (
                <Loader2 className="h-4 w-4 animate-spin" />
              ) : (
                <Save className="h-4 w-4" />
              )
            }
          >
            {submitting ? 'Menyimpan...' : 'Simpan'}
          </Button>
        </CardFooter>
      </Card>
    </div>
  );
};

export default OrganizationForm;
