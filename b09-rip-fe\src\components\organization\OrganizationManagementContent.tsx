// OrganizationManagementContent.tsx
import React, { useState } from 'react';
import { Plus } from 'lucide-react';
import { useRouter } from 'next/navigation';
import { Button } from '@/components/ui/button';
import { useOrganizationManagement } from '@/hooks/useOrganizationManagement';
import OrganizationTable from '@/components/organization/OrganizationTable';
import { OrganizationSearchFilter } from '@/components/organization/OrganizationSearchFilter';
import { PageTitle } from '@/components/ui/PageTitle';
import { SortDirection } from '@/components/ui/data-table';

const OrganizationManagementContent: React.FC = () => {
  const router = useRouter();
  const {
    organizations,
    loading,
    // currentPage, // Not used after DataTable changes
    search,
    clientType,
    handleSearchChange,
    handleClientTypeChange,
    // setCurrentPage, // Not used after DataTable changes
  } = useOrganizationManagement();

  // Add sorting state
  const [sortField, setSortField] = useState<string | undefined>(undefined);
  const [sortDirection, setSortDirection] = useState<SortDirection>(null);

  // Handle sorting
  const handleSort = (field: string, direction: SortDirection) => {
    setSortField(field);
    setSortDirection(direction);
    // Here you would typically call an API with the new sort parameters
    // or update your local sorting logic
  };

  const handleAddClient = () => {
    router.push('/client/add');
  };

  return (
    <div className="container mx-auto py-6 px-6">
      <div className="flex justify-between items-center mb-6">
        <PageTitle
          title="Manajemen Organisasi"
          subtitle="Kelola dan lihat semua klien"
        />
        <Button
          onClick={handleAddClient}
          leftIcon={<Plus className="h-4 w-4" />}
        >
          Tambah Klien
        </Button>
      </div>

      <div className="bg-white rounded-lg shadow p-6">
        <div className="mb-6">
          <OrganizationSearchFilter
            search={search}
            clientType={clientType}
            onSearchChange={handleSearchChange}
            onClientTypeChange={handleClientTypeChange}
          />
        </div>

        <div className="overflow-x-auto mb-4">
          <OrganizationTable
            organizations={organizations}
            // Pagination props removed as they're not used in OrganizationTable anymore
            // currentPage={currentPage}
            // itemsPerPage={10}
            // onPageChange={setCurrentPage}
            loading={loading}
            sortField={sortField}
            sortDirection={sortDirection}
            onSort={handleSort}
          />
        </div>
      </div>
    </div>
  );
};

export default OrganizationManagementContent;
