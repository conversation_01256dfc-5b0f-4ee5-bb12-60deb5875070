//OrganizationSearchFilter.tsx
'use client';

import { SearchFilter, Filter } from '@/components/ui/search-filter';

interface OrganizationSearchFilterProps {
  search: string;
  clientType: string | undefined;
  onSearchChange: (value: string) => void;
  onClientTypeChange: (value: string | undefined) => void;
}

export function OrganizationSearchFilter({
  search,
  clientType,
  onSearchChange,
  onClientTypeChange,
}: OrganizationSearchFilterProps) {
  // List of common client types
  const clientTypeFilter: Filter = {
    label: 'Tipe Klien',
    value: clientType,
    onChange: onClientTypeChange,
    options: [
      { value: 'Corporate', label: 'Corporate' },
      { value: 'Enterprise', label: 'Enterprise' },
      { value: 'Small Business', label: 'Small Business' },
      { value: 'Non-Profit', label: 'Non-Profit' },
      { value: 'Healthcare', label: 'Healthcare' },
      { value: 'Educational', label: 'Educational' },
      { value: 'Government', label: 'Government' },
      { value: 'Retail', label: 'Retail' },
    ],
  };

  return (
    <SearchFilter
      search={search}
      onSearchChange={onSearchChange}
      filters={[clientTypeFilter]}
      searchPlaceholder="Cari organisasi..."
    />
  );
}
