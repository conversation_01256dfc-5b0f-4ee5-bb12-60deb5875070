//OrganizationTable.tsx
import React from 'react';
import { Eye } from 'lucide-react';
import { But<PERSON> } from '@/components/ui/button';
import { Organization } from '@/types/organization';
import { formatDate } from '@/lib/utils/date';
import { useRouter } from 'next/navigation';
import { ClientTypeBadge } from './ClientTypeBadge';
import { DataTable, SortDirection } from '@/components/ui/data-table';

interface OrganizationTableProps {
  organizations: Organization[];
  // Pagination props removed as they're not used in DataTable anymore
  // currentPage: number;
  // itemsPerPage?: number;
  // onPageChange?: (page: number) => void;
  loading?: boolean;
  sortField?: string;
  sortDirection?: SortDirection;
  onSort?: (field: string, direction: SortDirection) => void;
}

const OrganizationTable: React.FC<OrganizationTableProps> = ({
  organizations,
  // Pagination props removed as they're not used in DataTable anymore
  // currentPage = 1,
  // itemsPerPage = 10,
  // onPageChange,
  loading = false,
  sortField,
  sortDirection,
  onSort,
}) => {
  const router = useRouter();

  // Define columns for the DataTable
  const columns = [
    {
      key: 'name',
      header: 'Nama Organisasi',
      sortable: false,
      render: (organization: Organization) => (
        <span className="font-medium">{organization.name}</span>
      ),
    },
    {
      key: 'phone',
      header: 'Telepon',
      sortable: false,
      render: (organization: Organization) => organization.phone,
    },
    {
      key: 'address',
      header: 'Alamat',
      hideOnMobile: true,
      render: (organization: Organization) => organization.address,
    },
    {
      key: 'client_type',
      header: 'Tipe Klien',
      sortable: false,
      render: (organization: Organization) => (
        <ClientTypeBadge type={organization.client_type} />
      ),
    },
    {
      key: 'created_at',
      header: 'Tanggal Dibuat',
      sortable: false,
      render: (organization: Organization) =>
        formatDate(organization.created_at),
    },
    {
      key: 'actions',
      header: 'Aksi',
      width: '120px',
      render: (organization: Organization) => (
        <Button
          onClick={() => router.push(`/client/${organization.id}`)}
          variant="outline"
          size="sm"
          leftIcon={<Eye className="h-4 w-4" />}
        >
          Lihat Detail
        </Button>
      ),
    },
  ];

  return (
    <DataTable
      columns={columns}
      data={organizations}
      keyExtractor={(organization) => organization.id}
      // Pagination props removed as they're not used in DataTable anymore
      // currentPage={currentPage}
      // itemsPerPage={itemsPerPage}
      // onPageChange={onPageChange}
      loading={loading}
      sortField={sortField}
      sortDirection={sortDirection}
      onSort={onSort}
      emptyStateMessage="Tidak ada data organisasi."
      // showPagination prop removed as it's not used in DataTable anymore
      // showPagination={false}
    />
  );
};

export default OrganizationTable;
