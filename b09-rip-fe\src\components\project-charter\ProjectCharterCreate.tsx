'use client';

import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { toast } from 'sonner';
import { Button } from '@/components/ui/button';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogDescription,
  DialogFooter,
} from '@/components/ui/dialog';
import { CreateProjectCharterDto } from '@/types/project-charter';
import { useProjectCharter } from '@/hooks/useProjectCharter';
import { projectApi } from '@/lib/api/project';
import { BackButton } from '@/components/ui/BackButton';
import { PageTitle } from '@/components/ui/PageTitle';
import SuccessDialog from './SuccessDialog';

interface ProjectCharterCreateProps {
  projectId: string;
}

export function ProjectCharterCreate({ projectId }: ProjectCharterCreateProps) {
  const router = useRouter();
  const [loading, setLoading] = useState(false);
  const [confirmDialogOpen, setConfirmDialogOpen] = useState(false);
  const [successDialogOpen, setSuccessDialogOpen] = useState(false);
  const { createProjectCharter } = useProjectCharter();
  const [projectName, setProjectName] = useState<string>('');
  const [projectStartDate, setProjectStartDate] = useState<string>('');
  const [projectEndDate, setProjectEndDate] = useState<string>('');
  const [initialLoading, setInitialLoading] = useState(true);

  const [formData, setFormData] = useState<CreateProjectCharterDto>({
    project_id: projectId,
    key_stakeholders: '',
    project_authority: '',
    project_description: '',
    objective_and_key_results: '',
    purpose: '',
    key_assumption: '',
    assumptions_constrains_risks: '',
    high_level_resources: '',
    high_level_milestones: '',
    statement_prediction_of_benefit: '',
    approval: false,
  });

  // State to track validation errors
  const [errors, setErrors] = useState<Record<string, boolean>>({
    key_stakeholders: false,
    project_authority: false,
    project_description: false,
    objective_and_key_results: false,
    purpose: false,
    key_assumption: false,
    assumptions_constrains_risks: false,
    high_level_resources: false,
    high_level_milestones: false,
    statement_prediction_of_benefit: false,
  });

  // Function to scroll to the first error field
  const scrollToFirstError = () => {
    const firstErrorField = Object.keys(errors).find(key => errors[key as keyof typeof errors]);
    if (firstErrorField) {
      const element = document.getElementById(firstErrorField);
      if (element) {
        element.scrollIntoView({ behavior: 'smooth', block: 'center' });
        element.focus();
      }
    }
  };

  // Load project details
  useEffect(() => {
    const fetchProjectDetails = async () => {
      if (!projectId) return;

      setInitialLoading(true);

      try {
        const response = await projectApi.getProjectById(projectId);
        if (response.success && response.data) {
          setProjectName(response.data.project_name);
          setProjectStartDate(response.data.start_project);
          setProjectEndDate(response.data.end_project);
        } else {
          toast.error('Failed to load project details');
          router.push('/project');
        }
      } catch (error) {
        console.error('Error fetching project details:', error);
        toast.error('Failed to load project details');
        router.push('/project');
      } finally {
        setInitialLoading(false);
      }
    };

    fetchProjectDetails();
  }, [projectId, router]);

  const handleInputChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setFormData((prev) => ({ ...prev, [name]: value }));

    // Clear error for this field if it has a value
    if (value.trim() !== '') {
      setErrors((prev) => ({ ...prev, [name]: false }));
    }
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();

    // Validate all fields
    const newErrors: Record<string, boolean> = {};
    let hasError = false;

    // Check each field
    Object.keys(formData).forEach((key) => {
      if (key !== 'approval' && key !== 'project_id') { // Skip approval and project_id
        const value = formData[key as keyof typeof formData];
        const isEmpty = typeof value === 'string' && (!value || value.trim() === '');
        newErrors[key] = isEmpty;
        if (isEmpty) hasError = true;
      }
    });

    // Update error state
    setErrors(newErrors);

    // Only show confirmation dialog if there are no errors
    if (!hasError) {
      setConfirmDialogOpen(true);
    } else {
      // Scroll to the first error field
      scrollToFirstError();
    }
  };

  const handleConfirmedSubmit = async () => {
    setLoading(true);

    try {
      const result = await createProjectCharter({
        ...formData,
        approval: false, // Default to false for new charters
      });

      if (result) {
        toast.success('Project charter berhasil ditambahkan');
        setConfirmDialogOpen(false);
        setSuccessDialogOpen(true);
      }
    } catch (error) {
      console.error('Error creating project charter:', error);
      setConfirmDialogOpen(false);
    } finally {
      setLoading(false);
    }
  };

  const handleSuccessDone = () => {
    router.push(`/project/${projectId}/charter`);
  };

  if (initialLoading) {
    return (
      <div className="container mx-auto py-6 px-6">
        <div className="flex items-center mb-6">
          <BackButton onClick={() => router.push(`/project/${projectId}`)} />
          <PageTitle title="Tambah Project Charter" />
        </div>
        <div className="bg-white rounded-lg shadow p-6">
          <p className="text-center py-8">Memuat detail proyek...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto py-6 px-6">
      <div className="flex items-center mb-6">
        <BackButton onClick={() => router.push(`/project/${projectId}`)} />
        <PageTitle title="Tambah Project Charter" />
      </div>

      <div className="bg-white rounded-lg shadow p-6">
        <form onSubmit={handleSubmit} className="space-y-6">
          {/* Error Summary */}
          {Object.values(errors).some(error => error) && (
            <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded mb-4">
              <div className="flex">
                <div className="py-1 mr-3">
                  <svg className="h-6 w-6 text-red-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
                  </svg>
                </div>
                <div>
                  <p className="font-bold">Mohon isi semua kolom yang diperlukan</p>
                  <p className="text-sm">Semua kolom harus diisi untuk membuat project charter.</p>
                </div>
              </div>
            </div>
          )}
          <div className="space-y-4">
            <div>
              <Label htmlFor="project_name">Nama Proyek</Label>
              <p className="mt-1 p-2 border rounded-md bg-gray-50">{projectName}</p>
            </div>

            <div>
              <Label htmlFor="start_project">Tanggal Mulai</Label>
              <p className="mt-1 p-2 border rounded-md bg-gray-50">{projectStartDate}</p>
            </div>

            <div>
              <Label htmlFor="end_project">Tanggal Selesai</Label>
              <p className="mt-1 p-2 border rounded-md bg-gray-50">{projectEndDate}</p>
            </div>

            <div>
              <Label htmlFor="key_stakeholders">Pemangku Kepentingan Utama</Label>
              <Textarea
                id="key_stakeholders"
                name="key_stakeholders"
                value={formData.key_stakeholders}
                onChange={handleInputChange}
                placeholder="Daftar pemangku kepentingan utama yang terlibat dalam proyek"
                className={`mt-1 ${errors.key_stakeholders ? 'border-red-500 focus:ring-red-500' : ''}`}
                rows={3}
              />
              {errors.key_stakeholders && (
                <p className="text-red-500 text-sm mt-1">Kolom ini wajib diisi</p>
              )}
            </div>

            <div>
              <Label htmlFor="project_authority">Otoritas Proyek</Label>
              <Textarea
                id="project_authority"
                name="project_authority"
                value={formData.project_authority}
                onChange={handleInputChange}
                placeholder="Jelaskan nama dan wewenang setiap orang yang terlibat dalam proyek"
                className={`mt-1 ${errors.project_authority ? 'border-red-500 focus:ring-red-500' : ''}`}
                rows={3}
              />
              {errors.project_authority && (
                <p className="text-red-500 text-sm mt-1">Kolom ini wajib diisi</p>
              )}
            </div>

            <div>
              <Label htmlFor="project_description">Deskripsi Proyek</Label>
              <Textarea
                id="project_description"
                name="project_description"
                value={formData.project_description}
                onChange={handleInputChange}
                placeholder="Berikan deskripsi detail tentang proyek (deskripsi, boundaries, dan key deliverables)"
                className={`mt-1 ${errors.project_description ? 'border-red-500 focus:ring-red-500' : ''}`}
                rows={4}
              />
              {errors.project_description && (
                <p className="text-red-500 text-sm mt-1">Kolom ini wajib diisi</p>
              )}
            </div>

            <div>
              <Label htmlFor="objective_and_key_results">Objectives dan Hasil Utama</Label>
              <Textarea
                id="objective_and_key_results"
                name="objective_and_key_results"
                value={formData.objective_and_key_results}
                onChange={handleInputChange}
                placeholder="Tentukan objectives dan hasil utama untuk proyek"
                className={`mt-1 ${errors.objective_and_key_results ? 'border-red-500 focus:ring-red-500' : ''}`}
                rows={4}
              />
              {errors.objective_and_key_results && (
                <p className="text-red-500 text-sm mt-1">Kolom ini wajib diisi</p>
              )}
            </div>

            <div>
              <Label htmlFor="purpose">Tujuan</Label>
              <Textarea
                id="purpose"
                name="purpose"
                value={formData.purpose}
                onChange={handleInputChange}
                placeholder="Nyatakan purpose atau kebutuhan bisnis dari proyek"
                className={`mt-1 ${errors.purpose ? 'border-red-500 focus:ring-red-500' : ''}`}
                rows={3}
              />
              {errors.purpose && (
                <p className="text-red-500 text-sm mt-1">Kolom ini wajib diisi</p>
              )}
            </div>

            <div>
              <Label htmlFor="key_assumption">Asumsi Utama</Label>
              <Textarea
                id="key_assumption"
                name="key_assumption"
                value={formData.key_assumption}
                onChange={handleInputChange}
                placeholder="Tuliskan asumsi dasar untuk proyek"
                className={`mt-1 ${errors.key_assumption ? 'border-red-500 focus:ring-red-500' : ''}`}
                rows={3}
              />
              {errors.key_assumption && (
                <p className="text-red-500 text-sm mt-1">Kolom ini wajib diisi</p>
              )}
            </div>

            <div>
              <Label htmlFor="assumptions_constrains_risks">Asumsi, Batasan, dan Risiko</Label>
              <Textarea
                id="assumptions_constrains_risks"
                name="assumptions_constrains_risks"
                value={formData.assumptions_constrains_risks}
                onChange={handleInputChange}
                placeholder="Jelaskan asumsi, batasan, dan risiko (what if plan) untuk proyek"
                className={`mt-1 ${errors.assumptions_constrains_risks ? 'border-red-500 focus:ring-red-500' : ''}`}
                rows={4}
              />
              {errors.assumptions_constrains_risks && (
                <p className="text-red-500 text-sm mt-1">Kolom ini wajib diisi</p>
              )}
            </div>

            <div>
              <Label htmlFor="high_level_resources">Sumber Daya Tingkat Tinggi</Label>
              <Textarea
                id="high_level_resources"
                name="high_level_resources"
                value={formData.high_level_resources}
                onChange={handleInputChange}
                placeholder="Daftar sumber daya tingkat tinggi yang diperlukan (personel, alat kerja, waktu, etc.)"
                className={`mt-1 ${errors.high_level_resources ? 'border-red-500 focus:ring-red-500' : ''}`}
                rows={3}
              />
              {errors.high_level_resources && (
                <p className="text-red-500 text-sm mt-1">Kolom ini wajib diisi</p>
              )}
            </div>

            <div>
              <Label htmlFor="high_level_milestones">High-Level Milestones</Label>
              <Textarea
                id="high_level_milestones"
                name="high_level_milestones"
                value={formData.high_level_milestones}
                onChange={handleInputChange}
                placeholder="Tentukan target krusial pencapaian (masukkan tenggat waktu jelas)"
                className={`mt-1 ${errors.high_level_milestones ? 'border-red-500 focus:ring-red-500' : ''}`}
                rows={3}
              />
              {errors.high_level_milestones && (
                <p className="text-red-500 text-sm mt-1">Kolom ini wajib diisi</p>
              )}
            </div>

            <div>
              <Label htmlFor="statement_prediction_of_benefit">Pernyataan dan Prediksi Manfaat</Label>
              <Textarea
                id="statement_prediction_of_benefit"
                name="statement_prediction_of_benefit"
                value={formData.statement_prediction_of_benefit}
                onChange={handleInputChange}
                placeholder="Berikan pernyataan dan prediksi manfaat dari proyek"
                className={`mt-1 ${errors.statement_prediction_of_benefit ? 'border-red-500 focus:ring-red-500' : ''}`}
                rows={4}
              />
              {errors.statement_prediction_of_benefit && (
                <p className="text-red-500 text-sm mt-1">Kolom ini wajib diisi</p>
              )}
            </div>
          </div>

          <div className="flex justify-end space-x-4 pt-4">
            <Button
              type="button"
              variant="outline"
              onClick={() => router.push(`/project/${projectId}`)}
              disabled={loading}
            >
              Batal
            </Button>
            <Button type="submit" disabled={loading}>
              {loading ? 'Membuat...' : 'Simpan Project Charter'}
            </Button>
          </div>
        </form>
      </div>

      {/* Confirmation Dialog */}
      <Dialog open={confirmDialogOpen} onOpenChange={setConfirmDialogOpen}>
        <DialogContent className="max-w-md max-h-[80vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>Konfirmasi Penambahan Project Charter</DialogTitle>
            <DialogDescription>
              Apakah Anda yakin ingin menambahkan project charter ini?
            </DialogDescription>
          </DialogHeader>

          <div className="py-4 space-y-6">
            {/* Basic Information */}
            <div className="border rounded-md p-4 bg-gray-50">
              <h3 className="font-medium text-gray-700 mb-3">Informasi Proyek</h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                <div>
                  <div className="mb-3">
                    <p className="text-sm font-medium text-gray-500">Nama Proyek</p>
                    <p className="text-sm text-gray-700 font-medium">{projectName}</p>
                  </div>
                  <div>
                    <p className="text-sm font-medium text-gray-500">Deskripsi Proyek</p>
                    <p className="text-sm text-gray-700">{formData.project_description || 'Tidak ditentukan'}</p>
                  </div>
                </div>
                <div>
                  <div className="mb-3">
                    <p className="text-sm font-medium text-gray-500">Tanggal Mulai</p>
                    <p className="text-sm text-gray-700">{projectStartDate}</p>
                  </div>
                  <div className="mb-3">
                    <p className="text-sm font-medium text-gray-500">Tanggal Selesai</p>
                    <p className="text-sm text-gray-700">{projectEndDate}</p>
                  </div>
                  <div>
                    <p className="text-sm font-medium text-gray-500">Tujuan</p>
                    <p className="text-sm text-gray-700">{formData.purpose || 'Tidak ditentukan'}</p>
                  </div>
                </div>
              </div>

              <div className="border-t pt-3">
                <p className="text-sm font-medium text-gray-500">Objectives dan Hasil Utama</p>
                <p className="text-sm text-gray-700">{formData.objective_and_key_results || 'Tidak ditentukan'}</p>
              </div>
            </div>

            {/* Stakeholders and Authority */}
            <div className="border rounded-md p-4 bg-gray-50">
              <h3 className="font-medium text-gray-700 mb-3">Pemangku Kepentingan dan Otoritas</h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <p className="text-sm font-medium text-gray-500">Pemangku Kepentingan Utama</p>
                  <p className="text-sm text-gray-700">{formData.key_stakeholders || 'Tidak ditentukan'}</p>
                </div>
                <div>
                  <p className="text-sm font-medium text-gray-500">Otoritas Proyek</p>
                  <p className="text-sm text-gray-700">{formData.project_authority || 'Tidak ditentukan'}</p>
                </div>
              </div>
            </div>

            {/* Assumptions and Risks */}
            <div className="border rounded-md p-4 bg-gray-50">
              <h3 className="font-medium text-gray-700 mb-3">Asumsi dan Risiko</h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <p className="text-sm font-medium text-gray-500">Asumsi Utama</p>
                  <p className="text-sm text-gray-700">{formData.key_assumption || 'Tidak ditentukan'}</p>
                </div>
                <div>
                  <p className="text-sm font-medium text-gray-500">Asumsi, Batasan, dan Risiko</p>
                  <p className="text-sm text-gray-700">{formData.assumptions_constrains_risks || 'Tidak ditentukan'}</p>
                </div>
              </div>
            </div>

            {/* Resources and Milestones */}
            <div className="border rounded-md p-4 bg-gray-50">
              <h3 className="font-medium text-gray-700 mb-3">Sumber Daya dan Milestones</h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <p className="text-sm font-medium text-gray-500">Sumber Daya Tingkat Tinggi</p>
                  <p className="text-sm text-gray-700">{formData.high_level_resources || 'Tidak ditentukan'}</p>
                </div>
                <div>
                  <p className="text-sm font-medium text-gray-500">High-Level Milestones</p>
                  <p className="text-sm text-gray-700">{formData.high_level_milestones || 'Tidak ditentukan'}</p>
                </div>
              </div>
            </div>

            {/* Benefits */}
            <div className="border rounded-md p-4 bg-gray-50">
              <h3 className="font-medium text-gray-700 mb-3">Manfaat</h3>
              <div>
                <p className="text-sm font-medium text-gray-500">Pernyataan dan Prediksi Manfaat</p>
                <p className="text-sm text-gray-700">{formData.statement_prediction_of_benefit || 'Tidak ditentukan'}</p>
              </div>
            </div>
          </div>

          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => setConfirmDialogOpen(false)}
              disabled={loading}
            >
              Kembali
            </Button>
            <Button onClick={handleConfirmedSubmit} disabled={loading}>
              {loading ? 'Membuat...' : 'Ya, Tambahkan'}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Success Dialog */}
      <SuccessDialog
        open={successDialogOpen}
        onOpenChange={setSuccessDialogOpen}
        title="Project Charter Berhasil Dibuat"
        message="Project charter telah berhasil dibuat dan disimpan."
        onDone={handleSuccessDone}
      />
    </div>
  );
}
