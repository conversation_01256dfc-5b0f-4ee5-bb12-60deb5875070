'use client';

import { useEffect, useState } from 'react';
import { useRouter } from 'next/navigation';
import { toast } from 'sonner';
import { Button } from '@/components/ui/button';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Badge } from '@/components/ui/badge';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogDescription,
  DialogFooter,
} from '@/components/ui/dialog';
import { UpdateProjectCharterDto } from '@/types/project-charter';
import { useProjectCharter } from '@/hooks/useProjectCharter';
import { projectCharterApi } from '@/lib/api/project-charter';
import { projectApi } from '@/lib/api/project';
import { BackButton } from '@/components/ui/BackButton';
import { PageTitle } from '@/components/ui/PageTitle';
import { FileText, CheckCircle, XCircle } from 'lucide-react';
import SuccessDialog from './SuccessDialog';

interface ProjectCharterEditProps {
  projectId: string;
}

export function ProjectCharterEdit({ projectId }: ProjectCharterEditProps) {
  const router = useRouter();
  const [loading, setLoading] = useState(false);
  const [confirmDialogOpen, setConfirmDialogOpen] = useState(false);
  const [successDialogOpen, setSuccessDialogOpen] = useState(false);
  const { updateProjectCharter } = useProjectCharter();
  const [projectName, setProjectName] = useState<string>('');
  const [projectStartDate, setProjectStartDate] = useState<string>('');
  const [projectEndDate, setProjectEndDate] = useState<string>('');
  const [initialLoading, setInitialLoading] = useState(true);
  const [charterId, setCharterId] = useState<string | null>(null);
  const [originalData, setOriginalData] =
    useState<UpdateProjectCharterDto | null>(null);

  const [formData, setFormData] = useState<UpdateProjectCharterDto>({
    key_stakeholders: '',
    project_authority: '',
    project_description: '',
    objective_and_key_results: '',
    purpose: '',
    key_assumption: '',
    assumptions_constrains_risks: '',
    high_level_resources: '',
    high_level_milestones: '',
    statement_prediction_of_benefit: '',
    approval: false,
  });

  // State to track validation errors
  const [errors, setErrors] = useState<Record<string, boolean>>({
    key_stakeholders: false,
    project_authority: false,
    project_description: false,
    objective_and_key_results: false,
    purpose: false,
    key_assumption: false,
    assumptions_constrains_risks: false,
    high_level_resources: false,
    high_level_milestones: false,
    statement_prediction_of_benefit: false,
  });

  // Function to scroll to the first error field
  const scrollToFirstError = () => {
    const firstErrorField = Object.keys(errors).find(
      (key) => errors[key as keyof typeof errors]
    );
    if (firstErrorField) {
      const element = document.getElementById(firstErrorField);
      if (element) {
        element.scrollIntoView({ behavior: 'smooth', block: 'center' });
        element.focus();
      }
    }
  };

  // Load project charter details
  useEffect(() => {
    const fetchProjectCharterDetails = async () => {
      if (!projectId) return;

      setInitialLoading(true);

      try {
        // First fetch project details to get the project name
        const projectResponse = await projectApi.getProjectById(projectId);
        if (projectResponse.success && projectResponse.data) {
          setProjectName(projectResponse.data.project_name);
          setProjectStartDate(projectResponse.data.start_project);
          setProjectEndDate(projectResponse.data.end_project);

          // Then fetch the project charter using the project ID
          const charterResponse =
            await projectCharterApi.getProjectCharterByProjectId(projectId);
          if (charterResponse.success && charterResponse.data) {
            const charter = charterResponse.data;
            setCharterId(charter.id);

            // Create data object from charter
            const charterData = {
              key_stakeholders: charter.key_stakeholders,
              project_authority: charter.project_authority,
              project_description: charter.project_description,
              objective_and_key_results: charter.objective_and_key_results,
              purpose: charter.purpose,
              key_assumption: charter.key_assumption,
              assumptions_constrains_risks:
                charter.assumptions_constrains_risks,
              high_level_resources: charter.high_level_resources,
              high_level_milestones: charter.high_level_milestones,
              statement_prediction_of_benefit:
                charter.statement_prediction_of_benefit,
              approval: charter.approval,
            };

            // Store original data for comparison
            setOriginalData(charterData);

            // Set form data from charter
            setFormData(charterData);
          } else {
            toast.error('Failed to load project charter details');
            router.push(`/project/${projectId}`);
          }
        } else {
          toast.error('Failed to load project details');
          router.push('/project');
        }
      } catch (error) {
        console.error('Error fetching project charter details:', error);
        toast.error('Failed to load project charter details');
        router.push(`/project/${projectId}`);
      } finally {
        setInitialLoading(false);
      }
    };

    fetchProjectCharterDetails();
  }, [projectId, router]);

  const handleInputChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setFormData((prev) => ({ ...prev, [name]: value }));

    // Clear error for this field if it has a value
    if (value.trim() !== '') {
      setErrors((prev) => ({ ...prev, [name]: false }));
    }
  };

  const handleSelectChange = (value: string) => {
    // Convert string value to boolean
    const boolValue = value === 'true';
    setFormData((prev) => ({ ...prev, approval: boolValue }));
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();

    // Validate all fields
    const newErrors: Record<string, boolean> = {};
    let hasError = false;

    // Check each field
    Object.keys(formData).forEach((key) => {
      if (key !== 'approval') {
        // Skip approval since it's a boolean
        const value = formData[key as keyof typeof formData];
        const isEmpty =
          typeof value === 'string' && (!value || value.trim() === '');
        newErrors[key] = isEmpty;
        if (isEmpty) hasError = true;
      }
    });

    // Update error state
    setErrors(newErrors);

    // Only show confirmation dialog if there are no errors
    if (!hasError) {
      setConfirmDialogOpen(true);
    } else {
      // Scroll to the first error field
      scrollToFirstError();
    }
  };

  // Check if a field has been changed
  const hasFieldChanged = (field: keyof UpdateProjectCharterDto): boolean => {
    if (!originalData) return false;

    // Handle special case for approval which is a boolean
    if (field === 'approval') {
      return formData.approval !== originalData.approval;
    }

    // For string fields
    return formData[field] !== originalData[field];
  };

  // Get only the changed fields for submission
  const getChangedFields = (): UpdateProjectCharterDto => {
    if (!originalData) return formData;

    const changedFields: UpdateProjectCharterDto = {};

    (Object.keys(formData) as Array<keyof UpdateProjectCharterDto>).forEach(
      (key) => {
        if (hasFieldChanged(key)) {
          if (key === 'approval') {
            changedFields[key] = formData[key] as boolean;
          } else {
            changedFields[key] = formData[key] as string;
          }
        }
      }
    );

    return changedFields;
  };

  const handleConfirmedSubmit = async () => {
    if (!charterId) {
      toast.error('Charter ID not found');
      return;
    }

    setLoading(true);

    try {
      // Only submit changed fields
      const changedFields = getChangedFields();
      const result = await updateProjectCharter(charterId, changedFields);

      if (result) {
        toast.success('Project charter berhasil diperbarui');
        setConfirmDialogOpen(false);
        setSuccessDialogOpen(true);
      }
    } catch (error) {
      console.error('Error updating project charter:', error);
      setConfirmDialogOpen(false);
    } finally {
      setLoading(false);
    }
  };

  const handleSuccessDone = () => {
    router.push(`/project/${projectId}/charter`);
  };

  if (initialLoading) {
    return (
      <div className="container mx-auto py-6 px-6">
        <div className="flex items-center mb-6">
          <BackButton
            onClick={() => router.push(`/project/${projectId}/charter`)}
          />
          <PageTitle title="Edit Project Charter" />
        </div>
        <div className="bg-white rounded-lg shadow p-6">
          <p className="text-center py-8">Memuat detail project charter</p>
        </div>
      </div>
    );
  }

  if (!charterId) {
    return (
      <div className="container mx-auto py-6 px-6">
        <div className="flex items-center justify-between mb-6">
          <div className="flex items-center">
            <BackButton onClick={() => router.push(`/project/${projectId}`)} />
            <PageTitle title="Project Charter Not Found" />
          </div>
          <Button
            onClick={() => router.push(`/project/${projectId}/charter/create`)}
            className="flex items-center gap-2"
          >
            <FileText className="h-4 w-4" />
            Create Project Charter
          </Button>
        </div>
        <div className="bg-white rounded-lg shadow p-6">
          <p className="text-center py-8">
            No project charter found for this project.
          </p>
          <p className="text-center text-gray-500">
            Click the &quot;Create Project Charter&quot; button to create one.
          </p>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto py-6 px-6">
      <div className="flex items-center mb-6">
        <BackButton
          onClick={() => router.push(`/project/${projectId}/charter`)}
        />
        <PageTitle title="Edit Project Charter" />
      </div>

      <div className="bg-white rounded-lg shadow p-6">
        <form onSubmit={handleSubmit} className="space-y-6">
          {/* Error Summary */}
          {Object.values(errors).some((error) => error) && (
            <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded mb-4">
              <div className="flex">
                <div className="py-1 mr-3">
                  <svg
                    className="h-6 w-6 text-red-500"
                    fill="none"
                    viewBox="0 0 24 24"
                    stroke="currentColor"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth="2"
                      d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z"
                    />
                  </svg>
                </div>
                <div>
                  <p className="font-bold">
                    Mohon isi semua kolom yang diperlukan
                  </p>
                  <p className="text-sm">
                    Semua kolom harus diisi untuk memperbarui project charter.
                  </p>
                </div>
              </div>
            </div>
          )}
          <div className="space-y-4">
            <div>
              <Label htmlFor="project_name">Nama Proyek</Label>
              <p className="mt-1 p-2 border rounded-md bg-gray-50">
                {projectName}
              </p>
            </div>

            <div>
              <Label htmlFor="start_project">Tanggal Mulai</Label>
              <p className="mt-1 p-2 border rounded-md bg-gray-50">
                {projectStartDate}
              </p>
            </div>

            <div>
              <Label htmlFor="end_project">Tanggal Selesai</Label>
              <p className="mt-1 p-2 border rounded-md bg-gray-50">
                {projectEndDate}
              </p>
            </div>

            <div>
              <Label htmlFor="approval">Status Persetujuan</Label>
              <Select
                value={(formData.approval !== undefined
                  ? formData.approval
                  : false
                ).toString()}
                onValueChange={handleSelectChange}
              >
                <SelectTrigger className="mt-1">
                  <SelectValue placeholder="Pilih status persetujuan" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="true">Disetujui</SelectItem>
                  <SelectItem value="false">Belum Disetujui</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div>
              <Label htmlFor="key_stakeholders">
                Pemangku Kepentingan Utama
              </Label>
              <Textarea
                id="key_stakeholders"
                name="key_stakeholders"
                value={formData.key_stakeholders}
                onChange={handleInputChange}
                placeholder="List key stakeholders involved in the project"
                className={`mt-1 ${errors.key_stakeholders ? 'border-red-500 focus:ring-red-500' : ''}`}
                rows={3}
              />
              {errors.key_stakeholders && (
                <p className="text-red-500 text-sm mt-1">
                  Kolom ini wajib diisi
                </p>
              )}
            </div>

            <div>
              <Label htmlFor="project_authority">Otoritas Proyek</Label>
              <Textarea
                id="project_authority"
                name="project_authority"
                value={formData.project_authority}
                onChange={handleInputChange}
                placeholder="Describe the project authority and governance structure"
                className={`mt-1 ${errors.project_authority ? 'border-red-500 focus:ring-red-500' : ''}`}
                rows={3}
              />
              {errors.project_authority && (
                <p className="text-red-500 text-sm mt-1">
                  Kolom ini wajib diisi
                </p>
              )}
            </div>

            <div>
              <Label htmlFor="project_description">Deskripsi Proyek</Label>
              <Textarea
                id="project_description"
                name="project_description"
                value={formData.project_description}
                onChange={handleInputChange}
                placeholder="Provide a detailed description of the project"
                className={`mt-1 ${errors.project_description ? 'border-red-500 focus:ring-red-500' : ''}`}
                rows={4}
              />
              {errors.project_description && (
                <p className="text-red-500 text-sm mt-1">
                  Kolom ini wajib diisi
                </p>
              )}
            </div>

            <div>
              <Label htmlFor="objective_and_key_results">
                Objectives dan Hasil Utama
              </Label>
              <Textarea
                id="objective_and_key_results"
                name="objective_and_key_results"
                value={formData.objective_and_key_results}
                onChange={handleInputChange}
                placeholder="Tentukan objectives dan hasil utama untuk proyek"
                className={`mt-1 ${errors.objective_and_key_results ? 'border-red-500 focus:ring-red-500' : ''}`}
                rows={4}
              />
              {errors.objective_and_key_results && (
                <p className="text-red-500 text-sm mt-1">
                  Kolom ini wajib diisi
                </p>
              )}
            </div>

            <div>
              <Label htmlFor="purpose">Tujuan</Label>
              <Textarea
                id="purpose"
                name="purpose"
                value={formData.purpose}
                onChange={handleInputChange}
                placeholder="State the purpose of the project"
                className={`mt-1 ${errors.purpose ? 'border-red-500 focus:ring-red-500' : ''}`}
                rows={3}
              />
              {errors.purpose && (
                <p className="text-red-500 text-sm mt-1">
                  Kolom ini wajib diisi
                </p>
              )}
            </div>

            <div>
              <Label htmlFor="key_assumption">Asumsi Utama</Label>
              <Textarea
                id="key_assumption"
                name="key_assumption"
                value={formData.key_assumption}
                onChange={handleInputChange}
                placeholder="List key assumptions for the project"
                className={`mt-1 ${errors.key_assumption ? 'border-red-500 focus:ring-red-500' : ''}`}
                rows={3}
              />
              {errors.key_assumption && (
                <p className="text-red-500 text-sm mt-1">
                  Kolom ini wajib diisi
                </p>
              )}
            </div>

            <div>
              <Label htmlFor="assumptions_constrains_risks">
                Asumsi, Batasan, dan Risiko
              </Label>
              <Textarea
                id="assumptions_constrains_risks"
                name="assumptions_constrains_risks"
                value={formData.assumptions_constrains_risks}
                onChange={handleInputChange}
                placeholder="Describe assumptions, constraints, and risks for the project"
                className={`mt-1 ${errors.assumptions_constrains_risks ? 'border-red-500 focus:ring-red-500' : ''}`}
                rows={4}
              />
              {errors.assumptions_constrains_risks && (
                <p className="text-red-500 text-sm mt-1">
                  Kolom ini wajib diisi
                </p>
              )}
            </div>

            <div>
              <Label htmlFor="high_level_resources">
                Sumber Daya Tingkat Tinggi
              </Label>
              <Textarea
                id="high_level_resources"
                name="high_level_resources"
                value={formData.high_level_resources}
                onChange={handleInputChange}
                placeholder="List high-level resources required for the project"
                className={`mt-1 ${errors.high_level_resources ? 'border-red-500 focus:ring-red-500' : ''}`}
                rows={3}
              />
              {errors.high_level_resources && (
                <p className="text-red-500 text-sm mt-1">
                  Kolom ini wajib diisi
                </p>
              )}
            </div>

            <div>
              <Label htmlFor="high_level_milestones">
                High-Level Milestones
              </Label>
              <Textarea
                id="high_level_milestones"
                name="high_level_milestones"
                value={formData.high_level_milestones}
                onChange={handleInputChange}
                placeholder="Define high-level milestones for the project"
                className={`mt-1 ${errors.high_level_milestones ? 'border-red-500 focus:ring-red-500' : ''}`}
                rows={3}
              />
              {errors.high_level_milestones && (
                <p className="text-red-500 text-sm mt-1">
                  Kolom ini wajib diisi
                </p>
              )}
            </div>

            <div>
              <Label htmlFor="statement_prediction_of_benefit">
                Pernyataan dan Prediksi Manfaat
              </Label>
              <Textarea
                id="statement_prediction_of_benefit"
                name="statement_prediction_of_benefit"
                value={formData.statement_prediction_of_benefit}
                onChange={handleInputChange}
                placeholder="Provide a statement and prediction of benefits from the project"
                className={`mt-1 ${errors.statement_prediction_of_benefit ? 'border-red-500 focus:ring-red-500' : ''}`}
                rows={4}
              />
              {errors.statement_prediction_of_benefit && (
                <p className="text-red-500 text-sm mt-1">
                  Kolom ini wajib diisi
                </p>
              )}
            </div>
          </div>

          <div className="flex justify-end space-x-4 pt-4">
            <Button
              type="button"
              variant="outline"
              onClick={() => router.push(`/project/${projectId}/charter`)}
              disabled={loading}
            >
              Batal
            </Button>
            <Button type="submit" disabled={loading}>
              {loading ? 'Updating...' : 'Lanjutkan'}
            </Button>
          </div>
        </form>
      </div>

      {/* Confirmation Dialog */}
      <Dialog open={confirmDialogOpen} onOpenChange={setConfirmDialogOpen}>
        <DialogContent className="max-w-md max-h-[80vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>Konfirmasi Perubahan Project Charter</DialogTitle>
            <DialogDescription>
              Apakah Anda yakin ingin menyimpan perubahan pada project charter
              ini?
            </DialogDescription>
          </DialogHeader>

          <div className="py-4 space-y-6">
            {/* Project Name (always show) */}
            <div className="border rounded-md p-4 bg-gray-50">
              <h3 className="font-medium text-gray-700 mb-3">
                Informasi Proyek
              </h3>
              <div className="mb-3">
                <p className="text-sm font-medium text-gray-500">Nama Proyek</p>
                <p className="text-sm text-gray-700 font-medium">
                  {projectName}
                </p>
              </div>

              {/* Only show changed fields */}
              {hasFieldChanged('approval') && (
                <div className="mb-3">
                  <p className="text-sm font-medium text-gray-500">
                    Status Persetujuan
                  </p>
                  <Badge
                    className={`px-2 py-1 text-xs font-medium flex items-center gap-1 mt-1 ${
                      formData.approval
                        ? 'bg-green-100 text-green-800 border-green-200'
                        : 'bg-amber-100 text-amber-800 border-amber-200'
                    }`}
                  >
                    {formData.approval ? (
                      <>
                        <CheckCircle className="h-3 w-3" />
                        Disetujui
                      </>
                    ) : (
                      <>
                        <XCircle className="h-3 w-3" />
                        Belum Disetujui
                      </>
                    )}
                  </Badge>
                </div>
              )}

              {hasFieldChanged('project_description') && (
                <div className="mb-3">
                  <p className="text-sm font-medium text-gray-500">
                    Deskripsi Proyek
                  </p>
                  <p className="text-sm text-gray-700">
                    {formData.project_description || 'Not specified'}
                  </p>
                </div>
              )}

              {hasFieldChanged('purpose') && (
                <div className="mb-3">
                  <p className="text-sm font-medium text-gray-500">Tujuan</p>
                  <p className="text-sm text-gray-700">
                    {formData.purpose || 'Not specified'}
                  </p>
                </div>
              )}

              {hasFieldChanged('objective_and_key_results') && (
                <div className="border-t pt-3">
                  <p className="text-sm font-medium text-gray-500">
                    Objectives dan Hasil Utama
                  </p>
                  <p className="text-sm text-gray-700">
                    {formData.objective_and_key_results || 'Tidak ditentukan'}
                  </p>
                </div>
              )}
            </div>

            {/* Stakeholders and Authority - only show if changed */}
            {(hasFieldChanged('key_stakeholders') ||
              hasFieldChanged('project_authority')) && (
              <div className="border rounded-md p-4 bg-gray-50">
                <h3 className="font-medium text-gray-700 mb-3">
                  Pemangku Kepentingan dan Otoritas
                </h3>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  {hasFieldChanged('key_stakeholders') && (
                    <div>
                      <p className="text-sm font-medium text-gray-500">
                        Pemangku Kepentingan Utama
                      </p>
                      <p className="text-sm text-gray-700">
                        {formData.key_stakeholders || 'Not specified'}
                      </p>
                    </div>
                  )}
                  {hasFieldChanged('project_authority') && (
                    <div>
                      <p className="text-sm font-medium text-gray-500">
                        Otoritas Proyek
                      </p>
                      <p className="text-sm text-gray-700">
                        {formData.project_authority || 'Not specified'}
                      </p>
                    </div>
                  )}
                </div>
              </div>
            )}

            {/* Assumptions and Risks - only show if changed */}
            {(hasFieldChanged('key_assumption') ||
              hasFieldChanged('assumptions_constrains_risks')) && (
              <div className="border rounded-md p-4 bg-gray-50">
                <h3 className="font-medium text-gray-700 mb-3">
                  Asumsi dan Risiko
                </h3>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  {hasFieldChanged('key_assumption') && (
                    <div>
                      <p className="text-sm font-medium text-gray-500">
                        Asumsi Utama
                      </p>
                      <p className="text-sm text-gray-700">
                        {formData.key_assumption || 'Not specified'}
                      </p>
                    </div>
                  )}
                  {hasFieldChanged('assumptions_constrains_risks') && (
                    <div>
                      <p className="text-sm font-medium text-gray-500">
                        Asumsi, Batasan, dan Risiko
                      </p>
                      <p className="text-sm text-gray-700">
                        {formData.assumptions_constrains_risks ||
                          'Not specified'}
                      </p>
                    </div>
                  )}
                </div>
              </div>
            )}

            {/* Resources and Milestones - only show if changed */}
            {(hasFieldChanged('high_level_resources') ||
              hasFieldChanged('high_level_milestones')) && (
              <div className="border rounded-md p-4 bg-gray-50">
                <h3 className="font-medium text-gray-700 mb-3">
                  Sumber Daya dan Milestones
                </h3>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  {hasFieldChanged('high_level_resources') && (
                    <div>
                      <p className="text-sm font-medium text-gray-500">
                        Sumber Daya Tigkat Tinggi
                      </p>
                      <p className="text-sm text-gray-700">
                        {formData.high_level_resources || 'Not specified'}
                      </p>
                    </div>
                  )}
                  {hasFieldChanged('high_level_milestones') && (
                    <div>
                      <p className="text-sm font-medium text-gray-500">
                        High-Level Milestones
                      </p>
                      <p className="text-sm text-gray-700">
                        {formData.high_level_milestones || 'Not specified'}
                      </p>
                    </div>
                  )}
                </div>
              </div>
            )}

            {/* Benefits - only show if changed */}
            {hasFieldChanged('statement_prediction_of_benefit') && (
              <div className="border rounded-md p-4 bg-gray-50">
                <h3 className="font-medium text-gray-700 mb-3">Manfaat</h3>
                <div>
                  <p className="text-sm font-medium text-gray-500">
                    Pernyataan dan Prediksi Manfaat
                  </p>
                  <p className="text-sm text-gray-700">
                    {formData.statement_prediction_of_benefit ||
                      'Not specified'}
                  </p>
                </div>
              </div>
            )}

            {/* Show message if no changes */}
            {Object.keys(getChangedFields()).length === 0 && (
              <div className="text-center py-4 text-gray-500">
                <p>Tidak ada perubahan yang dilakukan.</p>
              </div>
            )}
          </div>

          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => setConfirmDialogOpen(false)}
              disabled={loading}
            >
              Kembali
            </Button>
            <Button
              onClick={handleConfirmedSubmit}
              disabled={loading || Object.keys(getChangedFields()).length === 0}
            >
              {loading ? 'Menyimpan...' : 'Ya, Simpan Perubahan'}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Success Dialog */}
      <SuccessDialog
        open={successDialogOpen}
        onOpenChange={setSuccessDialogOpen}
        title="Project Charter Berhasil Diperbarui"
        message="Project charter telah berhasil diperbarui dan disimpan."
        onDone={handleSuccessDone}
      />
    </div>
  );
}
