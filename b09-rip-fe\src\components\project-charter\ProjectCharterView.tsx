'use client';

import React, { useEffect, useState } from 'react';
import { useRouter } from 'next/navigation';
import { But<PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { projectCharterApi } from '@/lib/api/project-charter';
import { projectApi } from '@/lib/api/project';
import { ProjectCharter } from '@/types/project-charter';
import { BackButton } from '@/components/ui/BackButton';
import { PageTitle } from '@/components/ui/PageTitle';
import { Edit, FileText, CheckCircle, XCircle } from 'lucide-react';
import { useRBAC } from '@/hooks/useRBAC';
import { format } from 'date-fns';

interface ProjectCharterViewProps {
  projectId: string;
}

export function ProjectCharterView({ projectId }: ProjectCharterViewProps) {
  const router = useRouter();
  const [projectCharter, setProjectCharter] = useState<ProjectCharter | null>(
    null
  );
  const [projectName, setProjectName] = useState<string>('');
  const [projectStartDate, setProjectStartDate] = useState<string>('');
  const [projectEndDate, setProjectEndDate] = useState<string>('');
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const { hasRole } = useRBAC();
  const canEditProjectCharter = hasRole(['Operation', 'Manager']);

  useEffect(() => {
    const fetchProjectData = async () => {
      if (!projectId) return;

      setLoading(true);
      setError(null);

      try {
        // First fetch project details to get project name and dates
        const projectResponse = await projectApi.getProjectById(projectId);
        if (projectResponse.success && projectResponse.data) {
          setProjectName(projectResponse.data.project_name);
          setProjectStartDate(projectResponse.data.start_project);
          setProjectEndDate(projectResponse.data.end_project);
        }

        // Then fetch the project charter
        const charterResponse =
          await projectCharterApi.getProjectCharterByProjectId(projectId);
        if (charterResponse.success && charterResponse.data) {
          setProjectCharter(charterResponse.data);
        } else {
          setError(
            charterResponse.message || 'Failed to fetch project charter'
          );
        }
      } catch (err: unknown) {
        setError(
          err instanceof Error
            ? err.message
            : 'An error occurred while fetching the project charter'
        );
      } finally {
        setLoading(false);
      }
    };

    fetchProjectData();
  }, [projectId]);

  if (loading) {
    return (
      <div className="container mx-auto py-6 px-6">
        Loading project charter...
      </div>
    );
  }

  if (error) {
    return <div className="container mx-auto py-6 px-6">Error: {error}</div>;
  }

  if (!projectCharter) {
    return (
      <div className="container mx-auto py-6 px-6">
        <div className="flex items-center justify-between mb-6">
          <div className="flex items-center gap-4">
            <BackButton onClick={() => router.push(`/project/${projectId}`)} />
            <PageTitle title="Project Charter Not Found" />
          </div>
          <Button
            onClick={() => router.push(`/project/${projectId}/charter/create`)}
            className="flex items-center gap-2"
          >
            <FileText className="h-4 w-4" />
            Create Project Charter
          </Button>
        </div>
        <div className="bg-white rounded-lg shadow p-6">
          <p className="text-center py-8">
            No project charter found for this project.
          </p>
          <p className="text-center text-gray-500">
            Click the &quot;Create Project Charter&quot; button to create one.
          </p>
        </div>
      </div>
    );
  }

  // Format date function
  const formatDate = (dateString: string) => {
    if (!dateString) return '-';
    try {
      const date = new Date(dateString);
      return format(date, 'dd MMMM yyyy');
    } catch {
      return dateString;
    }
  };

  return (
    <div className="container mx-auto py-6 px-6">
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center">
          <BackButton onClick={() => router.push(`/project/${projectId}`)} />
          <PageTitle title="Detail Project Charter" />
        </div>

        <div className="flex items-center gap-3">
          <Badge
            className={`px-3 py-1.5 text-sm font-medium flex items-center gap-1.5 ${
              projectCharter?.approval
                ? 'bg-green-100 text-green-800 border-green-200'
                : 'bg-red-200 text-red-800 border-red-500'
            }`}
          >
            {projectCharter?.approval ? (
              <>
                <CheckCircle className="h-4 w-4" />
                Disetujui
              </>
            ) : (
              <>
                <XCircle className="h-4 w-4" />
                Belum Disetujui
              </>
            )}
          </Badge>

          {canEditProjectCharter && (
            <Button
              onClick={() => router.push(`/project/${projectId}/charter/edit`)}
              variant="outline"
              className="flex items-center gap-2"
            >
              <Edit className="h-4 w-4" />
              Edit
            </Button>
          )}
        </div>
      </div>

      <div className="grid gap-6">
        {/* Basic Project Information */}
        <Card>
          <CardHeader className="bg-gray-50 border-b border-gray-200">
            <CardTitle className="text-[#AB8B3B]">Informasi Proyek</CardTitle>
          </CardHeader>
          <CardContent className="p-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
              <div>
                <div className="space-y-1.5 mb-4">
                  <p className="text-sm font-medium text-gray-500">
                    Nama Proyek
                  </p>
                  <p className="text-base font-medium">{projectName}</p>
                </div>
                <div className="space-y-1.5 mb-6">
                  <p className="text-sm font-medium text-gray-500">
                    Deskripsi Proyek
                  </p>
                  <p className="text-base whitespace-pre-line">
                    {projectCharter.project_description}
                  </p>
                </div>
                <div className="space-y-1.5">
                  <p className="text-sm font-medium text-gray-500">
                    Objectives dan Hasil Utama
                  </p>
                  <p className="text-base whitespace-pre-line">
                    {projectCharter.objective_and_key_results}
                  </p>
                </div>
              </div>
              <div>
                <div className="space-y-1.5 mb-4">
                  <p className="text-sm font-medium text-gray-500">
                    Tanggal Mulai
                  </p>
                  <p className="text-base">{formatDate(projectStartDate)}</p>
                </div>
                <div className="space-y-1.5 mb-6">
                  <p className="text-sm font-medium text-gray-500">
                    Tanggal Selesai
                  </p>
                  <p className="text-base">{formatDate(projectEndDate)}</p>
                </div>
                <div className="space-y-1.5">
                  <p className="text-sm font-medium text-gray-500">Tujuan</p>
                  <p className="text-base whitespace-pre-line">
                    {projectCharter.purpose}
                  </p>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Stakeholders and Authority */}
        <Card>
          <CardHeader className="bg-gray-50 border-b border-gray-200">
            <CardTitle className="text-[#AB8B3B]">
              Pemangku Kepentingan dan Otoritas
            </CardTitle>
          </CardHeader>
          <CardContent className="p-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="space-y-1.5">
                <p className="text-sm font-medium text-gray-500">
                  Pemangku Kepentingan Utama
                </p>
                <p className="text-base whitespace-pre-line">
                  {projectCharter.key_stakeholders}
                </p>
              </div>
              <div className="space-y-1.5">
                <p className="text-sm font-medium text-gray-500">
                  Otoritas Proyek
                </p>
                <p className="text-base whitespace-pre-line">
                  {projectCharter.project_authority}
                </p>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Assumptions and Risks */}
        <Card>
          <CardHeader className="bg-gray-50 border-b border-gray-200">
            <CardTitle className="text-[#AB8B3B]">Asumsi dan Risiko</CardTitle>
          </CardHeader>
          <CardContent className="p-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="space-y-1.5">
                <p className="text-sm font-medium text-gray-500">
                  Asumsi Utama
                </p>
                <p className="text-base whitespace-pre-line">
                  {projectCharter.key_assumption}
                </p>
              </div>
              <div className="space-y-1.5">
                <p className="text-sm font-medium text-gray-500">
                  Asumsi, Batasan, dan Risiko
                </p>
                <p className="text-base whitespace-pre-line">
                  {projectCharter.assumptions_constrains_risks}
                </p>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Resources and Milestones */}
        <Card>
          <CardHeader className="bg-gray-50 border-b border-gray-200">
            <CardTitle className="text-[#AB8B3B]">
              Sumber Daya dan Milestones
            </CardTitle>
          </CardHeader>
          <CardContent className="p-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="space-y-1.5">
                <p className="text-sm font-medium text-gray-500">
                  Sumber Daya Tingkat Tinggi
                </p>
                <p className="text-base whitespace-pre-line">
                  {projectCharter.high_level_resources}
                </p>
              </div>
              <div className="space-y-1.5">
                <p className="text-sm font-medium text-gray-500">
                  High-Level Milestones
                </p>
                <p className="text-base whitespace-pre-line">
                  {projectCharter.high_level_milestones}
                </p>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Benefits */}
        <Card>
          <CardHeader className="bg-gray-50 border-b border-gray-200">
            <CardTitle className="text-[#AB8B3B]">Manfaat</CardTitle>
          </CardHeader>
          <CardContent className="p-6">
            <div className="grid grid-cols-1 gap-6">
              <div className="space-y-1.5">
                <p className="text-sm font-medium text-gray-500">
                  Pernyataan dan Prediksi Manfaat
                </p>
                <p className="text-base whitespace-pre-line">
                  {projectCharter.statement_prediction_of_benefit}
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
