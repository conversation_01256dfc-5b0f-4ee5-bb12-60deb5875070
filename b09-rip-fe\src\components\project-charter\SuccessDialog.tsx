'use client';

import React from 'react';
import { CheckCircle2 } from 'lucide-react';
import { Dialog, DialogContent } from '@/components/ui/dialog';
import { But<PERSON> } from '@/components/ui/button';

interface SuccessDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  title: string;
  message: string;
  onDone?: () => void;
}

const SuccessDialog: React.FC<SuccessDialogProps> = ({
  open,
  onOpenChange,
  title,
  message,
  onDone,
}) => {
  const handleDone = () => {
    onOpenChange(false);
    if (onDone) onDone();
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent>
        <div className="py-6 flex flex-col items-center justify-center text-center">
          <CheckCircle2 className="h-16 w-16 text-green-500 mb-4" />
          <h3 className="text-lg font-semibold mb-2">{title}</h3>
          <p className="text-muted-foreground mb-6">{message}</p>
          <Button onClick={handleDone}>Se<PERSON><PERSON></Button>
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default SuccessDialog;
