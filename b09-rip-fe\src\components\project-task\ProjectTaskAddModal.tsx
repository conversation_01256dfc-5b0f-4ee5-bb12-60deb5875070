// src/components/project-task/ProjectTaskAddModal.tsx
'use client';

import React, { useState, useEffect } from 'react';
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogHeader,
  Di<PERSON>Title,
  DialogFooter,
  DialogDescription,
} from '@/components/ui/dialog';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import { CreateProjectTaskRequest } from '@/types/project-task';
import { Check, X, Loader2, AlertCircle } from 'lucide-react';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { format } from 'date-fns';
import api from '@/lib/api/client';
import { employeeApi } from '@/lib/api/employee';
import { ProjectCombobox } from '@/components/project/ProjectCombobox';

interface Employee {
  id: string;
  fullname: string;
  role?: string;
}

interface Project {
  id: string;
  project_name: string;
}

interface ProjectTaskAddModalProps {
  projectId?: string;
  projectName?: string; // New prop for project name
  isOpen: boolean;
  isCreating: boolean;
  onOpenChange: (open: boolean) => void;
  onSave: (data: CreateProjectTaskRequest) => Promise<void>;
  initialDate?: string; // New prop for initial date
}

const ProjectTaskAddModal: React.FC<ProjectTaskAddModalProps> = ({
  projectId,
  projectName,
  isOpen,
  isCreating,
  onOpenChange,
  onSave,
  initialDate,
}) => {
  // Initialize with initialDate if provided, otherwise use current date
  const today = initialDate || format(new Date(), 'yyyy-MM-dd');

  const [formData, setFormData] = useState<CreateProjectTaskRequest>({
    description: '',
    completion_status: 'not_completed',
    employee_id: '',
    assigned_by: '',
    initial_date: today,
    due_date: today,
    project_id: projectId || '',
    weekly_log_id: '',
  });

  // Confirmation dialog state
  const [showConfirmDialog, setShowConfirmDialog] = useState(false);

  // State for dropdown data
  const [employees, setEmployees] = useState<Employee[]>([]);
  const [projects, setProjects] = useState<Project[]>([]);
  const [loadingEmployees, setLoadingEmployees] = useState(false);
  // State for loading projects (used in fetchProjectData)
  const [, setLoadingProjects] = useState(false);
  const [selectedProjectName, setSelectedProjectName] = useState<string>('');

  // Fetch employees and projects data
  useEffect(() => {
    const fetchEmployeeData = async () => {
      setLoadingEmployees(true);
      try {
        // Fetch employees from employee API
        const response = await employeeApi.getEmployees({
          page: 1,
          pageSize: 100,
        });

        if (response.success && response.data) {
          // Map employee data to the format needed for the dropdown
          const employeeList = response.data.items
            .map((employee) => ({
              id: employee.id,
              fullname: employee.profile.fullname,
              role: employee.profile.role,
            }))
            .filter((employee) => employee.id); // Only include employees with an ID

          setEmployees(employeeList);
        }
      } catch (error) {
        console.error('Error fetching employees:', error);
      } finally {
        setLoadingEmployees(false);
      }
    };

    const fetchProjectData = async () => {
      setLoadingProjects(true);
      try {
        // Fetch projects directly from projects API
        const response = await api.get('/v1/projects?pageSize=100');
        if (response.data && response.data.success) {
          const projectData = response.data.data;

          // Extract project data directly from array
          let projectList: Project[] = [];

          if (Array.isArray(projectData)) {
            projectList = projectData.map((project) => ({
              id: project.id,
              project_name: project.project_name,
            }));
          }

          setProjects(projectList);
        }
      } catch (error) {
        console.error('Error fetching projects:', error);
      } finally {
        setLoadingProjects(false);
      }
    };

    if (isOpen) {
      fetchEmployeeData();
      fetchProjectData();
    }
  }, [isOpen]);

  // Reset form when modal opens or projectId changes
  useEffect(() => {
    if (isOpen) {
      setFormData({
        description: '',
        completion_status: 'not_completed',
        employee_id: '',
        assigned_by: '',
        initial_date: today,
        due_date: today,
        project_id: projectId || '',
        weekly_log_id: '',
      });

      // Set project name from prop if provided, otherwise try to find it in projects array
      if (projectName) {
        setSelectedProjectName(projectName);
      } else {
        setSelectedProjectName('');

        // If projectId is provided, find the project name
        if (projectId && projects.length > 0) {
          const project = projects.find((p) => p.id === projectId);
          if (project) {
            setSelectedProjectName(project.project_name);
          }
        }
      }
    }
  }, [isOpen, projectId, projectName, today, projects]);

  // Handle input changes
  const handleChange = (
    e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>
  ) => {
    const { name, value } = e.target;
    setFormData((prev) => ({ ...prev, [name]: value }));
  };

  // Handle status change
  const handleStatusChange = (value: string) => {
    setFormData((prev) => ({ ...prev, completion_status: value }));
  };

  // Handle employee change
  const handleEmployeeChange = (value: string) => {
    setFormData((prev) => ({ ...prev, employee_id: value }));
  };

  // Handle assigned by change
  const handleAssignedByChange = (value: string) => {
    setFormData((prev) => ({ ...prev, assigned_by: value }));
  };

  // Handle project change
  const handleProjectChange = (projectId: string, projectName: string) => {
    setFormData((prev) => ({ ...prev, project_id: projectId }));
    setSelectedProjectName(projectName);
  };

  // Handle form submission
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    // Show confirmation dialog instead of immediately saving
    setShowConfirmDialog(true);
  };

  // Confirmed save
  const handleConfirmedSave = async () => {
    setShowConfirmDialog(false);
    await onSave(formData);
  };

  return (
    <Dialog open={isOpen} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[500px]">
        <form onSubmit={handleSubmit}>
          <DialogHeader>
            <DialogTitle>Tambah Tugas Proyek Baru</DialogTitle>
          </DialogHeader>

          <div className="space-y-4 my-6">
            <div className="space-y-2">
              <Label htmlFor="description">Deskripsi Tugas</Label>
              <Textarea
                id="description"
                name="description"
                value={formData.description}
                onChange={handleChange}
                disabled={isCreating}
                required
                rows={3}
                placeholder="Masukkan deskripsi tugas"
              />
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="employee_id">Karyawan</Label>
                <Select
                  value={formData.employee_id}
                  onValueChange={handleEmployeeChange}
                  disabled={isCreating || loadingEmployees}
                >
                  <SelectTrigger id="employee_id" className="min-h-10">
                    <SelectValue placeholder="Pilih Karyawan" />
                  </SelectTrigger>
                  <SelectContent className="max-h-60">
                    {loadingEmployees ? (
                      <SelectItem value="loading" disabled>
                        Memuat data...
                      </SelectItem>
                    ) : employees.length === 0 ? (
                      <SelectItem value="empty" disabled>
                        Tidak ada data karyawan
                      </SelectItem>
                    ) : (
                      employees.map((employee) => (
                        <SelectItem key={employee.id} value={employee.id}>
                          {employee.fullname}
                          {employee.role ? ` - ${employee.role}` : ''}
                        </SelectItem>
                      ))
                    )}
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <Label htmlFor="assigned_by">Ditugaskan Oleh</Label>
                <Select
                  value={formData.assigned_by}
                  onValueChange={handleAssignedByChange}
                  disabled={isCreating || loadingEmployees}
                >
                  <SelectTrigger id="assigned_by" className="min-h-10">
                    <SelectValue placeholder="Pilih Penugasan" />
                  </SelectTrigger>
                  <SelectContent className="max-h-60">
                    {loadingEmployees ? (
                      <SelectItem value="loading" disabled>
                        Memuat data...
                      </SelectItem>
                    ) : employees.length === 0 ? (
                      <SelectItem value="empty" disabled>
                        Tidak ada data karyawan
                      </SelectItem>
                    ) : (
                      employees.map((employee) => (
                        <SelectItem key={employee.id} value={employee.id}>
                          {employee.fullname}
                          {employee.role ? ` - ${employee.role}` : ''}
                        </SelectItem>
                      ))
                    )}
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <Label htmlFor="project_id">Proyek</Label>
                <ProjectCombobox
                  value={selectedProjectName}
                  onSelect={handleProjectChange}
                  placeholder="Pilih Proyek"
                  disabled={isCreating || !!projectId}
                />
                {projectId && (
                  <p className="text-xs text-gray-500 mt-1">
                    Proyek sudah dipilih berdasarkan halaman saat ini
                  </p>
                )}
              </div>

              <div className="space-y-2">
                <Label htmlFor="completion_status">Status</Label>
                <Select
                  value={formData.completion_status}
                  onValueChange={handleStatusChange}
                  disabled={isCreating}
                >
                  <SelectTrigger id="completion_status" className="min-h-10">
                    <SelectValue placeholder="Pilih Status" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="not_completed">Belum Dimulai</SelectItem>
                    <SelectItem value="on_progress">Dalam Proses</SelectItem>
                    <SelectItem value="completed">Selesai</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <Label htmlFor="initial_date">Tanggal Mulai</Label>
                <Input
                  id="initial_date"
                  name="initial_date"
                  type="date"
                  value={formData.initial_date}
                  onChange={handleChange}
                  disabled={isCreating}
                  required
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="due_date">Tanggal Tenggat</Label>
                <Input
                  id="due_date"
                  name="due_date"
                  type="date"
                  value={formData.due_date}
                  onChange={handleChange}
                  disabled={isCreating}
                  required
                />
              </div>

              {/* Log Mingguan field hidden as requested */}
            </div>
          </div>

          <DialogFooter>
            <Button
              type="button"
              variant="outline"
              onClick={() => onOpenChange(false)}
              disabled={isCreating}
            >
              <X className="h-4 w-4 mr-2" />
              Batal
            </Button>
            <Button type="submit" disabled={isCreating}>
              {isCreating ? (
                <>
                  <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                  Menyimpan...
                </>
              ) : (
                <>
                  <Check className="h-4 w-4 mr-2" />
                  Simpan
                </>
              )}
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>

      {/* Confirmation Dialog */}
      <Dialog open={showConfirmDialog} onOpenChange={setShowConfirmDialog}>
        <DialogContent className="sm:max-w-[425px]">
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2">
              <AlertCircle className="h-5 w-5 text-amber-500" />
              Konfirmasi Penambahan Tugas
            </DialogTitle>
            <DialogDescription>
              Apakah Anda yakin ingin menambahkan tugas baru ini?
            </DialogDescription>
          </DialogHeader>
          <div className="py-3">
            <p className="text-sm text-gray-700 mb-2">
              <span className="font-medium">Deskripsi:</span>{' '}
              {formData.description?.substring(0, 50)}
              {formData.description && formData.description.length > 50
                ? '...'
                : ''}
            </p>
            {formData.employee_id && (
              <p className="text-sm text-gray-700 mb-2">
                <span className="font-medium">Karyawan:</span>{' '}
                {employees.find((e) => e.id === formData.employee_id)
                  ?.fullname || formData.employee_id}
              </p>
            )}
            {formData.project_id && (
              <p className="text-sm text-gray-700">
                <span className="font-medium">Proyek:</span>{' '}
                {projects.find((p) => p.id === formData.project_id)
                  ?.project_name || formData.project_id}
              </p>
            )}
          </div>
          <DialogFooter>
            <Button
              type="button"
              variant="outline"
              onClick={() => setShowConfirmDialog(false)}
              disabled={isCreating}
            >
              Batal
            </Button>
            <Button
              type="button"
              onClick={handleConfirmedSave}
              disabled={isCreating}
            >
              {isCreating ? (
                <>
                  <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                  Menyimpan...
                </>
              ) : (
                <>
                  <Check className="h-4 w-4 mr-2" />
                  Ya, Tambahkan
                </>
              )}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </Dialog>
  );
};

export default ProjectTaskAddModal;
