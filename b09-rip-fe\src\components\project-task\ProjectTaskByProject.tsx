'use client';

import React, { useState, useEffect } from 'react';
import { useProjectTasksByProject } from '@/hooks/useProjectTaskByProject';
import ProjectTaskTable from '@/components/project-task/ProjectTaskTable';
import { Button } from '@/components/ui/button';
import { Plus } from 'lucide-react';
import { useRouter, usePathname } from 'next/navigation';
import ProjectTaskSearchFilter from '@/components/project-task/ProjectTaskSearchFilter';
import ProjectTaskAddModal from '@/components/project-task/ProjectTaskAddModal';
import { CreateProjectTaskRequest } from '@/types/project-task';
import { toast } from 'sonner';
import { PageTitle } from '@/components/ui/PageTitle';
import { BackButton } from '@/components/ui/BackButton';
import projectTaskApi from '@/lib/api/project-task';
import api from '@/lib/api/client';

interface ProjectTasksByProjectProps {
  projectId: string;
}

const ProjectTasksByProject: React.FC<ProjectTasksByProjectProps> = ({
  projectId,
}) => {
  const router = useRouter();
  const pathname = usePathname();
  const [isAddModalOpen, setIsAddModalOpen] = useState(false);
  const [isCreating, setIsCreating] = useState(false);
  const [isApiStyleUrl, setIsApiStyleUrl] = useState(false);
  const [sortField, setSortField] = useState<string | undefined>(undefined);
  const [sortDirection, setSortDirection] = useState<'asc' | 'desc' | null>(
    null
  );

  useEffect(() => {
    // Check if we're using the API-style URL format
    setIsApiStyleUrl(pathname.startsWith('/v1/project-tasks/project/'));
  }, [pathname]);

  const {
    tasks,
    loading,
    projectName,
    // currentPage, // Not used after DataTable changes
    search,
    status,
    // setCurrentPage, // Not used after DataTable changes
    handleSearchChange,
    handleStatusChange,
    refreshTasks,
    setProjectNameDirectly,
  } = useProjectTasksByProject(projectId);

  // Effect to initialize project name from the filter when component mounts
  useEffect(() => {
    // This will fetch all projects and find the name for the current projectId
    const fetchProjectName = async () => {
      try {
        const response = await api.get('/v1/projects?pageSize=100');
        if (response.data && response.data.success) {
          const projectData = response.data.data;
          let projectList = [];

          if (Array.isArray(projectData)) {
            projectList = projectData;
          } else if (projectData?.items && Array.isArray(projectData.items)) {
            projectList = projectData.items;
          }

          // Find the project with matching ID
          const project = projectList.find(
            (p: { id: string }) => p.id === projectId
          );
          if (project && project.project_name) {
            // Set the project name directly
            setProjectNameDirectly(project.project_name);
          }
        }
      } catch (error) {
        console.error('Error fetching project name:', error);
      }
    };

    fetchProjectName();
  }, [projectId, setProjectNameDirectly]);

  // Handle project filter change - redirects to all tasks or specific project
  const handleProjectChange = (selectedProjectId: string | undefined) => {
    if (selectedProjectId) {
      // Navigate using the appropriate URL format
      if (isApiStyleUrl) {
        router.push(`/v1/project-tasks/project/${selectedProjectId}`);
      } else {
        router.push(`/project/task?projectId=${selectedProjectId}`);
      }
    } else {
      // Go back to all projects tasks page
      if (isApiStyleUrl) {
        router.push('/v1/project-tasks/');
      } else {
        router.push('/project/task');
      }
    }
  };

  // Handle sort
  const handleSort = (field: string, direction: 'asc' | 'desc' | null) => {
    setSortField(field);
    setSortDirection(direction);
  };

  // Handle add task
  const handleAddTask = () => {
    setIsAddModalOpen(true);
  };

  // Handle save new task
  const handleSaveNewTask = async (data: CreateProjectTaskRequest) => {
    setIsCreating(true);

    try {
      // Implementation would use the projectTaskApi
      const response = await projectTaskApi.createProjectTask(data);

      if (response.success && response.data) {
        toast.success('Tugas proyek berhasil ditambahkan');
        setIsAddModalOpen(false);
        refreshTasks();
      } else {
        toast.error(response.message || 'Gagal menambahkan tugas proyek');
      }
    } catch (error: unknown) {
      console.error('Error creating task:', error);
      const errorObj = error as { response?: { status?: number } };

      if (errorObj.response) {
        if (errorObj.response.status === 400) {
          toast.error('Data tidak valid. Silakan periksa kembali input Anda.');
        } else if (errorObj.response.status === 401) {
          toast.error('Sesi expired. Silakan login kembali.');
        } else if (errorObj.response.status === 403) {
          toast.error('Anda tidak memiliki izin untuk menambahkan tugas.');
        } else {
          toast.error('Gagal menambahkan tugas. Silakan coba lagi nanti.');
        }
      } else {
        toast.error('Gagal menambahkan tugas. Silakan coba lagi nanti.');
      }
    } finally {
      setIsCreating(false);
    }
  };

  // Handle back button navigation based on URL format
  const handleBackClick = () => {
    if (isApiStyleUrl) {
      router.push('/v1/project-tasks/');
    } else {
      router.push('/project/task');
    }
  };

  return (
    <div className="container mx-auto py-6 px-6">
      <div className="flex justify-between items-center mb-6">
        <div className="flex items-center gap-6">
          <BackButton onClick={handleBackClick} />
          {/* Only show the title if there are tasks */}
          {tasks.length > 0 && (
            <PageTitle
              title={
                loading
                  ? 'Memuat...'
                  : `Tugas Proyek: ${projectName || 'Tidak ada nama'}`
              }
              subtitle="Kelola dan pantau tugas dalam proyek ini"
            />
          )}
        </div>

        <Button onClick={handleAddTask} className="flex items-center gap-2">
          <Plus className="h-4 w-4" />
          Tambah Tugas
        </Button>
      </div>

      {projectId && projectName && (
        <div className="bg-gray-50 p-4 rounded-lg border border-gray-200 mb-6">
          <h2 className="text-lg font-medium text-gray-800">{projectName}</h2>
          <div className="flex flex-col text-sm text-gray-700 mt-1">
            <p>ID Proyek: {projectId}</p>
            {/* Tambahan opsional jika ingin menampilkan hal lain */}
          </div>
        </div>
      )}

      <div className="bg-white rounded-lg shadow p-6">
        <div className="mb-6">
          <ProjectTaskSearchFilter
            search={search}
            status={status}
            projectId={projectId}
            onSearchChange={handleSearchChange}
            onStatusChange={handleStatusChange}
            onProjectIdChange={handleProjectChange}
            onProjectNameChange={(id, name) => {
              // Set project name directly from the filter
              setProjectNameDirectly(name);
            }}
          />
        </div>

        <div className="overflow-x-auto">
          <ProjectTaskTable
            tasks={tasks}
            loading={loading}
            sortField={sortField}
            sortDirection={sortDirection}
            onSort={handleSort}
          />
        </div>
      </div>

      {/* Add Task Modal */}
      <ProjectTaskAddModal
        projectId={projectId}
        isOpen={isAddModalOpen}
        isCreating={isCreating}
        onOpenChange={setIsAddModalOpen}
        onSave={handleSaveNewTask}
      />
    </div>
  );
};

export default ProjectTasksByProject;
