// ProjectTaskDetail.tsx
'use client';

import React, { useState, useEffect, useCallback } from 'react';
import { useRouter } from 'next/navigation';
import { Edit, Trash2, ArrowLeft, Calendar } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { ProjectTask, UpdateProjectTaskRequest } from '@/types/project-task';
import { projectTaskApi } from '@/lib/api/project-task';
import { formatDate } from '@/lib/utils/date';
import { toast } from 'sonner';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { useRBAC } from '@/hooks/useRBAC';
import ProjectTaskEditModal from './ProjectTaskEditModal';
import useProjectTaskDelete from '@/hooks/useProjectTaskDelete';
import TaskStatusBadge from './TaskStatusBadge';
import { Skeleton } from '@/components/ui/skeleton';
import { PageTitle } from '@/components/ui/PageTitle';
import { BackButton } from '@/components/ui/BackButton';

interface ProjectTaskDetailProps {
  taskId: string;
}

const ProjectTaskDetail: React.FC<ProjectTaskDetailProps> = ({ taskId }) => {
  const router = useRouter();
  const { hasRole } = useRBAC();

  const [task, setTask] = useState<ProjectTask | null>(null);
  const [loading, setLoading] = useState(true);
  const [isEditModalOpen, setIsEditModalOpen] = useState(false);
  const [updating, setUpdating] = useState(false);

  // Use the task delete hook
  const {
    isConfirmOpen,
    loading: deleteLoading,
    confirmDelete,
    cancelDelete,
    deleteTask,
  } = useProjectTaskDelete({
    onSuccess: () => router.push('/project/task'),
  });

  // Fetch task details
  const fetchTaskDetails = useCallback(async () => {
    setLoading(true);
    try {
      const response = await projectTaskApi.getProjectTaskById(taskId);
      if (response.success && response.data) {
        setTask(response.data);
      } else {
        toast.error('Gagal memuat detail tugas proyek');
      }
    } catch (error: unknown) {
      console.error('Error fetching task details:', error);
      const errorObj = error as { message?: string };
      toast.error(errorObj.message || 'Terjadi kesalahan saat memuat detail');
    } finally {
      setLoading(false);
    }
  }, [taskId]);

  useEffect(() => {
    if (taskId) {
      fetchTaskDetails();
    }
  }, [fetchTaskDetails, taskId]);

  // Handle save changes
  const handleSaveChanges = async (data: UpdateProjectTaskRequest) => {
    if (!task) return;

    setUpdating(true);
    try {
      const response = await projectTaskApi.updateProjectTask(task.id, data);
      if (response.success && response.data) {
        setTask(response.data);
        setIsEditModalOpen(false);
        toast.success('Tugas berhasil diperbarui');
      } else {
        toast.error('Gagal memperbarui tugas');
      }
    } catch (error: unknown) {
      console.error('Error updating project task:', error);
      const errorObj = error as { response?: { status?: number } };

      // Error handling for different response codes
      if (errorObj.response) {
        if (errorObj.response.status === 400) {
          toast.error('Data tidak valid. Silakan periksa kembali input Anda.');
        } else if (errorObj.response.status === 401) {
          toast.error('Sesi expired. Silakan login kembali.');
        } else if (errorObj.response.status === 403) {
          toast.error('Anda tidak memiliki izin untuk mengubah tugas ini.');
        } else {
          toast.error('Gagal memperbarui tugas. Silakan coba lagi nanti.');
        }
      } else {
        toast.error('Gagal memperbarui tugas. Silakan coba lagi nanti.');
      }
    } finally {
      setUpdating(false);
    }
  };

  // Check if user has permission to edit/delete
  const canEdit = hasRole(['Operation', 'Manager']);
  const canDelete = hasRole(['Manager']);

  if (loading) {
    return (
      <div className="space-y-10 px-6 py-8 max-w-7xl mx-auto">
        <div className="flex justify-between items-center mb-10">
          <Skeleton className="h-10 w-64" />
          <div className="flex gap-4">
            <Skeleton className="h-12 w-32" />
            <Skeleton className="h-12 w-32" />
          </div>
        </div>
        {[1, 2].map((i) => (
          <Card key={i} className="border border-gray-200 shadow-sm mb-10">
            <CardHeader className="bg-gray-50 border-b border-gray-200 py-5 px-8">
              <Skeleton className="h-7 w-48" />
            </CardHeader>
            <CardContent className="p-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                {[1, 2, 3, 4].map((j) => (
                  <div key={j} className="space-y-2">
                    <Skeleton className="h-5 w-32" />
                    <Skeleton className="h-7 w-full" />
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
    );
  }

  if (!task) {
    return (
      <div className="flex flex-col items-center justify-center p-16 bg-gray-50 rounded-lg border border-gray-200 my-16 mx-auto max-w-3xl">
        <h2 className="text-2xl font-semibold text-[#AB8B3B] mb-6">
          Tugas Proyek tidak ditemukan
        </h2>
        <p className="text-gray-600 mb-10 text-center max-w-lg text-lg">
          Tugas proyek yang Anda cari tidak ada atau telah dihapus.
        </p>
        <Button
          onClick={() => router.push('/project/task')}
          variant="default"
          className="flex items-center gap-3 px-6 py-3 text-base"
        >
          <ArrowLeft className="h-5 w-5" />
          Kembali ke Daftar Tugas
        </Button>
      </div>
    );
  }

  return (
    <div className="space-y-10 py-8 px-4 max-w-7xl mx-auto">
      <div className="flex justify-between items-center pb-8 border-b border-gray-200 mb-4">
        <div className="flex items-center gap-6">
          <BackButton onClick={() => router.push('/project/task')} />
          <PageTitle title="Detail Tugas Proyek" />
        </div>
        <div className="flex gap-4">
          {canEdit && (
            <>
              <Button
                variant="outline"
                size="sm"
                className="flex items-center gap-3 px-5 py-2.5"
                onClick={() => router.push(`/project-tasks/${task.id}/logs`)}
              >
                <Calendar className="h-4 w-4" />
                Logs
              </Button>
              <Button
                size="sm"
                variant="default"
                className="flex items-center gap-3 px-5 py-2.5"
                onClick={() => setIsEditModalOpen(true)}
              >
                <Edit className="h-4 w-4" />
                Edit
              </Button>
            </>
          )}
        </div>
      </div>

      <Card className="border border-gray-200 shadow-sm mb-10">
        <CardHeader className="bg-gray-50 border-b border-gray-200 py-5 px-8">
          <CardTitle className="text-[#AB8B3B] text-lg">
            Informasi Task
          </CardTitle>
        </CardHeader>
        <CardContent className="p-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="space-y-2">
              <p className="text-sm font-medium text-gray-500">Deskripsi</p>
              <p className="text-base font-medium">{task.description}</p>
            </div>
            <div className="space-y-2">
              <p className="text-sm font-medium text-gray-500">Proyek</p>
              <p className="text-base">
                {task.project_name || 'Tidak ada nama proyek'}
              </p>
            </div>
            <div className="space-y-2">
              <p className="text-sm font-medium text-gray-500">Status</p>
              <TaskStatusBadge status={task.completion_status} />
            </div>
          </div>
        </CardContent>
      </Card>

      <Card className="border border-gray-200 shadow-sm mb-10">
        <CardHeader className="bg-gray-50 border-b border-gray-200 py-5 px-8">
          <CardTitle className="text-[#AB8B3B] text-lg">
            Informasi Penugasan
          </CardTitle>
        </CardHeader>
        <CardContent className="p-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="space-y-2">
              <p className="text-sm font-medium text-gray-500">Karyawan</p>
              <p className="text-base">{task.employee_name || '-'}</p>
            </div>
            <div className="space-y-2">
              <p className="text-sm font-medium text-gray-500">
                Ditugaskan Oleh
              </p>
              <p className="text-base">{task.assigned_by_name || '-'}</p>
            </div>

            <div className="space-y-2">
              <p className="text-sm font-medium text-gray-500">Tanggal Mulai</p>
              <p className="text-base">{formatDate(task.initial_date)}</p>
            </div>
            <div className="space-y-2">
              <p className="text-sm font-medium text-gray-500">Tenggat Waktu</p>
              <p className="text-base">{formatDate(task.due_date)}</p>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Delete Dialog */}
      {canDelete && (
        <Dialog open={isConfirmOpen} onOpenChange={cancelDelete}>
          <DialogContent className="max-w-md">
            <DialogHeader className="mb-6">
              <DialogTitle className="text-xl mb-2">
                Hapus Tugas Proyek
              </DialogTitle>
              <DialogDescription className="mt-3 text-gray-600">
                Apakah Anda yakin ingin menghapus tugas ini? Tindakan ini tidak
                dapat dibatalkan.
              </DialogDescription>
            </DialogHeader>
            <DialogFooter className="mt-8 space-x-4">
              <Button
                variant="outline"
                onClick={cancelDelete}
                disabled={deleteLoading}
                className="px-5 py-2"
              >
                Batal
              </Button>
              <Button
                variant="destructive"
                onClick={deleteTask}
                disabled={deleteLoading}
                className="px-5 py-2"
              >
                {deleteLoading ? 'Menghapus...' : 'Hapus'}
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>
      )}

      {/* Edit Modal */}
      {task && (
        <ProjectTaskEditModal
          task={task}
          isOpen={isEditModalOpen}
          isUpdating={updating}
          onOpenChange={setIsEditModalOpen}
          onSave={handleSaveChanges}
        />
      )}

      {/* Action Button (Delete) at the bottom */}
      {canDelete && (
        <div className="flex justify-end mt-16 mb-10 pt-4 border-t border-gray-200">
          <Button
            variant="destructive"
            size="sm"
            className="flex items-center gap-3 px-6 py-2.5"
            onClick={() => confirmDelete(task.id)}
          >
            <Trash2 className="h-5 w-5" />
            Hapus Tugas
          </Button>
        </div>
      )}
    </div>
  );
};

export default ProjectTaskDetail;
