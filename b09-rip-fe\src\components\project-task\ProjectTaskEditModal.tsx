// src/components/project-task/ProjectTaskEditModal.tsx
'use client';

import React, { useState, useEffect } from 'react';
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogHeader,
  <PERSON><PERSON>Title,
  DialogFooter,
  DialogDescription,
} from '@/components/ui/dialog';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import { ProjectTask, UpdateProjectTaskRequest } from '@/types/project-task';
import { Check, X, Loader2, AlertCircle } from 'lucide-react';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { format } from 'date-fns';
import { employeeApi } from '@/lib/api/employee';
import { ProjectCombobox } from '@/components/project/ProjectCombobox';

interface ProjectTaskEditModalProps {
  task: ProjectTask;
  isOpen: boolean;
  isUpdating: boolean;
  onOpenChange: (open: boolean) => void;
  onSave: (data: UpdateProjectTaskRequest) => Promise<void>;
}

interface Employee {
  id: string;
  fullname: string;
  role?: string;
}

const ProjectTaskEditModal: React.FC<ProjectTaskEditModalProps> = ({
  task,
  isOpen,
  isUpdating,
  onOpenChange,
  onSave,
}) => {
  const [formData, setFormData] = useState<UpdateProjectTaskRequest>({
    description: task.description,
    completion_status: task.completion_status,
    employee_id: task.employee_id,
    assigned_by: task.assigned_by,
    initial_date: task.initial_date,
    due_date: task.due_date,
    project_id: task.project_id,
    weekly_log_id: task.weekly_log_id,
  });

  // Confirmation dialog state
  const [showConfirmDialog, setShowConfirmDialog] = useState(false);

  // State for dropdown data
  const [employees, setEmployees] = useState<Employee[]>([]);
  const [loadingEmployees, setLoadingEmployees] = useState(false);
  const [selectedProjectName, setSelectedProjectName] = useState<string>(
    task.project_name || ''
  );

  // Fetch employees data
  useEffect(() => {
    const fetchEmployeeData = async () => {
      setLoadingEmployees(true);
      try {
        // Fetch employees from employee API
        const response = await employeeApi.getEmployees({
          page: 1,
          pageSize: 100,
        });

        if (response.success && response.data) {
          // Map employee data to the format needed for the dropdown
          const employeeList = response.data.items
            .map((employee) => ({
              id: employee.id,
              fullname: employee.profile.fullname,
              role: employee.profile.role,
            }))
            .filter((employee) => employee.id); // Only include employees with an ID

          setEmployees(employeeList);
        }
      } catch (error) {
        console.error('Error fetching employees:', error);
      } finally {
        setLoadingEmployees(false);
      }
    };

    if (isOpen) {
      fetchEmployeeData();
    }
  }, [isOpen]);

  // Reset form when task changes or modal opens
  useEffect(() => {
    if (isOpen) {
      setFormData({
        description: task.description,
        completion_status: task.completion_status,
        employee_id: task.employee_id,
        assigned_by: task.assigned_by,
        initial_date: task.initial_date,
        due_date: task.due_date,
        project_id: task.project_id,
        weekly_log_id: task.weekly_log_id,
      });
      setSelectedProjectName(task.project_name || '');
    }
  }, [task, isOpen]);

  // Handle input changes
  const handleChange = (
    e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>
  ) => {
    const { name, value } = e.target;
    setFormData((prev) => ({ ...prev, [name]: value }));
  };

  // Handle status change
  const handleStatusChange = (value: string) => {
    setFormData((prev) => ({ ...prev, completion_status: value }));
  };

  // Handle employee change
  const handleEmployeeChange = (value: string) => {
    setFormData((prev) => ({ ...prev, employee_id: value }));
  };

  // Handle assigned by change
  const handleAssignedByChange = (value: string) => {
    setFormData((prev) => ({ ...prev, assigned_by: value }));
  };

  // Handle project change
  const handleProjectChange = (projectId: string, projectName: string) => {
    setFormData((prev) => ({ ...prev, project_id: projectId }));
    setSelectedProjectName(projectName);
  };

  // Format date for input fields
  const formatDateForInput = (dateString: string) => {
    if (!dateString) return '';
    try {
      // Try to parse the date and format it as YYYY-MM-DD for input fields
      const date = new Date(dateString);
      return format(date, 'yyyy-MM-dd');
    } catch {
      return '';
    }
  };

  // Handle form submission
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    // Show confirmation dialog instead of immediately saving
    setShowConfirmDialog(true);
  };

  // Confirmed save
  const handleConfirmedSave = async () => {
    setShowConfirmDialog(false);
    await onSave(formData);
  };

  // Unused function - keeping for future reference but commented out
  /*
  const getEmployeeName = (id: string | undefined) => {
    if (!id) return '';
    // Try to find employee in our loaded data
    const employee = employees.find((emp) => emp.id === id);

    // If found, return formatted name with role
    if (employee) {
      return `${employee.fullname}${employee.role ? ` - ${employee.role}` : ''}`;
    }

    // If not found, check if we already have a name in the task data
    // For employee_id
    if (id === task.employee_id && task.employee_name) {
      return task.employee_name;
    }

    // For assigned_by
    if (id === task.assigned_by && task.assigned_by_name) {
      return task.assigned_by_name;
    }

    // Fallback to ID
    return id;
  };
  */

  // Unused function - keeping for future reference but commented out
  /*
  const getProjectName = (id: string | undefined) => {
    if (!id) return '';

    // Try to find project in our loaded data
    const project = projects.find((proj) => proj.id === id);

    // If found, return name
    if (project) {
      return project.project_name;
    }

    // If not found, check if we already have a name in the task data
    if (id === task.project_id && task.project_name) {
      return task.project_name;
    }

    // Fallback to ID
    return id;
  };
  */

  return (
    <Dialog open={isOpen} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[500px]">
        <form onSubmit={handleSubmit}>
          <DialogHeader>
            <DialogTitle>Edit Tugas Proyek</DialogTitle>
          </DialogHeader>

          <div className="space-y-4 my-6">
            <div className="space-y-2">
              <Label htmlFor="description">Deskripsi Tugas</Label>
              <Textarea
                id="description"
                name="description"
                value={formData.description || ''}
                onChange={handleChange}
                disabled={isUpdating}
                required
                rows={3}
                placeholder="Masukkan deskripsi tugas"
              />
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="employee_id">Karyawan</Label>
                <Select
                  value={formData.employee_id || ''}
                  onValueChange={handleEmployeeChange}
                  disabled={isUpdating || loadingEmployees}
                >
                  <SelectTrigger id="employee_id" className="min-h-10">
                    <SelectValue placeholder="Pilih Karyawan" />
                  </SelectTrigger>
                  <SelectContent className="max-h-60">
                    {loadingEmployees ? (
                      <SelectItem value="loading" disabled>
                        Memuat data...
                      </SelectItem>
                    ) : employees.length === 0 ? (
                      <SelectItem value="empty" disabled>
                        Tidak ada data karyawan
                      </SelectItem>
                    ) : (
                      employees.map((employee) => (
                        <SelectItem key={employee.id} value={employee.id}>
                          {employee.fullname}
                          {employee.role ? ` - ${employee.role}` : ''}
                        </SelectItem>
                      ))
                    )}
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <Label htmlFor="assigned_by">Ditugaskan Oleh</Label>
                <Select
                  value={formData.assigned_by || ''}
                  onValueChange={handleAssignedByChange}
                  disabled={isUpdating || loadingEmployees}
                >
                  <SelectTrigger id="assigned_by" className="min-h-10">
                    <SelectValue placeholder="Pilih Penugasan" />
                  </SelectTrigger>
                  <SelectContent className="max-h-60">
                    {loadingEmployees ? (
                      <SelectItem value="loading" disabled>
                        Memuat data...
                      </SelectItem>
                    ) : employees.length === 0 ? (
                      <SelectItem value="empty" disabled>
                        Tidak ada data karyawan
                      </SelectItem>
                    ) : (
                      employees.map((employee) => (
                        <SelectItem key={employee.id} value={employee.id}>
                          {employee.fullname}
                          {employee.role ? ` - ${employee.role}` : ''}
                        </SelectItem>
                      ))
                    )}
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <Label htmlFor="project_id">Proyek</Label>
                <ProjectCombobox
                  value={selectedProjectName}
                  onSelect={handleProjectChange}
                  placeholder="Pilih Proyek"
                  disabled={isUpdating}
                />
                {formData.project_id && task.project_name && (
                  <p className="text-xs text-gray-500 mt-1">
                    Terpilih: {task.project_name}
                  </p>
                )}
              </div>

              <div className="space-y-2">
                <Label htmlFor="completion_status">Status</Label>
                <Select
                  value={formData.completion_status || ''}
                  onValueChange={handleStatusChange}
                  disabled={isUpdating}
                >
                  <SelectTrigger id="completion_status" className="min-h-10">
                    <SelectValue placeholder="Pilih Status" />
                  </SelectTrigger>
                  <SelectContent className="max-h-60">
                    <SelectItem value="not_completed">Belum Dimulai</SelectItem>
                    <SelectItem value="on_progress">Dalam Proses</SelectItem>
                    <SelectItem value="completed">Selesai</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <Label htmlFor="initial_date">Tanggal Mulai</Label>
                <Input
                  id="initial_date"
                  name="initial_date"
                  type="date"
                  value={formatDateForInput(formData.initial_date || '')}
                  onChange={handleChange}
                  disabled={isUpdating}
                  required
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="due_date">Tanggal Tenggat</Label>
                <Input
                  id="due_date"
                  name="due_date"
                  type="date"
                  value={formatDateForInput(formData.due_date || '')}
                  onChange={handleChange}
                  disabled={isUpdating}
                  required
                />
              </div>

              {/* Log Mingguan field hidden as requested */}
            </div>
          </div>

          <DialogFooter>
            <Button
              type="button"
              variant="outline"
              onClick={() => onOpenChange(false)}
              disabled={isUpdating}
            >
              <X className="h-4 w-4 mr-2" />
              Batal
            </Button>
            <Button type="submit" disabled={isUpdating}>
              {isUpdating ? (
                <>
                  <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                  Menyimpan...
                </>
              ) : (
                <>
                  <Check className="h-4 w-4 mr-2" />
                  Simpan
                </>
              )}
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>

      {/* Confirmation Dialog */}
      <Dialog open={showConfirmDialog} onOpenChange={setShowConfirmDialog}>
        <DialogContent className="sm:max-w-[425px]">
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2">
              <AlertCircle className="h-5 w-5 text-amber-500" />
              Konfirmasi Perubahan Tugas
            </DialogTitle>
            <DialogDescription>
              Apakah Anda yakin ingin menyimpan perubahan pada tugas ini?
            </DialogDescription>
          </DialogHeader>
          <div className="py-3">
            <p className="text-sm text-gray-700 mb-2">
              <span className="font-medium">Deskripsi:</span>{' '}
              {formData.description?.substring(0, 50)}
              {formData.description && formData.description.length > 50
                ? '...'
                : ''}
            </p>
            {formData.employee_id && (
              <p className="text-sm text-gray-700 mb-2">
                <span className="font-medium">Karyawan:</span>{' '}
                {employees.find((e) => e.id === formData.employee_id)
                  ?.fullname || formData.employee_id}
              </p>
            )}
            {formData.project_id && (
              <p className="text-sm text-gray-700">
                <span className="font-medium">Proyek:</span>{' '}
                {selectedProjectName ||
                  task.project_name ||
                  formData.project_id}
              </p>
            )}
          </div>
          <DialogFooter>
            <Button
              type="button"
              variant="outline"
              onClick={() => setShowConfirmDialog(false)}
              disabled={isUpdating}
            >
              Batal
            </Button>
            <Button
              type="button"
              onClick={handleConfirmedSave}
              disabled={isUpdating}
            >
              {isUpdating ? (
                <>
                  <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                  Menyimpan...
                </>
              ) : (
                <>
                  <Check className="h-4 w-4 mr-2" />
                  Ya, Perbarui
                </>
              )}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </Dialog>
  );
};

export default ProjectTaskEditModal;
