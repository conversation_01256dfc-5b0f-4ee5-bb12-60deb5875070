'use client';

import React, { useState, useEffect, useCallback } from 'react';
import { Plus } from 'lucide-react';

import { Button } from '@/components/ui/button';
import ProjectTaskTable from './ProjectTaskTable';
import ProjectTaskSearchFilter from './ProjectTaskSearchFilter';
import { projectTaskApi } from '@/lib/api/project-task';
import { ProjectTask, CreateProjectTaskRequest } from '@/types/project-task';
import ProjectTaskAddModal from './ProjectTaskAddModal';
import { toast } from 'sonner';
import { PageTitle } from '@/components/ui/PageTitle';

interface ProjectTaskManagementContentProps {
  projectId?: string; // Optional: if provided, will filter tasks by project
}

const ProjectTaskManagementContent: React.FC<
  ProjectTaskManagementContentProps
> = ({ projectId }) => {
  // Router removed as it's not used in this component

  // State management
  const [originalTasks, setOriginalTasks] = useState<ProjectTask[]>([]); // Menyimpan semua tugas asli
  const [displayedTasks, setDisplayedTasks] = useState<ProjectTask[]>([]); // Tugas yang ditampilkan setelah pencarian
  const [loading, setLoading] = useState(true);

  // Filter state
  const [searchTerm, setSearchTerm] = useState('');
  const [status, setStatus] = useState<string | undefined>(undefined);
  const [selectedProjectId, setSelectedProjectId] = useState<
    string | undefined
  >(projectId);

  // Pagination
  const [currentPage, setCurrentPage] = useState(1);

  // Modal state
  const [isAddModalOpen, setIsAddModalOpen] = useState(false);
  const [isCreating, setIsCreating] = useState(false);

  // Fetch tasks - using useCallback to memoize the function
  const fetchTasks = useCallback(async () => {
    setLoading(true);

    try {
      const response = await projectTaskApi.getProjectTasks({
        project_id: selectedProjectId,
        page: currentPage,
        pageSize: 10,
        completion_status: status,
        // Tidak kirim parameter search ke API
      });

      console.log('API response:', response);

      if (response.success && response.data) {
        // Extract tasks from response
        let taskItems: ProjectTask[] = [];

        if (response.data.items && Array.isArray(response.data.items)) {
          taskItems = response.data.items;
        } else if (
          response.data.items &&
          typeof response.data.items === 'object' &&
          'items' in response.data.items
        ) {
          taskItems = Array.isArray(
            (response.data.items as { items: ProjectTask[] }).items
          )
            ? (response.data.items as { items: ProjectTask[] }).items
            : [];
        } else if (Array.isArray(response.data)) {
          taskItems = response.data;
        }

        console.log('Extracted tasks:', taskItems);

        // Simpan tugas asli
        setOriginalTasks(taskItems);

        // Pagination info is not used in the UI
      } else {
        setOriginalTasks([]);
      }
    } catch (error) {
      console.error('Error fetching tasks:', error);
      setOriginalTasks([]);
    } finally {
      setLoading(false);
    }
  }, [selectedProjectId, currentPage, status]);

  // Fetch tasks when dependencies change
  useEffect(() => {
    fetchTasks();
  }, [fetchTasks]);

  // Apply client-side search filter
  useEffect(() => {
    console.log('Applying search filter with term:', searchTerm);

    if (!searchTerm || searchTerm.trim() === '') {
      // Jika tidak ada pencarian, tampilkan semua tugas
      setDisplayedTasks(originalTasks);
      return;
    }

    const term = searchTerm.toLowerCase().trim();

    // Filter berdasarkan deskripsi dan nama karyawan
    const filtered = originalTasks.filter((task) => {
      const descriptionMatch = (task.description || '')
        .toLowerCase()
        .includes(term);
      const employeeMatch = (task.employee_name || '')
        .toLowerCase()
        .includes(term);

      return descriptionMatch || employeeMatch;
    });

    console.log(
      `Search "${searchTerm}" found ${filtered.length} matching tasks`
    );
    setDisplayedTasks(filtered);
  }, [searchTerm, originalTasks]);

  // Handle search
  const handleSearchChange = (value: string) => {
    console.log('Search changed to:', value);
    setSearchTerm(value);
    setCurrentPage(1); // Reset ke halaman pertama saat pencarian berubah
  };

  // Handle status filter
  const handleStatusChange = (value: string | undefined) => {
    console.log('Status changed to:', value);
    setStatus(value);
    setCurrentPage(1); // Reset ke halaman pertama saat status berubah
  };

  // Handle project change
  const handleProjectChange = (value: string | undefined) => {
    console.log('Project changed to:', value);
    setSelectedProjectId(value);
    setCurrentPage(1); // Reset ke halaman pertama saat proyek berubah
  };

  // Handle add task
  const handleAddTask = () => {
    setIsAddModalOpen(true);
  };

  // Handle save new task
  const handleSaveNewTask = async (data: CreateProjectTaskRequest) => {
    setIsCreating(true);

    try {
      const response = await projectTaskApi.createProjectTask(data);

      if (response.success && response.data) {
        toast.success('Tugas proyek berhasil ditambahkan');
        setIsAddModalOpen(false);
        fetchTasks();
      } else {
        toast.error(response.message || 'Gagal menambahkan tugas proyek');
      }
    } catch (error: unknown) {
      console.error('Error creating task:', error);
      const errorObj = error as { response?: { status?: number } };

      if (errorObj.response) {
        if (errorObj.response.status === 400) {
          toast.error('Data tidak valid. Silakan periksa kembali input Anda.');
        } else if (errorObj.response.status === 401) {
          toast.error('Sesi expired. Silakan login kembali.');
        } else if (errorObj.response.status === 403) {
          toast.error('Anda tidak memiliki izin untuk menambahkan tugas.');
        } else {
          toast.error('Gagal menambahkan tugas. Silakan coba lagi nanti.');
        }
      } else {
        toast.error('Gagal menambahkan tugas. Silakan coba lagi nanti.');
      }
    } finally {
      setIsCreating(false);
    }
  };

  // Find selected project info to display
  const selectedProject = originalTasks.find(
    (task) => task.project_id === selectedProjectId
  );
  const selectedProjectName =
    selectedProject?.project_name || selectedProjectId;

  return (
    <div className="container mx-auto py-6 px-6">
      <div className="flex justify-between items-center mb-6">
        <PageTitle
          title={
            // Only show project title if a project is selected AND there are tasks
            selectedProjectId && displayedTasks.length > 0
              ? `Tugas Proyek: ${selectedProjectName || ''}`
              : 'Manajemen Tugas Proyek'
          }
          subtitle="Kelola dan pantau tugas proyek"
        />
        <Button onClick={handleAddTask} className="flex items-center gap-2">
          <Plus className="h-4 w-4" />
          Tambah Tugas
        </Button>
      </div>

      {/* Blue card removed as requested */}

      <div className="bg-white rounded-lg shadow p-6">
        <div className="mb-6">
          <ProjectTaskSearchFilter
            search={searchTerm}
            status={status}
            projectId={selectedProjectId}
            onSearchChange={handleSearchChange}
            onStatusChange={handleStatusChange}
            onProjectIdChange={handleProjectChange}
          />
        </div>

        <div className="overflow-x-auto">
          <ProjectTaskTable
            tasks={displayedTasks} // Gunakan tugas yang telah difilter
            // Pagination props removed as they're not used in ProjectTaskTable anymore
            // currentPage={currentPage}
            // onPageChange={setCurrentPage}
            loading={loading}
          />
        </div>
      </div>

      {/* Add Task Modal */}
      <ProjectTaskAddModal
        projectId={selectedProjectId}
        isOpen={isAddModalOpen}
        isCreating={isCreating}
        onOpenChange={setIsAddModalOpen}
        onSave={handleSaveNewTask}
      />
    </div>
  );
};

export default ProjectTaskManagementContent;
