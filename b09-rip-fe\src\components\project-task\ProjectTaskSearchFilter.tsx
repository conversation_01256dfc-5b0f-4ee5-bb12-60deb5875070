'use client';

import React, { useEffect, useState } from 'react';
import { SearchFilter, Filter } from '@/components/ui/search-filter';
import api from '@/lib/api/client';

interface ProjectTaskSearchFilterProps {
  search: string;
  status: string | undefined;
  projectId: string | undefined;
  onSearchChange: (value: string) => void;
  onStatusChange: (value: string | undefined) => void;
  onProjectIdChange: (value: string | undefined) => void;
  onProjectNameChange?: (projectId: string, projectName: string) => void; // New callback for project name
}

interface Project {
  id: string;
  project_name: string;
}

const ProjectTaskSearchFilter: React.FC<ProjectTaskSearchFilterProps> = ({
  search,
  status,
  projectId,
  onSearchChange,
  onStatusChange,
  onProjectIdChange,
  onProjectNameChange,
}) => {
  const [projects, setProjects] = useState<Project[]>([]);
  // Loading state used in fetchProjects
  const [, setLoading] = useState(false);

  useEffect(() => {
    const fetchProjects = async () => {
      setLoading(true);
      try {
        const response = await api.get('/v1/projects?pageSize=100');
        if (response.data && response.data.success) {
          const projectData = response.data.data;
          let projectList: Project[] = [];

          if (Array.isArray(projectData)) {
            projectList = projectData.map((project) => ({
              id: project.id,
              project_name: project.project_name,
            }));
          } else if (projectData?.items && Array.isArray(projectData.items)) {
            projectList = projectData.items.map(
              (project: { id: string; project_name: string }) => ({
                id: project.id,
                project_name: project.project_name,
              })
            );
          }

          setProjects(projectList);
        }
      } catch (error) {
        console.error('Error fetching projects:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchProjects();
  }, []);

  const statusFilter: Filter = {
    label: 'Status',
    value: status || '',
    onChange: (value) => {
      onStatusChange(value || undefined);
    },
    options: [
      { value: 'not_completed', label: 'Belum Dimulai' },
      { value: 'on_progress', label: 'Dalam Proses' },
      { value: 'completed', label: 'Selesai' },
    ],
  };

  const projectFilter: Filter = {
    label: 'Proyek',
    value: projectId || '',
    onChange: (value) => {
      onProjectIdChange(value || undefined);

      // If project ID changed and callback exists, find the project name and call the callback
      if (value && value !== 'all' && onProjectNameChange) {
        const selectedProject = projects.find(
          (project) => project.id === value
        );
        if (selectedProject) {
          onProjectNameChange(value, selectedProject.project_name);
        }
      }
    },
    options: projects.map((project) => ({
      value: project.id,
      label: project.project_name,
    })),
  };

  return (
    <SearchFilter
      search={search}
      onSearchChange={onSearchChange}
      filters={[statusFilter, projectFilter]}
      searchPlaceholder="Cari tugas atau nama karyawan..."
    />
  );
};

export default ProjectTaskSearchFilter;
