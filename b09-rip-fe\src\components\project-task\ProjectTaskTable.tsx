'use client';

import React from 'react';
import { Eye } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { ProjectTask } from '@/types/project-task';
import { formatDate } from '@/lib/utils/date';
import { useRouter, usePathname } from 'next/navigation';
import TaskStatusBadge from './TaskStatusBadge';
import { DataTable, SortDirection } from '@/components/ui/data-table';

interface ProjectTaskTableProps {
  tasks: ProjectTask[];
  loading?: boolean;
  sortField?: string;
  sortDirection?: SortDirection;
  onSort?: (field: string, direction: SortDirection) => void;
}

const ProjectTaskTable: React.FC<ProjectTaskTableProps> = ({
  tasks,
  loading = false,
  sortField,
  sortDirection,
  onSort,
}) => {
  const router = useRouter();
  const pathname = usePathname();
  const isApiStyleUrl = pathname.startsWith('/v1/project-tasks');

  const handleProjectClick = (projectId: string) => {
    // Navigate using the appropriate URL format
    if (isApiStyleUrl) {
      router.push(`/v1/project-tasks/project/${projectId}`);
    } else {
      router.push(`/project/task?projectId=${projectId}`);
    }
  };

  const handleViewTaskDetail = (taskId: string) => {
    // Always navigate to the detail page with the correct path structure
    if (isApiStyleUrl) {
      router.push(`/v1/project-tasks/detail/${taskId}`);
    } else {
      // Use the correct path format for task details
      router.push(`/project/task//${taskId}`);
    }
  };

  // Define columns for the DataTable
  const columns = [
    {
      key: 'project',
      header: 'Nama Proyek',
      sortable: false,
      width: '20%',
      render: (task: ProjectTask) => (
        <button
          onClick={() => handleProjectClick(task.project_id)}
          className="font-semibold text-gray-800 underline text-left hover:text-gray-900"
        >
          {task.project_name || task.project_id}
        </button>
      ),
    },
    {
      key: 'description',
      header: 'Deskripsi Tugas',
      sortable: true,
      width: '25%',
      render: (task: ProjectTask) => (
        <span className="truncate block max-w-xs">{task.description}</span>
      ),
    },
    {
      key: 'employee',
      header: 'PIC Tugas',
      sortable: false,
      width: '15%',
      render: (task: ProjectTask) => task.employee_name || task.employee_id,
    },
    {
      key: 'due_date',
      header: 'Tenggat Waktu',
      sortable: true,
      width: '15%',
      render: (task: ProjectTask) => formatDate(task.due_date),
    },
    {
      key: 'status',
      header: 'Status',
      sortable: true,
      width: '15%',
      render: (task: ProjectTask) => (
        <TaskStatusBadge status={task.completion_status} />
      ),
    },
    {
      key: 'actions',
      header: 'Aksi',
      width: '10%',
      render: (task: ProjectTask) => (
        <Button
          onClick={() => handleViewTaskDetail(task.id)}
          variant="outline"
          size="sm"
          className="flex items-center gap-2"
        >
          <Eye className="h-4 w-4" />
          Lihat
        </Button>
      ),
    },
  ];

  return (
    <DataTable
      columns={columns}
      data={tasks}
      keyExtractor={(task) => task.id}
      loading={loading}
      sortField={sortField}
      sortDirection={sortDirection}
      onSort={onSort}
      emptyStateMessage="Tidak ada data tugas proyek."
    />
  );
};

export default ProjectTaskTable;
