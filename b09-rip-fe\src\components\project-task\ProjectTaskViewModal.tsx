'use client';

import React, { useState, useEffect, useCallback } from 'react';
import {
  <PERSON><PERSON>,
  DialogContent,
  Di<PERSON>Header,
  Di<PERSON>Title,
  DialogFooter,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { ProjectTask } from '@/types/project-task';
import { projectTaskApi } from '@/lib/api/project-task';
import { toast } from 'sonner';
import { Edit, X, Loader2, Trash2 } from 'lucide-react';
import TaskStatusBadge from './TaskStatusBadge';
import { formatDate } from '@/lib/utils/date';
import { useRBAC } from '@/hooks/useRBAC';

interface ProjectTaskViewModalProps {
  taskId: string;
  isOpen: boolean;
  onOpenChange: (open: boolean) => void;
  onEdit: (task: ProjectTask) => void;
  onDelete?: (taskId: string) => void;
}

const ProjectTaskViewModal: React.FC<ProjectTaskViewModalProps> = ({
  taskId,
  isOpen,
  onOpenChange,
  onEdit,
  onDelete,
}) => {
  const { hasRole } = useRBAC();
  const canEdit = hasRole(['Operation', 'Manager']);
  const canDelete = hasRole(['Manager']);

  const [task, setTask] = useState<ProjectTask | null>(null);
  const [loading, setLoading] = useState(true);

  // Define fetchTaskDetails with useCallback to prevent unnecessary re-renders
  const fetchTaskDetails = useCallback(async () => {
    setLoading(true);
    try {
      const response = await projectTaskApi.getProjectTaskById(taskId);
      if (response.success && response.data) {
        setTask(response.data);
      } else {
        toast.error('Gagal memuat detail tugas proyek');
      }
    } catch (error) {
      console.error('Error fetching task details:', error);
      toast.error('Terjadi kesalahan saat memuat detail');
    } finally {
      setLoading(false);
    }
  }, [taskId]);

  // Fetch task details when modal opens
  useEffect(() => {
    if (isOpen && taskId) {
      fetchTaskDetails();
    }
  }, [isOpen, taskId, fetchTaskDetails]);

  // Function to get status color
  const getStatusColor = (status: string) => {
    const statusColors: Record<string, string> = {
      not_completed: '#6B7280', // Gray
      on_progress: '#3B82F6', // Blue
      completed: '#10B981', // Green
    };
    return statusColors[status] || '#6B7280';
  };

  return (
    <Dialog open={isOpen} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[600px]">
        <DialogHeader>
          <DialogTitle>Detail Tugas Proyek</DialogTitle>
        </DialogHeader>

        {loading ? (
          <div className="flex justify-center items-center py-8">
            <Loader2 className="h-8 w-8 animate-spin text-gray-500" />
          </div>
        ) : task ? (
          <div className="py-4">
            <div
              className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6 p-4 rounded-md border"
              style={{ borderColor: getStatusColor(task.completion_status) }}
            >
              <div className="space-y-2">
                <p className="text-sm font-medium text-gray-500">Deskripsi</p>
                <p className="text-base font-medium">{task.description}</p>
              </div>
              <div className="space-y-2">
                <p className="text-sm font-medium text-gray-500">Proyek</p>
                <p className="text-base">
                  {task.project_name || 'Tidak ada nama proyek'}
                </p>
              </div>
              <div className="space-y-2">
                <p className="text-sm font-medium text-gray-500">Status</p>
                <div className="flex items-center gap-2">
                  <div
                    className="w-3 h-3 rounded-full"
                    style={{
                      backgroundColor: getStatusColor(task.completion_status),
                    }}
                  />
                  <TaskStatusBadge status={task.completion_status} />
                </div>
              </div>
              <div className="space-y-2">
                <p className="text-sm font-medium text-gray-500">Karyawan</p>
                <p className="text-base">{task.employee_name || '-'}</p>
              </div>
              <div className="space-y-2">
                <p className="text-sm font-medium text-gray-500">
                  Tanggal Mulai
                </p>
                <p className="text-base">
                  {task.initial_date ? formatDate(task.initial_date) : '-'}
                </p>
              </div>
              <div className="space-y-2">
                <p className="text-sm font-medium text-gray-500">
                  Tanggal Tenggat
                </p>
                <p className="text-base">
                  {task.due_date ? formatDate(task.due_date) : '-'}
                </p>
              </div>
              <div className="space-y-2">
                <p className="text-sm font-medium text-gray-500">
                  Ditugaskan Oleh
                </p>
                <p className="text-base">{task.assigned_by_name || '-'}</p>
              </div>
            </div>
          </div>
        ) : (
          <div className="py-4 text-center text-gray-500">
            Tugas tidak ditemukan
          </div>
        )}

        <DialogFooter>
          <Button
            type="button"
            variant="outline"
            onClick={() => onOpenChange(false)}
          >
            <X className="h-4 w-4 mr-2" />
            Tutup
          </Button>
          {task && (
            <>
              {onDelete && canDelete && (
                <Button
                  type="button"
                  variant="destructive"
                  onClick={() => {
                    if (task) onDelete(task.id);
                  }}
                  className="mr-2"
                >
                  <Trash2 className="h-4 w-4 mr-2" />
                  Hapus
                </Button>
              )}
              {canEdit && (
                <Button
                  type="button"
                  onClick={() => {
                    if (task) onEdit(task);
                  }}
                >
                  <Edit className="h-4 w-4 mr-2" />
                  Edit
                </Button>
              )}
            </>
          )}
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

export default ProjectTaskViewModal;
