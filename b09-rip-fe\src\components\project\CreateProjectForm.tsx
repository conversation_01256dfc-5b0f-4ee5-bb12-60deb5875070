'use client';

import React, { useState } from 'react';
import { useRouter } from 'next/navigation';
import { toast } from 'sonner';
import { Button } from '@/components/ui/button';
import { Label } from '@/components/ui/label';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import {
  ProjectCategory,
  ProjectStatus,
  CreateProjectDto,
} from '@/types/project';
import { projectApi } from '@/lib/api/project';
import { EmployeeCombobox } from '@/components/employee/EmployeeCombobox';
import { OrganizationCombobox } from '@/components/organization/OrganizationCombobox';
import { PageTitle } from '@/components/ui/PageTitle';
import { BackButton } from '@/components/ui/BackButton';
import DatePicker from 'react-datepicker';
import 'react-datepicker/dist/react-datepicker.css';
import { format } from 'date-fns';
import { id } from 'date-fns/locale';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogDescription,
  DialogFooter,
} from '@/components/ui/dialog';
import { formatCurrency } from '@/lib/utils/format';
import { formatDate } from '@/lib/utils/date';
import { Table, TableBody, TableCell, TableRow } from '@/components/ui/table';

export function CreateProjectForm() {
  const router = useRouter();
  const [loading, setLoading] = useState(false);
  const [confirmDialogOpen, setConfirmDialogOpen] = useState(false);
  // Use a modified version of CreateProjectDto that allows empty project_category
  type CreateProjectFormData = Omit<CreateProjectDto, 'project_category'> & {
    project_category: ProjectCategory | '';
  };

  const [formData, setFormData] = useState<CreateProjectFormData>({
    project_name: '',
    organization_id: '',
    project_category: '', // Empty to show placeholder
    pic_project: '',
    start_project: '',
    end_project: '',
    status_project: undefined, // Empty to show placeholder
    budget_project: '',
    objectives: '',
  });

  // State for storing organization and employee names for display in confirmation
  const [organizationName, setOrganizationName] = useState<string>('');
  const [employeeName, setEmployeeName] = useState<string>('');

  const handleInputChange = (
    e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>
  ) => {
    const { name, value } = e.target;
    setFormData((prev) => ({
      ...prev,
      [name]: value,
    }));
  };

  const handleSelectChange = (name: string, value: string) => {
    setFormData((prev) => ({
      ...prev,
      [name]: value,
    }));
  };

  const handleEmployeeSelect = (employeeId: string, name?: string) => {
    setFormData((prev) => ({
      ...prev,
      pic_project: employeeId,
    }));
    if (name) {
      setEmployeeName(name);
    }
  };

  const handleOrganizationSelect = (organizationId: string, name?: string) => {
    setFormData((prev) => ({
      ...prev,
      organization_id: organizationId,
    }));
    if (name) {
      setOrganizationName(name);
    }
  };

  const handleStartDateChange = (date: Date | null) => {
    if (date) {
      setFormData((prev) => ({
        ...prev,
        start_project: format(date, 'yyyy-MM-dd'),
      }));
    }
  };

  const handleEndDateChange = (date: Date | null) => {
    if (date) {
      setFormData((prev) => ({
        ...prev,
        end_project: format(date, 'yyyy-MM-dd'),
      }));
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    // Validate required fields
    if (!formData.project_category) {
      toast.error('Silakan pilih kategori proyek');
      return;
    }

    if (!formData.status_project) {
      toast.error('Silakan pilih status proyek');
      return;
    }

    setConfirmDialogOpen(true);
  };

  const confirmSubmit = async () => {
    setLoading(true);
    setConfirmDialogOpen(false);

    try {
      // Convert formData to CreateProjectDto
      const submitData: CreateProjectDto = {
        ...formData,
        // We've already validated that project_category is not empty in handleSubmit
        project_category: formData.project_category as ProjectCategory,
        // We've already validated that status_project is not undefined in handleSubmit
        status_project: formData.status_project as ProjectStatus,
        budget_project: formData.budget_project.toString(),
      };

      const response = await projectApi.createProject(submitData);
      if (response.success) {
        toast.success('Proyek berhasil dibuat');
        router.push('/project');
      } else {
        toast.error(`Gagal membuat proyek: ${response.message}`);
      }
    } catch (error) {
      console.error('Error creating project:', error);
      toast.error('Terjadi kesalahan saat membuat proyek');
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="container mx-auto py-6 px-6">
      <div className="flex items-center gap-4 mb-6">
        <BackButton onClick={() => router.push('/project')} />
        <PageTitle title="Buat Proyek Baru" />
      </div>

      <form onSubmit={handleSubmit}>
        <div className="grid gap-6">
          <Card>
            <CardHeader>
              <CardTitle>Informasi Proyek</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid gap-6 md:grid-cols-2">
                <div className="space-y-2">
                  <Label htmlFor="project_name">Nama Proyek</Label>
                  <Input
                    id="project_name"
                    name="project_name"
                    value={formData.project_name}
                    onChange={handleInputChange}
                    required
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="project_category">Kategori Proyek</Label>
                  <Select
                    value={formData.project_category}
                    onValueChange={(value) =>
                      handleSelectChange('project_category', value)
                    }
                  >
                    <SelectTrigger id="project_category">
                      <SelectValue placeholder="Pilih kategori proyek" />
                    </SelectTrigger>
                    <SelectContent>
                      {Object.values(ProjectCategory).map((category) => (
                        <SelectItem key={category} value={category}>
                          {category}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="organization_id">Klien</Label>
                  <OrganizationCombobox
                    value={formData.organization_id}
                    onSelect={handleOrganizationSelect}
                    placeholder="Pilih klien"
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="pic_project">PIC Proyek</Label>
                  <EmployeeCombobox
                    value={formData.pic_project}
                    onSelect={handleEmployeeSelect}
                    placeholder="Pilih PIC proyek"
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="budget_project">Budget Proyek</Label>
                  <Input
                    id="budget_project"
                    name="budget_project"
                    type="number"
                    min="0"
                    value={formData.budget_project}
                    onChange={handleInputChange}
                    required
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="status_project">Status Proyek</Label>
                  <Select
                    value={formData.status_project}
                    onValueChange={(value) =>
                      handleSelectChange('status_project', value)
                    }
                  >
                    <SelectTrigger id="status_project">
                      <SelectValue placeholder="Pilih status proyek" />
                    </SelectTrigger>
                    <SelectContent>
                      {Object.values(ProjectStatus).map((status) => (
                        <SelectItem key={status} value={status}>
                          {status === ProjectStatus.NOT_STARTED
                            ? 'Not Started'
                            : status === ProjectStatus.IN_PROGRESS
                              ? 'In Progress'
                              : status === ProjectStatus.COMPLETED
                                ? 'Completed'
                                : status === ProjectStatus.CANCELLED
                                  ? 'Cancelled'
                                  : status}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
              </div>

              <div className="mt-6 w-full">
                <div className="grid grid-cols-2 gap-6">
                  <div className="space-y-6 w-full">
                    <Label htmlFor="start_project" className="text-base">
                      Tanggal Mulai
                    </Label>
                    <div className="w-full">
                      <DatePicker
                        selected={
                          formData.start_project
                            ? new Date(formData.start_project)
                            : null
                        }
                        onChange={handleStartDateChange}
                        minDate={new Date()}
                        dateFormat="dd/MM/yyyy"
                        locale={id}
                        className="w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
                        placeholderText="Pilih tanggal mulai"
                        required
                      />
                    </div>
                  </div>

                  <div className="space-y-6 w-full">
                    <Label htmlFor="end_project" className="text-base">
                      Tanggal Selesai
                    </Label>
                    <div className="w-full">
                      <DatePicker
                        selected={
                          formData.end_project
                            ? new Date(formData.end_project)
                            : null
                        }
                        onChange={handleEndDateChange}
                        minDate={
                          formData.start_project
                            ? new Date(formData.start_project)
                            : new Date()
                        }
                        dateFormat="dd/MM/yyyy"
                        locale={id}
                        className="w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
                        placeholderText="Pilih tanggal selesai"
                        required
                      />
                    </div>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Tujuan Proyek</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-2">
                <Textarea
                  id="objectives"
                  name="objectives"
                  value={formData.objectives}
                  onChange={handleInputChange}
                  required
                  className="min-h-[100px]"
                />
              </div>
            </CardContent>
          </Card>

          <div className="flex justify-end gap-3">
            <Button
              type="button"
              variant="outline"
              onClick={() => router.push('/project')}
              disabled={loading}
            >
              Batal
            </Button>
            <Button type="submit" disabled={loading}>
              {loading ? 'Membuat...' : 'Buat Proyek'}
            </Button>
          </div>
        </div>
      </form>

      <Dialog open={confirmDialogOpen} onOpenChange={setConfirmDialogOpen}>
        <DialogContent className="max-w-md max-h-[80vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>Konfirmasi Pembuatan Proyek</DialogTitle>
            <DialogDescription>
              Berikut adalah detail proyek yang akan dibuat. Silakan periksa
              kembali sebelum melanjutkan.
            </DialogDescription>
          </DialogHeader>

          <div className="py-4">
            <Table>
              <TableBody>
                <TableRow>
                  <TableCell className="font-medium">Nama Proyek</TableCell>
                  <TableCell>{formData.project_name || '-'}</TableCell>
                </TableRow>
                <TableRow>
                  <TableCell className="font-medium">Klien</TableCell>
                  <TableCell>{organizationName || '-'}</TableCell>
                </TableRow>
                <TableRow>
                  <TableCell className="font-medium">Kategori Proyek</TableCell>
                  <TableCell>{formData.project_category || '-'}</TableCell>
                </TableRow>
                <TableRow>
                  <TableCell className="font-medium">PIC Proyek</TableCell>
                  <TableCell>{employeeName || '-'}</TableCell>
                </TableRow>
                <TableRow>
                  <TableCell className="font-medium">Tanggal Mulai</TableCell>
                  <TableCell>
                    {formData.start_project
                      ? formatDate(formData.start_project)
                      : '-'}
                  </TableCell>
                </TableRow>
                <TableRow>
                  <TableCell className="font-medium">Tanggal Selesai</TableCell>
                  <TableCell>
                    {formData.end_project
                      ? formatDate(formData.end_project)
                      : '-'}
                  </TableCell>
                </TableRow>
                <TableRow>
                  <TableCell className="font-medium">Status Proyek</TableCell>
                  <TableCell>{formData.status_project || '-'}</TableCell>
                </TableRow>
                <TableRow>
                  <TableCell className="font-medium">Budget Proyek</TableCell>
                  <TableCell>
                    {formData.budget_project
                      ? formatCurrency(Number(formData.budget_project))
                      : '-'}
                  </TableCell>
                </TableRow>
                <TableRow>
                  <TableCell className="font-medium">Tujuan Proyek</TableCell>
                  <TableCell className="max-w-[250px] break-words">
                    {formData.objectives || '-'}
                  </TableCell>
                </TableRow>
              </TableBody>
            </Table>
          </div>

          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => setConfirmDialogOpen(false)}
              disabled={loading}
            >
              Batal
            </Button>
            <Button onClick={confirmSubmit} disabled={loading}>
              {loading ? 'Membuat...' : 'Ya, Buat Proyek'}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}
