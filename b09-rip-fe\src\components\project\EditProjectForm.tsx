'use client';

import React, { useState, useEffect, useCallback } from 'react';
import { useRouter } from 'next/navigation';
import { toast } from 'sonner';
import { But<PERSON> } from '@/components/ui/button';
import { Label } from '@/components/ui/label';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import {
  Project,
  ProjectCategory,
  ProjectStatus,
  UpdateProjectDto,
} from '@/types/project';
import { projectApi } from '@/lib/api/project';
import { EmployeeCombobox } from '@/components/employee/EmployeeCombobox';
import { OrganizationCombobox } from '@/components/organization/OrganizationCombobox';
import { PageTitle } from '@/components/ui/PageTitle';
import { BackButton } from '@/components/ui/BackButton';
import DatePicker from 'react-datepicker';
import 'react-datepicker/dist/react-datepicker.css';
import { format } from 'date-fns';
import { id as idLocale } from 'date-fns/locale';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogDescription,
  DialogFooter,
} from '@/components/ui/dialog';
import { formatCurrency } from '@/lib/utils/format';
import { formatDate } from '@/lib/utils/date';
import { Table, TableBody, TableCell, TableRow } from '@/components/ui/table';

interface EditProjectFormProps {
  id: string;
}

export function EditProjectForm({ id }: EditProjectFormProps) {
  const router = useRouter();
  const [loading, setLoading] = useState(false);
  const [confirmDialogOpen, setConfirmDialogOpen] = useState(false);
  const [project, setProject] = useState<Project | null>(null);
  const [formData, setFormData] = useState<UpdateProjectDto>({
    project_name: '',
    organization_id: '',
    project_category: ProjectCategory.ADMINISTRASI,
    pic_project: '',
    start_project: '',
    end_project: '',
    status_project: ProjectStatus.NOT_STARTED,
    budget_project: '',
    objectives: '',
  });

  // State for storing organization and employee names for display in confirmation
  const [organizationName, setOrganizationName] = useState<string>('');
  const [employeeName, setEmployeeName] = useState<string>('');

  const fetchProject = useCallback(async () => {
    try {
      setLoading(true);
      const response = await projectApi.getProjectById(id);
      if (response.success && response.data) {
        setProject(response.data);

        // The API returns status in the same format as our enum values
        // No need for conversion

        setFormData({
          project_name: response.data.project_name,
          organization_id: response.data.organization_id,
          project_category: response.data.project_category,
          pic_project: response.data.pic_project,
          start_project: response.data.start_project,
          end_project: response.data.end_project,
          status_project: response.data.status_project,
          budget_project: response.data.budget_project,
          objectives: response.data.objectives,
        });

        // Fetch additional data for display purposes
        // We'll need to fetch these separately since we only have IDs
        try {
          // This is a simplified approach - in a real app, you might want to fetch these details
          // For now, we'll just set placeholders that will be updated when the user selects new values
          setOrganizationName('Organisasi Saat Ini');
          setEmployeeName('Karyawan Saat Ini');
        } catch (error) {
          console.error('Error fetching additional data:', error);
        }
      } else {
        toast.error(`Gagal mengambil data proyek: ${response.message}`);
      }
    } catch (error) {
      console.error('Error fetching project:', error);
      toast.error('Terjadi kesalahan saat mengambil data proyek');
    } finally {
      setLoading(false);
    }
  }, [id]);

  useEffect(() => {
    fetchProject();
  }, [fetchProject]);

  const handleInputChange = (
    e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>
  ) => {
    const { name, value } = e.target;
    setFormData((prev) => ({
      ...prev,
      [name]: value,
    }));
  };

  const handleSelectChange = (name: string, value: string) => {
    setFormData((prev) => ({
      ...prev,
      [name]: value,
    }));
  };

  const handleEmployeeSelect = (employeeId: string, name?: string) => {
    setFormData((prev) => ({
      ...prev,
      pic_project: employeeId,
    }));
    if (name) {
      setEmployeeName(name);
    }
  };

  const handleOrganizationSelect = (organizationId: string, name?: string) => {
    setFormData((prev) => ({
      ...prev,
      organization_id: organizationId,
    }));
    if (name) {
      setOrganizationName(name);
    }
  };

  const handleStartDateChange = (date: Date | null) => {
    if (date) {
      setFormData((prev) => ({
        ...prev,
        start_project: format(date, 'yyyy-MM-dd'),
      }));
    }
  };

  const handleEndDateChange = (date: Date | null) => {
    if (date) {
      setFormData((prev) => ({
        ...prev,
        end_project: format(date, 'yyyy-MM-dd'),
      }));
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setConfirmDialogOpen(true);
  };

  const confirmSubmit = async () => {
    setLoading(true);
    setConfirmDialogOpen(false);

    try {
      // No need to convert status_project as the backend accepts the enum values directly
      const submitData = {
        ...formData,
        budget_project: (formData.budget_project ?? '').toString(),
      };

      // Log the data being sent to the API
      console.log('Submitting project data:', submitData);

      const response = await projectApi.updateProject(id, submitData);
      if (response.success) {
        toast.success('Proyek berhasil diperbarui');
        router.push(`/project/${id}`);
      } else {
        toast.error(`Gagal memperbarui proyek: ${response.message}`);
      }
    } catch (error: unknown) {
      console.error('Error updating project:', error);
      // Log the full error object
      if (error && typeof error === 'object' && 'response' in error) {
        const axiosError = error as { response: { data: unknown } };
        console.error('Error response:', axiosError.response.data);
      }
      toast.error('Terjadi kesalahan saat memperbarui proyek');
    } finally {
      setLoading(false);
    }
  };

  if (loading && !project) {
    return (
      <div className="container mx-auto py-6 px-6">
        <div className="flex items-center gap-4 mb-6">
          <BackButton onClick={() => router.push(`/project/${id}`)} />
          <PageTitle title="Edit Proyek" />
        </div>
        <div className="animate-pulse space-y-4">
          <div className="h-8 bg-gray-200 rounded w-1/4"></div>
          <div className="h-32 bg-gray-200 rounded"></div>
        </div>
      </div>
    );
  }

  if (!project) {
    return (
      <div className="container mx-auto py-6 px-6">
        <div className="flex items-center gap-4 mb-6">
          <BackButton onClick={() => router.push('/project')} />
          <PageTitle title="Edit Proyek" />
        </div>
        <div className="text-center py-12">
          <h2 className="text-xl font-semibold text-gray-900">
            Proyek tidak ditemukan
          </h2>
          <p className="mt-2 text-gray-600">
            Proyek yang Anda cari tidak ditemukan atau telah dihapus.
          </p>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto py-6 px-6">
      <div className="flex items-center gap-4 mb-6">
        <BackButton onClick={() => router.push(`/project/${id}`)} />
        <PageTitle title="Edit Proyek" />
      </div>

      <form onSubmit={handleSubmit}>
        <div className="grid gap-6">
          <Card>
            <CardHeader>
              <CardTitle>Informasi Proyek</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid gap-6 md:grid-cols-2">
                <div className="space-y-2">
                  <Label htmlFor="project_name">Nama Proyek</Label>
                  <Input
                    id="project_name"
                    name="project_name"
                    value={formData.project_name}
                    onChange={handleInputChange}
                    required
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="project_category">Kategori Proyek</Label>
                  <Select
                    value={formData.project_category}
                    onValueChange={(value) =>
                      handleSelectChange('project_category', value)
                    }
                  >
                    <SelectTrigger id="project_category">
                      <SelectValue placeholder="Pilih kategori proyek" />
                    </SelectTrigger>
                    <SelectContent>
                      {Object.values(ProjectCategory).map((category) => (
                        <SelectItem key={category} value={category}>
                          {category}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="organization_id">Klien</Label>
                  <OrganizationCombobox
                    value={formData.organization_id || ''}
                    onSelect={handleOrganizationSelect}
                    placeholder="Pilih klien"
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="pic_project">PIC Proyek</Label>
                  <EmployeeCombobox
                    value={formData.pic_project || ''}
                    onSelect={handleEmployeeSelect}
                    placeholder="Pilih PIC proyek"
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="budget_project">Budget Proyek</Label>
                  <Input
                    id="budget_project"
                    name="budget_project"
                    type="number"
                    min="0"
                    value={formData.budget_project}
                    onChange={handleInputChange}
                    required
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="status_project">Status Proyek</Label>
                  <Select
                    value={formData.status_project}
                    onValueChange={(value) =>
                      handleSelectChange('status_project', value)
                    }
                  >
                    <SelectTrigger id="status_project">
                      <SelectValue placeholder="Pilih status proyek" />
                    </SelectTrigger>
                    <SelectContent>
                      {Object.values(ProjectStatus).map((status) => (
                        <SelectItem key={status} value={status}>
                          {status === ProjectStatus.NOT_STARTED
                            ? 'Not Started'
                            : status === ProjectStatus.IN_PROGRESS
                              ? 'In Progress'
                              : status === ProjectStatus.COMPLETED
                                ? 'Completed'
                                : status === ProjectStatus.CANCELLED
                                  ? 'Cancelled'
                                  : status}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
              </div>

              <div className="mt-6 w-full">
                <div className="grid grid-cols-2 gap-6">
                  <div className="space-y-6 w-full">
                    <Label htmlFor="start_project" className="text-base">
                      Tanggal Mulai
                    </Label>
                    <div className="w-full">
                      <DatePicker
                        selected={
                          formData.start_project
                            ? new Date(formData.start_project)
                            : null
                        }
                        onChange={handleStartDateChange}
                        minDate={new Date()}
                        dateFormat="dd/MM/yyyy"
                        locale={idLocale}
                        className="w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
                        placeholderText="Pilih tanggal mulai"
                        required
                      />
                    </div>
                  </div>

                  <div className="space-y-6 w-full">
                    <Label htmlFor="end_project" className="text-base">
                      Tanggal Selesai
                    </Label>
                    <div className="w-full">
                      <DatePicker
                        selected={
                          formData.end_project
                            ? new Date(formData.end_project)
                            : null
                        }
                        onChange={handleEndDateChange}
                        minDate={
                          formData.start_project
                            ? new Date(formData.start_project)
                            : new Date()
                        }
                        dateFormat="dd/MM/yyyy"
                        locale={idLocale}
                        className="w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
                        placeholderText="Pilih tanggal selesai"
                        required
                      />
                    </div>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Tujuan Proyek</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-2">
                <Textarea
                  id="objectives"
                  name="objectives"
                  value={formData.objectives}
                  onChange={handleInputChange}
                  required
                  className="min-h-[100px]"
                />
              </div>
            </CardContent>
          </Card>

          <div className="flex justify-end gap-3">
            <Button
              type="button"
              variant="outline"
              onClick={() => router.push(`/project/${id}`)}
              disabled={loading}
            >
              Batal
            </Button>
            <Button type="submit" disabled={loading}>
              {loading ? 'Menyimpan...' : 'Simpan Perubahan'}
            </Button>
          </div>
        </div>
      </form>

      <Dialog open={confirmDialogOpen} onOpenChange={setConfirmDialogOpen}>
        <DialogContent className="max-w-md max-h-[80vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>Konfirmasi Perubahan Proyek</DialogTitle>
            <DialogDescription>
              Berikut adalah detail perubahan proyek. Silakan periksa kembali
              sebelum melanjutkan.
            </DialogDescription>
          </DialogHeader>

          <div className="py-4">
            <Table>
              <TableBody>
                {project && project.project_name !== formData.project_name && (
                  <TableRow>
                    <TableCell className="font-medium">Nama Proyek</TableCell>
                    <TableCell>
                      <div className="flex flex-col">
                        <span className="text-sm text-muted-foreground line-through">
                          {project.project_name}
                        </span>
                        <span className="font-medium text-green-600">
                          {formData.project_name}
                        </span>
                      </div>
                    </TableCell>
                  </TableRow>
                )}

                {project &&
                  project.organization_id !== formData.organization_id && (
                    <TableRow>
                      <TableCell className="font-medium">Klien</TableCell>
                      <TableCell>
                        <div className="flex flex-col">
                          <span className="text-sm text-muted-foreground line-through">
                            Organisasi Saat Ini
                          </span>
                          <span className="font-medium text-green-600">
                            {organizationName || 'Organisasi Baru'}
                          </span>
                        </div>
                      </TableCell>
                    </TableRow>
                  )}

                {project &&
                  project.project_category !== formData.project_category && (
                    <TableRow>
                      <TableCell className="font-medium">
                        Kategori Proyek
                      </TableCell>
                      <TableCell>
                        <div className="flex flex-col">
                          <span className="text-sm text-muted-foreground line-through">
                            {project.project_category}
                          </span>
                          <span className="font-medium text-green-600">
                            {formData.project_category}
                          </span>
                        </div>
                      </TableCell>
                    </TableRow>
                  )}

                {project && project.pic_project !== formData.pic_project && (
                  <TableRow>
                    <TableCell className="font-medium">PIC Proyek</TableCell>
                    <TableCell>
                      <div className="flex flex-col">
                        <span className="text-sm text-muted-foreground line-through">
                          Karyawan Saat Ini
                        </span>
                        <span className="font-medium text-green-600">
                          {employeeName || 'Karyawan Baru'}
                        </span>
                      </div>
                    </TableCell>
                  </TableRow>
                )}

                {project &&
                  project.start_project !== formData.start_project && (
                    <TableRow>
                      <TableCell className="font-medium">
                        Tanggal Mulai
                      </TableCell>
                      <TableCell>
                        <div className="flex flex-col">
                          <span className="text-sm text-muted-foreground line-through">
                            {formatDate(project.start_project)}
                          </span>
                          <span className="font-medium text-green-600">
                            {formatDate(formData.start_project ?? null)}
                          </span>
                        </div>
                      </TableCell>
                    </TableRow>
                  )}

                {project && project.end_project !== formData.end_project && (
                  <TableRow>
                    <TableCell className="font-medium">
                      Tanggal Selesai
                    </TableCell>
                    <TableCell>
                      <div className="flex flex-col">
                        <span className="text-sm text-muted-foreground line-through">
                          {formatDate(project.end_project)}
                        </span>
                        <span className="font-medium text-green-600">
                          {formatDate(formData.end_project ?? null)}
                        </span>
                      </div>
                    </TableCell>
                  </TableRow>
                )}

                {project &&
                  project.status_project !== formData.status_project && (
                    <TableRow>
                      <TableCell className="font-medium">
                        Status Proyek
                      </TableCell>
                      <TableCell>
                        <div className="flex flex-col">
                          <span className="text-sm text-muted-foreground line-through">
                            {project.status_project}
                          </span>
                          <span className="font-medium text-green-600">
                            {formData.status_project}
                          </span>
                        </div>
                      </TableCell>
                    </TableRow>
                  )}

                {project &&
                  project.budget_project !== formData.budget_project && (
                    <TableRow>
                      <TableCell className="font-medium">
                        Budget Proyek
                      </TableCell>
                      <TableCell>
                        <div className="flex flex-col">
                          <span className="text-sm text-muted-foreground line-through">
                            {formatCurrency(Number(project.budget_project))}
                          </span>
                          <span className="font-medium text-green-600">
                            {formatCurrency(
                              Number(formData.budget_project ?? '0')
                            )}
                          </span>
                        </div>
                      </TableCell>
                    </TableRow>
                  )}

                {project && project.objectives !== formData.objectives && (
                  <TableRow>
                    <TableCell className="font-medium">Tujuan Proyek</TableCell>
                    <TableCell className="max-w-[250px] break-words">
                      <div className="flex flex-col">
                        <span className="text-sm text-muted-foreground line-through">
                          {project.objectives.length > 100
                            ? `${project.objectives.substring(0, 100)}...`
                            : project.objectives}
                        </span>
                        <span className="font-medium text-green-600">
                          {(formData.objectives ?? '').length > 100
                            ? `${(formData.objectives ?? '').substring(0, 100)}...`
                            : (formData.objectives ?? '')}
                        </span>
                      </div>
                    </TableCell>
                  </TableRow>
                )}

                {/* If no changes were made, show a message */}
                {project &&
                  project.project_name === formData.project_name &&
                  project.organization_id === formData.organization_id &&
                  project.project_category === formData.project_category &&
                  project.pic_project === formData.pic_project &&
                  project.start_project === formData.start_project &&
                  project.end_project === formData.end_project &&
                  project.status_project === formData.status_project &&
                  project.budget_project === formData.budget_project &&
                  project.objectives === formData.objectives && (
                    <TableRow>
                      <TableCell colSpan={2} className="text-center py-4">
                        Tidak ada perubahan yang dilakukan pada proyek ini.
                      </TableCell>
                    </TableRow>
                  )}
              </TableBody>
            </Table>
          </div>

          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => setConfirmDialogOpen(false)}
              disabled={loading}
            >
              Batal
            </Button>
            <Button onClick={confirmSubmit} disabled={loading}>
              {loading ? 'Menyimpan...' : 'Ya, Simpan Perubahan'}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}
