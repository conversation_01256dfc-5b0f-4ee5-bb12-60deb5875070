// ProjectCombobox Component - Updated

import * as React from 'react';
import { Check, ChevronsUpDown } from 'lucide-react';
import { cn } from '@/lib/utils';
import { Button } from '@/components/ui/button';
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
} from '@/components/ui/command';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover';
import { Project } from '@/types/project';
import { projectApi } from '@/lib/api/project';

interface ProjectComboboxProps {
  value: string; // project_name
  onSelect: (projectId: string, projectName: string) => void;
  placeholder?: string;
  className?: string;
  disabled?: boolean;
}

export function ProjectCombobox({
  value,
  onSelect,
  placeholder = 'Pilih proyek...',
  className,
  disabled = false,
}: ProjectComboboxProps) {
  const [open, setOpen] = React.useState(false);
  const [projects, setProjects] = React.useState<Project[]>([]);
  const [loading, setLoading] = React.useState(false);
  const [searchQuery, setSearchQuery] = React.useState('');

  // Load KPI projects on component mount
  React.useEffect(() => {
    if (disabled && value) return;

    const fetchProjects = async () => {
      setLoading(true);
      try {
        const response = await projectApi.getProjects({
          page: 1,
          pageSize: 1000,
          search: searchQuery,
        });

        if (response.success && response.data) {
          const data = response.data.items || response.data;

          if (Array.isArray(data)) {
            const uniqueProjects = Array.from(
              new Map(data.map((project) => [project.id, project])).values()
            );
            setProjects(uniqueProjects);
          } else {
            console.error('Unexpected response structure:', response.data);
            setProjects([]);
          }
        } else {
          console.error('API response not successful:', response);
          setProjects([]);
        }
      } catch (error) {
        console.error('Error fetching projects:', error);
        setProjects([]);
      } finally {
        setLoading(false);
      }
    };

    fetchProjects();
  }, [searchQuery, disabled, value]);

  const handleSearchChange = React.useCallback((value: string) => {
    const timeoutId = setTimeout(() => {
      setSearchQuery(value);
    }, 300);

    return () => clearTimeout(timeoutId);
  }, []);

  const selectedProject = projects.find((project) => project.project_name === value);
  const displayValue = selectedProject ? selectedProject.project_name : value || placeholder;

  const handleOpenChange = (open: boolean) => {
    if (!disabled) {
      setOpen(open);
    }
  };

  return (
    <Popover open={open} onOpenChange={handleOpenChange}>
      <PopoverTrigger asChild>
        <Button
          variant="outline"
          role="combobox"
          aria-expanded={open}
          className={cn('w-full justify-between', disabled && 'opacity-70 cursor-not-allowed', className)}
          disabled={disabled}
        >
          {displayValue}
          <ChevronsUpDown className={cn('opacity-50 h-4 w-4 ml-2 shrink-0', disabled && 'opacity-30')} />
        </Button>
      </PopoverTrigger>
      {!disabled && (
        <PopoverContent className="w-full p-0">
          <Command>
            <CommandInput placeholder="Cari proyek..." className="h-9" onValueChange={handleSearchChange} />
            <CommandList>
              {loading ? (
                <CommandEmpty>Loading...</CommandEmpty>
              ) : projects.length === 0 ? (
                <CommandEmpty>Proyek tidak ditemukan.</CommandEmpty>
              ) : (
                <CommandGroup>
                  {projects.map((project) => (
                    <CommandItem
                      key={project.id}
                      value={project.project_name}
                      onSelect={() => {
                        onSelect(project.id, project.project_name);
                        setOpen(false);
                      }}
                    >
                      <span>{project.project_name}</span>
                      <Check className={cn('ml-auto h-4 w-4', value === project.project_name ? 'opacity-100' : 'opacity-0')} />
                    </CommandItem>
                  ))}
                </CommandGroup>
              )}
            </CommandList>
          </Command>
        </PopoverContent>
      )}
    </Popover>
  );
}
