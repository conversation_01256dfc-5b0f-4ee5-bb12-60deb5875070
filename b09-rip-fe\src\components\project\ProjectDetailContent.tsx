'use client';

import { useState, useEffect, useCallback } from 'react';
import { useRouter } from 'next/navigation';
import { toast } from 'sonner';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { PageTitle } from '@/components/ui/PageTitle';
import { BackButton } from '@/components/ui/BackButton';
import { Project, ProjectStatus } from '@/types/project';
import { projectApi } from '@/lib/api/project';
import { organizationApi } from '@/lib/api/organization';
import { Organization } from '@/types/organization';
import { formatCurrency } from '@/lib/utils/format';
import { useRBAC } from '@/hooks/useRBAC';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogDescription,
  DialogFooter,
} from '@/components/ui/dialog';

interface ProjectDetailContentProps {
  id: string;
}

const formatDate = (dateString: string) => {
  if (!dateString) return '-';
  const date = new Date(dateString);
  const months = [
    'Januari',
    'Februari',
    'Maret',
    'April',
    'Mei',
    'Juni',
    'Juli',
    'Agustus',
    'September',
    'Oktober',
    'November',
    'Desember',
  ];
  return `${date.getDate()} ${months[date.getMonth()]} ${date.getFullYear()}`;
};

export function ProjectDetailContent({ id }: ProjectDetailContentProps) {
  const router = useRouter();
  const { hasRole } = useRBAC();
  const canEdit = hasRole(['Operation', 'Manager']);
  const canDelete = hasRole(['Manager']);
  const [project, setProject] = useState<Project | null>(null);
  const [organization, setOrganization] = useState<Organization | null>(null);
  const [loading, setLoading] = useState(true);
  const [organizationLoading, setOrganizationLoading] = useState(false);
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [deleteLoading, setDeleteLoading] = useState(false);

  const fetchOrganization = useCallback(async (organizationId: string) => {
    if (!organizationId) return;

    try {
      setOrganizationLoading(true);
      const response =
        await organizationApi.getOrganizationById(organizationId);
      if (response.success && response.data) {
        setOrganization(response.data);
      } else {
        setOrganization(null);
        console.error(`Failed to fetch organization: ${response.message}`);
      }
    } catch (error) {
      console.error('Error fetching organization:', error);
      setOrganization(null);
    } finally {
      setOrganizationLoading(false);
    }
  }, []);

  const fetchProject = useCallback(async () => {
    try {
      setLoading(true);
      const response = await projectApi.getProjectById(id);
      if (response.success && response.data) {
        setProject(response.data);
        // Fetch organization details after getting project
        fetchOrganization(response.data.organization_id);
      } else {
        setProject(null);
        toast.error(`Gagal mengambil data proyek: ${response.message}`);
      }
    } catch (error) {
      console.error('Error fetching project:', error);
      setProject(null);
      toast.error('Terjadi kesalahan saat mengambil data proyek');
    } finally {
      setLoading(false);
    }
  }, [id, fetchOrganization]);

  useEffect(() => {
    fetchProject();
  }, [fetchProject]);

  const handleDelete = async () => {
    if (!canDelete) {
      toast.error('Anda tidak memiliki izin untuk menghapus proyek');
      setDeleteDialogOpen(false);
      return;
    }

    try {
      setDeleteLoading(true);
      const response = await projectApi.deleteProject(id);
      if (response.success) {
        toast.success('Proyek berhasil dihapus');
        router.push('/project');
      } else {
        toast.error(`Gagal menghapus proyek: ${response.message}`);
      }
    } catch (error) {
      console.error('Error deleting project:', error);
      toast.error('Terjadi kesalahan saat menghapus proyek');
    } finally {
      setDeleteLoading(false);
      setDeleteDialogOpen(false);
    }
  };

  if (loading) {
    return (
      <div className="container mx-auto py-6 px-6">
        <div className="flex items-center gap-4 mb-6">
          <BackButton onClick={() => router.push(`/project/${id}`)} />
          <PageTitle title="Detail Proyek" />
        </div>
        <div className="animate-pulse space-y-4">
          <div className="h-8 bg-gray-200 rounded w-1/4"></div>
          <div className="h-32 bg-gray-200 rounded"></div>
        </div>
      </div>
    );
  }

  if (!project) {
    return (
      <div className="container mx-auto py-6 px-6">
        <div className="flex items-center gap-4 mb-6">
          <BackButton onClick={() => router.push(`/project/${id}`)} />
          <PageTitle title="Detail Proyek" />
        </div>
        <div className="text-center py-12">
          <h2 className="text-xl font-semibold text-gray-900">
            Proyek tidak ditemukan
          </h2>
          <p className="mt-2 text-gray-600">
            Proyek yang Anda cari tidak ditemukan atau telah dihapus.
          </p>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto py-6 px-6">
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center gap-4">
          <BackButton onClick={() => router.push(`/project/${id}`)} />
          <PageTitle title="Detail Proyek" />
        </div>
        <div className="flex gap-3">
          {canEdit && (
            <Button
              variant="outline"
              onClick={() => router.push(`/project/${id}/edit`)}
            >
              Edit
            </Button>
          )}
          {canDelete && (
            <Button
              variant="destructive"
              onClick={() => setDeleteDialogOpen(true)}
            >
              Hapus
            </Button>
          )}
        </div>
      </div>

      <div className="grid gap-6">
        <Card>
          <CardHeader>
            <CardTitle>Informasi Proyek</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid gap-4 md:grid-cols-2">
              <div>
                <h3 className="font-medium text-gray-500">Nama Proyek</h3>
                <p className="mt-1">{project.project_name}</p>
              </div>
              <div>
                <h3 className="font-medium text-gray-500">Klien</h3>
                {organizationLoading ? (
                  <p className="mt-1 text-gray-400">Memuat data klien...</p>
                ) : organization ? (
                  <p className="mt-1">{organization.name}</p>
                ) : (
                  <p className="mt-1 text-gray-400">Klien tidak ditemukan</p>
                )}
              </div>
              <div>
                <h3 className="font-medium text-gray-500">Kategori</h3>
                <p className="mt-1">{project.project_category}</p>
              </div>
              <div>
                <h3 className="font-medium text-gray-500">Status</h3>
                <Badge
                  className={`mt-1 ${
                    project.status_project === ProjectStatus.COMPLETED
                      ? 'bg-green-100 text-green-800'
                      : project.status_project === ProjectStatus.IN_PROGRESS
                        ? 'bg-blue-100 text-blue-800'
                        : project.status_project === ProjectStatus.CANCELLED
                          ? 'bg-red-100 text-red-800'
                          : 'bg-gray-100 text-gray-800'
                  }`}
                >
                  {project.status_project === ProjectStatus.NOT_STARTED
                    ? 'Not Started'
                    : project.status_project === ProjectStatus.IN_PROGRESS
                      ? 'In Progress'
                      : project.status_project === ProjectStatus.COMPLETED
                        ? 'Completed'
                        : project.status_project === ProjectStatus.CANCELLED
                          ? 'Cancelled'
                          : project.status_project}
                </Badge>
              </div>
              <div>
                <h3 className="font-medium text-gray-500">Budget</h3>
                <p className="mt-1">
                  {formatCurrency(Number(project.budget_project))}
                </p>
              </div>
              <div>
                <h3 className="font-medium text-gray-500">Tanggal Mulai</h3>
                <p className="mt-1">{formatDate(project.start_project)}</p>
              </div>
              <div>
                <h3 className="font-medium text-gray-500">Tanggal Selesai</h3>
                <p className="mt-1">{formatDate(project.end_project)}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Tujuan Proyek</CardTitle>
          </CardHeader>
          <CardContent>
            <p className="whitespace-pre-wrap">{project.objectives}</p>
          </CardContent>
        </Card>
      </div>

      <Dialog open={deleteDialogOpen} onOpenChange={setDeleteDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Hapus Proyek</DialogTitle>
            <DialogDescription>
              Apakah Anda yakin ingin menghapus proyek ini? Tindakan ini tidak
              dapat dibatalkan.
            </DialogDescription>
          </DialogHeader>
          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => setDeleteDialogOpen(false)}
              disabled={deleteLoading}
            >
              Batal
            </Button>
            <Button
              variant="destructive"
              onClick={handleDelete}
              disabled={deleteLoading}
            >
              {deleteLoading ? 'Menghapus...' : 'Hapus'}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}
