import React from 'react';
import { useRouter } from 'next/navigation';
import { ProjectTable } from './ProjectTable';
import { ProjectSearchFilter } from './ProjectSearchFilter';
import { PageTitle } from '@/components/ui/PageTitle';
import { useProjectManagement } from '@/hooks/useProjectManagement';
import { Button } from '@/components/ui/button';
import { Plus } from 'lucide-react';
import { useRBAC } from '@/hooks/useRBAC';

export function ProjectManagementContent() {
  const router = useRouter();
  const { hasRole } = useRBAC();
  const {
    projects,
    loading,
    // totalItems, // Not used after DataTable changes
    // currentPage, // Not used after DataTable changes
    search,
    projectCategory,
    projectStatus,
    handleSearchChange,
    handleProjectCategoryChange,
    handleProjectStatusChange,
    handleViewDetail,
    // setCurrentPage, // Not used after DataTable changes
    // refreshProjects, // Not used after DataTable changes
  } = useProjectManagement();

  return (
    <div className="container mx-auto py-6 px-6">
      <div className="flex justify-between items-center mb-6">
        <PageTitle title="Manajemen Proyek" />
        <div className="flex gap-3">
          <Button
            variant="outline"
            onClick={() => router.push('/project/dashboard')}
          >
            Dashboard Proyek
          </Button>
          {hasRole(['Operation', 'Manager']) && (
            <Button
              onClick={() => router.push('/project/create')}
              leftIcon={<Plus className="h-4 w-4" />}
            >
              Buat Proyek
            </Button>
          )}
        </div>
      </div>

      <div className="bg-white rounded-lg shadow p-6">
        <div className="mb-6">
          <ProjectSearchFilter
            search={search}
            onSearch={handleSearchChange}
            projectCategory={projectCategory}
            onProjectCategoryChange={handleProjectCategoryChange}
            projectStatus={projectStatus}
            onProjectStatusChange={handleProjectStatusChange}
          />
        </div>

        <div className="overflow-x-auto mb-4">
          <ProjectTable
            projects={projects}
            loading={loading}
            onRowClick={handleViewDetail}
            // Pagination props removed as they're not used in ProjectTable anymore
            // currentPage={currentPage}
            // itemsPerPage={10}
            // onPageChange={setCurrentPage}
            // totalProjects={totalItems}
            // onDelete prop removed as it's not used in ProjectTable
            // onDelete={refreshProjects}
          />
        </div>
      </div>
    </div>
  );
}
