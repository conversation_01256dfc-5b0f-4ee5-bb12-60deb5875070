import { ProjectCategory, ProjectStatus } from '@/types/project';
import { SearchFilter, Filter } from '@/components/ui/search-filter';

interface ProjectSearchFilterProps {
  search: string;
  onSearch: (value: string) => void;
  projectCategory: ProjectCategory | undefined;
  onProjectCategoryChange: (value: ProjectCategory | undefined) => void;
  projectStatus: ProjectStatus | undefined;
  onProjectStatusChange: (value: ProjectStatus | undefined) => void;
}

export function ProjectSearchFilter({
  search,
  onSearch,
  projectCategory,
  onProjectCategoryChange,
  projectStatus,
  onProjectStatusChange,
}: ProjectSearchFilterProps) {
  const filters: (Filter & { key: string })[] = [
    {
      key: 'project_category',
      label: 'Kategori',
      value: projectCategory,
      options: Object.values(ProjectCategory).map((category) => ({
        label: category,
        value: category,
      })),
      onChange: (value) =>
        onProjectCategoryChange(value as ProjectCategory | undefined),
    },
    {
      key: 'status_project',
      label: 'Status',
      value: projectStatus,
      options: Object.values(ProjectStatus).map((status) => ({
        label: status,
        value: status,
      })),
      onChange: (value) =>
        onProjectStatusChange(value as ProjectStatus | undefined),
    },
  ];

  return (
    <SearchFilter
      search={search}
      onSearchChange={onSearch}
      filters={filters}
      searchPlaceholder="Cari nama proyek..."
    />
  );
}
