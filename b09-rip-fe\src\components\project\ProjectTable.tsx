import { Project, ProjectStatus } from '@/types/project';
import { Eye } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { DataTable } from '@/components/ui/data-table';
import { format } from 'date-fns';
import { id } from 'date-fns/locale';

interface ProjectTableProps {
  projects: Project[];
  loading: boolean;
  onRowClick: (id: string) => void;
  // Pagination props removed as they're not used in DataTable anymore
  // currentPage?: number;
  // itemsPerPage?: number;
  // onPageChange?: (page: number) => void;
  // totalProjects?: number;
  sortField?: string;
  sortDirection?: 'asc' | 'desc' | null;
  onSort?: (field: string, direction: 'asc' | 'desc' | null) => void;
}

export function ProjectTable({
  projects = [],
  loading,
  onRowClick,
  // Pagination props removed as they're not used in DataTable anymore
  // currentPage = 1,
  // itemsPerPage = 10,
  // onPageChange,
  sortField,
  sortDirection,
  onSort,
}: ProjectTableProps) {
  // Define columns configuration for DataTable
  const columns = [
    {
      key: 'project_name',
      header: '<PERSON><PERSON>',
      sortable: true,
      render: (project: Project) => (
        <span className="font-medium">{project.project_name}</span>
      ),
    },
    {
      key: 'project_category',
      header: 'Kategori',
      sortable: true,
      render: (project: Project) => (
        <span className="text-gray-600">{project.project_category}</span>
      ),
    },
    {
      key: 'start_project',
      header: 'Tanggal Mulai',
      sortable: true,
      render: (project: Project) => (
        <span className="text-gray-600">
          {format(new Date(project.start_project), 'dd MMM yyyy', {
            locale: id,
          })}
        </span>
      ),
    },
    {
      key: 'end_project',
      header: 'Tanggal Selesai',
      sortable: true,
      render: (project: Project) => (
        <span className="text-gray-600">
          {format(new Date(project.end_project), 'dd MMM yyyy', {
            locale: id,
          })}
        </span>
      ),
    },
    {
      key: 'status_project',
      header: 'Status',
      sortable: true,
      render: (project: Project) => (
        <span
          className={`px-2 py-1 rounded-full text-xs font-medium ${
            project.status_project === ProjectStatus.COMPLETED
              ? 'bg-green-100 text-green-800'
              : project.status_project === ProjectStatus.IN_PROGRESS
                ? 'bg-blue-100 text-blue-800'
                : project.status_project === ProjectStatus.CANCELLED
                  ? 'bg-red-100 text-red-800'
                  : 'bg-gray-100 text-gray-800'
          }`}
        >
          {project.status_project === ProjectStatus.NOT_STARTED
            ? 'Not Started'
            : project.status_project === ProjectStatus.IN_PROGRESS
              ? 'In Progress'
              : project.status_project === ProjectStatus.COMPLETED
                ? 'Completed'
                : project.status_project === ProjectStatus.CANCELLED
                  ? 'Cancelled'
                  : project.status_project}
        </span>
      ),
    },
    {
      key: 'budget_project',
      header: 'Budget',
      sortable: true,
      render: (project: Project) => (
        <span className="text-gray-600">{project.budget_project}</span>
      ),
    },
    {
      key: 'actions',
      header: 'Aksi',
      width: '120px',
      render: (project: Project) => (
        <Button
          variant="outline"
          size="sm"
          onClick={(e) => {
            e.stopPropagation();
            onRowClick(project.id);
          }}
        >
          <Eye className="h-4 w-4 mr-2" />
          Lihat
        </Button>
      ),
    },
  ];

  // Empty state message
  const emptyStateMessage =
    (projects?.length === 0 || !projects) && !loading
      ? 'Tidak ada proyek ditemukan. Coba sesuaikan filter pencarian Anda.'
      : 'Tidak ada data untuk ditampilkan';

  return (
    <DataTable
      columns={columns}
      data={projects || []}
      keyExtractor={(project) => project.id}
      loading={loading}
      // Pagination props removed as they're not used in DataTable anymore
      // currentPage={currentPage}
      // itemsPerPage={itemsPerPage}
      // onPageChange={onPageChange}
      sortField={sortField}
      sortDirection={sortDirection}
      onSort={onSort}
      emptyStateMessage={emptyStateMessage}
      // showPagination prop removed as it's not used in DataTable anymore
      // showPagination={false}
    />
  );
}
