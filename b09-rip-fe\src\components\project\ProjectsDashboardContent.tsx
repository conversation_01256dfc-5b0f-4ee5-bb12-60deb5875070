'use client';

import { useRouter } from 'next/navigation';
import {
  BarChart2,
  Target,
  CheckSquare,
  AlertCircle,
  RefreshCw,
  Clock,
  TrendingUp,
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { PageTitle } from '@/components/ui/PageTitle';
import { Skeleton } from '@/components/ui/skeleton';
import { formatDate } from '@/lib/utils/date';
import { useProjectsDashboard } from '@/hooks/useProjectsDashboard';
import {
  ChartContainer,
  ChartLegend,
  ChartLegendContent,
  ChartTooltip,
  ChartTooltipContent,
} from '@/components/ui/chart';
import * as RechartsPrimitive from 'recharts';
import { LabelList, Label } from 'recharts';

export function ProjectsDashboardContent() {
  const router = useRouter();
  const { dashboardData, loading, error, refreshDashboard } =
    useProjectsDashboard();

  // Chart configurations
  const statusChartConfig = {
    not_started: {
      label: 'Belum Dimulai',
      theme: { light: '#9CA3AF', dark: '#9CA3AF' }, // Gray
      fill: '#9CA3AF',
    },
    in_progress: {
      label: 'Dalam Proses',
      theme: { light: '#3B82F6', dark: '#3B82F6' }, // Blue
      fill: '#3B82F6',
    },
    completed: {
      label: 'Selesai',
      theme: { light: '#10B981', dark: '#10B981' }, // Green
      fill: '#10B981',
    },
    cancelled: {
      label: 'Dibatalkan',
      theme: { light: '#EF4444', dark: '#EF4444' }, // Red
      fill: '#EF4444',
    },
  };

  const categoryChartConfig = {
    category1: {
      theme: { light: 'hsl(var(--chart-1))', dark: 'hsl(var(--chart-1))' },
      fill: 'hsl(var(--chart-1))',
    },
    category2: {
      theme: { light: 'hsl(var(--chart-2))', dark: 'hsl(var(--chart-2))' },
      fill: 'hsl(var(--chart-2))',
    },
    category3: {
      theme: { light: 'hsl(var(--chart-3))', dark: 'hsl(var(--chart-3))' },
      fill: 'hsl(var(--chart-3))',
    },
    category4: {
      theme: { light: 'hsl(var(--chart-4))', dark: 'hsl(var(--chart-4))' },
      fill: 'hsl(var(--chart-4))',
    },
    category5: {
      theme: { light: 'hsl(var(--chart-5))', dark: 'hsl(var(--chart-5))' },
      fill: 'hsl(var(--chart-5))',
    },
  };

  const picChartConfig = {
    pic: {
      theme: { light: '#AB8B3B', dark: '#AB8B3B' }, // Amber-500
      fill: '#AB8B3B',
    },
    label: {
      color: 'hsl(var(--background))',
    },
  };

  const kpiChartConfig = {
    not_started: {
      theme: { light: 'hsl(var(--chart-1))', dark: 'hsl(var(--chart-1))' },
      fill: 'hsl(var(--chart-1))',
    },
    in_progress: {
      theme: { light: 'hsl(var(--chart-2))', dark: 'hsl(var(--chart-2))' },
      fill: 'hsl(var(--chart-2))',
    },
    completed_below_target: {
      theme: { light: 'hsl(var(--chart-3))', dark: 'hsl(var(--chart-3))' },
      fill: 'hsl(var(--chart-3))',
    },
    completed_on_target: {
      theme: { light: 'hsl(var(--chart-4))', dark: 'hsl(var(--chart-4))' },
      fill: 'hsl(var(--chart-4))',
    },
    completed_above_target: {
      theme: { light: 'hsl(var(--chart-5))', dark: 'hsl(var(--chart-5))' },
      fill: 'hsl(var(--chart-5))',
    },
  };

  // Function to get status badge color
  const getStatusColor = (status: string) => {
    switch (status.toLowerCase()) {
      case 'not started':
      case 'not_started':
        return 'bg-gray-200 text-gray-800';
      case 'in progress':
      case 'in_progress':
        return 'bg-blue-100 text-blue-800';
      case 'completed':
        return 'bg-green-100 text-green-800';
      case 'cancelled':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-200 text-gray-800';
    }
  };

  // Function to get KPI status color and fill
  const getKpiStatusColor = (status: string, forChart = false) => {
    switch (status.toLowerCase()) {
      case 'not_started':
        return forChart ? '#9CA3AF' : 'bg-gray-500';
      case 'in_progress':
        return forChart ? '#3B82F6' : 'bg-blue-600';
      case 'completed_below_target':
        return forChart ? '#FBBF24' : 'bg-yellow-500';
      case 'completed_on_target':
        return forChart ? '#10B981' : 'bg-green-500';
      case 'completed_above_target':
        return forChart ? '#059669' : 'bg-emerald-600';
      default:
        return forChart ? '#3B82F6' : 'bg-blue-600';
    }
  };

  // Function to get chart fill color for project status
  const getStatusChartColor = (status: string) => {
    switch (status.toLowerCase()) {
      case 'not started':
      case 'not_started':
      case 'belum dimulai':
        return '#9CA3AF'; // Gray-400
      case 'in progress':
      case 'in_progress':
      case 'dalam proses':
        return '#3B82F6'; // Blue-500
      case 'completed':
      case 'selesai':
        return '#10B981'; // Green-500
      case 'cancelled':
      case 'dibatalkan':
        return '#EF4444'; // Red-500
      default:
        return '#9CA3AF'; // Gray-400
    }
  };

  // Loading state
  if (loading) {
    return (
      <div className="container mx-auto py-6 px-6">
        <PageTitle title="Dashboard Proyek" />
        <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-4 mt-6">
          {[...Array(4)].map((_, i) => (
            <Card key={i}>
              <CardHeader className="pb-2">
                <Skeleton className="h-5 w-40" />
              </CardHeader>
              <CardContent>
                <Skeleton className="h-10 w-20 mb-4" />
                <Skeleton className="h-32 w-full" />
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    );
  }

  // Error state
  if (error || !dashboardData) {
    return (
      <div className="container mx-auto py-6 px-6">
        <PageTitle title="Dashboard Proyek" />
        <Card className="bg-red-50 border-red-200 mt-6">
          <CardContent className="pt-6">
            <div className="flex items-center gap-2 text-red-700">
              <AlertCircle className="h-5 w-5" />
              <p>Terjadi kesalahan saat memuat data dashboard.</p>
            </div>
            <p className="mt-2 text-sm text-red-600">
              {error || 'Data tidak tersedia'}
            </p>
            <Button
              onClick={refreshDashboard}
              variant="outline"
              className="mt-4"
            >
              <RefreshCw className="h-4 w-4 mr-2" />
              Coba Lagi
            </Button>
          </CardContent>
        </Card>
      </div>
    );
  }

  // Prepare data for charts
  const statusData = Object.entries(dashboardData.projects.by_status).map(
    ([key, value]) => {
      const colorKey = key.toLowerCase().replace(/ /g, '_');
      return {
        name: key,
        value,
        fill: `var(--color-${colorKey})`,
      };
    }
  );

  // Create an array of colors for categories
  const categoryColors = [
    'hsl(var(--chart-1))',
    'hsl(var(--chart-2))',
    'hsl(var(--chart-3))',
    'hsl(var(--chart-4))',
    'hsl(var(--chart-5))',
  ];

  const categoryData = Object.entries(dashboardData.projects.by_category).map(
    ([key, value], index) => ({
      name: key,
      value,
      fill: categoryColors[index % categoryColors.length],
    })
  );

  const picData = dashboardData.projects.by_pic.map((pic) => ({
    name: pic.name,
    value: pic.count,
    fill: '#AB8B3B', // Amber-400
  }));

  // Prepare data for KPI donut chart
  const kpiStatusData = Object.entries(dashboardData.kpis.by_status).map(
    ([key, value]) => {
      return {
        name: key.replace(/_/g, ' ').replace(/\b\w/g, (l) => l.toUpperCase()),
        value,
        fill: getKpiStatusColor(key, true),
      };
    }
  );

  // Calculate total KPI achievement for donut chart center
  const totalKpiAchievement = dashboardData.kpis.achievement_percentage;

  // Filter upcoming deadlines to only show those within the next month (30 days)
  const upcomingDeadlinesNextMonth =
    dashboardData.projects.upcoming_deadlines.filter(
      (project) => project.days_remaining <= 30
    );

  return (
    <div className="container mx-auto py-6 px-6">
      <PageTitle title="Dashboard Proyek" />
      <p className="mt-2 text-gray-600">
        Ringkasan statistik untuk semua proyek.
      </p>

      {/* Summary Cards */}
      <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-4 mt-6">
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium flex items-center gap-2">
              <BarChart2 className="h-4 w-4" />
              Total Proyek
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {dashboardData.projects.total}
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium flex items-center gap-2">
              <Target className="h-4 w-4" />
              Total KPI
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{dashboardData.kpis.total}</div>
            <div className="text-xs text-gray-500 mt-1">
              Pencapaian: {dashboardData.kpis.achievement_percentage}%
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium flex items-center gap-2">
              <CheckSquare className="h-4 w-4" />
              Total Tugas
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {dashboardData.tasks.total}
            </div>
            <div className="text-xs text-gray-500 mt-1">
              Selesai: {dashboardData.tasks.completed} (
              {Math.round(
                (dashboardData.tasks.completed / dashboardData.tasks.total) *
                  100
              )}
              %)
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium flex items-center gap-2">
              <Clock className="h-4 w-4" />
              Tenggat Waktu
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {upcomingDeadlinesNextMonth.length}
            </div>
            <div className="text-xs text-gray-500 mt-1">
              Proyek dengan tenggat waktu dalam 30 hari mendatang
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Charts Section */}
      <div className="grid gap-6 md:grid-cols-2 mt-6">
        {/* Projects by Status Chart */}
        <Card>
          <CardHeader>
            <CardTitle className="text-lg">Proyek berdasarkan Status</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="h-80">
              <ChartContainer config={statusChartConfig}>
                <RechartsPrimitive.PieChart>
                  <RechartsPrimitive.Pie
                    data={statusData}
                    dataKey="value"
                    nameKey="name"
                    cx="50%"
                    cy="50%"
                    outerRadius={80}
                    paddingAngle={2}
                    label
                  >
                    {statusData.map((entry, index) => (
                      <RechartsPrimitive.Cell
                        key={`cell-${index}`}
                        fill={getStatusChartColor(entry.name)}
                      />
                    ))}
                  </RechartsPrimitive.Pie>
                  <ChartTooltip content={<ChartTooltipContent />} />
                  <ChartLegend
                    content={<ChartLegendContent nameKey="name" />}
                  />
                </RechartsPrimitive.PieChart>
              </ChartContainer>
            </div>
          </CardContent>
        </Card>

        {/* Projects by Category Chart */}
        <Card>
          <CardHeader>
            <CardTitle className="text-lg">
              Proyek berdasarkan Kategori
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="h-80">
              <ChartContainer config={categoryChartConfig}>
                <RechartsPrimitive.BarChart
                  data={categoryData}
                  layout="vertical"
                  margin={{
                    top: 5,
                    right: 30,
                    left: 20,
                    bottom: 5,
                  }}
                >
                  <RechartsPrimitive.CartesianGrid horizontal={false} />
                  <RechartsPrimitive.XAxis type="number" />
                  <RechartsPrimitive.YAxis
                    dataKey="name"
                    type="category"
                    tickLine={false}
                    axisLine={false}
                    width={120}
                  />
                  <ChartTooltip content={<ChartTooltipContent />} />
                  <RechartsPrimitive.Bar dataKey="value" radius={4}>
                    {categoryData.map((_, index) => (
                      <RechartsPrimitive.Cell
                        key={`cell-${index}`}
                        fill={`hsl(var(--chart-${(index % 5) + 1}))`}
                      />
                    ))}
                  </RechartsPrimitive.Bar>
                </RechartsPrimitive.BarChart>
              </ChartContainer>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* PIC and KPI Section */}
      <div className="grid gap-6 md:grid-cols-2 mt-6">
        {/* Projects by PIC Chart */}
        <Card>
          <CardHeader>
            <CardTitle className="text-lg">Proyek berdasarkan PIC</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="h-80">
              <ChartContainer config={picChartConfig}>
                <RechartsPrimitive.BarChart
                  data={picData}
                  layout="vertical"
                  margin={{
                    top: 5,
                    right: 50,
                    left: 20,
                    bottom: 5,
                  }}
                >
                  <RechartsPrimitive.CartesianGrid horizontal={false} />
                  <RechartsPrimitive.XAxis type="number" hide />
                  <RechartsPrimitive.YAxis
                    dataKey="name"
                    type="category"
                    tickLine={false}
                    axisLine={false}
                    hide
                  />
                  <ChartTooltip
                    cursor={false}
                    content={<ChartTooltipContent />}
                  />
                  <RechartsPrimitive.Bar
                    dataKey="value"
                    radius={4}
                    fill="#AB8B3B"
                  >
                    <LabelList
                      dataKey="name"
                      position="insideLeft"
                      offset={8}
                      className="fill-[--color-label]"
                      fontSize={12}
                    />
                    <LabelList
                      dataKey="value"
                      position="right"
                      offset={8}
                      className="fill-foreground"
                      fontSize={12}
                    />
                  </RechartsPrimitive.Bar>
                </RechartsPrimitive.BarChart>
              </ChartContainer>
            </div>
          </CardContent>
        </Card>

        {/* KPI Status Donut Chart */}
        <Card className="flex flex-col">
          <CardHeader>
            <CardTitle className="text-lg">Status KPI</CardTitle>
          </CardHeader>
          <CardContent className="flex-1 pb-0">
            <ChartContainer
              config={kpiChartConfig}
              className="mx-auto aspect-square max-h-[250px]"
            >
              <RechartsPrimitive.PieChart>
                <ChartTooltip
                  cursor={false}
                  content={<ChartTooltipContent hideLabel />}
                />
                <RechartsPrimitive.Pie
                  data={kpiStatusData}
                  dataKey="value"
                  nameKey="name"
                  innerRadius={60}
                  outerRadius={80}
                  paddingAngle={2}
                >
                  {kpiStatusData.map((entry) => (
                    <RechartsPrimitive.Cell
                      key={`cell-${entry.name}`}
                      fill={entry.fill}
                    />
                  ))}
                  <Label
                    content={({ viewBox }) => {
                      if (viewBox && 'cx' in viewBox && 'cy' in viewBox) {
                        return (
                          <text
                            x={viewBox.cx}
                            y={viewBox.cy}
                            textAnchor="middle"
                            dominantBaseline="middle"
                          >
                            <tspan
                              x={viewBox.cx}
                              y={viewBox.cy}
                              className="fill-foreground text-3xl font-bold"
                            >
                              {totalKpiAchievement}%
                            </tspan>
                            <tspan
                              x={viewBox.cx}
                              y={(viewBox.cy || 0) + 24}
                              className="fill-muted-foreground text-sm"
                            >
                              Pencapaian
                            </tspan>
                          </text>
                        );
                      }
                    }}
                  />
                </RechartsPrimitive.Pie>
              </RechartsPrimitive.PieChart>
            </ChartContainer>
          </CardContent>
          <CardContent className="pt-2">
            <div className="flex-col gap-2 text-sm">
              <div className="flex items-center gap-2 font-medium leading-none justify-center">
                {dashboardData.kpis.achievement_percentage >= 100 ? (
                  <>
                    Pencapaian melebihi target{' '}
                    <TrendingUp className="h-4 w-4 text-emerald-500" />
                  </>
                ) : dashboardData.kpis.achievement_percentage >= 80 ? (
                  <>
                    Pencapaian mendekati target{' '}
                    <TrendingUp className="h-4 w-4 text-green-500" />
                  </>
                ) : (
                  <>
                    Pencapaian perlu ditingkatkan{' '}
                    <TrendingUp className="h-4 w-4 text-yellow-500" />
                  </>
                )}
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Recent Projects and Upcoming Deadlines */}
      <div className="grid gap-6 md:grid-cols-2 mt-6">
        {/* Recent Projects */}
        <Card>
          <CardHeader>
            <CardTitle className="text-lg">Proyek Terbaru</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {dashboardData.projects.recent.map((project) => (
                <div key={project.id} className="border rounded-lg p-3">
                  <div className="flex justify-between items-start">
                    <div>
                      <p className="font-medium">{project.project_name}</p>
                      <p className="text-sm text-gray-600">
                        {project.organization_name}
                      </p>
                    </div>
                    <Badge className={getStatusColor(project.status_project)}>
                      {project.status_project}
                    </Badge>
                  </div>
                  <div className="flex justify-between mt-2 text-sm">
                    <span className="text-gray-500">
                      PIC: {project.pic_name}
                    </span>
                    <span className="text-gray-500">
                      {project.days_remaining > 0
                        ? `${project.days_remaining} hari tersisa`
                        : 'Tenggat waktu terlewati'}
                    </span>
                  </div>
                  <Button
                    variant="link"
                    className="px-0 h-auto text-sm mt-1"
                    onClick={() => router.push(`/project/${project.id}`)}
                  >
                    Lihat Detail
                  </Button>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        {/* Upcoming Deadlines */}
        <Card>
          <CardHeader>
            <CardTitle className="text-lg">
              Tenggat Waktu Mendatang (30 Hari)
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {upcomingDeadlinesNextMonth.map((project) => (
                <div key={project.id} className="border rounded-lg p-3">
                  <div className="flex justify-between items-start">
                    <p className="font-medium">{project.project_name}</p>
                    <Badge
                      variant="outline"
                      className={
                        project.days_remaining <= 7
                          ? 'border-red-500 text-red-700'
                          : project.days_remaining <= 30
                            ? 'border-yellow-500 text-yellow-700'
                            : 'border-green-500 text-green-700'
                      }
                    >
                      {project.days_remaining} hari
                    </Badge>
                  </div>
                  <div className="flex justify-between mt-2 text-sm">
                    <span className="text-gray-500">
                      Tenggat: {formatDate(project.end_project)}
                    </span>
                    <span className="text-gray-500">
                      Progres: {project.progress_percentage}%
                    </span>
                  </div>
                  <Button
                    variant="link"
                    className="px-0 h-auto text-sm mt-1"
                    onClick={() => router.push(`/project/${project.id}`)}
                  >
                    Lihat Detail
                  </Button>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
