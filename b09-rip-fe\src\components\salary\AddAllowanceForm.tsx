'use client';

import React, { useState } from 'react';
import { Loader2, CheckCircle2 } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Label } from '@/components/ui/label';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogDescription,
  DialogFooter,
} from '@/components/ui/dialog';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { AllowanceSalaryType } from '@/types/salary';
import { formatCurrency } from '@/lib/utils/format';
import { allowanceApi } from '@/lib/api/allowance';
import { toast } from 'sonner';

interface AddAllowanceFormProps {
  salaryId: string;
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onSuccess?: () => void;
}

const AddAllowanceForm: React.FC<AddAllowanceFormProps> = ({
  salaryId,
  open,
  onOpenChange,
  onSuccess,
}) => {
  const [amount, setAmount] = useState<number>(0);
  const [allowanceType, setAllowanceType] = useState<AllowanceSalaryType | ''>(
    ''
  );
  const [notes, setNotes] = useState<string>('');
  const [loading, setLoading] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);
  const [step, setStep] = useState<'form' | 'confirmation' | 'success'>('form');

  const handleAmountChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    const numericValue = parseInt(value, 10);

    if (value === '') {
      setAmount(0);
      setError(null);
    } else if (isNaN(numericValue)) {
      setError('Jumlah harus berupa angka');
    } else if (numericValue < 0) {
      setError('Jumlah tidak boleh negatif');
    } else {
      setAmount(numericValue);
      setError(null);
    }
  };

  // Handle form submission - move to confirmation step
  const handleSubmit = () => {
    if (error) return;
    setStep('confirmation');
  };

  // Handle confirmation submit
  const handleConfirmSubmit = async () => {
    try {
      setLoading(true);

      const data = {
        salary_id: salaryId,
        amount,
        allowance_type: allowanceType as AllowanceSalaryType,
        notes: notes || undefined,
      };

      const response = await allowanceApi.createAllowance(data);

      if (response.success) {
        toast.success('Tunjangan berhasil ditambahkan');
        // Call refresh function immediately after successful creation
        if (onSuccess) onSuccess();
        setStep('success');
      } else {
        toast.error(`Gagal menambahkan tunjangan: ${response.message}`);
        setStep('form');
      }
    } catch (err) {
      console.error('Error adding allowance:', err);
      toast.error('Terjadi kesalahan saat menambahkan tunjangan');
      setStep('form');
    } finally {
      setLoading(false);
    }
  };

  // Handle dialog close
  const handleDialogClose = (isOpen: boolean) => {
    if (!isOpen) {
      // If we're in the success step, trigger the refresh after dialog closes
      if (step === 'success') {
        if (onSuccess) onSuccess();
      }
      // Reset step
      setTimeout(() => {
        setStep('form');
      }, 300);
    }
  };

  return (
    <Dialog open={open} onOpenChange={handleDialogClose}>
      <DialogContent>
        {step === 'form' && (
          <>
            <DialogHeader>
              <DialogTitle>Tambah Tunjangan</DialogTitle>
              <DialogDescription>
                Tambahkan tunjangan baru untuk gaji ini
              </DialogDescription>
            </DialogHeader>

            <div className="space-y-4 py-4">
              <div className="space-y-2">
                <Label htmlFor="amount">Jumlah Tunjangan (Rp)</Label>
                <Input
                  id="amount"
                  type="number"
                  min="0"
                  value={amount}
                  onChange={handleAmountChange}
                />
                {error && <p className="text-red-500 text-sm">{error}</p>}
                {amount > 0 && (
                  <p className="text-sm text-gray-500 mt-1">
                    Format: {formatCurrency(amount)}
                  </p>
                )}
              </div>

              <div className="space-y-2">
                <Label htmlFor="allowance-type">Tipe Tunjangan</Label>
                <Select
                  value={allowanceType}
                  onValueChange={(value) =>
                    setAllowanceType(value as AllowanceSalaryType)
                  }
                >
                  <SelectTrigger id="allowance-type">
                    <SelectValue placeholder="Pilih tipe tunjangan" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value={AllowanceSalaryType.TRANSPORT}>
                      Transportasi
                    </SelectItem>
                    <SelectItem value={AllowanceSalaryType.MEAL}>
                      Makan
                    </SelectItem>
                    <SelectItem value={AllowanceSalaryType.HEALTH}>
                      Kesehatan
                    </SelectItem>
                    <SelectItem value={AllowanceSalaryType.POSITION}>
                      Jabatan
                    </SelectItem>
                    <SelectItem value={AllowanceSalaryType.TENURE}>
                      Masa Kerja
                    </SelectItem>
                    <SelectItem value={AllowanceSalaryType.THR}>THR</SelectItem>
                    <SelectItem value={AllowanceSalaryType.OTHER}>
                      Lainnya
                    </SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <Label htmlFor="notes">Catatan</Label>
                <Textarea
                  id="notes"
                  value={notes}
                  onChange={(e) => setNotes(e.target.value)}
                  placeholder="Catatan tambahan (opsional)"
                />
              </div>
            </div>

            <DialogFooter>
              <Button
                variant="outline"
                onClick={() => onOpenChange(false)}
                disabled={loading}
              >
                Batal
              </Button>
              <Button
                onClick={handleSubmit}
                disabled={
                  loading || error !== null || !allowanceType || amount <= 0
                }
              >
                {loading ? 'Menambahkan...' : 'Lanjutkan'}
              </Button>
            </DialogFooter>
          </>
        )}

        {step === 'confirmation' && (
          <>
            <DialogHeader>
              <DialogTitle>Konfirmasi Penambahan</DialogTitle>
              <DialogDescription>
                Apakah Anda yakin ingin menambahkan tunjangan ini?
              </DialogDescription>
            </DialogHeader>

            <div className="py-4">
              <div className="rounded-lg border p-4">
                <h4 className="font-medium mb-2">Detail Tunjangan:</h4>
                <p>
                  <span className="text-muted-foreground">Tipe:</span>{' '}
                  <span className="font-medium">
                    {allowanceType === AllowanceSalaryType.TRANSPORT
                      ? 'Transportasi'
                      : allowanceType === AllowanceSalaryType.MEAL
                        ? 'Makan'
                        : allowanceType === AllowanceSalaryType.HEALTH
                          ? 'Kesehatan'
                          : allowanceType === AllowanceSalaryType.POSITION
                            ? 'Jabatan'
                            : allowanceType === AllowanceSalaryType.TENURE
                              ? 'Masa Kerja'
                              : allowanceType === AllowanceSalaryType.THR
                                ? 'THR'
                                : 'Lainnya'}
                  </span>
                </p>
                <p>
                  <span className="text-muted-foreground">Jumlah:</span>{' '}
                  <span className="font-medium">{formatCurrency(amount)}</span>
                </p>
                {notes && (
                  <p>
                    <span className="text-muted-foreground">Catatan:</span>{' '}
                    <span className="font-medium">{notes}</span>
                  </p>
                )}
              </div>
            </div>

            <DialogFooter>
              <Button
                variant="outline"
                onClick={() => setStep('form')}
                disabled={loading}
              >
                Kembali
              </Button>
              <Button onClick={handleConfirmSubmit} disabled={loading}>
                {loading ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    Menambahkan...
                  </>
                ) : (
                  'Ya, Tambahkan'
                )}
              </Button>
            </DialogFooter>
          </>
        )}

        {step === 'success' && (
          <div className="py-6 flex flex-col items-center justify-center text-center">
            <CheckCircle2 className="h-16 w-16 text-green-500 mb-4" />
            <h3 className="text-lg font-semibold mb-2">
              Tunjangan Berhasil Ditambahkan
            </h3>
            <p className="text-muted-foreground mb-6">
              Tunjangan telah berhasil ditambahkan ke gaji ini.
            </p>
            <Button onClick={() => onOpenChange(false)}>Selesai</Button>
          </div>
        )}
      </DialogContent>
    </Dialog>
  );
};

export default AddAllowanceForm;
