'use client';

import React, { useState } from 'react';
import { Loader2, CheckCircle2 } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Label } from '@/components/ui/label';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogDescription,
  DialogFooter,
} from '@/components/ui/dialog';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { BonusSalaryType } from '@/types/salary';
import { formatCurrency } from '@/lib/utils/format';
import { bonusApi } from '@/lib/api/bonus';
import { toast } from 'sonner';
import { ProjectCombobox } from '@/components/project/ProjectCombobox';
import { KPICombobox } from '@/components/kpi/KPICombobox';

interface AddBonusFormProps {
  salaryId: string;
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onSuccess?: () => void;
}

const AddBonusForm: React.FC<AddBonusFormProps> = ({
  salaryId,
  open,
  onOpenChange,
  onSuccess,
}) => {
  const [amount, setAmount] = useState<number>(0);
  const [bonusType, setBonusType] = useState<BonusSalaryType | ''>('');
  const [notes, setNotes] = useState<string>('');
  const [kpiId, setKpiId] = useState<string>('');
  const [kpiName, setKpiName] = useState<string>('');
  const [projectId, setProjectId] = useState<string>('');
  const [projectName, setProjectName] = useState<string>('');
  const [loading, setLoading] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);
  const [step, setStep] = useState<'form' | 'confirmation' | 'success'>('form');

  const handleAmountChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    const numericValue = parseInt(value, 10);

    if (value === '') {
      setAmount(0);
      setError(null);
    } else if (isNaN(numericValue)) {
      setError('Jumlah harus berupa angka');
    } else if (numericValue < 0) {
      setError('Jumlah tidak boleh negatif');
    } else {
      setAmount(numericValue);
      setError(null);
    }
  };

  // Handle form submission - move to confirmation step
  const handleSubmit = () => {
    if (error) return;
    setStep('confirmation');
  };

  // Handle confirmation submit
  const handleConfirmSubmit = async () => {
    try {
      setLoading(true);

      const data = {
        salary_id: salaryId,
        amount,
        bonus_type: bonusType as BonusSalaryType,
        notes: notes || undefined,
        kpi_id: kpiId || undefined,
        project_id: projectId || undefined,
      };

      const response = await bonusApi.createBonus(data);

      if (response.success) {
        toast.success('Bonus berhasil ditambahkan');
        // Call refresh function immediately after successful creation
        if (onSuccess) onSuccess();
        setStep('success');
      } else {
        toast.error(`Gagal menambahkan bonus: ${response.message}`);
        setStep('form');
      }
    } catch (err) {
      console.error('Error adding bonus:', err);
      toast.error('Terjadi kesalahan saat menambahkan bonus');
      setStep('form');
    } finally {
      setLoading(false);
    }
  };

  // Reset form fields
  const resetForm = () => {
    setAmount(0);
    setBonusType('');
    setNotes('');
    setKpiId('');
    setKpiName('');
    setProjectId('');
    setProjectName('');
    setError(null);
  };

  // Handle dialog close
  const handleDialogClose = (isOpen: boolean) => {
    if (!isOpen) {
      // If we're in the success step, trigger the refresh after dialog closes
      if (step === 'success') {
        if (onSuccess) onSuccess();
      }
      // Reset step and form
      setTimeout(() => {
        setStep('form');
        resetForm();
      }, 300);
    }
  };

  return (
    <Dialog open={open} onOpenChange={handleDialogClose}>
      <DialogContent>
        {step === 'form' && (
          <>
            <DialogHeader>
              <DialogTitle>Tambah Bonus</DialogTitle>
              <DialogDescription>
                Tambahkan bonus baru untuk gaji ini
              </DialogDescription>
            </DialogHeader>

            <div className="space-y-4 py-4">
              <div className="space-y-2">
                <Label htmlFor="amount">Jumlah Bonus (Rp)</Label>
                <Input
                  id="amount"
                  type="number"
                  min="0"
                  value={amount}
                  onChange={handleAmountChange}
                />
                {error && <p className="text-red-500 text-sm">{error}</p>}
                {amount > 0 && (
                  <p className="text-sm text-gray-500 mt-1">
                    Format: {formatCurrency(amount)}
                  </p>
                )}
              </div>

              <div className="space-y-2">
                <Label htmlFor="bonus-type">Tipe Bonus</Label>
                <Select
                  value={bonusType}
                  onValueChange={(value) =>
                    setBonusType(value as BonusSalaryType)
                  }
                >
                  <SelectTrigger id="bonus-type">
                    <SelectValue placeholder="Pilih tipe bonus" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value={BonusSalaryType.KPI}>KPI</SelectItem>
                    <SelectItem value={BonusSalaryType.PROJECT}>
                      Project
                    </SelectItem>
                    <SelectItem value={BonusSalaryType.OTHER}>
                      Lainnya
                    </SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <Label htmlFor="notes">Catatan</Label>
                <Textarea
                  id="notes"
                  value={notes}
                  onChange={(e) => setNotes(e.target.value)}
                  placeholder="Catatan tambahan (opsional)"
                />
              </div>

              {bonusType === BonusSalaryType.KPI && (
                <div className="space-y-2">
                  <Label htmlFor="kpi-id">KPI</Label>
                  <KPICombobox
                    value={kpiName}
                    onSelect={(id, name) => {
                      setKpiId(id);
                      setKpiName(name);
                    }}
                    placeholder="Pilih KPI..."
                  />
                </div>
              )}

              {bonusType === BonusSalaryType.PROJECT && (
                <div className="space-y-2">
                  <Label htmlFor="project-id">Project</Label>
                  <ProjectCombobox
                    value={projectName}
                    onSelect={(id, name) => {
                      setProjectId(id);
                      setProjectName(name);
                    }}
                    placeholder="Pilih project..."
                  />
                </div>
              )}
            </div>

            <DialogFooter>
              <Button
                variant="outline"
                onClick={() => onOpenChange(false)}
                disabled={loading}
              >
                Batal
              </Button>
              <Button
                onClick={handleSubmit}
                disabled={
                  loading ||
                  error !== null ||
                  !bonusType ||
                  amount <= 0 ||
                  (bonusType === BonusSalaryType.KPI && !kpiId) ||
                  (bonusType === BonusSalaryType.PROJECT && !projectId)
                }
              >
                {loading ? 'Menambahkan...' : 'Lanjutkan'}
              </Button>
            </DialogFooter>
          </>
        )}

        {step === 'confirmation' && (
          <>
            <DialogHeader>
              <DialogTitle>Konfirmasi Penambahan</DialogTitle>
              <DialogDescription>
                Apakah Anda yakin ingin menambahkan bonus ini?
              </DialogDescription>
            </DialogHeader>

            <div className="py-4">
              <div className="rounded-lg border p-4">
                <h4 className="font-medium mb-2">Detail Bonus:</h4>
                <p>
                  <span className="text-muted-foreground">Tipe:</span>{' '}
                  <span className="font-medium">
                    {bonusType === BonusSalaryType.KPI
                      ? 'KPI'
                      : bonusType === BonusSalaryType.PROJECT
                        ? 'Project'
                        : 'Lainnya'}
                  </span>
                </p>
                <p>
                  <span className="text-muted-foreground">Jumlah:</span>{' '}
                  <span className="font-medium">{formatCurrency(amount)}</span>
                </p>
                {notes && (
                  <p>
                    <span className="text-muted-foreground">Catatan:</span>{' '}
                    <span className="font-medium">{notes}</span>
                  </p>
                )}
                {bonusType === BonusSalaryType.KPI && kpiId && (
                  <p>
                    <span className="text-muted-foreground">KPI:</span>{' '}
                    <span className="font-medium">{kpiName}</span>
                  </p>
                )}
                {bonusType === BonusSalaryType.PROJECT && projectId && (
                  <p>
                    <span className="text-muted-foreground">Project:</span>{' '}
                    <span className="font-medium">{projectName}</span>
                  </p>
                )}
              </div>
            </div>

            <DialogFooter>
              <Button
                variant="outline"
                onClick={() => setStep('form')}
                disabled={loading}
              >
                Kembali
              </Button>
              <Button onClick={handleConfirmSubmit} disabled={loading}>
                {loading ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    Menambahkan...
                  </>
                ) : (
                  'Ya, Tambahkan'
                )}
              </Button>
            </DialogFooter>
          </>
        )}

        {step === 'success' && (
          <div className="py-6 flex flex-col items-center justify-center text-center">
            <CheckCircle2 className="h-16 w-16 text-green-500 mb-4" />
            <h3 className="text-lg font-semibold mb-2">
              Bonus Berhasil Ditambahkan
            </h3>
            <p className="text-muted-foreground mb-6">
              Bonus telah berhasil ditambahkan ke gaji ini.
            </p>
            <Button onClick={() => onOpenChange(false)}>Selesai</Button>
          </div>
        )}
      </DialogContent>
    </Dialog>
  );
};

export default AddBonusForm;
