'use client';

import React, { useState } from 'react';
import { Loader2, CheckCircle2 } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Label } from '@/components/ui/label';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogDescription,
  DialogFooter,
} from '@/components/ui/dialog';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { DeductionType } from '@/types/salary';
import { formatCurrency } from '@/lib/utils/format';
import { deductionApi } from '@/lib/api/deduction';
import { toast } from 'sonner';

interface AddDeductionFormProps {
  salaryId: string;
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onSuccess?: () => void;
}

const AddDeductionForm: React.FC<AddDeductionFormProps> = ({
  salaryId,
  open,
  onOpenChange,
  onSuccess,
}) => {
  const [amount, setAmount] = useState<number>(0);
  const [deductionType, setDeductionType] = useState<DeductionType | ''>('');
  const [notes, setNotes] = useState<string>('');
  const [loading, setLoading] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);
  const [step, setStep] = useState<'form' | 'confirmation' | 'success'>('form');

  const handleAmountChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    const numericValue = parseInt(value, 10);

    if (value === '') {
      setAmount(0);
      setError(null);
    } else if (isNaN(numericValue)) {
      setError('Jumlah harus berupa angka');
    } else if (numericValue < 0) {
      setError('Jumlah tidak boleh negatif');
    } else {
      setAmount(numericValue);
      setError(null);
    }
  };

  // Handle form submission - move to confirmation step
  const handleSubmit = () => {
    if (error || !deductionType) return;
    setStep('confirmation');
  };

  // Handle confirmation submit
  const handleConfirmSubmit = async () => {
    try {
      setLoading(true);

      // Make sure deductionType is not empty string before submitting
      if (!deductionType) {
        toast.error('Tipe potongan harus dipilih');
        setStep('form');
        setLoading(false);
        return;
      }

      const data = {
        salary_id: salaryId,
        amount,
        deduction_type: deductionType as DeductionType,
        notes: notes || undefined,
      };

      const response = await deductionApi.createDeduction(data);

      if (response.success) {
        toast.success('Potongan berhasil ditambahkan');
        // Call refresh function immediately after successful creation
        if (onSuccess) onSuccess();
        setStep('success');
      } else {
        toast.error(
          `Gagal menambahkan potongan: ${response.message || 'Terjadi kesalahan'}`
        );
        setStep('form');
      }
    } catch (err: unknown) {
      console.error('Error creating deduction:', err);
      const errorObj = err as { message?: string };
      toast.error(
        errorObj.message || 'Terjadi kesalahan saat menambahkan potongan'
      );
      setStep('form');
    } finally {
      setLoading(false);
    }
  };

  // Handle dialog close
  const handleDialogClose = (isOpen: boolean) => {
    if (!isOpen) {
      // If we're in the success step, trigger the refresh after dialog closes
      if (step === 'success') {
        if (onSuccess) onSuccess();
      }
      // Reset step
      setTimeout(() => {
        setStep('form');
      }, 300);
    }
  };

  return (
    <Dialog open={open} onOpenChange={handleDialogClose}>
      <DialogContent>
        {step === 'form' && (
          <>
            <DialogHeader>
              <DialogTitle>Tambah Potongan</DialogTitle>
              <DialogDescription>
                Tambahkan potongan gaji baru
              </DialogDescription>
            </DialogHeader>

            <div className="space-y-4 py-4">
              <div className="space-y-2">
                <Label htmlFor="amount">Jumlah Potongan (Rp)</Label>
                <Input
                  id="amount"
                  type="number"
                  min="0"
                  value={amount}
                  onChange={handleAmountChange}
                />
                {error && <p className="text-red-500 text-sm">{error}</p>}
                {amount > 0 && (
                  <p className="text-sm text-gray-500 mt-1">
                    Format: {formatCurrency(amount)}
                  </p>
                )}
              </div>

              <div className="space-y-2">
                <Label htmlFor="deduction-type">Tipe Potongan</Label>
                <Select
                  value={deductionType}
                  onValueChange={(value) =>
                    setDeductionType(value as DeductionType)
                  }
                >
                  <SelectTrigger id="deduction-type">
                    <SelectValue placeholder="Pilih tipe potongan" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value={DeductionType.ABSENCE}>
                      Ketidakhadiran
                    </SelectItem>
                    <SelectItem value={DeductionType.LATENESS}>
                      Keterlambatan
                    </SelectItem>
                    <SelectItem value={DeductionType.LOAN}>Pinjaman</SelectItem>
                    <SelectItem value={DeductionType.OTHER}>Lainnya</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <Label htmlFor="notes">Catatan</Label>
                <Textarea
                  id="notes"
                  value={notes}
                  onChange={(e) => setNotes(e.target.value)}
                  placeholder="Catatan tambahan (opsional)"
                />
              </div>
            </div>

            <DialogFooter>
              <Button
                variant="outline"
                onClick={() => onOpenChange(false)}
                disabled={loading}
              >
                Batal
              </Button>
              <Button
                onClick={handleSubmit}
                disabled={
                  loading || error !== null || !deductionType || amount <= 0
                }
              >
                {loading ? 'Menambahkan...' : 'Lanjutkan'}
              </Button>
            </DialogFooter>
          </>
        )}

        {step === 'confirmation' && (
          <>
            <DialogHeader>
              <DialogTitle>Konfirmasi Penambahan</DialogTitle>
              <DialogDescription>
                Apakah Anda yakin ingin menambahkan potongan ini?
              </DialogDescription>
            </DialogHeader>

            <div className="py-4">
              <div className="rounded-lg border p-4">
                <h4 className="font-medium mb-2">Detail Potongan:</h4>
                <p>
                  <span className="text-muted-foreground">Tipe:</span>{' '}
                  <span className="font-medium">
                    {deductionType === DeductionType.ABSENCE
                      ? 'Ketidakhadiran'
                      : deductionType === DeductionType.LATENESS
                        ? 'Keterlambatan'
                        : deductionType === DeductionType.LOAN
                          ? 'Pinjaman'
                          : 'Lainnya'}
                  </span>
                </p>
                <p>
                  <span className="text-muted-foreground">Jumlah:</span>{' '}
                  <span className="font-medium">{formatCurrency(amount)}</span>
                </p>
                {notes && (
                  <p>
                    <span className="text-muted-foreground">Catatan:</span>{' '}
                    <span className="font-medium">{notes}</span>
                  </p>
                )}
              </div>
            </div>

            <DialogFooter>
              <Button
                variant="outline"
                onClick={() => setStep('form')}
                disabled={loading}
              >
                Kembali
              </Button>
              <Button onClick={handleConfirmSubmit} disabled={loading}>
                {loading ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    Menambahkan...
                  </>
                ) : (
                  'Ya, Tambahkan'
                )}
              </Button>
            </DialogFooter>
          </>
        )}

        {step === 'success' && (
          <div className="py-6 flex flex-col items-center justify-center text-center">
            <CheckCircle2 className="h-16 w-16 text-green-500 mb-4" />
            <h3 className="text-lg font-semibold mb-2">
              Potongan Berhasil Ditambahkan
            </h3>
            <p className="text-muted-foreground mb-6">
              Potongan telah berhasil ditambahkan ke gaji ini.
            </p>
            <Button onClick={() => onOpenChange(false)}>Selesai</Button>
          </div>
        )}
      </DialogContent>
    </Dialog>
  );
};

export default AddDeductionForm;
