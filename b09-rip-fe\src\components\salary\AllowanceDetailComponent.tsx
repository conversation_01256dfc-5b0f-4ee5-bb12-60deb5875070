'use client';

import React from 'react';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { formatCurrency, formatDateTime } from '@/lib/utils/format';
import { Allowance, AllowanceSalaryType } from '@/types/salary';
import { useRBAC } from '@/hooks/useRBAC';

interface AllowanceDetailComponentProps {
  allowance: Allowance;
  isOpen: boolean;
  onClose: () => void;
  onEdit: () => void;
  onDelete: () => void;
  isActionDisabled?: boolean;
}

const AllowanceDetailComponent: React.FC<AllowanceDetailComponentProps> = ({
  allowance,
  isOpen,
  onClose,
  onEdit,
  onDelete,
  isActionDisabled = false,
}) => {
  const { hasRole } = useRBAC();
  const canEdit = hasRole(['Admin', 'Finance', 'HR', 'Manager']);
  const canDelete = hasRole(['Admin', 'Finance', 'HR', 'Manager']);

  // Format allowance type
  const formatAllowanceType = (type: string): string => {
    switch (type) {
      case AllowanceSalaryType.TRANSPORT:
        return 'Transportasi';
      case AllowanceSalaryType.MEAL:
        return 'Makan';
      case AllowanceSalaryType.HEALTH:
        return 'Kesehatan';
      case AllowanceSalaryType.POSITION:
        return 'Jabatan';
      case AllowanceSalaryType.TENURE:
        return 'Masa Kerja';
      case AllowanceSalaryType.THR:
        return 'Tunjangan Hari Raya';
      case AllowanceSalaryType.OTHER:
        return 'Lainnya';
      default:
        return type;
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle>Detail Tunjangan Gaji</DialogTitle>
        </DialogHeader>
        <div className="grid gap-4 py-4">
          <div className="grid grid-cols-4 items-center gap-4">
            <div className="font-medium">Tipe Tunjangan</div>
            <div className="col-span-3">
              {formatAllowanceType(allowance.allowance_type)}
            </div>
          </div>
          <div className="grid grid-cols-4 items-center gap-4">
            <div className="font-medium">Jumlah</div>
            <div className="col-span-3">{formatCurrency(allowance.amount)}</div>
          </div>
          {allowance.notes && (
            <div className="grid grid-cols-4 items-center gap-4">
              <div className="font-medium">Catatan</div>
              <div className="col-span-3">{allowance.notes}</div>
            </div>
          )}
          <div className="grid grid-cols-4 items-center gap-4">
            <div className="font-medium">Dibuat</div>
            <div className="col-span-3">
              {formatDateTime(allowance.created_at)}
            </div>
          </div>
          <div className="grid grid-cols-4 items-center gap-4">
            <div className="font-medium">Diperbarui</div>
            <div className="col-span-3">
              {allowance.updated_at &&
              allowance.updated_at !== allowance.created_at
                ? formatDateTime(allowance.updated_at)
                : formatDateTime(allowance.created_at)}
            </div>
          </div>
        </div>
        <div className="flex justify-end space-x-2">
          {canEdit && (
            <Button
              variant="outline"
              onClick={onEdit}
              disabled={isActionDisabled}
            >
              Edit
            </Button>
          )}
          {canDelete && (
            <Button
              variant="destructive"
              onClick={onDelete}
              disabled={isActionDisabled}
            >
              Hapus
            </Button>
          )}
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default AllowanceDetailComponent;
