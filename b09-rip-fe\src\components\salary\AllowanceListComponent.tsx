'use client';

import React from 'react';
import { DataTable } from '@/components/ui/data-table';
import { Button } from '@/components/ui/button';
import { Edit, Trash2, Eye } from 'lucide-react';
import { formatCurrency } from '@/lib/utils/format';
import { Allowance, AllowanceSalaryType } from '@/types/salary';
import { useRBAC } from '@/hooks/useRBAC';

interface AllowanceListComponentProps {
  allowances: Allowance[];
  loading: boolean;
  onView: (allowance: Allowance) => void;
  onEdit: (allowance: Allowance) => void;
  onDelete: (allowance: Allowance) => void;
  isActionDisabled?: boolean;
}

const AllowanceListComponent: React.FC<AllowanceListComponentProps> = ({
  allowances,
  loading,
  onView,
  onEdit,
  onDelete,
  isActionDisabled = false,
}) => {
  const { hasRole } = useRBAC();
  const canEdit = hasRole(['Admin', 'Finance', 'HR', 'Manager']);
  const canDelete = hasRole(['Admin', 'Finance', 'HR', 'Manager']);
  const canView = hasRole(['Admin', 'Finance', 'HR', 'Manager']);

  // Format allowance type
  const formatAllowanceType = (type: string): string => {
    switch (type) {
      case AllowanceSalaryType.TRANSPORT:
        return 'Transportasi';
      case AllowanceSalaryType.MEAL:
        return 'Makan';
      case AllowanceSalaryType.HEALTH:
        return 'Kesehatan';
      case AllowanceSalaryType.POSITION:
        return 'Jabatan';
      case AllowanceSalaryType.TENURE:
        return 'Masa Kerja';
      case AllowanceSalaryType.THR:
        return 'Tunjangan Hari Raya';
      case AllowanceSalaryType.OTHER:
        return 'Lainnya';
      default:
        return type;
    }
  };

  const columns = [
    {
      key: 'allowance_type',
      header: 'TIPE TUNJANGAN',
      render: (allowance: Allowance) =>
        formatAllowanceType(allowance.allowance_type),
    },
    {
      key: 'amount',
      header: 'JUMLAH',
      render: (allowance: Allowance) => formatCurrency(allowance.amount),
    },
    {
      key: 'notes',
      header: 'CATATAN',
      render: (allowance: Allowance) => allowance.notes || '-',
    },
    {
      key: 'actions',
      header: 'AKSI',
      width: '180px',
      render: (allowance: Allowance) => (
        <div className="flex space-x-2">
          {canView && (
            <Button
              variant="outline"
              size="sm"
              onClick={() => onView(allowance)}
              className="h-8 w-8 p-0"
              disabled={isActionDisabled}
            >
              <Eye className="h-4 w-4" />
            </Button>
          )}
          {canEdit && (
            <Button
              variant="outline"
              size="sm"
              onClick={() => onEdit(allowance)}
              className="h-8 w-8 p-0"
              disabled={isActionDisabled}
            >
              <Edit className="h-4 w-4" />
            </Button>
          )}
          {canDelete && (
            <Button
              variant="outline"
              size="sm"
              onClick={() => onDelete(allowance)}
              className="h-8 w-8 p-0 text-red-500 hover:text-red-600"
              disabled={isActionDisabled}
            >
              <Trash2 className="h-4 w-4" />
            </Button>
          )}
        </div>
      ),
    },
  ];

  return (
    <DataTable
      columns={columns}
      data={allowances}
      keyExtractor={(allowance) => allowance.id}
      loading={loading}
      emptyStateMessage="Tidak ada data tunjangan yang ditemukan."
      // showPagination prop removed as it's not used in DataTable anymore
      // showPagination={false}
    />
  );
};

export default AllowanceListComponent;
