'use client';

import React from 'react';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { formatCurrency, formatDateTime } from '@/lib/utils/format';
import { Bonus, BonusSalaryType } from '@/types/salary';
import { useRBAC } from '@/hooks/useRBAC';

interface BonusDetailComponentProps {
  bonus: Bonus;
  isOpen: boolean;
  onClose: () => void;
  onEdit: () => void;
  onDelete: () => void;
  isActionDisabled?: boolean;
}

const BonusDetailComponent: React.FC<BonusDetailComponentProps> = ({
  bonus,
  isOpen,
  onClose,
  onEdit,
  onDelete,
  isActionDisabled = false,
}) => {
  const { hasRole } = useRBAC();
  const canEdit = hasRole(['Admin', 'Finance', 'HR', 'Manager']);
  const canDelete = hasRole(['Admin', 'Finance', 'HR', 'Manager']);

  // Format bonus type
  const formatBonusType = (type: string): string => {
    switch (type) {
      case BonusSalaryType.KPI:
        return 'KPI';
      case BonusSalaryType.PROJECT:
        return 'Proyek';
      case BonusSalaryType.OTHER:
        return 'Lainnya';
      default:
        return type;
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle>Detail Bonus Gaji</DialogTitle>
        </DialogHeader>
        <div className="grid gap-4 py-4">
          <div className="grid grid-cols-4 items-center gap-4">
            <div className="font-medium">Tipe Bonus</div>
            <div className="col-span-3">
              {formatBonusType(bonus.bonus_type)}
            </div>
          </div>
          <div className="grid grid-cols-4 items-center gap-4">
            <div className="font-medium">Jumlah</div>
            <div className="col-span-3">{formatCurrency(bonus.amount)}</div>
          </div>
          {bonus.notes && (
            <div className="grid grid-cols-4 items-center gap-4">
              <div className="font-medium">Catatan</div>
              <div className="col-span-3">{bonus.notes}</div>
            </div>
          )}
          {bonus.kpi_id && (
            <div className="grid grid-cols-4 items-center gap-4">
              <div className="font-medium">KPI ID</div>
              <div className="col-span-3">{bonus.kpi_id}</div>
            </div>
          )}
          {bonus.project_id && (
            <div className="grid grid-cols-4 items-center gap-4">
              <div className="font-medium">Proyek ID</div>
              <div className="col-span-3">{bonus.project_id}</div>
            </div>
          )}
          <div className="grid grid-cols-4 items-center gap-4">
            <div className="font-medium">Dibuat</div>
            <div className="col-span-3">{formatDateTime(bonus.created_at)}</div>
          </div>
          <div className="grid grid-cols-4 items-center gap-4">
            <div className="font-medium">Diperbarui</div>
            <div className="col-span-3">
              {bonus.updated_at && bonus.updated_at !== bonus.created_at
                ? formatDateTime(bonus.updated_at)
                : formatDateTime(bonus.created_at)}
            </div>
          </div>
        </div>
        <div className="flex justify-end space-x-2">
          {canEdit && (
            <Button
              variant="outline"
              onClick={onEdit}
              disabled={isActionDisabled}
            >
              Edit
            </Button>
          )}
          {canDelete && (
            <Button
              variant="destructive"
              onClick={onDelete}
              disabled={isActionDisabled}
            >
              Hapus
            </Button>
          )}
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default BonusDetailComponent;
