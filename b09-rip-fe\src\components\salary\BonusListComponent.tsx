'use client';

import React from 'react';
import { DataTable } from '@/components/ui/data-table';
import { Button } from '@/components/ui/button';
import { Edit, Trash2, Eye } from 'lucide-react';
import { formatCurrency } from '@/lib/utils/format';
import { Bonus, BonusSalaryType } from '@/types/salary';
import { useRBAC } from '@/hooks/useRBAC';

interface BonusListComponentProps {
  bonuses: Bonus[];
  loading: boolean;
  onView: (bonus: Bonus) => void;
  onEdit: (bonus: Bonus) => void;
  onDelete: (bonus: Bonus) => void;
  isActionDisabled?: boolean;
}

const BonusListComponent: React.FC<BonusListComponentProps> = ({
  bonuses,
  loading,
  onView,
  onEdit,
  onDelete,
  isActionDisabled = false,
}) => {
  const { hasRole } = useRBAC();
  const canEdit = hasRole(['Admin', 'Finance', 'HR', 'Manager']);
  const canDelete = hasRole(['Admin', 'Finance', 'HR', 'Manager']);
  const canView = hasRole(['Admin', 'Finance', 'HR', 'Manager']);

  // Format bonus type
  const formatBonusType = (type: string): string => {
    switch (type) {
      case BonusSalaryType.KPI:
        return 'KPI';
      case BonusSalaryType.PROJECT:
        return 'Proyek';
      case BonusSalaryType.OTHER:
        return 'Lainnya';
      default:
        return type;
    }
  };

  const columns = [
    {
      key: 'bonus_type',
      header: 'TIPE BONUS',
      render: (bonus: Bonus) => formatBonusType(bonus.bonus_type),
    },
    {
      key: 'amount',
      header: 'JUMLAH',
      render: (bonus: Bonus) => formatCurrency(bonus.amount),
    },
    {
      key: 'notes',
      header: 'CATATAN',
      render: (bonus: Bonus) => bonus.notes || '-',
    },
    {
      key: 'actions',
      header: 'AKSI',
      width: '180px',
      render: (bonus: Bonus) => (
        <div className="flex space-x-2">
          {canView && (
            <Button
              variant="outline"
              size="sm"
              onClick={() => onView(bonus)}
              className="h-8 w-8 p-0"
              disabled={isActionDisabled}
            >
              <Eye className="h-4 w-4" />
            </Button>
          )}
          {canEdit && (
            <Button
              variant="outline"
              size="sm"
              onClick={() => onEdit(bonus)}
              className="h-8 w-8 p-0"
              disabled={isActionDisabled}
            >
              <Edit className="h-4 w-4" />
            </Button>
          )}
          {canDelete && (
            <Button
              variant="outline"
              size="sm"
              onClick={() => onDelete(bonus)}
              className="h-8 w-8 p-0 text-red-500 hover:text-red-600"
              disabled={isActionDisabled}
            >
              <Trash2 className="h-4 w-4" />
            </Button>
          )}
        </div>
      ),
    },
  ];

  return (
    <DataTable
      columns={columns}
      data={bonuses}
      keyExtractor={(bonus) => bonus.id}
      loading={loading}
      emptyStateMessage="Tidak ada data bonus yang ditemukan."
      // showPagination prop removed as it's not used in DataTable anymore
      // showPagination={false}
    />
  );
};

export default BonusListComponent;
