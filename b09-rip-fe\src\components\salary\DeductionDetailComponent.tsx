'use client';

import React from 'react';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { formatCurrency, formatDateTime } from '@/lib/utils/format';
import { Deduction, DeductionType } from '@/types/salary';
import { useRBAC } from '@/hooks/useRBAC';

interface DeductionDetailComponentProps {
  deduction: Deduction;
  isOpen: boolean;
  onClose: () => void;
  onEdit: () => void;
  onDelete: () => void;
  isActionDisabled?: boolean;
}

const DeductionDetailComponent: React.FC<DeductionDetailComponentProps> = ({
  deduction,
  isOpen,
  onClose,
  onEdit,
  onDelete,
  isActionDisabled = false,
}) => {
  const { hasRole } = useRBAC();
  const canEdit = hasRole(['Admin', 'Finance', 'HR', 'Manager']);
  const canDelete = hasRole(['Admin', 'Finance', 'HR', 'Manager']);

  // Format deduction type
  const formatDeductionType = (type: string): string => {
    switch (type) {
      case DeductionType.ABSENCE:
        return 'Ketidakhadiran';
      case DeductionType.LATENESS:
        return 'Keterlambatan';
      case DeductionType.LOAN:
        return 'Pinjaman';
      case DeductionType.OTHER:
        return 'Lainnya';
      default:
        return type;
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle>Detail Potongan Gaji</DialogTitle>
        </DialogHeader>
        <div className="grid gap-4 py-4">
          <div className="grid grid-cols-4 items-center gap-4">
            <div className="font-medium">Tipe Potongan</div>
            <div className="col-span-3">
              {formatDeductionType(deduction.deduction_type)}
            </div>
          </div>
          <div className="grid grid-cols-4 items-center gap-4">
            <div className="font-medium">Jumlah</div>
            <div className="col-span-3">{formatCurrency(deduction.amount)}</div>
          </div>
          {deduction.notes && (
            <div className="grid grid-cols-4 items-center gap-4">
              <div className="font-medium">Catatan</div>
              <div className="col-span-3">{deduction.notes}</div>
            </div>
          )}
          <div className="grid grid-cols-4 items-center gap-4">
            <div className="font-medium">Dibuat</div>
            <div className="col-span-3">
              {formatDateTime(deduction.created_at)}
            </div>
          </div>
          <div className="grid grid-cols-4 items-center gap-4">
            <div className="font-medium">Diperbarui</div>
            <div className="col-span-3">
              {deduction.updated_at &&
              deduction.updated_at !== deduction.created_at
                ? formatDateTime(deduction.updated_at)
                : formatDateTime(deduction.created_at)}
            </div>
          </div>
        </div>
        <div className="flex justify-end space-x-2">
          {canEdit && (
            <Button
              variant="outline"
              onClick={onEdit}
              disabled={isActionDisabled}
            >
              Edit
            </Button>
          )}
          {canDelete && (
            <Button
              variant="destructive"
              onClick={onDelete}
              disabled={isActionDisabled}
            >
              Hapus
            </Button>
          )}
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default DeductionDetailComponent;
