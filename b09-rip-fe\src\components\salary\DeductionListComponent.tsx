'use client';

import React from 'react';
import { DataTable } from '@/components/ui/data-table';
import { Button } from '@/components/ui/button';
import { Edit, Trash2, Eye } from 'lucide-react';
import { Deduction } from '@/types/salary';
import { formatCurrency } from '@/lib/utils/format';
import { DeductionType } from '@/types/salary';
import { useRBAC } from '@/hooks/useRBAC';

interface DeductionListComponentProps {
  deductions: Deduction[];
  loading: boolean;
  onView: (deduction: Deduction) => void;
  onEdit: (deduction: Deduction) => void;
  onDelete: (deduction: Deduction) => void;
  isActionDisabled?: boolean;
}

const DeductionListComponent: React.FC<DeductionListComponentProps> = ({
  deductions,
  loading,
  onView,
  onEdit,
  onDelete,
  isActionDisabled = false,
}) => {
  const { hasRole } = useRBAC();
  const canEdit = hasRole(['Admin', 'Finance', 'HR', 'Manager']);
  const canDelete = hasRole(['Admin', 'Finance', 'HR', 'Manager']);
  const canView = hasRole(['Admin', 'Finance', 'HR', 'Manager']);

  // Format deduction type
  const formatDeductionType = (type: string): string => {
    switch (type) {
      case DeductionType.ABSENCE:
        return 'Ketidakhadiran';
      case DeductionType.LATENESS:
        return 'Keterlambatan';
      case DeductionType.LOAN:
        return 'Pinjaman';
      case DeductionType.OTHER:
        return 'Lainnya';
      default:
        return type;
    }
  };

  const columns = [
    {
      key: 'deduction_type',
      header: 'TIPE POTONGAN',
      render: (deduction: Deduction) =>
        formatDeductionType(deduction.deduction_type),
    },
    {
      key: 'amount',
      header: 'JUMLAH',
      render: (deduction: Deduction) => formatCurrency(deduction.amount),
    },
    {
      key: 'notes',
      header: 'CATATAN',
      render: (deduction: Deduction) => deduction.notes || '-',
    },
    {
      key: 'actions',
      header: 'AKSI',
      width: '180px',
      render: (deduction: Deduction) => (
        <div className="flex space-x-2">
          {canView && (
            <Button
              variant="outline"
              size="sm"
              onClick={() => onView(deduction)}
              className="h-8 w-8 p-0"
              disabled={isActionDisabled}
            >
              <Eye className="h-4 w-4" />
            </Button>
          )}
          {canEdit && (
            <Button
              variant="outline"
              size="sm"
              onClick={() => onEdit(deduction)}
              className="h-8 w-8 p-0"
              disabled={isActionDisabled}
            >
              <Edit className="h-4 w-4" />
            </Button>
          )}
          {canDelete && (
            <Button
              variant="outline"
              size="sm"
              onClick={() => onDelete(deduction)}
              className="h-8 w-8 p-0 text-red-500 hover:text-red-600"
              disabled={isActionDisabled}
            >
              <Trash2 className="h-4 w-4" />
            </Button>
          )}
        </div>
      ),
    },
  ];

  return (
    <DataTable
      columns={columns}
      data={deductions}
      keyExtractor={(deduction) => deduction.id}
      loading={loading}
      emptyStateMessage="Tidak ada data potongan yang ditemukan."
      // showPagination prop removed as it's not used in DataTable anymore
      // showPagination={false}
    />
  );
};

export default DeductionListComponent;
