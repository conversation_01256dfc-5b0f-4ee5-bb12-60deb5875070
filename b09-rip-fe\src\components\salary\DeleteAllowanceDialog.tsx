'use client';

import React from 'react';
import { Allowance } from '@/types/salary';
import { formatCurrency } from '@/lib/utils/format';
import { AllowanceSalaryType } from '@/types/salary';
import DeleteDialog from './DeleteDialog';
import { useRBAC } from '@/hooks/useRBAC';

interface DeleteAllowanceDialogProps {
  allowance: Allowance | null;
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onDelete: (id: string) => Promise<boolean>;
}

const DeleteAllowanceDialog: React.FC<DeleteAllowanceDialogProps> = ({
  allowance,
  open,
  onOpenChange,
  onDelete,
}) => {
  const { hasRole } = useRBAC();
  const canDelete = hasRole(['Admin', 'Finance', 'HR', 'Manager']);

  if (!canDelete) return null;

  // Format allowance type
  const formatAllowanceType = (type: string): string => {
    switch (type) {
      case AllowanceSalaryType.TRANSPORT:
        return 'Transport';
      case AllowanceSalaryType.MEAL:
        return 'Meal';
      case AllowanceSalaryType.HEALTH:
        return 'Health';
      case AllowanceSalaryType.POSITION:
        return 'Position';
      case AllowanceSalaryType.TENURE:
        return 'Tenure';
      case AllowanceSalaryType.THR:
        return 'THR';
      case AllowanceSalaryType.OTHER:
        return 'Lainnya';
      default:
        return type;
    }
  };

  // Format allowance details for display
  const formatAllowanceDetails = (allowance: Allowance) => (
    <div className="rounded-lg border p-4">
      <p>
        <span className="text-muted-foreground">Tipe:</span>{' '}
        <span className="font-medium">
          {formatAllowanceType(allowance.allowance_type)}
        </span>
      </p>
      <p>
        <span className="text-muted-foreground">Jumlah:</span>{' '}
        <span className="font-medium">{formatCurrency(allowance.amount)}</span>
      </p>
      {allowance.notes && (
        <p>
          <span className="text-muted-foreground">Catatan:</span>{' '}
          <span className="font-medium">{allowance.notes}</span>
        </p>
      )}
    </div>
  );

  return (
    <DeleteDialog
      item={allowance}
      itemType="allowance"
      open={open}
      onOpenChange={onOpenChange}
      onDelete={onDelete}
      formatItemDetails={formatAllowanceDetails}
      itemTypeName={{
        singular: 'tunjangan',
        capitalSingular: 'Tunjangan',
      }}
    />
  );
};

export default DeleteAllowanceDialog;
