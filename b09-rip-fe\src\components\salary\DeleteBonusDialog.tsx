'use client';

import React from 'react';
import { Bonus } from '@/types/salary';
import { formatCurrency } from '@/lib/utils/format';
import { BonusSalaryType } from '@/types/salary';
import DeleteDialog from './DeleteDialog';
import { useRBAC } from '@/hooks/useRBAC';

interface DeleteBonusDialogProps {
  bonus: Bonus | null;
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onDelete: (id: string) => Promise<boolean>;
}

const DeleteBonusDialog: React.FC<DeleteBonusDialogProps> = ({
  bonus,
  open,
  onOpenChange,
  onDelete,
}) => {
  const { hasRole } = useRBAC();
  const canDelete = hasRole(['Admin', 'Finance', 'HR', 'Manager']);

  if (!canDelete) return null;

  // Format bonus type
  const formatBonusType = (type: string): string => {
    switch (type) {
      case BonusSalaryType.KPI:
        return 'KPI';
      case BonusSalaryType.PROJECT:
        return 'Project';
      case BonusSalaryType.OTHER:
        return 'Lainnya';
      default:
        return type;
    }
  };

  // Format bonus details for display
  const formatBonusDetails = (bonus: Bonus) => (
    <div className="rounded-lg border p-4">
      <p>
        <span className="text-muted-foreground">Tipe:</span>{' '}
        <span className="font-medium">{formatBonusType(bonus.bonus_type)}</span>
      </p>
      <p>
        <span className="text-muted-foreground">Jumlah:</span>{' '}
        <span className="font-medium">{formatCurrency(bonus.amount)}</span>
      </p>
      {bonus.notes && (
        <p>
          <span className="text-muted-foreground">Catatan:</span>{' '}
          <span className="font-medium">{bonus.notes}</span>
        </p>
      )}
    </div>
  );

  return (
    <DeleteDialog
      item={bonus}
      itemType="bonus"
      open={open}
      onOpenChange={onOpenChange}
      onDelete={onDelete}
      formatItemDetails={formatBonusDetails}
      itemTypeName={{
        singular: 'bonus',
        capitalSingular: 'Bonus',
      }}
    />
  );
};

export default DeleteBonusDialog;
