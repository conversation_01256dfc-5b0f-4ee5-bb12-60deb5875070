'use client';

import React from 'react';
import { Deduction, DeductionType } from '@/types/salary';
import { formatCurrency } from '@/lib/utils/format';
import { useRBAC } from '@/hooks/useRBAC';
import DeleteDialog from './DeleteDialog';

interface DeleteDeductionDialogProps {
  deduction: Deduction;
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onDelete: (id: string) => Promise<boolean>;
}

const DeleteDeductionDialog: React.FC<DeleteDeductionDialogProps> = ({
  deduction,
  open,
  onOpenChange,
  onDelete,
}) => {
  const { hasRole } = useRBAC();
  const canDelete = hasRole(['Admin', 'Finance', 'HR', 'Manager']);

  if (!canDelete) return null;

  // Format deduction type
  const formatDeductionType = (type: string): string => {
    switch (type) {
      case DeductionType.ABSENCE:
        return 'Ketidak<PERSON>iran';
      case DeductionType.LATENESS:
        return 'Keterlambatan';
      case DeductionType.LOAN:
        return 'Pinjaman';
      case DeductionType.OTHER:
        return 'Lainnya';
      default:
        return type;
    }
  };

  // Format deduction details for display
  const formatDeductionDetails = (deduction: Deduction) => (
    <div className="rounded-lg border p-4">
      <p>
        <span className="text-muted-foreground">Tipe:</span>{' '}
        <span className="font-medium">
          {formatDeductionType(deduction.deduction_type)}
        </span>
      </p>
      <p>
        <span className="text-muted-foreground">Jumlah:</span>{' '}
        <span className="font-medium">{formatCurrency(deduction.amount)}</span>
      </p>
      {deduction.notes && (
        <p>
          <span className="text-muted-foreground">Catatan:</span>{' '}
          <span className="font-medium">{deduction.notes}</span>
        </p>
      )}
    </div>
  );

  return (
    <DeleteDialog
      item={deduction}
      itemType="deduction"
      open={open}
      onOpenChange={onOpenChange}
      onDelete={onDelete}
      formatItemDetails={formatDeductionDetails}
      itemTypeName={{
        singular: 'potongan',
        capitalSingular: 'Potongan',
      }}
    />
  );
};

export default DeleteDeductionDialog;
