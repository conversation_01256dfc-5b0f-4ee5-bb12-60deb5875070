'use client';

import React, { useState, useEffect } from 'react';
import { Loader2, CheckCircle2, AlertCircle } from 'lucide-react';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogDescription,
  DialogFooter,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';

interface DeleteDialogProps<T> {
  item: T | null;
  itemType: 'bonus' | 'allowance' | 'deduction';
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onDelete: (id: string) => Promise<boolean>;
  formatItemDetails?: (item: T) => React.ReactNode;
  itemTypeName?: {
    singular: string;
    capitalSingular: string;
  };
}

const DeleteDialog = <T extends { id: string }>({
  item,
  itemType,
  open,
  onOpenChange,
  onDelete,
  formatItemDetails,
  itemTypeName,
}: DeleteDialogProps<T>) => {
  const [step, setStep] = useState<'confirmation' | 'loading' | 'result'>('confirmation');
  const [result, setResult] = useState<{ success: boolean; message: string } | null>(null);
  const [loading, setLoading] = useState(false);
  
  // Default item type names if not provided
  const typeName = itemTypeName || {
    singular: itemType === 'bonus' ? 'bonus' : itemType === 'allowance' ? 'tunjangan' : 'potongan',
    capitalSingular: itemType === 'bonus' ? 'Bonus' : itemType === 'allowance' ? 'Tunjangan' : 'Potongan',
  };
  
  // Reset state when dialog closes
  useEffect(() => {
    if (!open) {
      setStep('confirmation');
      setResult(null);
      setLoading(false);
    }
  }, [open]);
  
  // Handle delete operation
  const handleDelete = async () => {
    if (!item) return;
    
    try {
      setLoading(true);
      setStep('loading');
      
      const success = await onDelete(item.id);
      
      if (success) {
        setResult({
          success: true,
          message: `${typeName.capitalSingular} berhasil dihapus`
        });
      } else {
        setResult({
          success: false,
          message: `Gagal menghapus ${typeName.singular}`
        });
      }
      
      setStep('result');
    } catch (err) {
      console.error(`Error deleting ${itemType}:`, err);
      setResult({
        success: false,
        message: `Terjadi kesalahan saat menghapus ${typeName.singular}`
      });
      setStep('result');
    } finally {
      setLoading(false);
    }
  };
  
  // Handle dialog close with proper cleanup
  const handleDialogClose = (isOpen: boolean) => {
    // Only allow closing if not loading
    if (!isOpen && !loading) {
      // If we're in the result step and it was successful, just close
      if (step === 'result') {
        onOpenChange(false);
      } else if (step !== 'loading') {
        // Allow closing if not in loading state
        onOpenChange(false);
      }
    }
    return !loading; // Prevent closing if loading
  };
  
  // Render confirmation content
  const renderConfirmationContent = () => (
    <>
      <DialogHeader>
        <DialogTitle>Hapus {typeName.capitalSingular}</DialogTitle>
        <DialogDescription>
          Apakah Anda yakin ingin menghapus {typeName.singular} ini? Tindakan ini tidak dapat dibatalkan.
        </DialogDescription>
      </DialogHeader>
      
      {item && formatItemDetails && (
        <div className="py-4">
          {formatItemDetails(item)}
        </div>
      )}
      
      <DialogFooter>
        <Button
          variant="outline"
          onClick={() => onOpenChange(false)}
          disabled={loading}
        >
          Batal
        </Button>
        <Button
          variant="destructive"
          onClick={handleDelete}
          disabled={loading}
        >
          Hapus {typeName.capitalSingular}
        </Button>
      </DialogFooter>
    </>
  );
  
  // Render loading content
  const renderLoadingContent = () => (
    <div className="py-8 flex flex-col items-center justify-center">
      <Loader2 className="h-8 w-8 animate-spin text-primary mb-4" />
      <p>Menghapus {typeName.singular}...</p>
    </div>
  );
  
  // Render result content
  const renderResultContent = () => {
    if (!result) return null;
    
    return (
      <>
        <DialogHeader>
          <DialogTitle>
            {result.success ? 'Berhasil' : 'Gagal'}
          </DialogTitle>
        </DialogHeader>
        
        <div className="py-6 flex flex-col items-center justify-center text-center">
          {result.success ? (
            <CheckCircle2 className="h-16 w-16 text-green-500 mb-4" />
          ) : (
            <AlertCircle className="h-16 w-16 text-red-500 mb-4" />
          )}
          
          <h3 className="text-lg font-semibold mb-2">
            {result.message}
          </h3>
          
          <div className="flex gap-2 mt-4">
            <Button onClick={() => handleDialogClose(false)}>
              {result.success ? 'Selesai' : 'Tutup'}
            </Button>
          </div>
        </div>
      </>
    );
  };
  
  return (
    <Dialog open={open} onOpenChange={handleDialogClose}>
      <DialogContent>
        {step === 'confirmation' && renderConfirmationContent()}
        {step === 'loading' && renderLoadingContent()}
        {step === 'result' && renderResultContent()}
      </DialogContent>
    </Dialog>
  );
};

export default DeleteDialog;
