'use client';

import { useState, useEffect } from 'react';
import Link from 'next/link';
import { useRouter } from 'next/navigation';
import { Button } from '@/components/ui/button';
import { DataTable } from '@/components/ui/data-table';
import { salaryApi } from '@/lib/api/salary';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { SalaryStatusBadge } from './SalaryStatusBadge';
import ErrorPage from '@/components/error/error-page';
import { BackButton } from '@/components/ui/BackButton';
import { PageTitle } from '@/components/ui/PageTitle';
import { SearchFilter } from '@/components/ui/search-filter';

interface EmployeeSalaryDetailContentProps {
  employeeId: string;
}

interface DisplaySalary {
  id: number;
  salaryId: string;
  period: string;
  totalSalary: string;
  paymentStatus: 'paid' | 'unpaid';
  baseSalary: string;
  bonus: string;
  payReduction: string;
  allowance: string; // Added for total_allowance
}

const EmployeeSalaryDetailContent: React.FC<
  EmployeeSalaryDetailContentProps
> = ({ employeeId }) => {
  const router = useRouter();
  const [salaries, setSalaries] = useState<DisplaySalary[]>([]);
  const [filteredSalaries, setFilteredSalaries] = useState<DisplaySalary[]>([]);
  const [searchQuery, setSearchQuery] = useState('');
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [employeeDetails, setEmployeeDetails] = useState<{
    fullname: string;
    role: string;
    department: string;
    bank_account: string;
    bank_name: string;
  } | null>(null);

  // Format the period from YYYY-MM to Month YYYY
  const formatPeriod = (period: string): string => {
    const [year, month] = period.split('-');
    const monthNames = [
      'Januari',
      'Februari',
      'Maret',
      'April',
      'Mei',
      'Juni',
      'Juli',
      'Agustus',
      'September',
      'Oktober',
      'November',
      'Desember',
    ];
    return `${monthNames[parseInt(month) - 1]} ${year}`;
  };

  // Format currency to Indonesian Rupiah
  const formatCurrency = (amount: number): string => {
    return `Rp${amount.toLocaleString('id-ID')}`;
  };

  useEffect(() => {
    const fetchEmployeeSalaries = async () => {
      try {
        setLoading(true);
        const response = await salaryApi.getSalariesByEmployeeId(employeeId);

        // Check if response is successful and data is an array (directly in data field)
        if (response.success && Array.isArray(response.data)) {
          if (response.data.length > 0) {
            // Store employee details from the first record
            setEmployeeDetails(response.data[0].employee_details);
          }

          // Transform API data to display format
          const transformedData: DisplaySalary[] = response.data.map(
            (item, index: number) => ({
              id: index + 1,
              salaryId: item.id,
              period: formatPeriod(item.period),
              totalSalary: formatCurrency(item.total_salary),
              paymentStatus: item.payment_status as 'paid' | 'unpaid',
              baseSalary: formatCurrency(item.base_salary),
              bonus: formatCurrency(item.total_bonus), // Changed from bonus
              payReduction: formatCurrency(item.total_deduction), // Changed from pay_reduction
              allowance: formatCurrency(item.total_allowance), // Added for total_allowance
            })
          );

          setSalaries(transformedData);
          setFilteredSalaries(transformedData);
        } else {
          console.warn(
            'API returned success but data is not in expected format:',
            response
          );
          setError('Data tidak dalam format yang diharapkan');
        }
      } catch (err) {
        console.error('Error fetching employee salaries:', err);

        // Check if this is a "not found" error
        const errorMsg = err instanceof Error ? err.message : String(err);
        const isNotFoundError =
          errorMsg.includes('not found') ||
          (typeof err === 'object' &&
            err !== null &&
            'response' in err &&
            typeof err.response === 'object' &&
            err.response !== null &&
            'data' in err.response &&
            typeof err.response.data === 'object' &&
            err.response.data !== null &&
            'message' in err.response.data &&
            typeof err.response.data.message === 'string' &&
            err.response.data.message.includes('not found'));

        // Set the appropriate error message
        setError(
          isNotFoundError
            ? 'not_found'
            : err instanceof Error
              ? err.message
              : 'An unknown error occurred'
        );
      } finally {
        setLoading(false);
      }
    };

    if (employeeId) {
      fetchEmployeeSalaries();
    }
  }, [employeeId]);

  const handleSearch = (value: string) => {
    setSearchQuery(value);

    if (!value) {
      setFilteredSalaries(salaries);
      return;
    }

    const filtered = salaries.filter(
      (salary) =>
        salary.period.toLowerCase().includes(value.toLowerCase()) ||
        salary.salaryId.toLowerCase().includes(value.toLowerCase())
    );
    setFilteredSalaries(filtered);
  };

  const handleBack = () => {
    router.back();
  };

  const columns = [
    { key: 'id', header: 'NO', width: '60px' },
    { key: 'period', header: 'PERIODE', width: '180px' },
    { key: 'baseSalary', header: 'GAJI POKOK' },
    { key: 'bonus', header: 'TOTAL BONUS' },
    { key: 'payReduction', header: 'TOTAL POTONGAN' },
    { key: 'allowance', header: 'TOTAL TUNJANGAN' },
    { key: 'totalSalary', header: 'TOTAL GAJI' },
    {
      key: 'paymentStatus',
      header: 'STATUS',
      render: (salary: DisplaySalary) => (
        <SalaryStatusBadge status={salary.paymentStatus} />
      ),
    },
    {
      key: 'actions',
      header: 'AKSI',
      render: (salary: DisplaySalary) => (
        <div className="text-left">
          <Link href={`/employee/salary/${salary.salaryId}`}>
            <Button
              variant="outline"
              className="border-[#AB8B3B] text-[#9B7533] hover:bg-[#AB8B3B]/10 hover:text-[#9B7533] transition-colors duration-200"
            >
              Lihat Detail
            </Button>
          </Link>
        </div>
      ),
    },
  ];

  if (loading) {
    return (
      <div className="flex h-screen w-full items-center justify-center">
        <div className="text-center">
          <div className="h-8 w-8 animate-spin rounded-full border-2 border-solid border-[#B78F38] border-t-transparent"></div>
          <p className="mt-4 text-gray-600">Memuat...</p>
        </div>
      </div>
    );
  }

  if (error) {
    // Handle "not found" error specially with 404 page
    if (error === 'not_found') {
      return (
        <ErrorPage
          statusCode="404"
          title="Karyawan Tidak Ditemukan"
          message="Maaf, data karyawan yang Anda cari tidak dapat ditemukan."
          showHomeButton={true}
          showBackButton={true}
          showRetryButton={false}
          homeHref="/"
          homeLabel="Kembali ke Beranda"
          backLabel="Kembali"
        />
      );
    }

    // Handle other errors with 500 page
    return (
      <ErrorPage
        statusCode="500"
        title="Kesalahan Memuat Data"
        message={`Terjadi kesalahan saat memuat data penggajian karyawan: ${error}`}
        showHomeButton={true}
        showBackButton={true}
        showRetryButton={true}
        homeHref="/"
        homeLabel="Kembali ke Beranda"
        backLabel="Kembali"
        retryLabel="Coba Lagi"
      />
    );
  }

  if (!loading && salaries.length === 0) {
    return (
      <div className="max-w-5xl mx-auto">
        <div className="mb-6 flex items-center gap-4">
          <BackButton onClick={handleBack} />
          <PageTitle
            title="Penggajian Karyawan"
            subtitle="Riwayat penggajian karyawan"
          />
        </div>

        <Card className="bg-white border shadow-sm">
          <CardContent className="pt-6 pb-6 text-center">
            <p className="text-gray-500">
              Tidak ada data penggajian untuk karyawan ini.
            </p>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="max-w-6xl mx-auto">
      <div className="mb-6 flex items-center gap-4">
        <BackButton onClick={handleBack} />
        <PageTitle
          title="Riwayat Penggajian Karyawan"
          subtitle="Daftar riwayat penggajian karyawan"
        />
      </div>

      {employeeDetails && (
        <Card className="bg-white border shadow-sm mb-8">
          <CardHeader className="pb-2">
            <CardTitle className="text-2xl">
              {employeeDetails.fullname}
            </CardTitle>
            <CardDescription className="text-gray-500">
              {employeeDetails.role} - {employeeDetails.department}
            </CardDescription>
          </CardHeader>
          <CardContent className="pt-4 pb-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <p className="text-sm font-medium text-gray-500">ID Karyawan</p>
                <p className="font-semibold">{employeeId}</p>
              </div>
              <div>
                <p className="text-sm font-medium text-gray-500">
                  Informasi Bank
                </p>
                <p className="font-semibold">
                  {employeeDetails.bank_name} - {employeeDetails.bank_account}
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      <Card className="bg-white border shadow-sm">
        <CardHeader className="pb-4">
          <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-4">
            <CardTitle className="text-xl">
              Daftar Penggajian
              <span className="ml-2 px-2.5 py-0.5 rounded-full bg-gray-100 text-gray-600 text-xs font-medium">
                {salaries.length} periode
              </span>
            </CardTitle>
            <div className="w-full md:w-auto">
              <SearchFilter
                search={searchQuery}
                onSearchChange={handleSearch}
                filters={[]}
                searchPlaceholder="Cari periode atau ID"
              />
            </div>
          </div>
        </CardHeader>
        <CardContent className="px-0">
          <div className="overflow-x-auto">
            <DataTable
              columns={columns}
              data={filteredSalaries}
              keyExtractor={(item) => item.id.toString()}
              filterComponent={null}
              searchComponent={null}
              emptyStateMessage="Tidak ada data penggajian yang ditemukan."
            />
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default EmployeeSalaryDetailContent;
