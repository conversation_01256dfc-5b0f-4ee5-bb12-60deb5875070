'use client';

import { useState } from 'react';
import { Button } from '@/components/ui/button';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogDescription,
  DialogFooter,
} from '@/components/ui/dialog';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { SalaryRecord } from '@/types/salary';

interface SalaryAmountUpdateFormProps {
  salary: SalaryRecord;
  open: boolean;
  onOpenChange: (open: boolean) => void;
  updateAmounts: (baseSalary: number) => Promise<boolean>;
  isLoading: boolean;
}

export default function SalaryAmountUpdateForm({
  salary,
  open,
  onOpenChange,
  updateAmounts,
  isLoading,
}: SalaryAmountUpdateFormProps) {
  const [baseSalary, setBaseSalary] = useState<number>(salary.base_salary || 0);

  // Format currency to Indonesian Rupiah
  const formatCurrency = (amount: number): string => {
    return `Rp${amount.toLocaleString('id-ID')}`;
  };

  const handleSubmit = async () => {
    const success = await updateAmounts(baseSalary);
    if (success) {
      onOpenChange(false);
    }
  };

  // Determine if changes have been made to enable/disable submit button
  const hasChanges = baseSalary !== salary.base_salary;

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent>
        <DialogHeader>
          <DialogTitle>Update Gaji Pokok</DialogTitle>
          <DialogDescription>
            Ubah jumlah gaji pokok untuk periode {salary.period.split('-')[1]}/
            {salary.period.split('-')[0]}.
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-4 py-4">
          <div className="space-y-2">
            <Label htmlFor="base_salary">Gaji Pokok</Label>
            <div className="relative">
              <Input
                id="base_salary"
                type="number"
                value={baseSalary}
                onChange={(e) => setBaseSalary(Number(e.target.value))}
                className="pl-8"
                disabled={isLoading}
              />
              <div className="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none">
                <span className="text-gray-500">Rp</span>
              </div>
            </div>
            <p className="text-xs text-gray-500">
              Sebelumnya: {formatCurrency(salary.base_salary)}
            </p>
          </div>
        </div>

        <DialogFooter>
          <Button
            variant="outline"
            onClick={() => onOpenChange(false)}
            disabled={isLoading}
          >
            Batal
          </Button>
          <Button
            onClick={handleSubmit}
            disabled={isLoading || !hasChanges}
            className="bg-[#AB8B3B] hover:bg-[#9B7533] text-white"
          >
            {isLoading ? 'Menyimpan...' : 'Simpan'}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
