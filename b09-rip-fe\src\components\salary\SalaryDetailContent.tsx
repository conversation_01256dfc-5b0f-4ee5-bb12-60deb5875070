'use client';

import { useState } from 'react';
import { useRouter, usePathname } from 'next/navigation';
import {
  Edit,
  Trash2,
  Plus,
  CheckCircle,
  XCircle,
  Loader2,
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { toast } from 'sonner';
import { useRBAC } from '@/hooks/useRBAC';

import { PaymentStatus } from './SalaryStatusBadge';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogFooter,
  DialogDescription,
} from '@/components/ui/dialog';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { useSalaryDetail } from '@/hooks/useSalaryDetail';
import { useSalaryBonuses } from '@/hooks/useSalaryBonuses';
import { useSalaryAllowances } from '@/hooks/useSalaryAllowance';
import { useSalaryDeductions } from '@/hooks/useSalaryDeductions';
import { SalaryStatusBadge } from './SalaryStatusBadge';
import { SalaryTimeline } from './SalaryTimeline';
import ErrorPage from '@/components/error/error-page';
import { PageTitle } from '@/components/ui/PageTitle';
import { BackButton } from '@/components/ui/BackButton';
import AddBonusForm from './AddBonusForm';
import AddDeductionForm from './AddDeductionForm';
import AddAllowanceForm from './AddAllowanceForm';
import BonusListComponent from './BonusListComponent';
import UpdateBonusForm from './UpdateBonusForm';
import DeleteBonusDialog from './DeleteBonusDialog';
import AllowanceListComponent from './AllowanceListComponent';
import UpdateAllowanceForm from './UpdateAllowanceForm';
import DeleteAllowanceDialog from './DeleteAllowanceDialog';
import { Bonus, Allowance, Deduction } from '@/types/salary';
import DeductionListComponent from './DeductionListComponent';
import UpdateDeductionForm from './UpdateDeductionForm';
import DeleteDeductionDialog from './DeleteDeductionDialog';
import DeductionDetailComponent from './DeductionDetailComponent';
import BonusDetailComponent from './BonusDetailComponent';
import AllowanceDetailComponent from './AllowanceDetailComponent';

interface SalaryDetailContentProps {
  id: string;
}

const SalaryDetailContent: React.FC<SalaryDetailContentProps> = ({ id }) => {
  const router = useRouter();
  const pathname = usePathname();
  const {
    salaryData,
    loading,
    refreshing,
    error,
    deleteSalary,
    deleteLoading,
    refreshData,
    updatePaymentStatus,
    statusUpdateLoading,
  } = useSalaryDetail(id);

  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [bonusFormOpen, setBonusFormOpen] = useState(false);
  const [deductionFormOpen, setDeductionFormOpen] = useState(false);
  const [allowanceFormOpen, setAllowanceFormOpen] = useState(false);
  const [paymentStatusDialogOpen, setPaymentStatusDialogOpen] = useState(false);
  const [paymentStatusAction, setPaymentStatusAction] =
    useState<PaymentStatus>('paid');
  const [highlightCard, setHighlightCard] = useState(false);

  // Bonus management state
  const [selectedBonus, setSelectedBonus] = useState<Bonus | null>(null);
  const [updateBonusOpen, setUpdateBonusOpen] = useState(false);
  const [deleteBonusOpen, setDeleteBonusOpen] = useState(false);

  // Allowance management state
  const [selectedAllowance, setSelectedAllowance] = useState<Allowance | null>(
    null
  );
  const [updateAllowanceOpen, setUpdateAllowanceOpen] = useState(false);
  const [deleteAllowanceOpen, setDeleteAllowanceOpen] = useState(false);

  // Deduction management state
  const [selectedDeduction, setSelectedDeduction] = useState<Deduction | null>(
    null
  );
  const [updateDeductionOpen, setUpdateDeductionOpen] = useState(false);
  const [deleteDeductionOpen, setDeleteDeductionOpen] = useState(false);
  const [deductionDetailOpen, setDeductionDetailOpen] = useState(false);
  const [bonusDetailOpen, setBonusDetailOpen] = useState(false);
  const [allowanceDetailOpen, setAllowanceDetailOpen] = useState(false);

  // Check if user has permission to update payment status
  const { hasRole } = useRBAC();
  const canUpdatePaymentStatus = hasRole(['Admin', 'Finance']);

  // Use the useSalaryBonuses hook
  const {
    bonuses,
    loading: bonusesLoading,
    error: bonusesError,
    deleteBonus,
    refreshData: refreshBonusData,
  } = useSalaryBonuses(id);

  const {
    allowances,
    loading: allowancesLoading,
    error: allowancesError,
    deleteAllowance,
    refreshData: refreshAllowanceData,
  } = useSalaryAllowances(id);

  // Use the useSalaryDeductions hook
  const {
    deductions,
    loading: deductionsLoading,
    error: deductionsError,
    deleteDeduction,
    refreshData: refreshDeductionData,
  } = useSalaryDeductions(id);

  // Handlers for bonus management
  const handleViewBonus = (bonus: Bonus) => {
    setSelectedBonus(bonus);
    setBonusDetailOpen(true);
  };

  const handleEditBonus = (bonus: Bonus) => {
    setSelectedBonus(bonus);
    setUpdateBonusOpen(true);
  };

  const handleDeleteBonus = (bonus: Bonus) => {
    setSelectedBonus(bonus);
    setDeleteBonusOpen(true);
  };

  // Handlers for allowance management
  const handleViewAllowance = (allowance: Allowance) => {
    setSelectedAllowance(allowance);
    setAllowanceDetailOpen(true);
  };

  const handleEditAllowance = (allowance: Allowance) => {
    setSelectedAllowance(allowance);
    setUpdateAllowanceOpen(true);
  };

  const handleDeleteAllowance = (allowance: Allowance) => {
    setSelectedAllowance(allowance);
    setDeleteAllowanceOpen(true);
  };

  // Handlers for deduction management
  const handleViewDeduction = (deduction: Deduction) => {
    setSelectedDeduction(deduction);
    setDeductionDetailOpen(true);
  };

  const handleEditDeduction = (deduction: Deduction) => {
    setSelectedDeduction(deduction);
    setDeductionDetailOpen(false);
    setUpdateDeductionOpen(true);
  };

  const handleDeleteDeduction = (deduction: Deduction) => {
    setSelectedDeduction(deduction);
    setDeductionDetailOpen(false);
    setDeleteDeductionOpen(true);
  };

  // Check if current path includes employee/salary/ route
  const isEmployeeSalaryRoute = pathname?.includes('employee/salary/');

  // Format the period from YYYY-MM to Month YYYY
  const formatPeriod = (period: string): string => {
    const [year, month] = period.split('-');
    const monthNames = [
      'Januari',
      'Februari',
      'Maret',
      'April',
      'Mei',
      'Juni',
      'Juli',
      'Agustus',
      'September',
      'Oktober',
      'November',
      'Desember',
    ];
    return `${monthNames[parseInt(month) - 1]} ${year}`;
  };

  // Format currency to Indonesian Rupiah
  const formatCurrency = (amount: number): string => {
    return `Rp${amount.toLocaleString('id-ID')}`;
  };

  const handleBack = () => {
    router.push(`/employee/salary`);
  };

  const handleEdit = () => {
    router.push(`/employee/salary/${id}/update`);
  };

  const handleDelete = () => {
    setDeleteDialogOpen(true);
  };

  const handleDeleteConfirm = async () => {
    await deleteSalary();
  };

  // Handler for toggling payment status
  const handleTogglePaymentStatus = () => {
    const newStatus = salaryData?.payment_status === 'paid' ? 'unpaid' : 'paid';

    setPaymentStatusAction(newStatus);
    setPaymentStatusDialogOpen(true);
  };

  // Handler for confirming payment status update
  const handleConfirmPaymentStatus = async () => {
    const success = await updatePaymentStatus(paymentStatusAction);
    if (success) {
      setPaymentStatusDialogOpen(false);

      // Show a specific success message based on the action
      const actionText =
        paymentStatusAction === 'paid' ? 'dibayarkan' : 'belum dibayarkan';
      toast.success(`Gaji berhasil ditandai sebagai ${actionText}`);

      // Refresh the data and trigger highlight effect
      refreshSalaryData();

      // Set up highlight effect after refresh
      setHighlightCard(true);
      setTimeout(() => setHighlightCard(false), 1500);
    }
  };

  // Function to refresh the entire salary data
  // Only use this for operations that need to refresh all salary data components
  // For component-specific updates, use refreshBonusData, refreshAllowanceData, or refreshDeductionData
  // This is currently used by handleConfirmPaymentStatus to refresh all data after payment status changes
  const refreshSalaryData = () => {
    if (id) {
      // Use the refreshData function from the hook instead of reloading the page
      refreshData();
    }
  };

  // Add a refresh indicator component
  const RefreshIndicator = () => {
    if (!refreshing) return null;

    return (
      <div className="absolute inset-0 bg-black/5 flex items-center justify-center z-10 rounded-md">
        <div className="bg-white p-4 rounded-md shadow-md flex flex-col items-center">
          <div className="h-8 w-8 animate-spin rounded-full border-3 border-solid border-[#B78F38] border-t-transparent mb-2"></div>
          <span className="text-gray-700 font-medium">Memperbarui data...</span>
        </div>
      </div>
    );
  };

  if (loading) {
    return (
      <div className="flex h-screen w-full items-center justify-center">
        <div className="text-center">
          <div className="h-8 w-8 animate-spin rounded-full border-2 border-solid border-[#B78F38] border-t-transparent"></div>
          <p className="mt-4 text-gray-600">Memuat...</p>
        </div>
      </div>
    );
  }

  if (error) {
    // Handle "not found" error specially with 404 page
    if (error === 'not_found') {
      return (
        <ErrorPage
          statusCode="404"
          title="Data Penggajian Tidak Ditemukan"
          message="Maaf, data penggajian yang Anda cari tidak dapat ditemukan."
          showHomeButton={true}
          showBackButton={true}
          showRetryButton={false}
          homeHref="/"
          homeLabel="Kembali ke Beranda"
          backLabel="Kembali"
        />
      );
    }

    // Handle other errors with 500 page
    return (
      <ErrorPage
        statusCode="500"
        title="Kesalahan Memuat Data"
        message={`Terjadi kesalahan saat memuat data penggajian: ${error}`}
        showHomeButton={true}
        showBackButton={true}
        showRetryButton={true}
        homeHref="/"
        homeLabel="Kembali ke Beranda"
        backLabel="Kembali"
        retryLabel="Coba Lagi"
      />
    );
  }

  if (!salaryData) {
    return (
      <ErrorPage
        statusCode="404"
        title="Data Penggajian Tidak Ditemukan"
        message="Tidak ada data yang ditemukan untuk ID penggajian ini."
        showHomeButton={true}
        showBackButton={true}
        homeHref="/dashboard"
        homeLabel="Kembali ke Dashboard"
        backLabel="Kembali ke Daftar Penggajian"
      />
    );
  }

  const paymentStatus =
    salaryData.payment_status === 'paid'
      ? 'Sudah Dibayarkan'
      : 'Belum Dibayarkan';

  return (
    <div className="p-8 max-w-5xl mx-auto">
      <div className="flex items-center gap-4 mb-6">
        <BackButton onClick={handleBack} />
        <PageTitle title="Detail Penggajian" />
        <div className="ml-auto flex space-x-2">
          {/* Payment Status Toggle Button - Only visible to Finance and Admin users */}
          {canUpdatePaymentStatus && (
            <Button
              onClick={handleTogglePaymentStatus}
              className={`flex items-center gap-1 ${
                salaryData.payment_status === 'paid'
                  ? 'bg-amber-600 hover:bg-amber-700 text-white' // Unpaid button style
                  : 'bg-green-600 hover:bg-green-700 text-white' // Paid button style
              }`}
              disabled={statusUpdateLoading}
            >
              {statusUpdateLoading ? (
                <Loader2 className="h-4 w-4 animate-spin" />
              ) : salaryData.payment_status === 'paid' ? (
                <XCircle className="h-4 w-4" />
              ) : (
                <CheckCircle className="h-4 w-4" />
              )}
              {statusUpdateLoading
                ? 'Memproses...'
                : salaryData.payment_status === 'paid'
                  ? 'Belum Dibayarkan'
                  : 'Sudah Dibayar'}
            </Button>
          )}

          {/* Edit Button - Disabled when salary is paid */}
          <Button
            variant="outline"
            onClick={handleEdit}
            className="flex items-center gap-1"
            disabled={salaryData.payment_status === 'paid'}
            title={
              salaryData.payment_status === 'paid'
                ? 'Tidak dapat mengedit gaji yang sudah dibayarkan'
                : 'Edit gaji'
            }
          >
            <Edit className="h-4 w-4" />
            Edit
          </Button>

          {!isEmployeeSalaryRoute && (
            <Button
              variant="destructive"
              onClick={handleDelete}
              className="flex items-center gap-1"
            >
              <Trash2 className="h-4 w-4" />
              Hapus
            </Button>
          )}
        </div>
      </div>

      <Card
        className={`bg-white border shadow-sm relative transition-all duration-300 ${
          highlightCard ? 'ring-2 ring-green-400 shadow-lg' : ''
        }`}
      >
        <RefreshIndicator />
        <CardHeader className="pb-4 border-b">
          <div className="flex justify-between items-center">
            <div>
              <CardTitle className="text-2xl font-bold">
                {salaryData.employee_details.fullname}
              </CardTitle>
              <p className="text-gray-500 mt-1">ID Gaji: {salaryData.id}</p>
            </div>
            <div className="flex items-center gap-3">
              <Badge className="px-3 py-1 rounded-md bg-yellow-100 text-yellow-800">
                {formatPeriod(salaryData.period)}
              </Badge>
              <SalaryStatusBadge
                status={salaryData.payment_status as 'paid' | 'unpaid'}
              />
            </div>
          </div>
        </CardHeader>

        <CardContent className="pt-6">
          <div className="grid grid-cols-2 gap-6">
            {/* Left column - Employee Information */}
            <div>
              <h3 className="text-lg font-semibold text-gray-500 mb-4">
                Informasi Karyawan
              </h3>
              <div className="space-y-4">
                <div>
                  <p className="text-sm font-medium text-gray-500">
                    ID Karyawan
                  </p>
                  <p className="font-semibold">{salaryData.employee_id}</p>
                </div>

                <div>
                  <p className="text-sm font-medium text-gray-500">Role</p>
                  <p className="font-semibold">
                    {salaryData.employee_details.role}
                  </p>
                </div>

                <div>
                  <p className="text-sm font-medium text-gray-500">
                    Departemen
                  </p>
                  <p className="font-semibold">
                    {salaryData.employee_details.department}
                  </p>
                </div>

                <div>
                  <p className="text-sm font-medium text-gray-500">Bank</p>
                  <p className="font-semibold">
                    {salaryData.employee_details.bank_name}
                  </p>
                </div>

                <div>
                  <p className="text-sm font-medium text-gray-500">
                    Nomor Rekening
                  </p>
                  <p className="font-semibold">
                    {salaryData.employee_details.bank_account}
                  </p>
                </div>
              </div>
            </div>

            {/* Right column - Salary Details */}
            <div>
              <h3 className="text-lg font-semibold text-gray-500 mb-4">
                Rincian Gaji
              </h3>
              <div className="space-y-4">
                <div>
                  <p className="text-sm font-medium text-gray-500">
                    Gaji Pokok
                  </p>
                  <p className="font-semibold">
                    {formatCurrency(salaryData.base_salary)}
                  </p>
                </div>

                <div>
                  <p className="text-sm font-medium text-gray-500">
                    Total Bonus
                  </p>
                  <p className="font-semibold text-green-600">
                    {formatCurrency(salaryData.total_bonus)}
                  </p>
                </div>

                <div>
                  <p className="text-sm font-medium text-gray-500">
                    Total Potongan
                  </p>
                  <p className="font-semibold text-red-600">
                    {formatCurrency(salaryData.total_deduction)}
                  </p>
                </div>

                <div>
                  <p className="text-sm font-medium text-gray-500">
                    Total Tunjangan
                  </p>
                  <p className="font-semibold text-blue-600">
                    {formatCurrency(salaryData.total_allowance)}
                  </p>
                </div>

                <div>
                  <p className="text-sm font-medium text-gray-500">
                    Total Gaji
                  </p>
                  <p className="font-semibold text-[#AB8B3B]">
                    {formatCurrency(salaryData.total_salary)}
                  </p>
                </div>

                <div>
                  <p className="text-sm font-medium text-gray-500">
                    Status Pembayaran
                  </p>
                  <p
                    className={`font-semibold ${
                      salaryData.payment_status === 'paid'
                        ? 'text-green-600'
                        : 'text-amber-600'
                    }`}
                  >
                    {paymentStatus}
                  </p>
                </div>
              </div>
            </div>
          </div>

          {/* Tunjangan List Section */}
          <div className="mt-8 mb-4 border-t pt-6">
            <div className="flex justify-between items-center mb-4">
              <h3 className="text-lg font-semibold text-gray-500">
                Daftar Tunjangan
              </h3>
              {salaryData.payment_status !== 'paid' && (
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setAllowanceFormOpen(true)}
                  className="flex items-center gap-1"
                >
                  <Plus className="h-4 w-4" />
                  Tambah Tunjangan
                </Button>
              )}
            </div>

            {allowancesError && (
              <div className="mb-4 p-3 bg-red-50 text-red-700 rounded-md">
                <p>Gagal memuat daftar tunjangan: {allowancesError}</p>
              </div>
            )}

            <AllowanceListComponent
              allowances={allowances}
              loading={allowancesLoading}
              onView={handleViewAllowance}
              onEdit={handleEditAllowance}
              onDelete={handleDeleteAllowance}
              isActionDisabled={salaryData.payment_status === 'paid'}
            />
          </div>

          {/* Bonus List Section */}
          <div className="mt-8 mb-4 border-t pt-6">
            <div className="flex justify-between items-center mb-4">
              <h3 className="text-lg font-semibold text-gray-500">
                Daftar Bonus
              </h3>
              {salaryData.payment_status !== 'paid' && (
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setBonusFormOpen(true)}
                  className="flex items-center gap-1"
                >
                  <Plus className="h-4 w-4" />
                  Tambah Bonus
                </Button>
              )}
            </div>

            {bonusesError && (
              <div className="mb-4 p-3 bg-red-50 text-red-700 rounded-md">
                <p>Gagal memuat daftar bonus: {bonusesError}</p>
              </div>
            )}

            <BonusListComponent
              bonuses={bonuses}
              loading={bonusesLoading}
              onView={handleViewBonus}
              onEdit={handleEditBonus}
              onDelete={handleDeleteBonus}
              isActionDisabled={salaryData.payment_status === 'paid'}
            />
          </div>

          {/* Deduction List Section */}
          <div className="mt-8 mb-4 border-t pt-6">
            <div className="flex justify-between items-center mb-4">
              <h3 className="text-lg font-semibold text-gray-500">
                Daftar Potongan
              </h3>
              {salaryData.payment_status !== 'paid' && (
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setDeductionFormOpen(true)}
                  className="flex items-center gap-1"
                >
                  <Plus className="h-4 w-4" />
                  Tambah Potongan
                </Button>
              )}
            </div>

            {deductionsError && (
              <div className="mb-4 p-3 bg-red-50 text-red-700 rounded-md">
                <p>Gagal memuat daftar potongan: {deductionsError}</p>
              </div>
            )}

            <DeductionListComponent
              deductions={deductions}
              loading={deductionsLoading}
              onEdit={handleEditDeduction}
              onDelete={handleDeleteDeduction}
              onView={handleViewDeduction}
              isActionDisabled={salaryData.payment_status === 'paid'}
            />
          </div>

          {/* Salary History Timeline */}
          <SalaryTimeline salary={salaryData} />
        </CardContent>
      </Card>

      {/* Delete Confirmation Dialog */}
      <Dialog open={deleteDialogOpen} onOpenChange={setDeleteDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Hapus Data Penggajian</DialogTitle>
            <DialogDescription>
              Apakah Anda yakin ingin menghapus data penggajian ini? Tindakan
              ini tidak dapat dibatalkan.
            </DialogDescription>
          </DialogHeader>
          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => setDeleteDialogOpen(false)}
              disabled={deleteLoading}
            >
              Batal
            </Button>
            <Button
              variant="destructive"
              onClick={handleDeleteConfirm}
              disabled={deleteLoading}
            >
              {deleteLoading ? 'Menghapus...' : 'Hapus'}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Salary Component Forms */}
      <AddBonusForm
        salaryId={id}
        open={bonusFormOpen}
        onOpenChange={setBonusFormOpen}
        onSuccess={refreshBonusData}
      />

      <AddDeductionForm
        salaryId={id}
        open={deductionFormOpen}
        onOpenChange={setDeductionFormOpen}
        onSuccess={refreshDeductionData}
      />

      <AddAllowanceForm
        salaryId={id}
        open={allowanceFormOpen}
        onOpenChange={setAllowanceFormOpen}
        onSuccess={refreshAllowanceData}
      />

      {/* Bonus Management Forms */}
      <UpdateBonusForm
        bonus={selectedBonus}
        open={updateBonusOpen}
        onOpenChange={setUpdateBonusOpen}
        onSuccess={refreshBonusData}
      />

      <DeleteBonusDialog
        bonus={selectedBonus}
        open={deleteBonusOpen}
        onOpenChange={setDeleteBonusOpen}
        onDelete={deleteBonus}
      />

      {/* Allowance Management Forms */}
      <UpdateAllowanceForm
        allowance={selectedAllowance}
        open={updateAllowanceOpen}
        onOpenChange={setUpdateAllowanceOpen}
        onSuccess={refreshAllowanceData}
      />

      {/* Use the correct DeleteAllowanceDialog */}
      <DeleteAllowanceDialog
        allowance={selectedAllowance}
        open={deleteAllowanceOpen}
        onOpenChange={setDeleteAllowanceOpen}
        onDelete={deleteAllowance}
      />

      {/* Bonus Detail Component */}
      {selectedBonus && (
        <BonusDetailComponent
          bonus={selectedBonus}
          isOpen={bonusDetailOpen}
          onClose={() => {
            setBonusDetailOpen(false);
            setSelectedBonus(null);
          }}
          onEdit={() => handleEditBonus(selectedBonus)}
          onDelete={() => handleDeleteBonus(selectedBonus)}
          isActionDisabled={salaryData.payment_status === 'paid'}
        />
      )}

      {/* Allowance Detail Component */}
      {selectedAllowance && (
        <AllowanceDetailComponent
          allowance={selectedAllowance}
          isOpen={allowanceDetailOpen}
          onClose={() => {
            setAllowanceDetailOpen(false);
            setSelectedAllowance(null);
          }}
          onEdit={() => handleEditAllowance(selectedAllowance)}
          onDelete={() => handleDeleteAllowance(selectedAllowance)}
          isActionDisabled={salaryData.payment_status === 'paid'}
        />
      )}

      {/* Deduction Management Forms */}
      {selectedDeduction && (
        <>
          <DeductionDetailComponent
            deduction={selectedDeduction}
            isOpen={deductionDetailOpen}
            onClose={() => {
              setDeductionDetailOpen(false);
              setSelectedDeduction(null);
            }}
            onEdit={() => handleEditDeduction(selectedDeduction)}
            onDelete={() => handleDeleteDeduction(selectedDeduction)}
            isActionDisabled={salaryData.payment_status === 'paid'}
          />

          <UpdateDeductionForm
            deduction={selectedDeduction}
            open={updateDeductionOpen}
            onOpenChange={setUpdateDeductionOpen}
            onSuccess={refreshDeductionData}
          />

          <DeleteDeductionDialog
            deduction={selectedDeduction}
            open={deleteDeductionOpen}
            onOpenChange={setDeleteDeductionOpen}
            onDelete={deleteDeduction}
          />
        </>
      )}

      {/* Payment Status Update Confirmation Dialog */}
      <Dialog
        open={paymentStatusDialogOpen}
        onOpenChange={setPaymentStatusDialogOpen}
      >
        <DialogContent>
          <DialogHeader>
            <DialogTitle>
              {paymentStatusAction === 'paid'
                ? 'Konfirmasi Pembayaran Gaji'
                : 'Konfirmasi Pembatalan Pembayaran Gaji'}
            </DialogTitle>
            <DialogDescription>
              {paymentStatusAction === 'paid'
                ? 'Apakah Anda yakin ingin menandai gaji ini sebagai sudah dibayarkan?'
                : 'Apakah Anda yakin ingin menandai gaji ini sebagai belum dibayarkan?'}
            </DialogDescription>
          </DialogHeader>
          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => setPaymentStatusDialogOpen(false)}
              disabled={statusUpdateLoading}
            >
              Batal
            </Button>
            <Button
              onClick={handleConfirmPaymentStatus}
              disabled={statusUpdateLoading}
              className={
                paymentStatusAction === 'paid'
                  ? 'bg-green-600 hover:bg-green-700 text-white'
                  : ''
              }
            >
              {statusUpdateLoading
                ? 'Memproses...'
                : paymentStatusAction === 'paid'
                  ? 'Konfirmasi Pembayaran'
                  : 'Konfirmasi Pembatalan'}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default SalaryDetailContent;
