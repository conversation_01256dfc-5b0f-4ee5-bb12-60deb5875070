import React from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { useSalaryManagement } from '@/hooks/useSalaryManagement';
import SalaryTable from './SalaryTable';
import { SalarySearchFilter } from './SalarySearchFilter';
import { PageTitle } from '@/components/ui/PageTitle';

const SalaryManagementContent: React.FC = () => {
  const {
    salaries,
    loading,
    error,
    searchQuery,
    selectedPeriod,
    uniquePeriods,
    // Pagination variables not used in this component
    // currentPage,
    // totalPages,
    // totalItems,
    // setCurrentPage,
    handleSearchChange,
    handlePeriodChange,
    handleViewDetail,
    handleViewEmployeeDetail,
  } = useSalaryManagement();

  if (loading) {
    return (
      <div className="flex h-screen w-full items-center justify-center">
        <div className="text-center">
          <div className="h-8 w-8 animate-spin rounded-full border-2 border-solid border-[#B78F38] border-t-transparent"></div>
          <p className="mt-4 text-gray-600">Memuat...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="p-8 text-center">
        <div className="text-red-500 mb-4">Error: {error}</div>
        {error.includes('Sesi') || error.includes('login') ? (
          <Button
            onClick={() => (window.location.href = '/login')}
            className="bg-[#AB8B3B] hover:bg-[#9B7533] text-white"
          >
            Login Kembali
          </Button>
        ) : (
          <Button
            onClick={() => window.location.reload()}
            className="bg-[#AB8B3B] hover:bg-[#9B7533] text-white"
          >
            Coba Lagi
          </Button>
        )}
      </div>
    );
  }

  // Create period options from uniquePeriods
  const periodOptions = uniquePeriods.map((p) => ({ value: p, label: p }));

  const handlePeriodChangeWrapper = (value: string | undefined) => {
    handlePeriodChange(value || 'all');
  };

  return (
    <div className="container mx-auto py-6 px-6">
      <div className="flex justify-between items-center mb-6">
        <PageTitle
          title="Informasi Penggajian"
          subtitle="Data penggajian karyawan"
        />
      </div>

      <div className="bg-white rounded-lg shadow p-6">
        <SalarySearchFilter
          search={searchQuery}
          period={selectedPeriod}
          periods={periodOptions}
          onSearchChange={handleSearchChange}
          onPeriodChange={handlePeriodChangeWrapper}
        />

        <div className="overflow-x-auto">
          <SalaryTable
            salaries={salaries}
            onViewDetail={handleViewDetail}
            onViewEmployeeDetail={handleViewEmployeeDetail}
            isLoading={loading}
          />
        </div>
      </div>
    </div>
  );
};

export default SalaryManagementContent;
