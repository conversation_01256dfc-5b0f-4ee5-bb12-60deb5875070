'use client';

import { useState } from 'react';
import { Button } from '@/components/ui/button';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogDescription,
  DialogFooter,
} from '@/components/ui/dialog';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Label } from '@/components/ui/label';
import { PaymentStatus } from './SalaryStatusBadge';
import { SalaryRecord } from '@/types/salary';

interface SalaryPaymentStatusUpdateFormProps {
  salary: SalaryRecord;
  open: boolean;
  onOpenChange: (open: boolean) => void;
  updatePaymentStatus: (status: PaymentStatus) => Promise<boolean>;
  isLoading: boolean;
}

export default function SalaryPaymentStatusUpdateForm({
  salary,
  open,
  onOpenChange,
  updatePaymentStatus,
  isLoading,
}: SalaryPaymentStatusUpdateFormProps) {
  const [status, setStatus] = useState<PaymentStatus>(
    (salary.payment_status as PaymentStatus) || 'unpaid'
  );

  const handleSubmit = async () => {
    const success = await updatePaymentStatus(status);
    if (success) {
      onOpenChange(false);
    }
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent>
        <DialogHeader>
          <DialogTitle>Update Status Pembayaran</DialogTitle>
          <DialogDescription>
            Ubah status pembayaran untuk gaji periode{' '}
            {salary.period.split('-')[1]}/{salary.period.split('-')[0]}.
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-4 py-4">
          <div className="space-y-2">
            <Label htmlFor="payment-status">Status Pembayaran</Label>
            <Select
              value={status}
              onValueChange={(value) => setStatus(value as PaymentStatus)}
              disabled={isLoading}
            >
              <SelectTrigger id="payment-status">
                <SelectValue placeholder="Pilih status pembayaran" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="unpaid">Belum Dibayarkan</SelectItem>
                <SelectItem value="paid">Sudah Dibayarkan</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </div>

        <DialogFooter>
          <Button
            variant="outline"
            onClick={() => onOpenChange(false)}
            disabled={isLoading}
          >
            Batal
          </Button>
          <Button
            onClick={handleSubmit}
            disabled={isLoading || status === salary.payment_status}
            className="bg-[#AB8B3B] hover:bg-[#9B7533] text-white"
          >
            {isLoading ? 'Menyimpan...' : 'Simpan'}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
