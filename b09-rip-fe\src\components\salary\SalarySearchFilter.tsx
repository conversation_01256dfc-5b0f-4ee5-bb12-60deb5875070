'use client';

import { SearchFilter, Filter } from '@/components/ui/search-filter';

interface SalarySearchFilterProps {
  search: string;
  period?: string;
  periods?: Array<{ value: string; label: string }>;
  onSearchChange: (value: string) => void;
  onPeriodChange: (value: string | undefined) => void;
}

export function SalarySearchFilter({
  search,
  period,
  periods = [],
  onSearchChange,
  onPeriodChange,
}: SalarySearchFilterProps) {
  // Use periods directly without modification
  const filters: Filter[] = [
    {
      label: 'Periode',
      value: period,
      onChange: onPeriodChange,
      options: periods,
    },
  ];

  return (
    <SearchFilter
      search={search}
      onSearchChange={onSearchChange}
      filters={filters}
      searchPlaceholder="Cari nama karyawan atau ID gaji"
    />
  );
}
