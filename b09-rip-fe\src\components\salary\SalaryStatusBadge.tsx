import { Badge } from '@/components/ui/badge';
import { CreditCard, CheckCircle } from 'lucide-react';

export type PaymentStatus = 'paid' | 'unpaid';

interface SalaryStatusBadgeProps {
  status: PaymentStatus;
  className?: string;
}

export function SalaryStatusBadge({
  status,
  className,
}: SalaryStatusBadgeProps) {
  const getVariant = (status: PaymentStatus) => {
    switch (status) {
      case 'paid':
        return 'success';
      case 'unpaid':
        return 'warning';
      default:
        return 'secondary';
    }
  };

  const formatStatus = (status: string): string => {
    const statusDisplayMap: Record<string, string> = {
      paid: 'Sudah Dibayarkan',
      unpaid: 'Belum Dibayarkan',
    };
    return statusDisplayMap[status] || status;
  };

  if (status === 'paid') {
    return (
      <Badge variant={getVariant(status)} className={className}>
        <CheckCircle className="h-3.5 w-3.5 mr-1" />
        {formatStatus(status)}
      </Badge>
    );
  }

  return (
    <Badge variant={getVariant(status)} className={className}>
      <CreditCard className="h-3.5 w-3.5 mr-1" />
      {formatStatus(status)}
    </Badge>
  );
}

export default SalaryStatusBadge;
