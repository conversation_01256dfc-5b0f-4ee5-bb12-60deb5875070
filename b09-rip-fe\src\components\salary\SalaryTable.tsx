import React from 'react';
import { Button } from '@/components/ui/button';
import { DataTable } from '@/components/ui/data-table';
import { Eye } from 'lucide-react';
import { SalaryStatusBadge } from './SalaryStatusBadge';

interface SalaryTableProps {
  salaries: {
    id: number;
    salaryId: string;
    employeeId: string;
    name: string;
    role: string;
    period: string;
    totalSalary: string;
    paymentStatus: string;
  }[];
  isLoading?: boolean;
  onViewDetail: (salaryId: string) => void;
  onViewEmployeeDetail: (employeeId: string) => void;
}

const SalaryTable: React.FC<SalaryTableProps> = ({
  salaries,
  isLoading = false,
  onViewDetail,
  onViewEmployeeDetail,
}) => {
  // Create responsive columns
  const columns = [
    {
      key: 'id',
      header: 'NO',
      width: '60px',
    },
    {
      key: 'name',
      header: 'NAMA',
      render: (salary: SalaryTableProps['salaries'][0]) => (
        <span
          onClick={() => onViewEmployeeDetail(salary.employeeId)}
          className="text-[#9B7533] hover:text-[#AB8B3B] hover:underline cursor-pointer font-medium"
        >
          {salary.name}
        </span>
      ),
    },
    {
      key: 'role',
      header: 'JABATAN',
      hideOnMobile: true,
    },
    {
      key: 'period',
      header: 'PERIODE',
    },
    {
      key: 'totalSalary',
      header: 'TOTAL GAJI',
    },
    {
      key: 'paymentStatus',
      header: 'STATUS',
      render: (salary: SalaryTableProps['salaries'][0]) => (
        <SalaryStatusBadge status={salary.paymentStatus as 'paid' | 'unpaid'} />
      ),
    },
    {
      key: 'actions',
      header: 'AKSI',
      width: '120px',
      render: (salary: SalaryTableProps['salaries'][0]) => (
        <Button
          variant="outline"
          size="sm"
          onClick={() => onViewDetail(salary.salaryId)}
          leftIcon={<Eye className="h-4 w-4" />}
        >
          Detail
        </Button>
      ),
    },
  ];

  return (
    <DataTable
      columns={columns}
      data={salaries}
      keyExtractor={(item) => item.id.toString()}
      loading={isLoading}
      emptyStateMessage="Tidak ada data gaji yang ditemukan."
    />
  );
};

export default SalaryTable;
