'use client';

import { <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>ary<PERSON>hang<PERSON> } from '@/types/salary';
import { useSalaryHistory } from '@/hooks/useSalaryHistory';
import { formatDate } from '@/lib/formatters';
import { Button } from '@/components/ui/button';
import {
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON>ipContent,
  TooltipProvider,
  TooltipTrigger,
} from '@/components/ui/tooltip';

interface SalaryTimelineProps {
  salary: SalaryRecord;
}

interface TimelineItem {
  id: string;
  title: string;
  timestamp: string;
  createdBy: string;
  createdByName?: string;
  changes: SalaryChange[];
}

export function SalaryTimeline({ salary }: SalaryTimelineProps) {
  const { history, loading, error } = useSalaryHistory(salary.id);

  // Function to get change description
  function getChangeDescription(change: SalaryChange): string {
    // Simply return the displayText
    return change.displayText;
  }

  // Function to get the appropriate color for a change
  function getChangeColor(change: SalaryChange): string {
    // For component changes
    if (change.changeType === 'component') {
      if (change.componentType === 'deduction') {
        return 'text-red-600';
      }

      if (change.componentType === 'bonus') {
        return 'text-green-600';
      }

      if (change.componentType === 'allowance') {
        return 'text-blue-600';
      }
    }
    // For field changes
    else if (change.changeType === 'field') {
      if (change.subType === 'base_salary') {
        return 'text-purple-600';
      }
    }

    // Default color
    return 'text-gray-600';
  }

  // Create the initial timeline item for salary creation
  const creationItem: TimelineItem = {
    id: 'creation',
    title: 'Gaji Dibuat',
    timestamp: salary.created_at,
    createdBy: salary.created_by,
    changes: [],
  };

  // Combine with history items if available
  const timelineItems: TimelineItem[] = [
    creationItem,
    ...(history || []).map((item) => ({
      id: item.id,
      title: 'Gaji Diperbarui',
      timestamp: item.created_at,
      createdBy: item.created_by,
      createdByName: item.created_by_name,
      changes: item.formatted_changes, // Use formatted_changes directly
    })),
  ].sort(
    (a, b) => new Date(a.timestamp).getTime() - new Date(b.timestamp).getTime()
  );

  return (
    <div className="mt-8 pt-6 border-t">
      <h3 className="text-lg font-semibold text-gray-500 mb-4">
        Riwayat Perubahan
      </h3>

      {error && (
        <div className="mb-4 p-3 bg-red-50 text-red-700 rounded-md">
          <p>Gagal memuat riwayat perubahan: {error}</p>
          <Button
            variant="outline"
            size="sm"
            className="mt-2"
            onClick={() => window.location.reload()}
          >
            Coba Lagi
          </Button>
        </div>
      )}

      <div className="relative">
        {/* Timeline line */}
        <div className="absolute left-4 top-0 bottom-0 w-0.5 bg-gray-200"></div>

        {/* Timeline items */}
        <div className="space-y-6">
          {timelineItems.map((item, index) => (
            <div key={item.id} className="relative pl-10">
              {/* Timeline dot */}
              <div className="absolute left-0 top-1.5 w-8 h-8 rounded-full bg-[#AB8B3B] flex items-center justify-center text-white">
                {index + 1}
              </div>

              {/* Content */}
              <div className="bg-white p-4 rounded-lg border shadow-sm">
                <div className="flex justify-between items-start mb-2">
                  <h4 className="font-semibold text-[#AB8B3B]">{item.title}</h4>
                  <div className="text-right">
                    <span className="text-sm text-gray-500 block">
                      {formatDate(item.timestamp)}
                    </span>
                    {item.createdByName && (
                      <span className="text-xs text-gray-500 block">
                        oleh {item.createdByName}
                      </span>
                    )}
                  </div>
                </div>

                {item.id === 'creation' ? (
                  <p className="text-gray-600">
                    Gaji periode {salary.period} dibuat
                  </p>
                ) : (
                  <div className="space-y-2">
                    {item.changes.map((change, i) => (
                      <TooltipProvider key={i}>
                        <Tooltip>
                          <TooltipTrigger asChild>
                            <div
                              className={`text-sm ${getChangeColor(change)} cursor-help`}
                            >
                              {getChangeDescription(change)}
                            </div>
                          </TooltipTrigger>
                          <TooltipContent>
                            <p>Detail perubahan:</p>
                            <pre className="text-xs mt-1 max-w-xs overflow-auto">
                              {JSON.stringify(change, null, 2)}
                            </pre>
                          </TooltipContent>
                        </Tooltip>
                      </TooltipProvider>
                    ))}
                  </div>
                )}
              </div>
            </div>
          ))}

          {/* Loading state */}
          {loading && (
            <div className="relative pl-10">
              <div className="absolute left-0 top-1.5 w-8 h-8 rounded-full bg-gray-200 animate-pulse"></div>
              <div className="bg-white p-4 rounded-lg border shadow-sm">
                <div className="h-5 bg-gray-200 rounded animate-pulse mb-2 w-1/3"></div>
                <div className="h-4 bg-gray-200 rounded animate-pulse w-1/2"></div>
              </div>
            </div>
          )}
        </div>
      </div>

      {/* Empty state (only creation, no history) */}
      {!loading && history && history.length === 0 && (
        <div className="text-center text-gray-500 mt-4">
          Belum ada perubahan pada data gaji ini.
        </div>
      )}
    </div>
  );
}
