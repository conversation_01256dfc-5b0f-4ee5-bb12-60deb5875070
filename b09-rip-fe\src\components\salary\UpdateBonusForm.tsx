'use client';

import React, { useState, useEffect } from 'react';
import { Loader2, CheckCircle2, AlertCircle } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Label } from '@/components/ui/label';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogDescription,
  DialogFooter,
} from '@/components/ui/dialog';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { BonusSalaryType } from '@/types/salary';
import { formatCurrency } from '@/lib/utils/format';
import { Bonus } from '@/types/salary';
import { bonusApi } from '@/lib/api/bonus';
import { toast } from 'sonner';
import { ProjectCombobox } from '@/components/project/ProjectCombobox';
import { KPICombobox } from '@/components/kpi/KPICombobox';

interface UpdateBonusFormProps {
  bonus: Bonus | null;
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onSuccess?: () => void;
}

const UpdateBonusForm: React.FC<UpdateBonusFormProps> = ({
  bonus,
  open,
  onOpenChange,
  onSuccess,
}) => {
  const [amount, setAmount] = useState<number>(0);
  const [bonusType, setBonusType] = useState<BonusSalaryType | ''>('');
  const [notes, setNotes] = useState<string>('');
  const [kpiId, setKpiId] = useState<string>('');
  const [kpiName, setKpiName] = useState<string>('');
  const [projectId, setProjectId] = useState<string>('');
  const [projectName, setProjectName] = useState<string>('');
  const [loading, setLoading] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);
  const [step, setStep] = useState<'form' | 'confirmation' | 'result'>('form');
  const [result, setResult] = useState<{
    success: boolean;
    message: string;
  } | null>(null);

  // Initialize form with bonus data when it changes
  useEffect(() => {
    if (bonus) {
      setAmount(bonus.amount);
      setBonusType(bonus.bonus_type);
      setNotes(bonus.notes || '');
      setKpiId(bonus.kpi_id || '');
      setProjectId(bonus.project_id || '');

      // We'll need to fetch KPI and Project names if IDs are present
      // For now, we'll just set empty strings and let the comboboxes handle it
      setKpiName('');
      setProjectName('');
    }
  }, [bonus]);

  // Handle amount change with validation
  const handleAmountChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    const numericValue = parseInt(value, 10);

    if (value === '') {
      setAmount(0);
      setError(null);
    } else if (isNaN(numericValue)) {
      setError('Jumlah harus berupa angka');
    } else if (numericValue < 0) {
      setError('Jumlah tidak boleh negatif');
    } else {
      setAmount(numericValue);
      setError(null);
    }
  };

  // Add cleanup effect to reset state when dialog closes
  useEffect(() => {
    if (!open) {
      // Reset to initial state when dialog closes
      setStep('form');
      setResult(null);
      setError(null);
      setLoading(false);
    }
  }, [open]);

  // Handle form submission - move to confirmation step
  const handleSubmit = () => {
    if (!bonus || error) return;
    setStep('confirmation');
  };

  // Handle back button in confirmation step
  const handleBack = () => {
    setStep('form');
  };

  // Handle dialog close with proper cleanup
  const handleDialogClose = (isOpen: boolean) => {
    // Only allow closing if not loading
    if (!isOpen && !loading) {
      // If we're in the result step and it was successful, trigger the refresh after dialog closes
      if (step === 'result' && result?.success) {
        // Reset step first
        setStep('form');
        // Close dialog
        onOpenChange(false);
        // Add a delay before refreshing to ensure dialog is fully closed
        setTimeout(() => {
          if (onSuccess) onSuccess();
        }, 300);
      } else {
        // For other steps, just close normally
        onOpenChange(false);
      }
    }
    return !loading; // Prevent closing if loading
  };

  // Handle confirmation submit
  const handleConfirmSubmit = async () => {
    if (!bonus) return;

    try {
      setLoading(true);

      const data = {
        amount,
        bonus_type: bonusType as BonusSalaryType,
        notes: notes || undefined,
        kpi_id: kpiId || undefined,
        project_id: projectId || undefined,
      };

      const response = await bonusApi.updateBonus(bonus.id, data);

      // Move to result step instead of closing dialog
      if (response.success) {
        setResult({
          success: true,
          message: 'Bonus berhasil diperbarui',
        });
        toast.success('Bonus berhasil diperbarui');
      } else {
        setResult({
          success: false,
          message: response.message || 'Gagal memperbarui bonus',
        });
        toast.error(`Gagal memperbarui bonus: ${response.message}`);
      }

      // Move to result step
      setStep('result');
    } catch (err: unknown) {
      console.error('Error updating bonus:', err);
      const errorObj = err as { message?: string };
      const errorMessage =
        errorObj.message || 'Terjadi kesalahan saat memperbarui bonus';
      setResult({
        success: false,
        message: errorMessage,
      });
      toast.error(errorMessage);
      setStep('result');
    } finally {
      setLoading(false);
    }
  };

  // Render form content
  const renderFormContent = () => (
    <>
      <DialogHeader>
        <DialogTitle>Perbarui Bonus</DialogTitle>
        <DialogDescription>Perbarui informasi bonus ini</DialogDescription>
      </DialogHeader>

      <div className="space-y-4 py-4">
        <div className="space-y-2">
          <Label htmlFor="amount">Jumlah Bonus (Rp)</Label>
          <Input
            id="amount"
            type="number"
            min="0"
            value={amount}
            onChange={handleAmountChange}
          />
          {error && <p className="text-red-500 text-sm">{error}</p>}
          {amount > 0 && (
            <p className="text-sm text-gray-500 mt-1">
              Format: {formatCurrency(amount)}
            </p>
          )}
        </div>

        <div className="space-y-2">
          <Label htmlFor="bonus-type">Tipe Bonus</Label>
          <Select
            value={bonusType}
            onValueChange={(value) => setBonusType(value as BonusSalaryType)}
          >
            <SelectTrigger id="bonus-type">
              <SelectValue placeholder="Pilih tipe bonus" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value={BonusSalaryType.KPI}>KPI</SelectItem>
              <SelectItem value={BonusSalaryType.PROJECT}>Project</SelectItem>
              <SelectItem value={BonusSalaryType.OTHER}>Lainnya</SelectItem>
            </SelectContent>
          </Select>
        </div>

        <div className="space-y-2">
          <Label htmlFor="notes">Catatan</Label>
          <Textarea
            id="notes"
            value={notes}
            onChange={(e) => setNotes(e.target.value)}
            placeholder="Catatan tambahan (opsional)"
          />
        </div>

        {bonusType === BonusSalaryType.KPI && (
          <div className="space-y-2">
            <Label htmlFor="kpi-id">KPI</Label>
            <KPICombobox
              value={kpiName}
              onSelect={(id, name) => {
                setKpiId(id);
                setKpiName(name);
              }}
              placeholder="Pilih KPI..."
            />
          </div>
        )}

        {bonusType === BonusSalaryType.PROJECT && (
          <div className="space-y-2">
            <Label htmlFor="project-id">Project</Label>
            <ProjectCombobox
              value={projectName}
              onSelect={(id, name) => {
                setProjectId(id);
                setProjectName(name);
              }}
              placeholder="Pilih project..."
            />
          </div>
        )}
      </div>

      <DialogFooter>
        <Button
          variant="outline"
          onClick={() => onOpenChange(false)}
          disabled={loading}
        >
          Batal
        </Button>
        <Button
          onClick={handleSubmit}
          disabled={
            loading ||
            error !== null ||
            !bonusType ||
            amount <= 0 ||
            (bonusType === BonusSalaryType.KPI && !kpiId) ||
            (bonusType === BonusSalaryType.PROJECT && !projectId)
          }
        >
          {loading ? 'Memperbarui...' : 'Lanjutkan'}
        </Button>
      </DialogFooter>
    </>
  );

  // Render confirmation content
  const renderConfirmationContent = () => (
    <>
      <DialogHeader>
        <DialogTitle>Konfirmasi Perubahan</DialogTitle>
        <DialogDescription>
          Apakah Anda yakin ingin menyimpan perubahan bonus ini?
        </DialogDescription>
      </DialogHeader>

      <div className="py-4">
        <div className="rounded-lg border p-4">
          <h4 className="font-medium mb-2">Detail Bonus:</h4>
          <p>
            <span className="text-muted-foreground">Tipe:</span>{' '}
            <span className="font-medium">
              {bonusType === BonusSalaryType.KPI
                ? 'KPI'
                : bonusType === BonusSalaryType.PROJECT
                  ? 'Project'
                  : 'Lainnya'}
            </span>
          </p>
          <p>
            <span className="text-muted-foreground">Jumlah:</span>{' '}
            <span className="font-medium">{formatCurrency(amount)}</span>
          </p>
          {notes && (
            <p>
              <span className="text-muted-foreground">Catatan:</span>{' '}
              <span className="font-medium">{notes}</span>
            </p>
          )}
          {bonusType === BonusSalaryType.KPI && kpiId && (
            <p>
              <span className="text-muted-foreground">KPI:</span>{' '}
              <span className="font-medium">{kpiName}</span>
            </p>
          )}
          {bonusType === BonusSalaryType.PROJECT && projectId && (
            <p>
              <span className="text-muted-foreground">Project:</span>{' '}
              <span className="font-medium">{projectName}</span>
            </p>
          )}
        </div>
      </div>

      <DialogFooter>
        <Button variant="outline" onClick={handleBack} disabled={loading}>
          Kembali
        </Button>
        <Button
          onClick={handleConfirmSubmit}
          disabled={loading}
          className="bg-primary text-primary-foreground hover:bg-primary/90"
        >
          {loading ? (
            <>
              <Loader2 className="mr-2 h-4 w-4 animate-spin" />
              Memperbarui...
            </>
          ) : (
            'Ya, Simpan Perubahan'
          )}
        </Button>
      </DialogFooter>
    </>
  );

  // Render result content
  const renderResultContent = () => {
    if (!result) return null;

    return (
      <>
        <DialogHeader>
          <DialogTitle>{result.success ? 'Berhasil' : 'Gagal'}</DialogTitle>
        </DialogHeader>

        <div className="py-6 flex flex-col items-center justify-center text-center">
          {result.success ? (
            <CheckCircle2 className="h-16 w-16 text-green-500 mb-4" />
          ) : (
            <AlertCircle className="h-16 w-16 text-red-500 mb-4" />
          )}

          <h3 className="text-lg font-semibold mb-2">
            {result.success
              ? 'Bonus berhasil diperbarui'
              : 'Gagal memperbarui bonus'}
          </h3>

          <p className="text-muted-foreground mb-6">
            {result.success
              ? 'Perubahan bonus telah berhasil disimpan.'
              : result.message || 'Terjadi kesalahan saat memperbarui bonus.'}
          </p>

          <div className="flex gap-2">
            {!result.success && (
              <Button variant="outline" onClick={() => setStep('form')}>
                Coba Lagi
              </Button>
            )}
            <Button onClick={() => handleDialogClose(false)}>
              {result.success ? 'Selesai' : 'Tutup'}
            </Button>
          </div>
        </div>
      </>
    );
  };

  return (
    <Dialog open={open} onOpenChange={handleDialogClose}>
      <DialogContent>
        {step === 'form' && renderFormContent()}
        {step === 'confirmation' && renderConfirmationContent()}
        {step === 'result' && renderResultContent()}
      </DialogContent>
    </Dialog>
  );
};

export default UpdateBonusForm;
