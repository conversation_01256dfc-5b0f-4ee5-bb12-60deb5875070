'use client';

import { useState, useEffect, ChangeEvent, FormEvent, JSX } from 'react';
import { useRouter } from 'next/navigation';
import { toast } from 'sonner';
import { Button } from '@/components/ui/button';
import salaryApi from '@/lib/api/salary';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Badge } from '@/components/ui/badge';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { SalaryRecord } from '@/types/salary';
import ErrorPage from '@/components/error/error-page';
import { PageTitle } from '@/components/ui/PageTitle';
import { BackButton } from '@/components/ui/BackButton';

interface EditSalaryProps {
  id: string;
  userRole: string;
}

interface FormDataType {
  base_salary: number;
  payment_status: 'unpaid' | 'paid';
}

export default function UpdateSalaryForm({
  id,
  userRole,
}: EditSalaryProps): JSX.Element {
  const router = useRouter();
  const [salaryData, setSalaryData] = useState<SalaryRecord | null>(null);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  const [saving, setSaving] = useState<boolean>(false);
  const [formData, setFormData] = useState<FormDataType>({
    base_salary: 0,
    payment_status: 'unpaid',
  });

  // Format the period from YYYY-MM to Month YYYY
  const formatPeriod = (period: string): string => {
    const [year, month] = period.split('-');
    const monthNames = [
      'Januari',
      'Februari',
      'Maret',
      'April',
      'Mei',
      'Juni',
      'Juli',
      'Agustus',
      'September',
      'Oktober',
      'November',
      'Desember',
    ];
    return `${monthNames[parseInt(month) - 1]} ${year}`;
  };

  // Format currency to Indonesian Rupiah
  const formatCurrency = (amount: number): string => {
    return `Rp${amount.toLocaleString('id-ID')}`;
  };

  useEffect(() => {
    const fetchSalaryData = async (): Promise<void> => {
      if (!id) {
        setError('Invalid salary ID');
        setLoading(false);
        return;
      }

      try {
        setLoading(true);
        const response = await salaryApi.getSalaryById(id);

        // Check if response is a direct 404 error response
        if (
          !response.success &&
          response.error &&
          response.error.code === 'NOT_FOUND'
        ) {
          setError('not_found');
          return;
        }

        // Check if response has a "not found" message
        if (
          !response.success &&
          response.message &&
          response.message.toLowerCase().includes('not found')
        ) {
          setError('not_found');
          return;
        }

        if (response.success && response.data) {
          setSalaryData(response.data);

          // Fix: Ensure numeric values are properly set by using Number() or parsing
          setFormData({
            base_salary: Number(response.data.base_salary) || 0,
            payment_status: response.data.payment_status as 'unpaid' | 'paid',
          });
        } else {
          throw new Error(response.message || 'Failed to fetch salary data');
        }
      } catch (err) {
        console.error('Error fetching salary data:', err);

        // Check if this is a "not found" error
        let errorMsg = '';

        if (err instanceof Error) {
          errorMsg = err.message;
        } else if (typeof err === 'object' && err !== null) {
          // Try to extract the message from various possible error structures
          if ('message' in err && typeof err.message === 'string') {
            errorMsg = err.message;
          } else if (
            'response' in err &&
            typeof err.response === 'object' &&
            err.response !== null
          ) {
            if (
              'data' in err.response &&
              typeof err.response.data === 'object' &&
              err.response.data !== null
            ) {
              if (
                'message' in err.response.data &&
                typeof err.response.data.message === 'string'
              ) {
                errorMsg = err.response.data.message;
              }
            } else if (
              'statusText' in err.response &&
              typeof err.response.statusText === 'string'
            ) {
              errorMsg = err.response.statusText;
            }
          }
        } else {
          errorMsg = String(err);
        }

        const isNotFoundError =
          errorMsg.toLowerCase().includes('not found') ||
          (typeof err === 'object' &&
            err !== null &&
            'response' in err &&
            typeof err.response === 'object' &&
            err.response !== null &&
            'status' in err.response &&
            err.response.status === 404);

        // Set the appropriate error message
        setError(
          isNotFoundError
            ? 'not_found'
            : errorMsg || 'An unknown error occurred'
        );
      } finally {
        setLoading(false);
      }
    };

    fetchSalaryData();
  }, [id]);

  const handleInputChange = (e: ChangeEvent<HTMLInputElement>): void => {
    const { name, value } = e.target;

    if (name === 'base_salary') {
      // Convert to number or use 0 if it can't be converted
      const numValue = value === '' ? 0 : Number(value);
      setFormData({
        ...formData,
        [name]: isNaN(numValue) ? 0 : numValue, // Ensure we never set NaN
      });
    } else {
      setFormData({
        ...formData,
        [name]: value,
      });
    }
  };

  const handleSelectChange = (value: string): void => {
    setFormData({
      ...formData,
      payment_status: value as 'unpaid' | 'paid',
    });
  };

  const handleBack = (): void => {
    router.push(`/employee/salary/${id}`);
  };

  // No need to calculate total salary as we're only updating base_salary now

  // Helper function to check if user can edit payment status
  const canEditPaymentStatus = (): boolean => {
    return userRole === 'Finance' || userRole === 'Admin';
  };

  const handleSubmit = async (e: FormEvent): Promise<void> => {
    e.preventDefault();

    if (!salaryData) return;

    try {
      setSaving(true);

      // Use the new updateSalary API with base_salary and payment_status
      // The backend will handle permissions based on the user's role

      // Pass base_salary and payment_status if the user can edit it
      const updateData: {
        base_salary: number;
        payment_status?: 'unpaid' | 'paid';
      } = {
        base_salary: formData.base_salary,
      };

      if (canEditPaymentStatus()) {
        updateData.payment_status = formData.payment_status;
      }

      const response = await salaryApi.updateSalary(id, updateData);

      // The form now collects base_salary and payment_status, which matches
      // the new API design.

      if (response.success) {
        toast.success('Data penggajian berhasil diperbarui');
        router.push(`/employee/salary/${id}`);
      } else {
        toast.error(response.message || 'Gagal memperbarui data penggajian');
        throw new Error(response.message || 'Failed to update salary');
      }
    } catch (err) {
      console.error('Error updating salary:', err);

      // Show toast error notification
      const errorMessage =
        err instanceof Error
          ? err.message
          : 'Terjadi kesalahan saat memperbarui data penggajian';
      toast.error(errorMessage);

      setError(
        err instanceof Error ? err.message : 'An unknown error occurred'
      );
    } finally {
      setSaving(false);
    }
  };

  if (loading) {
    return (
      <div className="flex h-screen w-full items-center justify-center">
        <div className="text-center">
          <div className="h-8 w-8 animate-spin rounded-full border-2 border-solid border-[#B78F38] border-t-transparent"></div>
          <p className="mt-4 text-gray-600">Memuat...</p>
        </div>
      </div>
    );
  }

  if (error) {
    // Handle "not found" error specially with 404 page
    if (error === 'not_found') {
      return (
        <ErrorPage
          statusCode="404"
          title="Data Penggajian Tidak Ditemukan"
          message="Maaf, data penggajian yang Anda cari tidak dapat ditemukan."
          showHomeButton={true}
          showBackButton={true}
          showRetryButton={false}
          homeHref="/dashboard"
          homeLabel="Kembali ke Dashboard"
          backLabel="Kembali"
        />
      );
    }

    // Handle other errors with 500 page
    return (
      <ErrorPage
        statusCode="500"
        title="Kesalahan Memuat Data"
        message={`Terjadi kesalahan saat memuat data penggajian: ${error}`}
        showHomeButton={true}
        showBackButton={true}
        showRetryButton={true}
        homeHref="/dashboard"
        homeLabel="Kembali ke Dashboard"
        backLabel="Kembali"
        retryLabel="Coba Lagi"
      />
    );
  }

  if (!salaryData) {
    return (
      <ErrorPage
        statusCode="404"
        title="Data Penggajian Tidak Ditemukan"
        message="Tidak ada data yang ditemukan untuk ID penggajian ini."
        showHomeButton={true}
        showBackButton={true}
        homeHref="/dashboard"
        homeLabel="Kembali ke Dashboard"
        backLabel="Kembali"
      />
    );
  }

  const paymentStatus =
    formData.payment_status === 'paid'
      ? 'Sudah Dibayarkan'
      : 'Belum Dibayarkan';

  return (
    <div className="p-8 max-w-5xl mx-auto">
      <div className="flex items-center gap-4 mb-6">
        <BackButton onClick={handleBack} />
        <PageTitle title="Edit Penggajian" />
      </div>

      <form onSubmit={handleSubmit}>
        <Card className="bg-white border shadow-sm">
          <CardHeader className="pb-4 border-b">
            <div className="flex justify-between items-center">
              <div>
                <CardTitle className="text-2xl font-bold">
                  {salaryData.employee_details.fullname}
                </CardTitle>
                <p className="text-gray-500 mt-1">ID Gaji: {salaryData.id}</p>
              </div>
              <Badge className="px-3 py-1 rounded-md bg-yellow-100 text-yellow-800">
                {formatPeriod(salaryData.period)}
              </Badge>
            </div>
          </CardHeader>

          <CardContent className="pt-6">
            <div className="grid grid-cols-2 gap-6">
              {/* Left column - Employee Information */}
              <div>
                <h3 className="text-lg font-semibold text-gray-500 mb-4">
                  Informasi Karyawan
                </h3>
                <div className="space-y-4">
                  <div>
                    <p className="text-sm font-medium text-gray-500">
                      ID Karyawan
                    </p>
                    <p className="font-semibold">{salaryData.employee_id}</p>
                  </div>

                  <div>
                    <p className="text-sm font-medium text-gray-500">Role</p>
                    <p className="font-semibold">
                      {salaryData.employee_details.role}
                    </p>
                  </div>

                  <div>
                    <p className="text-sm font-medium text-gray-500">
                      Departemen
                    </p>
                    <p className="font-semibold">
                      {salaryData.employee_details.department}
                    </p>
                  </div>

                  <div>
                    <p className="text-sm font-medium text-gray-500">Bank</p>
                    <p className="font-semibold">
                      {salaryData.employee_details.bank_name}
                    </p>
                  </div>

                  <div>
                    <p className="text-sm font-medium text-gray-500">
                      Nomor Rekening
                    </p>
                    <p className="font-semibold">
                      {salaryData.employee_details.bank_account}
                    </p>
                  </div>
                </div>
              </div>

              {/* Right column - Salary Details */}
              <div>
                <h3 className="text-lg font-semibold text-gray-500 mb-4">
                  Rincian Gaji
                </h3>
                <div className="space-y-4">
                  <div>
                    <Label
                      htmlFor="base_salary"
                      className="text-sm font-medium text-gray-500"
                    >
                      Gaji Pokok
                    </Label>
                    <div className="relative mt-1">
                      <Input
                        id="base_salary"
                        name="base_salary"
                        type="number"
                        value={formData.base_salary}
                        onChange={handleInputChange}
                        className="border-gray-300 pl-8"
                      />
                      <div className="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none">
                        <span className="text-gray-500">Rp</span>
                      </div>
                    </div>
                    <p className="text-xs text-gray-500 mt-1">
                      Sebelumnya: {formatCurrency(salaryData.base_salary)}
                    </p>
                  </div>

                  <div>
                    <p className="text-sm font-medium text-gray-500">
                      Status Pembayaran
                    </p>
                    {canEditPaymentStatus() ? (
                      <div className="mt-1">
                        <Select
                          value={formData.payment_status}
                          onValueChange={handleSelectChange}
                        >
                          <SelectTrigger className="w-full border-gray-300">
                            <SelectValue placeholder="Status Pembayaran" />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="unpaid">
                              Belum Dibayarkan
                            </SelectItem>
                            <SelectItem value="paid">
                              Sudah Dibayarkan
                            </SelectItem>
                          </SelectContent>
                        </Select>
                      </div>
                    ) : (
                      <p
                        className={`font-semibold ${
                          formData.payment_status === 'paid'
                            ? 'text-green-600'
                            : 'text-amber-600'
                        }`}
                      >
                        {paymentStatus}
                      </p>
                    )}
                  </div>
                </div>
              </div>
            </div>

            <div className="mt-8 pt-6 border-t flex justify-end gap-3">
              <Button type="button" variant="outline" onClick={handleBack}>
                Batal
              </Button>
              <Button
                type="submit"
                className="bg-[#AB8B3B] hover:bg-[#9B7533] text-white"
                disabled={saving}
              >
                {saving ? (
                  <>
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                    Menyimpan...
                  </>
                ) : (
                  <>Simpan</>
                )}
              </Button>
            </div>
          </CardContent>
        </Card>
      </form>
    </div>
  );
}
