// src/components/ui/data-table.tsx
'use client';

import React, { ReactNode, useState, Fragment } from 'react';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { Button } from '@/components/ui/button';
import { ChevronDown, ChevronUp, ChevronRight } from 'lucide-react';
import { Skeleton } from '@/components/ui/skeleton';

export type SortDirection = 'asc' | 'desc' | null;

interface DataTableProps<T> {
  columns: {
    key: string;
    header: string | React.ReactNode;
    width?: string;
    render?: (item: T) => ReactNode;
    sortable?: boolean;
    hideOnMobile?: boolean;
  }[];
  data: T[];
  keyExtractor: (item: T) => string | number;
  filterComponent?: ReactNode;
  searchComponent?: ReactNode;
  // Pagination props removed as they're not used
  // currentPage?: number;
  // itemsPerPage?: number;
  // onPageChange?: (page: number) => void;
  loading?: boolean;
  sortField?: string;
  sortDirection?: SortDirection;
  onSort?: (field: string, direction: SortDirection) => void;
  expandableContent?: (item: T) => ReactNode;
  emptyStateMessage?: string;
  expandedRows?: Record<string | number, boolean>;
  onExpandRow?: (id: string | number, expanded: boolean) => void;
  // showPagination?: boolean; // Removed as it's not used
}

export function DataTable<T>({
  columns,
  data,
  keyExtractor,
  filterComponent,
  searchComponent,
  loading = false,
  sortField,
  sortDirection,
  onSort,
  expandableContent,
  emptyStateMessage = 'Tidak ada data untuk ditampilkan',
  expandedRows: propExpandedRows,
  onExpandRow,
}: DataTableProps<T>) {
  // Default number of items to show in loading state
  const itemsPerPage = 10;
  // Internal expanded rows state if not provided as prop
  const [internalExpandedRows, setInternalExpandedRows] = useState<
    Record<string | number, boolean>
  >({});

  // Use provided expanded rows state or internal state
  const expandedRows = propExpandedRows || internalExpandedRows;

  // Function to toggle row expansion
  const toggleRowExpand = (id: string | number) => {
    const newExpandedState = !expandedRows[id];

    if (onExpandRow) {
      onExpandRow(id, newExpandedState);
    } else {
      setInternalExpandedRows((prev) => ({
        ...prev,
        [id]: newExpandedState,
      }));
    }
  };

  // Handle sort click
  const handleSortClick = (key: string) => {
    if (!onSort) return;

    const newDirection =
      sortField === key && sortDirection === 'asc' ? 'desc' : 'asc';

    onSort(key, newDirection);
  };

  // Function to render sort indicator
  const renderSortIndicator = (key: string) => {
    if (sortField !== key) return null;

    return sortDirection === 'asc' ? (
      <ChevronUp className="ml-1 h-4 w-4 inline" />
    ) : (
      <ChevronDown className="ml-1 h-4 w-4 inline" />
    );
  };

  // No pagination calculations - display all data
  const actualTotalItems = data.length;
  // Use all data instead of paginated data
  const currentPageData = data;

  // Loading state rows
  const renderLoadingRows = () =>
    Array.from({ length: itemsPerPage }).map((_, index) => (
      <TableRow key={`loading-${index}`}>
        {expandableContent && (
          <TableCell className="w-[40px]">
            <Skeleton className="h-8 w-8" />
          </TableCell>
        )}
        {columns.map((_, cellIndex) => (
          <TableCell key={`loading-cell-${cellIndex}`}>
            <Skeleton className="h-8 w-full" />
          </TableCell>
        ))}
      </TableRow>
    ));

  // Empty state row
  const renderEmptyState = () => (
    <TableRow>
      <TableCell
        colSpan={expandableContent ? columns.length + 1 : columns.length}
        className="text-center py-8 text-gray-500"
      >
        {emptyStateMessage}
      </TableCell>
    </TableRow>
  );

  return (
    <div>
      {/* Header with filter and search */}
      {(filterComponent || searchComponent) && (
        <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center mb-6 gap-4">
          {filterComponent}
          {searchComponent}
        </div>
      )}

      {/* Table data */}
      <div className="rounded-lg border border-gray-200 shadow-sm overflow-hidden">
        <Table>
          <TableHeader className="bg-gray-50">
            <TableRow>
              {/* Expand column if expandable content is provided */}
              {expandableContent && (
                <TableHead className="w-[40px]"></TableHead>
              )}

              {columns.map((column) => (
                <TableHead
                  key={column.key}
                  className={`text-[#AB8B3B] font-semibold text-left ${column.hideOnMobile ? 'hidden md:table-cell' : ''}`}
                  style={{ width: column.width }}
                  onClick={
                    column.sortable && onSort
                      ? () => handleSortClick(column.key)
                      : undefined
                  }
                >
                  <div
                    className={`flex items-center ${column.sortable && onSort ? 'cursor-pointer' : ''}`}
                  >
                    {column.header}
                    {column.sortable && renderSortIndicator(column.key)}
                  </div>
                </TableHead>
              ))}
            </TableRow>
          </TableHeader>
          <TableBody>
            {loading
              ? renderLoadingRows()
              : currentPageData.length > 0
                ? currentPageData.map((item) => {
                    const rowId = keyExtractor(item);
                    const isExpanded = expandedRows[rowId];

                    return (
                      <Fragment key={rowId}>
                        <TableRow className="hover:bg-gray-50 transition-colors">
                          {/* Expandable icon column */}
                          {expandableContent && (
                            <TableCell className="w-[40px]">
                              <Button
                                variant="ghost"
                                size="icon"
                                className="h-8 w-8 p-0"
                                onClick={() => toggleRowExpand(rowId)}
                              >
                                {isExpanded ? (
                                  <ChevronDown className="h-4 w-4" />
                                ) : (
                                  <ChevronRight className="h-4 w-4" />
                                )}
                              </Button>
                            </TableCell>
                          )}

                          {/* Data columns */}
                          {columns.map((column) => (
                            <TableCell
                              key={`${rowId}-${column.key}`}
                              className={`text-left ${column.hideOnMobile ? 'hidden md:table-cell' : ''}`}
                            >
                              {column.render
                                ? column.render(item)
                                : String(
                                    (item as Record<string, unknown>)[
                                      column.key
                                    ] ?? ''
                                  )}
                            </TableCell>
                          ))}
                        </TableRow>

                        {/* Expandable content row */}
                        {expandableContent && isExpanded && (
                          <TableRow className="bg-gray-50">
                            <TableCell
                              colSpan={columns.length + 1}
                              className="p-4"
                            >
                              {expandableContent(item)}
                            </TableCell>
                          </TableRow>
                        )}
                      </Fragment>
                    );
                  })
                : renderEmptyState()}
          </TableBody>
        </Table>
      </div>

      {/* Pagination has been removed */}
      {actualTotalItems > 0 && !loading && (
        <div className="flex items-center justify-end mt-4">
          <div className="text-sm text-gray-500">
            {`Menampilkan ${actualTotalItems} data`}
          </div>
        </div>
      )}
    </div>
  );
}
