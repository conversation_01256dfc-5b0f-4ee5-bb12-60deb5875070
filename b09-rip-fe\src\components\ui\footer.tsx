// src/components/ui/footer.tsx
import Link from 'next/link';
import Image from 'next/image';
import { cn } from '@/lib/utils';

export function Footer({ className }: { className?: string }) {
  return (
    <footer className={cn('bg-[#111111] text-white py-8', className)}>
      <div className="px-6">
        <div className="flex flex-col justify-between gap-8 md:flex-row md:items-start">
          {/* Left side with logo and copyright */}
          <div className="space-y-4">
            <Link href="/" className="flex items-center gap-3">
              <Image
                src="/logo-footer.svg"
                alt="Kasuat"
                width={200}
                height={160}
              />
            </Link>
            <p className="text-sm text-gray-400">
              © 2025 PT Halaman Hidup Bersama x Propensi RIP. All rights
              reserved.
            </p>
          </div>

          {/* Right side with company info */}
          <div className="space-y-2 md:text-right text-left">
            <h3 className="text-lg font-medium text-[#C3A64C]">
              PT Halaman Tumbuh Bersama
            </h3>
            <div className="space-y-1 text-sm text-gray-300">
              <p>Jl. Terusan Sukadamai II No.5</p>
              <p>Sukabungah, Kec. Sukajadi, Kota Bandung</p>
              <p className="text-white">+62 87802514374</p>
              <p>
                <Link
                  href="mailto:<EMAIL>"
                  className="text-white hover:text-[#C3A64C] transition-colors"
                >
                  <EMAIL>
                </Link>
              </p>
            </div>
          </div>
        </div>
      </div>
    </footer>
  );
}
