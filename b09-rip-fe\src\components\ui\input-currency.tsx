// src/components/ui/input-currency.tsx
import React from "react"
import { Input } from "@/components/ui/input"
import { FormField } from "@/components/ui/input-field"

interface InputCurrencyProps {
  id: string
  label: string
  value: string
  onChange: (e: React.ChangeEvent<HTMLInputElement>) => void
  className?: string
  disabled?: boolean
  required?: boolean
  error?: string
}

export function InputCurrency({
  id,
  label,
  value,
  onChange,
  className,
  disabled = false,
  required = false,
  error
}: InputCurrencyProps) {
  const formatCurrency = (value: string) => {
    // Remove non-digit characters
    const numericValue = value.replace(/\D/g, '')

    // Format with Rupiah
    if (numericValue === '') return ''

    const number = parseInt(numericValue, 10)
    return `Rp.${number.toLocaleString('id-ID')}`
  }

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const rawValue = e.target.value
    const numericValue = rawValue.replace(/\D/g, '')

    // Create a synthetic event with the numeric value
    const syntheticEvent = {
      ...e,
      target: {
        ...e.target,
        value: numericValue
      }
    }

    onChange(syntheticEvent as React.ChangeEvent<HTMLInputElement>)
  }

  return (
    <FormField label={label} htmlFor={id} className={className} error={error}>
      <Input
        id={id}
        value={formatCurrency(value)}
        onChange={handleChange}
        disabled={disabled}
        required={required}
        className="w-full"
      />
    </FormField>
  )
}
