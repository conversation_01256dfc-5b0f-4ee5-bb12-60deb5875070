// src/components/ui/input-percentage.tsx
import React from "react"
import { Input } from "@/components/ui/input"
import { FormField } from "@/components/ui/input-field"

interface InputPercentageProps {
  id: string
  label: string
  value: string
  onChange: (e: React.ChangeEvent<HTMLInputElement>) => void
  className?: string
  disabled?: boolean
  required?: boolean
  error?: string
}

export function InputPercentage({
  id,
  label,
  value,
  onChange,
  className,
  disabled = false,
  required = false,
  error
}: InputPercentageProps) {
  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const inputValue = e.target.value;
    // Only allow numbers and a single decimal point
    if (/^[0-9]*\.?[0-9]*$/.test(inputValue) || inputValue === '') {
      onChange(e);
    }
  };

  return (
    <FormField label={label} htmlFor={id} className={className} error={error}>
      <div className="relative">
        <Input
          id={id}
          value={value}
          onChange={handleChange}
          disabled={disabled}
          required={required}
          className="w-full pr-8"
          max={100}
        />
        <span className="absolute inset-y-0 right-3 flex items-center text-gray-500">
          %
        </span>
      </div>
    </FormField>
  )
}
