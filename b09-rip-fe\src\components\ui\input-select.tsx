// src/components/ui/input-select.tsx
import React from 'react';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { FormField } from '@/components/ui/input-field';

interface Option {
  value: string;
  label: string;
}

interface InputSelectProps {
  id: string;
  label: string;
  placeholder?: string;
  value: string;
  onChange: (value: string) => void;
  options: Option[];
  className?: string;
  disabled?: boolean;
  error?: string;
}

export function InputSelect({
  id,
  label,
  placeholder,
  value,
  onChange,
  options,
  className,
  disabled = false,
  error,
}: InputSelectProps) {
  return (
    <FormField label={label} htmlFor={id} className={className} error={error}>
      <Select value={value} onValueChange={onChange} disabled={disabled}>
        <SelectTrigger id={id} className="w-full">
          <SelectValue placeholder={placeholder} />
        </SelectTrigger>
        <SelectContent>
          {options.map((option) => (
            <SelectItem key={option.value} value={option.value}>
              {option.label}
            </SelectItem>
          ))}
        </SelectContent>
      </Select>
    </FormField>
  );
}
