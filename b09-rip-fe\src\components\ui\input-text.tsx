// src/components/ui/input-text.tsx
import React from "react"
import { Input } from "@/components/ui/input"
import { FormField } from "@/components/ui/input-field"

interface InputTextProps {
  id: string
  label: string
  placeholder?: string
  value: string
  onChange: (e: React.ChangeEvent<HTMLInputElement>) => void
  className?: string
  disabled?: boolean
  required?: boolean
  error?: string
}

export function InputText({
  id,
  label,
  placeholder,
  value,
  onChange,
  className,
  disabled = false,
  required = false,
  error
}: InputTextProps) {
  return (
    <FormField label={label} htmlFor={id} className={className} error={error}>
      <Input
        id={id}
        value={value}
        onChange={onChange}
        placeholder={placeholder}
        disabled={disabled}
        required={required}
        className="w-full"
      />
    </FormField>
  )
}
