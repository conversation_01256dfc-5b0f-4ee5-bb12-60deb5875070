// src/components/ui/input-textarea.tsx
import React from "react"
import { Textarea } from "@/components/ui/textarea"
import { FormField } from "@/components/ui/input-field"

interface InputTextareaProps {
  id: string
  label: string
  placeholder?: string
  value: string
  onChange: (e: React.ChangeEvent<HTMLTextAreaElement>) => void
  className?: string
  disabled?: boolean
  required?: boolean
  rows?: number
  error?: string
}

export function InputTextarea({
  id,
  label,
  placeholder,
  value,
  onChange,
  className,
  disabled = false,
  required = false,
  rows = 3,
  error
}: InputTextareaProps) {
  return (
    <FormField label={label} htmlFor={id} className={className} error={error}>
      <Textarea
        id={id}
        value={value}
        onChange={onChange}
        placeholder={placeholder}
        disabled={disabled}
        required={required}
        className="w-full resize-none"
        rows={rows}
      />
    </FormField>
  )
}
