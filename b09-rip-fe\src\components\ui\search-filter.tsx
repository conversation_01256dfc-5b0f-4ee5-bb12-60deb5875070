'use client';

import { useEffect, useState } from 'react';
import { Search, CalendarIcon } from 'lucide-react';
import { Input } from '@/components/ui/input';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Button } from '@/components/ui/button';
import { Calendar } from '@/components/ui/calendar';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover';
import { cn } from '@/lib/utils';
import { DateRange } from 'react-day-picker';
import { format } from 'date-fns';
import { id } from 'date-fns/locale';

export interface FilterOption {
  value: string;
  label: string;
}

export interface Filter {
  label: string;
  value: string | undefined;
  onChange: (value: string | undefined) => void;
  options: FilterOption[];
}

export interface DateRangeFilter {
  value: DateRange | undefined;
  onChange: (range: DateRange | undefined) => void;
}

interface SearchFilterProps {
  search: string;
  onSearchChange: (value: string) => void;
  filters: Filter[];
  dateRange?: DateRangeFilter;
  className?: string;
  searchPlaceholder?: string;
}

export function SearchFilter({
  search,
  onSearchChange,
  filters,
  dateRange,
  className,
  searchPlaceholder = 'Cari...',
}: SearchFilterProps) {
  const [searchValue, setSearchValue] = useState(search);
  const [date, setDate] = useState<DateRange | undefined>(dateRange?.value);

  // Debounce search input
  useEffect(() => {
    const timeoutId = setTimeout(() => {
      onSearchChange(searchValue);
    }, 300);

    return () => clearTimeout(timeoutId);
  }, [searchValue, onSearchChange]);

  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setSearchValue(e.target.value);
  };

  const handleDateChange = (range: DateRange | undefined) => {
    setDate(range);
    if (range?.from && range?.to) {
      dateRange?.onChange(range);
    }
  };

  return (
    <div
      className={cn(
        'flex flex-col sm:flex-row items-start sm:items-center gap-4 mb-6',
        className
      )}
    >
      {/* Search Input */}
      <div className="relative flex-1">
        <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
        <Input
          value={searchValue}
          onChange={handleSearchChange}
          placeholder={searchPlaceholder}
          className="pl-10 w-full"
        />
      </div>

      {/* Select Filters */}
      {filters.map((filter) => {
        // Find the label for the selected value
        const selectedOption = filter.options.find(
          (option) => option.value === filter.value
        );
        const displayValue = selectedOption
          ? selectedOption.label
          : filter.value;

        return (
          <Select
            key={filter.label}
            value={filter.value || 'all'}
            onValueChange={(value) =>
              filter.onChange(value === 'all' ? undefined : value)
            }
          >
            <SelectTrigger className="w-[200px]">
              <SelectValue placeholder={filter.label}>
                {filter.value && filter.value !== 'all'
                  ? displayValue
                  : `Semua ${filter.label}`}
              </SelectValue>
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">Semua {filter.label}</SelectItem>
              {filter.options.map((option) => (
                <SelectItem key={option.value} value={option.value}>
                  {option.label}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        );
      })}

      {/* Date Range Filter */}
      {dateRange && (
        <Popover>
          <PopoverTrigger asChild>
            <Button
              variant="outline"
              className={cn(
                'w-[240px] justify-start text-left font-normal',
                !date && 'text-muted-foreground'
              )}
            >
              <CalendarIcon className="mr-2 h-4 w-4" />
              {date?.from ? (
                date.to ? (
                  <>
                    {format(date.from, 'dd MMM yyyy', { locale: id })} -{' '}
                    {format(date.to, 'dd MMM yyyy', { locale: id })}
                  </>
                ) : (
                  format(date.from, 'dd MMM yyyy', { locale: id })
                )
              ) : (
                'Pilih rentang tanggal'
              )}
            </Button>
          </PopoverTrigger>
          <PopoverContent className="w-auto p-0" align="start">
            <Calendar
              initialFocus
              mode="range"
              defaultMonth={date?.from}
              selected={date}
              onSelect={handleDateChange}
              numberOfMonths={2}
              locale={id}
            />
          </PopoverContent>
        </Popover>
      )}
    </div>
  );
}
