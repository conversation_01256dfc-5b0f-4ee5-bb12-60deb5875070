'use client';

import { useState, useEffect, useCallback } from 'react';
import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { format } from 'date-fns';
import { ChevronLeft, Edit } from 'lucide-react';
import { WeeklyLogNote } from './WeeklyLogNote';
import { WeeklyLogTask } from './WeeklyLogTask';
import { WeeklyLogEditModal } from './WeeklyLogEditModal';
import { useRBAC } from '@/hooks/useRBAC';
import { DayActivities } from '@/types/weekly-log';

interface WeeklyLogDetailProps {
  weeklyLogId: string;
}

interface WeeklyLogDayData {
  date: string;
  day_of_week: number;
  note: {
    id: string;
    note: string;
  } | null;
  activities: DayActivities;
}

interface WeeklyLog {
  id: string;
  week_number: number;
  week_start_date: string;
  week_end_date: string;
  project_id: string;
  project_name: string;
  notes_by_day: Record<string, { note: string } | string>;
  days_data: Record<string, WeeklyLogDayData>;
}

export function WeeklyLogDetail({ weeklyLogId }: WeeklyLogDetailProps) {
  const [weeklyLog, setWeeklyLog] = useState<WeeklyLog | null>(null);
  const [loading, setLoading] = useState(true);
  const [isEditModalOpen, setIsEditModalOpen] = useState(false);
  const { toast } = useToast();
  const { hasRole } = useRBAC();

  // Check if user has permission to edit notes (Manager or Operation)
  const canEditNotes = hasRole(['Manager', 'Operation']);

  const fetchWeeklyLog = useCallback(async () => {
    try {
      setLoading(true);
      const response = await fetch(`/api/weekly-logs/id/${weeklyLogId}`);
      const data = await response.json();

      if (data.success) {
        setWeeklyLog(data.data);

        // Show success toast if refreshing after an edit
        if (isAfterEdit) {
          toast.success('Notes updated successfully');
        }
      } else {
        toast({
          title: 'Error',
          description: data.message ?? 'Failed to fetch weekly log',
          variant: 'destructive',
        });
      }
    } catch (error: unknown) {
      toast({
        title: 'Error',
        description:
          error instanceof Error ? error.message : 'Failed to fetch weekly log',
        variant: 'destructive',
      });
    } finally {
      setLoading(false);
    }
  }, [weeklyLogId, toast]);

  useEffect(() => {
    fetchWeeklyLog();
  }, [fetchWeeklyLog]);



  if (loading) {
    return <div>Loading...</div>;
  }

  if (!weeklyLog) {
    return <div>Weekly log not found</div>;
  }

  const days = ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday'];

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <Button variant="outline" onClick={() => window.history.back()}>
          <ChevronLeft className="h-4 w-4 mr-2" />
          Back
        </Button>

        {canEditNotes && (
          <Button onClick={() => setIsEditModalOpen(true)} variant="default">
            <Edit className="h-4 w-4 mr-2" />
            Edit Notes
          </Button>
        )}
      </div>

      <Card>
        <CardHeader>
          <CardTitle>
            {weeklyLog.project_name} - Week {weeklyLog.week_number}
          </CardTitle>
          <p className="text-sm text-gray-500">
            {format(new Date(weeklyLog.week_start_date), 'MMM d')} -{' '}
            {format(new Date(weeklyLog.week_end_date), 'MMM d, yyyy')}
          </p>
        </CardHeader>
      </Card>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {days.map((day, index) => {
          const dayNumber = (index + 1).toString();
          const dayData = weeklyLog.days_data[dayNumber];

          return (
            <Card key={day}>
              <CardHeader>
                <CardTitle>{day}</CardTitle>
                <p className="text-sm text-gray-500">
                  {format(new Date(dayData.date), 'MMM d, yyyy')}
                </p>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <h4 className="text-sm font-medium mb-2">Notes</h4>
                  {dayData.note ? (
                    <WeeklyLogNote
                      note={{
                        ...dayData.note,
                        created_at: dayData.date,
                        created_by: 'System',
                      }}
                    />
                  ) : (
                    <p className="text-sm text-gray-500">
                      No notes for this day
                    </p>
                  )}
                </div>

                <div>
                  <h4 className="text-sm font-medium mb-2">Tasks</h4>
                  <div className="space-y-2">
                    {dayData.activities.starting.length > 0 && (
                      <div>
                        <h5 className="text-xs font-medium text-gray-500">
                          Starting
                        </h5>
                        {dayData.activities.starting.map((task) => (
                          <WeeklyLogTask key={task.id} task={task} />
                        ))}
                      </div>
                    )}

                    {dayData.activities.ongoing.length > 0 && (
                      <div>
                        <h5 className="text-xs font-medium text-gray-500">
                          Ongoing
                        </h5>
                        {dayData.activities.ongoing.map((task) => (
                          <WeeklyLogTask key={task.id} task={task} />
                        ))}
                      </div>
                    )}

                    {dayData.activities.ending.length > 0 && (
                      <div>
                        <h5 className="text-xs font-medium text-gray-500">
                          Ending
                        </h5>
                        {dayData.activities.ending.map((task) => (
                          <WeeklyLogTask key={task.id} task={task} />
                        ))}
                      </div>
                    )}
                  </div>
                </div>
              </CardContent>
            </Card>
          );
        })}
      </div>

      {/* Edit Modal */}
      <WeeklyLogEditModal
        weeklyLogId={weeklyLogId}
        isOpen={isEditModalOpen}
        onOpenChange={setIsEditModalOpen}
        onSuccess={fetchWeeklyLog}
        initialNotes={
          weeklyLog
            ? Object.fromEntries(
                Object.entries(weeklyLog.days_data).map(([key, value]) => [
                  Number(key),
                  value.note,
                ])
              )
            : {}
        }
        dayLabels={days}
      />
    </div>
  );
}
