'use client';

import React, { useState, useEffect } from 'react';
import {
  <PERSON><PERSON>,
  <PERSON>alogContent,
  Di<PERSON>Header,
  <PERSON>alogTitle,
  DialogFooter,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { weeklyLogApi } from '@/lib/api/weekly-log';
import { toast } from 'sonner';
import { Loader2 } from 'lucide-react';

interface WeeklyLogEditModalProps {
  weeklyLogId: string;
  isOpen: boolean;
  onOpenChange: (open: boolean) => void;
  onSuccess: () => void;
  onNotesUpdate?: (notes: Record<number, string>) => void; // New callback for direct updates
  initialNotes: {
    [key: number]: {
      id: string;
      note: string;
    } | null;
  };
  dayLabels: string[];
}

export function WeeklyLogEditModal({
  weeklyLogId,
  isOpen,
  onOpenChange,
  onSuccess,
  onNotesUpdate,
  initialNotes,
  dayLabels,
}: WeeklyLogEditModalProps) {
  const [notes, setNotes] = useState<Record<number, string>>({});
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Initialize notes when modal opens
  useEffect(() => {
    if (isOpen) {
      console.log('WeeklyLogEditModal - initialNotes:', initialNotes);

      const initialNotesData: Record<number, string> = {};

      // Convert from 1-based to 0-based for array indexing
      for (let i = 1; i <= 5; i++) {
        // Handle different note data structures
        let noteText = '';

        if (initialNotes[i]) {
          const note = initialNotes[i];
          if (note && typeof note === 'object' && 'note' in note) {
            noteText = note.note;
          }
        }

        initialNotesData[i] = noteText;
        console.log(`Day ${i} initial note:`, noteText);
      }

      console.log('WeeklyLogEditModal - initialNotesData:', initialNotesData);
      setNotes(initialNotesData);
    }
  }, [isOpen, initialNotes]);

  const handleNoteChange = (day: number, value: string) => {
    setNotes((prev) => ({
      ...prev,
      [day]: value,
    }));
  };

  const handleSubmit = async () => {
    try {
      setIsSubmitting(true);

      // Filter out empty notes that don't need to be updated
      const notesToUpdate: Record<number, string> = {};
      for (let i = 1; i <= 5; i++) {
        // Get the initial note text based on different possible structures
        let initialNoteText = '';
        if (initialNotes[i]) {
          const note = initialNotes[i];
          if (note && typeof note === 'object' && 'note' in note) {
            initialNoteText = note.note;
          }
        }

        // Only include notes that have changed
        if (notes[i] !== initialNoteText) {
          console.log(
            `Day ${i} note changed from "${initialNoteText}" to "${notes[i]}"`
          );
          notesToUpdate[i] = notes[i];
        }
      }

      // Only make API call if there are notes to update
      if (Object.keys(notesToUpdate).length > 0) {
        console.log('Notes to update:', notesToUpdate);
        const response = await weeklyLogApi.updateNotes(
          weeklyLogId,
          notesToUpdate
        );
        console.log('Update notes response:', response);

        if (response.success) {
          toast.success('Notes updated successfully');

          // Call onNotesUpdate callback to directly update the UI
          if (onNotesUpdate) {
            console.log('Calling onNotesUpdate with:', notesToUpdate);
            onNotesUpdate(notesToUpdate);
          }

          // Also call onSuccess to refresh data from API
          console.log('Calling onSuccess to refresh data');
          onSuccess();
          onOpenChange(false);
        } else {
          toast.error(response.message ?? 'Failed to update notes');
        }
      } else {
        // No changes to save
        toast.info('No changes to save');
        onOpenChange(false);
      }
    } catch (error) {
      console.error('Error updating notes:', error);
      toast.error('Failed to update notes');
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle>Edit Weekly Log Notes</DialogTitle>
        </DialogHeader>

        <div className="space-y-4 py-4">
          {dayLabels.map((day, index) => {
            // Convert from 0-based to 1-based for API
            const dayNumber = index + 1;

            return (
              <div key={day} className="space-y-2">
                <Label htmlFor={`note-${dayNumber}`}>{day}</Label>
                <Textarea
                  id={`note-${dayNumber}`}
                  value={notes[dayNumber] || ''}
                  onChange={(e) => handleNoteChange(dayNumber, e.target.value)}
                  placeholder={`Enter notes for ${day}`}
                  rows={3}
                  disabled={isSubmitting}
                />
              </div>
            );
          })}
        </div>

        <DialogFooter className="sm:justify-end">
          <Button
            type="button"
            variant="outline"
            onClick={() => onOpenChange(false)}
            disabled={isSubmitting}
          >
            Cancel
          </Button>
          <Button type="button" onClick={handleSubmit} disabled={isSubmitting}>
            {isSubmitting ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                Saving...
              </>
            ) : (
              'Save Changes'
            )}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
