'use client';

import { useState, useEffect, useCallback } from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON>nt, Card<PERSON><PERSON>er, CardTitle } from '@/components/ui/card';

import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { useToast } from '@/components/ui/use-toast';
import {
  format,
  addDays,
  isWithinInterval,
  parseISO,
  startOfMonth,
  endOfMonth,
  parse,
  getYear,
  getMonth,
  isValid,
} from 'date-fns';
import { CalendarDays, AlertCircle, Check, X } from 'lucide-react';
import { useRouter } from 'next/navigation';
import { weeklyLogApi } from '@/lib/api/weekly-log';
import { WeeklyLog, Task } from '@/types/weekly-log';
import { Badge } from '@/components/ui/badge';
import { PageTitle } from '@/components/ui/PageTitle';
import { BackButton } from '@/components/ui/BackButton';
import { projectApi } from '@/lib/api/project';
import { useRBAC } from '@/hooks/useRBAC';
import { Textarea } from '@/components/ui/textarea';
import { Calendar } from '@/components/ui/calendar';
import { id as localeID } from 'date-fns/locale';
import { Button } from '@/components/ui/button';
import { Loader2 } from 'lucide-react';

interface WeeklyLogListProps {
  projectId: string;
  projectStatus?: string;
}

interface AvailableWeek {
  week_number: number;
  date_range: string;
}

interface TaskWithEmployee extends Task {
  employee_name?: string;
  employee_department?: string;
  employee_role?: string;
  assigned_by_name?: string;
  assigned_by_department?: string;
  assigned_by_role?: string;
}

export function WeeklyLogList({
  projectId,
  projectStatus: initialProjectStatus,
}: WeeklyLogListProps) {
  const [weeklyLog, setWeeklyLog] = useState<WeeklyLog | null>(null);
  const [loadingProjectDetails, setLoadingProjectDetails] = useState(true);
  const [loadingAvailableWeeks, setLoadingAvailableWeeks] = useState(true);
  const [loadingWeeklyLog, setLoadingWeeklyLog] = useState(false);
  const [selectedWeek, setSelectedWeek] = useState<number | null>(null);
  const [availableWeeks, setAvailableWeeks] = useState<AvailableWeek[]>([]);
  const [projectName, setProjectName] = useState<string>('');
  const [currentProjectStatus, setCurrentProjectStatus] = useState<
    string | undefined
  >(initialProjectStatus);
  const [calendarMonth, setCalendarMonth] = useState<Date>(
    startOfMonth(new Date())
  );
  const [editingNoteKey, setEditingNoteKey] = useState<string | null>(null);
  const [editingNoteContent, setEditingNoteContent] = useState<string>('');
  const { toast } = useToast();
  const router = useRouter();
  const { hasRole } = useRBAC();

  const canEditNotes = hasRole(['Manager', 'Operation']);

  const handleNoteChange = (value: string) => {
    setEditingNoteContent(value);
  };

  const startEditing = (dateKey: string, initialContent: string) => {
    setEditingNoteKey(dateKey);
    setEditingNoteContent(initialContent);
  };

  const saveNoteToBackend = async (dateKey: string, noteText: string) => {
    if (!weeklyLog) return;

    try {
      const date = new Date(dateKey);
      const dayOfWeek = date.getDay();

      if (dayOfWeek < 1 || dayOfWeek > 5) {
        console.error(
          `Cannot save note for weekend day: ${dateKey}, day of week: ${dayOfWeek}`
        );
        toast({
          title: 'Error',
          description: 'Cannot save notes for weekend days',
          variant: 'destructive',
        });
        return;
      }

      console.log(`Saving note for date ${dateKey}, day of week: ${dayOfWeek}`);

      const notes: Record<number, string> = {
        [dayOfWeek]: noteText,
      };

      const response = await weeklyLogApi.updateNotes(weeklyLog.id, notes);

      if (response.success) {
        console.log('Note saved successfully');
        toast({
          title: 'Success',
          description: 'Note saved successfully',
        });

        fetchWeeklyLog();
      } else {
        console.error('Failed to save note:', response.message);
        toast({
          title: 'Error',
          description: 'Failed to save note',
          variant: 'destructive',
        });
      }
    } catch (error) {
      console.error('Error saving note:', error);
      toast({
        title: 'Error',
        description: 'Failed to save note',
        variant: 'destructive',
      });
    }
  };

  const handleEndEditing = (dateKey: string, shouldSave: boolean = true) => {
    if (editingNoteKey === dateKey && shouldSave) {
      saveNoteToBackend(dateKey, editingNoteContent);
    }

    setEditingNoteKey(null);
    setEditingNoteContent('');
  };

  const parseDateRange = (
    dateRange: string
  ): { start: Date; end: Date } | null => {
    try {
      const parts = dateRange.split(' - ');
      const year = dateRange.match(/(\d{4})/g)?.pop();
      if (!year) return null;

      let startDateStr = parts[0];
      let endDateStr = parts[1];

      if (!startDateStr.includes(year)) startDateStr += `, ${year}`;
      if (!endDateStr.includes(year)) endDateStr += `, ${year}`;

      const dateFnsFormat = 'MMM d, yyyy';

      let startDate = parse(startDateStr, dateFnsFormat, new Date());
      let endDate = parse(endDateStr, dateFnsFormat, new Date());

      if (!isValid(startDate) || !isValid(endDate)) {
        console.warn(
          "Could not parse date range with 'MMM d, yyyy':",
          dateRange
        );
        const alternativeFormat = 'MMMM d, yyyy';
        startDate = parse(startDateStr, alternativeFormat, new Date());
        endDate = parse(endDateStr, alternativeFormat, new Date());
        if (!isValid(startDate) || !isValid(endDate)) {
          console.error(
            'Failed to parse date range with alternative format too:',
            dateRange
          );
          return null;
        }
      }
      return { start: startDate, end: endDate };
    } catch (e) {
      console.error('Error parsing date range:', dateRange, e);
      return null;
    }
  };

  const fetchProjectDetails = useCallback(async () => {
    if (projectId === 'all') {
      setProjectName('All Projects');
      setCurrentProjectStatus(undefined);
      setLoadingProjectDetails(false);
      return;
    }
    try {
      setLoadingProjectDetails(true);
      const response = await projectApi.getProjectById(projectId);
      if (response.success && response.data) {
        setProjectName(response.data.project_name);
        setCurrentProjectStatus(response.data.status_project);
      } else {
        toast({
          title: 'Error',
          description: 'Failed to fetch project details',
          variant: 'destructive',
        });
      }
    } catch (error) {
      console.error('Error fetching project details:', error);
      toast.error('Failed to fetch project details');
    } finally {
      setLoadingProjectDetails(false);
    }
  }, [projectId, toast]);

  const fetchAvailableWeeks = useCallback(async () => {
    if (projectId === 'all') {
      setAvailableWeeks([]);
      setLoadingAvailableWeeks(false);
      return;
    }
    try {
      setLoadingAvailableWeeks(true);
      const response = await weeklyLogApi.getAvailableWeeks(projectId);

      if (response.success && response.data) {
        const weekRanges = response.data.week_numbers_with_ranges || [];

        weekRanges.sort((a, b) => b.week_number - a.week_number);

        setAvailableWeeks(weekRanges);

        const currentSelectedWeekData = selectedWeek
          ? weekRanges.find((w) => w.week_number === selectedWeek)
          : null;

        if (weekRanges.length > 0 && !currentSelectedWeekData) {
          const mostRecentWeek = weekRanges[0];
          setSelectedWeek(mostRecentWeek.week_number);
          const range = parseDateRange(mostRecentWeek.date_range);
          if (range && range.start) {
            setCalendarMonth(startOfMonth(range.start));
          }
        } else if (weekRanges.length === 0) {
          setSelectedWeek(null);
        } else if (currentSelectedWeekData) {
          const range = parseDateRange(currentSelectedWeekData.date_range);
          if (range && range.start) {
            if (
              getMonth(calendarMonth) !== getMonth(range.start) ||
              getYear(calendarMonth) !== getYear(range.start)
            ) {
              setCalendarMonth(startOfMonth(range.start));
            }
          }
        }
      } else {
        toast({
          title: 'Error',
          description: response.message || 'Failed to fetch available weeks',
          variant: 'destructive',
        });
      }
    } catch (error) {
      console.error('Error fetching available weeks:', error);
      toast.error('Failed to fetch available weeks');
    } finally {
      setLoadingAvailableWeeks(false);
    }
  }, [projectId, toast, calendarMonth, selectedWeek]);

  const fetchWeeklyLog = useCallback(async () => {
    if (!selectedWeek || projectId === 'all') {
      setWeeklyLog(null);
      if (availableWeeks.length > 0 && !selectedWeek) {
        setSelectedWeek(availableWeeks[0].week_number);
      }
      return;
    }
    try {
      setLoadingWeeklyLog(true);
      console.log(
        'Fetching weekly log for project:',
        projectId,
        'week:',
        selectedWeek
      );
      const response = await weeklyLogApi.getWeeklyLogByProjectAndWeek(
        projectId,
        selectedWeek
      );

      if (response.success && response.data) {
        const updatedWeeklyLog = { ...response.data };

        if (!updatedWeeklyLog.notes_by_day) {
          updatedWeeklyLog.notes_by_day = {};
        }

        setWeeklyLog(updatedWeeklyLog);
        setProjectName(response.data.project_name);
      } else {
        console.error('Failed to fetch weekly log:', response.message);
        toast.error('Failed to fetch weekly log');
      }
    } catch (error) {
      console.error('Error fetching weekly log:', error);
      toast({
        title: 'Error',
        description:
          error instanceof Error ? error.message : 'Failed to fetch weekly log',
        variant: 'destructive',
      });
    } finally {
      setLoadingWeeklyLog(false);
    }
  }, [projectId, selectedWeek, toast, availableWeeks]);

  useEffect(() => {
    fetchProjectDetails();
  }, [fetchProjectDetails]);

  useEffect(() => {
    if (projectId !== 'all' && !loadingProjectDetails) {
      fetchAvailableWeeks();
    }
  }, [fetchAvailableWeeks, projectId, loadingProjectDetails]);

  useEffect(() => {
    if (selectedWeek !== null) {
      fetchWeeklyLog();
    } else {
      setWeeklyLog(null);
    }
  }, [selectedWeek, fetchWeeklyLog]);

  const handleWeekChange = (weekNumber: number) => {
    setSelectedWeek(weekNumber);
  };

  const handleMonthChange = (month: Date) => {
    setCalendarMonth(startOfMonth(month));
  };

  const handleDayClick = (date: Date | undefined) => {
    if (!date) return;

    // Find which week contains this date
    const weekContainingDate = availableWeeks.find((week) => {
      const range = parseDateRange(week.date_range);
      if (!range) return false;
      return isWithinInterval(date, { start: range.start, end: range.end });
    });

    // Only allow clicks on available working days (Monday-Friday)
    if (weekContainingDate && date.getDay() >= 1 && date.getDay() <= 5) {
      handleWeekChange(weekContainingDate.week_number);
    }
  };

  const getModifiers = () => {
    const modifiers: { [key: string]: Date[] | ((date: Date) => boolean) } = {
      available: [],
      selected: [],
    };
    const availableDays: Date[] = [];
    availableWeeks.forEach((week) => {
      const range = parseDateRange(week.date_range);
      if (range) {
        for (let d = new Date(range.start); d <= range.end; d = addDays(d, 1)) {
          if (d.getDay() >= 1 && d.getDay() <= 5) {
            availableDays.push(new Date(d));
          }
        }
        if (week.week_number === selectedWeek) {
          const selectedDays: Date[] = [];
          for (
            let d = new Date(range.start);
            d <= range.end;
            d = addDays(d, 1)
          ) {
            if (d.getDay() >= 1 && d.getDay() <= 5) {
              selectedDays.push(new Date(d));
            }
          }
          modifiers.selected = selectedDays;
        }
      }
    });
    modifiers.available = availableDays;
    return modifiers;
  };

  const modifierStyles = {
    available: {
      backgroundColor: 'hsl(var(--primary) / 0.1)',
      color: 'hsl(var(--primary))',
      borderRadius: 'var(--radius)',
    },
    selected: {
      backgroundColor: 'hsl(var(--primary))',
      color: 'hsl(var(--primary-foreground))',
      fontWeight: 'bold',
      borderRadius: 'var(--radius)',
    },
  };

  const filteredWeeksByMonth = availableWeeks.filter((week) => {
    const range = parseDateRange(week.date_range);
    if (!range) return false;
    const calStart = startOfMonth(calendarMonth);
    const calEnd = endOfMonth(calendarMonth);
    return (
      isWithinInterval(range.start, { start: calStart, end: calEnd }) ||
      isWithinInterval(range.end, { start: calStart, end: calEnd }) ||
      (range.start < calStart && range.end > calEnd)
    );
  });

  const handleBack = () => {
    if (projectId === 'all') {
      router.push('/weekly-log');
    } else {
      router.push(`/project/${projectId}`);
    }
  };

  const getStatusMessage = () => {
    if (projectId === 'all') return null;

    switch (currentProjectStatus?.toLowerCase()) {
      case 'not started':
        return {
          title: 'Project Not Started',
          message:
            'Weekly logs will be available once the project status is set to "In Progress".',
          icon: (
            <AlertCircle className="h-12 w-12 mx-auto text-yellow-500 mb-4" />
          ),
        };
      case 'in progress':
        return {
          title: 'No Weekly Logs Yet',
          message:
            'Weekly logs will be automatically created for this project. The first weekly log will be created at the start of the current week.',
          icon: (
            <CalendarDays className="h-12 w-12 mx-auto text-blue-500 mb-4" />
          ),
        };
      case 'completed':
        return {
          title: 'Project Completed',
          message:
            'This project has been completed. You can view historical weekly logs by selecting a week from the dropdown.',
          icon: (
            <CalendarDays className="h-12 w-12 mx-auto text-green-500 mb-4" />
          ),
        };
      case 'cancelled':
        return {
          title: 'Project Cancelled',
          message:
            'This project has been cancelled. You can view historical weekly logs by selecting a week from the dropdown.',
          icon: <AlertCircle className="h-12 w-12 mx-auto text-red-500 mb-4" />,
        };
      default:
        return null;
    }
  };

  const statusMessage = getStatusMessage();

  if (loadingProjectDetails || (projectId !== 'all' && loadingAvailableWeeks)) {
    return (
      <div className="container mx-auto py-6 px-6">
        <div className="flex items-center gap-4 mb-6">
          <BackButton onClick={handleBack} />
          <PageTitle title="Weekly Log" />
        </div>
        <Card>
          <CardContent>
            <div className="text-center py-8">Loading weekly log...</div>
          </CardContent>
        </Card>
      </div>
    );
  }

  if (
    projectId !== 'all' &&
    availableWeeks.length === 0 &&
    !loadingAvailableWeeks
  ) {
    return (
      <div className="container mx-auto py-6 px-6">
        <div className="flex items-center gap-4 mb-6">
          <BackButton onClick={handleBack} />
          <PageTitle title="Weekly Log" />
        </div>
        <Card>
          <CardContent>
            <div className="text-center py-8">
              {statusMessage?.icon || (
                <CalendarDays className="h-12 w-12 mx-auto text-gray-400 mb-4" />
              )}
              <h3 className="text-lg font-semibold text-gray-900 mb-2">
                {statusMessage?.title || 'No Weekly Logs Found'}
              </h3>
              <p className="text-gray-500 mb-4">
                {statusMessage?.message ||
                  (projectId === 'all'
                    ? 'There are no weekly logs available.'
                    : 'No weekly logs have been generated for this project yet.')}
              </p>
              {currentProjectStatus?.toLowerCase() === 'in progress' && (
                <Badge
                  variant="outline"
                  className="text-blue-500 border-blue-500"
                >
                  Project is in progress - Weekly logs will be created
                  automatically
                </Badge>
              )}
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  if (selectedWeek && loadingWeeklyLog) {
    return (
      <div className="container mx-auto py-6 px-6">
        <div className="flex items-center gap-4 mb-6">
          <BackButton onClick={handleBack} />
          <PageTitle
            title={projectName ? `${projectName} - Weekly Log` : 'Weekly Log'}
          />
        </div>
        <Card>
          <CardContent>
            <div className="text-center py-8">
              Loading weekly log for week {selectedWeek}...
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  if (
    selectedWeek &&
    !weeklyLog &&
    !loadingWeeklyLog &&
    availableWeeks.length > 0
  ) {
    return (
      <div className="container mx-auto py-6 px-6">
        <div className="flex items-center gap-4 mb-6">
          <BackButton onClick={handleBack} />
          <PageTitle
            title={projectName ? `${projectName} - Weekly Log` : 'Weekly Log'}
          />
        </div>
        <Card>
          <CardContent>
            <div className="text-center py-8">
              <AlertCircle className="h-12 w-12 mx-auto text-red-500 mb-4" />
              <h3 className="text-lg font-semibold text-gray-900 mb-2">
                Error Loading Log
              </h3>
              <p className="text-gray-500 mb-4">
                Could not load details for Week {selectedWeek}. Please try
                another week or contact support.
              </p>
              <div className="flex flex-col sm:flex-row items-center justify-between mb-6">
                <div className="flex items-center gap-4">
                  {/* Placeholder for project name and status badge */}
                </div>
                <div className="flex flex-col md:flex-row gap-4 mt-4 md:mt-0 w-full md:w-auto">
                  <div className="w-full md:w-[300px] border rounded-md p-2 bg-background">
                    <Calendar
                      mode="single"
                      selected={calendarMonth}
                      onSelect={handleDayClick}
                      onMonthChange={handleMonthChange}
                      month={calendarMonth}
                      className="p-0"
                      classNames={{
                        months:
                          'flex flex-col sm:flex-row space-y-4 sm:space-x-4 sm:space-y-0',
                        month: 'space-y-4',
                        caption_label: 'text-sm font-medium',
                        nav_button_previous: 'absolute left-1',
                        nav_button_next: 'absolute right-1',
                        table: 'w-full border-collapse space-y-1',
                        head_row: 'flex',
                        head_cell:
                          'text-muted-foreground rounded-md w-8 font-normal text-[0.8rem]',
                        row: 'flex w-full mt-2',
                        cell: 'text-center text-sm p-0 relative [&:has([aria-selected])]:bg-accent first:[&:has([aria-selected])]:rounded-l-md last:[&:has([aria-selected])]:rounded-r-md focus-within:relative focus-within:z-20',
                        day: 'h-8 w-8 p-0 font-normal aria-selected:opacity-100',
                        day_selected:
                          'bg-primary text-primary-foreground hover:bg-primary hover:text-primary-foreground focus:bg-primary focus:text-primary-foreground',
                        day_today: 'bg-accent text-accent-foreground',
                        day_outside: 'text-muted-foreground opacity-50',
                        day_disabled: 'text-muted-foreground opacity-50',
                        day_range_middle:
                          'aria-selected:bg-accent aria-selected:text-accent-foreground',
                        day_hidden: 'invisible',
                      }}
                      modifiers={getModifiers()}
                      modifiersStyles={modifierStyles}
                    />
                  </div>
                  <div className="w-full md:w-[280px] border rounded-md p-3">
                    <h4 className="text-sm font-medium mb-2 text-center">
                      Available Weeks for {format(calendarMonth, 'MMMM yyyy')}
                    </h4>
                    {filteredWeeksByMonth.length > 0 ? (
                      <div className="space-y-2 h-[250px] overflow-y-auto pr-1">
                        {filteredWeeksByMonth.map((week) => (
                          <Button
                            key={week.week_number}
                            variant={
                              selectedWeek === week.week_number
                                ? 'default'
                                : 'outline'
                            }
                            onClick={() => handleWeekChange(week.week_number)}
                            className="w-full justify-start text-left h-auto py-2"
                          >
                            <div className="flex flex-col">
                              <span className="font-semibold">
                                Week {week.week_number}
                              </span>
                              <span className="text-xs text-muted-foreground">
                                {week.date_range}
                              </span>
                            </div>
                          </Button>
                        ))}
                      </div>
                    ) : (
                      <p className="text-sm text-muted-foreground text-center py-4">
                        No weekly logs available for this month.
                      </p>
                    )}
                  </div>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  if (!weeklyLog && availableWeeks.length > 0) {
  } else if (
    !weeklyLog &&
    availableWeeks.length === 0 &&
    !loadingAvailableWeeks &&
    projectId !== 'all'
  ) {
    return (
      <div className="container mx-auto py-6 px-6">
        <div className="flex items-center gap-4 mb-6">
          <BackButton onClick={handleBack} />
          <PageTitle
            title={projectName ? `${projectName} - Weekly Log` : 'Weekly Log'}
          />
        </div>
        <Card>
          <CardContent>
            <div className="text-center py-8">
              {statusMessage?.icon || (
                <CalendarDays className="h-12 w-12 mx-auto text-gray-400 mb-4" />
              )}
              <h3 className="text-lg font-semibold text-gray-900 mb-2">
                {statusMessage?.title || 'No Weekly Logs Found'}
              </h3>
              <p className="text-gray-500 mb-4">
                {statusMessage?.message ||
                  (projectId === 'all'
                    ? 'There are no weekly logs available.'
                    : 'No weekly logs have been generated for this project yet. Logs are created when project status is "In Progress".')}
              </p>
              {currentProjectStatus?.toLowerCase() === 'in progress' &&
                availableWeeks.length === 0 && (
                  <Badge
                    variant="outline"
                    className="text-blue-500 border-blue-500"
                  >
                    Project is in progress - Weekly logs will be created
                    automatically.
                  </Badge>
                )}
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  const weekDays = weeklyLog
    ? Array.from({ length: 5 }, (_, i) =>
        addDays(parseISO(weeklyLog.week_start_date), i)
      )
    : [];

  return (
    <div className="container mx-auto py-6 px-6">
      {/* Page Header */}
      <div className="flex flex-col sm:flex-row items-start sm:items-center justify-between mb-6 gap-4">
        <div className="flex items-center gap-4">
          <BackButton onClick={handleBack} />
          <PageTitle
            title={projectName ? `${projectName} - Weekly Log` : 'Weekly Log'}
          />
          {currentProjectStatus && projectId !== 'all' && (
            <Badge
              variant={
                currentProjectStatus.toLowerCase() === 'in progress'
                  ? 'default'
                  : currentProjectStatus.toLowerCase() === 'completed'
                    ? 'success'
                    : currentProjectStatus.toLowerCase() === 'cancelled'
                      ? 'destructive'
                      : 'outline'
              }
              className="text-sm whitespace-nowrap"
            >
              {currentProjectStatus}
            </Badge>
          )}
        </div>
        {/* Moved week selector logic to a conditional render below header, to be part of the new two-column layout if weeks are available */}
      </div>

      {/* Main Content Area: Two-column layout for desktop, stacked for mobile */}
      <div className="flex flex-col md:flex-row gap-6">
        {/* Left Column: Calendar and Week List (Only if projectId is specific and weeks are available or loading) */}
        {projectId !== 'all' &&
          (loadingAvailableWeeks || availableWeeks.length > 0) && (
            <div className="md:w-[340px] lg:w-[360px] flex-shrink-0 flex flex-col gap-4">
              <div className="border rounded-md p-2 bg-background shadow-sm">
                <Calendar
                  mode="single"
                  selected={
                    selectedWeek
                      ? (() => {
                          const weekData = availableWeeks.find(
                            (w) => w.week_number === selectedWeek
                          );
                          if (weekData) {
                            const range = parseDateRange(weekData.date_range);
                            return range?.start;
                          }
                          return undefined;
                        })()
                      : undefined
                  }
                  onSelect={handleDayClick}
                  onMonthChange={handleMonthChange}
                  month={calendarMonth}
                  className="p-0"
                  classNames={{
                    months:
                      'flex flex-col sm:flex-row space-y-4 sm:space-x-4 sm:space-y-0',
                    month: 'space-y-4',
                    caption_label: 'text-sm font-medium',
                    nav_button_previous: 'absolute left-1 top-1',
                    nav_button_next: 'absolute right-1 top-1',
                    table: 'w-full border-collapse space-y-1',
                    head_row: 'flex',
                    head_cell:
                      'text-muted-foreground rounded-md w-9 font-normal text-[0.8rem]',
                    row: 'flex w-full mt-2',
                    cell: 'h-9 w-9 text-center text-sm p-0 relative [&:has([aria-selected])]:bg-accent first:[&:has([aria-selected])]:rounded-l-md last:[&:has([aria-selected])]:rounded-r-md focus-within:relative focus-within:z-20',
                    day: 'h-9 w-9 p-0 font-normal aria-selected:opacity-100',
                    day_selected:
                      'bg-primary text-primary-foreground hover:bg-primary hover:text-primary-foreground focus:bg-primary focus:text-primary-foreground',
                    day_today: 'bg-accent text-accent-foreground',
                    day_outside:
                      'text-muted-foreground opacity-50 aria-selected:bg-accent/50 aria-selected:text-muted-foreground aria-selected:opacity-30',
                    day_disabled: 'text-muted-foreground opacity-50',
                    day_range_middle:
                      'aria-selected:bg-accent aria-selected:text-accent-foreground',
                    day_hidden: 'invisible',
                  }}
                  modifiers={getModifiers()}
                  modifiersStyles={modifierStyles}
                  footer={
                    selectedWeek && weeklyLog ? (
                      <p className="text-xs text-center text-muted-foreground pt-2">
                        Selected: Week {selectedWeek} (
                        {
                          availableWeeks.find(
                            (w) => w.week_number === selectedWeek
                          )?.date_range
                        }
                        )
                      </p>
                    ) : null
                  }
                />
              </div>
              <div className="border rounded-md p-3 bg-background shadow-sm">
                <h4 className="text-sm font-medium mb-2 text-center">
                  Available Weeks:{' '}
                  {format(calendarMonth, 'MMMM yyyy', { locale: localeID })}
                </h4>
                {loadingAvailableWeeks ? (
                  <div className="text-center py-4 text-sm text-muted-foreground h-[260px]">
                    Loading weeks...
                  </div>
                ) : filteredWeeksByMonth.length > 0 ? (
                  <div className="space-y-2 pr-3 h-[260px] overflow-y-auto">
                    {filteredWeeksByMonth.map((week) => (
                      <Button
                        key={week.week_number}
                        variant={
                          selectedWeek === week.week_number
                            ? 'default'
                            : 'outline'
                        }
                        onClick={() => handleWeekChange(week.week_number)}
                        className="w-full justify-start text-left h-auto py-2"
                        disabled={loadingWeeklyLog}
                      >
                        <div className="flex flex-col">
                          <span className="font-semibold">
                            Week {week.week_number}
                          </span>
                          <span className="text-xs text-muted-foreground">
                            {week.date_range}
                          </span>
                        </div>
                        {selectedWeek === week.week_number &&
                          loadingWeeklyLog && (
                            <Loader2 className="ml-auto h-4 w-4 animate-spin" />
                          )}
                      </Button>
                    ))}
                  </div>
                ) : (
                  <p className="text-sm text-muted-foreground text-center py-4 h-[260px]">
                    No weekly logs for this month.
                  </p>
                )}
              </div>
            </div>
          )}

        {/* Right Column: Weekly Log Details or Placeholder Messages */}
        <div className="flex-1 min-w-0">
          {' '}
          {/* min-w-0 is important for flex item to shrink properly */}
          {
            weeklyLog && projectId !== 'all' ? (
              <Card>
                <CardHeader>
                  <div className="flex items-center justify-between">
                    <div className="space-y-1">
                      <CardTitle className="text-xl font-semibold text-[#AB8B3B]">
                        Weekly Log Details
                      </CardTitle>
                    </div>
                  </div>
                </CardHeader>
                <CardContent>
                  <div className="rounded-md border">
                    <Table>
                      <TableHeader>
                        <TableRow>
                          <TableHead className="w-[120px] bg-gray-50">
                            Day
                          </TableHead>
                          <TableHead className="w-[120px] bg-gray-50">
                            Date
                          </TableHead>
                          <TableHead className="w-[300px] bg-gray-50">
                            Tasks
                          </TableHead>
                          <TableHead className="w-[180px] bg-gray-50">
                            PIC
                          </TableHead>
                          <TableHead className="w-[120px] bg-gray-50">
                            Status
                          </TableHead>
                          <TableHead className="w-[200px] bg-gray-50">
                            Notes
                          </TableHead>
                        </TableRow>
                      </TableHeader>
                      <TableBody>
                        {weekDays.map((day) => {
                          const dateKey = format(day, 'yyyy-MM-dd');
                          const jsDay = day.getDay();
                          const dayOfWeek = jsDay === 0 ? 7 : jsDay;

                          const dayData =
                            weeklyLog.days_data[dayOfWeek.toString()];

                          console.log(
                            `Processing day ${dayOfWeek}, date: ${dateKey}`,
                            dayData
                          );

                          const activities = dayData?.activities || {
                            starting: [],
                            ending: [],
                            ongoing: [],
                            not_completed: [],
                            on_progress: [],
                            completed: [],
                          };

                          const allTasks = [
                            ...activities.starting,
                            ...activities.ending,
                            ...activities.ongoing,
                            ...activities.not_completed,
                            ...activities.on_progress,
                            ...activities.completed,
                          ].filter(
                            (task, index, self) =>
                              index === self.findIndex((t) => t.id === task.id)
                          ) as TaskWithEmployee[];

                          return (
                            <TableRow
                              key={day.toISOString()}
                              className="hover:bg-gray-50"
                            >
                              <TableCell className="font-medium">
                                {format(day, 'EEEE')}
                              </TableCell>
                              <TableCell>
                                {format(day, 'MMM d, yyyy')}
                              </TableCell>
                              <TableCell>
                                {allTasks.length > 0 ? (
                                  <div className="space-y-4">
                                    {allTasks.map((task) => (
                                      <div
                                        key={`${task.id}-${dateKey}`}
                                        className="p-4 border rounded-lg bg-white hover:bg-gray-50 transition-colors"
                                      >
                                        <div className="font-medium text-gray-900">
                                          {task.description}
                                        </div>
                                      </div>
                                    ))}
                                  </div>
                                ) : (
                                  <span className="text-gray-500">
                                    No tasks
                                  </span>
                                )}
                              </TableCell>
                              <TableCell>
                                {allTasks.length > 0 ? (
                                  <div className="space-y-4">
                                    {allTasks.map((task) => (
                                      <div
                                        key={`${task.id}-${dateKey}-pic`}
                                        className="p-4 border rounded-lg bg-white hover:bg-gray-50 transition-colors"
                                      >
                                        <span className="text-sm font-medium text-gray-900">
                                          {task.employee_name || 'Unknown'}
                                        </span>
                                      </div>
                                    ))}
                                  </div>
                                ) : (
                                  <span className="text-gray-500">-</span>
                                )}
                              </TableCell>
                              <TableCell>
                                {allTasks.length > 0 ? (
                                  <div className="space-y-4">
                                    {allTasks.map((task) => (
                                      <div
                                        key={`${task.id}-${dateKey}-status`}
                                        className="p-4 border rounded-lg bg-white hover:bg-gray-50 transition-colors"
                                      >
                                        <Badge
                                          variant="outline"
                                          className={
                                            task.completion_status ===
                                            'completed'
                                              ? 'bg-green-100 text-green-800 border-green-200'
                                              : task.completion_status ===
                                                  'on_progress'
                                                ? 'bg-yellow-100 text-yellow-800 border-yellow-200'
                                                : 'bg-red-100 text-red-800 border-red-200'
                                          }
                                        >
                                          {task.completion_status.replace(
                                            '_',
                                            ' '
                                          )}
                                        </Badge>
                                      </div>
                                    ))}
                                  </div>
                                ) : (
                                  <span className="text-gray-500">-</span>
                                )}
                              </TableCell>
                              <TableCell>
                                {editingNoteKey === dateKey && canEditNotes ? (
                                  <div>
                                    <Textarea
                                      autoFocus
                                      className="w-full min-h-[80px] mb-2"
                                      value={editingNoteContent}
                                      onChange={(e) =>
                                        handleNoteChange(e.target.value)
                                      }
                                      onKeyDown={(e) => {
                                        if (e.key === 'Enter' && !e.shiftKey) {
                                          e.preventDefault();
                                          handleEndEditing(dateKey);
                                        }
                                      }}
                                    />
                                    <div className="flex justify-end space-x-2">
                                      <button
                                        type="button"
                                        className="p-1 rounded-full bg-red-100 text-red-600 hover:bg-red-200"
                                        onClick={() =>
                                          handleEndEditing(dateKey, false)
                                        }
                                      >
                                        <X className="h-4 w-4" />
                                      </button>
                                      <button
                                        type="button"
                                        className="p-1 rounded-full bg-green-100 text-green-600 hover:bg-green-200"
                                        onClick={() =>
                                          handleEndEditing(dateKey)
                                        }
                                      >
                                        <Check className="h-4 w-4" />
                                      </button>
                                    </div>
                                  </div>
                                ) : canEditNotes ? (
                                  <button
                                    type="button"
                                    className="text-sm text-gray-600 min-h-[40px] p-2 border border-transparent hover:border-gray-200 rounded cursor-pointer w-full text-left"
                                    onClick={() => {
                                      const jsDay = day.getDay();
                                      const dayOfWeek = jsDay;
                                      const noteData =
                                        weeklyLog.notes_by_day[
                                          dayOfWeek.toString()
                                        ];
                                      let noteContent = '';

                                      if (noteData) {
                                        if (typeof noteData === 'string') {
                                          noteContent = noteData;
                                        } else if (noteData.note) {
                                          noteContent = noteData.note;
                                        }
                                      }

                                      startEditing(dateKey, noteContent);
                                    }}
                                  >
                                    {(() => {
                                      if (
                                        !weeklyLog.notes_by_day ||
                                        Object.keys(weeklyLog.notes_by_day)
                                          .length === 0
                                      ) {
                                        console.log(
                                          `No notes_by_day data for ${dateKey}`
                                        );
                                        return (
                                          <span className="text-[#AB8B3B] font-medium">
                                            Click to add notes
                                          </span>
                                        );
                                      }

                                      const jsDay = day.getDay();
                                      const dayOfWeek = jsDay;
                                      const noteData =
                                        weeklyLog.notes_by_day[
                                          dayOfWeek.toString()
                                        ];
                                      console.log(
                                        `Note for day ${dayOfWeek} (${dateKey}):`,
                                        noteData
                                      );

                                      if (!noteData) {
                                        return (
                                          <span className="text-[#AB8B3B] font-medium">
                                            Click to add notes
                                          </span>
                                        );
                                      } else if (typeof noteData === 'string') {
                                        return (
                                          noteData || (
                                            <span className="text-[#AB8B3B] font-medium">
                                              Click to add notes
                                            </span>
                                          )
                                        );
                                      } else if (noteData.note) {
                                        return (
                                          noteData.note || (
                                            <span className="text-[#AB8B3B] font-medium">
                                              Click to add notes
                                            </span>
                                          )
                                        );
                                      } else {
                                        return (
                                          <span className="text-[#AB8B3B] font-medium">
                                            Click to add notes
                                          </span>
                                        );
                                      }
                                    })()}
                                  </button>
                                ) : (
                                  <div className="text-sm text-gray-600 min-h-[40px] p-2">
                                    {(() => {
                                      if (
                                        !weeklyLog.notes_by_day ||
                                        Object.keys(weeklyLog.notes_by_day)
                                          .length === 0
                                      ) {
                                        console.log(
                                          `No notes_by_day data for ${dateKey}`
                                        );
                                        return 'No notes';
                                      }

                                      const jsDay = day.getDay();
                                      const dayOfWeek = jsDay;
                                      const noteData =
                                        weeklyLog.notes_by_day[
                                          dayOfWeek.toString()
                                        ];
                                      console.log(
                                        `Note for day ${dayOfWeek} (${dateKey}):`,
                                        noteData
                                      );

                                      if (!noteData) {
                                        return 'No notes';
                                      } else if (typeof noteData === 'string') {
                                        return noteData || 'No notes';
                                      } else if (noteData.note) {
                                        return noteData.note || 'No notes';
                                      } else {
                                        return 'No notes';
                                      }
                                    })()}
                                  </div>
                                )}
                              </TableCell>
                            </TableRow>
                          );
                        })}
                      </TableBody>
                    </Table>
                  </div>
                </CardContent>
              </Card>
            ) : projectId !== 'all' &&
              !loadingAvailableWeeks &&
              !loadingProjectDetails &&
              availableWeeks.length > 0 &&
              !selectedWeek ? (
              <Card>
                <CardContent>
                  <div className="text-center py-16">
                    <CalendarDays className="h-16 w-16 mx-auto text-gray-400 mb-6" />
                    <h3 className="text-xl font-semibold text-gray-800 mb-2">
                      Select a Week
                    </h3>
                    <p className="text-gray-500">
                      Please choose an available week from the calendar to view
                      its details.
                    </p>
                  </div>
                </CardContent>
              </Card>
            ) : projectId === 'all' &&
              !loadingAvailableWeeks &&
              availableWeeks.length === 0 ? (
              // Specific message for "All Projects" if no logs exist at all
              <Card>
                <CardContent>
                  <div className="text-center py-16">
                    <CalendarDays className="h-16 w-16 mx-auto text-gray-400 mb-6" />
                    <h3 className="text-xl font-semibold text-gray-800 mb-2">
                      No Weekly Logs
                    </h3>
                    <p className="text-gray-500">
                      There are no weekly logs available across all projects.
                    </p>
                  </div>
                </CardContent>
              </Card>
            ) : null /* Other states like initial loading or specific error messages are handled by earlier return statements */
          }
        </div>
      </div>
    </div>
  );
}
