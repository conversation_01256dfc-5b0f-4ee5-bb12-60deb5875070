import { useState } from 'react';
import { Card } from '@/components/ui/card';
import { format } from 'date-fns';
import { Edit, Check, X } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import { toast } from 'sonner';
import { weeklyLogApi } from '@/lib/api/weekly-log';
import { useToast } from '@/components/ui/use-toast';

interface Note {
  id: string;
  note: string;
  created_at: string;
  created_by: string;
}

interface WeeklyLogNoteProps {
  note: Note;
  canEdit?: boolean;
  weeklyLogId: string;
  dayNumber: number;
}

export function WeeklyLogNote({ note, canEdit = false, weeklyLogId, dayNumber }: WeeklyLogNoteProps) {
  const [isEditing, setIsEditing] = useState(false);
  const [noteText, setNoteText] = useState(note.note);
  const { toast: uiToast } = useToast();

  // Function to handle note changes
  const handleNoteChange = (value: string) => {
    setNoteText(value);
  };

  // Function to save notes to the backend
  const saveNoteToBackend = async () => {
    try {
      console.log('Saving note to backend:', { weeklyLogId, dayNumber, noteText });

      // Prepare the notes object
      const notes: Record<number, string> = {
        [dayNumber]: noteText
      };

      // Call the API
      console.log('Calling weeklyLogApi.updateNotes with:', { weeklyLogId, notes });
      const response = await weeklyLogApi.updateNotes(weeklyLogId, notes);
      console.log('API response:', response);

      if (response.success) {
        console.log('Note saved successfully, showing toast');

        // Try multiple toast methods to ensure one works
        toast.success('Note saved successfully');

        // Use the shadcn/ui toast
        uiToast({
          title: 'Success',
          description: 'Note saved successfully',
          variant: 'default',
        });

        // Force a direct alert for debugging
        setTimeout(() => {
          alert('Note saved successfully');
        }, 100);

        setIsEditing(false);
      } else {
        console.error('Failed to save note:', response);
        toast.error('Failed to save note');
        uiToast({
          title: 'Error',
          description: 'Failed to save note',
          variant: 'destructive',
        });
      }
    } catch (error) {
      console.error('Error saving note:', error);
      toast.error('Failed to save note');
      uiToast({
        title: 'Error',
        description: 'Failed to save note',
        variant: 'destructive',
      });
    }
  };

  // Function to handle the end of editing
  const handleEndEditing = (shouldSave: boolean = true) => {
    if (shouldSave) {
      saveNoteToBackend();
    } else {
      setNoteText(note.note); // Reset to original note
      setIsEditing(false);
    }
  };

  return (
    <Card className="p-3 relative group">
      <div className="space-y-2">
        {isEditing ? (
          <div>
            <Textarea
              autoFocus
              className="w-full min-h-[80px] mb-2"
              value={noteText}
              onChange={(e) => handleNoteChange(e.target.value)}
              onKeyDown={(e) => {
                if (e.key === 'Enter' && !e.shiftKey) {
                  e.preventDefault();
                  handleEndEditing(true);
                }
              }}
            />
            <div className="flex justify-end space-x-2">
              <button
                type="button"
                className="p-1 rounded-full bg-red-100 text-red-600 hover:bg-red-200"
                onClick={() => handleEndEditing(false)}
              >
                <X className="h-4 w-4" />
              </button>
              <button
                type="button"
                className="p-1 rounded-full bg-green-100 text-green-600 hover:bg-green-200"
                onClick={() => handleEndEditing(true)}
              >
                <Check className="h-4 w-4" />
              </button>
            </div>
          </div>
        ) : (
          <div className="flex justify-between items-start">
            <p className="text-sm pr-8">{note.note}</p>
            {canEdit && (
              <Button
                variant="ghost"
                size="sm"
                className="h-8 w-8 p-0 absolute top-2 right-2 opacity-100 hover:bg-gray-100"
                onClick={() => setIsEditing(true)}
              >
                <Edit className="h-4 w-4 text-[#AB8B3B]" />
              </Button>
            )}
          </div>
        )}
        <div className="text-xs text-gray-500">
          <p>Created on {format(new Date(note.created_at), 'MMM d, yyyy')}</p>
        </div>
      </div>
    </Card>
  );
}
