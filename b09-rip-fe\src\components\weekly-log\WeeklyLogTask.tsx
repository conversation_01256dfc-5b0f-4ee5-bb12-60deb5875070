import { Card } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { format } from 'date-fns';

interface Task {
  id: string;
  description: string;
  completion_status: string;
  initial_date: string;
  due_date: string;
  employee_name?: string;
  assigned_by_name?: string;
}

interface WeeklyLogTaskProps {
  task: Task;
}

const getStatusColor = (status: string) => {
  switch (status.toLowerCase()) {
    case 'completed':
      return 'bg-green-100 text-green-800';
    case 'on_progress':
      return 'bg-blue-100 text-blue-800';
    case 'not_completed':
      return 'bg-red-100 text-red-800';
    default:
      return 'bg-gray-100 text-gray-800';
  }
};

export function WeeklyLogTask({ task }: WeeklyLogTaskProps) {
  return (
    <Card className="p-3">
      <div className="space-y-2">
        <div className="flex items-start justify-between">
          <p className="text-sm font-medium">{task.description}</p>
          <Badge className={getStatusColor(task.completion_status)}>
            {task.completion_status.replace('_', ' ')}
          </Badge>
        </div>
        
        <div className="text-xs text-gray-500 space-y-1">
          <p>
            <span className="font-medium">Assigned to:</span> {task.employee_name || 'Unassigned'}
          </p>
          <p>
            <span className="font-medium">Assigned by:</span> {task.assigned_by_name || 'Unknown'}
          </p>
          <p>
            <span className="font-medium">Due:</span> {format(new Date(task.due_date), 'MMM d, yyyy')}
          </p>
        </div>
      </div>
    </Card>
  );
} 