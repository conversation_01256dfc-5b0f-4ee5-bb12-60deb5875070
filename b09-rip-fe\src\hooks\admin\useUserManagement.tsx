import { useState, useEffect } from 'react';
import { toast } from 'sonner';
import { UserWithProfile } from '@/types/admin';
import { UserFilterParams, UserRole } from '@/types/admin';
import { adminApi } from '@/lib/api/admin';
import { organizationApi } from '@/lib/api/organization';
import { Organization } from '@/types/organization';
import { ApiError } from '@/types/api';

export const useUserManagement = () => {
  const [users, setUsers] = useState<UserWithProfile[]>([]);
  const [selectedUsers, setSelectedUsers] = useState<string[]>([]);
  const [loading, setLoading] = useState(true);
  const [actionLoading, setActionLoading] = useState(false);

  // Pagination state
  const [totalPages, setTotalPages] = useState(1);
  const [currentPage, setCurrentPage] = useState(1);
  const [totalItems, setTotalItems] = useState(0);

  // Filter state
  const [search, setSearch] = useState('');
  const [role, setRole] = useState<string | undefined>(undefined);

  // Dialog state
  const [activateDialogOpen, setActivateDialogOpen] = useState(false);
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [selectedUser, setSelectedUser] = useState<UserWithProfile | null>(
    null
  );
  const [bulkActivateDialogOpen, setBulkActivateDialogOpen] = useState(false);
  const [bulkDeleteDialogOpen, setBulkDeleteDialogOpen] = useState(false);

  // Organizations state
  const [organizations, setOrganizations] = useState<Organization[]>([]);
  const [loadingOrganizations, setLoadingOrganizations] = useState(false);

  // Load organizations
  useEffect(() => {
    const fetchOrganizations = async () => {
      setLoadingOrganizations(true);
      try {
        const response = await organizationApi.getOrganizations({
          page: 1,
          pageSize: 1000, // Get all organizations
        });

        if (response.success && response.data) {
          setOrganizations(response.data.items);
        } else {
          toast.error('Failed to load organizations');
        }
      } catch (error: unknown) {
        console.error('Error fetching organizations:', error);
        toast.error('Failed to load organizations');
      } finally {
        setLoadingOrganizations(false);
      }
    };

    fetchOrganizations();
  }, []);

  // Check if ANY selected users have the Client role
  const hasClientUsersInSelection = () => {
    return users.some(
      (user) =>
        selectedUsers.includes(user.profile.id) &&
        user.profile.role === 'Client'
    );
  };

  // Check if ALL selected users are unactivated
  const allSelectedUsersUnactivated = () => {
    const selectedUserProfiles = users.filter((user) =>
      selectedUsers.includes(user.profile.id)
    );
    return (
      selectedUserProfiles.length > 0 &&
      selectedUserProfiles.every((user) => !user.profile.is_active)
    );
  };

  // Load users on initial render and when filters/pagination change
  useEffect(() => {
    const fetchUsers = async () => {
      setLoading(true);
      try {
        const params: UserFilterParams = {
          page: currentPage,
          pageSize: 10,
          search: search || undefined,
          role: role as UserRole | undefined,
        };

        // Log the response structure to debug pagination issues
        const response = await adminApi.getUsers(params);
        console.log('User API response:', response);
        console.log('User pagination data:', response.data?.pagination);

        if (response.success && response.data) {
          // Check if items exists in the expected structure
          if (response.data.items && Array.isArray(response.data.items)) {
            setUsers(response.data.items);
          } else {
            console.error(
              'Unexpected data structure in user response:',
              response.data
            );
            setUsers([]);
          }

          // Ensure pagination data exists and has the expected properties
          if (response.data.pagination) {
            setTotalPages(response.data.pagination.pageCount || 1);
            setTotalItems(response.data.pagination.total || 0);
          } else {
            console.error('Missing pagination data in user response');
            setTotalPages(1);
            setTotalItems(0);
          }
        } else {
          toast.error('Failed to load users');
        }
      } catch (error: unknown) {
        console.error('Error fetching users:', error);

        // More specific error message based on error code
        const apiError = error as ApiError;
        if (apiError.response?.status === 401) {
          toast.error('Session expired. Please login again.');
        } else if (apiError.response?.status === 403) {
          toast.error('You do not have permission to view this page.');
        } else {
          toast.error('Failed to load users. Please try again later.');
        }
      } finally {
        setLoading(false);
      }
    };

    fetchUsers();
  }, [currentPage, search, role]);

  // Handle user selection
  const handleSelectUser = (id: string, checked: boolean) => {
    if (checked) {
      setSelectedUsers([...selectedUsers, id]);
    } else {
      setSelectedUsers(selectedUsers.filter((userId) => userId !== id));
    }
  };

  // Handle select all users
  const handleSelectAll = (checked: boolean) => {
    if (checked) {
      setSelectedUsers(users.map((user) => user.profile.id));
    } else {
      setSelectedUsers([]);
    }
  };

  // Open activation dialog
  const handleActivateClick = (user: UserWithProfile) => {
    setSelectedUser(user);
    setActivateDialogOpen(true);
  };

  // Open delete dialog
  const handleDeleteClick = (user: UserWithProfile) => {
    setSelectedUser(user);
    setDeleteDialogOpen(true);
  };

  // Handle bulk activate click
  const handleBulkActivateClick = () => {
    setBulkActivateDialogOpen(true);
  };

  // Handle bulk delete click
  const handleBulkDeleteClick = () => {
    setBulkDeleteDialogOpen(true);
  };

  // Handle cancel selection
  const handleCancelSelection = () => {
    setSelectedUsers([]);
  };

  // Perform bulk activation
  const handleBulkActivate = async (organizationId?: string) => {
    setActionLoading(true);
    try {
      // Get unactivated users from selection
      const unactivatedUsers = users.filter(
        (user) =>
          selectedUsers.includes(user.profile.id) && !user.profile.is_active
      );

      let successCount = 0;
      let errorCount = 0;

      // Activate each user sequentially
      for (const user of unactivatedUsers) {
        try {
          const response = await adminApi.activateUser({
            id: user.profile.id,
            // Only pass org_id for clients
            org_id: user.profile.role === 'Client' ? organizationId : undefined,
          });

          if (response.success) {
            successCount++;
          } else {
            errorCount++;
          }
        } catch (error) {
          errorCount++;
          console.error('Error activating user:', error);
        }
      }

      // Update UI
      if (successCount > 0) {
        toast.success(`${successCount} user berhasil diaktivasi`);

        // Update local state to reflect changes
        const updatedUsers = users.map((user) => {
          if (
            selectedUsers.includes(user.profile.id) &&
            !user.profile.is_active
          ) {
            return {
              ...user,
              profile: {
                ...user.profile,
                is_active: true,
              },
            };
          }
          return user;
        });

        setUsers(updatedUsers);
      }

      if (errorCount > 0) {
        toast.error(`${errorCount} user gagal diaktivasi`);
      }

      setBulkActivateDialogOpen(false);
      setSelectedUsers([]);
    } catch (error: unknown) {
      console.error('Error bulk activating users:', error);
      toast.error('Gagal mengaktivasi user');
    } finally {
      setActionLoading(false);
    }
  };

  // Perform bulk deletion
  const handleBulkDelete = async () => {
    setActionLoading(true);
    try {
      let successCount = 0;
      let errorCount = 0;

      // Delete each user sequentially
      for (const id of selectedUsers) {
        try {
          const response = await adminApi.deleteUser(id);
          if (response.success) {
            successCount++;
          } else {
            errorCount++;
          }
        } catch (error) {
          errorCount++;
          console.error('Error deleting user:', error);
        }
      }

      // Update UI
      if (successCount > 0) {
        toast.success(`${successCount} user berhasil dihapus`);

        // Remove deleted users from local state
        const remainingUsers = users.filter(
          (user) => !selectedUsers.includes(user.profile.id)
        );
        setUsers(remainingUsers);
        setTotalItems((prev) => prev - successCount);
      }

      if (errorCount > 0) {
        toast.error(`${errorCount} user gagal dihapus`);
      }

      setBulkDeleteDialogOpen(false);
      setSelectedUsers([]);
    } catch (error: unknown) {
      console.error('Error bulk deleting users:', error);
      toast.error('Gagal menghapus user');
    } finally {
      setActionLoading(false);
    }
  };

  // Handle single user activation
  const handleActivate = async (organizationId?: string) => {
    if (!selectedUser) return;

    setActionLoading(true);
    try {
      const response = await adminApi.activateUser({
        id: selectedUser.profile.id,
        // Only pass org_id for clients
        org_id:
          selectedUser.profile.role === 'Client' ? organizationId : undefined,
      });

      if (response.success) {
        toast.success('User berhasil diaktivasi');

        // Update the user in local state
        const updatedUsers = users.map((user) => {
          if (user.profile.id === selectedUser.profile.id) {
            return {
              ...user,
              profile: {
                ...user.profile,
                is_active: true,
              },
            };
          }
          return user;
        });

        setUsers(updatedUsers);
      } else {
        toast.error('Gagal mengaktivasi user');
      }

      setActivateDialogOpen(false);
      setSelectedUser(null);
    } catch (error: unknown) {
      console.error('Error activating user:', error);
      toast.error('Gagal mengaktivasi user');
    } finally {
      setActionLoading(false);
    }
  };

  // Handle single user deletion
  const handleDelete = async () => {
    if (!selectedUser) return;

    setActionLoading(true);
    try {
      const response = await adminApi.deleteUser(selectedUser.profile.id);

      if (response.success) {
        toast.success('User berhasil dihapus');

        // Remove user from local state
        const updatedUsers = users.filter(
          (user) => user.profile.id !== selectedUser.profile.id
        );
        setUsers(updatedUsers);
        setTotalItems((prev) => prev - 1);
      } else {
        toast.error('Gagal menghapus user');
      }

      setDeleteDialogOpen(false);
      setSelectedUser(null);
    } catch (error: unknown) {
      console.error('Error deleting user:', error);
      toast.error('Gagal menghapus user');
    } finally {
      setActionLoading(false);
    }
  };

  // Filter handlers
  const handleSearchChange = (value: string) => {
    setSearch(value);
    setCurrentPage(1); // Reset to first page when search changes
  };

  const handleRoleChange = (value: string | undefined) => {
    setRole(value);
    setCurrentPage(1); // Reset to first page when filter changes
  };

  return {
    users,
    selectedUsers,
    loading,
    actionLoading,
    totalPages,
    totalItems,
    currentPage,
    search,
    role,
    organizations,
    loadingOrganizations,
    activateDialogOpen,
    deleteDialogOpen,
    selectedUser,
    bulkActivateDialogOpen,
    bulkDeleteDialogOpen,
    hasClientUsersInSelection,
    allSelectedUsersUnactivated,
    setCurrentPage,
    setSearch,
    setRole,
    handleSelectUser,
    handleSelectAll,
    handleActivateClick,
    handleDeleteClick,
    handleBulkActivateClick,
    handleBulkDeleteClick,
    handleCancelSelection,
    handleBulkActivate,
    handleBulkDelete,
    handleActivate,
    handleDelete,
    setActivateDialogOpen,
    setDeleteDialogOpen,
    setBulkActivateDialogOpen,
    setBulkDeleteDialogOpen,
    handleSearchChange,
    handleRoleChange,
  };
};
