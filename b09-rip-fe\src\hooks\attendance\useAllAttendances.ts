import { SortDirection } from '@/components/ui/data-table';
import { useAttendanceManagement } from '../useAttendanceManagement';

export function useAllAttendances() {
  // Use the hook-based implementation with "all attendances" mode initialized
  const {
    attendances,
    loading,
    search,
    status,
    dateRange,
    sortField,
    sortDirection,
    handleSearchChange,
    handleStatusChange,
    handleDateRangeChange,
    handleSort,
  } = useAttendanceManagement({ initialIsMyAttendance: false });

  // Handler for sorting to match the component's expected interface
  const handleSorting = (field: string, direction: SortDirection) => {
    handleSort(field, direction);
  };

  return {
    // Data
    attendances,
    loading,

    // Filter state
    searchTerm: search,
    statusFilter: status,
    dateRangeFilter: dateRange,
    sortField,
    sortDirection,

    // Handlers
    handleSearchChange,
    handleStatusChange,
    handleDateRangeChange,
    handleSorting,
  };
}
