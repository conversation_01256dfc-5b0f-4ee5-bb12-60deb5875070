import { SortDirection } from '@/components/ui/data-table';
import { useAttendanceManagement } from '../useAttendanceManagement';

export function useMyAttendance() {
  // Use the new hook-based implementation instead of the Zustand store
  const {
    attendances,
    loading,
    search,
    status,
    dateRange,
    sortField,
    sortDirection,
    handleSearchChange,
    handleStatusChange,
    handleDateRangeChange,
    handleSort,
  } = useAttendanceManagement();

  // Handler for sorting to match the component's expected interface
  const handleSorting = (field: string, direction: SortDirection) => {
    handleSort(field, direction);
  };

  return {
    // Data
    attendances,
    loading,

    // Filter state
    searchTerm: search,
    statusFilter: status,
    dateRangeFilter: dateRange,
    sortField,
    sortDirection,

    // Handlers
    handleSearchChange,
    handleStatusChange,
    handleDateRangeChange,
    handleSorting,
  };
}
