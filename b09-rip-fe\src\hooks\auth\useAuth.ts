import { useState, useCallback } from 'react';
import { useRouter } from 'next/navigation';
import { authApi } from '@/lib/api/auth';
import { JWTManager } from '@/lib/auth/jwt';
import { useAuthStore } from '@/lib/store/auth-store';
import { authService } from '@/lib/auth/auth-service';
import {
  SignInRequest,
  SignUpRequest,
  ProfileResponse,
  UserProfile,
} from '@/types/auth';
import { toast } from 'sonner';
import { ApiError } from '@/types/api';

export function useAuth() {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [accountInactive, setAccountInactive] = useState(() => {
    // Initialize from localStorage if available (client-side only)
    if (typeof window !== 'undefined') {
      // Get the stored value
      const storedValue = localStorage.getItem('accountInactive') === 'true';

      // If the value is true, we'll verify it with an API call later via checkAuth/checkAccountActivation
      // But for initial rendering, return the stored value
      return storedValue;
    }
    return false;
  });
  const [profileIncomplete, setProfileIncomplete] = useState(false);
  const router = useRouter();
  const { login } = useAuthStore();

  // Helper function to set account inactive state and persist it
  const setAccountInactiveStatus = useCallback((status: boolean) => {
    setAccountInactive(status);
    if (typeof window !== 'undefined') {
      if (status) {
        localStorage.setItem('accountInactive', 'true');
      } else {
        localStorage.removeItem('accountInactive');
      }
    }
  }, []);

  /**
   * Helper function to extract and set the correct role from JWT
   */
  const getProfileWithCorrectRole = (profile: UserProfile): UserProfile => {
    const jwtPayload = JWTManager.getPayload();
    const roleFromJWT = jwtPayload?.user_metadata?.role;

    if (roleFromJWT && (!profile.role || profile.role !== roleFromJWT)) {
      return {
        ...profile,
        role: roleFromJWT,
      };
    }

    return profile;
  };

  /**
   * Helper function to handle authentication success and check profile status
   */
  const handleAuthSuccess = useCallback(
    (
      profile: UserProfile,
      access_token: string,
      refresh_token: string,
      responseData: ProfileResponse,
      autoRedirect: boolean = true
    ) => {
      // Update profile with correct role from JWT
      const updatedProfile = getProfileWithCorrectRole(profile);

      // Add profileComplete property to the profile from the response data
      // This ensures the property is stored with the user profile
      const profileWithCompletionStatus = {
        ...updatedProfile,
        profileComplete: responseData.profileComplete,
      };

      // Check profile completion status
      const isProfileComplete = responseData.profileComplete !== false;
      setProfileIncomplete(!isProfileComplete);

      // Update tokens in centralized auth service
      authService.updateTokens(access_token, refresh_token);

      // Update auth store with profile that includes profileComplete
      login(profileWithCompletionStatus, access_token, refresh_token);

      // Only redirect if autoRedirect is true
      if (autoRedirect) {
        // Redirect based on profile completion
        if (!isProfileComplete) {
          router.push('/update-account');
        } else {
          router.push('/dashboard');
        }
      }

      return true;
    },
    [login, router, setProfileIncomplete]
  );

  /**
   * Sign in with email and password
   */
  const signIn = useCallback(
    async (
      credentials: SignInRequest,
      autoRedirect: boolean = true
    ): Promise<boolean> => {
      setLoading(true);
      setError(null);
      setAccountInactiveStatus(false);
      setProfileIncomplete(false);

      try {
        const response = await authApi.signIn(credentials);

        if (response.success) {
          const { session, profile } = response.data;
          const { access_token, refresh_token } = session;

          // Update tokens in centralized auth service
          authService.updateTokens(access_token, refresh_token);

          // Check if account is active by calling the profile endpoint
          try {
            const profileResponse = await authApi.getProfile();

            // If we get here, account is active
            return handleAuthSuccess(
              profile,
              access_token,
              refresh_token,
              profileResponse.data,
              autoRedirect
            );
          } catch (profileErr: unknown) {
            // Check if it's the inactive account error
            const apiError = profileErr as ApiError;
            if (
              apiError.response?.status === 403 &&
              apiError.response?.data?.error?.code === 'ACCOUNT_INACTIVE'
            ) {
              setAccountInactiveStatus(true);
              setError(
                'Akun Anda belum diaktivasi. Mohon tunggu admin untuk mengaktivasi akun Anda.'
              );

              // Don't remove the tokens so user doesn't have to log in again when activated
              return false;
            }

            // For other errors, log out and show error
            authService.logout();
            throw profileErr;
          }
        } else {
          setError(response.message || 'Login gagal');
          return false;
        }
      } catch (err: unknown) {
        // Improved error handling for different error types
        const apiError = err as ApiError;
        if (apiError.response) {
          // The request was made and the server responded with a status code
          // that falls out of the range of 2xx
          if (apiError.response.status === 401) {
            setError('Email atau password salah. Silakan coba lagi.');
          } else if (apiError.response.status === 429) {
            setError(
              'Terlalu banyak percobaan login. Silakan coba lagi nanti.'
            );
          } else {
            const message =
              apiError.response.data?.message || 'Terjadi kesalahan saat login';
            setError(message);
          }
        } else if (apiError.request) {
          // The request was made but no response was received
          setError('Tidak dapat terhubung ke server. Periksa koneksi Anda.');
        } else {
          // Something happened in setting up the request that triggered an Error
          setError('Terjadi kesalahan saat login');
        }

        return false;
      } finally {
        setLoading(false);
      }
    },
    [handleAuthSuccess, setAccountInactiveStatus]
  );

  /**
   * Register a new user
   */
  const signUp = useCallback(
    async (
      userData: SignUpRequest,
      autoRedirect: boolean = true
    ): Promise<boolean> => {
      setLoading(true);
      setError(null);
      // Clear any inactive account status when registering a new user
      setAccountInactiveStatus(false);

      try {
        const response = await authApi.signUp(userData);

        if (response.success) {
          // Clear inactive account flag in localStorage to prevent it from persisting
          if (typeof window !== 'undefined') {
            localStorage.removeItem('accountInactive');
          }

          // Only redirect if autoRedirect is true
          if (autoRedirect) {
            router.push('/?registered=true');
          }
          return true;
        } else {
          // Capture the error message from the unsuccessful response
          setError(response.message || 'Registrasi gagal');
          return false;
        }
      } catch (err: unknown) {
        // Improved error handling to better capture API error messages
        const apiError = err as ApiError;
        const errorMessage =
          apiError.response?.data?.message || // Direct message from API
          apiError.message || // Error object message
          'Terjadi kesalahan saat registrasi';

        setError(errorMessage);
        return false;
      } finally {
        setLoading(false);
      }
    },
    [router, setAccountInactiveStatus]
  );

  /**
   * Sign out the current user
   */
  const signOut = useCallback(async (): Promise<void> => {
    setLoading(true);

    try {
      // Try to call the sign-out API endpoint
      await authApi.signOut();
    } catch (err: unknown) {
      // Log error but continue with logout process regardless of API failure
      console.error('Error during sign out:', err);
    }

    // Use centralized auth service to logout
    authService.logout();

    // Remove account inactive flag
    setAccountInactiveStatus(false);

    // Reset states
    setProfileIncomplete(false);

    // Set loading to false before showing toast
    setLoading(false);

    // Show success toast notification with explicit top-right position
    toast.success('Berhasil keluar dari sistem', {
      position: 'top-right',
      duration: 4000,
      id: 'logout-toast', // Add an ID to prevent duplicate toasts
      style: {
        fontWeight: 'bold',
      },
    });

    // Add a delay before redirecting to ensure toast is visible
    setTimeout(() => {
      router.push('/');
    }, 1500);
  }, [router, setAccountInactiveStatus]);

  /**
   * Check if the user is authenticated
   */
  const checkAuth = useCallback(async (): Promise<boolean> => {
    // Check if token is valid
    if (!JWTManager.isAccessTokenValid()) {
      try {
        // Use centralized auth service to refresh tokens
        const refreshSuccess = await authService.refreshTokens();
        if (!refreshSuccess) {
          console.log('Token refresh failed during checkAuth');
          return false;
        }

        console.log('Token refresh successful during checkAuth');

        // Token refresh was successful, now check if account is active
        try {
          const profileResponse = await authApi.getProfile();
          const profileData = profileResponse.data;

          // Account is active, check profile completion
          const isProfileComplete = profileData.profileComplete !== false;
          setProfileIncomplete(!isProfileComplete);

          // Get tokens from auth service
          const accessToken = authService.getAccessToken() || '';
          const refreshToken = authService.getRefreshToken() || '';

          // Get profile from response
          const profile = profileData.profile || {};

          // Update profile with correct role from JWT and add profileComplete
          const updatedProfile = {
            ...getProfileWithCorrectRole(profile),
            profileComplete: profileData.profileComplete,
          };

          // Update auth store
          login(updatedProfile, accessToken, refreshToken);

          return true;
        } catch (profileErr: unknown) {
          const apiError = profileErr as ApiError;
          // Check if it's the inactive account error
          if (
            apiError.response?.status === 403 &&
            apiError.response?.data?.error?.code === 'ACCOUNT_INACTIVE'
          ) {
            setAccountInactiveStatus(true);
            setError(
              'Akun Anda belum diaktivasi. Mohon tunggu admin untuk mengaktivasi akun Anda.'
            );
            return false;
          }

          // For other errors, logout
          authService.logout();
          return false;
        }
      } catch (err: unknown) {
        console.error('Error in checkAuth:', err);
        // Logout if refresh fails
        authService.logout();
        return false;
      }
    }

    // If token is valid but we don't have user info, fetch it
    if (JWTManager.isAccessTokenValid() && !useAuthStore.getState().user) {
      try {
        const profileResponse = await authApi.getProfile();

        if (profileResponse.success) {
          const accessToken = authService.getAccessToken() || '';
          const refreshToken = authService.getRefreshToken() || '';
          const profileData = profileResponse.data;

          // Check profile completion status
          const isProfileComplete = profileData.profileComplete !== false;
          setProfileIncomplete(!isProfileComplete);

          // Update profile with correct role from JWT and add profileComplete
          const updatedProfile = {
            ...getProfileWithCorrectRole(profileData.profile),
            profileComplete: profileData.profileComplete,
          };

          login(updatedProfile, accessToken, refreshToken);
          return true;
        }
        return false;
      } catch (err: unknown) {
        const apiError = err as ApiError;
        // Check if it's the inactive account error
        if (
          apiError.response?.status === 403 &&
          apiError.response?.data?.error?.code === 'ACCOUNT_INACTIVE'
        ) {
          setAccountInactiveStatus(true);
          setError(
            'Akun Anda belum diaktivasi. Mohon tunggu admin untuk mengaktivasi akun Anda.'
          );
          return false;
        }
        return false;
      }
    }

    return useAuthStore.getState().isAuthenticated;
  }, [login, setAccountInactiveStatus, setError]);

  /**
   * Check if account is active by calling the profile endpoint
   */
  const checkAccountActivation = useCallback(async (): Promise<boolean> => {
    try {
      const profileResponse = await authApi.getProfile();

      // If this succeeds, account is active
      setAccountInactiveStatus(false);
      setError(null); // Clear any existing error messages

      // Since the account is now active, set up the session properly
      const accessToken = JWTManager.getAccessToken() || '';
      const refreshToken = JWTManager.getRefreshToken() || '';

      if (accessToken && refreshToken && profileResponse.success) {
        const profileData = profileResponse.data;

        // Check profile completion status
        const isProfileComplete = profileData.profileComplete !== false;
        setProfileIncomplete(!isProfileComplete);

        // Update profile with correct role from JWT and add profileComplete
        const updatedProfile = {
          ...getProfileWithCorrectRole(profileData.profile),
          profileComplete: profileData.profileComplete,
        };

        // Set up the full authenticated session
        login(updatedProfile, accessToken, refreshToken);
      }

      return true;
    } catch (err: unknown) {
      const apiError = err as ApiError;
      // Check if it's still the inactive account error
      if (
        apiError.response?.status === 403 &&
        apiError.response?.data?.error?.code === 'ACCOUNT_INACTIVE'
      ) {
        setAccountInactiveStatus(true);
        return false;
      }

      // For other errors, probably not authenticated or server issue
      return false;
    }
  }, [login, setAccountInactiveStatus, setError, setProfileIncomplete]);

  return {
    signIn,
    signUp,
    signOut,
    checkAuth,
    checkAccountActivation,
    loading,
    error,
    accountInactive,
    profileIncomplete,
    isAuthenticated: useAuthStore.getState().isAuthenticated,
    user: useAuthStore.getState().user,
  };
}

export default useAuth;
