import { useState, useEffect, useCallback } from 'react';
import { toast } from 'sonner';
import { useRouter } from 'next/navigation';
import { attendanceApi } from '@/lib/api/attendance';
import {
  Attendance,
  PresenceStatus,
  EmployeeAttendancePostParams,
  AttendanceQueryParams,
} from '@/types/attendance';
import { DateRange } from 'react-day-picker';
import { format } from 'date-fns';
import { SortDirection } from '@/components/ui/data-table';

// Add initialization props to allow for different views
type AttendanceManagementProps = {
  initialIsMyAttendance?: boolean;
  initialEmployeeId?: string;
};

export const useAttendanceManagement = (props?: AttendanceManagementProps) => {
  const router = useRouter();
  const [attendances, setAttendances] = useState<Attendance[]>([]);
  const [loading, setLoading] = useState(true);

  // Filter state
  const [search, setSearch] = useState('');
  const [status, setStatus] = useState<PresenceStatus | undefined>(undefined);
  const [dateRange, setDateRange] = useState<DateRange | undefined>(undefined);

  // Initialize with props if provided
  const [employeeId, setEmployeeId] = useState<string | undefined>(
    props?.initialEmployeeId
  );
  const [isMyAttendance, setIsMyAttendance] = useState(
    props?.initialIsMyAttendance !== undefined
      ? props.initialIsMyAttendance
      : true
  );

  // Sort state
  const [sortField, setSortField] = useState<string>('date');
  const [sortDirection, setSortDirection] = useState<SortDirection>('desc');

  // Load attendances on initial render and when filters change
  useEffect(() => {
    const fetchAttendances = async () => {
      setLoading(true);
      try {
        // Prepare common params
        const params: AttendanceQueryParams = {
          search: search || undefined,
          status,
        };

        // Add date range params if available
        if (dateRange?.from && dateRange?.to) {
          params.fromDate = format(dateRange.from, 'yyyy-MM-dd');
          params.toDate = format(dateRange.to, 'yyyy-MM-dd');
        }

        let response;

        // Choose the appropriate API endpoint based on the view type
        if (employeeId) {
          // If we have an employeeId, use the employee-specific endpoint
          console.log('Fetching employee-specific attendances:', employeeId);
          const postParams: EmployeeAttendancePostParams = {
            ...params,
            employee_id: employeeId,
          };
          response = await attendanceApi.postEmployeeAttendance(postParams);
        } else if (isMyAttendance) {
          // If this is "my attendance" view, use the my attendance endpoint
          console.log('Fetching my attendances');
          response = await attendanceApi.getMyAttendanceHistory(params);
        } else {
          // Otherwise use the general attendance endpoint
          console.log('Fetching all attendances');
          response = await attendanceApi.getAttendances(params);
        }

        console.log('Attendance API response:', response);

        if (response.success) {
          // Check if data exists in the expected structure
          if (response.data?.data && Array.isArray(response.data.data)) {
            setAttendances(response.data.data);
          } else {
            console.error(
              'Unexpected data structure in attendance response:',
              response.data
            );
            setAttendances([]);
          }
        } else {
          toast.error('Failed to load attendances');
        }
      } catch (error: unknown) {
        console.error('Error fetching attendances:', error);
        toast.error('Failed to load attendances. Please try again later.');
      } finally {
        setLoading(false);
      }
    };

    fetchAttendances();
  }, [search, status, dateRange, employeeId, isMyAttendance]);

  // Memoize handlers to prevent recreating functions on each render
  const handleViewAttendance = useCallback(
    (attendance: Attendance) => {
      router.push(`/attendance/${attendance.id}`);
    },
    [router]
  );

  const handleEditAttendance = useCallback(
    (attendance: Attendance) => {
      router.push(`/attendance/${attendance.id}/edit`);
    },
    [router]
  );

  const handleSearchChange = useCallback((value: string) => {
    setSearch(value);
  }, []);

  const handleStatusChange = useCallback(
    (value: PresenceStatus | undefined) => {
      setStatus(value);
    },
    []
  );

  const handleDateRangeChange = useCallback((range: DateRange | undefined) => {
    setDateRange(range);
  }, []);

  const handleSort = useCallback((field: string, direction: SortDirection) => {
    setSortField(field);
    setSortDirection(direction);
    // Sorting would trigger API call when implemented on backend
  }, []);

  const handleSetEmployeeId = useCallback((id: string | undefined) => {
    setEmployeeId(id);
  }, []);

  const toggleMyAttendances = useCallback(() => {
    setIsMyAttendance((prev) => !prev);
  }, []);

  return {
    attendances,
    loading,
    search,
    status,
    dateRange,
    employeeId,
    isMyAttendance,
    sortField,
    sortDirection,
    handleViewAttendance,
    handleEditAttendance,
    handleSearchChange,
    handleStatusChange,
    handleDateRangeChange,
    handleSort,
    handleSetEmployeeId,
    toggleMyAttendances,
  };
};
