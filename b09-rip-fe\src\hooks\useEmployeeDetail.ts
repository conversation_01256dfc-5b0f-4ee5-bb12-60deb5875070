//path: src/hooks/useEmployeeDetail.ts
import { useState, useEffect } from 'react';
import { toast } from 'sonner';
import { useRouter } from 'next/navigation';
import { Employee, UpdateEmployeeDto } from '@/types/employee';
import { getEmployeeById, updateEmployee } from '@/lib/api/employee';
import { ApiError } from '@/types/api';

export const useEmployeeDetail = (id: string) => {
  const router = useRouter();
  const [employee, setEmployee] = useState<Employee | null>(null);
  const [loading, setLoading] = useState(true);
  const [updating, setUpdating] = useState(false);

  // Fetch employee details
  useEffect(() => {
    const fetchEmployeeDetail = async () => {
      setLoading(true);
      try {
        const response = await getEmployeeById(id);

        if (response.success && response.data) {
          setEmployee(response.data);
        } else {
          toast.error(response.message || 'Failed to load employee details');
        }
      } catch (error: unknown) {
        console.error('Error fetching employee details:', error);

        const apiError = error as ApiError;
        if (apiError.response?.status === 404) {
          toast.error('Employee not found');
          router.push('/employee');
        } else if (apiError.response?.status === 401) {
          toast.error('Session expired. Please login again.');
        } else if (apiError.response?.status === 403) {
          toast.error('You do not have permission to view this page.');
        } else {
          toast.error(
            'Failed to load employee details. Please try again later.'
          );
        }
      } finally {
        setLoading(false);
      }
    };

    if (id) {
      fetchEmployeeDetail();
    }
  }, [id, router]);

  // Handle save employee changes
  const handleSaveChanges = async (data: UpdateEmployeeDto) => {
    if (!employee) return false;

    setUpdating(true);
    try {
      console.log('Updating employee with data:', data);

      const response = await updateEmployee(id, data);

      if (response.success && response.data) {
        // Create a safe merged object that ensures no undefined values
        const updatedEmployee = {
          ...employee, // Start with all existing employee data
          ...response.data, // Override with new data from response
          // Ensure profile data is preserved and not undefined
          profile: {
            ...employee.profile, // Keep all existing profile data
            ...(response.data.profile || {}), // Override with any new profile data if available
          },
          // Ensure these fields are never undefined (provide empty string as fallback)
          dob: response.data.dob || employee.dob || '',
          address: response.data.address || employee.address || '',
          bank_account:
            response.data.bank_account || employee.bank_account || '',
          bank_name: response.data.bank_name || employee.bank_name || '',
          email: response.data.email || employee.email || '',
          employment_status:
            response.data.employment_status || employee.employment_status,
          presence_status:
            response.data.presence_status || employee.presence_status,
          department: response.data.department || employee.department,
          start_date: response.data.start_date || employee.start_date || '',
        };

        setEmployee(updatedEmployee);
        // Remove toast from hook to prevent duplicates
        return true;
      } else {
        console.error('API returned success: false', response);
        toast.error(response.message || 'Failed to update employee');
        return false;
      }
    } catch (error: unknown) {
      console.error('Error updating employee:', error);

      // Handle specific error cases
      const apiError = error as ApiError;
      if (apiError.response) {
        console.error('Response data:', apiError.response.data);

        if (apiError.response.status === 400) {
          toast.error('Invalid data. Please check your inputs and try again.');
        } else if (apiError.response.status === 401) {
          toast.error('Session expired. Please login again.');
        } else if (apiError.response.status === 403) {
          toast.error('You do not have permission to update this employee.');
        } else if (apiError.response.status === 404) {
          toast.error('Employee not found.');
        } else if (apiError.response.status === 422) {
          const validationErrors = apiError.response.data?.error?.details || {};
          const errorMessage = Object.values(validationErrors)
            .flat()
            .join(', ');
          toast.error(
            `Validation error: ${errorMessage || 'Please check your input data'}`
          );
        } else {
          toast.error(
            `Server error (${apiError.response.status}): Please try again later.`
          );
        }
      } else {
        toast.error(
          'Network error. Please check your connection and try again.'
        );
      }
      return false;
    } finally {
      setUpdating(false);
    }
  };

  return {
    employee,
    loading,
    updating,
    setEmployee,
    handleSaveChanges,
  };
};
