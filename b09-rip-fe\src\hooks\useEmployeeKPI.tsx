// path: b09-rip-fe/src/hooks/useEmployeeKPI.tsx
import { useState, useEffect, useCallback } from 'react';
import { toast } from 'sonner';
import { useRouter } from 'next/navigation';
import { KPI, KPIFilterParams, KPIStatus } from '@/types/kpi';
import { kpiApi } from '@/lib/api/kpi';
import { ApiError } from '@/types/api';
import api from '@/lib/api/client';

interface EmployeeProfile {
  id: string;
  fullname: string;
  role: string;
  employee_id: string;
}

export const useEmployeeKPI = (employeeId: string) => {
  const router = useRouter();
  const [kpis, setKpis] = useState<KPI[]>([]);
  const [loading, setLoading] = useState(true);
  const [employeeProfile, setEmployeeProfile] = useState<EmployeeProfile | null>(null);
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);

  // Filter state
  const [search, setSearch] = useState('');
  const [period, setPeriod] = useState<string | undefined>(undefined);
  const [status, setStatus] = useState<KPIStatus | undefined>(undefined);

  // Fetch employee profile
  const fetchEmployeeProfile = useCallback(async () => {
    if (!employeeId) return;

    try {
      const response = await api.get('/v1/admin/users/');

      // Debug: Log the API response
      console.log('API Response:', response.data);

      if (response.data?.success && Array.isArray(response.data.data.items)) {
        const users = response.data.data.items;

        // Debug: Log all users
        console.log('All users:', users);

        const userWithMatchingEmployeeId = users.find(
          (user: { profile: { employee_id: string; }; }) => {
            // Debug: Log each user's employee_id for comparison
            console.log('Comparing:', user.profile?.employee_id, 'with', employeeId);
            return user.profile?.employee_id === employeeId;
          }
        );

        // Debug: Log the matched user
        console.log('Matched user:', userWithMatchingEmployeeId);

        if (userWithMatchingEmployeeId?.profile) {
          const profile = {
            id: userWithMatchingEmployeeId.id,
            fullname: userWithMatchingEmployeeId.profile.fullname,
            role: userWithMatchingEmployeeId.profile.role,
            employee_id: userWithMatchingEmployeeId.profile.employee_id
          };

          // Debug: Log the profile being set
          console.log('Setting profile:', profile);

          setEmployeeProfile(profile);
        } else {
          console.warn('No matching employee profile found for ID:', employeeId);
        }
      } else {
        console.warn('Invalid API response structure:', response.data);
      }
    } catch (error) {
      console.error('Error fetching employee profile:', error);
    }
  }, [employeeId]);

  // Load KPIs on initial render and when filters change
  useEffect(() => {
    const fetchKPIs = async () => {
      if (!employeeId) return;

      setLoading(true);
      try {
        const params: KPIFilterParams = {
          page: currentPage,
          pageSize: 10,
          search: search || undefined,
          period: period === 'all' ? undefined : period,
          status: status === undefined ? undefined : status
        };

        const response = await kpiApi.getKPIsByEmployeeId(employeeId, params);

        if (response.success && response.data) {
          if (response.data.items && Array.isArray(response.data.items)) {
            setKpis(response.data.items);

            // Set pagination info
            if (response.data.pagination) {
              setTotalPages(response.data.pagination.pageCount || 1);
            }
          } else {
            console.error(
              'Unexpected data structure in KPI response:',
              response.data
            );
            setKpis([]);
          }
        } else {
          toast.error('Failed to load KPIs');
        }
      } catch (error: unknown) {
        console.error('Error fetching KPIs:', error);

        // More specific error message based on error code
        const apiError = error as ApiError;
        if (apiError.response?.status === 401) {
          toast.error('Session expired. Please login again.');
        } else if (apiError.response?.status === 403) {
          toast.error('You do not have permission to view this page.');
        } else {
          toast.error('Failed to load KPIs. Please try again later.');
        }
      } finally {
        setLoading(false);
      }
    };

    fetchKPIs();
    fetchEmployeeProfile();
  }, [employeeId, search, period, status, currentPage, fetchEmployeeProfile]);

  // Handle view KPI detail
  const handleViewDetail = (kpi: KPI) => {
    router.push(`/employee/kpi/${kpi.id}`);
  };

  // Filter handlers
  const handleSearchChange = (value: string) => {
    setSearch(value);
    setCurrentPage(1);
  };

  const handlePeriodChange = (value: string | undefined) => {
    setPeriod(value);
    setCurrentPage(1);
  };

  const handleStatusChange = (value: KPIStatus | undefined) => {
    setStatus(value);
    setCurrentPage(1);
  };

  const handlePageChange = (page: number) => {
    setCurrentPage(page);
  };

  // Handle back button to all KPIs
  const handleBackToAll = () => {
    router.push('/employee/kpi');
  };

  return {
    kpis,
    loading,
    search,
    period,
    status,
    employeeProfile,
    currentPage,
    totalPages,
    handleViewDetail,
    handleSearchChange,
    handlePeriodChange,
    handleStatusChange,
    handlePageChange,
    handleBackToAll
  };
};

export default useEmployeeKPI;
