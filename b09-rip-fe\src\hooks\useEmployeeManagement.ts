import { useState, useEffect, useCallback } from 'react';
import { useSearchParams } from 'next/navigation';
import { toast } from 'sonner';
import {
  Employee,
  Department,
  EmploymentStatus,
  PresenceStatus,
} from '@/types/employee';
import { getEmployees } from '@/lib/api/employee';

export function useEmployeeManagement() {
  const searchParams = useSearchParams();
  const [employees, setEmployees] = useState<Employee[]>([]);
  const [loading, setLoading] = useState(true);
  const [totalPages, setTotalPages] = useState(1);
  const [totalItems, setTotalItems] = useState(0);
  const [currentPage, setCurrentPage] = useState(1);
  const [search, setSearch] = useState(searchParams.get('search') || '');

  // Map the URL parameter to enum value if it's valid
  const mapToEnum = <T extends Record<string, unknown>>(
    value: string | null,
    enumObj: T
  ): T[keyof T] | undefined => {
    if (!value) return undefined;
    return Object.values(enumObj).includes(value as T[keyof T])
      ? (value as T[keyof T])
      : undefined;
  };

  const [department, setDepartment] = useState<Department | undefined>(
    mapToEnum(searchParams.get('department'), Department)
  );

  const [employmentStatus, setEmploymentStatus] = useState<
    EmploymentStatus | undefined
  >(mapToEnum(searchParams.get('employment_status'), EmploymentStatus));

  const [presenceStatus, setPresenceStatus] = useState<
    PresenceStatus | undefined
  >(mapToEnum(searchParams.get('presence_status'), PresenceStatus));

  const fetchEmployees = useCallback(async () => {
    try {
      setLoading(true);
      console.log('Fetching employees with filters:', {
        search,
        department,
        employment_status: employmentStatus,
        presence_status: presenceStatus,
        page: currentPage,
      });

      // Log the response structure to debug pagination issues
      const response = await getEmployees({
        search,
        department,
        employment_status: employmentStatus,
        presence_status: presenceStatus,
        page: currentPage,
        pageSize: 10,
      });

      console.log('Employee API response:', response);
      console.log('Employee pagination data:', response.data?.pagination);

      if (response.success) {
        // Check if items exists in the expected structure
        if (response.data.items && Array.isArray(response.data.items)) {
          setEmployees(response.data.items);
        } else {
          console.error(
            'Unexpected data structure in employee response:',
            response.data
          );
          setEmployees([]);
        }

        // Ensure pagination data exists and has the expected properties
        if (response.data.pagination) {
          setTotalItems(response.data.pagination.total || 0);
          // Calculate total pages based on total items and page size
          const pageSize = response.data.pagination.pageSize || 10;
          const calculatedTotalPages = Math.ceil(
            (response.data.pagination.total || 0) / pageSize
          );
          setTotalPages(calculatedTotalPages || 1);
        } else {
          console.error('Missing pagination data in employee response');
          setTotalItems(0);
          setTotalPages(1);
        }
      } else {
        toast.error(response.message || 'Failed to fetch employees');
      }
    } catch (error: unknown) {
      toast.error('Failed to fetch employees');
      console.error('Error fetching employees:', error);
    } finally {
      setLoading(false);
    }
  }, [search, department, employmentStatus, presenceStatus, currentPage]);

  useEffect(() => {
    fetchEmployees();
  }, [fetchEmployees]);

  const handleSearchChange = (value: string) => {
    setSearch(value);
    setCurrentPage(1);
  };

  const handleDepartmentChange = (value: Department | undefined) => {
    setDepartment(value);
    setCurrentPage(1);
  };

  const handleEmploymentStatusChange = (
    value: EmploymentStatus | undefined
  ) => {
    setEmploymentStatus(value);
    setCurrentPage(1);
  };

  const handlePresenceStatusChange = (value: PresenceStatus | undefined) => {
    setPresenceStatus(value);
    setCurrentPage(1);
  };

  const handleViewDetail = (id: string) => {
    window.location.href = `/employee/${id}`;
  };

  return {
    employees,
    loading,
    totalPages,
    totalItems,
    currentPage,
    search,
    department,
    employmentStatus,
    presenceStatus,
    handleSearchChange,
    handleDepartmentChange,
    handleEmploymentStatusChange,
    handlePresenceStatusChange,
    handleViewDetail,
    setCurrentPage,
  };
}
