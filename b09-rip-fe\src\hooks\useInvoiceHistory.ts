import { useState, useEffect } from 'react';
import { invoiceApi } from '@/lib/api/invoice';
import { InvoiceHistoryRecord } from '@/types/invoice';

export function useInvoiceHistory(invoiceId: string) {
  const [history, setHistory] = useState<InvoiceHistoryRecord[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchInvoiceHistory = async () => {
      try {
        setLoading(true);
        const response = await invoiceApi.getInvoiceHistory(invoiceId);
        
        if (response.success) {
          setHistory(response.data.items || []);
        } else {
          setError(response.message || 'Failed to fetch invoice history');
        }
      } catch (err) {
        console.error('Error fetching invoice history:', err);
        setError(err instanceof Error ? err.message : 'An unknown error occurred');
      } finally {
        setLoading(false);
      }
    };

    fetchInvoiceHistory();
  }, [invoiceId]);

  return { history, loading, error };
}
