import { useState, useEffect } from 'react';
import { toast } from 'sonner';
import { useRouter } from 'next/navigation';
import { Invoice, PaymentStatus } from '@/types/invoice';
import { invoiceApi } from '@/lib/api/invoice';
import { SortDirection } from '@/components/ui/data-table';

export const useInvoiceManagement = () => {
  const router = useRouter();
  const [invoices, setInvoices] = useState<Invoice[]>([]);
  const [loading, setLoading] = useState(true);

  // Filter state
  const [search, setSearch] = useState('');
  const [paymentStatus, setPaymentStatus] = useState<PaymentStatus | undefined>(
    undefined
  );

  // Sort state
  const [sortField, setSortField] = useState<string | undefined>(undefined);
  const [sortDirection, setSortDirection] = useState<SortDirection>(null);

  // Load invoices on initial render and when filters change
  useEffect(() => {
    const fetchInvoices = async () => {
      setLoading(true);
      try {
        const params = {
          search: search || undefined,
          payment_status: paymentStatus,
          // Add sort params when implemented on the API
        };

        const response = await invoiceApi.getInvoices(params);

        if (response.success && response.data) {
          // Check if items exists in the expected structure
          if (response.data.items && Array.isArray(response.data.items)) {
            setInvoices(response.data.items);
          } else {
            console.error(
              'Unexpected data structure in invoice response:',
              response.data
            );
            setInvoices([]);
          }
        } else {
          toast.error('Failed to load invoices');
        }
      } catch (error: unknown) {
        console.error('Error fetching invoices:', error);
        toast.error('Failed to load invoices. Please try again later.');
      } finally {
        setLoading(false);
      }
    };

    fetchInvoices();
  }, [search, paymentStatus]);

  // Action handlers
  const handleViewInvoice = (invoice: Invoice) => {
    router.push(`/invoice/${invoice.id}`);
  };

  const handleEditInvoice = (invoice: Invoice) => {
    router.push(`/invoice/${invoice.id}/edit`);
  };

  const handleSearchChange = (value: string | undefined) => {
    setSearch(value || '');
  };

  const handleStatusChange = (value: string | undefined) => {
    setPaymentStatus(value as PaymentStatus | undefined);
  };

  const handleSort = (field: string, direction: SortDirection) => {
    setSortField(field);
    setSortDirection(direction);
    // Sorting would trigger API call when implemented on backend
  };

  return {
    invoices,
    loading,
    search,
    paymentStatus,
    sortField,
    sortDirection,
    handleViewInvoice,
    handleEditInvoice,
    handleSearchChange,
    handleStatusChange,
    handleSort,
  };
};
