import { useState, useEffect } from 'react';
import { toast } from 'sonner';
import { useRouter } from 'next/navigation';
import {
  KPI,
  UpdateKPIRequest,
  UpdateKPIStatusRequest,
  UpdateKPIBonusRequest,
  KPIStatus,
} from '@/types/kpi';
import { kpiApi } from '@/lib/api/kpi';
import { ApiError } from '@/types/api';

export const useKPIDetail = (id: string) => {
  const router = useRouter();
  const [kpi, setKpi] = useState<KPI | null>(null);
  const [loading, setLoading] = useState(true);
  const [updateLoading, setUpdateLoading] = useState(false);
  const [deleteLoading, setDeleteLoading] = useState(false);
  const [statusUpdateLoading, setStatusUpdateLoading] = useState(false);
  const [bonusUpdateLoading, setBonusUpdateLoading] = useState(false);

  // Load KPI details on initial render
  useEffect(() => {
    const fetchKPI = async () => {
      setLoading(true);
      try {
        const response = await kpiApi.getKPIById(id);

        if (response.success && response.data) {
          setKpi(response.data);
        } else {
          toast.error('Failed to load KPI details');
          router.push('/employee/kpi');
        }
      } catch (error: unknown) {
        console.error('Error fetching KPI details:', error);

        // More specific error message based on error code
        const apiError = error as ApiError;
        if (apiError.response?.status === 401) {
          toast.error('Session expired. Please login again.');
        } else if (apiError.response?.status === 403) {
          toast.error('You do not have permission to view this page.');
          router.push('/employee/kpi');
        } else if (apiError.response?.status === 404) {
          toast.error('KPI not found.');
          router.push('/employee/kpi');
        } else {
          toast.error('Failed to load KPI details. Please try again later.');
          router.push('/employee/kpi');
        }
      } finally {
        setLoading(false);
      }
    };

    if (id) {
      fetchKPI();
    }
  }, [id, router]);

  // Update KPI details
  const updateKPI = async (data: UpdateKPIRequest) => {
    if (!kpi) return;

    setUpdateLoading(true);
    try {
      const response = await kpiApi.updateKPI(kpi.id, data);

      if (response.success && response.data) {
        setKpi(response.data);
        toast.success('KPI updated successfully');
        return true;
      } else {
        toast.error('Failed to update KPI');
        return false;
      }
    } catch (error: unknown) {
      console.error('Error updating KPI:', error);

      const apiError = error as ApiError;
      if (apiError.response?.status === 401) {
        toast.error('Session expired. Please login again.');
      } else if (apiError.response?.status === 403) {
        toast.error('You do not have permission to update this KPI.');
      } else {
        toast.error('Failed to update KPI. Please try again later.');
      }
      return false;
    } finally {
      setUpdateLoading(false);
    }
  };

  // Delete KPI
  const deleteKPI = async () => {
    if (!kpi) return;

    setDeleteLoading(true);
    try {
      const response = await kpiApi.deleteKPI(kpi.id);

      if (response.success) {
        toast.success('KPI deleted successfully');
        router.push('/employee/kpi');
        return true;
      } else {
        toast.error('Failed to delete KPI');
        return false;
      }
    } catch (error: unknown) {
      console.error('Error deleting KPI:', error);

      const apiError = error as ApiError;
      if (apiError.response?.status === 401) {
        toast.error('Session expired. Please login again.');
      } else if (apiError.response?.status === 403) {
        toast.error('You do not have permission to delete this KPI.');
      } else {
        toast.error('Failed to delete KPI. Please try again later.');
      }
      return false;
    } finally {
      setDeleteLoading(false);
    }
  };

  // Update KPI status
  const updateStatus = async (status: KPIStatus) => {
    if (!kpi) return;

    setStatusUpdateLoading(true);
    try {
      const data: UpdateKPIStatusRequest = { status };
      const response = await kpiApi.updateKPIStatus(kpi.id, data);

      if (response.success && response.data) {
        setKpi(response.data);
        toast.success('KPI status updated successfully');
        return true;
      } else {
        toast.error('Failed to update KPI status');
        return false;
      }
    } catch (error: unknown) {
      console.error('Error updating KPI status:', error);

      const apiError = error as ApiError;
      if (apiError.response?.status === 401) {
        toast.error('Session expired. Please login again.');
      } else if (apiError.response?.status === 403) {
        toast.error('You do not have permission to update this KPI status.');
      } else {
        toast.error('Failed to update KPI status. Please try again later.');
      }
      return false;
    } finally {
      setStatusUpdateLoading(false);
    }
  };

  // Update KPI bonus
  const updateBonus = async (bonusReceived: number) => {
    if (!kpi) return;

    setBonusUpdateLoading(true);
    try {
      const data: UpdateKPIBonusRequest = { bonus_received: bonusReceived };
      const response = await kpiApi.updateKPIBonus(kpi.id, data);

      if (response.success && response.data) {
        setKpi(response.data);
        toast.success('KPI bonus updated successfully');
        return true;
      } else {
        toast.error('Failed to update KPI bonus');
        return false;
      }
    } catch (error: unknown) {
      console.error('Error updating KPI bonus:', error);

      const apiError = error as ApiError;
      if (apiError.response?.status === 401) {
        toast.error('Session expired. Please login again.');
      } else if (apiError.response?.status === 403) {
        toast.error('You do not have permission to update this KPI bonus.');
      } else {
        toast.error('Failed to update KPI bonus. Please try again later.');
      }
      return false;
    } finally {
      setBonusUpdateLoading(false);
    }
  };

  return {
    kpi,
    loading,
    updateLoading,
    deleteLoading,
    statusUpdateLoading,
    bonusUpdateLoading,
    updateKPI,
    deleteKPI,
    updateStatus,
    updateBonus,
  };
};
