// path: b09-rip-fe/src/hooks/useKPIManagement.tsx
import { useState, useEffect } from 'react';
import { toast } from 'sonner';
import { useRouter } from 'next/navigation';
import { KPI, KPIFilterParams, KPIStatus } from '@/types/kpi';
import { kpiApi } from '@/lib/api/kpi';
import { ApiError } from '@/types/api';

export const useKPIManagement = () => {
  const router = useRouter();
  const [kpis, setKpis] = useState<KPI[]>([]);
  const [loading, setLoading] = useState(true);

  // Filter state
  const [search, setSearch] = useState('');
  const [period, setPeriod] = useState<string | undefined>(undefined);
  const [status, setStatus] = useState<KPIStatus | undefined>(undefined);

  // Load KPIs on initial render and when filters change
  useEffect(() => {
    const fetchKPIs = async () => {
      setLoading(true);
      try {
        const params: KPIFilterParams = {
          search: search || undefined,
          period: period === 'all' ? undefined : period,
          status: status === undefined ? undefined : status,
        };

        const response = await kpiApi.getKPIs(params);

        if (response.success && response.data) {
          // Check if items exists in the expected structure
          if (response.data.items && Array.isArray(response.data.items)) {
            setKpis(response.data.items);
          } else {
            console.error(
              'Unexpected data structure in KPI response:',
              response.data
            );
            setKpis([]);
          }
        } else {
          toast.error('Failed to load KPIs');
        }
      } catch (error: unknown) {
        console.error('Error fetching KPIs:', error);

        // More specific error message based on error code
        const apiError = error as ApiError;
        if (apiError.response?.status === 401) {
          toast.error('Session expired. Please login again.');
        } else if (apiError.response?.status === 403) {
          toast.error('You do not have permission to view this page.');
        } else {
          toast.error('Failed to load KPIs. Please try again later.');
        }
      } finally {
        setLoading(false);
      }
    };

    fetchKPIs();
  }, [search, period, status]);

  // Handle view KPI detail
  const handleViewDetail = (kpi: KPI) => {
    router.push(`/employee/kpi/${kpi.id}`);
  };

  // Filter handlers
  const handleSearchChange = (value: string) => {
    setSearch(value);
  };

  const handlePeriodChange = (value: string | undefined) => {
    setPeriod(value);
  };

  const handleStatusChange = (value: KPIStatus | undefined) => {
    setStatus(value);
  };

  return {
    kpis,
    loading,
    search,
    period,
    status,
    handleViewDetail,
    handleSearchChange,
    handlePeriodChange,
    handleStatusChange,
  };
};
