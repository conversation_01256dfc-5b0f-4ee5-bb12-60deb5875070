import { useState, useEffect } from 'react';
import { toast } from 'sonner';
import { useRouter } from 'next/navigation';
import { KpiProject } from '@/types/kpi-project';
import { kpiProjectApi } from '@/lib/api/kpi-project';
import { ApiError } from '@/types/api';

export const useKpiPerProjectDetail = (id: string) => {
  const router = useRouter();
  const [kpiProject, setKpiProject] = useState<KpiProject | null>(null);
  const [loading, setLoading] = useState(true);

  // Load KPI Project details on initial render
  useEffect(() => {
    const fetchKpiProject = async () => {
      setLoading(true);
      try {
        const response = await kpiProjectApi.getKpiProjectById(id);

        if (response.success && response.data) {
          setKpiProject(response.data);
        } else {
          toast.error('Failed to load KPI Project details');
          router.push('/project');
        }
      } catch (error: unknown) {
        console.error('Error fetching KPI Project details:', error);

        // Handle errors
        const apiError = error as ApiError;
        if (apiError.response?.status === 401) {
          toast.error('Session expired. Please login again.');
        } else if (apiError.response?.status === 403) {
          toast.error('You do not have permission to view this page.');
          router.push('/project');
        } else if (apiError.response?.status === 404) {
          toast.error('KPI Project not found.');
          router.push('/project');
        } else {
          toast.error('Failed to load KPI Project details. Please try again later.');
          router.push('/project');
        }
      } finally {
        setLoading(false);
      }
    };

    if (id) {
      fetchKpiProject();
    }
  }, [id, router]);

  return {
    kpiProject,
    loading
  };
};
