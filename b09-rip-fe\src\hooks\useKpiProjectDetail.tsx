import { useState, useEffect } from 'react';
import { toast } from 'sonner';
import { useRouter } from 'next/navigation';
import {
  KpiProject,
  UpdateKpiProjectRequest,
  UpdateKpiProjectStatusRequest,
  KpiProjectStatus,
} from '@/types/kpi-project';
import { kpiProjectApi } from '@/lib/api/kpi-project';
import { ApiError } from '@/types/api';

export const useKpiProjectDetail = (id: string) => {
  const router = useRouter();
  const [kpiProject, setKpiProject] = useState<KpiProject | null>(null);
  const [loading, setLoading] = useState(true);
  const [updateLoading, setUpdateLoading] = useState(false);
  const [deleteLoading, setDeleteLoading] = useState(false);
  const [statusUpdateLoading, setStatusUpdateLoading] = useState(false);
  //const [bonusUpdateLoading, setBonusUpdateLoading] = useState(false);

  // Load Kpi Project details on initial render
  useEffect(() => {
    const fetchKpiProject = async () => {
      setLoading(true);
      try {
        const response = await kpiProjectApi.getKpiProjectById(id);

        if (response.success && response.data) {
          setKpiProject(response.data);
        } else {
          toast.error('Failed to load KPI Project details');
          router.push('/kpi-project');
        }
      } catch (error: unknown) {
        console.error('Error fetching KPI Project details:', error);

        // More specific error message based on error code
        const apiError = error as ApiError;
        if (apiError.response?.status === 401) {
          toast.error('Session expired. Please login again.');
        } else if (apiError.response?.status === 403) {
          toast.error('You do not have permission to view this page.');
          router.push('/kpi-project');
        } else if (apiError.response?.status === 404) {
          toast.error('KPI Project not found.');
          router.push('/kpi-project');
        } else {
          toast.error('Failed to load KPI Project details. Please try again later.');
          router.push('/kpi-project');
        }
      } finally {
        setLoading(false);
      }
    };

    if (id) {
      fetchKpiProject();
    }
  }, [id, router]);

  // Update KPI Project details
  const updateKpiProject = async (data: UpdateKpiProjectRequest) => {
    if (!kpiProject) return;

    setUpdateLoading(true);
    try {
      const response = await kpiProjectApi.updateKpiProject(kpiProject.id, data);

      if (response.success && response.data) {
        setKpiProject(response.data);
        toast.success('KPI Project updated successfully');
        return true;
      } else {
        toast.error('Failed to update KPI Project');
        return false;
      }
    } catch (error: unknown) {
      console.error('Error updating KPI Project:', error);

      const apiError = error as ApiError;
      if (apiError.response?.status === 401) {
        toast.error('Session expired. Please login again.');
      } else if (apiError.response?.status === 403) {
        toast.error('You do not have permission to update this KPI Project.');
      } else {
        toast.error('Failed to update KPI Project. Please try again later.');
      }
      return false;
    } finally {
      setUpdateLoading(false);
    }
  };

  // Delete KPI Project
  const deleteKpiProject = async () => {
    if (!kpiProject) return;

    setDeleteLoading(true);
    try {
      const response = await kpiProjectApi.deleteKpiProject(kpiProject.id);

      if (response.success) {
        toast.success('KPI Project deleted successfully');
        router.push('/kpi-project');
        return true;
      } else {
        toast.error('Failed to delete KPI Project');
        return false;
      }
    } catch (error: unknown) {
      console.error('Error deleting KPI Project:', error);

      const apiError = error as ApiError;
      if (apiError.response?.status === 401) {
        toast.error('Session expired. Please login again.');
      } else if (apiError.response?.status === 403) {
        toast.error('You do not have permission to delete this KPI Project.');
      } else {
        toast.error('Failed to delete KPI Project. Please try again later.');
      }
      return false;
    } finally {
      setDeleteLoading(false);
    }
  };

  // Update KPI Project status
  const updateStatus = async (status: KpiProjectStatus) => {
    if (!kpiProject) return;

    console.log('Updating status to:', status);
    setStatusUpdateLoading(true);
    try {
      const data: UpdateKpiProjectStatusRequest = { status };
      console.log('Sending update request with data:', data);
      const response = await kpiProjectApi.updateKpiProjectStatus(kpiProject.id, data);
      console.log('Update status response received:', response);

      if (response && response.success && response.data) {
        setKpiProject(response.data);
        toast.success('KPI Project status updated successfully');
        return true;
      } else {
        console.error('Failed API response:', response);
        toast.error('Failed to update KPI Project status');
        return false;
      }
    } catch (error: unknown) {
      console.error('Error updating KPI Project status:', error);

      const apiError = error as ApiError;
      console.error('API Error details:', apiError.response);

      if (apiError.response?.status === 401) {
        toast.error('Session expired. Please login again.');
      } else if (apiError.response?.status === 403) {
        toast.error('You do not have permission to update this KPI Project status.');
      } else {
        toast.error('Failed to update KPI Project status. Please try again later.');
      }
      return false;
    } finally {
      setStatusUpdateLoading(false);
    }
  };

  return {
    kpiProject,
    loading,
    updateLoading,
    deleteLoading,
    statusUpdateLoading,
    //bonusUpdateLoading,
    updateKpiProject,
    deleteKpiProject,
    updateStatus,
  };
};
