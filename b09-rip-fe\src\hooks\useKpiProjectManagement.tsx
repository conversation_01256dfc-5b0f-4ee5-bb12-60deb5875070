import { useState, useEffect } from 'react';
import { toast } from 'sonner';
import { useRouter } from 'next/navigation';
import {
  KpiProject,
  KpiProjectFilterParams,
  KpiProjectStatus,
  PaginatedKpiProjectsResponse,
} from '@/types/kpi-project';
import { kpiProjectApi } from '@/lib/api/kpi-project';
import { ApiError } from '@/types/api';

export const useKpiProjectManagement = () => {
  const router = useRouter();
  const [kpiProjects, setKpiProjects] = useState<KpiProject[]>([]);
  const [loading, setLoading] = useState(true);

  // Pagination state
  const [totalItems, setTotalItems] = useState(0);
  const [totalPages, setTotalPages] = useState(1);
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize] = useState(10); // Assuming a fixed page size

  // Filter state
  const [search, setSearch] = useState('');
  const [status, setStatus] = useState<KpiProjectStatus | undefined>(undefined);
  const [projectNames, setProjectNames] = useState<string[]>([]); // State to hold project names
  const [selectedProject, setSelectedProject] = useState<string | undefined>(
    undefined
  ); // Selected project

  // Fetch project names for the dropdown - only once when component mounts
  useEffect(() => {
    const fetchProjectNames = async () => {
      try {
        const response = await kpiProjectApi.getKpiProjects({
          page: 1,
          pageSize: 1000, // Get a large number to ensure we get all names
        });

        if (response.success && response.data && response.data.data) {
          // Extract unique project names and sort alphabetically
          const uniqueProjectNames = Array.from(
            new Set(response.data.data.map((project) => project.project_name))
          ).sort();
          setProjectNames(uniqueProjectNames);
        }
      } catch (error) {
        console.error('Failed to load project names:', error);
        toast.error('Failed to load project names');
      }
    };

    fetchProjectNames();
  }, []); // Empty dependency array means this runs once on mount

  // Fetch KPI projects when filters or pagination changes
  useEffect(() => {
    const fetchKpiProjects = async () => {
      setLoading(true);
      try {
        const params: KpiProjectFilterParams = {
          status: status === undefined ? undefined : status,
          page: currentPage,
          pageSize,
        };

        // Menambahkan filter berdasarkan proyek yang dipilih
        if (selectedProject && selectedProject !== 'all') {
          params.project_name = selectedProject; // Filter berdasarkan nama proyek
        }

        // Jika ada search, tambahkan filter search
        if (search) {
          params.search = search;
        }

        // Add role-based filters
        const response = await kpiProjectApi.getKpiProjects(params);

        if (response.success && response.data) {
          const paginatedData: PaginatedKpiProjectsResponse = response.data;
          // Handle both data structures - check for data field first, then items field
          let filteredProjects =
            paginatedData.data || paginatedData.items || [];

          console.log('Filtered projects before processing:', filteredProjects);

          // Menambahkan filter manual jika nama proyek tidak cocok
          if (selectedProject && filteredProjects.length > 0) {
            filteredProjects = filteredProjects.filter(
              (project) => project.project_name === selectedProject
            );

            // Menyesuaikan pagination setelah filter manual
            if (paginatedData.pagination) {
              setTotalItems(filteredProjects.length);
              setTotalPages(Math.ceil(filteredProjects.length / pageSize));
            }
          } else {
            if (paginatedData.pagination) {
              setTotalItems(paginatedData.pagination.total || 0);
              setTotalPages(paginatedData.pagination.pageCount || 1);
            }
          }

          setKpiProjects(filteredProjects);
        } else {
          toast.error('Failed to load KPI Projects');
          setKpiProjects([]);
          setTotalItems(0);
          setTotalPages(1);
        }
      } catch (error: unknown) {
        console.error('Error fetching KPI Projects:', error);

        const apiError = error as ApiError;
        if (apiError.response?.status === 401) {
          toast.error('Session expired. Please login again.');
        } else if (apiError.response?.status === 403) {
          toast.error('You do not have permission to view this page.');
        } else {
          toast.error('Failed to load KPI Projects. Please try again later.');
        }

        setKpiProjects([]);
        setTotalItems(0);
        setTotalPages(1);
      } finally {
        setLoading(false);
      }
    };

    fetchKpiProjects();
  }, [search, status, selectedProject, currentPage, pageSize]);

  const handleSearchChange = (value: string) => {
    setSearch(value);
    setSelectedProject(undefined); // Clear selected project when search changes
    setCurrentPage(1); // Reset to page 1 when search changes
  };

  const handleStatusChange = (value: KpiProjectStatus | undefined) => {
    setStatus(value);
    setCurrentPage(1); // Reset to page 1 when status changes
  };

  const handleProjectChange = (value: string | undefined) => {
    setSelectedProject(value);
    setSearch(''); // Clear search field when project is selected
    setCurrentPage(1); // Reset to page 1 when project selection changes
  };

  const handleViewDetail = (kpiProject: KpiProject) => {
    router.push(`/kpi-project/${kpiProject.id}`);
  };

  return {
    kpiProjects,
    loading,
    totalItems,
    totalPages,
    currentPage,
    pageSize,
    search,
    status,
    projectNames, // Expose the project names for the dropdown
    selectedProject, // Expose the selected project
    handleViewDetail,
    handleSearchChange,
    handleStatusChange,
    handleProjectChange, // Provide the handler for project dropdown
    setCurrentPage,
  };
};
