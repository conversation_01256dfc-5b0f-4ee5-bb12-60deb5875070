// path: b09-rip-fe/src/hooks/useMyKPI.tsx
import { useState, useEffect } from 'react';
import { toast } from 'sonner';
import { useRouter } from 'next/navigation';
import { KPI, KPIFilterParams, KPIStatus } from '@/types/kpi';
import { kpiApi } from '@/lib/api/kpi';
import { ApiError } from '@/types/api';
import { useAuthStore } from '@/lib/store/auth-store';

export const useMyKPI = () => {
  const router = useRouter();
  const { user } = useAuthStore();
  const [kpis, setKpis] = useState<KPI[]>([]);
  const [loading, setLoading] = useState(true);
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);

  // Filter state
  const [search, setSearch] = useState('');
  const [period, setPeriod] = useState<string | undefined>(undefined);
  const [status, setStatus] = useState<KPIStatus | undefined>(undefined);

  // Load KPIs on initial render and when filters change
  useEffect(() => {
    const fetchMyKPIs = async () => {
      setLoading(true);
      try {
        const params: KPIFilterParams = {
          page: currentPage,
          pageSize: 10,
          search: search || undefined,
          period: period === 'all' ? undefined : period,
          status: status === undefined ? undefined : status,
        };

        const response = await kpiApi.getMyKPIs(params);

        if (response.success && response.data) {
          if (response.data.items && Array.isArray(response.data.items)) {
            setKpis(response.data.items);

            // Set pagination info
            if (response.data.pagination) {
              setTotalPages(response.data.pagination.pageCount || 1);
            }
          } else {
            console.error(
              'Unexpected data structure in KPI response:',
              response.data
            );
            setKpis([]);
          }
        } else {
          toast.error('Failed to load KPIs');
        }
      } catch (error: unknown) {
        console.error('Error fetching KPIs:', error);

        // More specific error message based on error code
        const apiError = error as ApiError;
        if (apiError.response?.status === 401) {
          toast.error('Session expired. Please login again.');
        } else if (apiError.response?.status === 403) {
          toast.error('You do not have permission to view this page.');
        } else {
          toast.error('Failed to load KPIs. Please try again later.');
        }
      } finally {
        setLoading(false);
      }
    };

    fetchMyKPIs();
  }, [search, period, status, currentPage]);

  // Handle view KPI detail
  const handleViewDetail = (kpi: KPI) => {
    router.push(`/employee/mykpi/${kpi.id}`);
  };

  // Filter handlers
  const handleSearchChange = (value: string) => {
    setSearch(value);
    setCurrentPage(1);
  };

  const handlePeriodChange = (value: string | undefined) => {
    setPeriod(value);
    setCurrentPage(1);
  };

  const handleStatusChange = (value: KPIStatus | undefined) => {
    setStatus(value);
    setCurrentPage(1);
  };

  const handlePageChange = (page: number) => {
    setCurrentPage(page);
  };

  return {
    kpis,
    loading,
    search,
    period,
    status,
    currentPage,
    totalPages,
    user,
    handleViewDetail,
    handleSearchChange,
    handlePeriodChange,
    handleStatusChange,
    handlePageChange,
  };
};

export default useMyKPI;
