import { useState, useEffect } from 'react';
import { toast } from 'sonner';
import { useRouter } from 'next/navigation';
import { Organization, UpdateOrganizationRequest } from '@/types/organization';
import { organizationApi } from '@/lib/api/organization';
import { ApiError } from '@/types/api';

export const useOrganizationDetail = (id: string) => {
  const router = useRouter();
  const [organization, setOrganization] = useState<Organization | null>(null);
  const [loading, setLoading] = useState(true);
  const [updating, setUpdating] = useState(false);
  const [isEditing, setIsEditing] = useState(false);
  const [deleting, setDeleting] = useState(false);

  // Fetch organization details
  useEffect(() => {
    const fetchOrganizationDetail = async () => {
      setLoading(true);
      try {
        const response = await organizationApi.getOrganizationById(id);

        if (response.success && response.data) {
          setOrganization(response.data);
        } else {
          toast.error('Failed to load organization details');
        }
      } catch (error: unknown) {
        console.error('Error fetching organization details:', error);

        const apiError = error as ApiError;
        if (apiError.response?.status === 404) {
          toast.error('Organization not found');
          router.push('/client');
        } else if (apiError.response?.status === 401) {
          toast.error('Session expired. Please login again.');
        } else if (apiError.response?.status === 403) {
          toast.error('You do not have permission to view this page.');
        } else {
          toast.error(
            'Failed to load organization details. Please try again later.'
          );
        }
      } finally {
        setLoading(false);
      }
    };

    if (id) {
      fetchOrganizationDetail();
    }
  }, [id, router]);

  // Handle edit mode toggle
  const handleEditToggle = () => {
    setIsEditing(!isEditing);
  };

  // Handle save organization changes
  const handleSaveChanges = async (data: UpdateOrganizationRequest) => {
    if (!organization) return;

    setUpdating(true);
    try {
      const response = await organizationApi.updateOrganization(
        organization.id,
        data
      );

      if (response.success && response.data) {
        setOrganization(response.data);
        setIsEditing(false);
        toast.success('Organization updated successfully');
      } else {
        toast.error('Failed to update organization');
      }
    } catch (error: unknown) {
      console.error('Error updating organization:', error);

      const apiError = error as ApiError;
      if (apiError.response?.status === 400) {
        toast.error('Invalid data. Please check your inputs.');
      } else if (apiError.response?.status === 401) {
        toast.error('Session expired. Please login again.');
      } else if (apiError.response?.status === 403) {
        toast.error('You do not have permission to update this organization.');
      } else {
        toast.error('Failed to update organization. Please try again later.');
      }
    } finally {
      setUpdating(false);
    }
  };

  // Handle cancel edit
  const handleCancelEdit = () => {
    setIsEditing(false);
  };

  // Handle delete organization
  const handleDelete = async () => {
    if (!organization) return;

    setDeleting(true);
    try {
      const response = await organizationApi.deleteOrganization(
        organization.id
      );

      if (response.success) {
        toast.success('Organization deleted successfully');
        router.push('/client');
      } else {
        toast.error('Failed to delete organization');
      }
    } catch (error: unknown) {
      console.error('Error deleting organization:', error);

      const apiError = error as ApiError;
      if (apiError.response?.status === 401) {
        toast.error('Session expired. Please login again.');
      } else if (apiError.response?.status === 403) {
        toast.error('You do not have permission to delete this organization.');
      } else if (apiError.response?.status === 404) {
        toast.error('Organization not found.');
        router.push('/client');
      } else {
        toast.error('Failed to delete organization. Please try again later.');
      }
    } finally {
      setDeleting(false);
    }
  };

  return {
    organization,
    loading,
    updating,
    isEditing,
    deleting,
    handleEditToggle,
    handleSaveChanges,
    handleCancelEdit,
    handleDelete,
  };
};
