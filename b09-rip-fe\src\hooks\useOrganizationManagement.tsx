import { useState, useEffect } from 'react';
import { toast } from 'sonner';
import { useRouter } from 'next/navigation';
import { Organization, OrganizationFilterParams } from '@/types/organization';
import { organizationApi } from '@/lib/api/organization';
import { ApiError } from '@/types/api';

export const useOrganizationManagement = () => {
  const router = useRouter();
  const [organizations, setOrganizations] = useState<Organization[]>([]);
  const [loading, setLoading] = useState(true);

  // Pagination state
  const [totalPages, setTotalPages] = useState(1);
  const [currentPage, setCurrentPage] = useState(1);
  const [totalItems, setTotalItems] = useState(0);

  // Filter state
  const [search, setSearch] = useState('');
  const [clientType, setClientType] = useState<string | undefined>(undefined);

  // Load organizations on initial render and when filters/pagination change
  useEffect(() => {
    const fetchOrganizations = async () => {
      setLoading(true);
      try {
        const params: OrganizationFilterParams = {
          page: currentPage,
          pageSize: 10,
          search: search || undefined,
          client_type: clientType,
        };

        // Log the response structure to debug pagination issues
        const response = await organizationApi.getOrganizations(params);
        console.log('Organization API response:', response);
        console.log('Organization pagination data:', response.data?.pagination);

        if (response.success && response.data) {
          // Check if items exists in the expected structure
          if (response.data.items && Array.isArray(response.data.items)) {
            setOrganizations(response.data.items);
          } else {
            console.error(
              'Unexpected data structure in organization response:',
              response.data
            );
            setOrganizations([]);
          }

          // Ensure pagination data exists and has the expected properties
          if (response.data.pagination) {
            setTotalPages(response.data.pagination.pageCount || 1);
            setTotalItems(response.data.pagination.total || 0);
          } else {
            console.error('Missing pagination data in organization response');
            setTotalPages(1);
            setTotalItems(0);
          }
        } else {
          toast.error('Failed to load organizations');
        }
      } catch (error: unknown) {
        console.error('Error fetching organizations:', error);

        // More specific error message based on error code
        const apiError = error as ApiError;
        if (apiError.response?.status === 401) {
          toast.error('Session expired. Please login again.');
        } else if (apiError.response?.status === 403) {
          toast.error('You do not have permission to view this page.');
        } else {
          toast.error('Failed to load organizations. Please try again later.');
        }
      } finally {
        setLoading(false);
      }
    };

    fetchOrganizations();
  }, [currentPage, search, clientType]);

  // Handle view organization detail
  const handleViewDetail = (organization: Organization) => {
    router.push(`/client/${organization.id}`);
  };

  // Filter handlers
  const handleSearchChange = (value: string) => {
    setSearch(value);
    setCurrentPage(1); // Reset to first page when search changes
  };

  const handleClientTypeChange = (value: string | undefined) => {
    setClientType(value);
    setCurrentPage(1); // Reset to first page when filter changes
  };

  return {
    organizations,
    loading,
    totalPages,
    totalItems,
    currentPage,
    search,
    clientType,
    handleViewDetail,
    handleSearchChange,
    handleClientTypeChange,
    setCurrentPage,
  };
};
