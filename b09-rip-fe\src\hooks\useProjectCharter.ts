import { useState, useEffect, useCallback } from 'react';
import { toast } from 'sonner';
import {
  ProjectCharter,
  CreateProjectCharterDto,
  UpdateProjectCharterDto,
} from '@/types/project-charter';
import { projectCharterApi } from '@/lib/api/project-charter';
import { ApiError } from '@/types/api';

export function useProjectCharter(projectId?: string) {
  const [projectCharter, setProjectCharter] = useState<ProjectCharter | null>(
    null
  );
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Fetch project charter by project ID
  const fetchProjectCharter = useCallback(async () => {
    if (!projectId) return;

    setLoading(true);
    setError(null);

    try {
      const response =
        await projectCharterApi.getProjectCharterByProjectId(projectId);
      if (response.success && response.data) {
        setProjectCharter(response.data);
      } else {
        setError(response.message || 'Failed to fetch project charter');
      }
    } catch (err) {
      const apiError = err as ApiError;
      setError(
        apiError.message ||
          'An error occurred while fetching the project charter'
      );
      toast.error(
        `Error: ${apiError.message || 'Failed to fetch project charter'}`
      );
    } finally {
      setLoading(false);
    }
  }, [projectId]);

  // Create a new project charter
  const createProjectCharter = async (data: CreateProjectCharterDto) => {
    setLoading(true);
    setError(null);

    try {
      const response = await projectCharterApi.createProjectCharter(data);
      if (response.success && response.data) {
        setProjectCharter(response.data);
        toast.success('Project charter created successfully');
        return response.data;
      } else {
        setError(response.message || 'Failed to create project charter');
        toast.error(response.message || 'Failed to create project charter');
        return null;
      }
    } catch (err) {
      const apiError = err as ApiError;
      setError(
        apiError.message ||
          'An error occurred while creating the project charter'
      );
      toast.error(
        `Error: ${apiError.message || 'Failed to create project charter'}`
      );
      return null;
    } finally {
      setLoading(false);
    }
  };

  // Update an existing project charter
  const updateProjectCharter = async (
    id: string,
    data: UpdateProjectCharterDto
  ) => {
    setLoading(true);
    setError(null);

    try {
      const response = await projectCharterApi.updateProjectCharter(id, data);
      if (response.success && response.data) {
        setProjectCharter(response.data);
        toast.success('Project charter updated successfully');
        return response.data;
      } else {
        setError(response.message || 'Failed to update project charter');
        toast.error(response.message || 'Failed to update project charter');
        return null;
      }
    } catch (err) {
      const apiError = err as ApiError;
      setError(
        apiError.message ||
          'An error occurred while updating the project charter'
      );
      toast.error(
        `Error: ${apiError.message || 'Failed to update project charter'}`
      );
      return null;
    } finally {
      setLoading(false);
    }
  };

  // Load project charter on component mount if projectId is provided
  useEffect(() => {
    if (projectId) {
      fetchProjectCharter();
    }
  }, [projectId, fetchProjectCharter]);

  return {
    projectCharter,
    loading,
    error,
    fetchProjectCharter,
    createProjectCharter,
    updateProjectCharter,
  };
}
