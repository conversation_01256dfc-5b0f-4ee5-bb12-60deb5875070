// src/hooks/useProjectDashboard.ts
import { useState, useEffect, useCallback } from 'react';
import { toast } from 'sonner';
import { useRouter } from 'next/navigation';
import { ProjectDashboardData } from '@/types/project-dashboard';
import { projectApi } from '@/lib/api/project';
import { ApiError } from '@/types/api';

/**
 * Custom hook for fetching and managing project dashboard data
 */
export function useProjectDashboard(projectId: string) {
  const router = useRouter();
  const [dashboardData, setDashboardData] = useState<ProjectDashboardData | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  /**
   * Fetch dashboard data from the API
   */
  const fetchDashboardData = useCallback(async () => {
    if (!projectId) return;

    setLoading(true);
    setError(null);

    try {
      const response = await projectApi.getProjectDashboard(projectId);
      
      if (response.success && response.data) {
        setDashboardData(response.data);
      } else {
        setError(response.message || 'Failed to fetch dashboard data');
        toast.error(`Error: ${response.message || 'Failed to fetch dashboard data'}`);
      }
    } catch (err) {
      const apiError = err as ApiError;
      setError(apiError.message || 'An error occurred while fetching dashboard data');
      
      // Handle specific error cases
      if (apiError.response?.status === 401) {
        toast.error('Session expired. Please login again.');
      } else if (apiError.response?.status === 403) {
        toast.error('You do not have permission to view this dashboard.');
        router.push('/project');
      } else if (apiError.response?.status === 404) {
        toast.error('Project not found.');
        router.push('/project');
      } else {
        toast.error(`Error: ${apiError.message || 'Failed to fetch dashboard data'}`);
      }
    } finally {
      setLoading(false);
    }
  }, [projectId, router]);

  // Fetch dashboard data on initial render
  useEffect(() => {
    fetchDashboardData();
  }, [fetchDashboardData]);

  return {
    dashboardData,
    loading,
    error,
    refreshDashboard: fetchDashboardData,
  };
}

export default useProjectDashboard;
