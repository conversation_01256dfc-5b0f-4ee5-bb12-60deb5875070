import { useState, useEffect, useCallback } from 'react';
import { toast } from 'sonner';
import { useRouter } from 'next/navigation';
import {
  KpiProject,
  // KpiProjectFilterParams, // Not used in this component
  KpiProjectStatus,
} from '@/types/kpi-project';
import { kpiProjectApi } from '@/lib/api/kpi-project';
import { ApiError } from '@/types/api';
import { projectApi } from '@/lib/api/project';

interface ProjectInfo {
  project_id: string;
  project_name: string;
}

export const useProjectKPI = (projectId: string) => {
  const router = useRouter();
  const [kpiProjects, setKpiProjects] = useState<KpiProject[]>([]);
  const [loading, setLoading] = useState(true);
  const [projectInfo, setProjectInfo] = useState<ProjectInfo | null>(null);
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);

  // Filter state
  const [search, setSearch] = useState('');
  const [status, setStatus] = useState<KpiProjectStatus | undefined>(undefined);

  // Fetch project information
  const fetchProjectInfo = useCallback(async () => {
    if (!projectId) return;

    try {
      const response = await projectApi.getProjectById(projectId);

      if (response.success && response.data) {
        setProjectInfo({
          project_id: response.data.id,
          project_name: response.data.project_name,
        });
      }
    } catch (error) {
      console.error('Error fetching project information:', error);
    }
  }, [projectId]);

  // Load KPI projects on initial render and when filters change
  useEffect(() => {
    const fetchKpiProjects = async () => {
      if (!projectId) return;

      setLoading(true);
      try {
        // For filtering, we'll still send these params to the API
        const params = {
          search: search || undefined,
          status: status === undefined ? undefined : status,
        };

        const response = await kpiProjectApi.getKpiProjectsByProjectId(
          projectId,
          params
        );

        if (response.success && response.data) {
          // The API returns the array directly in response.data
          const projects = Array.isArray(response.data) ? response.data : [];
          setKpiProjects(projects);

          // Set pagination info (assuming we're using the same pagination as other features)
          // If we need to implement client-side pagination, we can do that later
          setTotalPages(1); // For now, we'll just set it to 1 page
        } else {
          toast.error('Failed to load KPI Projects');
          setKpiProjects([]);
          setTotalPages(1);
        }
      } catch (error: unknown) {
        console.error('Error fetching KPI Projects:', error);

        // More specific error message based on error code
        const apiError = error as ApiError;
        if (apiError.response?.status === 401) {
          toast.error('Session expired. Please login again.');
        } else if (apiError.response?.status === 403) {
          toast.error('You do not have permission to view this page.');
        } else if (apiError.response?.status === 404) {
          toast.error('Project not found.');
          router.push('/kpi-project');
        } else {
          toast.error('Failed to load KPI Projects. Please try again later.');
        }

        setKpiProjects([]);
        setTotalPages(1);
      } finally {
        setLoading(false);
      }
    };

    fetchKpiProjects();
    fetchProjectInfo();
  }, [projectId, search, status, currentPage, fetchProjectInfo, router]);

  // Handle view KPI project detail
  const handleViewDetail = (kpiProject: KpiProject) => {
    router.push(
      `/kpi-project/${kpiProject.id}?fromProject=true&projectId=${projectId}`
    );
  };

  // Filter handlers
  const handleSearchChange = (value: string) => {
    setSearch(value);
    setCurrentPage(1);
  };

  const handleStatusChange = (value: KpiProjectStatus | undefined) => {
    setStatus(value);
    setCurrentPage(1);
  };

  const handlePageChange = (page: number) => {
    setCurrentPage(page);
  };

  // Handle back button to all KPI projects
  const handleBackToAll = () => {
    router.push('/kpi-project');
  };

  return {
    kpiProjects,
    loading,
    search,
    status,
    projectInfo,
    currentPage,
    totalPages,
    handleViewDetail,
    handleSearchChange,
    handleStatusChange,
    handlePageChange,
    handleBackToAll,
  };
};

export default useProjectKPI;
