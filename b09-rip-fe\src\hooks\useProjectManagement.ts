import { useState, useEffect, useCallback } from 'react';
import { useRouter } from 'next/navigation';
import {
  Project,
  ProjectCategory,
  ProjectStatus,
  // ApiResponse, // Not used in this component
} from '@/types/project';
import { projectApi } from '@/lib/api/project';
import { toast } from 'sonner';
import { ApiError } from '@/types/api';

export function useProjectManagement() {
  const router = useRouter();
  const [projects, setProjects] = useState<Project[]>([]);
  const [loading, setLoading] = useState(true);
  const [totalPages, setTotalPages] = useState(1);
  const [totalItems, setTotalItems] = useState(0);
  const [currentPage, setCurrentPage] = useState(1);
  const [search, setSearch] = useState('');
  const [projectCategory, setProjectCategory] = useState<
    ProjectCategory | undefined
  >();
  const [projectStatus, setProjectStatus] = useState<
    ProjectStatus | undefined
  >();

  const fetchProjects = useCallback(async () => {
    try {
      setLoading(true);
      const response = await projectApi.getProjects({
        search,
        page: currentPage,
        pageSize: 10,
        project_category: projectCategory,
        status_project: projectStatus,
      });

      if (response.success && response.data) {
        // Check if items exists in the expected structure
        if (response.data.items && Array.isArray(response.data.items)) {
          setProjects(response.data.items);
        } else {
          console.error(
            'Unexpected data structure in project response:',
            response.data
          );
          setProjects([]);
        }

        // Ensure pagination data exists and has the expected properties
        if (response.data.pagination) {
          setTotalPages(response.data.pagination.pageCount || 1);
          setTotalItems(response.data.pagination.total || 0);
        } else {
          console.error('Missing pagination data in project response');
          setTotalPages(1);
          setTotalItems(0);
        }
      } else {
        toast.error('Failed to load projects');
        setProjects([]);
        setTotalItems(0);
        setTotalPages(1);
      }
    } catch (error) {
      console.error('Error fetching projects:', error);

      // More specific error message based on error code
      const apiError = error as ApiError;
      if (apiError.response?.status === 401) {
        toast.error('Session expired. Please login again.');
      } else if (apiError.response?.status === 403) {
        toast.error('You do not have permission to view this page.');
      } else {
        toast.error('Failed to load projects. Please try again later.');
      }

      setProjects([]);
      setTotalItems(0);
      setTotalPages(1);
    } finally {
      setLoading(false);
    }
  }, [search, currentPage, projectCategory, projectStatus]);

  useEffect(() => {
    fetchProjects();
  }, [fetchProjects]);

  const refreshProjects = () => {
    fetchProjects();
  };

  const handleSearchChange = (value: string) => {
    setSearch(value);
    setCurrentPage(1);
  };

  const handleProjectCategoryChange = (value: ProjectCategory | undefined) => {
    setProjectCategory(value);
    setCurrentPage(1);
  };

  const handleProjectStatusChange = (value: ProjectStatus | undefined) => {
    setProjectStatus(value);
    setCurrentPage(1);
  };

  const handleViewDetail = (id: string) => {
    router.push(`/project/${id}`);
  };

  return {
    projects,
    loading,
    totalPages,
    totalItems,
    currentPage,
    search,
    projectCategory,
    projectStatus,
    handleSearchChange,
    handleProjectCategoryChange,
    handleProjectStatusChange,
    handleViewDetail,
    setCurrentPage,
    refreshProjects,
  };
}
