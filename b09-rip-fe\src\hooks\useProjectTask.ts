import { useState, useEffect, useCallback } from 'react';
import { toast } from 'sonner';
import { projectTaskApi } from '@/lib/api/project-task';
import { ProjectTask, ProjectTaskFilterParams } from '@/types/project-task';

export function useProjectTasks(initialParams: ProjectTaskFilterParams = {}) {
  // Get project ID from URL if available
  const getProjectIdFromUrl = useCallback(() => {
    if (typeof window !== 'undefined') {
      const urlParams = new URLSearchParams(window.location.search);
      return urlParams.get('projectId') || initialParams.project_id;
    }
    return initialParams.project_id;
  }, [initialParams.project_id]);

  // State for tasks and pagination
  const [tasks, setTasks] = useState<ProjectTask[]>([]);
  const [filteredTasks, setFilteredTasks] = useState<ProjectTask[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Pagination state
  const [currentPage, setCurrentPage] = useState(initialParams.page || 1);
  const [pageSize, setPageSize] = useState(initialParams.pageSize || 10);
  const [totalItems, setTotalItems] = useState(0);
  const [totalPages, setTotalPages] = useState(1);

  // Filter state
  const [search, setSearch] = useState(initialParams.search || '');
  const [status, setStatus] = useState(
    initialParams.completion_status || undefined
  );
  const [projectId, setProjectId] = useState(
    getProjectIdFromUrl() || undefined
  );
  const [employeeId, setEmployeeId] = useState(
    initialParams.employee_id || undefined
  );

  // Update URL with current filters
  const updateUrlParams = useCallback(() => {
    if (typeof window !== 'undefined') {
      const url = new URL(window.location.href);

      // Update or remove projectId parameter
      if (projectId) {
        url.searchParams.set('projectId', projectId);
      } else {
        url.searchParams.delete('projectId');
      }

      // Optionally add other parameters
      if (status) {
        url.searchParams.set('status', status);
      } else {
        url.searchParams.delete('status');
      }

      // Add search parameter to URL
      if (search) {
        url.searchParams.set('search', search);
      } else {
        url.searchParams.delete('search');
      }

      // Update URL without page refresh
      window.history.pushState({}, '', url.toString());
    }
  }, [projectId, status, search]);

  // Fetch tasks based on current params
  const fetchTasks = useCallback(
    async (params?: ProjectTaskFilterParams) => {
      setLoading(true);

      try {
        // Use provided params or build from current state
        const queryParams: ProjectTaskFilterParams = params || {
          page: currentPage,
          pageSize: pageSize,
          search: '', // Kirim string kosong untuk get all data, kita akan filter di client-side
          completion_status: status,
          project_id: projectId,
          employee_id: employeeId,
        };

        // Log the params we're sending to the API for debugging
        console.log('Fetching tasks with params:', queryParams);

        const response = (await projectTaskApi.getProjectTasks(
          queryParams
        )) as {
          success: boolean;
          data: {
            items?: ProjectTask[] | { items: ProjectTask[] };
            pagination?: { total: number; pageCount: number };
          };
          message?: string;
        };

        console.log('API response:', response);

        if (response.success && response.data) {
          // Handle different response formats
          let taskItems: ProjectTask[] = [];

          if (response.data.items) {
            if (Array.isArray(response.data.items)) {
              taskItems = response.data.items;
            } else if (
              response.data.items.items &&
              Array.isArray(response.data.items.items)
            ) {
              taskItems = response.data.items.items;
            }
          } else if (Array.isArray(response.data)) {
            taskItems = response.data;
          }

          // Simpan semua tasks untuk difilter
          setTasks(taskItems);

          // Extract pagination info if available
          if (response.data.pagination) {
            setTotalItems(response.data.pagination.total || taskItems.length);
            setTotalPages(
              response.data.pagination.pageCount ||
                Math.ceil(taskItems.length / pageSize)
            );
          } else {
            setTotalItems(taskItems.length);
            setTotalPages(Math.ceil(taskItems.length / pageSize));
          }

          setError(null);
        } else {
          setError(response.message || 'Failed to fetch tasks');
          toast.error('Gagal memuat data tugas proyek');
          setTasks([]);
          setFilteredTasks([]);
        }
      } catch (err: unknown) {
        console.error('Error fetching tasks:', err);
        const errorObj = err as { message?: string };
        setError(errorObj.message || 'An unexpected error occurred');
        toast.error(errorObj.message || 'Terjadi kesalahan saat memuat data');
        setTasks([]);
        setFilteredTasks([]);
      } finally {
        setLoading(false);
      }
    },
    [currentPage, pageSize, status, projectId, employeeId]
  );

  // Lakukan pencarian client-side
  useEffect(() => {
    if (!search || search.trim() === '') {
      setFilteredTasks(tasks);
      return;
    }

    const lowerCaseSearch = search.toLowerCase().trim();
    const filtered = tasks.filter((task) => {
      const descriptionMatch =
        task.description?.toLowerCase().includes(lowerCaseSearch) || false;
      const employeeMatch =
        task.employee_name?.toLowerCase().includes(lowerCaseSearch) || false;

      return descriptionMatch || employeeMatch;
    });

    console.log(
      `Client-side search: Found ${filtered.length} results for "${search}"`
    );
    setFilteredTasks(filtered);

    // Update pagination for filtered results
    setTotalItems(filtered.length);
    setTotalPages(Math.ceil(filtered.length / pageSize));
  }, [search, tasks, pageSize]);

  // Refetch when params change - with a debounce for search term
  useEffect(() => {
    // Add a small delay to avoid making too many requests
    const timer = setTimeout(() => {
      fetchTasks();
    }, 300);

    return () => clearTimeout(timer);
  }, [currentPage, pageSize, status, projectId, employeeId, fetchTasks]);

  // Update URL when filters change
  useEffect(() => {
    updateUrlParams();
  }, [projectId, status, search, updateUrlParams]);

  // Handler for page change
  const handlePageChange = (page: number) => {
    setCurrentPage(page);
  };

  // Handler for search change
  const handleSearchChange = (value: string) => {
    console.log('Search term being set to:', value);
    setSearch(value);
    setCurrentPage(1); // Reset to first page
  };

  // Handler for status filter change
  const handleStatusChange = (value: string | undefined) => {
    console.log('Status being set to:', value);
    setStatus(value);
    setCurrentPage(1); // Reset to first page
  };

  // Handler for project filter change
  const handleProjectChange = (value: string | undefined) => {
    console.log('Project being set to:', value);
    setProjectId(value);
    setCurrentPage(1); // Reset to first page
    // URL will be updated in the useEffect
  };

  // Handler for employee filter change
  const handleEmployeeChange = (value: string | undefined) => {
    setEmployeeId(value);
    setCurrentPage(1); // Reset to first page
  };

  // Reset all filters
  const resetFilters = () => {
    setSearch('');
    setStatus(undefined);
    if (!initialParams.project_id) setProjectId(undefined);
    if (!initialParams.employee_id) setEmployeeId(undefined);
    setCurrentPage(1);
  };

  return {
    // Data - return filtered tasks instead of all tasks
    tasks: filteredTasks,
    loading,
    error,

    // Pagination
    currentPage,
    pageSize,
    totalItems,
    totalPages,

    // Filters
    search,
    status,
    projectId,
    employeeId,

    // Actions
    fetchTasks,
    handlePageChange,
    handleSearchChange,
    handleStatusChange,
    handleProjectChange,
    handleEmployeeChange,
    resetFilters,
    setPageSize,
  };
}

export default useProjectTasks;
