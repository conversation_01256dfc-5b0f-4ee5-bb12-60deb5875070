import { useState, useEffect, useCallback } from 'react';
import { toast } from 'sonner';
import { projectTaskApi } from '@/lib/api/project-task';
import { ProjectTask, ProjectTaskFilterParams } from '@/types/project-task';
// import { usePathname } from 'next/navigation'; // Not used after removing API-style URL check
import api from '@/lib/api/client';

interface UseProjectTasksByProjectResult {
  tasks: ProjectTask[];
  loading: boolean;
  error: string | null;
  projectName: string | null;
  currentPage: number;
  totalPages: number;
  search: string;
  status: string | undefined;
  setCurrentPage: (page: number) => void;
  handleSearchChange: (value: string) => void;
  handleStatusChange: (value: string | undefined) => void;
  refreshTasks: () => void;
  setProjectNameDirectly: (name: string) => void; // New function to set project name directly
}

export function useProjectTasksByProject(
  projectId: string
): UseProjectTasksByProjectResult {
  // const pathname = usePathname(); // Not used after removing API-style URL check
  const [tasks, setTasks] = useState<ProjectTask[]>([]);
  const [filteredTasks, setFilteredTasks] = useState<ProjectTask[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [projectName, setProjectName] = useState<string | null>(null);
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [search, setSearch] = useState('');
  const [status, setStatus] = useState<string | undefined>(undefined);
  // const [isApiStyleUrl, setIsApiStyleUrl] = useState(false); // Not used in this component

  // This effect was used to check API-style URL format but is no longer needed
  // useEffect(() => {
  //   setIsApiStyleUrl(pathname.startsWith('/v1/project-tasks/project/'));
  // }, [pathname]);

  const fetchTasks = useCallback(async () => {
    if (!projectId) return;

    setLoading(true);
    setError(null);

    try {
      // Use the same API call for both URL formats
      const params: ProjectTaskFilterParams = {
        page: currentPage,
        pageSize: 10,
        completion_status: status,
        project_id: projectId,
      };

      const response = await projectTaskApi.getProjectTasks(params);

      console.log('API response:', response);

      if (response.success && response.data) {
        // Extract tasks from response
        let taskItems: ProjectTask[] = [];

        if (response.data.items) {
          if (Array.isArray(response.data.items)) {
            taskItems = response.data.items;
          } else if (
            typeof response.data.items === 'object' &&
            'items' in response.data.items
          ) {
            taskItems = Array.isArray(
              (response.data.items as { items: ProjectTask[] }).items
            )
              ? (response.data.items as { items: ProjectTask[] }).items
              : [];
          }
        } else if (Array.isArray(response.data)) {
          taskItems = response.data;
        }

        // Set the original task list
        setTasks(taskItems);

        // Apply search filter
        if (search && search.trim() !== '') {
          const lowerCaseSearch = search.toLowerCase().trim();
          const filtered = taskItems.filter((task) => {
            const descriptionMatch =
              task.description?.toLowerCase().includes(lowerCaseSearch) ||
              false;
            const employeeMatch =
              task.employee_name?.toLowerCase().includes(lowerCaseSearch) ||
              false;

            return descriptionMatch || employeeMatch;
          });
          setFilteredTasks(filtered);
        } else {
          setFilteredTasks(taskItems);
        }

        // Get project name from tasks if available and not already set
        if (!projectName && taskItems.length > 0 && taskItems[0].project_name) {
          setProjectName(taskItems[0].project_name);
        }

        // Get pagination info
        if (response.data.pagination) {
          setTotalPages(response.data.pagination.pageCount || 1);
        } else {
          setTotalPages(Math.ceil(taskItems.length / 10));
        }

        setError(null);
      } else {
        setError(response.message || 'Failed to fetch tasks');
        toast.error('Gagal memuat data tugas proyek');
        setTasks([]);
        setFilteredTasks([]);
      }
    } catch (err: unknown) {
      console.error('Error fetching tasks:', err);
      const errorObj = err as { message?: string };
      setError(errorObj.message || 'An unexpected error occurred');
      toast.error(errorObj.message || 'Terjadi kesalahan saat memuat data');
      setTasks([]);
      setFilteredTasks([]);
    } finally {
      setLoading(false);
    }
  }, [currentPage, projectId, status, search, projectName]);

  // Fetch project details to get the name
  const fetchProjectDetails = useCallback(async () => {
    if (!projectId) return;

    try {
      const response = await api.get(`/v1/projects/${projectId}`);
      if (response.data && response.data.success && response.data.data) {
        setProjectName(response.data.data.project_name);
      }
    } catch (error) {
      console.error('Error fetching project details:', error);
      // Don't set an error state here as it's not critical
    }
  }, [projectId]);

  // Function to set project name directly from outside the hook
  const setProjectNameDirectly = useCallback((name: string) => {
    if (name) {
      setProjectName(name);
    }
  }, []);

  // Apply client-side search filter
  useEffect(() => {
    if (!search || search.trim() === '') {
      setFilteredTasks(tasks);
      return;
    }

    const lowerCaseSearch = search.toLowerCase().trim();
    const filtered = tasks.filter((task) => {
      const descriptionMatch =
        task.description?.toLowerCase().includes(lowerCaseSearch) || false;
      const employeeMatch =
        task.employee_name?.toLowerCase().includes(lowerCaseSearch) || false;

      return descriptionMatch || employeeMatch;
    });

    console.log(
      `Client-side search: Found ${filtered.length} results for "${search}"`
    );
    setFilteredTasks(filtered);
  }, [search, tasks]);

  // Fetch tasks and project details when dependencies change
  useEffect(() => {
    // First fetch project details to get the name
    fetchProjectDetails();
    // Then fetch tasks
    fetchTasks();
  }, [fetchTasks, fetchProjectDetails]);

  // Handler for search change
  const handleSearchChange = (value: string) => {
    console.log('Search term being set to:', value);
    setSearch(value);
    setCurrentPage(1); // Reset to first page
  };

  // Handler for status filter change
  const handleStatusChange = (value: string | undefined) => {
    console.log('Status being set to:', value);
    setStatus(value);
    setCurrentPage(1); // Reset to first page
  };

  return {
    tasks: filteredTasks,
    loading,
    error,
    projectName,
    currentPage,
    totalPages,
    search,
    status,
    setCurrentPage,
    handleSearchChange,
    handleStatusChange,
    refreshTasks: fetchTasks,
    setProjectNameDirectly,
  };
}

export default useProjectTasksByProject;
