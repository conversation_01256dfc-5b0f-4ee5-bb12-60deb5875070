// src/hooks/useProjectTaskDelete.ts
import { useState } from 'react';
import { toast } from 'sonner';
import { projectTaskApi } from '@/lib/api/project-task';

interface UseProjectTaskDeleteProps {
  onSuccess?: (taskId: string) => void;
}

/**
 * Custom hook for handling project task deletion
 */
export function useProjectTaskDelete({
  onSuccess,
}: UseProjectTaskDeleteProps = {}) {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [isConfirmOpen, setIsConfirmOpen] = useState(false);
  const [taskToDelete, setTaskToDelete] = useState<string | null>(null);

  /**
   * Open the deletion confirmation dialog
   */
  const confirmDelete = (taskId: string) => {
    setTaskToDelete(taskId);
    setIsConfirmOpen(true);
    setError(null);
  };

  /**
   * Cancel the deletion process
   */
  const cancelDelete = () => {
    setIsConfirmOpen(false);
    setTaskToDelete(null);
    setError(null);
  };

  /**
   * Proceed with deleting the task
   */
  const deleteTask = async () => {
    if (!taskToDelete) {
      setError('No task selected for deletion');
      return { success: false, error: 'No task selected for deletion' };
    }

    setLoading(true);
    setError(null);

    try {
      const response = await projectTaskApi.deleteProjectTask(taskToDelete);

      if (response.success) {
        toast.success('Tugas proyek berhasil dihapus');
        setIsConfirmOpen(false);

        // Call the success callback if provided
        if (onSuccess) {
          onSuccess(taskToDelete);
        }

        // Reset state
        const deletedTaskId = taskToDelete;
        setTaskToDelete(null);

        return { success: true, taskId: deletedTaskId };
      } else {
        const errorMessage = response.message || 'Gagal menghapus tugas proyek';
        setError(errorMessage);
        toast.error(errorMessage);
        return { success: false, error: errorMessage };
      }
    } catch (err: unknown) {
      const errorObj = err as { message?: string };
      const errorMessage =
        errorObj.message || 'Terjadi kesalahan saat menghapus tugas';
      console.error('Error deleting task:', err);
      setError(errorMessage);
      toast.error(errorMessage);
      return { success: false, error: errorMessage };
    } finally {
      setLoading(false);
    }
  };

  return {
    // State
    loading,
    error,
    isConfirmOpen,
    taskToDelete,

    // Actions
    confirmDelete,
    cancelDelete,
    deleteTask,
  };
}

export default useProjectTaskDelete;
