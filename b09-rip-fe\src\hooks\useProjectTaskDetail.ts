// src/hooks/useProjectTaskDetail.ts
'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { toast } from 'sonner';
import { ProjectTask, UpdateProjectTaskRequest } from '@/types/project-task';
import { projectTaskApi } from '@/lib/api/project-task';

export const useProjectTaskDetail = (id: string) => {
  const [task, setTask] = useState<ProjectTask | null>(null);
  const [loading, setLoading] = useState(true);
  const [updating, setUpdating] = useState(false);
  const [deleting, setDeleting] = useState(false);
  const [isEditing, setIsEditing] = useState(false);
  const router = useRouter();

  useEffect(() => {
    const fetchTask = async () => {
      setLoading(true);
      try {
        const response = await projectTaskApi.getProjectTaskById(id);
        if (response.success && response.data) {
          setTask(response.data);
        } else {
          toast.error('Gagal memuat detail tugas');
        }
      } catch (error) {
        console.error('Error fetching project task:', error);
        toast.error('Terjadi kesalahan saat memuat detail tugas');
      } finally {
        setLoading(false);
      }
    };

    if (id) {
      fetchTask();
    }
  }, [id]);

  const handleEditToggle = () => {
    setIsEditing(true);
  };

  const handleCancelEdit = () => {
    setIsEditing(false);
  };

  const handleSaveChanges = async (data: UpdateProjectTaskRequest) => {
    if (!task) return;

    setUpdating(true);
    try {
      const response = await projectTaskApi.updateProjectTask(task.id, data);
      if (response.success && response.data) {
        setTask(response.data);
        setIsEditing(false);
        toast.success('Tugas berhasil diperbarui');
      } else {
        toast.error('Gagal memperbarui tugas');
      }
    } catch (error: unknown) {
      console.error('Error updating project task:', error);

      if (error && typeof error === 'object' && 'response' in error) {
        const errorResponse = error.response as { status?: number };
        if (errorResponse.status === 400) {
          toast.error('Data tidak valid. Silakan periksa kembali input Anda.');
        } else if (errorResponse.status === 401) {
          toast.error('Sesi expired. Silakan login kembali.');
        } else if (errorResponse.status === 403) {
          toast.error('Anda tidak memiliki izin untuk mengubah tugas ini.');
        } else {
          toast.error('Gagal memperbarui tugas. Silakan coba lagi nanti.');
        }
      } else {
        toast.error('Gagal memperbarui tugas. Silakan coba lagi nanti.');
      }
    } finally {
      setUpdating(false);
    }
  };

  const handleDelete = async () => {
    if (!task) return;

    setDeleting(true);
    try {
      const response = await projectTaskApi.deleteProjectTask(task.id);
      if (response.success) {
        toast.success('Tugas berhasil dihapus');
        router.push('/project-tasks');
      } else {
        toast.error('Gagal menghapus tugas');
        setDeleting(false);
      }
    } catch (error: unknown) {
      console.error('Error deleting project task:', error);

      if (error && typeof error === 'object' && 'response' in error) {
        const errorResponse = error.response as { status?: number };
        if (errorResponse.status === 401) {
          toast.error('Sesi expired. Silakan login kembali.');
        } else if (errorResponse.status === 403) {
          toast.error('Anda tidak memiliki izin untuk menghapus tugas ini.');
        } else {
          toast.error('Gagal menghapus tugas. Silakan coba lagi nanti.');
        }
      } else {
        toast.error('Gagal menghapus tugas. Silakan coba lagi nanti.');
      }
      setDeleting(false);
    }
  };

  return {
    task,
    loading,
    updating,
    isEditing,
    deleting,
    handleEditToggle,
    handleSaveChanges,
    handleCancelEdit,
    handleDelete,
  };
};

export default useProjectTaskDetail;
