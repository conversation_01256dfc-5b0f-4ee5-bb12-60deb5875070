//path: src/hooks/useProjectTaskForm.ts

import { useState, useEffect } from 'react';
import { toast } from 'sonner';
import { projectTaskApi } from '@/lib/api/project-task';
import {
  ProjectTask,
  CreateProjectTaskRequest,
  UpdateProjectTaskRequest,
} from '@/types/project-task';
import { useRouter } from 'next/navigation';

interface UseProjectTaskFormProps {
  taskId?: string; // If provided, we're in edit mode
  projectId?: string; // If provided, pre-select the project
  onSuccess?: (task: ProjectTask) => void;
}

/**
 * Custom hook for managing project task form operations (create/update)
 */
export function useProjectTaskForm({
  taskId,
  projectId,
  onSuccess,
}: UseProjectTaskFormProps = {}) {
  const router = useRouter();
  const isEditMode = !!taskId;

  // Form state
  const [initialData, setInitialData] = useState<Partial<ProjectTask> | null>(
    null
  );
  const [loading, setLoading] = useState(false);
  const [initialLoading, setInitialLoading] = useState(isEditMode);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState(false);

  // Fetch task data if in edit mode
  useEffect(() => {
    const fetchTaskData = async () => {
      if (!isEditMode) {
        setInitialLoading(false);
        if (projectId) {
          setInitialData({ project_id: projectId });
        }
        return;
      }

      setInitialLoading(true);

      try {
        const response = await projectTaskApi.getProjectTaskById(taskId);

        if (response.success && response.data) {
          setInitialData(response.data);
          setError(null);
        } else {
          setError(response.message || 'Failed to fetch task data');
          toast.error('Gagal memuat data tugas');
          router.back();
        }
      } catch (err: unknown) {
        console.error('Error fetching task:', err);
        const errorObj = err as { message?: string };
        setError(errorObj.message || 'An unexpected error occurred');
        toast.error(errorObj.message || 'Terjadi kesalahan saat memuat data');
        router.back();
      } finally {
        setInitialLoading(false);
      }
    };

    fetchTaskData();
  }, [isEditMode, taskId, projectId, router]);

  // Handle form submission for create/update
  const handleSubmit = async (
    values: CreateProjectTaskRequest | UpdateProjectTaskRequest
  ) => {
    setLoading(true);
    setError(null);
    setSuccess(false);

    try {
      let response;

      if (isEditMode && taskId) {
        // Update existing task
        response = await projectTaskApi.updateProjectTask(
          taskId,
          values as UpdateProjectTaskRequest
        );
      } else {
        // Create new task
        response = await projectTaskApi.createProjectTask(
          values as CreateProjectTaskRequest
        );
      }

      if (response.success && response.data) {
        setSuccess(true);
        toast.success(
          isEditMode
            ? 'Tugas proyek berhasil diperbarui'
            : 'Tugas proyek berhasil dibuat'
        );

        // Call success callback if provided
        if (onSuccess) {
          onSuccess(response.data);
        }

        // Navigate based on context
        if (!isEditMode) {
          // For new tasks, go to the detail page
          router.push(`/project/task/${response.data.id}`);
        } else {
          // For edits, go back
          router.back();
        }

        return { success: true, data: response.data };
      } else {
        setError(response.message || 'Failed to save task');
        toast.error(response.message || 'Gagal menyimpan tugas proyek');
        return { success: false, error: response.message };
      }
    } catch (err: unknown) {
      console.error('Error saving task:', err);
      const errorObj = err as { message?: string };
      setError(errorObj.message || 'An unexpected error occurred');
      toast.error(errorObj.message || 'Terjadi kesalahan saat menyimpan');
      return { success: false, error: errorObj.message };
    } finally {
      setLoading(false);
    }
  };

  return {
    // Mode
    isEditMode,

    // Data
    initialData,

    // State
    loading,
    initialLoading,
    error,
    success,

    // Actions
    handleSubmit,
  };
}

export default useProjectTaskForm;
