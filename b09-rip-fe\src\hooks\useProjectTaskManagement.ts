import { useState, useEffect, useCallback } from 'react';
import { toast } from 'sonner';
import { projectTaskApi } from '@/lib/api/project-task';
import { ProjectTask, ProjectTaskFilterParams } from '@/types/project-task';

interface UseProjectTaskManagementProps {
  projectId?: string;
}

export function useProjectTaskManagement({
  projectId,
}: UseProjectTaskManagementProps = {}) {
  // Get project ID from URL if available
  const getProjectIdFromUrl = () => {
    if (typeof window !== 'undefined') {
      const urlParams = new URLSearchParams(window.location.search);
      return urlParams.get('projectId') || projectId;
    }
    return projectId;
  };

  // State for tasks and pagination
  const [tasks, setTasks] = useState<ProjectTask[]>([]);
  const [loading, setLoading] = useState(true);

  // Pagination state
  const [totalPages, setTotalPages] = useState(1);
  const [totalItems, setTotalItems] = useState(0);
  const [currentPage, setCurrentPage] = useState(1);

  // Filter states
  const [search, setSearch] = useState('');
  const [status, setStatus] = useState<string | undefined>(undefined);
  const [selectedProjectId, setSelectedProjectId] = useState<
    string | undefined
  >(getProjectIdFromUrl());

  // Update URL when project filter changes
  const updateUrlWithProjectId = useCallback(() => {
    if (typeof window !== 'undefined') {
      const url = new URL(window.location.href);

      if (selectedProjectId) {
        url.searchParams.set('projectId', selectedProjectId);
      } else {
        url.searchParams.delete('projectId');
      }

      // Update URL without page refresh
      window.history.pushState({}, '', url.toString());
    }
  }, [selectedProjectId]);

  // Define the fetch function
  const fetchTasks = useCallback(async () => {
    setLoading(true);
    try {
      // Prepare API params
      const params: ProjectTaskFilterParams = {
        page: currentPage,
        pageSize: 10,
        search: search || undefined,
        completion_status: status,
        project_id: selectedProjectId,
      };

      // Log the search parameters being used
      console.log('Fetching tasks with params:', params);

      // Call API
      const response = await projectTaskApi.getProjectTasks(params);

      console.log('API response:', response);

      if (response.success && response.data) {
        // Extract tasks - simplest approach
        let extractedTasks: ProjectTask[] = [];

        // Try to extract from data.items
        if (response.data.items) {
          if (Array.isArray(response.data.items)) {
            extractedTasks = response.data.items;
          }
          // Try nested structure
          else if (
            typeof response.data.items === 'object' &&
            'items' in response.data.items
          ) {
            extractedTasks = Array.isArray(
              (response.data.items as { items: ProjectTask[] }).items
            )
              ? (response.data.items as { items: ProjectTask[] }).items
              : [];
          }
        }
        // Try direct array
        else if (Array.isArray(response.data)) {
          extractedTasks = response.data;
        }

        setTasks(extractedTasks || []);

        // Handle pagination
        const pagination = response.data.pagination;
        if (pagination) {
          setTotalPages(pagination.pageCount || 1);
          setTotalItems(pagination.total || extractedTasks.length);
        } else {
          setTotalPages(1);
          setTotalItems(extractedTasks.length);
        }
      } else {
        toast.error('Gagal memuat data tugas proyek');
        setTasks([]);
      }
    } catch (error: unknown) {
      console.error('Error fetching tasks:', error);
      toast.error('Terjadi kesalahan saat memuat data');
      setTasks([]);
    } finally {
      setLoading(false);
    }
  }, [currentPage, search, status, selectedProjectId]);

  // Update URL when project ID changes
  useEffect(() => {
    updateUrlWithProjectId();
  }, [selectedProjectId, updateUrlWithProjectId]);

  // Fetch when dependencies change
  useEffect(() => {
    // Add a small delay before fetching to avoid too many requests
    const timer = setTimeout(() => {
      fetchTasks();
    }, 300);

    return () => clearTimeout(timer);
  }, [currentPage, search, status, selectedProjectId, fetchTasks]);

  // Handle search change with debounce
  const handleSearchChange = (value: string) => {
    setSearch(value);
    setCurrentPage(1); // Reset to first page on new search
  };

  // Handle status filter change
  const handleStatusChange = (value: string | undefined) => {
    setStatus(value);
    setCurrentPage(1); // Reset to first page on new filter
  };

  // Handle project filter change
  const handleProjectChange = (value: string | undefined) => {
    setSelectedProjectId(value);
    setCurrentPage(1); // Reset to first page
    // URL will be updated in the useEffect
  };

  return {
    tasks,
    loading,
    totalPages,
    totalItems,
    currentPage,
    search,
    status,
    selectedProjectId,
    handleSearchChange,
    handleStatusChange,
    handleProjectChange,
    setCurrentPage,
    refreshTasks: fetchTasks,
  };
}

export default useProjectTaskManagement;
