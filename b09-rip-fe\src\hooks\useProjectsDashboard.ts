// src/hooks/useProjectsDashboard.ts
import { useState, useEffect, useCallback } from 'react';
import { toast } from 'sonner';
import { ProjectsDashboardData } from '@/types/projects-dashboard';
import { projectApi } from '@/lib/api/project';
import { ApiError } from '@/types/api';

/**
 * Custom hook for fetching and managing projects dashboard data
 */
export function useProjectsDashboard() {
  const [dashboardData, setDashboardData] = useState<ProjectsDashboardData | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  /**
   * Fetch dashboard data from the API
   */
  const fetchDashboardData = useCallback(async () => {
    setLoading(true);
    setError(null);

    try {
      const response = await projectApi.getProjectsDashboard();
      
      if (response.success && response.data) {
        setDashboardData(response.data);
      } else {
        setError(response.message || 'Failed to fetch dashboard data');
        toast.error(`Error: ${response.message || 'Failed to fetch dashboard data'}`);
      }
    } catch (err) {
      const apiError = err as ApiError;
      setError(apiError.message || 'An error occurred while fetching dashboard data');
      toast.error(`Error: ${apiError.message || 'Failed to fetch dashboard data'}`);
    } finally {
      setLoading(false);
    }
  }, []);

  // Fetch dashboard data on initial render
  useEffect(() => {
    fetchDashboardData();
  }, [fetchDashboardData]);

  return {
    dashboardData,
    loading,
    error,
    refreshDashboard: fetchDashboardData
  };
}
