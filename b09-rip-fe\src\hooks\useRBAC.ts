import { useAuthStore } from '@/lib/store/auth-store';
import { JWTManager } from '@/lib/auth/jwt';
import { UserRole } from '@/types/auth';

export function useRBAC() {
  const user = useAuthStore((state) => state.user);
  const loading = useAuthStore((state) => state.loading);

  const hasRole = (allowedRoles: UserRole[]): boolean => {
    const userRole = user?.role || JWTManager.getUserRole();

    // If user is Admin, allow access to everything
    if (userRole === 'Admin') {
      return true;
    }

    // Otherwise, check if user has one of the allowed roles
    return userRole ? allowedRoles.includes(userRole as UserRole) : false;
  };

  const isAdmin = (): boolean => {
    const userRole = user?.role || JWTManager.getUserRole();
    return userRole === 'Admin';
  };

  return { hasRole, isAdmin, loading };
}
