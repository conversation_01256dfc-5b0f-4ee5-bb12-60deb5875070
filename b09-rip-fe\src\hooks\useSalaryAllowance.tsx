import { useState, useEffect, useCallback } from 'react';
import { allowanceApi } from '@/lib/api/allowance';
import { Allowance } from '@/types/salary';
import { toast } from 'sonner';

export function useSalaryAllowances(salaryId: string) {
  const [allowances, setAllowances] = useState<Allowance[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [refreshTrigger, setRefreshTrigger] = useState(0);

  // Fetch allowances
  const fetchAllowances = useCallback(async () => {
    try {
      setLoading(true);
      const response = await allowanceApi.getAllowancesBySalaryId(salaryId);
      if (response.success) {
        // API returns data directly as an array
        setAllowances(response.data);
      } else {
        setError(response.message || 'Failed to fetch allowances');
      }
    } catch (err) {
      console.error('Error fetching allowances:', err);
      setError(
        err instanceof Error ? err.message : 'An unknown error occurred'
      );
    } finally {
      setLoading(false);
    }
  }, [salaryId]);

  // Initial fetch
  useEffect(() => {
    fetchAllowances();
  }, [fetchAllowances, refreshTrigger]);

  // Refresh data
  const refreshData = useCallback(() => {
    setRefreshTrigger((prev) => prev + 1);
  }, []);

  // Update allowance
  const updateAllowance = async (
    id: string,
    data: Partial<Allowance>
  ): Promise<boolean> => {
    try {
      const response = await allowanceApi.updateAllowance(id, data);
      if (response.success) {
        toast.success('Allowance berhasil diperbarui');
        refreshData();
        return true;
      } else {
        toast.error(`Gagal memperbarui allowance: ${response.message}`);
        return false;
      }
    } catch (err) {
      console.error('Error updating allowance:', err);
      toast.error('Terjadi kesalahan saat memperbarui allowance');
      return false;
    }
  };

  // Delete allowance
  const deleteAllowance = async (id: string): Promise<boolean> => {
    try {
      const response = await allowanceApi.deleteAllowance(id);
      if (response.success) {
        toast.success('Allowance berhasil dihapus');
        refreshData();
        return true;
      } else {
        toast.error(`Gagal menghapus allowance: ${response.message}`);
        return false;
      }
    } catch (err) {
      console.error('Error deleting allowance:', err);
      toast.error('Terjadi kesalahan saat menghapus allowance');
      return false;
    }
  };

  return {
    allowances,
    loading,
    error,
    refreshData,
    updateAllowance,
    deleteAllowance,
  };
}
