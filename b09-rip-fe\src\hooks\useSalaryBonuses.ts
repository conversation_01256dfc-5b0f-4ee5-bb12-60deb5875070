import { useState, useEffect, useCallback } from 'react';
import { bonusApi } from '@/lib/api/bonus';
import { Bonus } from '@/types/salary';
import { toast } from 'sonner';

export function useSalaryBonuses(salaryId: string) {
  const [bonuses, setBonuses] = useState<Bonus[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [refreshTrigger, setRefreshTrigger] = useState(0);

  // Fetch bonuses
  const fetchBonuses = useCallback(async () => {
    try {
      setLoading(true);
      const response = await bonusApi.getBonusesBySalaryId(salaryId);
      if (response.success) {
        // API returns data directly as an array
        setBonuses(response.data);
      } else {
        setError(response.message || 'Failed to fetch bonuses');
      }
    } catch (err) {
      console.error('Error fetching bonuses:', err);
      setError(
        err instanceof Error ? err.message : 'An unknown error occurred'
      );
    } finally {
      setLoading(false);
    }
  }, [salaryId]);

  // Initial fetch
  useEffect(() => {
    fetchBonuses();
  }, [fetchBonuses, refreshTrigger]);

  // Refresh data
  const refreshData = useCallback(() => {
    setRefreshTrigger((prev) => prev + 1);
  }, []);

  // Update bonus
  const updateBonus = async (
    id: string,
    data: Partial<Bonus>
  ): Promise<boolean> => {
    try {
      const response = await bonusApi.updateBonus(id, data);
      if (response.success) {
        toast.success('Bonus berhasil diperbarui');
        refreshData();
        return true;
      } else {
        toast.error(`Gagal memperbarui bonus: ${response.message}`);
        return false;
      }
    } catch (err) {
      console.error('Error updating bonus:', err);
      toast.error('Terjadi kesalahan saat memperbarui bonus');
      return false;
    }
  };

  // Delete bonus
  const deleteBonus = async (id: string): Promise<boolean> => {
    try {
      const response = await bonusApi.deleteBonus(id);
      if (response.success) {
        toast.success('Bonus berhasil dihapus');
        refreshData();
        return true;
      } else {
        toast.error(`Gagal menghapus bonus: ${response.message}`);
        return false;
      }
    } catch (err) {
      console.error('Error deleting bonus:', err);
      toast.error('Terjadi kesalahan saat menghapus bonus');
      return false;
    }
  };

  return {
    bonuses,
    loading,
    error,
    refreshData,
    updateBonus,
    deleteBonus,
  };
}
