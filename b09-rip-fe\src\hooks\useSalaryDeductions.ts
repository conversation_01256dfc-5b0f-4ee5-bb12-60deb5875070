import { useState, useEffect, useCallback } from 'react';
import { toast } from 'sonner';
import { deductionApi, UpdateDeductionDto } from '@/lib/api/deduction';
import { Deduction } from '@/types/salary';

export const useSalaryDeductions = (salaryId: string) => {
  const [deductions, setDeductions] = useState<Deduction[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [refreshTrigger, setRefreshTrigger] = useState(0);

  // Load deductions
  const loadDeductions = useCallback(async () => {
    setLoading(true);
    setError(null);
    try {
      const response = await deductionApi.getBySalaryId(salaryId);
      if (response.success) {
        // The API returns an array directly in the data field
        setDeductions(Array.isArray(response.data) ? response.data : []);
      } else {
        setError(response.message || 'Failed to load deductions');
        toast.error('Gagal memuat daftar potongan');
      }
    } catch (err: unknown) {
      const errorObj = err as { message?: string };
      setError(errorObj.message || 'Failed to load deductions');
      toast.error('Gagal memuat daftar potongan');
    } finally {
      setLoading(false);
    }
  }, [salaryId]);

  // Update deduction
  const updateDeduction = async (id: string, data: UpdateDeductionDto) => {
    try {
      const response = await deductionApi.update(id, data);
      if (response.success) {
        // Refresh the deductions list to get the updated data
        loadDeductions();
        toast.success('Potongan berhasil diperbarui');
        return true;
      } else {
        toast.error(response.message || 'Gagal memperbarui potongan');
        return false;
      }
    } catch (err: unknown) {
      const errorObj = err as { message?: string };
      toast.error(errorObj.message || 'Gagal memperbarui potongan');
      return false;
    }
  };

  // Refresh data
  const refreshData = useCallback(() => {
    setRefreshTrigger((prev) => prev + 1);
  }, []);

  // Delete deduction
  const deleteDeduction = async (id: string) => {
    try {
      const response = await deductionApi.delete(id);
      if (response.success) {
        setDeductions((prev) => prev.filter((d) => d.id !== id));
        toast.success('Potongan berhasil dihapus');
        return true;
      } else {
        toast.error(response.message || 'Gagal menghapus potongan');
        return false;
      }
    } catch (err: unknown) {
      const errorObj = err as { message?: string };
      toast.error(errorObj.message || 'Gagal menghapus potongan');
      return false;
    }
  };

  // Load deductions on mount and when salaryId changes or refresh is triggered
  useEffect(() => {
    if (salaryId) {
      loadDeductions();
    }
  }, [salaryId, loadDeductions, refreshTrigger]);

  return {
    deductions,
    loading,
    error,
    updateDeduction,
    deleteDeduction,
    refreshDeductions: loadDeductions,
    refreshData,
  };
};
