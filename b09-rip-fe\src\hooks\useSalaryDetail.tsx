import { useState, useEffect, useCallback } from 'react';
import { toast } from 'sonner';
import { useRouter } from 'next/navigation';
import { salaryApi } from '@/lib/api/salary';
import { SalaryRecord } from '@/types/salary';
import { PaymentStatus } from '@/components/salary/SalaryStatusBadge';

export function useSalaryDetail(id: string) {
  const router = useRouter();
  const [salaryData, setSalaryData] = useState<SalaryRecord | null>(null);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false); // New state for refresh loading
  const [error, setError] = useState<string | null>(null);
  const [refreshTrigger, setRefreshTrigger] = useState(0); // New state to trigger refreshes

  // Delete states
  const [deleteLoading, setDeleteLoading] = useState(false);

  // Status update states
  const [statusUpdateLoading, setStatusUpdateLoading] = useState(false);

  // Amount update states
  const [amountUpdateLoading, setAmountUpdateLoading] = useState(false);

  // Extract fetch logic into a separate function
  const fetchSalaryData = useCallback(
    async (isRefresh = false) => {
      if (!id) {
        setError('Invalid salary ID');
        setLoading(false);
        return;
      }

      try {
        // Set appropriate loading state based on whether this is initial load or refresh
        if (isRefresh) {
          setRefreshing(true);
        } else {
          setLoading(true);
        }

        const response = await salaryApi.getSalaryById(id);

        if (!response.success) {
          if (
            response.error?.code === 'NOT_FOUND' ||
            (response.message &&
              response.message.toLowerCase().includes('not found'))
          ) {
            setError('not_found');
          } else {
            throw new Error(response.message || 'Failed to fetch salary data');
          }
          return;
        }

        if (response.data) {
          setSalaryData(response.data);
        } else {
          throw new Error('No data received from API');
        }
      } catch (err) {
        console.error('Error fetching salary data:', err);

        // Determine if this is a not found error
        const errorMsg = err instanceof Error ? err.message : String(err);

        if (isRefresh) {
          // For refreshes, show a toast but don't change the error state
          toast.error(`Failed to refresh data: ${errorMsg}`);
        } else {
          setError(
            errorMsg.toLowerCase().includes('not found')
              ? 'not_found'
              : errorMsg || 'An unknown error occurred'
          );
        }
      } finally {
        if (isRefresh) {
          setRefreshing(false);
        } else {
          setLoading(false);
        }
      }
    },
    [id]
  );

  // Initial data fetch
  useEffect(() => {
    fetchSalaryData(false);
  }, [fetchSalaryData]);

  // Refresh trigger effect
  useEffect(() => {
    if (refreshTrigger > 0) {
      fetchSalaryData(true);
    }
  }, [refreshTrigger, fetchSalaryData]);

  // Function to trigger a refresh
  const refreshData = useCallback(() => {
    // Simple approach to trigger a refresh
    setRefreshTrigger((prev) => prev + 1);
  }, []);

  // Update payment status
  const updatePaymentStatus = async (
    status: PaymentStatus
  ): Promise<boolean> => {
    if (!salaryData) return false;

    try {
      setStatusUpdateLoading(true);
      // Use the new unified API endpoint
      const response = await salaryApi.updateSalary(id, {
        payment_status: status,
      });

      if (response.success) {
        // Update local state
        setSalaryData({
          ...salaryData,
          payment_status: status,
        });

        // Always fetch fresh data to ensure all calculations are updated
        fetchSalaryData(true);

        return true;
      } else {
        toast.error(`Gagal memperbarui status: ${response.message}`);
        return false;
      }
    } catch (err) {
      console.error('Error updating payment status:', err);
      toast.error('Terjadi kesalahan saat memperbarui status pembayaran');
      return false;
    } finally {
      setStatusUpdateLoading(false);
    }
  };

  // Update base salary
  const updateAmounts = async (newBaseSalary: number): Promise<boolean> => {
    if (!salaryData) return false;

    try {
      setAmountUpdateLoading(true);

      const response = await salaryApi.updateSalary(id, {
        base_salary: newBaseSalary,
      });

      if (response.success) {
        // Update local state
        setSalaryData({
          ...salaryData,
          base_salary: newBaseSalary,
        });
        toast.success('Gaji pokok berhasil diperbarui');
        return true;
      } else {
        toast.error(`Gagal memperbarui gaji pokok: ${response.message}`);
        return false;
      }
    } catch (err) {
      console.error('Error updating base salary:', err);
      toast.error('Terjadi kesalahan saat memperbarui gaji pokok');
      return false;
    } finally {
      setAmountUpdateLoading(false);
    }
  };

  // Delete salary
  const deleteSalary = async (): Promise<boolean> => {
    if (!id) return false;

    try {
      setDeleteLoading(true);
      // Replace with actual delete API call when available
      // const response = await salaryApi.deleteSalary(id);

      // Mock successful deletion for now
      toast.success('Data penggajian berhasil dihapus');
      router.push('/employee/salary');
      return true;
    } catch (err) {
      console.error('Error deleting salary:', err);
      toast.error('Terjadi kesalahan saat menghapus data penggajian');
      return false;
    } finally {
      setDeleteLoading(false);
    }
  };

  return {
    salaryData,
    loading,
    refreshing,
    error,
    deleteSalary,
    deleteLoading,
    updatePaymentStatus,
    statusUpdateLoading,
    updateAmounts,
    amountUpdateLoading,
    refreshData,
  };
}
