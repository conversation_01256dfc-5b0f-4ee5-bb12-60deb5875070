// src/hooks/useSalaryHistory.ts
import { useState, useEffect } from 'react';
import { salaryApi } from '@/lib/api/salary';
import { SalaryHistoryRecord } from '@/types/salary';

export function useSalaryHistory(salaryId: string) {
  const [history, setHistory] = useState<SalaryHistoryRecord[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchSalaryHistory = async () => {
      try {
        setLoading(true);
        const response = await salaryApi.getSalaryHistory(salaryId);
        
        if (response.success) {
          setHistory(response.data.items || []);
        } else {
          setError(response.message || 'Failed to fetch salary history');
        }
      } catch (err) {
        console.error('Error fetching salary history:', err);
        setError(err instanceof Error ? err.message : 'An unknown error occurred');
      } finally {
        setLoading(false);
      }
    };

    fetchSalaryHistory();
  }, [salaryId]);

  return { history, loading, error };
}
