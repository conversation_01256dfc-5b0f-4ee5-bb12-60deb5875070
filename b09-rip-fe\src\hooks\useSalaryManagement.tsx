import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { salaryApi } from '@/lib/api/salary';
import { ApiError } from '@/types/api';

interface DisplaySalary {
  id: number;
  salaryId: string;
  employeeId: string;
  name: string;
  role: string;
  period: string;
  totalSalary: string;
  paymentStatus: string;
}

export const useSalaryManagement = () => {
  const router = useRouter();
  const [salaries, setSalaries] = useState<DisplaySalary[]>([]);
  const [filteredSalaries, setFilteredSalaries] = useState<DisplaySalary[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Pagination state
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [itemsPerPage] = useState(10);

  // Filter state
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedPeriod, setSelectedPeriod] = useState<string | undefined>(
    undefined
  );
  const [uniquePeriods, setUniquePeriods] = useState<string[]>([]);

  // Total items is derived from filtered salaries
  const totalItems = filteredSalaries.length;

  // Format the period from YYYY-MM to Month YYYY
  const formatPeriod = (period: string): string => {
    const [year, month] = period.split('-');
    const monthNames = [
      'Januari',
      'Februari',
      'Maret',
      'April',
      'Mei',
      'Juni',
      'Juli',
      'Agustus',
      'September',
      'Oktober',
      'November',
      'Desember',
    ];
    return `${monthNames[parseInt(month) - 1]} ${year}`;
  };

  // Format currency to Indonesian Rupiah
  const formatCurrency = (amount: number): string => {
    return `Rp${amount.toLocaleString('id-ID')}`;
  };

  // Fetch salaries on component mount
  useEffect(() => {
    const fetchSalaries = async () => {
      try {
        setLoading(true);

        // Use the API client
        const response = await salaryApi.getSalaries();

        // Access the data array using optional chaining
        if (response.data?.items && Array.isArray(response.data.items)) {
          // Transform API data to display format
          const transformedData: DisplaySalary[] = response.data.items.map(
            (item, index: number) => ({
              id: index + 1,
              salaryId: item.id,
              employeeId: item.employee_id,
              name: item.employee_details.fullname,
              role: item.employee_details.role,
              period: formatPeriod(item.period),
              totalSalary: formatCurrency(item.total_salary),
              paymentStatus: item.payment_status,
            })
          );

          setSalaries(transformedData);

          // Extract unique periods for filter dropdown
          const periods = Array.from(
            new Set(transformedData.map((s) => s.period))
          );
          setUniquePeriods(periods);

          setError(null);
        } else {
          console.warn(
            'API returned success but data is not in expected format:',
            response
          );
          setError('Data tidak dalam format yang diharapkan');
        }
      } catch (err: unknown) {
        console.error('Error fetching salaries:', err);

        const apiError = err as ApiError;
        if (apiError.response?.status === 401) {
          setError('Sesi Anda telah berakhir. Silakan login kembali.');
        } else {
          setError(
            err instanceof Error ? err.message : 'An unknown error occurred'
          );
        }
      } finally {
        setLoading(false);
      }
    };

    fetchSalaries();
  }, []);

  // Apply filters and update pagination when filters or salaries change
  useEffect(() => {
    let filtered = [...salaries];

    // Apply search filter
    if (searchQuery) {
      filtered = filtered.filter(
        (salary) =>
          salary.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
          salary.salaryId.toLowerCase().includes(searchQuery.toLowerCase())
      );
    }

    // Apply period filter
    if (selectedPeriod && selectedPeriod !== 'all') {
      filtered = filtered.filter((salary) => salary.period === selectedPeriod);
    }

    // Update filtered salaries
    setFilteredSalaries(filtered);

    // Calculate total pages based on filtered data
    const newTotalPages = Math.ceil(filtered.length / itemsPerPage);
    setTotalPages(newTotalPages);

    // Reset to page 1 if current page would be out of bounds with new filter
    if (currentPage > newTotalPages && newTotalPages > 0) {
      setCurrentPage(1);
    }
  }, [
    salaries,
    searchQuery,
    selectedPeriod,
    itemsPerPage,
    currentPage,
    setCurrentPage,
    setTotalPages,
    setFilteredSalaries,
  ]);

  // Get paginated data from filtered results
  const getPaginatedSalaries = () => {
    // Safety check - ensure current page is valid
    const validCurrentPage = Math.min(
      Math.max(1, currentPage),
      Math.max(1, totalPages)
    );

    const startIndex = (validCurrentPage - 1) * itemsPerPage;
    const endIndex = startIndex + itemsPerPage;
    return filteredSalaries.slice(startIndex, endIndex);
  };

  // Handle view salary detail
  const handleViewDetail = (salaryId: string) => {
    router.push(`/employee/salary/${salaryId}`);
  };

  // Handle employee detail view
  const handleViewEmployeeDetail = (employeeId: string) => {
    router.push(`/employee/salary/employee/${employeeId}`);
  };

  // Handle search change
  const handleSearchChange = (value: string) => {
    setSearchQuery(value);
    setCurrentPage(1); // Reset to first page when search changes
  };

  // Handle period filter change
  const handlePeriodChange = (value: string) => {
    setSelectedPeriod(value === 'all' ? undefined : value);
    setCurrentPage(1); // Reset to first page when filter changes
  };

  return {
    salaries: getPaginatedSalaries(),
    allSalaries: filteredSalaries,
    loading,
    error,
    searchQuery,
    selectedPeriod,
    uniquePeriods,
    currentPage,
    totalPages,
    totalItems,
    setCurrentPage,
    handleSearchChange,
    handlePeriodChange,
    handleViewDetail,
    handleViewEmployeeDetail,
  };
};
