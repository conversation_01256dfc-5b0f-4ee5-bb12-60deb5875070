import { useState, useEffect } from 'react';
import { toast } from 'sonner';
import { useRouter } from 'next/navigation';
import {
  KpiProject,
  KpiProjectFilterParams,
  KpiProjectStatus,
  PaginatedKpiProjectsResponse,
} from '@/types/kpi-project';
import { kpiProjectApi } from '@/lib/api/kpi-project';
import { ApiError } from '@/types/api';

// Define the response type for KPI projects by project ID
interface KpiProjectsByProjectIdResponse {
  data: KpiProject[];
}

export const useViewingKpiProjects = (projectId?: string) => {
  const router = useRouter();
  const [kpiProjects, setKpiProjects] = useState<KpiProject[]>([]);
  const [loading, setLoading] = useState(true);

  // Pagination state
  const [totalItems, setTotalItems] = useState(0);
  const [totalPages, setTotalPages] = useState(1);
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize] = useState(10); // Assuming a fixed page size

  // Filter state
  const [search, setSearch] = useState('');
  const [status, setStatus] = useState<KpiProjectStatus | undefined>(undefined);

  // Fetch KPI projects when filters or pagination changes
  useEffect(() => {
    const fetchKpiProjects = async () => {
      setLoading(true);
      try {
        const params: KpiProjectFilterParams = {
          status: status === undefined ? undefined : status,
          page: currentPage,
          pageSize,
        };

        // If there's a search query, add it to the params
        if (search) {
          params.search = search;
        }

        // If there's a project ID, add it to the params
        if (projectId) {
          params.project_id = projectId;
        }

        // No need for role-based filters as the backend already handles this
        // when the /with-details endpoint is called

        let response;

        if (projectId) {
          // If projectId is provided, use the new endpoint with filter params
          console.log(
            'Fetching KPI Projects for project ID:',
            projectId,
            'with params:',
            params
          );
          // Create a new params object without projectId since it's in the URL path
          const filterParams: KpiProjectFilterParams = {
            search: params.search,
            status: params.status,
            page: params.page,
            pageSize: params.pageSize,
          };
          response = await kpiProjectApi.getKpiProjectsByProjectId(
            projectId,
            filterParams
          );
          console.log('KPI Projects by project ID response:', response);
        } else {
          // Otherwise, use the original endpoint
          console.log('Fetching KPI Projects with details, params:', params);
          response = await kpiProjectApi.getViewingKpiProjects(params);
          console.log('KPI Projects with details response:', response);
        }

        if (response.success && response.data) {
          // Handle both data structures - check for data field first, then items field
          let filteredProjects: KpiProject[] = [];
          let pagination = null;

          if ('pagination' in response.data) {
            // This is a PaginatedKpiProjectsResponse
            const paginatedData = response.data as PaginatedKpiProjectsResponse;
            filteredProjects = paginatedData.data || paginatedData.items || [];
            pagination = paginatedData.pagination;
          } else if ('data' in response.data) {
            // This is a KpiProjectsByProjectIdResponse
            const projectData = response.data as KpiProjectsByProjectIdResponse;
            filteredProjects = projectData.data || [];
            // Set default pagination since it's not provided
            pagination = {
              total: filteredProjects.length,
              page: 1,
              pageSize: filteredProjects.length,
              pageCount: 1,
            };
          }

          console.log('Filtered projects before processing:', filteredProjects);

          if (pagination) {
            setTotalItems(pagination.total || 0);
            setTotalPages(pagination.pageCount || 1);
          }

          setKpiProjects(filteredProjects);
        } else {
          toast.error('Failed to load KPI Projects');
          setKpiProjects([]);
          setTotalItems(0);
          setTotalPages(1);
        }
      } catch (error: unknown) {
        console.error('Error fetching KPI Projects:', error);

        // Log more detailed error information
        if (error instanceof Error) {
          console.error('Error message:', error.message);
          console.error('Error stack:', error.stack);
        }

        // Try to log the response data if available
        const apiError = error as ApiError;
        if (apiError.response) {
          console.error('API Error Response:', {
            status: apiError.response.status,
            statusText: apiError.response.statusText,
            data: apiError.response.data,
            headers: apiError.response.headers,
          });
        }

        if (apiError.response?.status === 401) {
          toast.error('Session expired. Please login again.');
        } else if (apiError.response?.status === 403) {
          toast.error('You do not have permission to view this page.');
        } else {
          toast.error('Failed to load KPI Projects. Please try again later.');
        }

        setKpiProjects([]);
        setTotalItems(0);
        setTotalPages(1);
      } finally {
        setLoading(false);
      }
    };

    fetchKpiProjects();
  }, [search, status, currentPage, pageSize, projectId]);

  const handleSearchChange = (value: string) => {
    setSearch(value);
    setCurrentPage(1); // Reset to page 1 when search changes
  };

  const handleStatusChange = (value: KpiProjectStatus | undefined) => {
    setStatus(value);
    setCurrentPage(1); // Reset to page 1 when status changes
  };

  const handleViewDetail = (kpiProject: KpiProject) => {
    router.push(`/kpi-project/with-details/${kpiProject.id}`);
  };

  return {
    kpiProjects,
    loading,
    totalItems,
    totalPages,
    currentPage,
    pageSize,
    search,
    status,
    handleViewDetail,
    handleSearchChange,
    handleStatusChange,
    setCurrentPage,
  };
};
