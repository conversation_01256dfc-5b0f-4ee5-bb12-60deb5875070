import api from './client';
import { ApiResponse } from '@/types/auth';
import {
  ActivateUserRequest,
  PaginatedUsersResponse,
  UserFilterParams,
} from '@/types/admin';

// Define a generic empty response type
type EmptyResponse = Record<string, never>;

/**
 * Admin API services
 */
export const adminApi = {
  /**
   * Get all users with filtering and pagination
   */
  getUsers: async (
    params: UserFilterParams = {}
  ): Promise<ApiResponse<PaginatedUsersResponse>> => {
    const response = await api.get<ApiResponse<PaginatedUsersResponse>>(
      '/v1/admin/users/',
      { params }
    );
    return response.data;
  },

  /**
   * Activate a user
   */
  activateUser: async (
    data: ActivateUserRequest
  ): Promise<ApiResponse<EmptyResponse>> => {
    const response = await api.patch<ApiResponse<EmptyResponse>>(
      '/v1/admin/users/activate',
      data
    );
    return response.data;
  },

  /**
   * Delete a user profile
   */
  deleteUser: async (id: string): Promise<ApiResponse<EmptyResponse>> => {
    const response = await api.delete<ApiResponse<EmptyResponse>>(
      `/v1/admin/users/${id}`
    );
    return response.data;
  },
};

export default adminApi;
