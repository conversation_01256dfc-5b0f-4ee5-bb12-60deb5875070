import api from './client';
import {
  ApiResponseSingle,
  // ApiResponseList, // Not used in this file
  Allowance,
  AllowanceSalaryType,
} from '@/types/salary';

export interface CreateAllowanceDto {
  salary_id: string;
  amount: number;
  allowance_type: AllowanceSalaryType;
  notes?: string;
}

export const allowanceApi = {
  // Create a new allowance
  createAllowance: async (
    data: CreateAllowanceDto
  ): Promise<ApiResponseSingle<Allowance>> => {
    const response = await api.post('/v1/allowances/', data);
    return response.data;
  },

  // Get allowances by salary ID
  getAllowancesBySalaryId: async (
    salaryId: string
  ): Promise<{
    success: boolean;
    message: string;
    data: Allowance[];
  }> => {
    const response = await api.get(`/v1/allowances/salary/${salaryId}`);
    return response.data;
  },

  // Get allowance by ID
  getAllowanceById: async (
    id: string
  ): Promise<ApiResponseSingle<Allowance>> => {
    const response = await api.get(`/v1/allowances/${id}`);
    return response.data;
  },

  // Update an allowance
  updateAllowance: async (
    id: string,
    data: Partial<CreateAllowanceDto>
  ): Promise<{
    success: boolean;
    message: string;
    data: Allowance;
  }> => {
    const response = await api.put(`/v1/allowances/${id}`, data);
    return response.data;
  },

  // Delete an allowance
  deleteAllowance: async (
    id: string
  ): Promise<{
    success: boolean;
    message: string;
    data: null;
  }> => {
    const response = await api.delete(`/v1/allowances/${id}`);
    return response.data;
  },
};

export default allowanceApi;
