import api from './client';
import { ApiResponse } from '@/types/auth';
import {
  Attendance,
  CreateAttendanceDto,
  ViewDailyAbsenceDto,
  PaginatedAttendancesResponse,
  AttendanceWithTasks,
  EmployeeAttendancePostParams,
} from '@/types/attendance';

/**
 * Attendance API services
 */
export const attendanceApi = {
  /**
   * Get all attendance records with filtering and pagination
   */
  getAttendances: async (
    params: ViewDailyAbsenceDto = {}
  ): Promise<ApiResponse<PaginatedAttendancesResponse>> => {
    const response = await api.get<ApiResponse<PaginatedAttendancesResponse>>(
      '/v1/attendances/',
      { params }
    );
    return response.data;
  },

  /**
   * Create a new attendance record (clock-in or clock-out)
   */
  createAttendance: async (
    data: CreateAttendanceDto
  ): Promise<ApiResponse<Attendance>> => {
    const response = await api.post<ApiResponse<Attendance>>(
      '/v1/attendances/',
      data
    );
    return response.data;
  },

  /**
   * Get today's attendance record for the current user
   */
  getTodayAttendance: async (): Promise<ApiResponse<AttendanceWithTasks>> => {
    const response = await api.get<ApiResponse<AttendanceWithTasks>>(
      '/v1/attendances/today'
    );
    return response.data;
  },

  /**
   * Get current user's attendance history
   */
  getMyAttendanceHistory: async (
    params: ViewDailyAbsenceDto = {}
  ): Promise<ApiResponse<PaginatedAttendancesResponse>> => {
    const response = await api.get<ApiResponse<PaginatedAttendancesResponse>>(
      '/v1/attendances/me',
      { params }
    );
    return response.data;
  },

  /**
   * Get attendance records for a specific employee using POST method
   */
  postEmployeeAttendance: async (
    params: EmployeeAttendancePostParams
  ): Promise<ApiResponse<PaginatedAttendancesResponse>> => {
    const response = await api.post<ApiResponse<PaginatedAttendancesResponse>>(
      '/v1/attendances/employee',
      params
    );
    return response.data;
  },
};
