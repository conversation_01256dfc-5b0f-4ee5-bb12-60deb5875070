import api from './client';
import {
  SignInRequest,
  SignUpRequest,
  AuthResponse,
  RefreshTokenRequest,
  ApiResponse,
  ProfileResponse,
} from '@/types/auth';

/**
 * Authentication API services
 */
export const authApi = {
  /**
   * Register a new user
   */
  signUp: async (
    data: SignUpRequest
  ): Promise<ApiResponse<AuthResponse['data']>> => {
    const response = await api.post<ApiResponse<AuthResponse['data']>>(
      '/v1/auth/sign-up',
      data
    );
    return response.data;
  },

  /**
   * Login with email and password
   */
  signIn: async (
    data: SignInRequest
  ): Promise<ApiResponse<AuthResponse['data']>> => {
    const response = await api.post<ApiResponse<AuthResponse['data']>>(
      '/v1/auth/sign-in',
      data
    );
    return response.data;
  },

  /**
   * Logout the current user
   */
  signOut: async (): Promise<ApiResponse<null>> => {
    const response = await api.post<ApiResponse<null>>('/v1/auth/sign-out');
    return response.data;
  },

  /**
   * Refresh the access token using a refresh token
   */
  refreshToken: async (
    refreshToken: string
  ): Promise<ApiResponse<AuthResponse['data']>> => {
    const data: RefreshTokenRequest = { refresh_token: refreshToken };
    const response = await api.post<ApiResponse<AuthResponse['data']>>(
      '/v1/auth/refresh',
      data
    );

    // If the response doesn't have session structure but has direct token properties,
    // transform it to match our expected structure
    if (response.data.success && response.data.data) {
      // Handle legacy API response format (direct properties)
      interface LegacyResponse {
        access_token?: string;
        refresh_token?: string;
        user?: Record<string, unknown>;
      }

      const data = response.data.data as LegacyResponse;

      if (data && 'access_token' in data && !('session' in data)) {
        const responseData = {
          session: {
            access_token: data.access_token,
            refresh_token: data.refresh_token,
            token_type: 'bearer',
            expires_in: 3600, // Default value
            expires_at: Math.floor(Date.now() / 1000) + 3600, // Default expiry in 1 hour
            user: data.user || {},
          },
          profile: data.user || {},
          user: data.user,
        };

        // Override with the transformed structure
        response.data.data = responseData as AuthResponse['data'];
      }
    }

    return response.data;
  },

  /**
   * Get the current user's profile
   */
  getProfile: async (): Promise<ApiResponse<ProfileResponse>> => {
    const response =
      await api.get<ApiResponse<ProfileResponse>>('/v1/auth/me/');
    return response.data;
  },
};

export default authApi;
