import api from './client';
import {
  ApiResponseSingle,
  // ApiResponseList, // Not used in this file
  Bonus,
  BonusSalaryType,
} from '@/types/salary';

export interface CreateBonusDto {
  salary_id: string;
  amount: number;
  bonus_type: BonusSalaryType;
  notes?: string;
  kpi_id?: string;
  project_id?: string;
}

export const bonusApi = {
  // Create a new bonus
  createBonus: async (
    data: CreateBonusDto
  ): Promise<ApiResponseSingle<Bonus>> => {
    const response = await api.post('/v1/bonuses/', data);
    return response.data;
  },

  // Get bonuses by salary ID
  getBonusesBySalaryId: async (
    salaryId: string
  ): Promise<{
    success: boolean;
    message: string;
    data: Bonus[];
  }> => {
    const response = await api.get(`/v1/bonuses/salary/${salaryId}`);
    return response.data;
  },

  // Get bonus by ID
  getBonusById: async (id: string): Promise<ApiResponseSingle<Bonus>> => {
    const response = await api.get(`/v1/bonuses/${id}`);
    return response.data;
  },

  // Update a bonus
  updateBonus: async (
    id: string,
    data: Partial<CreateBonusDto>
  ): Promise<{
    success: boolean;
    message: string;
    data: Bonus;
  }> => {
    const response = await api.put(`/v1/bonuses/${id}`, data);
    return response.data;
  },

  // Delete a bonus
  deleteBonus: async (
    id: string
  ): Promise<{
    success: boolean;
    message: string;
    data: null;
  }> => {
    const response = await api.delete(`/v1/bonuses/${id}`);
    return response.data;
  },
};

export default bonusApi;
