import axios, {
  AxiosError,
  AxiosInstance,
  InternalAxiosRequestConfig,
} from 'axios';
import { authService } from '@/lib/auth/auth-service';
import { toast } from 'sonner';

// Get the API URL from environment variables or default to localhost
const API_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:3000';

// Create a base axios instance with common configuration
export const api: AxiosInstance = axios.create({
  baseURL: API_URL,
  headers: {
    'Content-Type': 'application/json',
  },
});

// Function to handle redirection to login page (with debounce protection)
let isRedirectingToLogin = false;
const redirectToLogin = () => {
  // Prevent multiple redirects
  if (isRedirectingToLogin) return;
  isRedirectingToLogin = true;

  // Complete logout
  authService.logout();

  // Show toast notification for session expiration
  if (typeof window !== 'undefined') {
    toast.error('<PERSON><PERSON> Anda telah be<PERSON>. Silakan login kembali.', {
      position: 'top-right',
      duration: 5000,
      id: 'session-expired-toast',
    });

    // Delay the redirect slightly to ensure toast is visible
    setTimeout(() => {
      window.location.href = '/login';
      isRedirectingToLogin = false;
    }, 1000);
  }
};

// Request interceptor to attach auth token to requests
api.interceptors.request.use(
  (config) => {
    // Get token from centralized auth service
    const token = authService.getAccessToken();
    if (token) {
      config.headers['Authorization'] = `Bearer ${token}`;
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Response interceptor to handle token refresh on 401 errors
api.interceptors.response.use(
  (response) => response,
  async (error: AxiosError) => {
    const originalRequest = error.config;

    // Prevent infinite retry loops
    if (
      !originalRequest ||
      (originalRequest as RequestWithRetry)._retry === true
    ) {
      return Promise.reject(error);
    }

    // Adding type for the custom property
    interface RequestWithRetry extends InternalAxiosRequestConfig {
      _retry?: boolean;
    }

    // Safe cast
    const typedOriginalRequest = originalRequest as RequestWithRetry;

    // Check if this is a login or authentication-related endpoint
    const isAuthEndpoint =
      typedOriginalRequest.url?.includes('/auth/sign-in') ||
      typedOriginalRequest.url?.includes('/auth/sign-up') ||
      typedOriginalRequest.url?.includes('/auth/refresh');

    // If the error is 401 (Unauthorized)
    if (error.response?.status === 401) {
      // Don't attempt token refresh for auth endpoints
      if (isAuthEndpoint) {
        return Promise.reject(error);
      }

      // Mark this request as retried
      typedOriginalRequest._retry = true;

      // If refresh is already in progress, queue this request
      if (authService.isRefreshingTokens()) {
        console.log('Token refresh already in progress, queuing request');

        try {
          // Return a new promise that will resolve when the token is refreshed
          return new Promise((resolve, reject) => {
            authService.addToQueue({
              resolve,
              reject,
              config: originalRequest,
            });
          });
        } catch (queueError) {
          return Promise.reject(queueError);
        }
      }

      // Start a new token refresh process
      try {
        console.log('Starting token refresh process');
        const refreshSuccess = await authService.refreshTokens();

        if (refreshSuccess) {
          // If refresh was successful, get the new token and retry
          const newToken = authService.getAccessToken();
          console.log('Token refresh successful, retrying original request');

          // Retry original request with new token
          originalRequest.headers['Authorization'] = `Bearer ${newToken}`;
          return axios(originalRequest);
        } else {
          console.error('Token refresh failed, redirecting to login');
          // If refresh failed, logout user and redirect
          redirectToLogin();
          return Promise.reject(error);
        }
      } catch (refreshError) {
        console.error('Error during token refresh:', refreshError);
        // Only redirect to login if this wasn't a network error
        // This prevents logout during temporary connectivity issues
        if (axios.isAxiosError(refreshError) && !refreshError.response) {
          console.warn('Network error during refresh, not logging out');
          return Promise.reject(error);
        }

        redirectToLogin();
        return Promise.reject(refreshError);
      }
    } else if (error.response?.status === 403) {
      // Handle forbidden errors (e.g., incorrect permission)
      type ErrorData = {
        message?: string;
      };

      const errorData = error.response.data as ErrorData;
      console.error(
        'Access forbidden:',
        errorData.message || 'Insufficient permissions'
      );
    }

    return Promise.reject(error);
  }
);

// Export default api client
export default api;
