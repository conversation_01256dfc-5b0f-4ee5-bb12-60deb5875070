import api from './client';
import {
  Deduction,
  DeductionType,
  // ApiResponseList, // Not used in this file
  ApiResponseSingle,
} from '@/types/salary';

export interface CreateDeductionDto {
  salary_id: string;
  amount: number;
  deduction_type: DeductionType;
  notes?: string;
}

export interface UpdateDeductionDto {
  amount: number;
  deduction_type: DeductionType;
  notes?: string;
}

export const deductionApi = {
  // Create a new deduction
  createDeduction: async (
    data: CreateDeductionDto
  ): Promise<ApiResponseSingle<Deduction>> => {
    const response = await api.post('/v1/deductions/', data);
    return response.data;
  },

  // Get deductions by salary ID
  getBySalaryId: async (
    salaryId: string
  ): Promise<{
    success: boolean;
    message: string;
    data: Deduction[];
  }> => {
    const response = await api.get(`/v1/deductions/salary/${salaryId}`);
    return response.data;
  },

  // Get deduction by ID
  getById: async (id: string): Promise<ApiResponseSingle<Deduction>> => {
    const response = await api.get(`/v1/deductions/${id}`);
    return response.data;
  },

  // Update a deduction
  update: async (
    id: string,
    data: UpdateDeductionDto
  ): Promise<{
    success: boolean;
    message: string;
    data: Deduction;
  }> => {
    const response = await api.put(`/v1/deductions/${id}`, data);
    return response.data;
  },

  // Delete a deduction
  delete: async (
    id: string
  ): Promise<{
    success: boolean;
    message: string;
    data: null;
  }> => {
    const response = await api.delete(`/v1/deductions/${id}`);
    return response.data;
  },
};

export default deductionApi;
