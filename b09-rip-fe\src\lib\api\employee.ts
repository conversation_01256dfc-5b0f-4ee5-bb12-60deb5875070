import { api } from '@/lib/api/client';
import {
  Employee,
  UpdateEmployeeDto,
  GetAllEmployeesQuery,
  PaginatedResponse,
  ApiResponse,
} from '@/types/employee';

export const employeeApi = {
  /**
   * Get all employees with filtering and pagination
   */
  getEmployees: async (
    query?: GetAllEmployeesQuery
  ): Promise<ApiResponse<PaginatedResponse<Employee>>> => {
    try {
      const response = await api.get<ApiResponse<PaginatedResponse<Employee>>>(
        '/v1/employees',
        {
          params: query,
        }
      );
      return response.data;
    } catch (error) {
      console.error('Error fetching employees:', error);
      throw error;
    }
  },

  /**
   * Get a single employee by ID
   */
  getEmployeeById: async (id: string): Promise<ApiResponse<Employee>> => {
    try {
      const response = await api.get<ApiResponse<Employee>>(
        `/v1/employees/${id}`
      );
      return response.data;
    } catch (error) {
      console.error(`Error fetching employee ${id}:`, error);
      throw error;
    }
  },

  /**
   * Update an employee's information
   */
  updateEmployee: async (
    id: string,
    data: UpdateEmployeeDto
  ): Promise<ApiResponse<Employee>> => {
    try {
      // Create a clean object with only the backend expected fields
      // and ensure data is in the format expected by the backend
      const apiPayload = {
        dob: data.dob || null,
        address: data.address || null,
        bank_account: data.bank_account || null,
        bank_name: data.bank_name || null,
        employment_status: data.employment_status || null,
        presence_status: data.presence_status || null,
        department: data.department || null,
        start_date: data.start_date || null,
        salary_id: data.salary_id || null,
      };

      // Remove null values to avoid sending them to the API
      const cleanPayload = Object.fromEntries(
        Object.entries(apiPayload).filter(([, value]) => value !== null)
      );

      // Log the payload being sent to help with debugging

      const response = await api.patch<ApiResponse<Employee>>(
        `/v1/employees/${id}`,
        cleanPayload
      );

      return response.data;
    } catch (error) {
      console.error(`Error updating employee ${id}:`, error);
      throw error;
    }
  },
};

// Export individual functions for easier imports
export const getEmployees = employeeApi.getEmployees;
export const getEmployeeById = employeeApi.getEmployeeById;
export const updateEmployee = employeeApi.updateEmployee;
