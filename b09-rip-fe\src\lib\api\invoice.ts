import api from './client';
import { ApiResponse } from '@/types/invoice';
import {
  Invoice,
  InvoiceQueryParams,
  InvoicesResponse,
  CreateInvoiceDto,
  PaymentStatus,
  PaymentProof,
  InvoiceHistoryResponse,
} from '@/types/invoice';

export const invoiceApi = {
  /**
   * Get all invoices with filtering and pagination
   */
  getInvoices: async (
    params: InvoiceQueryParams = {}
  ): Promise<ApiResponse<InvoicesResponse>> => {
    try {
      const response = await api.get<ApiResponse<InvoicesResponse>>(
        '/v1/invoices',
        {
          params,
        }
      );
      return response.data;
    } catch (error) {
      console.error('Error fetching invoices:', error);
      throw error;
    }
  },

  /**
   * Get a single invoice by ID
   */
  getInvoiceById: async (id: string): Promise<ApiResponse<Invoice>> => {
    try {
      const response = await api.get<ApiResponse<Invoice>>(
        `/v1/invoices/${id}`
      );
      return response.data;
    } catch (error) {
      console.error(`Error fetching invoice ${id}:`, error);
      throw error;
    }
  },

  /**
   * Create a new invoice
   */
  createInvoice: async (
    data: CreateInvoiceDto
  ): Promise<ApiResponse<Invoice>> => {
    try {
      const response = await api.post<ApiResponse<Invoice>>(
        '/v1/invoices',
        data
      );
      return response.data;
    } catch (error) {
      console.error('Error creating invoice:', error);
      throw error;
    }
  },

  /**
   * Update an invoice
   */
  updateInvoice: async (
    id: string,
    data: CreateInvoiceDto
  ): Promise<ApiResponse<Invoice>> => {
    try {
      const response = await api.put<ApiResponse<Invoice>>(
        `/v1/invoices/${id}`,
        data
      );
      return response.data;
    } catch (error) {
      console.error(`Error updating invoice ${id}:`, error);
      throw error;
    }
  },

  /**
   * Update an invoice's payment status
   */
  updateInvoiceStatus: async (
    id: string,
    status: PaymentStatus,
    amountPaid?: number
  ): Promise<ApiResponse<Invoice>> => {
    try {
      const data = {
        payment_status: status,
        ...(amountPaid !== undefined && { amount_paid: amountPaid }),
      };

      const response = await api.patch<ApiResponse<Invoice>>(
        `/v1/invoices/${id}/status`,
        data
      );
      return response.data;
    } catch (error) {
      console.error(`Error updating invoice status ${id}:`, error);
      throw error;
    }
  },

  /**
   * Delete an invoice
   */
  deleteInvoice: async (id: string): Promise<ApiResponse<null>> => {
    try {
      const response = await api.delete<ApiResponse<null>>(
        `/v1/invoices/${id}`
      );
      return response.data;
    } catch (error) {
      console.error(`Error deleting invoice ${id}:`, error);
      throw error;
    }
  },

  /**
   * Download the invoice PDF
   * @param id The ID of the invoice to download
   * @returns Promise resolving to the blob data
   */
  downloadInvoice: async (id: string): Promise<Blob> => {
    try {
      const response = await api.get(`/v1/invoices/${id}/download`, {
        responseType: 'blob',
      });
      return response.data;
    } catch (error) {
      console.error(`Error downloading invoice ${id}:`, error);
      throw error;
    }
  },

  /**
   * Upload a payment proof for an invoice
   */
  uploadPaymentProof: async (
    id: string,
    formData: FormData
  ): Promise<ApiResponse<PaymentProof>> => {
    try {
      const response = await api.post<ApiResponse<PaymentProof>>(
        `/v1/invoices/${id}/payment-proofs`,
        formData,
        {
          headers: {
            'Content-Type': 'multipart/form-data',
          },
        }
      );
      return response.data;
    } catch (error) {
      console.error(`Error uploading payment proof for invoice ${id}:`, error);
      throw error;
    }
  },

  /**
   * Get all payment proofs for an invoice
   */
  getPaymentProofs: async (
    id: string
  ): Promise<ApiResponse<PaymentProof[]>> => {
    try {
      const response = await api.get(`/v1/invoices/${id}/payment-proofs`, {
        // Add validateStatus to prevent Axios from throwing on 204
        validateStatus: (status) => status >= 200 && status < 300,
      });

      // Handle 204 No Content response
      if (response.status === 204) {
        return {
          success: true,
          message: 'No payment proofs found',
          data: [], // Return empty array for 204 response
        };
      }

      return response.data;
    } catch (error) {
      console.error(`Error fetching payment proofs for invoice ${id}:`, error);
      throw error;
    }
  },

  /**
   * Delete a payment proof
   */
  deletePaymentProof: async (
    id: string,
    proofId: string
  ): Promise<ApiResponse<null>> => {
    try {
      const response = await api.delete<ApiResponse<null>>(
        `/v1/invoices/${id}/payment-proofs/${proofId}`
      );
      return response.data;
    } catch (error) {
      console.error(
        `Error deleting payment proof ${proofId} for invoice ${id}:`,
        error
      );
      throw error;
    }
  },

  /**
   * Get invoice history
   */
  getInvoiceHistory: async (
    id: string
  ): Promise<ApiResponse<InvoiceHistoryResponse>> => {
    try {
      const response = await api.get<ApiResponse<InvoiceHistoryResponse>>(
        `/v1/invoices/${id}/history`
      );
      return response.data;
    } catch (error) {
      console.error(`Error fetching invoice history for ${id}:`, error);
      throw error;
    }
  },
};

// Export individual functions for easier imports
export const getInvoices = invoiceApi.getInvoices;
export const getInvoiceById = invoiceApi.getInvoiceById;
export const createInvoice = invoiceApi.createInvoice;
export const deleteInvoice = invoiceApi.deleteInvoice;
export const downloadInvoice = invoiceApi.downloadInvoice;
export const updateInvoiceStatus = invoiceApi.updateInvoiceStatus;
export const updateInvoice = invoiceApi.updateInvoice;
export const uploadPaymentProof = invoiceApi.uploadPaymentProof;
export const getPaymentProofs = invoiceApi.getPaymentProofs;
export const deletePaymentProof = invoiceApi.deletePaymentProof;
export const getInvoiceHistory = invoiceApi.getInvoiceHistory;
