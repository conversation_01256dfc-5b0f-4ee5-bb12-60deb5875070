import api from './client'; // Impor API client
import { ApiResponse } from '@/types/auth';
import {
  KpiProject,
  PaginatedKpiProjectsResponse,
  KpiProjectFilterParams,
  CreateKpiProjectRequest,
  UpdateKpiProjectRequest,
  UpdateKpiProjectStatusRequest,
  KpiProjectsByProjectIdResponse,
} from '@/types/kpi-project';

// Define the API methods for KPI Projects
export const kpiProjectApi = {
  /**
   * Get all KPI Projects with filtering and pagination
   */
  getKpiProjects: async (
    params: KpiProjectFilterParams = {},
    signal?: AbortSignal
  ): Promise<ApiResponse<PaginatedKpiProjectsResponse>> => {
    console.log('Fetching KPI Projects with params:', params);
    const response = await api.get<ApiResponse<PaginatedKpiProjectsResponse>>(
      '/v1/kpi-projects/',
      { params, signal }
    );
    console.log('KPI Projects response:', response.data);
    return response.data;
  },

  /**
   * Get a KPI Project by ID
   */
  getKpiProjectById: async (id: string): Promise<ApiResponse<KpiProject>> => {
    console.log('Fetching KPI Project by ID:', id);
    const response = await api.get<ApiResponse<KpiProject>>(
      `/v1/kpi-projects/${id}`
    );
    console.log('KPI Project by ID response:', response.data);
    return response.data;
  },

  /**
   * Create a new KPI Project
   */
  createKpiProject: async (
    data: CreateKpiProjectRequest
  ): Promise<ApiResponse<KpiProject>> => {
    console.log('Creating KPI Project with data:', data);
    const response = await api.post<ApiResponse<KpiProject>>(
      '/v1/kpi-projects/',
      data
    );
    console.log('Create KPI Project response:', response.data);
    return response.data;
  },

  /**
   * Update an existing KPI Project
   */
  updateKpiProject: async (
    id: string,
    data: UpdateKpiProjectRequest
  ): Promise<ApiResponse<KpiProject>> => {
    console.log('Updating KPI Project with ID:', id, 'and data:', data);
    const response = await api.put<ApiResponse<KpiProject>>(
      `/v1/kpi-projects/${id}`,
      data
    );
    console.log('Update KPI Project response:', response.data);
    return response.data;
  },

  /**
   * Delete a KPI Project by ID
   */
  deleteKpiProject: async (
    id: string
  ): Promise<ApiResponse<Record<string, never>>> => {
    console.log('Deleting KPI Project with ID:', id);
    const response = await api.delete<ApiResponse<Record<string, never>>>(
      `/v1/kpi-projects/${id}`
    );
    console.log('Delete KPI Project response:', response.data);
    return response.data;
  },

  /**
   * Update the status of a KPI Project
   */
  updateKpiProjectStatus: async (
    id: string,
    data: UpdateKpiProjectStatusRequest
  ): Promise<ApiResponse<KpiProject>> => {
    console.log('Updating KPI Project status:', id, data);
    const response = await api.patch<ApiResponse<KpiProject>>(
      `/v1/kpi-projects/${id}/status`,
      data
    );
    console.log('Update status response:', response.data);
    return response.data;
  },

  getViewingKpiProjects: async (
    params: KpiProjectFilterParams = {}
    // signal?: AbortSignal
  ): Promise<ApiResponse<PaginatedKpiProjectsResponse>> => {
    console.log('Fetching KPI Projects with params:', params);
    const response = await api.get<ApiResponse<PaginatedKpiProjectsResponse>>(
      '/v1/kpi-projects/with-details',
      { params }
    );
    console.log('KPI Projects response:', response.data);
    return response.data;
  },

  /**
   * Get KPI Projects by Project ID with filtering
   */
  getKpiProjectsByProjectId: async (
    projectId: string,
    params: KpiProjectFilterParams = {}
  ): Promise<ApiResponse<KpiProjectsByProjectIdResponse>> => {
    console.log('Fetching KPI Projects for project ID:', projectId, 'with params:', params);
    const response = await api.get<ApiResponse<KpiProjectsByProjectIdResponse>>(
      `/v1/kpi-projects/all-by-project/${projectId}`,
      { params }
    );
    console.log('KPI Projects by project ID response:', response.data);
    return response.data;
  },


};

export default kpiProjectApi;
