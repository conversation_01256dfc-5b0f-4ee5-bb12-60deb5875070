//path: b09-rip-fe/src/lib/api/kpi.ts
import api from './client';
import { ApiResponse } from '@/types/auth';
import {
  KPI,
  PaginatedKPIsResponse,
  KPIFilterParams,
  CreateKPIRequest,
  UpdateKPIRequest,
  UpdateKPIStatusRequest,
  UpdateKPIBonusRequest,
} from '@/types/kpi';

// Define a generic empty response type
type EmptyResponse = Record<string, never>;

/**
 * KPI API services
 */
export const kpiApi = {
  /**
   * Get all KPIs with filtering and pagination
   */
  getKPIs: async (
    params: KPIFilterParams = {}
  ): Promise<ApiResponse<PaginatedKPIsResponse>> => {
    const response = await api.get<ApiResponse<PaginatedKPIsResponse>>(
      '/v1/kpi/',
      { params }
    );
    return response.data;
  },

  /**
   * Get KPIs for the currently logged-in user
   */
  getMyKPIs: async (
    params: KPIFilterParams = {}
  ): Promise<ApiResponse<PaginatedKPIsResponse>> => {
    const response = await api.get<ApiResponse<PaginatedKPIsResponse>>(
      '/v1/kpi/my-kpis',
      { params }
    );
    return response.data;
  },

  /**
   * Get KPIs by employee ID
   */
  getKPIsByEmployeeId: async (
    employeeId: string,
    params: KPIFilterParams = {}
  ): Promise<ApiResponse<PaginatedKPIsResponse>> => {
    // Ensure we include the employee_id in the params
    const queryParams = {
      ...params,
      employee_id: employeeId
    };

    const response = await api.get<ApiResponse<PaginatedKPIsResponse>>(
      '/v1/kpi/',
      { params: queryParams }
    );
    return response.data;
  },

  /**
   * Get KPI by ID
   */
  getKPIById: async (id: string): Promise<ApiResponse<KPI>> => {
    const response = await api.get<ApiResponse<KPI>>(`/v1/kpi/${id}`);
    return response.data;
  },

  /**
   * Create a new KPI
   */
  createKPI: async (data: CreateKPIRequest): Promise<ApiResponse<KPI>> => {
    const response = await api.post<ApiResponse<KPI>>('/v1/kpi/', data);
    return response.data;
  },

  /**
   * Update a KPI
   */
  updateKPI: async (
    id: string,
    data: UpdateKPIRequest
  ): Promise<ApiResponse<KPI>> => {
    const response = await api.put<ApiResponse<KPI>>(`/v1/kpi/${id}`, data);
    return response.data;
  },

  /**
   * Delete a KPI
   */
  deleteKPI: async (id: string): Promise<ApiResponse<EmptyResponse>> => {
    const response = await api.delete<ApiResponse<EmptyResponse>>(
      `/v1/kpi/${id}`
    );
    return response.data;
  },

  /**
   * Update KPI status
   */
  updateKPIStatus: async (
    id: string,
    data: UpdateKPIStatusRequest
  ): Promise<ApiResponse<KPI>> => {
    const response = await api.patch<ApiResponse<KPI>>(
      `/v1/kpi/${id}/status`,
      data
    );
    return response.data;
  },

  /**
   * Update KPI bonus
   */
  updateKPIBonus: async (
    id: string,
    data: UpdateKPIBonusRequest
  ): Promise<ApiResponse<KPI>> => {
    const response = await api.patch<ApiResponse<KPI>>(
      `/v1/kpi/${id}/bonus`,
      data
    );
    return response.data;
  },
};

export default kpiApi;
