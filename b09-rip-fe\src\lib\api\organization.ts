import api from './client';
import { ApiResponse } from '@/types/auth';
import {
  Organization,
  PaginatedOrganizationsResponse,
  OrganizationFilterParams,
  CreateOrganizationRequest,
  UpdateOrganizationRequest,
} from '@/types/organization';

// Define a generic empty response type
type EmptyResponse = Record<string, never>;

/**
 * Organization API services
 */
export const organizationApi = {
  /**
   * Get all organizations with filtering and pagination
   */
  getOrganizations: async (
    params: OrganizationFilterParams = {}
  ): Promise<ApiResponse<PaginatedOrganizationsResponse>> => {
    const response = await api.get<ApiResponse<PaginatedOrganizationsResponse>>(
      '/v1/organizations/',
      { params }
    );
    return response.data;
  },

  /**
   * Get organization by ID
   */
  getOrganizationById: async (
    id: string
  ): Promise<ApiResponse<Organization>> => {
    const response = await api.get<ApiResponse<Organization>>(
      `/v1/organizations/${id}`
    );
    return response.data;
  },

  /**
   * Create a new organization
   */
  createOrganization: async (
    data: CreateOrganizationRequest
  ): Promise<ApiResponse<Organization>> => {
    const response = await api.post<ApiResponse<Organization>>(
      '/v1/organizations/',
      data
    );
    return response.data;
  },

  /**
   * Update an organization
   */
  updateOrganization: async (
    id: string,
    data: UpdateOrganizationRequest
  ): Promise<ApiResponse<Organization>> => {
    const response = await api.put<ApiResponse<Organization>>(
      `/v1/organizations/${id}`,
      data
    );
    return response.data;
  },

  /**
   * Delete an organization
   */
  deleteOrganization: async (
    id: string
  ): Promise<ApiResponse<EmptyResponse>> => {
    const response = await api.delete<ApiResponse<EmptyResponse>>(
      `/v1/organizations/${id}`
    );
    return response.data;
  },
};

export default organizationApi;
