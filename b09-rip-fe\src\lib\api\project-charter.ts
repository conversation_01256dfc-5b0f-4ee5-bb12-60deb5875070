import api from './client';
import { ApiResponse } from '@/types/api';
import {
  ProjectCharter,
  CreateProjectCharterDto,
  UpdateProjectCharterDto,
} from '@/types/project-charter';

export const projectCharterApi = {
  /**
   * Get a project charter by ID
   */
  getProjectCharterById: async (
    id: string
  ): Promise<ApiResponse<ProjectCharter>> => {
    try {
      const response = await api.get<ApiResponse<ProjectCharter>>(
        `/v1/project-charters/${id}`
      );
      return response.data;
    } catch (error) {
      console.error(`Error fetching project charter ${id}:`, error);
      throw error;
    }
  },

  /**
   * Get a project charter by project ID
   */
  getProjectCharterByProjectId: async (
    projectId: string
  ): Promise<ApiResponse<ProjectCharter>> => {
    try {
      const response = await api.get<ApiResponse<ProjectCharter>>(
        `/v1/project-charters/project/${projectId}`
      );
      return response.data;
    } catch (error) {
      console.error(
        `Error fetching project charter for project ${projectId}:`,
        error
      );
      throw error;
    }
  },

  /**
   * Create a new project charter
   */
  createProjectCharter: async (
    data: CreateProjectCharterDto
  ): Promise<ApiResponse<ProjectCharter>> => {
    try {
      const response = await api.post<ApiResponse<ProjectCharter>>(
        '/v1/project-charters',
        data
      );
      return response.data;
    } catch (error) {
      console.error('Error creating project charter:', error);
      throw error;
    }
  },

  /**
   * Update a project charter
   */
  updateProjectCharter: async (
    id: string,
    data: UpdateProjectCharterDto
  ): Promise<ApiResponse<ProjectCharter>> => {
    try {
      const response = await api.put<ApiResponse<ProjectCharter>>(
        `/v1/project-charters/${id}`,
        data
      );
      return response.data;
    } catch (error) {
      console.error(`Error updating project charter ${id}:`, error);
      throw error;
    }
  },
};

// Export individual functions for easier imports
export const getProjectCharterById = projectCharterApi.getProjectCharterById;
export const getProjectCharterByProjectId =
  projectCharterApi.getProjectCharterByProjectId;
export const createProjectCharter = projectCharterApi.createProjectCharter;
export const updateProjectCharter = projectCharterApi.updateProjectCharter;
