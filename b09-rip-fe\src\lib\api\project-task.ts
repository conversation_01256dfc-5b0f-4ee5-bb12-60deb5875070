// src\lib\api\project-task.ts

import api from './client';
import { ApiResponse } from '@/types/auth';
import {
  ProjectTask,
  PaginatedProjectTasksResponse,
  ProjectTaskFilterParams,
  CreateProjectTaskRequest,
  UpdateProjectTaskRequest,
  TaskStatus,
} from '@/types/project-task';

type EmptyResponse = Record<string, never>;

/**
 * Project Task API services
 */
export const projectTaskApi = {
  /**
   * Get all project tasks with filtering and pagination
   */
  getProjectTasks: async (
    params: ProjectTaskFilterParams = {}
  ): Promise<ApiResponse<PaginatedProjectTasksResponse>> => {
    const response = await api.get<ApiResponse<PaginatedProjectTasksResponse>>(
      '/v1/project-tasks/',
      { params }
    );
    return response.data;
  },

  /**
   * Get a project task by ID
   */
  getProjectTaskById: async (
    id: string
  ): Promise<ApiResponse<ProjectTask>> => {
    const response = await api.get<ApiResponse<ProjectTask>>(
      `/v1/project-tasks/${id}`
    );
    return response.data;
  },

  /**
   * Create a new project task
   */
  createProjectTask: async (
    data: CreateProjectTaskRequest
  ): Promise<ApiResponse<ProjectTask>> => {
    const response = await api.post<ApiResponse<ProjectTask>>(
      '/v1/project-tasks/',
      data
    );
    return response.data;
  },

  /**
   * Update a project task
   */
  updateProjectTask: async (
    id: string,
    data: UpdateProjectTaskRequest
  ): Promise<ApiResponse<ProjectTask>> => {
    const response = await api.put<ApiResponse<ProjectTask>>(
      `/v1/project-tasks/${id}`,
      data
    );
    return response.data;
  },

  /**
   * Update only the completion status of a project task
   */
  updateProjectTaskStatus: async (
    id: string,
    completion_status: TaskStatus
  ): Promise<ApiResponse<ProjectTask>> => {
    const response = await api.put<ApiResponse<ProjectTask>>(
      `/v1/project-tasks/${id}/status`,
      { completion_status }
    );
    return response.data;
  },

  /**
   * Delete a project task
   */
  deleteProjectTask: async (
    id: string
  ): Promise<ApiResponse<EmptyResponse>> => {
    const response = await api.delete<ApiResponse<EmptyResponse>>(
      `/v1/project-tasks/${id}`
    );
    return response.data;
  },
};

export default projectTaskApi;
