import api from './client';
import { ApiResponse } from '@/types/api';
import {
  Project,
  CreateProjectDto,
  UpdateProjectDto,
  PaginatedProjectsResponse,
  ProjectFilterParams,
  ProjectStatus,
} from '@/types/project';
import { ProjectDashboardData } from '@/types/project-dashboard';
import { ProjectsDashboardData } from '@/types/projects-dashboard';
import { weeklyLogApi } from './weekly-log';

/**
 * Project API services
 */
export const projectApi = {
  /**
   * Get all projects with filtering and pagination
   */
  getProjects: async (
    params: ProjectFilterParams = {}
  ): Promise<ApiResponse<PaginatedProjectsResponse>> => {
    try {
      const response = await api.get<ApiResponse<PaginatedProjectsResponse>>(
        '/v1/projects/',
        { params }
      );
      return response.data;
    } catch (error) {
      console.error('Error fetching projects:', error);
      throw error;
    }
  },

  /**
   * Get project by ID
   */
  getProjectById: async (id: string): Promise<ApiResponse<Project>> => {
    try {
      const response = await api.get<ApiResponse<Project>>(
        `/v1/projects/${id}`
      );
      return response.data;
    } catch (error) {
      console.error(`Error fetching project ${id}:`, error);
      throw error;
    }
  },

  /**
   * Create a new project
   */
  createProject: async (
    data: CreateProjectDto
  ): Promise<ApiResponse<Project>> => {
    try {
      const response = await api.post<ApiResponse<Project>>(
        '/v1/projects/',
        data
      );

      // If project is created with "In Progress" status, trigger weekly log creation
      if (data.status_project === ProjectStatus.IN_PROGRESS) {
        try {
          await weeklyLogApi.triggerCreation();
        } catch (error) {
          console.error(
            'Error triggering weekly log creation after project creation:',
            error
          );
          // Don't throw the error as we still want to return the created project
        }
      }

      return response.data;
    } catch (error) {
      console.error('Error creating project:', error);
      throw error;
    }
  },

  /**
   * Update an existing project
   */
  updateProject: async (
    id: string,
    data: UpdateProjectDto
  ): Promise<ApiResponse<Project>> => {
    try {
      const response = await api.put<ApiResponse<Project>>(
        `/v1/projects/${id}`,
        data
      );

      // If project status is updated to "In Progress", trigger weekly log creation
      if (data.status_project === ProjectStatus.IN_PROGRESS) {
        try {
          await weeklyLogApi.triggerCreation();
        } catch (error) {
          console.error(
            'Error triggering weekly log creation after project update:',
            error
          );
          // Don't throw the error as we still want to return the updated project
        }
      }

      return response.data;
    } catch (error) {
      console.error(`Error updating project ${id}:`, error);
      throw error;
    }
  },

  /**
   * Delete a project
   */
  deleteProject: async (id: string): Promise<ApiResponse<null>> => {
    try {
      const response = await api.delete<ApiResponse<null>>(
        `/v1/projects/${id}`
      );
      return response.data;
    } catch (error) {
      console.error(`Error deleting project ${id}:`, error);
      throw error;
    }
  },

  /**
   * Get project dashboard data
   */
  getProjectDashboard: async (
    id: string
  ): Promise<ApiResponse<ProjectDashboardData>> => {
    try {
      const response = await api.get<ApiResponse<ProjectDashboardData>>(
        `/v1/projects/${id}/dashboard`
      );
      return response.data;
    } catch (error) {
      console.error(`Error fetching project dashboard for ${id}:`, error);
      throw error;
    }
  },

  /**
   * Get overall projects dashboard data
   */
  getProjectsDashboard: async (): Promise<
    ApiResponse<ProjectsDashboardData>
  > => {
    try {
      const response = await api.get<ApiResponse<ProjectsDashboardData>>(
        '/v1/projects/dashboard'
      );
      return response.data;
    } catch (error) {
      console.error('Error fetching projects dashboard:', error);
      throw error;
    }
  },
};

// Export individual functions for easier imports
export const getProjects = projectApi.getProjects;
export const getProjectById = projectApi.getProjectById;
export const createProject = projectApi.createProject;
export const updateProject = projectApi.updateProject;
export const deleteProject = projectApi.deleteProject;
export const getProjectDashboard = projectApi.getProjectDashboard;
export const getProjectsDashboard = projectApi.getProjectsDashboard;
