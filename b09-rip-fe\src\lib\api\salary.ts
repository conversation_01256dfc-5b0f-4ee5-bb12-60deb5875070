import {
  Salary<PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  ApiResponseList,
  ApiResponseSingle,
  UpdateSalaryDto,
  Salary,
} from '@/types/salary';
import { api } from '@/lib/api/client';

export interface CreateSalaryDto {
  employee_id: string;
  base_salary: number;
  allowances: number;
  month: number;
  year: number;
}

export interface LegacyUpdateSalaryDto {
  base_salary?: number;
  allowances?: number;
  status?: 'pending' | 'approved' | 'rejected';
}

export class SalaryApi {
  static async getAll() {
    const response = await api.get<Salary[]>('/salaries');
    return response.data;
  }

  static async getById(id: string) {
    const response = await api.get<Salary>(`/salaries/${id}`);
    return response.data;
  }

  static async getByEmployeeId(employeeId: string) {
    const response = await api.get<Salary[]>(
      `/salaries/employee/${employeeId}`
    );
    return response.data;
  }

  static async create(data: CreateSalaryDto) {
    const response = await api.post<Salary>('/salaries', data);
    return response.data;
  }

  static async update(id: string, data: LegacyUpdateSalaryDto) {
    const response = await api.patch<Salary>(`/salaries/${id}`, data);
    return response.data;
  }

  static async delete(id: string) {
    await api.delete(`/salaries/${id}`);
  }
}

export const salaryApi = {
  // Get all salaries
  getSalaries: async (): Promise<ApiResponseList<SalaryRecord>> => {
    const response = await api.get('/v1/salaries/');
    return response.data;
  },

  // Get salary by ID
  getSalaryById: async (
    id: string
  ): Promise<ApiResponseSingle<SalaryRecord>> => {
    const response = await api.get(`/v1/salaries/${id}`);
    return response.data;
  },

  // Get salaries by employee ID
  getSalariesByEmployeeId: async (
    employeeId: string
  ): Promise<ApiResponseList<SalaryRecord>> => {
    // This is the key change - use the correct endpoint for employee salaries
    const response = await api.get(`/v1/salaries/employee/${employeeId}`);
    return response.data;
  },

  // Update salary by HR (total_bonus and total_deduction only)
  updateSalaryByHR: async (
    id: string,
    data: { total_bonus?: number; total_deduction?: number }
  ): Promise<ApiResponseSingle<SalaryRecord>> => {
    const response = await api.patch(`/v1/salaries/${id}/update-by-hr`, data);
    return response.data;
  },

  // Update salary by Finance (total_bonus, total_deduction, and payment_status)
  updateSalaryByFinance: async (
    id: string,
    data: {
      total_bonus?: number;
      total_deduction?: number;
      payment_status?: 'paid' | 'unpaid';
    }
  ): Promise<ApiResponseSingle<SalaryRecord>> => {
    const response = await api.patch(
      `/v1/salaries/${id}/update-by-finance`,
      data
    );
    return response.data;
  },
  // Get salary history
  getSalaryHistory: async (
    id: string
  ): Promise<ApiResponseList<SalaryHistoryRecord>> => {
    const response = await api.get(`/v1/salaries/${id}/history`);
    return response.data;
  },

  // New unified update salary endpoint with role-based permissions
  // HR can update base_salary for unpaid salaries
  // Finance can update base_salary and payment_status
  // Managers can do both
  updateSalary: async (
    id: string,
    data: UpdateSalaryDto
  ): Promise<ApiResponseSingle<SalaryRecord>> => {
    const response = await api.patch(`/v1/salaries/${id}`, data);
    return response.data;
  },
};

export default salaryApi;
