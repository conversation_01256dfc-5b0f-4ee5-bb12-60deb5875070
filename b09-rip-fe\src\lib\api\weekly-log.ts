import api from './client';
import { ApiResponse } from '@/types/api';
import {
  WeeklyLog,
  PaginatedWeeklyLogsResponse,
  WeeklyLogFilterParams,
  AvailableWeeksResponse,
  TriggerCreationResponse,
  BatchUpdateResult,
} from '@/types/weekly-log';

/**
 * Weekly Log API services
 */
export const weeklyLogApi = {
  /**
   * Get all weekly logs with filtering and pagination
   */
  getWeeklyLogs: async (
    params?: WeeklyLogFilterParams
  ): Promise<ApiResponse<PaginatedWeeklyLogsResponse>> => {
    try {
      const response = await api.get<ApiResponse<PaginatedWeeklyLogsResponse>>(
        '/v1/weekly-logs',
        { params }
      );
      return response.data;
    } catch (error) {
      console.error('Error fetching weekly logs:', error);
      throw error;
    }
  },

  /**
   * Get weekly logs by project ID
   */
  getWeeklyLogsByProjectId: async (
    projectId: string,
    params?: WeeklyLogFilterParams
  ): Promise<ApiResponse<WeeklyLog>> => {
    try {
      // Ensure projectId is a valid UUID
      if (
        !/^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i.test(
          projectId
        )
      ) {
        throw new Error('Invalid project ID format');
      }
      const response = await api.get<ApiResponse<WeeklyLog>>(
        `/v1/weekly-logs/${projectId}`,
        { params }
      );
      return response.data;
    } catch (error) {
      console.error(
        `Error fetching weekly logs for project ${projectId}:`,
        error
      );
      throw error;
    }
  },

  /**
   * Get weekly log by ID
   */
  getWeeklyLogById: async (id: string): Promise<ApiResponse<WeeklyLog>> => {
    try {
      const response = await api.get<ApiResponse<WeeklyLog>>(
        `/v1/weekly-logs/id/${id}`
      );
      return response.data;
    } catch (error) {
      console.error(`Error fetching weekly log ${id}:`, error);
      throw error;
    }
  },

  /**
   * Get weekly log by project ID and week number
   */
  getWeeklyLogByProjectAndWeek: async (
    projectId: string,
    weekNumber: number
  ): Promise<ApiResponse<WeeklyLog>> => {
    try {
      // Ensure projectId is a valid UUID
      if (
        !/^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i.test(
          projectId
        )
      ) {
        throw new Error('Invalid project ID format');
      }
      // Ensure weekNumber is a valid number between 1 and 52
      if (typeof weekNumber !== 'number' || weekNumber < 1 || weekNumber > 52) {
        throw new Error('Invalid week number');
      }
      const response = await api.get<ApiResponse<WeeklyLog>>(
        `/v1/weekly-logs/${projectId}/${weekNumber}`
      );
      return response.data;
    } catch (error) {
      console.error(
        `Error fetching weekly log for project ${projectId} week ${weekNumber}:`,
        error
      );
      throw error;
    }
  },

  /**
   * Get available weeks for a project
   */
  getAvailableWeeks: async (
    projectId: string
  ): Promise<ApiResponse<AvailableWeeksResponse>> => {
    try {
      // Ensure projectId is a valid UUID
      if (
        !/^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i.test(
          projectId
        )
      ) {
        throw new Error('Invalid project ID format');
      }
      const response = await api.get<ApiResponse<AvailableWeeksResponse>>(
        `/v1/weekly-logs/project/${projectId}/weeks`
      );
      return response.data;
    } catch (error) {
      console.error(
        `Error fetching available weeks for project ${projectId}:`,
        error
      );
      throw error;
    }
  },

  triggerCreation: async (): Promise<ApiResponse<TriggerCreationResponse>> => {
    try {
      const response = await api.post<ApiResponse<TriggerCreationResponse>>(
        '/v1/weekly-logs/trigger-creation'
      );
      return response.data;
    } catch (error) {
      console.error('Error triggering weekly log creation:', error);
      throw error;
    }
  },

  /**
   * Update notes for a weekly log
   */
  updateNotes: async (
    weeklyLogId: string,
    notes: Record<number, string>
  ): Promise<ApiResponse<BatchUpdateResult>> => {
    try {
      // Ensure weeklyLogId is a valid UUID
      if (
        !/^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i.test(
          weeklyLogId
        )
      ) {
        throw new Error('Invalid weekly log ID format');
      }

      const response = await api.put<ApiResponse<BatchUpdateResult>>(
        `/v1/weekly-logs/${weeklyLogId}/notes`,
        { notes }
      );
      return response.data;
    } catch (error) {
      console.error(
        `Error updating notes for weekly log ${weeklyLogId}:`,
        error
      );
      throw error;
    }
  },
};
