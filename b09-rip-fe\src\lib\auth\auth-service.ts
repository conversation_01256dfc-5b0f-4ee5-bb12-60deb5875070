import { JWTManager } from './jwt';
import { useAuthStore } from '../store/auth-store';
import { TokenSync } from './token-sync';
import axios, { AxiosRequestConfig } from 'axios';

// Constants
const API_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:3000';

// Define types for the request queue
interface QueuedRequest {
  resolve: (value: unknown) => void;
  reject: (reason?: unknown) => void;
  config: AxiosRequestConfig;
}

// Define the AuthService singleton
class AuthService {
  private isRefreshing = false;
  private refreshSubscribers: QueuedRequest[] = [];
  private refreshPromise: Promise<boolean> | null = null;

  /**
   * Get the current access token
   */
  getAccessToken(): string | null {
    // First try from the store (which should be the most up-to-date)
    const storeToken = useAuthStore.getState().accessToken;
    if (storeToken) return storeToken;

    // Fall back to localStorage via JWTManager if needed
    return JWTManager.getAccessToken();
  }

  /**
   * Get the current refresh token
   */
  getRefreshToken(): string | null {
    // First try from the store (which should be the most up-to-date)
    const storeRefreshToken = useAuthStore.getState().refreshToken;
    if (storeRefreshToken) return storeRefreshToken;

    // Fall back to localStorage via JWTManager if needed
    return JWTManager.getRefreshToken();
  }

  /**
   * Update tokens in all storage locations
   */
  updateTokens(accessToken: string, refreshToken: string): void {
    // Update in Zustand store
    useAuthStore.getState().updateTokens(accessToken, refreshToken);

    // Update in localStorage via JWTManager
    JWTManager.setAccessToken(accessToken);
    JWTManager.setRefreshToken(refreshToken);

    // Update in cookies via TokenSync
    TokenSync.updateTokens(accessToken, refreshToken);

    console.log('Tokens updated successfully');
  }

  /**
   * Add request to the queue to be processed after token refresh
   */
  addToQueue(request: QueuedRequest): void {
    this.refreshSubscribers.push(request);
  }

  /**
   * Process all queued requests with the new token
   */
  processQueue(error: unknown = null): void {
    this.refreshSubscribers.forEach(({ resolve, reject, config }) => {
      if (error) {
        reject(error);
      } else {
        // Update the authorization header with the new token
        config.headers = config.headers || {};
        config.headers['Authorization'] = `Bearer ${this.getAccessToken()}`;
        resolve(axios(config));
      }
    });

    // Clear the queue
    this.refreshSubscribers = [];
  }

  /**
   * Refresh the authentication tokens
   */
  async refreshTokens(): Promise<boolean> {
    // If already refreshing, return the existing promise
    if (this.refreshPromise) {
      return this.refreshPromise;
    }

    // Set refresh flag
    this.isRefreshing = true;

    // Create a new promise to handle refreshing
    this.refreshPromise = new Promise<boolean>(async (resolve) => {
      try {
        const refreshToken = this.getRefreshToken();

        if (!refreshToken) {
          console.error('No refresh token available');
          this.handleRefreshError();
          resolve(false);
          return;
        }

        console.log('Attempting to refresh token...');

        // Make the refresh token request
        const response = await axios.post(`${API_URL}/v1/auth/refresh`, {
          refresh_token: refreshToken,
        });

        if (response.data.success) {
          const { access_token, refresh_token } = response.data.data;

          // Update tokens
          this.updateTokens(access_token, refresh_token);
          console.log('Token refresh successful');

          // Process queued requests
          this.processQueue();
          resolve(true);
        } else {
          console.error('Token refresh failed:', response.data.message);
          this.handleRefreshError();
          resolve(false);
        }
      } catch (error) {
        console.error('Error during token refresh:', error);
        this.handleRefreshError(error);
        resolve(false);
      } finally {
        // Reset refresh state
        this.isRefreshing = false;
        this.refreshPromise = null;
      }
    });

    return this.refreshPromise;
  }

  /**
   * Check if currently refreshing tokens
   */
  isRefreshingTokens(): boolean {
    return this.isRefreshing;
  }

  /**
   * Handle refresh token failure
   */
  private handleRefreshError(error?: unknown): void {
    // Process any queued requests with the error
    this.processQueue(error || new Error('Token refresh failed'));

    // Clear tokens and logout
    this.logout();
  }

  /**
   * Check if token appears to be valid (does basic validation)
   */
  isTokenValid(): boolean {
    return JWTManager.isAccessTokenValid();
  }

  /**
   * Log out the user completely
   */
  logout(): void {
    // Clear tokens from localStorage and cookies
    TokenSync.clearTokens();
    JWTManager.removeTokens();

    // Clear auth store state
    useAuthStore.getState().logout();

    console.log('User logged out');
  }

  /**
   * Ensure cookies and localStorage are in sync
   */
  syncTokenStorages(): void {
    // First try to sync from cookies (useful on page load)
    TokenSync.syncFromCookies();

    // Then make sure cookies reflect what's in localStorage
    TokenSync.syncToCookies();
  }
}

// Export singleton instance
export const authService = new AuthService();
