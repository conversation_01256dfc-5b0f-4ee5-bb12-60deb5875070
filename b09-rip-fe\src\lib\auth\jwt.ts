import { jwtDecode } from 'jwt-decode';
import { UserRole } from '@/types/auth';

interface JWTPayload {
  sub: string;
  exp: number;
  iat: number;
  role: string; // This is "authenticated", not the actual user role
  user_metadata: {
    email_verified: boolean;
    role: UserRole; // The actual role is here
  };
}

export class JWTManager {
  private static readonly ACCESS_TOKEN_KEY = 'access_token';
  private static readonly REFRESH_TOKEN_KEY = 'refresh_token';

  /**
   * Get the access token from local storage
   */
  static getAccessToken(): string | null {
    if (typeof window !== 'undefined') {
      return localStorage.getItem(this.ACCESS_TOKEN_KEY);
    }
    return null;
  }

  /**
   * Set the access token in local storage
   */
  static setAccessToken(token: string): void {
    if (typeof window !== 'undefined') {
      localStorage.setItem(this.ACCESS_TOKEN_KEY, token);
    }
  }

  /**
   * Get the refresh token from local storage
   */
  static getRefreshToken(): string | null {
    if (typeof window !== 'undefined') {
      return localStorage.getItem(this.REFRESH_TOKEN_KEY);
    }
    return null;
  }

  /**
   * Set the refresh token in local storage
   */
  static setRefreshToken(token: string): void {
    if (typeof window !== 'undefined') {
      localStorage.setItem(this.REFRESH_TOKEN_KEY, token);
    }
  }

  /**
   * Remove both tokens from local storage
   */
  static removeTokens(): void {
    if (typeof window !== 'undefined') {
      localStorage.removeItem(this.ACCESS_TOKEN_KEY);
      localStorage.removeItem(this.REFRESH_TOKEN_KEY);
    }
  }

  /**
   * Check if an access token is valid (not expired)
   */
  static isAccessTokenValid(): boolean {
    const token = this.getAccessToken();
    if (!token) return false;

    try {
      const decoded = jwtDecode<JWTPayload>(token);

      // Add buffer time (30 seconds) to ensure token isn't about to expire
      const bufferTime = 30; // seconds
      const currentTime = Math.floor(Date.now() / 1000); // current time in seconds

      // Check if token is expired or about to expire soon
      return decoded.exp > currentTime + bufferTime;
    } catch (error) {
      console.error('Error decoding token:', error);
      return false;
    }
  }

  /**
   * Get payload from access token
   */
  static getPayload(): JWTPayload | null {
    const token = this.getAccessToken();
    if (!token) return null;

    try {
      return jwtDecode<JWTPayload>(token);
    } catch (error) {
      console.error('Error decoding token payload:', error);
      return null;
    }
  }

  /**
   * Get user role from token
   */
  static getUserRole(): UserRole | null {
    const payload = this.getPayload();
    if (!payload) return null;
    return payload.user_metadata?.role || null;
  }

  /**
   * Get token expiration time in seconds
   */
  static getTokenExpirationTime(): number | null {
    const payload = this.getPayload();
    if (!payload) return null;
    return payload.exp;
  }

  /**
   * Get time until token expiration in seconds
   */
  static getTimeUntilExpiration(): number | null {
    const expTime = this.getTokenExpirationTime();
    if (!expTime) return null;

    const currentTime = Math.floor(Date.now() / 1000);
    return expTime - currentTime;
  }

  /**
   * Check if user has admin role
   */
  static isAdmin(): boolean {
    return this.getUserRole() === 'Admin';
  }
}
