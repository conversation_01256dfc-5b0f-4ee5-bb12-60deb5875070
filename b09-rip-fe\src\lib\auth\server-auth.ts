import { cookies } from 'next/headers';

/**
 * Get the authentication token from cookies for server-side API calls
 *
 * This function is designed to be used in server components and API routes
 * to retrieve the authentication token stored in cookies.
 */
export async function getAuthToken(): Promise<string | null> {
  // Get the cookie store
  const cookieStore = await cookies();

  // Get the access token from cookies
  const accessToken = cookieStore.get('access_token');

  // Return the token value or null if not found
  return accessToken?.value || null;
}
