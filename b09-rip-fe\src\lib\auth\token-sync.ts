import { JWTManager } from './jwt';

/**
 * TokenSync utility
 *
 * This utility ensures tokens are synchronized between localStorage and cookies
 * so that both client-side and server-side authentication work consistently.
 */
export const TokenSync = {
  /**
   * Synchronize tokens from localStorage to cookies
   */
  syncToCookies(): void {
    if (typeof window === 'undefined') return;

    // Get tokens from localStorage
    const accessToken = JWTManager.getAccessToken();
    const refreshToken = JWTManager.getRefreshToken();

    // Set cookies from localStorage values
    if (accessToken) {
      this.setCookie('access_token', accessToken, 7); // 7 days expiry
    } else {
      this.deleteCookie('access_token');
    }

    if (refreshToken) {
      this.setCookie('refresh_token', refreshToken, 30); // 30 days expiry
    } else {
      this.deleteCookie('refresh_token');
    }
  },

  /**
   * Synchronize tokens from cookies to localStorage
   */
  syncFromCookies(): void {
    if (typeof window === 'undefined') return;

    // Get tokens from cookies
    const accessToken = this.getCookie('access_token');
    const refreshToken = this.getCookie('refresh_token');

    // Set localStorage from cookie values
    if (accessToken) {
      JWTManager.setAccessToken(accessToken);
    }

    if (refreshToken) {
      JWTManager.setRefreshToken(refreshToken);
    }
  },

  /**
   * Clear tokens from both localStorage and cookies
   */
  clearTokens(): void {
    if (typeof window === 'undefined') return;

    // Clear localStorage
    JWTManager.removeTokens();

    // Clear cookies
    this.deleteCookie('access_token');
    this.deleteCookie('refresh_token');
  },

  /**
   * Update tokens in both localStorage and cookies
   */
  updateTokens(accessToken: string, refreshToken: string): void {
    if (typeof window === 'undefined') return;

    // Update localStorage
    JWTManager.setAccessToken(accessToken);
    JWTManager.setRefreshToken(refreshToken);

    // Update cookies
    this.setCookie('access_token', accessToken, 7); // 7 days expiry
    this.setCookie('refresh_token', refreshToken, 30); // 30 days expiry
  },

  /**
   * Set a cookie with the given name, value and days until expiry
   */
  setCookie(name: string, value: string, days: number): void {
    const expires = new Date();
    expires.setTime(expires.getTime() + days * 24 * 60 * 60 * 1000);
    document.cookie = `${name}=${value};expires=${expires.toUTCString()};path=/;SameSite=Strict`;
  },

  /**
   * Get a cookie value by name
   */
  getCookie(name: string): string | null {
    if (typeof document === 'undefined') return null;

    const cookies = document.cookie.split(';');
    for (let i = 0; i < cookies.length; i++) {
      const cookie = cookies[i].trim();
      if (cookie.startsWith(name + '=')) {
        return cookie.substring(name.length + 1);
      }
    }
    return null;
  },

  /**
   * Delete a cookie by setting its expiry date to the past
   */
  deleteCookie(name: string): void {
    document.cookie = `${name}=;expires=Thu, 01 Jan 1970 00:00:00 GMT;path=/`;
  },
};
