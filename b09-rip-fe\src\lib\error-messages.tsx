'use client';

import Link from 'next/link';
import {
  AlertCircle,
  Ban,
  FileQuestion,
  Lock,
  Server,
  XCircle,
} from 'lucide-react';
import { Button } from '@/components/ui/button';

// Define error code types
export type ErrorCodeType =
  | '404'
  | '500'
  | '403'
  | '401'
  | '400'
  | 'unknown'
  | string;

// Define specific error codes for typing
export type KnownErrorCode = '404' | '500' | '403' | '401' | '400' | 'unknown';

// Define error configuration interface
export interface ErrorConfig {
  title: string;
  message: string;
  icon: React.ElementType;
  iconColor: string;
  iconBgColor: string;
}

// Default error configurations with icons and colors
const errorConfigs: Record<KnownErrorCode, ErrorConfig> = {
  '404': {
    title: 'Halaman Tidak Ditemukan',
    message: 'Maaf, halaman yang Anda cari tidak dapat ditemukan.',
    icon: FileQuestion,
    iconColor: 'text-blue-500',
    iconBgColor: 'bg-blue-100',
  },
  '500': {
    title: '<PERSON><PERSON>ahan Server',
    message: '<PERSON><PERSON>, terjadi kesalahan pada server. Silakan coba lagi nanti.',
    icon: Server,
    iconColor: 'text-red-500',
    iconBgColor: 'bg-red-100',
  },
  '403': {
    title: 'Akses Ditolak',
    message: 'Anda tidak memiliki izin untuk mengakses halaman ini.',
    icon: Ban,
    iconColor: 'text-amber-500',
    iconBgColor: 'bg-amber-100',
  },
  '401': {
    title: 'Tidak Terautentikasi',
    message: 'Anda perlu login untuk mengakses halaman ini.',
    icon: Lock,
    iconColor: 'text-amber-500',
    iconBgColor: 'bg-amber-100',
  },
  '400': {
    title: 'Permintaan Tidak Valid',
    message: 'Permintaan yang Anda kirim tidak valid.',
    icon: XCircle,
    iconColor: 'text-orange-500',
    iconBgColor: 'bg-orange-100',
  },
  unknown: {
    title: 'Terjadi Kesalahan',
    message: 'Maaf, terjadi kesalahan. Silakan coba lagi nanti.',
    icon: AlertCircle,
    iconColor: 'text-gray-500',
    iconBgColor: 'bg-gray-100',
  },
};

// Function to get error configuration based on status code with custom overrides
const getErrorConfig = (
  statusCode: ErrorCodeType,
  customConfigs?: Partial<Record<ErrorCodeType, Partial<ErrorConfig>>>
): ErrorConfig => {
  // Check if statusCode is a known error code
  const isKnownErrorCode = (code: string): code is KnownErrorCode =>
    Object.keys(errorConfigs).includes(code);

  // For known error codes, use the predefined config
  if (isKnownErrorCode(statusCode)) {
    const baseConfig = errorConfigs[statusCode];

    // Apply custom overrides if available
    if (customConfigs && customConfigs[statusCode]) {
      const customConfig = customConfigs[statusCode] as Partial<ErrorConfig>;
      return { ...baseConfig, ...customConfig };
    }

    return baseConfig;
  }

  // Default to unknown for any other status code
  return errorConfigs['unknown'];
};

interface ErrorHandlerProps {
  statusCode?: ErrorCodeType;
  resetError?: () => void;
  customTitle?: string;
  customMessage?: string;
  customConfigs?: Partial<Record<ErrorCodeType, Partial<ErrorConfig>>>;
  homeHref?: string;
  homeLabel?: string;
  backLabel?: string;
  retryLabel?: string;
}

export default function ErrorHandler({
  statusCode = 'unknown',
  resetError,
  customTitle,
  customMessage,
  customConfigs,
  homeHref = '/',
  homeLabel = 'Kembali ke Beranda',
  backLabel = 'Kembali',
  retryLabel = 'Coba Lagi',
}: ErrorHandlerProps) {
  // Get error config with potential custom overrides
  const errorConfig = getErrorConfig(statusCode, customConfigs);

  // Override with direct props if provided
  const title = customTitle || errorConfig.title;
  const message = customMessage || errorConfig.message;
  const Icon = errorConfig.icon;
  const { iconColor, iconBgColor } = errorConfig;

  return (
    <div className="flex min-h-screen flex-col items-center justify-center bg-gray-50 px-4 text-center">
      <div className="mx-auto max-w-md">
        <div className={`rounded-full ${iconBgColor} p-6 mx-auto w-fit mb-6`}>
          <Icon className={`h-16 w-16 ${iconColor}`} />
        </div>

        <h1 className="text-5xl font-bold text-gray-800 mb-2">
          {statusCode !== 'unknown' ? statusCode : 'Error'}
        </h1>
        <h2 className="text-2xl font-semibold text-gray-800 mb-4">{title}</h2>

        <p className="text-gray-600 mb-8">{message}</p>

        <div className="flex flex-col sm:flex-row gap-4 justify-center">
          {resetError ? (
            <Button
              onClick={resetError}
              variant="outline"
              className="border-[#AB8B3B] text-[#AB8B3B] hover:bg-[#AB8B3B]/10 transition-colors duration-200"
            >
              {retryLabel}
            </Button>
          ) : (
            <Button
              onClick={() => window.history.back()}
              variant="outline"
              className="border-[#AB8B3B] text-[#AB8B3B] hover:bg-[#AB8B3B]/10 transition-colors duration-200"
            >
              {backLabel}
            </Button>
          )}

          <Link href={homeHref}>
            <Button className="bg-[#AB8B3B] hover:bg-[#9B7533] text-white transition-colors duration-200">
              {homeLabel}
            </Button>
          </Link>
        </div>
      </div>
    </div>
  );
}
