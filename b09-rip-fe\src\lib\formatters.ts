// src/lib/formatters.ts

/**
 * Format a date string to a user-friendly format
 */
export function formatDate(dateString: string): string {
  const date = new Date(dateString);

  // Format: "25 April 2025, 14:30"
  return date.toLocaleString('id-ID', {
    day: 'numeric',
    month: 'long',
    year: 'numeric',
    hour: '2-digit',
    minute: '2-digit',
  });
}

/**
 * Format a currency value
 */
export function formatCurrency(amount: number): string {
  return `Rp${Number(amount).toLocaleString('id-ID')}`;
}

/**
 * Parse a field name to extract component type and action
 */
export function parseFieldName(field: string): {
  componentType: string;
  action: string;
} {
  const parts = field.split('_');
  if (parts.length === 2) {
    return {
      componentType: parts[0],
      action: parts[1],
    };
  }

  return {
    componentType: field,
    action: '',
  };
}

/**
 * Format a component type name based on its type and component
 */
export function formatComponentTypeName(
  type: string,
  componentType: string
): string {
  if (componentType === 'deduction') {
    const deductionTypeMap: Record<string, string> = {
      absence: 'Ke<PERSON><PERSON><PERSON><PERSON>n',
      lateness: 'Keterlam<PERSON>an',
      loan: 'Pinjaman',
      other: 'Lainnya',
    };
    return deductionTypeMap[type] || type;
  }

  if (componentType === 'bonus') {
    const bonusTypeMap: Record<string, string> = {
      kpi: 'KPI',
      project: 'Proyek',
      other: 'Lainnya',
    };
    return bonusTypeMap[type] || type;
  }

  if (componentType === 'allowance') {
    const allowanceTypeMap: Record<string, string> = {
      transport: 'Transportasi',
      meal: 'Makan',
      health: 'Kesehatan',
      position: 'Jabatan',
      tenure: 'Masa Kerja',
      thr: 'THR',
      other: 'Lainnya',
    };
    return allowanceTypeMap[type] || type;
  }

  return type;
}

/**
 * Format an action type to a user-friendly display name
 */
export function formatActionType(action: string): string {
  const actionMap: Record<string, string> = {
    add: 'Penambahan',
    update: 'Perubahan',
    delete: 'Penghapusan',
  };

  return actionMap[action] || action;
}

/**
 * Format a field name to a user-friendly display name
 */
export function formatFieldName(field: string): string {
  // Handle component_action fields
  const { componentType, action } = parseFieldName(field);
  if (
    ['deduction', 'bonus', 'allowance'].includes(componentType) &&
    ['add', 'update', 'delete'].includes(action)
  ) {
    const componentTypeMap: Record<string, string> = {
      deduction: 'Potongan',
      bonus: 'Bonus',
      allowance: 'Tunjangan',
    };

    return componentTypeMap[componentType] || componentType;
  }

  // Handle other fields
  const fieldMap: Record<string, string> = {
    base_salary: 'Gaji Pokok',
    total_bonus: 'Total Bonus',
    total_deduction: 'Total Potongan',
    total_allowance: 'Total Tunjangan',
    total_salary: 'Total Gaji',
    payment_status: 'Status Pembayaran',
    // Add more field mappings as needed
  };

  return fieldMap[field] || field;
}

/**
 * Format a field value based on its field type
 */
export function formatFieldValue(field: string, value: unknown): string {
  // Handle null or undefined
  if (value === null || value === undefined) {
    return '-';
  }

  // Handle numbers and currency fields
  if (
    field.includes('salary') ||
    field.includes('bonus') ||
    field.includes('deduction') ||
    field.includes('allowance') ||
    typeof value === 'number'
  ) {
    return formatCurrency(Number(value));
  }

  // Handle payment status
  if (field === 'payment_status') {
    return value === 'paid' ? 'Sudah Dibayarkan' : 'Belum Dibayarkan';
  }

  // Handle complex objects
  if (typeof value === 'object' && value !== null) {
    // Handle salary component values
    if ('type' in value && 'total' in value && 'total_salary' in value) {
      const { componentType } = parseFieldName(field);
      // Add type assertion for value.type
      const typeName = formatComponentTypeName(
        value.type as string,
        componentType
      );
      return `${typeName}: ${formatCurrency(Number(value.total))}`;
    }

    // Fallback for other objects
    try {
      return JSON.stringify(value);
    } catch {
      return 'Nilai Kompleks';
    }
  }

  // Default string representation
  return String(value);
}
