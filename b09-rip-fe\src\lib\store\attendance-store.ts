import { create } from 'zustand';
import { devtools } from 'zustand/middleware';
import { attendanceApi } from '@/lib/api/attendance';
import {
  Attendance,
  PresenceStatus,
  EmployeeAttendancePostParams,
} from '@/types/attendance';
import { DateRange } from 'react-day-picker';
import { format } from 'date-fns';

interface AttendanceFilters {
  page?: number;
  pageSize?: number;
  fromDate?: string;
  toDate?: string;
  status?: PresenceStatus;
  search?: string;
  dateRange?: DateRange;
  employeeId?: string;
  isMyAttendance: boolean;
  initialLoad?: boolean;
}

interface AttendancePagination {
  total: number;
  page: number;
  pageCount: number;
  pageSize: number;
}

interface AttendanceStore {
  // State
  attendances: Attendance[];
  pagination: AttendancePagination | null;
  loading: boolean;
  error: string | null;
  filters: AttendanceFilters;
  sortField: string | null;
  sortDirection: 'asc' | 'desc' | null;

  // Actions
  fetchAttendances: () => Promise<void>;
  fetchMyAttendances: () => Promise<void>;
  fetchEmployeeAttendances: (employeeId: string) => Promise<void>;
  setFilters: (filters: Partial<AttendanceFilters>) => void;
  setSorting: (field: string, direction: 'asc' | 'desc') => void;
  clearErrors: () => void;
}

// Helper to ensure pagination values are numbers
const parsePagination = (pagination: {
  total?: number | string;
  page?: number | string;
  pageCount?: number | string;
  totalPages?: number | string;
  pageSize?: number | string;
}): AttendancePagination => {
  return {
    total: Math.max(0, Number(pagination.total) || 0),
    page: Math.max(1, Number(pagination.page) || 1),
    pageCount: Math.max(
      1,
      Number(pagination.pageCount || pagination.totalPages) || 1
    ),
    pageSize: Math.max(1, Number(pagination.pageSize) || 10),
  };
};

// eslint-disable-next-line @typescript-eslint/ban-ts-comment
// @ts-ignore - This is due to a type compatibility issue with zustand versions
export const useAttendanceStore = create<AttendanceStore>()(
  devtools((set, get) => ({
    // Initial state
    attendances: [],
    pagination: null,
    loading: false,
    error: null,
    filters: { page: 1, pageSize: 10, isMyAttendance: false },
    sortField: 'date',
    sortDirection: 'desc',

    // Actions
    fetchAttendances: async () => {
      const { filters } = get();
      set({ loading: true, error: null });

      try {
        // Extract dates from dateRange if it exists
        const params: Record<string, unknown> = { ...filters };

        // Remove internal flags that shouldn't be sent to the API
        delete params.isMyAttendance;
        delete params.initialLoad;

        if (filters.dateRange?.from && filters.dateRange?.to) {
          params.fromDate = format(filters.dateRange.from, 'yyyy-MM-dd');
          params.toDate = format(filters.dateRange.to, 'yyyy-MM-dd');
          delete params.dateRange;
        }

        // Log params for debugging
        console.log('All attendances GET params:', params);

        const response = await attendanceApi.getAttendances(params);

        if (response.success) {
          console.log(
            'All attendances response pagination:',
            response.data.pagination
          );
          set({
            attendances: response.data.data,
            pagination: parsePagination({
              total: response.data.pagination.total,
              page: response.data.pagination.page,
              totalPages: response.data.pagination.pageCount,
              pageSize: response.data.pagination.pageSize,
            }),
            loading: false,
          });
        } else {
          set({
            error: response.message || 'Failed to fetch attendances',
            loading: false,
          });
        }
      } catch (error: unknown) {
        set({
          error: error instanceof Error ? error.message : 'An error occurred',
          loading: false,
        });
      }
    },

    fetchMyAttendances: async () => {
      const { filters } = get();
      set({ loading: true, error: null });

      try {
        // Extract dates from dateRange if it exists
        const params: Record<string, unknown> = { ...filters };

        // Remove internal flags that shouldn't be sent to the API
        delete params.isMyAttendance;
        delete params.initialLoad;

        if (filters.dateRange?.from && filters.dateRange?.to) {
          params.fromDate = format(filters.dateRange.from, 'yyyy-MM-dd');
          params.toDate = format(filters.dateRange.to, 'yyyy-MM-dd');
          delete params.dateRange;
        }

        const response = await attendanceApi.getMyAttendanceHistory(params);

        if (response.success) {
          set({
            attendances: response.data.data,
            pagination: parsePagination({
              total: response.data.pagination.total,
              page: response.data.pagination.page,
              totalPages: response.data.pagination.pageCount,
              pageSize: response.data.pagination.pageSize,
            }),
            loading: false,
          });
        } else {
          set({
            error: response.message || 'Failed to fetch attendances',
            loading: false,
          });
        }
      } catch (error: unknown) {
        set({
          error: error instanceof Error ? error.message : 'An error occurred',
          loading: false,
        });
      }
    },

    fetchEmployeeAttendances: async (employeeId: string) => {
      const { filters } = get();
      set({ loading: true, error: null });

      try {
        // Prepare POST request body
        const postParams: EmployeeAttendancePostParams = {
          employee_id: employeeId,
          page: filters.page,
          pageSize: filters.pageSize,
          status: filters.status,
          search: filters.search,
        };

        // Extract dates from dateRange if it exists
        if (filters.dateRange?.from && filters.dateRange?.to) {
          postParams.fromDate = format(filters.dateRange.from, 'yyyy-MM-dd');
          postParams.toDate = format(filters.dateRange.to, 'yyyy-MM-dd');
        }

        // Log POST params for debugging
        console.log('Employee attendance POST params:', postParams);

        const response = await attendanceApi.postEmployeeAttendance(postParams);

        if (response.success) {
          console.log(
            'Employee attendance response pagination:',
            response.data.pagination
          );
          set({
            attendances: response.data.data,
            pagination: parsePagination({
              total: response.data.pagination.total,
              page: response.data.pagination.page,
              totalPages: response.data.pagination.pageCount,
              pageSize: response.data.pagination.pageSize,
            }),
            loading: false,
          });
        } else {
          set({
            error: response.message || 'Failed to fetch attendances',
            loading: false,
          });
        }
      } catch (error: unknown) {
        set({
          error: error instanceof Error ? error.message : 'An error occurred',
          loading: false,
        });
      }
    },

    setFilters: (newFilters) => {
      const state = get();
      const employeeId = state.filters.employeeId;
      const initialLoad = newFilters.initialLoad;

      set((state) => ({
        filters: { ...state.filters, ...newFilters },
      }));

      // Skip automatic API call on initialLoad flag, as we'll handle the combined fetch
      // with setSorting if necessary
      if (initialLoad) {
        return;
      }

      // Automatically fetch data based on context
      const updatedState = get();

      // Check if we have an employeeId stored to determine which fetch method to use
      if (employeeId) {
        get().fetchEmployeeAttendances(employeeId);
      } else if (updatedState.filters.isMyAttendance) {
        get().fetchMyAttendances();
      } else {
        get().fetchAttendances();
      }
    },

    setSorting: (field, direction) => {
      const state = get();
      const employeeId = state.filters.employeeId;
      const initialLoad = state.filters.initialLoad;

      set({
        sortField: field,
        sortDirection: direction,
        // Clear the initialLoad flag after setting initial sorting
        filters: initialLoad
          ? { ...state.filters, initialLoad: false }
          : state.filters,
      });

      // Now, check if this was the initial load and we need to make our first API call
      if (initialLoad) {
        if (employeeId) {
          get().fetchEmployeeAttendances(employeeId);
        } else if (state.filters.isMyAttendance) {
          get().fetchMyAttendances();
        } else {
          get().fetchAttendances();
        }
        return;
      }

      // Otherwise, handle the sorting change as usual
      if (employeeId) {
        get().fetchEmployeeAttendances(employeeId);
      } else if (state.filters.isMyAttendance) {
        get().fetchMyAttendances();
      } else {
        get().fetchAttendances();
      }
    },

    clearErrors: () => set({ error: null }),
  }))
);
