'use client';

import { create } from 'zustand';
import { Invoice, PaymentStatus, PaymentProof } from '@/types/invoice';
import { invoiceApi } from '@/lib/api/invoice';

interface InvoiceDetailState {
  // Invoice data
  invoice: Invoice | null;

  // Loading states
  loading: boolean;
  error: string | null;

  // Update states
  isUpdatingStatus: boolean;
  statusUpdateError: string | null;

  // Delete states
  isDeleting: boolean;
  deleteError: string | null;
  showDeleteDialog: boolean;

  // Payment proof states
  paymentProofs: PaymentProof[];
  isLoadingProofs: boolean;
  isUploadingProof: boolean;
  isDeletingProof: boolean;
  proofError: string | null;
  showPaymentProofModal: boolean;

  // Actions
  fetchInvoice: (id: string) => Promise<void>;
  updateInvoiceStatus: (id: string, status: PaymentStatus) => Promise<boolean>;
  deleteInvoice: (id: string) => Promise<boolean>;
  setShowDeleteDialog: (show: boolean) => void;

  // Payment proof actions
  fetchPaymentProofs: (id: string) => Promise<void>;
  uploadPaymentProof: (
    id: string,
    file: File,
    notes?: string
  ) => Promise<boolean>;
  deletePaymentProof: (id: string, proofId: string) => Promise<boolean>;
  setShowPaymentProofModal: (show: boolean) => void;

  reset: () => void;
}

export const useInvoiceDetailStore = create<InvoiceDetailState>((set, get) => ({
  // Initial state
  invoice: null,
  loading: false,
  error: null,
  isUpdatingStatus: false,
  statusUpdateError: null,
  isDeleting: false,
  deleteError: null,
  showDeleteDialog: false,

  // Payment proof initial state
  paymentProofs: [],
  isLoadingProofs: false,
  isUploadingProof: false,
  isDeletingProof: false,
  proofError: null,
  showPaymentProofModal: false,

  // Actions
  fetchInvoice: async (id: string) => {
    set({ loading: true, error: null });

    try {
      const response = await invoiceApi.getInvoiceById(id);

      if (response.success) {
        set({ invoice: response.data, loading: false });
      } else {
        set({
          error: response.message || 'Failed to fetch invoice details',
          loading: false,
        });
      }
    } catch (error: unknown) {
      console.error(`Error fetching invoice ${id}:`, error);

      let errorMessage = 'An error occurred while fetching the invoice';

      if (error && typeof error === 'object' && 'response' in error) {
        const err = error as { response: { status: number } };
        switch (err.response.status) {
          case 401:
            errorMessage = 'Session expired. Please login again.';
            break;
          case 403:
            errorMessage = 'You do not have permission to view this invoice.';
            break;
          case 404:
            errorMessage = 'Invoice not found.';
            break;
          default:
            errorMessage =
              'message' in error && typeof error.message === 'string'
                ? error.message
                : errorMessage;
        }
      }

      set({ error: errorMessage, loading: false });
    }
  },

  updateInvoiceStatus: async (id: string, status: PaymentStatus) => {
    // Store the previous invoice for rollback if needed
    const previousInvoice = get().invoice;

    // Optimistically update the UI immediately
    set((state) => ({
      isUpdatingStatus: true,
      statusUpdateError: null,
      invoice: state.invoice
        ? {
            ...state.invoice,
            payment_status: status,
            updated_at: new Date().toISOString(),
          }
        : null,
    }));

    try {
      const response = await invoiceApi.updateInvoiceStatus(id, status);

      if (response.success) {
        // Update with complete server response
        set({
          invoice: response.data,
          isUpdatingStatus: false,
        });
        return true;
      } else {
        // Rollback to previous state on error
        set({
          invoice: previousInvoice,
          statusUpdateError:
            response.message || 'Failed to update invoice status',
          isUpdatingStatus: false,
        });
        return false;
      }
    } catch (error: unknown) {
      console.error(`Error updating invoice status ${id}:`, error);

      let errorMessage = 'An error occurred while updating the invoice status';

      if (error && typeof error === 'object' && 'response' in error) {
        const err = error as { response: { status: number } };
        switch (err.response.status) {
          case 401:
            errorMessage = 'Session expired. Please login again.';
            break;
          case 403:
            errorMessage = 'You do not have permission to update this invoice.';
            break;
          case 404:
            errorMessage = 'Invoice not found.';
            break;
          default:
            errorMessage =
              'message' in error && typeof error.message === 'string'
                ? error.message
                : errorMessage;
        }
      }

      // Rollback to previous state on error
      set({
        invoice: previousInvoice,
        statusUpdateError: errorMessage,
        isUpdatingStatus: false,
      });
      return false;
    }
  },

  deleteInvoice: async (id: string) => {
    set({ isDeleting: true, deleteError: null });

    try {
      const response = await invoiceApi.deleteInvoice(id);

      if (response.success) {
        set({
          isDeleting: false,
          showDeleteDialog: false,
          invoice: null, // Clear the invoice data after successful deletion
        });
        return true;
      } else {
        set({
          deleteError: response.message || 'Failed to delete invoice',
          isDeleting: false,
        });
        return false;
      }
    } catch (error: unknown) {
      console.error(`Error deleting invoice ${id}:`, error);

      let errorMessage = 'An error occurred while deleting the invoice';

      if (error && typeof error === 'object' && 'response' in error) {
        const err = error as { response: { status: number } };
        switch (err.response.status) {
          case 401:
            errorMessage = 'Session expired. Please login again.';
            break;
          case 403:
            errorMessage = 'You do not have permission to delete this invoice.';
            break;
          case 404:
            errorMessage = 'Invoice not found.';
            break;
          default:
            errorMessage =
              'message' in error && typeof error.message === 'string'
                ? error.message
                : errorMessage;
        }
      }

      set({ deleteError: errorMessage, isDeleting: false });
      return false;
    }
  },

  setShowDeleteDialog: (show: boolean) => {
    set({ showDeleteDialog: show, deleteError: null });
  },

  // Payment proof actions
  fetchPaymentProofs: async (id: string) => {
    set({ isLoadingProofs: true, proofError: null });
    try {
      const response = await invoiceApi.getPaymentProofs(id);
      if (response.success) {
        set({ paymentProofs: response.data, isLoadingProofs: false });
      } else {
        set({
          proofError: response.message || 'Failed to fetch payment proofs',
          isLoadingProofs: false,
        });
      }
    } catch (error: unknown) {
      console.error(`Error fetching payment proofs for invoice ${id}:`, error);

      let errorMessage = 'An error occurred while fetching payment proofs';

      if (error && typeof error === 'object' && 'response' in error) {
        const err = error as { response: { status: number } };
        switch (err.response.status) {
          case 401:
            errorMessage = 'Session expired. Please login again.';
            break;
          case 403:
            errorMessage = 'You do not have permission to view payment proofs.';
            break;
          case 404:
            errorMessage = 'Invoice not found.';
            break;
          default:
            errorMessage =
              'message' in error && typeof error.message === 'string'
                ? error.message
                : errorMessage;
        }
      }

      set({ proofError: errorMessage, isLoadingProofs: false });
    }
  },

  uploadPaymentProof: async (id: string, file: File, notes?: string) => {
    set({ isUploadingProof: true, proofError: null });

    const formData = new FormData();
    formData.append('file', file);
    if (notes) formData.append('notes', notes);

    try {
      const response = await invoiceApi.uploadPaymentProof(id, formData);
      if (response.success) {
        // Add the new proof to the list
        set((state) => ({
          paymentProofs: [...state.paymentProofs, response.data],
          isUploadingProof: false,
        }));
        return true;
      } else {
        set({
          proofError: response.message || 'Failed to upload payment proof',
          isUploadingProof: false,
        });
        return false;
      }
    } catch (error: unknown) {
      console.error(`Error uploading payment proof for invoice ${id}:`, error);

      let errorMessage = 'An error occurred while uploading payment proof';

      if (error && typeof error === 'object' && 'response' in error) {
        const err = error as { response: { status: number } };
        switch (err.response.status) {
          case 401:
            errorMessage = 'Session expired. Please login again.';
            break;
          case 403:
            errorMessage =
              'You do not have permission to upload payment proofs.';
            break;
          case 404:
            errorMessage = 'Invoice not found.';
            break;
          default:
            errorMessage =
              'message' in error && typeof error.message === 'string'
                ? error.message
                : errorMessage;
        }
      }

      set({ proofError: errorMessage, isUploadingProof: false });
      return false;
    }
  },

  deletePaymentProof: async (id: string, proofId: string) => {
    set({ isDeletingProof: true, proofError: null });
    try {
      const response = await invoiceApi.deletePaymentProof(id, proofId);
      if (response.success) {
        // Remove the deleted proof from the list
        set((state) => ({
          paymentProofs: state.paymentProofs.filter(
            (proof) => proof.id !== proofId
          ),
          isDeletingProof: false,
        }));
        return true;
      } else {
        set({
          proofError: response.message || 'Failed to delete payment proof',
          isDeletingProof: false,
        });
        return false;
      }
    } catch (error: unknown) {
      console.error(
        `Error deleting payment proof ${proofId} for invoice ${id}:`,
        error
      );

      let errorMessage = 'An error occurred while deleting payment proof';

      if (error && typeof error === 'object' && 'response' in error) {
        const err = error as { response: { status: number } };
        switch (err.response.status) {
          case 401:
            errorMessage = 'Session expired. Please login again.';
            break;
          case 403:
            errorMessage =
              'You do not have permission to delete payment proofs.';
            break;
          case 404:
            errorMessage = 'Payment proof not found.';
            break;
          default:
            errorMessage =
              'message' in error && typeof error.message === 'string'
                ? error.message
                : errorMessage;
        }
      }

      set({ proofError: errorMessage, isDeletingProof: false });
      return false;
    }
  },

  setShowPaymentProofModal: (show: boolean) => {
    set({ showPaymentProofModal: show });
  },

  reset: () => {
    set({
      invoice: null,
      loading: false,
      error: null,
      isUpdatingStatus: false,
      statusUpdateError: null,
      isDeleting: false,
      deleteError: null,
      showDeleteDialog: false,
      paymentProofs: [],
      isLoadingProofs: false,
      isUploadingProof: false,
      isDeletingProof: false,
      proofError: null,
      showPaymentProofModal: false,
    });
  },
}));
