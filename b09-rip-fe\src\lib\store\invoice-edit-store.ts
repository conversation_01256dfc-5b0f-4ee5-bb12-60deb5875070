'use client';

import { create } from 'zustand';
import {
  CreateInvoiceDto,
  CreateInvoiceItemDto,
  Invoice,
} from '@/types/invoice';
import { invoiceApi } from '@/lib/api/invoice';
import { formatDateForInput } from '@/lib/utils/date-utils';

// Separate interfaces for field errors and form errors
interface FieldErrors {
  [key: string]: string | undefined;
}

interface ItemErrors {
  [index: number]: {
    [field: string]: string;
  };
}

interface EditFormState {
  // Original invoice data
  originalInvoice: Invoice | null;

  // Form data
  formData: CreateInvoiceDto;

  // Form state
  fieldErrors: FieldErrors;
  itemErrors: ItemErrors;
  generalError: string | null;
  isLoading: boolean;
  isSubmitting: boolean;
  isSuccess: boolean;

  // Actions
  loadInvoice: (id: string) => Promise<void>;
  updateField: <K extends keyof Omit<CreateInvoiceDto, 'items'>>(
    field: K,
    value: CreateInvoiceDto[K]
  ) => void;

  addItem: () => void;
  updateItem: (
    index: number,
    field: keyof CreateInvoiceItemDto,
    value: string | number
  ) => void;
  removeItem: (index: number) => void;

  validateForm: () => boolean;
  resetForm: () => void;
  submitForm: (id: string) => Promise<boolean>;
}

// Default empty invoice
const defaultInvoice: CreateInvoiceDto = {
  invoice_type: 'external',
  service_type: 'OTHER',
  recipient_name: '',
  due_date: new Date().toISOString().split('T')[0], // Current date in YYYY-MM-DD format
  payment_method: 'bank_transfer',
  payment_status: 'pending',
  notes: '',
  items: [],
};

export const useInvoiceEditStore = create<EditFormState>((set, get) => ({
  // Original invoice
  originalInvoice: null,

  // Form data
  formData: { ...defaultInvoice },

  // Form state
  fieldErrors: {},
  itemErrors: {},
  generalError: null,
  isLoading: false,
  isSubmitting: false,
  isSuccess: false,

  // Load invoice data
  loadInvoice: async (id: string) => {
    set({ isLoading: true, generalError: null });

    try {
      const response = await invoiceApi.getInvoiceById(id);

      if (response.success && response.data) {
        const invoice = response.data;

        // Map the invoice data to form data
        const formData: CreateInvoiceDto = {
          invoice_type: invoice.invoice_type,
          service_type: invoice.service_type,
          recipient_name: invoice.recipient_name,
          project_id: invoice.project_id || undefined,
          project_name: invoice.project_name || undefined,
          due_date: formatDateForInput(invoice.due_date),
          payment_method: invoice.payment_method,
          payment_status: invoice.payment_status,
          notes: invoice.notes,
          items: invoice.items.map((item) => ({
            item_name: item.item_name,
            item_amount: item.item_amount,
            item_price: item.item_price,
          })),
        };

        set({
          originalInvoice: invoice,
          formData,
          isLoading: false,
        });
      } else {
        set({
          isLoading: false,
          generalError: response.message || 'Failed to load invoice data',
        });
      }
    } catch (error: unknown) {
      set({
        isLoading: false,
        generalError:
          error instanceof Error
            ? error.message
            : 'An error occurred while loading the invoice',
      });
    }
  },

  // Update form field
  updateField: (field, value) => {
    set((state) => {
      const newFieldErrors = { ...state.fieldErrors };
      delete newFieldErrors[field];

      return {
        formData: {
          ...state.formData,
          [field]: value,
        },
        fieldErrors: newFieldErrors,
      };
    });
  },

  // Add new item
  addItem: () => {
    const newItem: CreateInvoiceItemDto = {
      item_name: '',
      item_amount: 1,
      item_price: 0,
    };

    set((state) => ({
      formData: {
        ...state.formData,
        items: [...state.formData.items, newItem],
      },
    }));
  },

  // Update item fields
  updateItem: (index, field, value) => {
    set((state) => {
      const newItems = [...state.formData.items];

      if (index >= 0 && index < newItems.length) {
        newItems[index] = {
          ...newItems[index],
          [field]:
            field === 'item_amount' || field === 'item_price'
              ? Number(value)
              : value,
        };
      }

      // Clear item errors for this field if any
      const newItemErrors = { ...state.itemErrors };
      if (newItemErrors[index] && newItemErrors[index][field]) {
        delete newItemErrors[index][field];
        if (Object.keys(newItemErrors[index]).length === 0) {
          delete newItemErrors[index];
        }
      }

      return {
        formData: {
          ...state.formData,
          items: newItems,
        },
        itemErrors: newItemErrors,
      };
    });
  },

  // Remove item
  removeItem: (index) => {
    set((state) => {
      const newItems = [...state.formData.items];
      newItems.splice(index, 1);

      // Clear errors for this item
      const newItemErrors = { ...state.itemErrors };
      delete newItemErrors[index];

      // Remap error indices if there are any errors for items after this one
      const remappedItemErrors: ItemErrors = {};
      Object.keys(newItemErrors).forEach((i) => {
        const itemIndex = parseInt(i);
        if (itemIndex > index) {
          remappedItemErrors[itemIndex - 1] = newItemErrors[itemIndex];
        } else if (itemIndex < index) {
          remappedItemErrors[itemIndex] = newItemErrors[itemIndex];
        }
      });

      return {
        formData: {
          ...state.formData,
          items: newItems,
        },
        itemErrors: remappedItemErrors,
      };
    });
  },

  // Validate form
  validateForm: () => {
    const { formData } = get();
    const fieldErrors: FieldErrors = {};
    const itemErrors: ItemErrors = {};
    let hasErrors = false;

    // Validate recipient
    if (!formData.recipient_name.trim()) {
      fieldErrors.recipient_name = 'Recipient name is required';
      hasErrors = true;
    }

    // Validate date
    if (!formData.due_date) {
      fieldErrors.due_date = 'Due date is required';
      hasErrors = true;
    } else {
      // Check if the date is valid
      const date = new Date(formData.due_date);
      if (isNaN(date.getTime())) {
        fieldErrors.due_date = 'Please enter a valid date';
        hasErrors = true;
      }
    }

    // Validate we have at least one item
    if (formData.items.length === 0) {
      fieldErrors.items = 'At least one item is required';
      hasErrors = true;
    } else {
      // Validate each item
      formData.items.forEach((item, index) => {
        const errors: { [key: string]: string } = {};

        if (!item.item_name.trim()) {
          errors.item_name = 'Item name is required';
          hasErrors = true;
        }

        if (item.item_amount <= 0) {
          errors.item_amount = 'Quantity must be greater than 0';
          hasErrors = true;
        }

        if (item.item_price < 0) {
          errors.item_price = 'Price cannot be negative';
          hasErrors = true;
        }

        if (Object.keys(errors).length > 0) {
          itemErrors[index] = errors;
        }
      });
    }

    set({ fieldErrors, itemErrors });
    return !hasErrors;
  },

  // Reset form to original state
  resetForm: () => {
    const { originalInvoice } = get();

    if (originalInvoice) {
      // Reset to original invoice data
      const formData: CreateInvoiceDto = {
        invoice_type: originalInvoice.invoice_type,
        service_type: originalInvoice.service_type,
        recipient_name: originalInvoice.recipient_name,
        project_id: originalInvoice.project_id || undefined,
        project_name: originalInvoice.project_name || undefined,
        due_date: formatDateForInput(originalInvoice.due_date),
        payment_method: originalInvoice.payment_method,
        payment_status: originalInvoice.payment_status,
        notes: originalInvoice.notes,
        items: originalInvoice.items.map((item) => ({
          item_name: item.item_name,
          item_amount: item.item_amount,
          item_price: item.item_price,
        })),
      };

      set({
        formData,
        fieldErrors: {},
        itemErrors: {},
        generalError: null,
        isSubmitting: false,
        isSuccess: false,
      });
    } else {
      // If no original invoice, reset to empty form
      set({
        formData: { ...defaultInvoice },
        fieldErrors: {},
        itemErrors: {},
        generalError: null,
        isSubmitting: false,
        isSuccess: false,
      });
    }
  },

  // Submit the form
  submitForm: async (id: string) => {
    const { formData, validateForm, originalInvoice } = get();

    // Validate form first
    const isValid = validateForm();
    if (!isValid || !originalInvoice) {
      return false;
    }

    set({ isSubmitting: true, generalError: null });

    try {
      const response = await invoiceApi.updateInvoice(id, formData);

      if (response.success) {
        set({ isSubmitting: false, isSuccess: true });
        return true;
      } else {
        set({
          isSubmitting: false,
          generalError: response.message || 'Failed to update invoice',
        });
        return false;
      }
    } catch (error: unknown) {
      set({
        isSubmitting: false,
        generalError:
          error instanceof Error
            ? error.message
            : 'An error occurred while updating the invoice',
      });
      return false;
    }
  },
}));
