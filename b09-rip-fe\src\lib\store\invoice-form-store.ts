'use client';

import { create } from 'zustand';
import { CreateInvoiceDto, CreateInvoiceItemDto } from '@/types/invoice';
import { invoiceApi } from '@/lib/api/invoice';

// Separate interfaces for field errors and form errors
interface FieldErrors {
  [key: string]: string | undefined;
}

interface ItemErrors {
  [index: number]: {
    item_name?: string;
    item_amount?: string;
    item_price?: string;
  };
}

interface FormState {
  // Form data
  formData: CreateInvoiceDto;

  // Form state
  fieldErrors: FieldErrors;
  itemErrors: ItemErrors;
  generalError: string | null;
  isSubmitting: boolean;
  isSuccess: boolean;

  // Actions
  updateField: <K extends keyof Omit<CreateInvoiceDto, 'items'>>(
    field: K,
    value: CreateInvoiceDto[K]
  ) => void;

  addItem: () => void;
  updateItem: (
    index: number,
    field: keyof CreateInvoiceItemDto,
    value: string | number
  ) => void;
  removeItem: (index: number) => void;

  validateForm: () => boolean;
  resetForm: () => void;
  submitForm: () => Promise<boolean>;
}

// Default empty invoice
const defaultInvoice: CreateInvoiceDto = {
  invoice_type: 'external',
  service_type: 'OTHER',
  recipient_name: '',
  due_date: new Date().toISOString().split('T')[0], // Current date in YYYY-MM-DD format
  payment_method: 'bank_transfer',
  payment_status: 'pending',
  notes: '',
  items: [],
};

export const useInvoiceFormStore = create<FormState>((set, get) => ({
  // Form data
  formData: { ...defaultInvoice },

  // Form state
  fieldErrors: {},
  itemErrors: {},
  generalError: null,
  isSubmitting: false,
  isSuccess: false,

  // Actions
  updateField: (field, value) => {
    set((state) => {
      const newFieldErrors = { ...state.fieldErrors };
      delete newFieldErrors[field];

      return {
        formData: {
          ...state.formData,
          [field]: value,
        },
        fieldErrors: newFieldErrors,
      };
    });
  },

  addItem: () => {
    const newItem: CreateInvoiceItemDto = {
      item_name: '',
      item_amount: 1,
      item_price: 0,
    };

    set((state) => ({
      formData: {
        ...state.formData,
        items: [...state.formData.items, newItem],
      },
    }));
  },

  updateItem: (index, field, value) => {
    set((state) => {
      // Update item in the form data
      const updatedItems = [...state.formData.items];

      if (updatedItems[index]) {
        updatedItems[index] = {
          ...updatedItems[index],
          [field]: value,
        };
      }

      // Clear error for this field in this item
      const updatedItemErrors = { ...state.itemErrors };
      if (updatedItemErrors[index]) {
        updatedItemErrors[index] = {
          ...updatedItemErrors[index],
        };
        delete updatedItemErrors[index][field];

        // Remove the item entry if no errors remain
        if (Object.keys(updatedItemErrors[index]).length === 0) {
          delete updatedItemErrors[index];
        }
      }

      return {
        formData: {
          ...state.formData,
          items: updatedItems,
        },
        itemErrors: updatedItemErrors,
      };
    });
  },

  removeItem: (index) => {
    set((state) => {
      // Remove the item
      const updatedItems = state.formData.items.filter((_, i) => i !== index);

      // Remove any errors for this item
      const updatedItemErrors = { ...state.itemErrors };
      delete updatedItemErrors[index];

      // Reindex the errors for items after the removed one
      const newItemErrors: ItemErrors = {};
      Object.keys(updatedItemErrors).forEach((key) => {
        const i = parseInt(key);
        if (i > index) {
          newItemErrors[i - 1] = updatedItemErrors[i];
        } else if (i < index) {
          newItemErrors[i] = updatedItemErrors[i];
        }
      });

      return {
        formData: {
          ...state.formData,
          items: updatedItems,
        },
        itemErrors: newItemErrors,
      };
    });
  },

  validateForm: () => {
    const { formData } = get();
    const fieldErrors: FieldErrors = {};
    const itemErrors: ItemErrors = {};
    let hasErrors = false;

    // Validate recipient
    if (!formData.recipient_name.trim()) {
      fieldErrors.recipient_name = 'Recipient name is required';
      hasErrors = true;
    }

    // Validate date
    if (!formData.due_date) {
      fieldErrors.due_date = 'Due date is required';
      hasErrors = true;
    }

    // Validate we have at least one item
    if (formData.items.length === 0) {
      fieldErrors.items = 'At least one item is required';
      hasErrors = true;
    } else {
      // Validate each item
      formData.items.forEach((item, index) => {
        const errors: { [key: string]: string } = {};

        if (!item.item_name.trim()) {
          errors.item_name = 'Item name is required';
          hasErrors = true;
        }

        if (item.item_amount <= 0) {
          errors.item_amount = 'Quantity must be greater than 0';
          hasErrors = true;
        }

        if (item.item_price < 0) {
          errors.item_price = 'Price cannot be negative';
          hasErrors = true;
        }

        if (Object.keys(errors).length > 0) {
          itemErrors[index] = errors;
        }
      });
    }

    set({ fieldErrors, itemErrors });
    return !hasErrors;
  },

  resetForm: () => {
    set({
      formData: { ...defaultInvoice },
      fieldErrors: {},
      itemErrors: {},
      generalError: null,
      isSubmitting: false,
      isSuccess: false,
    });
  },

  submitForm: async () => {
    const { formData, validateForm } = get();

    // Validate form first
    const isValid = validateForm();
    if (!isValid) {
      return false;
    }

    set({ isSubmitting: true, generalError: null });

    try {
      const response = await invoiceApi.createInvoice(formData);

      if (response.success) {
        set({ isSubmitting: false, isSuccess: true });
        return true;
      } else {
        set({
          isSubmitting: false,
          generalError: response.message || 'Failed to create invoice',
        });
        return false;
      }
    } catch (error: unknown) {
      set({
        isSubmitting: false,
        generalError:
          error instanceof Error
            ? error.message
            : 'An error occurred while submitting the form',
      });
      return false;
    }
  },
}));
