import { create } from 'zustand';
import { Invoice, InvoiceQueryParams, PaginationData } from '@/types/invoice';
import { invoiceApi } from '@/lib/api/invoice';

interface InvoiceState {
  // Data
  invoices: Invoice[];
  pagination: PaginationData | null;
  selectedInvoice: Invoice | null;

  // UI State
  loading: boolean;
  error: string | null;

  // Filters and sorting
  filters: InvoiceQueryParams;
  sortField: keyof Invoice | null;
  sortDirection: 'asc' | 'desc';

  // Actions
  fetchInvoices: () => Promise<void>;
  setFilters: (filters: Partial<InvoiceQueryParams>) => void;
  setSorting: (field: keyof Invoice) => void;
  resetFilters: () => void;
  setSelectedInvoice: (invoice: Invoice | null) => void;
}

const defaultFilters: InvoiceQueryParams = {
  page: 1,
  pageSize: 10,
  search: '',
  payment_status: undefined,
};

export const useInvoiceStore = create<InvoiceState>((set, get) => ({
  // Data
  invoices: [],
  pagination: null,
  selectedInvoice: null,

  // UI State
  loading: false,
  error: null,

  // Filters and sorting
  filters: defaultFilters,
  sortField: 'created_at',
  sortDirection: 'desc',

  // Actions
  fetchInvoices: async () => {
    const { filters } = get();

    set({ loading: true, error: null });

    try {
      const response = await invoiceApi.getInvoices(filters);

      if (response.success) {
        set({
          invoices: response.data.items,
          pagination: response.data.pagination,
          loading: false,
        });
      } else {
        set({
          error: response.message,
          loading: false,
        });
      }
    } catch (error: unknown) {
      set({
        error:
          error instanceof Error ? error.message : 'Failed to fetch invoices',
        loading: false,
      });
    }
  },

  setFilters: (newFilters) => {
    set((state) => ({
      filters: { ...state.filters, ...newFilters, page: newFilters.page || 1 },
    }));

    // Fetch invoices with new filters
    get().fetchInvoices();
  },

  setSorting: (field) => {
    set((state) => {
      // If clicking the same field, toggle direction
      const direction =
        state.sortField === field && state.sortDirection === 'asc'
          ? 'desc'
          : 'asc';

      return {
        sortField: field,
        sortDirection: direction,
      };
    });

    // Fetch invoices with new sorting
    get().fetchInvoices();
  },

  resetFilters: () => {
    set({ filters: defaultFilters });
    get().fetchInvoices();
  },

  setSelectedInvoice: (invoice) => {
    set({ selectedInvoice: invoice });
  },
}));
