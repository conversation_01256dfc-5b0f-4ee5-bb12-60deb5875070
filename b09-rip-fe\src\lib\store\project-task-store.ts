// src\lib\store\project-task-store.ts

import { create } from 'zustand';
import { persist } from 'zustand/middleware';
import { projectTaskApi } from '@/lib/api/project-task';
import {
  ProjectTask,
  TaskStatus,
  ProjectTaskFilterParams,
} from '@/types/project-task';

interface ProjectTaskState {
  // Current selected task
  currentTask: ProjectTask | null;

  // Tasks list state
  tasks: ProjectTask[];
  loading: boolean;
  error: string | null;

  // Pagination state
  totalItems: number;
  totalPages: number;
  currentPage: number;
  pageSize: number;

  // Filter state
  filters: {
    search: string;
    status: TaskStatus | undefined;
    projectId: string | undefined;
    employeeId: string | undefined;
  };

  // Actions
  setCurrentTask: (task: ProjectTask | null) => void;
  fetchTasks: (params?: ProjectTaskFilterParams) => Promise<void>;
  fetchTaskById: (id: string) => Promise<ProjectTask | null>;
  updateTaskStatus: (id: string, status: TaskStatus) => Promise<boolean>;
  deleteTask: (id: string) => Promise<boolean>;

  // Pagination actions
  setPage: (page: number) => void;
  setPageSize: (size: number) => void;

  // Filter actions
  setSearchFilter: (search: string) => void;
  setStatusFilter: (status: TaskStatus | undefined) => void;
  setProjectFilter: (projectId: string | undefined) => void;
  setEmployeeFilter: (employeeId: string | undefined) => void;
  clearFilters: () => void;
}

export const useProjectTaskStore = create<ProjectTaskState>()(
  persist(
    (set, get) => ({
      // Initial state
      currentTask: null,
      tasks: [],
      loading: false,
      error: null,
      totalItems: 0,
      totalPages: 1,
      currentPage: 1,
      pageSize: 10,
      filters: {
        search: '',
        status: undefined,
        projectId: undefined,
        employeeId: undefined,
      },

      // Actions
      setCurrentTask: (task) => set({ currentTask: task }),

      fetchTasks: async (customParams) => {
        const state = get();
        set({ loading: true, error: null });

        try {
          // Build params from state or custom params
          const params: ProjectTaskFilterParams = customParams || {
            page: state.currentPage,
            pageSize: state.pageSize,
            search: state.filters.search || undefined,
            completion_status: state.filters.status,
            project_id: state.filters.projectId,
            employee_id: state.filters.employeeId,
          };

          const response = await projectTaskApi.getProjectTasks(params);

          if (response.success && response.data) {
            set({
              tasks: response.data.items,
              totalItems: response.data.pagination.total,
              totalPages: response.data.pagination.pageCount,
              error: null,
            });
          } else {
            set({ error: response.message || 'Failed to fetch tasks' });
          }
        } catch (error: unknown) {
          const errorObj = error as { message?: string };
          set({ error: errorObj.message || 'An unexpected error occurred' });
        } finally {
          set({ loading: false });
        }
      },

      fetchTaskById: async (id) => {
        set({ loading: true, error: null });

        try {
          const response = await projectTaskApi.getProjectTaskById(id);

          if (response.success && response.data) {
            const task = response.data;
            set({ currentTask: task });
            return task;
          } else {
            set({ error: response.message || 'Failed to fetch task details' });
            return null;
          }
        } catch (error: unknown) {
          const errorObj = error as { message?: string };
          set({ error: errorObj.message || 'An unexpected error occurred' });
          return null;
        } finally {
          set({ loading: false });
        }
      },

      updateTaskStatus: async (id, status) => {
        set({ loading: true, error: null });

        try {
          const response = await projectTaskApi.updateProjectTaskStatus(
            id,
            status
          );

          if (response.success) {
            // Update the task in the list if it exists
            const state = get();
            const updatedTasks = state.tasks.map((task) =>
              task.id === id ? { ...task, completion_status: status } : task
            );

            // Update current task if it's the one being updated
            if (state.currentTask && state.currentTask.id === id) {
              set({
                currentTask: {
                  ...state.currentTask,
                  completion_status: status,
                },
                tasks: updatedTasks,
              });
            } else {
              set({ tasks: updatedTasks });
            }

            return true;
          } else {
            set({ error: response.message || 'Failed to update task status' });
            return false;
          }
        } catch (error: unknown) {
          const errorObj = error as { message?: string };
          set({ error: errorObj.message || 'An unexpected error occurred' });
          return false;
        } finally {
          set({ loading: false });
        }
      },

      deleteTask: async (id) => {
        set({ loading: true, error: null });

        try {
          const response = await projectTaskApi.deleteProjectTask(id);

          if (response.success) {
            // Remove the task from the list
            const state = get();
            const updatedTasks = state.tasks.filter((task) => task.id !== id);

            // Clear current task if it's the one being deleted
            if (state.currentTask && state.currentTask.id === id) {
              set({
                currentTask: null,
                tasks: updatedTasks,
              });
            } else {
              set({ tasks: updatedTasks });
            }

            return true;
          } else {
            set({ error: response.message || 'Failed to delete task' });
            return false;
          }
        } catch (error: unknown) {
          const errorObj = error as { message?: string };
          set({ error: errorObj.message || 'An unexpected error occurred' });
          return false;
        } finally {
          set({ loading: false });
        }
      },

      // Pagination actions
      setPage: (page) => {
        set({ currentPage: page });
        get().fetchTasks();
      },

      setPageSize: (size) => {
        set({ pageSize: size, currentPage: 1 });
        get().fetchTasks();
      },

      // Filter actions
      setSearchFilter: (search) => {
        set((state) => ({
          filters: { ...state.filters, search },
          currentPage: 1, // Reset to first page
        }));
        get().fetchTasks();
      },

      setStatusFilter: (status) => {
        set((state) => ({
          filters: { ...state.filters, status },
          currentPage: 1, // Reset to first page
        }));
        get().fetchTasks();
      },

      setProjectFilter: (projectId) => {
        set((state) => ({
          filters: { ...state.filters, projectId },
          currentPage: 1, // Reset to first page
        }));
        get().fetchTasks();
      },

      setEmployeeFilter: (employeeId) => {
        set((state) => ({
          filters: { ...state.filters, employeeId },
          currentPage: 1, // Reset to first page
        }));
        get().fetchTasks();
      },

      clearFilters: () => {
        set({
          filters: {
            search: '',
            status: undefined,
            projectId: undefined,
            employeeId: undefined,
          },
          currentPage: 1, // Reset to first page
        });
        get().fetchTasks();
      },
    }),
    {
      name: 'project-task-storage',
      partialize: (state) => ({
        // Only persist these fields
        filters: state.filters,
        pageSize: state.pageSize,
      }),
    }
  )
);

export default useProjectTaskStore;
