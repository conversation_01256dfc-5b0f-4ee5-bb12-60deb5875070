/**
 * Formats a date string to YYYY-MM-DD format for input fields
 * @param dateString Date string to format
 * @returns Formatted date string in YYYY-MM-DD format
 */
export const formatDateForInput = (dateString: string): string => {
  try {
    const date = new Date(dateString);
    // Check if date is valid
    if (isNaN(date.getTime())) {
      return '';
    }
    return date.toISOString().split('T')[0]; // Returns YYYY-MM-DD
  } catch (error) {
    console.error('Error formatting date:', error);
    return '';
  }
};

/**
 * Validates if a string is a valid date
 * @param dateString Date string to validate
 * @returns Boolean indicating if the date is valid
 */
export const isValidDate = (dateString: string): boolean => {
  const date = new Date(dateString);
  return !isNaN(date.getTime());
};
