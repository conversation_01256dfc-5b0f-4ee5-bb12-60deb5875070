/**
 * Format a date string to a human-readable format
 * @param dateString Date string to format
 * @param locale Locale to use for formatting (default: 'id-ID')
 * @returns Formatted date string
 */
export const formatDate = (
  dateString: string | null,
  locale: string = 'id-ID'
): string => {
  if (!dateString) return '-';

  try {
    return new Date(dateString).toLocaleDateString(locale, {
      day: '2-digit',
      month: 'short',
      year: 'numeric',
    });
  } catch (error) {
    console.error('Error formatting date:', error);
    return dateString;
  }
};

/**
 * Format a date and time string to a human-readable format
 * @param dateTimeString Date time string to format
 * @param locale Locale to use for formatting (default: 'id-ID')
 * @returns Formatted date and time string
 */
export const formatDateTime = (
  dateTimeString: string | null,
  locale: string = 'id-ID'
): string => {
  if (!dateTimeString) return '-';

  try {
    return new Date(dateTimeString).toLocaleString(locale, {
      day: '2-digit',
      month: 'short',
      year: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    });
  } catch (error) {
    console.error('Error formatting date time:', error);
    return dateTimeString;
  }
};
