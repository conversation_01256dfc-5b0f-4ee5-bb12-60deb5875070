/**
 * Format a number as Indonesian Rupiah currency
 */
export const formatCurrency = (value: number): string => {
  return new Intl.NumberFormat('id-ID', {
    style: 'currency',
    currency: 'IDR',
    minimumFractionDigits: 0,
    maximumFractionDigits: 0,
  }).format(value);
};

/**
 * Format a number with thousand separators
 */
export const formatNumber = (value: number): string => {
  return new Intl.NumberFormat('id-ID').format(value);
};

/**
 * Format a percentage value
 */
export const formatPercentage = (value: number): string => {
  return `${value}%`;
};

/**
 * Format a date string to display both date and time in Indonesian format
 */
export const formatDateTime = (dateString: string | null): string => {
  if (!dateString) return '-';

  const date = new Date(dateString);

  // Check if the date is valid
  if (isNaN(date.getTime())) return '-';

  // Format the date part
  const formattedDate = date.toLocaleDateString('id-ID', {
    day: 'numeric',
    month: 'long',
    year: 'numeric',
  });

  // Format the time part
  const formattedTime = date.toLocaleTimeString('id-ID', {
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit',
    hour12: false, // Use 24-hour format
  });

  // Combine date and time
  return `${formattedDate}, ${formattedTime}`;
};
