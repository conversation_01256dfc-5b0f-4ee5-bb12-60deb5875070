import jsPDF from 'jspdf';
import html2canvas from 'html2canvas';
import { format } from 'date-fns';
import { Invoice, InvoiceItem } from '@/types/invoice';
import { formatCurrency } from '@/lib/utils';

/**
 * Generate and download a PDF for an invoice
 *
 * @param invoice The invoice data
 * @param elementId The ID of the HTML element to convert to PDF
 * @returns Promise resolving when the PDF has been generated and downloaded
 */
export async function generateInvoicePDF(
  invoice: Invoice,
  elementId: string = 'invoice-container'
): Promise<void> {
  // Find the element to capture
  const element = document.getElementById(elementId);
  if (!element) {
    throw new Error(`Element with ID "${elementId}" not found.`);
  }

  try {
    // Temporarily add print class to body for CSS styling to take effect
    document.body.classList.add('printing-pdf');

    // Hide navigation and UI elements that shouldn't be in the PDF
    const elementsToHide = [
      document.querySelector('nav'),
      document.querySelector('header'),
      document.querySelector('.sidebar'),
      document.querySelector('.navbar'),
      document.querySelector('footer'),
      ...Array.from(
        document.querySelectorAll('button:not([data-print-include])')
      ),
    ];

    // Store original display values to restore later
    const originalDisplayValues = elementsToHide.filter(Boolean).map((el) => ({
      el,
      display: el instanceof HTMLElement ? el.style.display : '',
    }));

    // Hide elements
    originalDisplayValues.forEach((item) => {
      if (item.el && item.el instanceof HTMLElement) {
        item.el.style.display = 'none';
      }
    });

    // Force any images to load with proper attributes
    const images = element.querySelectorAll('img');
    for (const img of Array.from(images)) {
      img.setAttribute('crossorigin', 'anonymous');

      // If it's an SVG image, ensure it maintains aspect ratio
      if (img.src.includes('.svg')) {
        if (img instanceof HTMLImageElement) {
          img.style.objectFit = 'contain';
          img.style.width = '100%';
          img.style.height = 'auto';
        }
      }
    }

    // Allow time for any CSS animations and image loading to complete
    await new Promise((resolve) => setTimeout(resolve, 800));

    // Create a clean formatted date for the filename
    const formattedDate = format(new Date(), 'yyyy-MM-dd');
    const safeRecipientName = invoice.recipient_name
      .replace(/[^a-z0-9]/gi, '_')
      .toLowerCase();
    const fileName = `faktur_${invoice.invoice_number.replace(/\//g, '-')}_${safeRecipientName}_${formattedDate}.pdf`;

    // Set up PDF with A4 dimensions
    const pdf = new jsPDF({
      orientation: 'portrait',
      unit: 'mm',
      format: 'a4',
    });

    // Calculate PDF dimensions
    const pdfWidth = pdf.internal.pageSize.getWidth();
    const pdfHeight = pdf.internal.pageSize.getHeight();

    // Capture the element as a canvas with higher resolution
    const canvas = await html2canvas(element, {
      scale: 2, // Higher scale for better quality
      useCORS: true, // Allow images from other domains
      logging: false,
      allowTaint: true,
      backgroundColor: '#ffffff',
      windowWidth: 1200, // Fixed width for consistent rendering
      onclone: (clonedDoc) => {
        // Adjust the cloned document to prepare for PDF
        const container = clonedDoc.getElementById(elementId);
        if (container) {
          // Apply styles to the container
          container.style.width = '1200px';
          container.style.margin = '0';
          container.style.padding = '40px';
          container.style.boxSizing = 'border-box';
          container.style.backgroundColor = '#ffffff';

          // Remove any navigation or UI elements from the cloned document
          const elementsToRemove = [
            ...Array.from(clonedDoc.querySelectorAll('nav')),
            ...Array.from(
              clonedDoc.querySelectorAll('header:not([data-print-include])')
            ),
            ...Array.from(clonedDoc.querySelectorAll('.sidebar')),
            ...Array.from(clonedDoc.querySelectorAll('.navbar')),
            ...Array.from(clonedDoc.querySelectorAll('footer')),
            ...Array.from(
              clonedDoc.querySelectorAll('button:not([data-print-include])')
            ),
            ...Array.from(clonedDoc.querySelectorAll('.print\\:hidden')),
            ...Array.from(clonedDoc.querySelectorAll('[class*="tabs"]')),
            ...Array.from(clonedDoc.querySelectorAll('[role="tablist"]')),
            ...Array.from(clonedDoc.querySelectorAll('[role="tab"]')),
            ...Array.from(clonedDoc.querySelectorAll('[class*="badge"]')),
          ];

          elementsToRemove.forEach((el) => {
            if (el && el.parentNode) {
              el.parentNode.removeChild(el);
            }
          });

          // Ensure all print-specific styles are applied
          const style = clonedDoc.createElement('style');
          style.textContent = `
            /* Base styles */
            body {
              background-color: white !important;
              font-family: Arial, sans-serif !important;
              color: black !important;
            }

            /* Image handling */
            img {
              max-width: 100% !important;
              height: auto !important;
              object-fit: contain !important;
              object-position: left center !important;
            }

            /* SVG specific handling */
            img[src$=".svg"] {
              width: auto !important;
              height: auto !important;
              max-height: 80px !important;
            }

            /* Hide unnecessary elements */
            .print\\:hidden, nav, .sidebar, .navbar, footer, button:not([data-print-include]) {
              display: none !important;
            }

            /* Show print-only elements */
            .print\\:block { display: block !important; }
            .print\\:hidden { display: none !important; }
            .print\\:text-right { text-align: right !important; }
            .print\\:w-40 { width: 10rem !important; }
            .print\\:border-none { border: none !important; }
            .print\\:shadow-none { box-shadow: none !important; }
            .print\\:bg-transparent { background-color: transparent !important; }
            .print\\:text-black { color: black !important; }
            .print\\:flex { display: flex !important; }
            .print\\:items-start { align-items: flex-start !important; }
            .print\\:gap-4 { gap: 1rem !important; }
            .print\\:mt-0 { margin-top: 0 !important; }
            .print\\:px-0 { padding-left: 0 !important; padding-right: 0 !important; }

            /* Layout and spacing */
            #invoice-container {
              padding: 40px !important;
              margin: 0 !important;
              background-color: white !important;
            }

            /* Table styling */
            table {
              width: 100% !important;
              border-collapse: collapse !important;
              margin-bottom: 1rem !important;
            }

            th, td {
              padding: 12px 8px !important;
              text-align: left !important;
              border-bottom: 1px solid #eeeeee !important;
            }

            th {
              font-weight: bold !important;
              background-color: #f8f9fa !important;
              color: #333333 !important;
            }

            tr:nth-child(even) {
              background-color: #f9fafb !important;
            }

            tr:hover {
              background-color: transparent !important;
            }

            /* Typography */
            h1, h2, h3, h4, h5, h6 {
              margin-bottom: 0.5rem !important;
              color: black !important;
            }

            p {
              margin-bottom: 0.5rem !important;
              color: black !important;
            }

            /* Status text colors */
            .text-green-600 { color: #059669 !important; }
            .text-amber-600 { color: #d97706 !important; }
            .text-blue-600 { color: #2563eb !important; }
            .text-red-600 { color: #dc2626 !important; }
            .text-gray-600 { color: #4b5563 !important; }

            /* Font weights */
            .font-medium { font-weight: 500 !important; }
            .font-bold { font-weight: 700 !important; }

            /* Clear borders and shadows */
            .border, .border-gray-100, [class*="shadow"] {
              border: none !important;
              box-shadow: none !important;
            }

            /* Ensure proper content sizing */
            .container {
              max-width: 100% !important;
              padding: 0 !important;
            }

            /* Improve spacing */
            .mb-4 { margin-bottom: 1rem !important; }
            .mb-8 { margin-bottom: 2rem !important; }
            .mt-4 { margin-top: 1rem !important; }
            .mt-8 { margin-top: 2rem !important; }
            .pb-4 { padding-bottom: 1rem !important; }
            .py-4 { padding-top: 1rem !important; padding-bottom: 1rem !important; }
          `;
          clonedDoc.head.appendChild(style);
        }
      },
    });

    // Add canvas image to PDF
    const imgData = canvas.toDataURL('image/png');

    // Calculate dimensions
    const marginX = 10;
    const marginY = 10;
    const printWidth = pdfWidth - 2 * marginX;
    const printHeight = (canvas.height * printWidth) / canvas.width;
    const pageHeight = pdfHeight - 20;
    let heightLeft = printHeight;
    let position = 0;

    // Add first page
    pdf.addImage(imgData, 'PNG', marginX, marginY, printWidth, printHeight);
    heightLeft -= pageHeight;
    position = 1;

    // Add subsequent pages
    while (heightLeft > 0 && position < 5) {
      // Limit to 5 pages
      pdf.addPage();
      pdf.addImage(
        imgData,
        'PNG',
        marginX,
        marginY - position * pageHeight,
        printWidth,
        printHeight
      );
      heightLeft -= pageHeight;
      position++;
    }

    // Save the PDF with the generated filename
    pdf.save(fileName);

    // Restore original display values
    originalDisplayValues.forEach((item) => {
      if (item.el && item.el instanceof HTMLElement) {
        item.el.style.display = item.display || '';
      }
    });

    // Remove the temporary class after a small delay
    setTimeout(() => {
      document.body.classList.remove('printing-pdf');
    }, 100);

    return;
  } catch (error) {
    // Make sure to clean up if there's an error
    document.body.classList.remove('printing-pdf');
    console.error('Error generating PDF:', error);
    throw new Error('Failed to generate PDF. Please try again.');
  }
}

/**
 * Alternative implementation to manually create a PDF without html2canvas
 * This provides more control over layout but requires more code
 */
export const generateInvoicePDFManual = (invoice: Invoice): void => {
  try {
    // Create PDF document
    const pdf = new jsPDF();
    const pageWidth = pdf.internal.pageSize.getWidth();
    let y = 20; // Starting y position

    // Add header with invoice number
    pdf.setFontSize(20);
    pdf.setFont('helvetica', 'bold');
    pdf.text(`Invoice ${invoice.invoice_number}`, pageWidth / 2, y, {
      align: 'center',
    });
    y += 15;

    // Add company info
    pdf.setFontSize(12);
    pdf.setFont('helvetica', 'bold');
    pdf.text('PT Halaman Tumbuh Bersama', 20, y);
    y += 7;

    pdf.setFont('helvetica', 'normal');
    pdf.setFontSize(10);
    pdf.text(
      'Jl. Terusan Sukadamai II No.5, Sukabungah, Kec. Sukajadi, Kota Bandung',
      20,
      y
    );
    y += 5;
    pdf.text('+62 87802514374 | <EMAIL>', 20, y);
    y += 10;

    // Add recipient info
    pdf.setFontSize(12);
    pdf.setFont('helvetica', 'normal');
    pdf.text(`Recipient: ${invoice.recipient_name}`, 20, y);
    y += 7;

    // Add dates
    const createdDate = format(new Date(invoice.created_at), 'dd/MM/yyyy');
    const dueDate = format(new Date(invoice.due_date), 'dd/MM/yyyy');

    pdf.text(`Created: ${createdDate}`, 20, y);
    y += 7;
    pdf.text(`Due Date: ${dueDate}`, 20, y);
    y += 7;

    // Add payment status
    pdf.text(`Status: ${invoice.payment_status}`, 20, y);
    y += 15;

    // Add items table header
    pdf.setFont('helvetica', 'bold');
    pdf.text('Description', 20, y);
    pdf.text('Qty', 120, y);
    pdf.text('Unit Price', 140, y);
    pdf.text('Total', 180, y, { align: 'right' });
    y += 7;

    // Draw a line
    pdf.line(20, y, 190, y);
    y += 5;

    // Reset font
    pdf.setFont('helvetica', 'normal');

    // Add items
    invoice.items.forEach((item: InvoiceItem) => {
      // Handle long item names
      const itemName = item.item_name;

      pdf.text(itemName, 20, y);
      pdf.text(item.item_amount.toString(), 120, y);
      pdf.text(formatCurrency(item.item_price).replace('Rp', ''), 140, y);
      pdf.text(formatCurrency(item.total_price).replace('Rp', ''), 180, y, {
        align: 'right',
      });

      y += 10;

      // Check if we need a new page
      if (y > 270) {
        pdf.addPage();
        y = 20;
      }
    });

    // Draw a line
    pdf.line(20, y, 190, y);
    y += 10;

    // Add total
    pdf.setFont('helvetica', 'bold');
    pdf.text('Total', 140, y);
    pdf.text(formatCurrency(invoice.total_amount).replace('Rp', ''), 180, y, {
      align: 'right',
    });

    // Generate filename from invoice number and date
    const formattedDate = format(new Date(), 'yyyyMMdd');
    const filename = `Invoice_${invoice.invoice_number.replace(/\//g, '-')}_${formattedDate}.pdf`;

    // Download the PDF
    pdf.save(filename);
  } catch (error) {
    console.error('Error generating PDF:', error);
    throw new Error('Failed to generate invoice PDF');
  }
};
