import { UserProfile } from './auth';

export interface PaginationInfo {
  total: number;
  pageCount: number;
  currentPage: number;
  perPage: number;
}

export interface PaginatedResponse<T> {
  items: T[];
  pagination: PaginationInfo;
}

export interface UserWithProfile {
  id: string; // This is now the auth user id
  email: string; // Email field directly in the user object
  profile: UserProfile; // Profile is now nested
}

export interface PaginatedUsersResponse {
  items: UserWithProfile[];
  pagination: PaginationInfo;
}

export type UserRole = 'Admin' | 'Manager' | 'Employee' | 'Client';

export interface UserFilterParams {
  page?: number;
  pageSize?: number;
  search?: string;
  role?: string;
}

export interface ActivateUserRequest {
  id: string;
  org_id?: string; // Optional, required only for client role
}
