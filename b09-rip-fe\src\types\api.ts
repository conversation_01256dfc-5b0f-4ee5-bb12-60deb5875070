/**
 * Interface for API error responses to provide type-safe error handling
 */
export interface ApiError {
  message?: string;
  response?: {
    status?: number;
    statusText?: string;
    data?: {
      message?: string;
      error?: {
        code?: string;
        message?: string;
        details?: Record<string, string[]>;
      };
    };
    headers?: Record<string, string>;
  };
  request?: unknown;
  config?: Record<string, unknown>;
}

/**
 * Standard API response interface
 */
export interface ApiResponse<T> {
  success: boolean;
  message?: string;
  data?: T;
  error?: {
    code: string;
    message: string;
    details?: Record<string, string[]>;
  };
}
