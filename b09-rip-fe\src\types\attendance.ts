import { Task } from './task';

// Enum untuk status kehadiran
export enum PresenceStatus {
  PRESENT = 'present',
  ABSENT = 'absent',
  PERMIT = 'permit',
  LEAVE = 'leave',
}

// Interface untuk informasi pagination
export interface PaginationInfo {
  total: number;
  pageCount: number;
  currentPage: number;
  perPage: number;
}

// Interface untuk response pagination
export interface PaginatedResponse<T> {
  items: T[];
  pagination: PaginationInfo;
}

// Interface untuk Attendance record
export interface Attendance {
  id: string; // Unique attendance ID
  date: string; // Format YYYY-MM-DD
  employee_id: string; // Employee ID
  presence_status?: PresenceStatus; // Status kehadiran (frontend naming)
  status?: PresenceStatus; // Status kehadiran (API naming)
  clock_in?: string | null; // Format HH:MM:SS (frontend naming)
  clock_out?: string | null; // Format HH:MM:SS (frontend naming)
  check_in?: string | null; // Format HH:MM:SS (API naming)
  check_out?: string | null; // Format HH:MM:SS (API naming)
  notes?: string | null; // Additional notes
  tasks: Task[]; // Associated tasks
  created_at?: string;
  updated_at?: string | null;
  deleted_at?: string | null;
  created_by?: string;
  updated_by?: string | null;
  deleted_by?: string | null;
  employee_name?: string; // Employee name from the API
}

// Interface untuk response paginated attendance
export interface PaginatedAttendancesResponse {
  data: Attendance[];
  pagination: {
    total: number;
    page: number;
    pageSize: number;
    pageCount: number;
  };
}

// DTO untuk membuat kehadiran baru
export interface CreateAttendanceDto {
  presence_status: PresenceStatus;
  notes?: string | null;
  tasks?: {
    description: string;
    due_date: string; // Format YYYY-MM-DD
    assigned_by: string;
  }[];
}

// Fungsi untuk mengubah status presence menjadi readable text
export function getStatusText(status: PresenceStatus | string): string {
  switch (status) {
    case PresenceStatus.PRESENT:
      return 'Hadir';
    case PresenceStatus.ABSENT:
      return 'Tidak Hadir';
    case PresenceStatus.PERMIT:
      return 'Izin';
    case PresenceStatus.LEAVE:
      return 'Cuti';
    default:
      return status;
  }
}

// Interface untuk attendance with tasks (as defined in OpenAPI)
export interface AttendanceWithTasks {
  id: string;
  employee_id: string;
  date: string;
  clock_in: string;
  clock_out?: string | null;
  presence_status: string;
  notes?: string | null;
  tasks: {
    id: string;
    description: string;
    due_date: string;
    assigned_by: string;
    completion_status: boolean;
  }[];
}

// Interface untuk parameter pencarian presensi (used for GET requests)
export interface ViewDailyAbsenceDto {
  page?: number;
  pageSize?: number;
  fromDate?: string;
  toDate?: string;
  status?: PresenceStatus;
  search?: string;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
}

// Interface for common query parameters used in attendance API requests
export interface AttendanceQueryParams {
  page?: number;
  pageSize?: number;
  fromDate?: string;
  toDate?: string;
  status?: PresenceStatus;
  search?: string;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
}

// Interface for POST request to fetch employee attendance
export interface EmployeeAttendancePostParams {
  employee_id: string;
  page?: number;
  pageSize?: number;
  fromDate?: string;
  toDate?: string;
  status?: PresenceStatus;
  search?: string;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
}
