// Authentication-related types based on the API specification

import { z } from 'zod';

export type UserRole =
  | 'Admin'
  | 'Manager'
  | 'HR'
  | 'Finance'
  | 'Operation'
  | 'Client';

export type SignInRequest = {
  email: string;
  password: string;
};

export type SignUpRequest = {
  email: string;
  password: string;
  fullname: string;
  phonenum: string;
  role?: string;
};

export type RefreshTokenRequest = {
  refresh_token: string;
};

export type JWTPayload = {
  sub: string;
  email?: string;
  iat: number;
  exp: number;
  user_metadata?: {
    role?: string;
    [key: string]: unknown;
  };
  [key: string]: unknown;
};

export type UserProfile = {
  id: string;
  user_id?: string;
  email?: string;
  fullname?: string;
  phonenum?: string;
  role?: string;
  org_id?: string | null;
  is_active?: boolean;
  created_at?: string;
  created_by?: string;
  updated_at?: string | null;
  updated_by?: string | null;
  deleted_at?: string | null;
  deleted_by?: string | null;
  employee_id?: string;
  profileComplete?: boolean;
  additionalData?: {
    employee?: Record<string, unknown>;
    [key: string]: unknown;
  };
  [key: string]: unknown;
};

export type ProfileResponse = {
  user: {
    id: string;
    email: string;
    [key: string]: unknown;
  };
  profile: UserProfile;
  profileComplete: boolean;
  additionalData?: {
    employee?: Record<string, unknown>;
    [key: string]: unknown;
  };
};

export type AuthSession = {
  access_token: string;
  refresh_token: string;
  token_type: string;
  expires_in: number;
  expires_at: number;
  user?: {
    id: string;
    email: string;
    [key: string]: unknown;
  };
};

export type AuthResponse = {
  data: {
    session: AuthSession;
    profile: UserProfile;
    user?: UserProfile;
  };
};

export type ApiResponse<T = unknown> = {
  success: boolean;
  message: string;
  data: T;
  error?: {
    code?: string;
    message?: string;
    [key: string]: unknown;
  };
};

// Validation schemas

export const signInSchema = z.object({
  email: z.string().email({ message: 'Email tidak valid' }),
  password: z.string().min(8, { message: 'Password minimal 8 karakter' }),
});

export const signUpSchema = z
  .object({
    email: z.string().email({ message: 'Email tidak valid' }),
    password: z
      .string()
      .min(8, { message: 'Password harus minimal 8 karakter' })
      .regex(/[A-Z]/, { message: 'Password harus mengandung huruf besar' })
      .regex(/[a-z]/, { message: 'Password harus mengandung huruf kecil' })
      .regex(/[0-9]/, { message: 'Password harus mengandung angka' }),
    passwordConfirm: z.string(),
    fullname: z.string().min(3, { message: 'Nama lengkap minimal 3 karakter' }),
    phonenum: z
      .string()
      .min(10, { message: 'Nomor telepon minimal 10 digit' })
      .regex(/^[0-9]+$/, { message: 'Nomor telepon hanya boleh berisi angka' }),
    role: z.string().optional(),
  })
  .refine((data) => data.password === data.passwordConfirm, {
    message: 'Password dan konfirmasi password tidak sama',
    path: ['passwordConfirm'],
  });
