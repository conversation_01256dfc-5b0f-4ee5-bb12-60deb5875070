export enum EmploymentStatus {
  Fulltime = 'Fulltime',
  Intern = 'Intern',
  NotProvided = 'Not provided',
}

export enum PresenceStatus {
  Present = 'present',
  Absent = 'absent',
  Permit = 'permit',
  Leave = 'leave',
}

export enum Department {
  HR = 'HR',
  Finance = 'Finance',
  Operation = 'Operation',
  Manager = 'Manager',
  Admin = 'Admin',
  Client = 'Client',
}

export interface UserProfile {
  fullname: string;
  phonenum: string;
  role: string;
}

export interface Employee {
  id: string;
  email: string;
  profile: UserProfile;
  dob: string;
  address: string;
  bank_account: string;
  bank_name: string;
  employment_status: EmploymentStatus;
  presence_status: PresenceStatus;
  department: Department;
  start_date: string;
  salary_id: string | null;
  created_at: string;
  created_by: string;
  updated_at: string | null;
  updated_by: string | null;
  deleted_at: string | null;
  deleted_by: string | null;
}

export interface EmployeeResponse {
  items: Employee[];
  pagination: {
    total: number;
    page: number;
    pageSize: number;
  };
}

export interface EmployeeFilterParams {
  search?: string;
  page?: number;
  pageSize?: number;
}

export interface ApiResponse<T> {
  success: boolean;
  message: string;
  data: T;
}

export interface UpdateEmployeeDto {
  dob?: string;
  address?: string;
  bank_account?: string;
  bank_name?: string;
  employment_status?: EmploymentStatus;
  presence_status?: PresenceStatus;
  department?: Department;
  start_date?: string;
  salary_id?: string | null;
}

export interface GetAllEmployeesQuery {
  search?: string;
  page?: number;
  pageSize?: number;
  department?: Department;
  employment_status?: EmploymentStatus;
  presence_status?: PresenceStatus;
}

export interface PaginatedResponse<T> {
  items: T[];
  pagination: {
    total: number;
    page: number;
    pageSize: number;
    pageCount?: number;
  };
}
