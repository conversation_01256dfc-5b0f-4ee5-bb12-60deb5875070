// API Response Types
export interface ApiResponse<T> {
  success: boolean;
  message: string;
  data: T;
}

export interface PaginationData {
  total: number;
  page: number;
  pageSize: number;
  pageCount: number;
}

export interface InvoicesResponse {
  items: Invoice[];
  pagination: PaginationData;
}

// Invoice Types
export interface InvoiceItem {
  id: string;
  invoice_id: string;
  item_name: string;
  item_amount: number;
  item_price: number;
  total_price: number;
  created_at: string;
  created_by: string;
  updated_at: string | null;
  updated_by: string | null;
  deleted_at: string | null;
  deleted_by: string | null;
}

export type InvoiceType = 'internal' | 'external';
export type ServiceType =
  | 'HCM'
  | 'ORDEV'
  | 'BE'
  | 'IT'
  | 'MARKETING'
  | 'FINANCE'
  | 'SALES'
  | 'OTHER';
export type PaymentMethod =
  | 'bank_transfer'
  | 'cash'
  | 'credit_card'
  | 'cheque'
  | 'other';
export type PaymentStatus =
  | 'pending'
  | 'partial'
  | 'paid'
  | 'overdue'
  | 'cancelled';

export interface Invoice {
  id: string;
  invoice_number: string;
  invoice_type: InvoiceType;
  service_type: ServiceType;
  recipient_name: string;
  project_id: string | null;
  project_name: string | null;
  due_date: string;
  payment_method: PaymentMethod;
  payment_status: PaymentStatus;
  notes: string;
  total_amount: number;
  amount_paid?: number;
  created_at: string;
  created_by: string;
  updated_at: string | null;
  updated_by: string | null;
  deleted_at: string | null;
  deleted_by: string | null;
  items: InvoiceItem[];
}

// Create Invoice DTO
export interface CreateInvoiceItemDto {
  item_name: string;
  item_amount: number;
  item_price: number;
}

export interface CreateInvoiceDto {
  invoice_type: InvoiceType;
  service_type: ServiceType;
  recipient_name: string;
  project_id?: string;
  project_name?: string;
  due_date: string;
  payment_method: PaymentMethod;
  payment_status?: PaymentStatus;
  notes?: string;
  items: CreateInvoiceItemDto[];
}

// Payment Proof Types
export interface PaymentProof {
  id: string;
  invoice_id: string;
  file_path: string;
  file_name: string;
  file_type: string;
  file_size: number;
  notes?: string | null;
  created_at: string;
  created_by: string;
  updated_at: string | null;
  updated_by: string | null;
  deleted_at: string | null;
  deleted_by: string | null;
  download_url: string;
}

// Request Parameter Types
export interface InvoiceQueryParams {
  search?: string;
  page?: number;
  pageSize?: number;
  payment_status?: PaymentStatus;
}

// Invoice History Types
export interface InvoiceChange {
  field: string;
  from_value: unknown;
  to_value: unknown;
}

export interface InvoiceHistoryRecord {
  id: string;
  invoice_id: string;
  change_description: string;
  created_at: string;
  created_by: string;
  updated_at: string | null;
  updated_by: string | null;
  deleted_at: string | null;
  deleted_by: string | null;
  parsed_changes: InvoiceChange[];
}

export interface InvoiceHistoryResponse {
  items: InvoiceHistoryRecord[];
  pagination: PaginationData;
}
