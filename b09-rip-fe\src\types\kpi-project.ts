// types/kpiProject.ts

// Status Enum untuk KPI Project
export type KpiProjectStatus =
  | 'not_started'
  | 'in_progress'
  | 'completed_below_target'
  | 'completed_on_target'
  | 'completed_above_target';

// Interface untuk KPI Project
export interface KpiProject {
  id: string;
  project_name: string;
  project_id: string;
  description: string;
  target: string;
  period: string;
  status: KpiProjectStatus; // Status KPI Project (mirip dengan KPI Employee)
  additional_notes?: string;
  created_at: string;
  created_by: string;
  updated_at: string | null;
  updated_by: string | null;
  deleted_at: string | null;
  deleted_by: string | null;
}

// Pagination Info untuk KPI Project
export interface PaginationInfo {
  total: number;
  page: number;
  pageSize: number;
  pageCount: number;
}

// Response Paginated KPI Project
export interface PaginatedKpiProjectsResponse {
  data?: KpiProject[];
  items?: KpiProject[]; // Add support for 'items' field for API consistency
  pagination: PaginationInfo;
}

// Params filter untuk pencarian KPI Project
export interface KpiProjectFilterParams {
  page?: number;
  pageSize?: number;
  search?: string; // Pencarian berdasarkan nama proyek atau deskripsi
  project_name?: string;
  project_id?: string;
  organization_id?: string; // For filtering by organization (client)
  pic_id?: string; // For filtering by project PIC (operation user)
  status?: KpiProjectStatus; // Status KPI Project (pilihan)
}

// Request untuk membuat KPI Project
export interface CreateKpiProjectRequest {
  project_name: string;
  project_id: string;
  description: string;
  target: string;
  period: string;
  status?: KpiProjectStatus; // Defaultnya bisa "not_started"
  additional_notes?: string;
}

// Request untuk update KPI Project
export interface UpdateKpiProjectRequest {
  project_name?: string;
  project_id?: string;
  description?: string;
  target?: string;
  period?: string;
  status?: KpiProjectStatus; // Update status
  additional_notes?: string;
}

// Update Status untuk KPI Project
export interface UpdateKpiProjectStatusRequest {
  status: KpiProjectStatus; // Status yang akan di-update
}

// Response for KPI Projects by project ID
export interface KpiProjectsByProjectIdResponse {
  data: KpiProject[];
}
