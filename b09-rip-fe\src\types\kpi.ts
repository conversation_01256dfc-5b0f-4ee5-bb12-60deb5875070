export interface KPI {
  id: string;
  full_name: string;
  employee_id: string;
  description: string;
  target: string;
  period: string;
  status: KPIStatus;
  bonus_received: number | null;
  additional_notes?: string;
  created_at: string;
  created_by: string;
  updated_at: string | null;
  updated_by: string | null;
  deleted_at: string | null;
  deleted_by: string | null;
}

export type KPIStatus =
  | 'not_started'
  | 'in_progress'
  | 'completed_below_target'
  | 'completed_on_target'
  | 'completed_above_target';

export interface PaginationInfo {
  total: number;
  page: number;
  pageSize: number;
  pageCount: number;
}

export interface PaginatedKPIsResponse {
  items: KPI[];
  pagination: PaginationInfo;
}

export interface KPIFilterParams {
  page?: number;
  pageSize?: number;
  search?: string;
  employee_id?: string;
  period?: string;
  status?: KPIStatus;
}

export interface CreateKPIRequest {
  full_name: string;
  employee_id: string;
  description: string;
  target: string;
  period: string;
  status?: KPIStatus;
  bonus_received?: number;
  additional_notes?: string;
}

export interface UpdateKPIRequest {
  full_name?: string;
  employee_id?: string;
  description?: string;
  target?: string;
  period?: string;
  status?: KPIStatus;
  bonus_received?: number;
  additional_notes?: string;
}

export interface UpdateKPIStatusRequest {
  status: KPIStatus;
}

export interface UpdateKPIBonusRequest {
  bonus_received: number;
}
