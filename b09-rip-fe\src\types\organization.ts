export interface Organization {
  id: string;
  name: string;
  phone: string;
  address: string;
  client_type: string;
  notes?: string;
  created_at: string;
  created_by: string;
  updated_at: string | null;
  updated_by: string | null;
  deleted_at: string | null;
  deleted_by: string | null;
}

export interface PaginationInfo {
  total: number;
  page: number;
  pageSize: number;
  pageCount: number;
}

export interface PaginatedOrganizationsResponse {
  items: Organization[];
  pagination: PaginationInfo;
}

export interface OrganizationFilterParams {
  page?: number;
  pageSize?: number;
  search?: string;
  client_type?: string;
}

export interface CreateOrganizationRequest {
  name: string;
  phone: string;
  address: string;
  client_type: string;
  notes?: string;
}

export interface UpdateOrganizationRequest {
  name?: string;
  phone?: string;
  address?: string;
  client_type?: string;
  notes?: string;
}
