// Define the PaginatedResponse interface locally
interface PaginatedResponse {
  data: ProjectCharter[];
  pagination: {
    total: number;
    page: number;
    pageSize: number;
    pageCount: number;
  };
}

/**
 * Interface for Project Charter model
 */
export interface ProjectCharter {
  id: string;
  project_id: string;
  key_stakeholders: string;
  project_authority: string;
  project_description: string;
  objective_and_key_results: string;
  purpose: string;
  key_assumption: string;
  assumptions_constrains_risks: string;
  high_level_resources: string;
  high_level_milestones: string;
  statement_prediction_of_benefit: string;
  approval: boolean;
  created_at: string;
  created_by: string;
  updated_at: string | null;
  updated_by: string | null;
  deleted_at: string | null;
  deleted_by: string | null;
}

/**
 * DTO for creating a project charter
 */
export interface CreateProjectCharterDto {
  project_id: string;
  key_stakeholders: string;
  project_authority: string;
  project_description: string;
  objective_and_key_results: string;
  purpose: string;
  key_assumption: string;
  assumptions_constrains_risks: string;
  high_level_resources: string;
  high_level_milestones: string;
  statement_prediction_of_benefit: string;
  approval: boolean;
}

/**
 * DTO for updating a project charter
 */
export interface UpdateProjectCharterDto {
  key_stakeholders?: string;
  project_authority?: string;
  project_description?: string;
  objective_and_key_results?: string;
  purpose?: string;
  key_assumption?: string;
  assumptions_constrains_risks?: string;
  high_level_resources?: string;
  high_level_milestones?: string;
  statement_prediction_of_benefit?: string;
  approval?: boolean;
}

/**
 * Response for paginated project charters
 */
export interface PaginatedProjectChartersResponse extends PaginatedResponse {
  data: ProjectCharter[];
}
