// src/types/project-dashboard.ts
import { KpiProjectStatus } from './kpi-project';
import { ProjectStatus } from './project';

/**
 * Interface for KPI data in the project dashboard
 */
export interface DashboardKPI {
  id: string;
  description: string;
  target: string;
  period: string;
  status: KpiProjectStatus;
}

/**
 * Interface for task data in the project dashboard
 */
export interface DashboardTask {
  id: string;
  description: string;
  completion_status: 'completed' | 'on_progress' | 'not_completed';
  employee_name: string | null;
  due_date: string;
}

/**
 * Interface for weekly log data in the project dashboard
 */
export interface DashboardWeeklyLog {
  id: string;
  week_number: number;
  week_start_date: string;
  week_end_date: string;
  notes_count: number;
}

/**
 * Interface for project dashboard data
 */
export interface ProjectDashboardData {
  id: string;
  project_name: string;
  organization_id: string;
  organization_name: string;
  pic_project: string;
  project_category: string;
  start_project: string;
  end_project: string;
  status_project: ProjectStatus;
  objectives: string;
  budget_project: string;
  progress_percentage: number;
  days_elapsed: number;
  days_remaining: number;
  days_total: number;
  kpi_status: KpiProjectStatus;
  kpi_count: number;
  kpis: DashboardKPI[];
  tasks_total: number;
  tasks_completed: number;
  tasks_in_progress: number;
  tasks_not_started: number;
  recent_tasks: DashboardTask[];
  weekly_logs_count: number;
  recent_weekly_logs: DashboardWeeklyLog[];
}
