// src\types\project-task.ts

export interface ProjectTask {
  id: string;
  assigned_by: string;
  description: string;
  completion_status: string; // gunakan enum TaskStatus jika perlu
  employee_id: string;
  initial_date: string;
  due_date: string;
  project_id: string;
  weekly_log_id?: string;
  created_at: string;
  created_by: string;
  updated_at: string | null;
  updated_by: string | null;
  deleted_at: string | null;
  deleted_by: string | null;

  // Tambahan informasi dari API
  employee_department?: string;
  employee_name?: string;
  employee_role?: string;
  assigned_by_department?: string;
  assigned_by_name?: string;
  assigned_by_role?: string;
  project_name?: string;
}

export type TaskStatus =
| 'not_completed'
| 'on_progress'
| 'completed'


export interface PaginationInfo {
  totalItems: number;
  totalPages: number;
  total: number;
  page: number;
  pageSize: number;
  pageCount: number;
}

export interface PaginatedProjectTasksResponse {
  items: ProjectTask[];
  pagination: PaginationInfo;
}

export interface ProjectTaskFilterParams {
  page?: number;
  pageSize?: number;
  search?: string;
  completion_status?: string;
  employee_id?: string;
  project_id?: string;
}

export interface CreateProjectTaskRequest {
  assigned_by: string;
  description: string;
  completion_status: string;
  employee_id: string;
  initial_date: string;
  due_date: string;
  project_id: string;
  weekly_log_id?: string;
}

export interface UpdateProjectTaskRequest {
  assigned_by?: string;
  description?: string;
  completion_status?: string;
  employee_id?: string;
  initial_date?: string;
  due_date?: string;
  project_id?: string;
  weekly_log_id?: string;
}

export interface Project {
  id: string;
  name: string;
}
