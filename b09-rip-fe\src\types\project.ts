// Project-related types based on the API specification

/**
 * Enum for project status
 */
export enum ProjectStatus {
  NOT_STARTED = 'Not Started',
  IN_PROGRESS = 'In Progress',
  COMPLETED = 'Completed',
  CANCELLED = 'Cancelled',
}

/**
 * Enum for project categories
 */
export enum ProjectCategory {
  PRELIMINARY_RESEARCH = 'Preliminary Research',
  ADMINISTRASI = 'Administrasi',
  MONITORING = 'Monitoring',
  DIGITAL_MARKETING = 'Digital Marketing',
  BRAND_AUDIT = 'Brand Audit',
  BRAND_STRATEGY = 'Brand Strategy',
  DRAFT_MONTHLY_REPORT = 'Draft Monthly Report',
}

/**
 * Interface for Project model
 */
export interface Project {
  id: string;
  organization_id: string;
  project_category: ProjectCategory;
  project_name: string;
  pic_project: string; // Employee ID
  start_project: string; // Format YYYY-MM-DD
  end_project: string; // Format YYYY-MM-DD
  status_project: ProjectStatus;
  budget_project: string;
  gantt_chart_id?: string; // Optional
  project_charter_id?: string; // Optional
  objectives: string;
  created_at: string;
  created_by: string;
  updated_at: string | null;
  updated_by: string | null;
  deleted_at: string | null;
  deleted_by: string | null;
}

/**
 * DTO for creating a project
 */
export interface CreateProjectDto {
  organization_id: string;
  project_category: ProjectCategory;
  project_name: string;
  pic_project: string; // Employee ID
  start_project: string; // Format YYYY-MM-DD
  end_project: string; // Format YYYY-MM-DD
  status_project?: ProjectStatus; // Default: NOT_STARTED
  budget_project: string;
  objectives: string;
}

/**
 * DTO for updating a project
 */
export interface UpdateProjectDto {
  organization_id?: string;
  project_category?: ProjectCategory;
  project_name?: string;
  pic_project?: string; // Employee ID
  start_project?: string; // Format YYYY-MM-DD
  end_project?: string; // Format YYYY-MM-DD
  status_project?: ProjectStatus;
  budget_project?: string;
  objectives?: string;
}

/**
 * Interface for pagination information
 */
export interface PaginationInfo {
  total: number;
  page: number;
  pageSize: number;
  pageCount: number;
}

/**
 * Interface for paginated projects response
 */
export interface PaginatedProjectsResponse {
  items: Project[];
  pagination: PaginationInfo;
}

/**
 * Interface for project filter parameters
 */
export interface ProjectFilterParams {
  search?: string;
  page?: number;
  pageSize?: number;
  project_category?: string;
  status_project?: string;
}
