// src/types/projects-dashboard.ts

/**
 * Interface for project PIC data in the projects dashboard
 */
export interface ProjectPIC {
  name: string;
  count: number;
}

/**
 * Interface for recent project data in the projects dashboard
 */
export interface RecentProject {
  id: string;
  project_name: string;
  organization_name: string;
  pic_name: string;
  status_project: string;
  progress_percentage: number;
  days_remaining: number;
}

/**
 * Interface for upcoming deadline data in the projects dashboard
 */
export interface UpcomingDeadline {
  id: string;
  project_name: string;
  end_project: string;
  days_remaining: number;
  progress_percentage: number;
}

/**
 * Interface for projects dashboard data
 */
export interface ProjectsDashboardData {
  projects: {
    total: number;
    by_status: Record<string, number>;
    by_category: Record<string, number>;
    by_pic: ProjectPIC[];
    recent: RecentProject[];
    upcoming_deadlines: UpcomingDeadline[];
  };
  kpis: {
    total: number;
    by_status: Record<string, number>;
    achievement_percentage: number;
    achieved_count: number;
    not_achieved_count: number;
  };
  tasks: {
    total: number;
    completed: number;
    in_progress: number;
    not_started: number;
    overdue: number;
  };
}
