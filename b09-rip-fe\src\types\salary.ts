// src/types/salary.ts

export interface SalaryRecord {
  id: string;
  employee_id: string;
  base_salary: number;
  total_bonus: number; // Changed from bonus to match API response
  total_deduction: number; // Changed from pay_reduction to match API response
  total_allowance: number; // Added new field from API response
  total_salary: number;
  payment_status: string;
  period: string;
  created_at: string;
  created_by: string;
  updated_at: string | null;
  updated_by: string | null;
  deleted_at: string | null;
  deleted_by: string | null;
  employee_details: {
    department: string;
    bank_account: string;
    bank_name: string;
    role: string;
    fullname: string;
  };
}

export interface SalaryQueryParams {
  page?: number;
  pageSize?: number;
  period?: string;
  search?: string;
}

// Generic API response for list endpoints
export interface ApiResponseList<T> {
  error: ErrorResponse | null;
  success: boolean;
  message: string;
  data: {
    items: T[];
  };
}

// Generic API response for single item endpoints
export interface ApiResponseSingle<T> {
  error: ErrorResponse | null;
  success: boolean;
  message: string;
  data: T;
}

// Generic type to handle both formats
export type ApiResponse<T> = ApiResponseList<T> | ApiResponseSingle<T>;

// DTOs for updating salary
export interface UpdateSalaryByHRDto {
  total_bonus?: number; // Changed from bonus to match API response
  total_deduction?: number; // Changed from pay_reduction to match API response
}

export interface UpdateSalaryByFinanceDto extends UpdateSalaryByHRDto {
  payment_status?: string;
}

// New DTO for the unified update endpoint
export interface UpdateSalaryDto {
  base_salary?: number;
  payment_status?: 'paid' | 'unpaid';
}

// Salary component type enums
export enum AllowanceSalaryType {
  TRANSPORT = 'transport',
  MEAL = 'meal',
  HEALTH = 'health',
  POSITION = 'position',
  TENURE = 'tenure',
  THR = 'thr',
  OTHER = 'other',
}

export enum BonusSalaryType {
  KPI = 'kpi',
  PROJECT = 'project',
  OTHER = 'other',
}

export enum DeductionType {
  ABSENCE = 'absence',
  LATENESS = 'lateness',
  LOAN = 'loan',
  OTHER = 'other',
}

// Salary history types
export interface SalaryHistoryRecord {
  id: string;
  salary_id: string;
  change_description: string;
  created_at: string;
  created_by: string;
  created_by_name?: string;
  updated_at: string | null;
  updated_by: string | null;
  deleted_at: string | null;
  deleted_by: string | null;
  parsed_changes: SalaryChange[];
  formatted_changes: SalaryChange[]; // Pre-formatted changes with display text
}

export interface SalaryChange {
  field: string;
  changeType: string; // "field" or "component"
  subType?: string; // For field changes
  componentType?: string; // For component changes
  action?: string; // For component changes
  from_value: unknown;
  to_value: unknown;
  displayText: string; // Pre-formatted display text
}

// Salary component value interface
export interface SalaryComponentValue {
  type: string;
  amount?: number;
  total: number;
  total_salary: number;
}

// Error response type
export interface ErrorResponse {
  code?: string;
  message?: string;
  details?: Record<string, unknown>;
}

export interface Bonus {
  id: string;
  salary_id: string;
  amount: number;
  bonus_type: BonusSalaryType;
  notes?: string;
  kpi_id?: string;
  project_id?: string;
  created_at: string;
  updated_at: string | null;
}

export interface Allowance {
  id: string;
  salary_id: string;
  amount: number;
  allowance_type: AllowanceSalaryType;
  notes?: string;
  created_at: string;
  updated_at: string | null;
}

export interface Deduction {
  id: string;
  salary_id: string;
  amount: number;
  deduction_type: DeductionType;
  notes?: string;
  created_at: string;
  updated_at: string;
}

export interface Salary {
  id: string;
  employee_id: string;
  base_salary: number;
  allowances: number;
  deductions: Deduction[];
  net_salary: number;
  month: number;
  year: number;
  status: 'pending' | 'approved' | 'rejected';
  created_at: string;
  updated_at: string;
}
