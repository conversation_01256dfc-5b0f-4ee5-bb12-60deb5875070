// Enum untuk status penyelesaian tugas
// export enum TaskCompletionStatus {
//     PENDING = "pending",
//     IN_PROGRESS = "in_progress",
//     COMPLETED = "completed",
//     CANCELLED = "cancelled"
//   }

// Interface untuk informasi pagination
export interface PaginationInfo {
  total: number;
  pageCount: number;
  currentPage: number;
  perPage: number;
}

// Interface untuk response pagination
export interface PaginatedResponse<T> {
  items: T[];
  pagination: PaginationInfo;
}

// Interface untuk Task record
export interface Task {
  id: string;
  description: string;
  due_date: string; // Format YYYY-MM-DD
  assigned_by: string; // User ID or name who assigned the task
  completion_status: boolean;
  employee_id: string;
  attendance_id: string;
  created_at?: string;
  created_by?: string;
  updated_at?: string | null;
  updated_by?: string | null;
  deleted_at?: string | null;
  deleted_by?: string | null;
}

// Since tasks are now embedded within attendance records and managed through the attendance API,
// we no longer need separate DTO interfaces for tasks.
