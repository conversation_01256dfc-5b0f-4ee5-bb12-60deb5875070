export interface Task {
  id: string;
  assigned_by: string;
  description: string;
  completion_status: 'completed' | 'on_progress' | 'not_completed';
  employee_id: string;
  initial_date: string;
  due_date: string;
  project_id: string;
  weekly_log_id: string | null;
  created_at: string;
  created_by: string;
  updated_at: string | null;
  updated_by: string | null;
  deleted_at: string | null;
  deleted_by: string | null;
}

export interface DayActivities {
  starting: Task[];
  ending: Task[];
  ongoing: Task[];
  not_completed: Task[];
  on_progress: Task[];
  completed: Task[];
}

export interface DayData {
  date: string;
  day_of_week: number;
  activities: DayActivities;
}

export interface WeeklyLog {
  id: string;
  week_number: number;
  week_start_date: string;
  week_end_date: string;
  project_id: string;
  created_at: string;
  created_by: string;
  updated_at: string | null;
  updated_by: string | null;
  deleted_at: string | null;
  deleted_by: string | null;
  project_name: string;
  notes_by_day: Record<string, { note: string } | string>;
  tasks: Task[];
  days_data: Record<string, DayData>;
}

export interface PaginatedWeeklyLogsResponse {
  items: WeeklyLog[];
  pagination: {
    totalItems: number;
    totalPages: number;
    currentPage: number;
    itemsPerPage: number;
  };
}

export interface WeeklyLogFilterParams {
  page?: number;
  limit?: number;
  week_number?: number;
}

export interface AvailableWeeksResponse {
  week_numbers: number[];
  week_numbers_with_ranges: {
    week_number: number;
    date_range: string;
  }[];
}

export interface TriggerCreationResponse {
  count: number;
  tasks_updated: number;
}

export interface UpdateWeeklyLogNotesRequest {
  notes: Record<number, string>;
}

export interface BatchUpdateResult {
  created: number;
  updated: number;
  deleted: number;
  unchanged: number;
}
